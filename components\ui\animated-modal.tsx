import React, { useRef, useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { useModalContext } from './modal-context';

interface AnimatedModalProps {
  trigger?: React.ReactNode;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | '8xl' | '9xl';
  children: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  closeButton?: boolean;
}

export const AnimatedModal: React.FC<AnimatedModalProps> = ({
  trigger,
  title,
  size = 'md',
  children,
  open: controlledOpen,
  onOpenChange,
  header,
  footer,
  closeButton = true,
}) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [triggerRect, setTriggerRect] = useState<DOMRect | null>(null);

  // Use the modal context
  const { registerModal, unregisterModal, getBaseZIndex } = useModalContext();
  const [modalId, setModalId] = useState<number | null>(null);

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : internalOpen;

  const sizeClassMap = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    "2xl": "max-w-2xl",
    "3xl": "max-w-3xl",
    "4xl": "max-w-4xl",
    "5xl": "max-w-5xl",
    "6xl": "max-w-6xl",
    "7xl": "max-w-7xl",
    "8xl": "max-w-8xl",
    "9xl": "max-w-9xl",
  };

  const modalSizeClass = sizeClassMap[size] || "max-w-md";

  // Register modal when opened, unregister when closed
  useEffect(() => {
    if (open && modalId === null) {
      const id = registerModal();
      setModalId(id);
    } else if (!open && modalId !== null) {
      unregisterModal(modalId);
      setModalId(null);
    }
  }, [open, modalId, registerModal, unregisterModal]);

  // Calculate z-index values
  const baseZIndex = getBaseZIndex();
  const zIndexOffset = modalId ? (modalId - 1) * 10 : 0;
  const backdropZIndex = baseZIndex + zIndexOffset;
  const contentZIndex = backdropZIndex + 5;

  const handleOpenChange = (isOpen: boolean) => {
    if (isOpen && triggerRef.current) {
      setTriggerRect(triggerRef.current.getBoundingClientRect());
    }

    if (isControlled) {
      onOpenChange?.(isOpen);
    } else {
      setInternalOpen(isOpen);
    }
  };

  useEffect(() => {
    if (isControlled) {
      setInternalOpen(controlledOpen);
    }
  }, [controlledOpen, isControlled]);

  return (
    <Dialog.Root open={open} onOpenChange={handleOpenChange}>
      {trigger && <Dialog.Trigger asChild>
        <button
          ref={triggerRef}
          type="button"
          className="absolute top-4 rifocus:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 dark:focus-visible:ring-white focus-visible:ring-offset-2"
        >
          {trigger}
        </button>
      </Dialog.Trigger>}

      <AnimatePresence>
        {open && (
          <Dialog.Portal forceMount>
            <Dialog.Overlay asChild>
              <motion.div
                className="fixed inset-0 bg-black/40 backdrop-blur-md"
                style={{ zIndex: backdropZIndex }}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.4, ease: "easeInOut" }}
                onClick={() => handleOpenChange(false)}
                data-modal-id={modalId}
                data-modal-backdrop-z-index={backdropZIndex}
              />
            </Dialog.Overlay>

            <Dialog.Content asChild>
              <div
                className="fixed inset-0 flex items-center justify-center max-h-screen overflow-y-auto"
                style={{ zIndex: contentZIndex }}
                onClick={(e) => e.stopPropagation()}
                data-modal-content-z-index={contentZIndex}
              >
                <motion.div
                  className={`bg-white dark:bg-zinc-800 rounded-lg shadow-xl w-full ${modalSizeClass} mx-auto flex flex-col max-h-[90vh]`}
                  initial={{
                    opacity: 0,
                    scale: 0.95,
                    y: 10,
                  }}
                  animate={{
                    opacity: 1,
                    scale: 1,
                    y: 0,
                  }}
                  exit={{
                    opacity: 0,
                    scale: 0.95,
                    y: 10,
                  }}
                  transition={{
                    type: 'spring',
                    damping: 25,
                    stiffness: 300,
                    duration: 0.3,
                  }}
                  onClick={(e) => e.stopPropagation()}
                >
                  {/* Header */}
                  {header || (title && (
                    <div className="relative flex justify-between items-center p-3 border-b border-gray-200 dark:border-zinc-700 py-3">
                      <Dialog.Title className="text-lg font-medium text-gray-900 dark:text-white flex-1 text-center">
                        {title}
                      </Dialog.Title>
                      {closeButton && (
                        <Dialog.Close asChild>
                          <button
                            type="button"
                            className="absolute top-3 right-3 rounded-full p-1 inline-flex items-center justify-center text-gray-400 hover:text-gray-500 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-white"
                            aria-label="Close"
                          >
                            <X size={18} />
                          </button>
                        </Dialog.Close>
                      )}
                    </div>
                  ))}

                  {/* Scrollable Content */}
                  <div className="flex-1 overflow-y-auto">
                    {children}
                  </div>

                  {/* Footer */}
                  {footer && (
                    <div className="p-3 border-t border-gray-200 dark:border-zinc-700">
                      {footer}
                    </div>
                  )}
                </motion.div>
              </div>
            </Dialog.Content>
          </Dialog.Portal>
        )}
      </AnimatePresence>
    </Dialog.Root>
  );
};
