const e = {
  SUNDAESWAPV3: {
    name: "SundaeSwap V3",
    isEnabled: !0,
    url: "https://sundaeswap.finance/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/sundaev3logodh.png",
    code: "SUNDAESWAPV3",
    image: !0,
    isStopLossEnabled: !1,
    isLimitEnabled: !0,
    isDCAEnabled: !0,
    isV2: !0
  },
  SPLASH: {
    isEnabled: !0,
    name: "Splash",
    url: "https://splash.trade/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/splashlogodh.png",
    code: "SPLASH",
    image: !0,
    isStopLossEnabled: !1,
    isLimitEnabled: !0,
    isDCAEnabled: !0,
    isV2: !0
  },
  MINSWAPV2: {
    name: "Minswap V2",
    isEnabled: !0,
    url: "https://app.minswap.org/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/minswapv2logodh.png",
    code: "MINSWAPV2",
    image: !0,
    isStopLossEnabled: !1,
    isLimitEnabled: !0,
    isDCAEnabled: !0,
    isV2: !0
  },
  MINSWAP: {
    name: "MinSwap V1",
    isEnabled: !0,
    url: "https://minswap.org/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/minswapv1logodh.png",
    code: "MINSWAP",
    image: !0,
    isStopLossEnabled: !0,
    isLimitEnabled: !0,
    isDCAEnabled: !0,
    isV2: !1
  },
  AXO: {
    name: "Axo Trade",
    isEnabled: !0,
    url: "https://axo.trade/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/axologodh.png",
    code: "AXO",
    image: !0,
    isStopLossEnabled: !1,
    isLimitEnabled: !1,
    isDCAEnabled: !1,
    isV2: !0
  },
  WINGRIDER: {
    name: "Wingriders",
    isEnabled: !0,
    url: "https://wingriders.io/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/wrlogodh.png",
    code: "WINGRIDER",
    image: !0,
    isStopLossEnabled: !0,
    isLimitEnabled: !0,
    isDCAEnabled: !0,
    isV2: !1
  },
  WINGRIDERV2: {
    name: "Wingriders V2",
    isEnabled: !0,
    url: "https://wingriders.io/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/wingridersv2.png",
    code: "WINGRIDERV2",
    image: !0,
    isStopLossEnabled: !1,
    isLimitEnabled: !0,
    isDCAEnabled: !0,
    isV2: !0
  },
  SNEKFUN: {
    name: "SnekFun",
    isEnabled: !0,
    url: "https://snek.fun/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/snekfun.jpg",
    code: "SNEKFUN",
    image: !0,
    isStopLossEnabled: !1,
    isLimitEnabled: !1,
    isDCAEnabled: !1,
    isV2: !0
  },
  // GENIUS: {
  //   name: 'Genius Yield',
  //   isEnabled: true,
  //   url: 'https://app.geniusyield.co/',
  //   logo: "https://storage.googleapis.com/dexhunter-images/public/geniusyield.jpeg",
  //   code: 'GENIUS',
  //   image: true,
  //   isStopLossEnabled: false,
  //   isLimitEnabled: false,
  //   isDCAEnabled: false,
  //   isV2: true,
  // },
  SPECTRUM: {
    name: "Splash Spectrum",
    isEnabled: !1,
    url: "https://spectrum.cards/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/splashlogodh.png",
    code: "SPECTRUM",
    isStopLossEnabled: !1,
    isLimitEnabled: !1,
    isDCAEnabled: !1,
    isV2: !0
  },
  SUNDAESWAP: {
    name: "SundaeSwap V1",
    isEnabled: !0,
    url: "https://sundaeswap.finance/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/sundaev1logodh.png",
    code: "SUNDAESWAP",
    isStopLossEnabled: !0,
    isLimitEnabled: !0,
    isDCAEnabled: !0,
    isV2: !1
  },
  VYFI: {
    name: "VyFinance",
    isEnabled: !0,
    url: "https://vy.finance/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/vyfilogodh.png",
    code: "VYFI",
    image: !0,
    isStopLossEnabled: !0,
    isLimitEnabled: !0,
    isDCAEnabled: !0,
    isV2: !1
  },
  // CERRASWAP: {
  //   name: 'Cerra',
  //   isEnabled: true,
  //   url: 'https://app.cerra.io',
  //   logo: "https://storage.googleapis.com/dexhunter-images/public/cerralogodh.png",
  //   code: 'CERRASWAP',
  //   isStopLossEnabled: false,
  //   isLimitEnabled: false,
  //   isDCAEnabled: false,
  //   isV2: false,
  // },
  MUESLISWAP: {
    name: "MuesliSwap",
    isEnabled: !0,
    url: "https://muesliswap.com/",
    logo: "https://storage.googleapis.com/dexhunter-images/public/mueslilogodh.png",
    code: "MUESLISWAP",
    image: !1,
    isStopLossEnabled: !1,
    isLimitEnabled: !1,
    isDCAEnabled: !1,
    isV2: !1
  }
}, s = {
  name: "DexHunter",
  url: "https://dexhunter.io/",
  logo: "https://storage.googleapis.com/dexhunter-images/public/hunttoken.svg",
  code: "DEXHUNTER"
}, a = Object.keys(e);
export {
  s as DEXHUNTER_DEX_INFO,
  a as DEX_NAMES,
  e as dexes
};
