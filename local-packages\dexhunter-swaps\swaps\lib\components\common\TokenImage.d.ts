export declare const TokenImage: ({ token, isVerified, size, verifiedSize, className, disableTooltip, verifiedClass, }: {
    token: any;
    size?: number | undefined;
    isVerified?: boolean | undefined;
    verifiedSize?: number | undefined;
    className?: string | undefined;
    disableTooltip?: boolean | undefined;
    verifiedClass?: string | undefined;
}) => import("react/jsx-runtime").JSX.Element;
export default TokenImage;
