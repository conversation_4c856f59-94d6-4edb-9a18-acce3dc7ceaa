'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>alogRoot,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogCloseTrigger,
} from "@/components/ui/dialog";
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';
import StyledButton from '@/components/ui/styled-button';
import socials from '@/data/socials.json';
import Swal from 'sweetalert2';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

interface ConnectSocialModalProps {
  isOpen: boolean;
  onClose: () => void;
  connectedAccounts?: any;
  onSocialAdded?: (social: any) => void;
}

const ConnectSocialModal = ({ isOpen, onClose, onSocialAdded, connectedAccounts: initialConnectedAccounts }: ConnectSocialModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [connectingSocial, setConnectingSocial] = useState(false);
  const [selectedSocial, setSelectedSocial] = useState(null) as any;
  const [connectedAccounts, setConnectedAccounts] = useState(initialConnectedAccounts || []);

  const { userId } = useSelector((state: any) => state.wallet);

  // Use Convex query for connected accounts
  const connectedAccountsConvex = useQuery(
    api.accounts.getConnectedAccounts,
    userId ? { userId } : 'skip'
  );

  useEffect(() => {
    if (connectedAccountsConvex) {
      setConnectedAccounts(connectedAccountsConvex);
    }
  }, [connectedAccountsConvex]);

  const handleSocialSelect = (social: any) => {
    if (!connectedAccounts.some((account: any) => account.platform === social.value)) {
      setSelectedSocial(social);
    }
  };

  const handleConnect = () => {
    if (selectedSocial?.value === 'telegram') {
      toast.error('Telegram is not supported yet.');
      return;
    }

    setConnectingSocial(true);

    if (selectedSocial && selectedSocial?.connect_url) {
      const popupId = `${selectedSocial.value}-${Date.now()}`;
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

      if (isMobile) {
        // For mobile, open in the same window
        window.location.href = `${selectedSocial.connect_url}?popup=true&popupId=${popupId}&userId=${userId}`;
      } else {
        // For desktop, open in a popup
        const width = 600;
        const height = 600;
        const left = window.screen.width / 2 - width / 2;
        const top = window.screen.height / 2 - height / 2;

        const popupWindow = window.open(
          `${selectedSocial.connect_url}?popup=true&popupId=${popupId}&userId=${userId}`,
          'Auth',
          `width=${width},height=${height},left=${left},top=${top}`
        );

        // Poll the popup window to check if it's closed
        const pollTimer = setInterval(() => {
          if (popupWindow?.closed) {
            clearInterval(pollTimer);
            handleAuthComplete();
          }
        }, 500);
      }
    }
  };

  const handleAuthComplete = async () => {
    try {
      const response = await fetch('/api/verify-social-connection', {
        method: 'POST',
        headers: {
          credentials: 'include',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId, platform: selectedSocial.value })
      });

      const data = await response.json();

      if (data.success) {
        Swal.fire({
          html: 'Social account connected successfully!',
          icon: 'success',
          confirmButtonText: 'Close',
        });
        if (onSocialAdded) {
          onSocialAdded(data.connectedSocial);
        }
        // Refresh the connected accounts list
        // No need to fetch, handled by Convex query
        onClose();
      } else {
        toast.error('Failed to connect social account. Please try again.');
      }
    } catch (error) {
      console.error('Error verifying social connection:', error);
      toast.error('Error connecting social account. Please try again.');
    } finally {
      setIsLoading(false);
      setSelectedSocial(null);
      setConnectingSocial(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      // No need to fetch, handled by Convex query
    }
    if (!isOpen) {
      setSelectedSocial(null);
      setConnectingSocial(false);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <DialogRoot open={isOpen} onOpenChange={onClose} placement="center" motionPreset="slide-in-bottom">
      <DialogContent className="!max-w-4xl w-full backdrop-blur border-2 border-solid border-white/20 p-2 add-social-popup">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-center text-[#121212] dark:text-white">
            Connect Social Media Accounts
          </DialogTitle>
          <DialogCloseTrigger className="text-[#121212] dark:text-white" />
        </DialogHeader>

        <DialogBody className="flex flex-col items-center gap-6 py-4">
          {/* Display social media options here */}
          {isLoading ? (
            <div className="my-14 flex justify-center w-full">
              <div className="social-bar-loader"></div>
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-2 w-full">
              {socials.map((social, index) => {
                const isConnected = connectedAccounts.some((account: any) => account.platform === social.value);
                const isTelegram = social.value === 'telegram';

                return (
                  <div
                    className={`social-item animate__animated animate__fadeIn ${isConnected || isTelegram ? 'social-connected' : ''}`}
                    onClick={() => !(isConnected || isTelegram) && handleSocialSelect(social)}
                    key={index}
                  >
                    <div className={`social_card__style ${selectedSocial && selectedSocial.value === social.value ? 'active' : ''}`} style={{ cursor: isConnected || isTelegram ? 'not-allowed' : 'pointer', opacity: isConnected || isTelegram ? 0.5 : 1 }}>
                      <div className="social_icon" id={social.value.toLowerCase()} style={{ backgroundImage: `url(${social.icon ?? '/images/not-found.webp'})` }}></div>
                      <h5 className="social__title">{social.label}</h5>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </DialogBody>

        <DialogFooter className="flex !justify-center gap-4 mt-2 !mb-3 w-full !px-1">
          <StyledButton
            onClick={handleConnect}
            disabled={isLoading || !selectedSocial || connectedAccounts.some((account: any) => account.platform === selectedSocial?.value)}
            buttonText={connectingSocial ? "Connecting..." : "Connect"}
            className={`!text-sm w-40 h-10 flex items-center justify-center !p-0 ${connectingSocial ? 'hover' : ''}`}
          />
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  );
};
export default ConnectSocialModal;