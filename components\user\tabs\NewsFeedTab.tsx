'use client';

import React, { useState, useEffect, useRef, useCallback, memo, useMemo, useLayoutEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useUser } from '@/hooks/useUser';
import { debounce } from 'lodash';
import {
  useFetchPostsFeed,
  useCreatePostREST,
  useUpdatePostREST,
  useDeletePostREST,
  useLikePost,
  useSavePost,
  useReportPostREST,
  useCreateComment,
  useTrackPostView,
} from '@/hooks/convexHooks';
import {
  setPosts as setPostsRedux,
  setVisiblePostIds,
} from '@/redux/slices/postsSlice';
import { LayoutGrid, PanelBottom } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';
import PostCard from '@/components/social/PostCard';
import CreatePostForm from '@/components/social/CreatePostForm';
import MediaLightbox from '@/components/social/MediaLightbox';
import Tippy from '@tippyjs/react';
import { Post, PostData } from '@/types/post';
import { SelectDropdown } from '@/components/ui/select-dropdown';
import { cn } from '@/lib/utils';
import SearchBar from '@/components/user/SearchBar';
import GridPostCard from '@/components/social/GridPostCard';
import { setPostsCache, clearPostsCache } from '@/redux/slices/postCacheSlice';
import VirtualList from 'react-tiny-virtual-list';

interface NewsFeedTabProps {
  profileUserId: string;
  userData: any;
  setHasUnsavedChanges: (value: boolean) => void;
}

const PostSkeleton = () => (
  <div className="py-4 mb-4">
    <div className="flex items-center gap-4 mb-4">
      <Skeleton className="h-12 w-12 !rounded-full" />
      <div className="space-y-2 flex-1">
        <Skeleton className="h-4 w-[200px]" />
        <Skeleton className="h-4 w-[150px]" />
      </div>
    </div>
    <Skeleton className="h-4 w-full mb-2" />
    <Skeleton className="h-4 w-3/4 mb-4" />
    <Skeleton className="h-[300px] w-full mb-4 rounded-lg" />
    <div className="flex justify-between">
      <Skeleton className="h-8 w-[100px]" />
      <Skeleton className="h-8 w-[100px]" />
      <Skeleton className="h-8 w-[100px]" />
    </div>
  </div>
);

const GridSkeleton = () => (
  <Skeleton className="w-full h-full rounded-md aspect-[1]" />
);

const PostItem = memo(
  ({
    post,
    index,
    viewMode,
    onEditPost,
    onOpenLightbox,
    onDeletePost,
    onLike,
    onSave,
    onShare,
    onReport,
    onTrackView,
    panel,
  }: {
    post: Post;
    index: number;
    viewMode: 'grid' | 'post';
    onEditPost: (postId: string, updatedPost: any) => void;
    onOpenLightbox: (post: Post, mediaArray: any[], initialIndex: number) => void;
    onDeletePost: (postId: string) => void;
    onLike: (postId: string) => void;
    onSave: (post: Post) => void;
    onShare: (postId: string) => void;
    onReport: (postId: string, reason: string) => void;
    onTrackView: (contentId: string, contentType: string, ipAddress: string, userAgent: string, sessionId: string) => void;
    panel?: string;
  }) => {
    const uniqueKey = post.id || `post-${index}`;
    return (
      <div key={uniqueKey}>
        {viewMode === 'post' ? (
          <PostCard
            post={post}
            isLiked={post.liked}
            onLike={() => onLike(post.id)}
            onDelete={() => onDeletePost(post.id)}
            onComment={() => { }} // Provide a no-op or implement as needed
            onPostUpdated={(postId, updatedPost) => onEditPost(postId, updatedPost)}
            onPostDeleted={() => onDeletePost(post.id)}
            onOpenLightbox={onOpenLightbox}
            onSave={(post: any) => onSave(post)}
            onShare={onShare}
            onReport={onReport}
            onTrackView={onTrackView}
          />
        ) : (
          <GridPostCard
            post={post}
            onOpenLightbox={onOpenLightbox}
            panel={panel}
          />
        )}
      </div>
    );
  },
  // Improve the memoization comparison to prevent unnecessary re-renders
  (prevProps, nextProps) => {
    return (
      prevProps.post.id === nextProps.post.id &&
      prevProps.viewMode === nextProps.viewMode &&
      prevProps.panel === nextProps.panel &&
      prevProps.post.content === nextProps.post.content &&
      prevProps.post.likes === nextProps.post.likes &&
      prevProps.post.saves === nextProps.post.saves &&
      prevProps.post.liked === nextProps.post.liked &&
      prevProps.post.saved === nextProps.post.saved &&
      prevProps.post.views === nextProps.post.views &&
      prevProps.post.media === nextProps.post.media
    );
  }
);

// Base filter options
const baseFilterOptions = [
  { value: 'recently-added', label: 'Recently Added' },
  { value: 'most-viewed', label: 'Most Viewed' },
  { value: 'most-liked', label: 'Most Liked' },
];

// Additional filter options for media content
const mediaFilterOptions = [
  { value: 'highest-price', label: 'Highest Priced' },
  { value: 'lowest-price', label: 'Lowest Priced' },
  { value: 'free', label: 'Free' },
  { value: 'most-sold', label: 'Most Sold' },
  { value: 'longest', label: 'Longest' },
  { value: 'oldest', label: 'Oldest - Newest' },
];

// Panel options
const panelOptions = [
  { value: 'all-posts', label: 'All Posts' },
  { value: 'photos', label: 'Photos' },
  { value: 'videos', label: 'Videos' },
  { value: 'audio', label: 'Audio' },
  { value: 'clips', label: 'Clips' },
  { value: 'gifs', label: 'GIFs' },
];

function getCacheKey(profileUserId: string, filter: string, panel: string, selectedTag: string | null, page: number) {
  return [profileUserId, filter, panel, selectedTag ?? '', page].join('|');
}

const NewsFeedTab = ({ profileUserId, userData, setHasUnsavedChanges }: NewsFeedTabProps) => {
  const dispatch = useDispatch();
  const { user } = useUser();

  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentPost, setCurrentPost] = useState<Post | null>(null);
  const [filter, setFilter] = useState<string>('recently-added');
  const [panel, setPanel] = useState<string>('all-posts');
  const [viewMode, setViewMode] = useState<'grid' | 'post'>('post');
  const [isFocused, setIsFocused] = useState(false);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [isChildPopoverOpen, setIsChildPopoverOpen] = useState(false);
  const [isFileInputActive, setIsFileInputActive] = useState(false);
  const [mediaArray, setMediaArray] = useState<any[]>([]);
  const [initialIndex, setInitialIndex] = useState(0);
  const formContainerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<any>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const [page, setPage] = useState(1);

  // Reset page and posts when filters change
  useEffect(() => {
    setPage(1);
    dispatch(clearPostsCache(profileUserId));
  }, [filter, panel, selectedTag, profileUserId, dispatch]);

  const cacheKey = getCacheKey(profileUserId, filter, panel, selectedTag, page);
  const cached = useSelector((state: any) => state.postsCache[cacheKey]);

  const posts = cached ? cached.posts : [];

  useEffect(() => {
    if (!cached) {
      dispatch(setPostsCache({ key: cacheKey, posts: [] }));
    }
  }, [cacheKey, cached, dispatch, profileUserId, filter, panel, selectedTag, page]);

  const fetchedResult = useFetchPostsFeed({
    page,
    limit: 20,
    profileUserId,
    userId: user?.user_id,
    filter,
    panel,
    tag: selectedTag || undefined,
    direction: 'forward',
  });

  const isLoading = fetchedResult === undefined;
  const isFetching = isLoading;
  const hasNextPage = fetchedResult?.hasMore ?? false;
  const error = null;

  useEffect(() => {
    if (fetchedResult?.posts) {
      if (page === 1) {
        dispatch(setPostsCache({ key: cacheKey, posts: fetchedResult.posts }));
      } else {
        // Append new posts, avoiding duplicates
        const existingIds = new Set(posts.map((p: any) => p.id));
        const newPosts = fetchedResult.posts.filter((p: any) => !existingIds.has(p.id));
        if (newPosts.length > 0) {
          dispatch(setPostsCache({ key: cacheKey, posts: [...posts, ...newPosts] }));
        }
      }
    }
  }, [fetchedResult, page, posts, cacheKey, dispatch]);

  const fetchNextPage = () => {
    if (hasNextPage && !isFetching) {
      setPage((p) => p + 1);
    }
  };

  const refetch = () => {
    setPage(1);
    dispatch(clearPostsCache(profileUserId));
  };

  const handleViewModeToggle = useCallback(
    (mode: 'grid' | 'post') => {
      setViewMode(mode);
    },
    []
  );

  const createPost = useCreatePostREST();
  const updatePost = useUpdatePostREST();
  const deletePost = useDeletePostREST();
  const likePost = useLikePost();
  const savePost = useSavePost();
  const reportPost = useReportPostREST();
  const createComment = useCreateComment();
  const trackPostView = useTrackPostView();

  // Use the correct property for user ID comparison
  const isProfileOwner = user?.user_id === profileUserId;

  // Optimize the filteredPosts memoization to prevent unnecessary re-renders
  const filteredPosts = useMemo(() => {
    if (viewMode === 'post') return posts;
    return posts.filter(
      (post: Post) =>
        !!post &&
        (
          post.media?.[0]?.mediaType?.startsWith('image/') ||
          post.media?.[0]?.mediaType?.startsWith('video/') ||
          post.media?.[0]?.mediaType?.startsWith('audio/')
        )
    );
  }, [posts, viewMode]);

  const handleLocalUnsavedChanges = (value: boolean) => {
    setHasUnsavedChanges(value);
  };

  const handlePostDeleted = async (postId: string) => {
    try {
      dispatch(setPostsCache({ key: cacheKey, posts: posts.filter((p: Post) => p.id !== postId) }));
      toast.success('Post deleted successfully');
    } catch (err: any) {
      toast.error(`Failed to delete post: ${err.message}`);
    }
  };

  const handlePostReported = async (postId: string, reason: string) => {
    try {
      await reportPost({ postId, reason });
      toast.success('Post reported');
    } catch (err: any) {
      toast.error(`Failed to report post: ${err.message}`);
    }
  };

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      setIsFocused(false);
      if (editorRef.current) editorRef.current.blur();
    }
  };

  const handleSearchExpand = useCallback((expanded: boolean) => {
    setIsSearchExpanded(expanded);
  }, []);

  const handleTagSelect = useCallback((tagName: string) => {
    setSelectedTag(tagName);
    refetch();
  }, []);

  const handleCreatePost = async (postData: PostData) => {
    setHasUnsavedChanges(false);
    try {
      const result = await createPost(postData);
      if (result.post) {
        const newPost: Post = {
          id: result.post.id,
          contentType: result.post.contentType,
          content: result.post.content,
          media: result.post.media || [],
          isPaid: result.post.isPaid || false,
          createdAt: result.post.createdAt,
          user: {
            id: user?.convexUserId || result.post.user.id || '',
            username: userData?.account?.username || '',
            name: userData?.account?.displayName || '',
            profileImage: userData?.account?.profilePhoto || result.post.user.profileImage || '',
          },
          taggedUsers: result.post.taggedUsers || [],
          mentions: result.post.mentions || [],
          hashtags: Array.from(new Set(result.post.hashtags || [])),
          likes: 0,
          comments: 0,
          liked: false,
          saved: false,
          privacy: result.post.privacy || 'public',
          privateType: result.post.privateType || 'subscription',
          ppvPrice: result.post.ppvPrice || null,
          is_edited: false,
          saves: 0,
          views: 0,
          price: result.post.price || null,
        };
        dispatch(setPostsCache({ key: cacheKey, posts: [newPost, ...posts] }));
        setIsFocused(false);
        return newPost;
      }
    } catch (err: any) {
      toast.error(`Failed to create post: ${err.message}`);
    }
  };

  const handleOpenLightbox = useCallback(
    (post: Post, mediaArray: any[], initialIndex: number) => {
      setCurrentPost(post);
      setMediaArray(mediaArray);
      setInitialIndex(initialIndex);
      setLightboxOpen(true);
    },
    []
  );

  const handleEditPost = useCallback(
    async (postId: string, updatedData: any) => {
      try {
        await updatePost(updatedData);
        toast.success('Post updated successfully');
      } catch (err: any) {
        toast.error(`Failed to update post: ${err.message}`);
      }
    },
    [updatePost]
  );

  const handleLike = useCallback(
    async (postId: string) => {
      try {
        await likePost({ contentId: postId, contentType: 'post' });
      } catch (err: any) {
        toast.error(`Failed to like post: ${err.message}`);
      }
    },
    [likePost]
  );

  const handleSave = useCallback(
    async (post: Post) => {
      try {
        await savePost({ contentId: post.id, contentType: post.media?.[0]?.mediaType || '' });
      } catch (err: any) {
        toast.error(`Failed to save post: ${err.message}`);
      }
    },
    [savePost]
  );

  const handlePostView = useCallback(
    async (viewData: any) => {
      await trackPostView(viewData);
    },
    [trackPostView]
  );

  const handleShare = useCallback(async (postId: string) => {
    try {
      const postUrl = `${window.location.origin}/post/${postId}`;
      if (navigator.share) {
        await navigator.share({
          title: userData?.account?.displayName || userData?.account?.username,
          text: 'Check out this post on Sugar Club!',
          url: postUrl,
        });
      } else {
        await navigator.clipboard.writeText(postUrl);
        toast.success('Link copied!');
      }
    } catch (error) {
      toast.error('Failed to share');
    }
  }, []);

  const getFilterOptions = useCallback(() => {
    switch (panel) {
      case 'videos':
      case 'photos':
      case 'clips':
        return [
          baseFilterOptions[0],
          ...mediaFilterOptions.slice(0, 3),
          ...baseFilterOptions.slice(1),
          mediaFilterOptions[3],
          ...mediaFilterOptions.slice(4),
        ];
      default:
        return [...baseFilterOptions, { value: 'oldest', label: 'Oldest To Newest' }];
    }
  }, [panel]);

  useEffect(() => {
    const availableFilters = getFilterOptions().map((o) => o.value);
    if (!availableFilters.includes(filter)) {
      setFilter('recently-added');
    }
  }, [panel, filter, getFilterOptions]);

  const debouncedSetVisiblePostIds = useCallback(
    debounce((visibleIds: string[]) => {
      dispatch(setVisiblePostIds(visibleIds));
    }, 100),
    [dispatch]
  );

  // Handle clicks outside the form to dismiss focus
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isFocused &&
        formContainerRef.current &&
        !formContainerRef.current.contains(event.target as Node) &&
        !isChildPopoverOpen &&
        !isFileInputActive
      ) {
        setIsFocused(false);
        if (editorRef.current) editorRef.current.blur();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isFocused, isChildPopoverOpen, isFileInputActive]);

  // Force stacking context update when isFocused changes
  useLayoutEffect(() => {
    if (formContainerRef.current && isFocused) {
      formContainerRef.current.style.zIndex = '50';
    } else if (formContainerRef.current) {
      formContainerRef.current.style.zIndex = '0';
    }
  }, [isFocused]);

  // Preload images for upcoming posts
  /* useEffect(() => {
    const preloadImages = () => {
      filteredPosts.slice(0, 10).forEach((post: Post) => {
        if (post.media?.[0]?.mediaUrl) {
          const img = new Image();
          img.src = post.media[0].mediaUrl;
        }
      });
    };
    preloadImages();
  }, [filteredPosts]); */

  const renderPostMode = () => (
    <VirtualList
      width="100%"
      height={window.innerHeight * 0.7} // or fixed: 600
      itemCount={filteredPosts.length}
      itemSize={400} // average estimate
      overscanCount={3}
      renderItem={({ index, style }: any) =>
        <div style={style}>
          <PostItem
            post={filteredPosts[index]}
            index={index}
            viewMode="post"
            onEditPost={handleEditPost}
            onOpenLightbox={handleOpenLightbox}
            onDeletePost={handlePostDeleted}
            onLike={handleLike}
            onSave={handleSave}
            onShare={handleShare}
            onReport={handlePostReported}
            onTrackView={handlePostView}
            panel={panel}
          />
        </div>
      }
      onItemsRendered={({ visibleStartIndex, visibleStopIndex }: any) => {
        const visibleIds = [];
        for (let i = visibleStartIndex; i <= visibleStopIndex; i++) {
          if (filteredPosts[i]?.id) visibleIds.push(filteredPosts[i].id);
        }
        debouncedSetVisiblePostIds(visibleIds);

        if (visibleStopIndex >= filteredPosts.length - 5 && hasNextPage && !isFetching) {
          fetchNextPage();
        }
      }}
    />
  );

  const GRID_COLUMNS = 3;
  const gridRows = Math.ceil(filteredPosts.length / GRID_COLUMNS);

  const renderGridMode = () => (
    <VirtualList
      width="100%"
      height={window.innerHeight * 0.7}
      itemCount={gridRows}
      itemSize={300}
      overscanCount={2}
      renderItem={({ index, style }: any) => {
        const start = index * GRID_COLUMNS;
        const rowPosts = filteredPosts.slice(start, start + GRID_COLUMNS);
        return (
          <div style={style} className="grid grid-cols-3 gap-1.5 py-1">
            {rowPosts.map((post: any) => (
              <div key={post.id} className="w-full h-full">
                <GridPostCard post={post} onOpenLightbox={handleOpenLightbox} panel={panel} />
              </div>
            ))}
          </div>
        );
      }}
      onItemsRendered={({ visibleStopIndex }: any) => {
        const lastPostIndex = (visibleStopIndex + 1) * GRID_COLUMNS;
        if (lastPostIndex >= filteredPosts.length - 2 && hasNextPage && !isFetching) {
          fetchNextPage();
        }

        const visibleIds = filteredPosts
          .slice(0, lastPostIndex)
          .map((p) => p.id)
          .filter(Boolean);
        debouncedSetVisiblePostIds(visibleIds);
      }}
    />
  );

  return (
    <>
      {isFocused && (
        <div
          className="fixed inset-0 bg-black/10 backdrop-blur-md z-40 transition-opacity duration-200"
          onClick={handleOverlayClick}
          aria-hidden="true"
        />
      )}

      <div className="space-y-4 pb-[100px] px-2 pl-0">
        {error ? (
          <>
            {isProfileOwner && (
              <div
                ref={formContainerRef}
                className={cn(
                  "border border-solid rounded-lg border-gray-300 p-4 mb-6 relative bg-white dark:bg-[#18181b] transition-colors duration-300 ease-in-out",
                  isFocused ? "bg-white/90 dark:bg-[#18181b]/90" : ""
                )}
                style={{ zIndex: isFocused ? 50 : 0 }}
                tabIndex={-1}
              >
                <CreatePostForm
                  onSubmit={handleCreatePost}
                  setHasUnsavedChanges={handleLocalUnsavedChanges}
                  isFocused={isFocused}
                  setIsFocused={setIsFocused}
                  setIsChildPopoverOpen={setIsChildPopoverOpen}
                  setIsFileInputActive={setIsFileInputActive}
                  editorRef={editorRef}
                  user={userData}
                />
              </div>
            )}
            <div className="flex items-center justify-center h-64 flex-col gap-4">
              <p className="text-red-500">Error loading posts: {(error as any)?.message}</p>
              <Button onClick={() => refetch()}>Retry</Button>
            </div>
          </>
        ) : (
          <>
            <div className={`flex items-center justify-between mt-0 mb-0.5 z-[2] relative ${isSearchExpanded ? 'gap-4' : 'gap-3'}`}>
              <div className={`flex items-center gap-4 w-full ${isSearchExpanded ? 'xl:max-w-[74.8%] 2xl:max-w-[81.85%]' : 'max-w-[72.6%]'} transition-[max-width] duration-300 ease-in-out`}>
                <SelectDropdown
                  value={panel}
                  onChange={(value) => setPanel(value)}
                  options={panelOptions}
                  placeholder="Feed"
                  fixedPlaceholder="Feed: "
                  className="w-full max-w-40"
                  buttonClassName="bg-transparent dark:bg-transparent capitalize"
                  dropdownClassName="rounded-lg dark:bg-[#18181b]/80 bg-white/80 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-[#18181b]/20 dark:border-white/20 z-[1]"
                />

                <SearchBar
                  onTagSelect={handleTagSelect}
                  isExpanded={isSearchExpanded}
                  onExpand={handleSearchExpand}
                />
              </div>

              <div className="flex items-center justify-center gap-2">
                <div className={`flex items-center justify-center gap-2 -ml-0.5 rounded-lg p-1 transition-all duration-300 ease-in-out ${isSearchExpanded ? 'opacity-0 invisible hidden' : 'opacity-100 visible'}`}>
                  <Tippy content="Post View" animation="shift-toward-subtle" placement="top" arrow={true} theme="sugar">
                    <button className="p-0 rounded" onClick={() => handleViewModeToggle('post')}>
                      <PanelBottom
                        size={32}
                        className={viewMode === 'post' ? 'text-turquoise' : 'text-gray-500 dark:text-white'}
                      />
                    </button>
                  </Tippy>
                  <Tippy content="Grid View" animation="shift-toward-subtle" placement="top" arrow={true} theme="sugar">
                    <button className="p-0 rounded" onClick={() => handleViewModeToggle('grid')}>
                      <LayoutGrid
                        size={32}
                        className={viewMode === 'grid' ? 'text-turquoise' : 'text-gray-500 dark:text-white'}
                      />
                    </button>
                  </Tippy>
                </div>

                <SelectDropdown
                  value={filter}
                  onChange={(value) => setFilter(value)}
                  options={getFilterOptions()}
                  className="w-full min-w-[146px]"
                  buttonClassName="bg-transparent dark:bg-transparent rounded-full min-w-[146px] whitespace-nowrap"
                  dropdownClassName="rounded-lg dark:bg-[#18181b]/80 bg-white/80 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-[#18181b]/20 dark:border-white/20"
                />
              </div>
            </div>

            {/* Display selected tag */}
            {selectedTag && (
              <div className="flex items-center gap-2 mb-4 w-full">
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  Selected tag: <span className="inline-flex items-center justify-center rounded-full px-4 py-1 bg-gray-400 dark:bg-white text-white dark:text-[#18181b] ml-2">#{selectedTag}</span>
                </span>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedTag(null);
                    refetch();
                  }}
                  className="bg-red-500 dark:bg-red-500 text-white dark:text-white rounded-full px-4 py-1 ml-2"
                >
                  Clear
                </Button>
              </div>
            )}

            {isProfileOwner && (
              <div
                ref={formContainerRef}
                className={cn(
                  "border border-solid rounded-lg border-gray-300 p-4 mb-6 relative bg-white dark:bg-[#18181b] transition-colors duration-300 ease-in-out",
                  isFocused ? "bg-white/90 dark:bg-[#18181b]/90" : ""
                )}
                style={{ zIndex: isFocused ? 50 : 0 }}
                tabIndex={-1}
              >
                <CreatePostForm
                  onSubmit={handleCreatePost}
                  setHasUnsavedChanges={handleLocalUnsavedChanges}
                  isFocused={isFocused}
                  setIsFocused={setIsFocused}
                  setIsChildPopoverOpen={setIsChildPopoverOpen}
                  setIsFileInputActive={setIsFileInputActive}
                  editorRef={editorRef}
                  user={userData}
                />
              </div>
            )}

            {/* Posts Feed */}
            <div>
              {isLoading && filteredPosts.length === 0 ? (
                <div className={viewMode === 'grid' ? 'grid grid-cols-3 gap-2' : 'space-y-4'}>
                  {Array.from({ length: viewMode === 'grid' ? 8 : 2 }).map((_, index) =>
                    viewMode === 'grid' ? <GridSkeleton key={index} /> : <PostSkeleton key={index} />
                  )}
                </div>
              ) : filteredPosts.length > 0 ? (
                viewMode === 'post' ? renderPostMode() : renderGridMode()
              ) : !isLoading && (
                <div className="p-8 text-center">
                  <p className="text-gray-500 dark:text-gray-400 text-2xl font-bold uppercase">
                    {selectedTag
                      ? `No posts found for #${selectedTag}`
                      : viewMode === 'grid'
                        ? 'No media posts found'
                        : 'No posts found'}
                  </p>
                  {isProfileOwner && (
                    <p className="text-gray-500 dark:text-gray-400 mt-2 text-2xl font-bold uppercase">Create your first post above</p>
                  )}
                </div>
              )}
            </div>

            {currentPost && (
              <MediaLightbox
                open={lightboxOpen}
                onOpenChange={setLightboxOpen}
                post={currentPost}
                mediaArray={mediaArray}
                initialIndex={initialIndex}
              />
            )}
          </>
        )}
      </div>
    </>
  );
};
export default NewsFeedTab;