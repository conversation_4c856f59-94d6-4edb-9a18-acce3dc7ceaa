import { jsxs as e, jsx as t } from "react/jsx-runtime";
import { memo as o } from "react";
const i = (r) => /* @__PURE__ */ e(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 16 12",
    fill: "none",
    ...r,
    children: [
      /* @__PURE__ */ t("rect", { width: 16, height: 1.5, rx: 0.75, fill: "currentColor" }),
      /* @__PURE__ */ t("rect", { y: 5, width: 16, height: 1.5, rx: 0.75, fill: "currentColor" }),
      /* @__PURE__ */ t("rect", { y: 10, width: 16, height: 1.5, rx: 0.75, fill: "currentColor" })
    ]
  }
), n = o(i);
export {
  n as default
};
