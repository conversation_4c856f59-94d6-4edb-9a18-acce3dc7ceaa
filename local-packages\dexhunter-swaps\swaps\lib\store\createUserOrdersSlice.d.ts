export interface UserOrdersSlice {
    selectedOrdersIds: any[];
    searchInput: string;
    isOrderCancelLoading: boolean;
    pendingOrdersCount: number;
    upcomingOrders: any[];
    cancellingOrders: any[];
    pendingDCAs: any[];
    setSearchInput: (input: string) => void;
    setSelectedOrdersIds: (orders: any[]) => void;
    toggleSelectedOrderId: (order: any) => void;
    setIsOrderCancelLoading: (loading: boolean) => void;
    setPendingOrdersCount: (count: number) => void;
    setUpcomingOrders: (orders: any[]) => void;
    setCancellingOrders: (orders: any[]) => void;
    setPendingDCAs: (orders: any[]) => void;
}
declare const createTokenSearchSlice: (set: any) => {
    selectedOrdersIds: never[];
    searchInput: string;
    isOrderCancelLoading: boolean;
    pendingOrdersCount: number;
    upcomingOrders: never[];
    cancellingOrders: never[];
    pendingDCAs: never[];
    setSelectedOrdersIds: (orders: any[]) => void;
    toggleSelectedOrderId: (orderId: any) => void;
    setSearchInput: (input: string) => void;
    setIsOrderCancelLoading: (isOrderCancelLoading: boolean) => void;
    setPendingOrdersCount: (count: number) => void;
    setUpcomingOrders: (orders: any[]) => void;
    setCancellingOrders: (orders: any[]) => void;
    setPendingDCAs: (orders: any[]) => void;
};
export default createTokenSearchSlice;
