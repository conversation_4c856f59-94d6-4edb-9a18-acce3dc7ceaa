import { SelectedWallet } from '../typescript/cardano-api';
declare global {
    interface Window {
        cardano: any;
    }
}
type onWalletConnectType = ((data: any) => void) | undefined;
export declare const useWalletConnect: (onWalletConnect?: onWalletConnectType) => {
    connectWallet: (wallet: SelectedWallet) => Promise<void>;
    reconnectToWallet: (selectedWallet?: string) => Promise<void>;
};
export {};
