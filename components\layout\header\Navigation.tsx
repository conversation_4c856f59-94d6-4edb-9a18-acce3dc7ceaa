// Navigation.tsx
'use client';

import Header from '@/components/layout/header/Header';
import Footer from '@/components/layout/footer/Footer';

interface NavigationProps {
  children: React.ReactNode;
  isMobile?: boolean;
}

export default function Navigation({ children, isMobile = false }: NavigationProps) {
  return (
    <>
      <Header isMobile={isMobile} />
      {children}
      <Footer />
    </>
  );
}