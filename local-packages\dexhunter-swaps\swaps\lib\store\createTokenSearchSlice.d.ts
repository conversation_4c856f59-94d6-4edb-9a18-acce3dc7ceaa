export interface TokenSearchSlice {
    open: boolean;
    searchInput: string;
    swapType: string;
    adaUsdPrice: number;
    tokenList: any[];
    unverifiedTokenList: any[];
    supportedTokens: any[];
    callbackSelectedToken: (token: any) => void;
    openModal: () => void;
    closeModal: (callback?: () => void) => void;
    toggleModal: () => void;
    setSearchInput: (input: string) => void;
    setSwapType: (type: string) => void;
    setAdaUsdPrice: (price: number) => void;
    setTokenList: (tokenList: any[]) => void;
    setUnverifiedTokenList: (tokenList: any[]) => void;
    setCallbackSelectedToken: (callback: (token: any) => void) => void;
    setSupportedTokens: (supportedTokens: any[]) => void;
}
declare const createTokenSearchSlice: (set: any) => {
    open: boolean;
    searchInput: string;
    swapType: string;
    adaUsdPrice: number;
    tokenList: never[];
    unverifiedTokenList: never[];
    supportedTokens: never[];
    callbackSelectedToken: () => void;
    openModal: () => void;
    closeModal: (callback?: () => void) => void;
    toggleModal: () => void;
    setSearchInput: (input: string) => void;
    setSwapType: (type: string) => void;
    setAdaUsdPrice: (price: number) => void;
    setTokenList: (tokenList: any[]) => void;
    setUnverifiedTokenList: (tokenList: any[]) => void;
    setCallbackSelectedToken: (callback: any) => void;
    setSupportedTokens: (supportedTokens: any[]) => void;
};
export default createTokenSearchSlice;
