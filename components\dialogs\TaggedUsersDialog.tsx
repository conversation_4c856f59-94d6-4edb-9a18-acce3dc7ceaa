import React from 'react';
import { DialogRoot as <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import Link from 'next/link';

interface TaggedUser {
  userId: string;
  username: string;
  profilePhoto: string;
}

interface TaggedUsersModalProps {
  isOpen: boolean;
  onClose: () => void;
  taggedUsers: TaggedUser[];
}

const TaggedUsersModal: React.FC<TaggedUsersModalProps> = ({
  isOpen,
  onClose,
  taggedUsers
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Tagged Users</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 max-h-[60vh] overflow-y-auto">
          {taggedUsers?.map((user) => (
            <Link
              key={user.userId}
              href={`/user/${user.username}`}
              className="flex items-center space-x-3 p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg"
            >
              <Avatar className="h-10 w-10">
                <AvatarImage src={user.profilePhoto} alt={user.username} />
                <AvatarFallback>{user.username[0].toUpperCase()}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="font-medium">{user.username}</p>
              </div>
            </Link>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default TaggedUsersModal;