import { jsx as ge, jsxs as Ve } from "react/jsx-runtime";
import { Input as ci } from "../ui/input.js";
import Mn from "../../store/useStore.js";
import S, { useRef as fr, useState as di, useEffect as fs, useMemo as Dn } from "react";
import { PRIORITY_TOKEN_IDS as bn, CARDANO_TOKEN as Xe, DEFAULT_TOKEN_LIST as fi } from "../../swap/components/tokens.js";
import { formatBalance as hs } from "../../utils/formatNumber.js";
import { R as hi } from "../../index-c8f2666b.js";
import { u as tn } from "../../useQuery-febd7967.js";
import { TokenImage as ms } from "./TokenImage.js";
import { server as nn } from "../../config/axios.js";
import { cn as mi } from "../../lib/utils.js";
import pi from "../../hooks/useScreen.js";
import gi from "../../assets/svg/IconRemove.js";
import yi from "../ui/tooltipDialog.js";
import { getBalance as _i } from "../../utils/cardanoUtils.js";
import wi from "../../hooks/useGlobalSettings.js";
import Si from "../../assets/svg/IconWallet.js";
import vi from "../../assets/svg/IconWalletCross.js";
import xi from "../../assets/svg/IconMagnifier.js";
import { Sheet as Ti, SheetContent as ki } from "../ui/sheet-connect-wallet.js";
import { I as Ii } from "../../IconTilde-bf643edd.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../index-1c873780.js";
import "../../query-013b86c3.js";
import "../../QueryClientProvider-6bcd4331.js";
import "../ui/skeleton.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
import "../ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../index-1d6812f7.js";
import "../../createReactComponent-ec43b511.js";
const Zn = 0, Ct = 1, Jt = 2, Ws = 4;
function As(e, t) {
  return (n) => e(t(n));
}
function Mi(e, t) {
  return t(e);
}
function Vs(e, t) {
  return (n) => e(t, n);
}
function ps(e, t) {
  return () => e(t);
}
function qn(e, t) {
  return t(e), e;
}
function pe(...e) {
  return e;
}
function Di(e) {
  e();
}
function gs(e) {
  return () => e;
}
function bi(...e) {
  return () => {
    e.map(Di);
  };
}
function Er(e) {
  return e !== void 0;
}
function Qt() {
}
function le(e, t) {
  return e(Ct, t);
}
function j(e, t) {
  e(Zn, t);
}
function Nr(e) {
  e(Jt);
}
function ze(e) {
  return e(Ws);
}
function H(e, t) {
  return le(e, Vs(t, Zn));
}
function wt(e, t) {
  const n = e(Ct, (r) => {
    n(), t(r);
  });
  return n;
}
function ee() {
  const e = [];
  return (t, n) => {
    switch (t) {
      case Jt:
        e.splice(0, e.length);
        return;
      case Ct:
        return e.push(n), () => {
          const r = e.indexOf(n);
          r > -1 && e.splice(r, 1);
        };
      case Zn:
        e.slice().forEach((r) => {
          r(n);
        });
        return;
      default:
        throw new Error(`unrecognized action ${t}`);
    }
  };
}
function k(e) {
  let t = e;
  const n = ee();
  return (r, s) => {
    switch (r) {
      case Ct:
        s(t);
        break;
      case Zn:
        t = s;
        break;
      case Ws:
        return t;
    }
    return n(r, s);
  };
}
function Oi(e) {
  let t, n;
  const r = () => t && t();
  return function(s, o) {
    switch (s) {
      case Ct:
        return o ? n === o ? void 0 : (r(), n = o, t = le(e, o), t) : (r(), Qt);
      case Jt:
        r(), n = null;
        return;
      default:
        throw new Error(`unrecognized action ${s}`);
    }
  };
}
function Be(e) {
  return qn(ee(), (t) => H(e, t));
}
function Ee(e, t) {
  return qn(k(t), (n) => H(e, n));
}
function Ci(...e) {
  return (t) => e.reduceRight(Mi, t);
}
function _(e, ...t) {
  const n = Ci(...t);
  return (r, s) => {
    switch (r) {
      case Ct:
        return le(e, n(s));
      case Jt:
        Nr(e);
        return;
    }
  };
}
function Us(e, t) {
  return e === t;
}
function me(e = Us) {
  let t;
  return (n) => (r) => {
    e(t, r) || (t = r, n(r));
  };
}
function U(e) {
  return (t) => (n) => {
    e(n) && t(n);
  };
}
function R(e) {
  return (t) => As(t, e);
}
function pt(e) {
  return (t) => () => t(e);
}
function lt(e, t) {
  return (n) => (r) => n(t = e(t, r));
}
function Kt(e) {
  return (t) => (n) => {
    e > 0 ? e-- : t(n);
  };
}
function It(e) {
  let t = null, n;
  return (r) => (s) => {
    t = s, !n && (n = setTimeout(() => {
      n = void 0, r(t);
    }, e));
  };
}
function ys(e) {
  let t, n;
  return (r) => (s) => {
    t = s, n && clearTimeout(n), n = setTimeout(() => {
      r(t);
    }, e);
  };
}
function Z(...e) {
  const t = new Array(e.length);
  let n = 0, r = null;
  const s = Math.pow(2, e.length) - 1;
  return e.forEach((o, i) => {
    const a = Math.pow(2, i);
    le(o, (l) => {
      const u = n;
      n = n | a, t[i] = l, u !== s && n === s && r && (r(), r = null);
    });
  }), (o) => (i) => {
    const a = () => o([i].concat(t));
    n === s ? a() : r = a;
  };
}
function _s(...e) {
  return function(t, n) {
    switch (t) {
      case Ct:
        return bi(...e.map((r) => le(r, n)));
      case Jt:
        return;
      default:
        throw new Error(`unrecognized action ${t}`);
    }
  };
}
function V(e, t = Us) {
  return _(e, me(t));
}
function ke(...e) {
  const t = ee(), n = new Array(e.length);
  let r = 0;
  const s = Math.pow(2, e.length) - 1;
  return e.forEach((o, i) => {
    const a = Math.pow(2, i);
    le(o, (l) => {
      n[i] = l, r = r | a, r === s && j(t, n);
    });
  }), function(o, i) {
    switch (o) {
      case Ct:
        return r === s && i(n), le(t, i);
      case Jt:
        return Nr(t);
      default:
        throw new Error(`unrecognized action ${o}`);
    }
  };
}
function ie(e, t = [], { singleton: n } = { singleton: !0 }) {
  return {
    id: Ri(),
    constructor: e,
    dependencies: t,
    singleton: n
  };
}
const Ri = () => Symbol();
function Yi(e) {
  const t = /* @__PURE__ */ new Map(), n = ({ id: r, constructor: s, dependencies: o, singleton: i }) => {
    if (i && t.has(r))
      return t.get(r);
    const a = s(o.map((l) => n(l)));
    return i && t.set(r, a), a;
  };
  return n(e);
}
function Ei(e, t) {
  const n = {}, r = {};
  let s = 0;
  const o = e.length;
  for (; s < o; )
    r[e[s]] = 1, s += 1;
  for (const i in t)
    r.hasOwnProperty(i) || (n[i] = t[i]);
  return n;
}
const On = typeof document < "u" ? S.useLayoutEffect : S.useEffect;
function Gs(e, t, n) {
  const r = Object.keys(t.required || {}), s = Object.keys(t.optional || {}), o = Object.keys(t.methods || {}), i = Object.keys(t.events || {}), a = S.createContext({});
  function l(d, v) {
    d.propsReady && j(d.propsReady, !1);
    for (const h of r) {
      const m = d[t.required[h]];
      j(m, v[h]);
    }
    for (const h of s)
      if (h in v) {
        const m = d[t.optional[h]];
        j(m, v[h]);
      }
    d.propsReady && j(d.propsReady, !0);
  }
  function u(d) {
    return o.reduce((v, h) => (v[h] = (m) => {
      const T = d[t.methods[h]];
      j(T, m);
    }, v), {});
  }
  function c(d) {
    return i.reduce((v, h) => (v[h] = Oi(d[t.events[h]]), v), {});
  }
  return {
    Component: S.forwardRef((d, v) => {
      const { children: h, ...m } = d, [T] = S.useState(() => qn(Yi(e), (g) => l(g, m))), [E] = S.useState(ps(c, T));
      return On(() => {
        for (const g of i)
          g in m && le(E[g], m[g]);
        return () => {
          Object.values(E).map(Nr);
        };
      }, [m, E, T]), On(() => {
        l(T, m);
      }), S.useImperativeHandle(v, gs(u(T))), S.createElement(
        a.Provider,
        { value: T },
        n ? S.createElement(
          n,
          Ei([...r, ...s, ...i], m),
          h
        ) : h
      );
    }),
    usePublisher: (d) => S.useCallback(Vs(j, S.useContext(a)[d]), [d]),
    useEmitterValue: (d) => {
      const h = S.useContext(a)[d], [m, T] = S.useState(ps(ze, h));
      return On(
        () => le(h, (E) => {
          E !== m && T(gs(E));
        }),
        [h, m]
      ), m;
    },
    useEmitter: (d, v) => {
      const m = S.useContext(a)[d];
      On(() => le(m, v), [v, m]);
    }
  };
}
const Ni = typeof document < "u" ? S.useLayoutEffect : S.useEffect, Pi = Ni;
var je = /* @__PURE__ */ ((e) => (e[e.DEBUG = 0] = "DEBUG", e[e.INFO = 1] = "INFO", e[e.WARN = 2] = "WARN", e[e.ERROR = 3] = "ERROR", e))(je || {});
const Li = {
  0: "debug",
  1: "log",
  2: "warn",
  3: "error"
}, Hi = () => typeof globalThis > "u" ? window : globalThis, Rt = ie(
  () => {
    const e = k(
      3
      /* ERROR */
    );
    return {
      log: k((n, r, s = 1) => {
        var o;
        const i = (o = Hi().VIRTUOSO_LOG_LEVEL) != null ? o : ze(e);
        s >= i && console[Li[s]](
          "%creact-virtuoso: %c%s %o",
          "color: #0253b3; font-weight: bold",
          "color: initial",
          n,
          r
        );
      }),
      logLevel: e
    };
  },
  [],
  { singleton: !0 }
);
function Pr(e, t = !0) {
  const n = S.useRef(null);
  let r = (s) => {
  };
  if (typeof ResizeObserver < "u") {
    const s = S.useMemo(() => new ResizeObserver((o) => {
      const i = o[0].target;
      i.offsetParent !== null && e(i);
    }), [e]);
    r = (o) => {
      o && t ? (s.observe(o), n.current = o) : (n.current && s.unobserve(n.current), n.current = null);
    };
  }
  return { ref: n, callbackRef: r };
}
function Ht(e, t = !0) {
  return Pr(e, t).callbackRef;
}
function Fi(e, t, n, r, s, o, i) {
  const a = S.useCallback(
    (l) => {
      const u = Wi(l.children, t, "offsetHeight", s);
      let c = l.parentElement;
      for (; !c.dataset.virtuosoScroller; )
        c = c.parentElement;
      const p = c.lastElementChild.dataset.viewportType === "window", y = i ? i.scrollTop : p ? window.pageYOffset || document.documentElement.scrollTop : c.scrollTop, D = i ? i.scrollHeight : p ? document.documentElement.scrollHeight : c.scrollHeight, M = i ? i.offsetHeight : p ? window.innerHeight : c.offsetHeight;
      r({
        scrollTop: Math.max(y, 0),
        scrollHeight: D,
        viewportHeight: M
      }), o == null || o(Ai("row-gap", getComputedStyle(l).rowGap, s)), u !== null && e(u);
    },
    [e, t, s, o, i, r]
  );
  return Pr(a, n);
}
function Wi(e, t, n, r) {
  const s = e.length;
  if (s === 0)
    return null;
  const o = [];
  for (let i = 0; i < s; i++) {
    const a = e.item(i);
    if (!a || a.dataset.index === void 0)
      continue;
    const l = parseInt(a.dataset.index), u = parseFloat(a.dataset.knownSize), c = t(a, n);
    if (c === 0 && r("Zero-sized element, this should not happen", { child: a }, je.ERROR), c === u)
      continue;
    const p = o[o.length - 1];
    o.length === 0 || p.size !== c || p.endIndex !== l - 1 ? o.push({ startIndex: l, endIndex: l, size: c }) : o[o.length - 1].endIndex++;
  }
  return o;
}
function Ai(e, t, n) {
  return t !== "normal" && !(t != null && t.endsWith("px")) && n(`${e} was not resolved to pixel value correctly`, t, je.WARN), t === "normal" ? 0 : parseInt(t ?? "0", 10);
}
function Ot(e, t) {
  return Math.round(e.getBoundingClientRect()[t]);
}
function zs(e, t) {
  return Math.abs(e - t) < 1.01;
}
function Bs(e, t, n, r = Qt, s) {
  const o = S.useRef(null), i = S.useRef(null), a = S.useRef(null), l = S.useCallback(
    (p) => {
      const y = p.target, D = y === window || y === document, M = D ? window.pageYOffset || document.documentElement.scrollTop : y.scrollTop, d = D ? document.documentElement.scrollHeight : y.scrollHeight, v = D ? window.innerHeight : y.offsetHeight, h = () => {
        e({
          scrollTop: Math.max(M, 0),
          scrollHeight: d,
          viewportHeight: v
        });
      };
      p.suppressFlushSync ? h() : hi.flushSync(h), i.current !== null && (M === i.current || M <= 0 || M === d - v) && (i.current = null, t(!0), a.current && (clearTimeout(a.current), a.current = null));
    },
    [e, t]
  );
  S.useEffect(() => {
    const p = s || o.current;
    return r(s || o.current), l({ target: p, suppressFlushSync: !0 }), p.addEventListener("scroll", l, { passive: !0 }), () => {
      r(null), p.removeEventListener("scroll", l);
    };
  }, [o, l, n, r, s]);
  function u(p) {
    const y = o.current;
    if (!y || "offsetHeight" in y && y.offsetHeight === 0)
      return;
    const D = p.behavior === "smooth";
    let M, d, v;
    y === window ? (d = Math.max(Ot(document.documentElement, "height"), document.documentElement.scrollHeight), M = window.innerHeight, v = document.documentElement.scrollTop) : (d = y.scrollHeight, M = Ot(y, "height"), v = y.scrollTop);
    const h = d - M;
    if (p.top = Math.ceil(Math.max(Math.min(h, p.top), 0)), zs(M, d) || p.top === v) {
      e({ scrollTop: v, scrollHeight: d, viewportHeight: M }), D && t(!0);
      return;
    }
    D ? (i.current = p.top, a.current && clearTimeout(a.current), a.current = setTimeout(() => {
      a.current = null, i.current = null, t(!0);
    }, 1e3)) : i.current = null, y.scrollTo(p);
  }
  function c(p) {
    o.current.scrollBy(p);
  }
  return { scrollerRef: o, scrollByCallback: c, scrollToCallback: u };
}
const Ne = ie(
  () => {
    const e = ee(), t = ee(), n = k(0), r = ee(), s = k(0), o = ee(), i = ee(), a = k(0), l = k(0), u = k(0), c = k(0), p = ee(), y = ee(), D = k(!1);
    return H(
      _(
        e,
        R(({ scrollTop: M }) => M)
      ),
      t
    ), H(
      _(
        e,
        R(({ scrollHeight: M }) => M)
      ),
      i
    ), H(t, s), {
      // input
      scrollContainerState: e,
      scrollTop: t,
      viewportHeight: o,
      headerHeight: a,
      fixedHeaderHeight: l,
      fixedFooterHeight: u,
      footerHeight: c,
      scrollHeight: i,
      smoothScrollTargetReached: r,
      // signals
      scrollTo: p,
      scrollBy: y,
      // state
      statefulScrollTop: s,
      deviation: n,
      scrollingInProgress: D
    };
  },
  [],
  { singleton: !0 }
), cn = { lvl: 0 };
function js(e, t, n, r = cn, s = cn) {
  return { k: e, v: t, lvl: n, l: r, r: s };
}
function ce(e) {
  return e === cn;
}
function jt() {
  return cn;
}
function vr(e, t) {
  if (ce(e))
    return cn;
  const { k: n, l: r, r: s } = e;
  if (t === n) {
    if (ce(r))
      return s;
    if (ce(s))
      return r;
    {
      const [o, i] = $s(r);
      return En(xe(e, { k: o, v: i, l: Zs(r) }));
    }
  } else
    return t < n ? En(xe(e, { l: vr(r, t) })) : En(xe(e, { r: vr(s, t) }));
}
function dn(e, t) {
  if (!ce(e))
    return t === e.k ? e.v : t < e.k ? dn(e.l, t) : dn(e.r, t);
}
function nt(e, t, n = "k") {
  if (ce(e))
    return [-1 / 0, void 0];
  if (Number(e[n]) === t)
    return [e.k, e.v];
  if (Number(e[n]) < t) {
    const r = nt(e.r, t, n);
    return r[0] === -1 / 0 ? [e.k, e.v] : r;
  }
  return nt(e.l, t, n);
}
function Ge(e, t, n) {
  return ce(e) ? js(t, n, 1) : t === e.k ? xe(e, { k: t, v: n }) : t < e.k ? ws(xe(e, { l: Ge(e.l, t, n) })) : ws(xe(e, { r: Ge(e.r, t, n) }));
}
function xr(e, t, n) {
  if (ce(e))
    return [];
  const { k: r, v: s, l: o, r: i } = e;
  let a = [];
  return r > t && (a = a.concat(xr(o, t, n))), r >= t && r <= n && a.push({ k: r, v: s }), r <= n && (a = a.concat(xr(i, t, n))), a;
}
function Nt(e) {
  return ce(e) ? [] : [...Nt(e.l), { k: e.k, v: e.v }, ...Nt(e.r)];
}
function $s(e) {
  return ce(e.r) ? [e.k, e.v] : $s(e.r);
}
function Zs(e) {
  return ce(e.r) ? e.l : En(xe(e, { r: Zs(e.r) }));
}
function xe(e, t) {
  return js(
    t.k !== void 0 ? t.k : e.k,
    t.v !== void 0 ? t.v : e.v,
    t.lvl !== void 0 ? t.lvl : e.lvl,
    t.l !== void 0 ? t.l : e.l,
    t.r !== void 0 ? t.r : e.r
  );
}
function hr(e) {
  return ce(e) || e.lvl > e.r.lvl;
}
function ws(e) {
  return Tr(Ks(e));
}
function En(e) {
  const { l: t, r: n, lvl: r } = e;
  if (n.lvl >= r - 1 && t.lvl >= r - 1)
    return e;
  if (r > n.lvl + 1) {
    if (hr(t))
      return Ks(xe(e, { lvl: r - 1 }));
    if (!ce(t) && !ce(t.r))
      return xe(t.r, {
        l: xe(t, { r: t.r.l }),
        r: xe(e, {
          l: t.r.r,
          lvl: r - 1
        }),
        lvl: r
      });
    throw new Error("Unexpected empty nodes");
  } else {
    if (hr(e))
      return Tr(xe(e, { lvl: r - 1 }));
    if (!ce(n) && !ce(n.l)) {
      const s = n.l, o = hr(s) ? n.lvl - 1 : n.lvl;
      return xe(s, {
        l: xe(e, {
          r: s.l,
          lvl: r - 1
        }),
        r: Tr(xe(n, { l: s.r, lvl: o })),
        lvl: s.lvl + 1
      });
    } else
      throw new Error("Unexpected empty nodes");
  }
}
function Kn(e, t, n) {
  if (ce(e))
    return [];
  const r = nt(e, t)[0];
  return Vi(xr(e, r, n));
}
function qs(e, t) {
  const n = e.length;
  if (n === 0)
    return [];
  let { index: r, value: s } = t(e[0]);
  const o = [];
  for (let i = 1; i < n; i++) {
    const { index: a, value: l } = t(e[i]);
    o.push({ start: r, end: a - 1, value: s }), r = a, s = l;
  }
  return o.push({ start: r, end: 1 / 0, value: s }), o;
}
function Vi(e) {
  return qs(e, ({ k: t, v: n }) => ({ index: t, value: n }));
}
function Tr(e) {
  const { r: t, lvl: n } = e;
  return !ce(t) && !ce(t.r) && t.lvl === n && t.r.lvl === n ? xe(t, { l: xe(e, { r: t.l }), lvl: n + 1 }) : e;
}
function Ks(e) {
  const { l: t } = e;
  return !ce(t) && t.lvl === e.lvl ? xe(t, { r: xe(e, { l: t.r }) }) : e;
}
function Fn(e, t, n, r = 0) {
  let s = e.length - 1;
  for (; r <= s; ) {
    const o = Math.floor((r + s) / 2), i = e[o], a = n(i, t);
    if (a === 0)
      return o;
    if (a === -1) {
      if (s - r < 2)
        return o - 1;
      s = o - 1;
    } else {
      if (s === r)
        return o;
      r = o + 1;
    }
  }
  throw new Error(`Failed binary finding record in array - ${e.join(",")}, searched for ${t}`);
}
function Js(e, t, n) {
  return e[Fn(e, t, n)];
}
function Ui(e, t, n, r) {
  const s = Fn(e, t, r), o = Fn(e, n, r, s);
  return e.slice(s, o + 1);
}
const Lr = ie(
  () => ({ recalcInProgress: k(!1) }),
  [],
  { singleton: !0 }
);
function Gi(e) {
  const { size: t, startIndex: n, endIndex: r } = e;
  return (s) => s.start === n && (s.end === r || s.end === 1 / 0) && s.value === t;
}
function Ss(e, t) {
  let n = 0, r = 0;
  for (; n < e; )
    n += t[r + 1] - t[r] - 1, r++;
  return r - (n === e ? 0 : 1);
}
function zi(e, t) {
  let n = ce(e) ? 0 : 1 / 0;
  for (const r of t) {
    const { size: s, startIndex: o, endIndex: i } = r;
    if (n = Math.min(n, o), ce(e)) {
      e = Ge(e, 0, s);
      continue;
    }
    const a = Kn(e, o - 1, i + 1);
    if (a.some(Gi(r)))
      continue;
    let l = !1, u = !1;
    for (const { start: c, end: p, value: y } of a)
      l ? (i >= c || s === y) && (e = vr(e, c)) : (u = y !== s, l = !0), p > i && i >= c && y !== s && (e = Ge(e, i + 1, y));
    u && (e = Ge(e, o, s));
  }
  return [e, n];
}
function Bi() {
  return {
    offsetTree: [],
    sizeTree: jt(),
    groupOffsetTree: jt(),
    lastIndex: 0,
    lastOffset: 0,
    lastSize: 0,
    groupIndices: []
  };
}
function Hr({ index: e }, t) {
  return t === e ? 0 : t < e ? -1 : 1;
}
function ji({ offset: e }, t) {
  return t === e ? 0 : t < e ? -1 : 1;
}
function $i(e) {
  return { index: e.index, value: e };
}
function Zi(e, t, n, r = 0) {
  return r > 0 && (t = Math.max(t, Js(e, r, Hr).offset)), qs(Ui(e, t, n, ji), $i);
}
function kr(e, t, n, r) {
  let s = e, o = 0, i = 0, a = 0, l = 0;
  if (t !== 0) {
    l = Fn(s, t - 1, Hr), a = s[l].offset;
    const c = nt(n, t - 1);
    o = c[0], i = c[1], s.length && s[l].size === nt(n, t)[1] && (l -= 1), s = s.slice(0, l + 1);
  } else
    s = [];
  for (const { start: u, value: c } of Kn(n, t, 1 / 0)) {
    const p = u - o, y = p * i + a + p * r;
    s.push({
      offset: y,
      size: c,
      index: u
    }), o = u, a = y, i = c;
  }
  return {
    offsetTree: s,
    lastIndex: o,
    lastOffset: a,
    lastSize: i
  };
}
function qi(e, [t, n, r, s]) {
  t.length > 0 && r("received item sizes", t, je.DEBUG);
  const o = e.sizeTree;
  let i = o, a = 0;
  if (n.length > 0 && ce(o) && t.length === 2) {
    const y = t[0].size, D = t[1].size;
    i = n.reduce((M, d) => Ge(Ge(M, d, y), d + 1, D), i);
  } else
    [i, a] = zi(i, t);
  if (i === o)
    return e;
  const { offsetTree: l, lastIndex: u, lastSize: c, lastOffset: p } = kr(e.offsetTree, a, i, s);
  return {
    sizeTree: i,
    offsetTree: l,
    lastIndex: u,
    lastOffset: p,
    lastSize: c,
    groupOffsetTree: n.reduce((y, D) => Ge(y, D, fn(D, l, s)), jt()),
    groupIndices: n
  };
}
function fn(e, t, n) {
  if (t.length === 0)
    return 0;
  const { offset: r, index: s, size: o } = Js(t, e, Hr), i = e - s, a = o * i + (i - 1) * n + r;
  return a > 0 ? a + n : a;
}
function Ki(e) {
  return typeof e.groupIndex < "u";
}
function Qs(e, t, n) {
  if (Ki(e))
    return t.groupIndices[e.groupIndex] + 1;
  {
    const r = e.index === "LAST" ? n : e.index;
    let s = Xs(r, t);
    return s = Math.max(0, s, Math.min(n, s)), s;
  }
}
function Xs(e, t) {
  if (!Jn(t))
    return e;
  let n = 0;
  for (; t.groupIndices[n] <= e + n; )
    n++;
  return e + n;
}
function Jn(e) {
  return !ce(e.groupOffsetTree);
}
function Ji(e) {
  return Nt(e).map(({ k: t, v: n }, r, s) => {
    const o = s[r + 1], i = o ? o.k - 1 : 1 / 0;
    return { startIndex: t, endIndex: i, size: n };
  });
}
const Qi = {
  offsetHeight: "height",
  offsetWidth: "width"
}, ct = ie(
  ([{ log: e }, { recalcInProgress: t }]) => {
    const n = ee(), r = ee(), s = Ee(r, 0), o = ee(), i = ee(), a = k(0), l = k([]), u = k(void 0), c = k(void 0), p = k((g, f) => Ot(g, Qi[f])), y = k(void 0), D = k(0), M = Bi(), d = Ee(
      _(n, Z(l, e, D), lt(qi, M), me()),
      M
    ), v = Ee(
      _(
        l,
        me(),
        lt((g, f) => ({ prev: g.current, current: f }), {
          prev: [],
          current: []
        }),
        R(({ prev: g }) => g)
      ),
      []
    );
    H(
      _(
        l,
        U((g) => g.length > 0),
        Z(d, D),
        R(([g, f, b]) => {
          const Y = g.reduce((N, F, G) => Ge(N, F, fn(F, f.offsetTree, b) || G), jt());
          return {
            ...f,
            groupIndices: g,
            groupOffsetTree: Y
          };
        })
      ),
      d
    ), H(
      _(
        r,
        Z(d),
        U(([g, { lastIndex: f }]) => g < f),
        R(([g, { lastIndex: f, lastSize: b }]) => [
          {
            startIndex: g,
            endIndex: f,
            size: b
          }
        ])
      ),
      n
    ), H(u, c);
    const h = Ee(
      _(
        u,
        R((g) => g === void 0)
      ),
      !0
    );
    H(
      _(
        c,
        U((g) => g !== void 0 && ce(ze(d).sizeTree)),
        R((g) => [{ startIndex: 0, endIndex: 0, size: g }])
      ),
      n
    );
    const m = Be(
      _(
        n,
        Z(d),
        lt(
          ({ sizes: g }, [f, b]) => ({
            changed: b !== g,
            sizes: b
          }),
          { changed: !1, sizes: M }
        ),
        R((g) => g.changed)
      )
    );
    le(
      _(
        a,
        lt(
          (g, f) => ({ diff: g.prev - f, prev: f }),
          { diff: 0, prev: 0 }
        ),
        R((g) => g.diff)
      ),
      (g) => {
        const { groupIndices: f } = ze(d);
        if (g > 0)
          j(t, !0), j(o, g + Ss(g, f));
        else if (g < 0) {
          const b = ze(v);
          b.length > 0 && (g -= Ss(-g, b)), j(i, g);
        }
      }
    ), le(_(a, Z(e)), ([g, f]) => {
      g < 0 && f(
        "`firstItemIndex` prop should not be set to less than zero. If you don't know the total count, just use a very high value",
        { firstItemIndex: a },
        je.ERROR
      );
    });
    const T = Be(o);
    H(
      _(
        o,
        Z(d),
        R(([g, f]) => {
          const b = f.groupIndices.length > 0, Y = [], N = f.lastSize;
          if (b) {
            const F = dn(f.sizeTree, 0);
            let G = 0, q = 0;
            for (; G < g; ) {
              const z = f.groupIndices[q], se = f.groupIndices.length === q + 1 ? 1 / 0 : f.groupIndices[q + 1] - z - 1;
              Y.push({
                startIndex: z,
                endIndex: z,
                size: F
              }), Y.push({
                startIndex: z + 1,
                endIndex: z + 1 + se - 1,
                size: N
              }), q++, G += se + 1;
            }
            const ae = Nt(f.sizeTree);
            return G !== g && ae.shift(), ae.reduce(
              (z, { k: se, v: _e }) => {
                let Pe = z.ranges;
                return z.prevSize !== 0 && (Pe = [
                  ...z.ranges,
                  {
                    startIndex: z.prevIndex,
                    endIndex: se + g - 1,
                    size: z.prevSize
                  }
                ]), {
                  ranges: Pe,
                  prevIndex: se + g,
                  prevSize: _e
                };
              },
              {
                ranges: Y,
                prevIndex: g,
                prevSize: 0
              }
            ).ranges;
          }
          return Nt(f.sizeTree).reduce(
            (F, { k: G, v: q }) => ({
              ranges: [...F.ranges, { startIndex: F.prevIndex, endIndex: G + g - 1, size: F.prevSize }],
              prevIndex: G + g,
              prevSize: q
            }),
            {
              ranges: [],
              prevIndex: 0,
              prevSize: N
            }
          ).ranges;
        })
      ),
      n
    );
    const E = Be(
      _(
        i,
        Z(d, D),
        R(([g, { offsetTree: f }, b]) => {
          const Y = -g;
          return fn(Y, f, b);
        })
      )
    );
    return H(
      _(
        i,
        Z(d, D),
        R(([g, f, b]) => {
          if (f.groupIndices.length > 0) {
            if (ce(f.sizeTree))
              return f;
            let N = jt();
            const F = ze(v);
            let G = 0, q = 0, ae = 0;
            for (; G < -g; ) {
              ae = F[q];
              const z = F[q + 1] - ae - 1;
              q++, G += z + 1;
            }
            if (N = Nt(f.sizeTree).reduce((z, { k: se, v: _e }) => Ge(z, Math.max(0, se + g), _e), N), G !== -g) {
              const z = dn(f.sizeTree, ae);
              N = Ge(N, 0, z);
              const se = nt(f.sizeTree, -g + 1)[1];
              N = Ge(N, 1, se);
            }
            return {
              ...f,
              sizeTree: N,
              ...kr(f.offsetTree, 0, N, b)
            };
          } else {
            const N = Nt(f.sizeTree).reduce((F, { k: G, v: q }) => Ge(F, Math.max(0, G + g), q), jt());
            return {
              ...f,
              sizeTree: N,
              ...kr(f.offsetTree, 0, N, b)
            };
          }
        })
      ),
      d
    ), {
      // input
      data: y,
      totalCount: r,
      sizeRanges: n,
      groupIndices: l,
      defaultItemSize: c,
      fixedItemSize: u,
      unshiftWith: o,
      shiftWith: i,
      shiftWithOffset: E,
      beforeUnshiftWith: T,
      firstItemIndex: a,
      gap: D,
      // output
      sizes: d,
      listRefresh: m,
      statefulTotalCount: s,
      trackItemSizes: h,
      itemSize: p
    };
  },
  pe(Rt, Lr),
  { singleton: !0 }
), Xi = typeof document < "u" && "scrollBehavior" in document.documentElement.style;
function eo(e) {
  const t = typeof e == "number" ? { index: e } : e;
  return t.align || (t.align = "start"), (!t.behavior || !Xi) && (t.behavior = "auto"), t.offset || (t.offset = 0), t;
}
const _n = ie(
  ([
    { sizes: e, totalCount: t, listRefresh: n, gap: r },
    {
      scrollingInProgress: s,
      viewportHeight: o,
      scrollTo: i,
      smoothScrollTargetReached: a,
      headerHeight: l,
      footerHeight: u,
      fixedHeaderHeight: c,
      fixedFooterHeight: p
    },
    { log: y }
  ]) => {
    const D = ee(), M = k(0);
    let d = null, v = null, h = null;
    function m() {
      d && (d(), d = null), h && (h(), h = null), v && (clearTimeout(v), v = null), j(s, !1);
    }
    return H(
      _(
        D,
        Z(e, o, t, M, l, u, y),
        Z(r, c, p),
        R(
          ([
            [T, E, g, f, b, Y, N, F],
            G,
            q,
            ae
          ]) => {
            const A = eo(T), { align: z, behavior: se, offset: _e } = A, Pe = f - 1, Me = Qs(A, E, Pe);
            let De = fn(Me, E.offsetTree, G) + Y;
            z === "end" ? (De += q + nt(E.sizeTree, Me)[1] - g + ae, Me === Pe && (De += N)) : z === "center" ? De += (q + nt(E.sizeTree, Me)[1] - g + ae) / 2 : De -= b, _e && (De += _e);
            const We = (Ae) => {
              m(), Ae ? (F("retrying to scroll to", { location: T }, je.DEBUG), j(D, T)) : F("list did not change, scroll successful", {}, je.DEBUG);
            };
            if (m(), se === "smooth") {
              let Ae = !1;
              h = le(n, (Tt) => {
                Ae = Ae || Tt;
              }), d = wt(a, () => {
                We(Ae);
              });
            } else
              d = wt(_(n, ea(150)), We);
            return v = setTimeout(() => {
              m();
            }, 1200), j(s, !0), F("scrolling from index to", { index: Me, top: De, behavior: se }, je.DEBUG), { top: De, behavior: se };
          }
        )
      ),
      i
    ), {
      scrollToIndex: D,
      topListHeight: M
    };
  },
  pe(ct, Ne, Rt),
  { singleton: !0 }
);
function ea(e) {
  return (t) => {
    const n = setTimeout(() => {
      t(!1);
    }, e);
    return (r) => {
      r && (t(!0), clearTimeout(n));
    };
  };
}
const hn = "up", on = "down", ta = "none", na = {
  atBottom: !1,
  notAtBottomBecause: "NOT_SHOWING_LAST_ITEM",
  state: {
    offsetBottom: 0,
    scrollTop: 0,
    viewportHeight: 0,
    scrollHeight: 0
  }
}, ra = 0, wn = ie(([{ scrollContainerState: e, scrollTop: t, viewportHeight: n, headerHeight: r, footerHeight: s, scrollBy: o }]) => {
  const i = k(!1), a = k(!0), l = ee(), u = ee(), c = k(4), p = k(ra), y = Ee(
    _(
      _s(_(V(t), Kt(1), pt(!0)), _(V(t), Kt(1), pt(!1), ys(100))),
      me()
    ),
    !1
  ), D = Ee(
    _(_s(_(o, pt(!0)), _(o, pt(!1), ys(200))), me()),
    !1
  );
  H(
    _(
      ke(V(t), V(p)),
      R(([m, T]) => m <= T),
      me()
    ),
    a
  ), H(_(a, It(50)), u);
  const M = Be(
    _(
      ke(e, V(n), V(r), V(s), V(c)),
      lt((m, [{ scrollTop: T, scrollHeight: E }, g, f, b, Y]) => {
        const N = T + g - E > -Y, F = {
          viewportHeight: g,
          scrollTop: T,
          scrollHeight: E
        };
        if (N) {
          let q, ae;
          return T > m.state.scrollTop ? (q = "SCROLLED_DOWN", ae = m.state.scrollTop - T) : (q = "SIZE_DECREASED", ae = m.state.scrollTop - T || m.scrollTopDelta), {
            atBottom: !0,
            state: F,
            atBottomBecause: q,
            scrollTopDelta: ae
          };
        }
        let G;
        return F.scrollHeight > m.state.scrollHeight ? G = "SIZE_INCREASED" : g < m.state.viewportHeight ? G = "VIEWPORT_HEIGHT_DECREASING" : T < m.state.scrollTop ? G = "SCROLLING_UPWARDS" : G = "NOT_FULLY_SCROLLED_TO_LAST_ITEM_BOTTOM", {
          atBottom: !1,
          notAtBottomBecause: G,
          state: F
        };
      }, na),
      me((m, T) => m && m.atBottom === T.atBottom)
    )
  ), d = Ee(
    _(
      e,
      lt(
        (m, { scrollTop: T, scrollHeight: E, viewportHeight: g }) => {
          if (zs(m.scrollHeight, E))
            return {
              scrollTop: T,
              scrollHeight: E,
              jump: 0,
              changed: !1
            };
          {
            const f = E - (T + g) < 1;
            return m.scrollTop !== T && f ? {
              scrollHeight: E,
              scrollTop: T,
              jump: m.scrollTop - T,
              changed: !0
            } : {
              scrollHeight: E,
              scrollTop: T,
              jump: 0,
              changed: !0
            };
          }
        },
        { scrollHeight: 0, jump: 0, scrollTop: 0, changed: !1 }
      ),
      U((m) => m.changed),
      R((m) => m.jump)
    ),
    0
  );
  H(
    _(
      M,
      R((m) => m.atBottom)
    ),
    i
  ), H(_(i, It(50)), l);
  const v = k(on);
  H(
    _(
      e,
      R(({ scrollTop: m }) => m),
      me(),
      lt(
        (m, T) => ze(D) ? { direction: m.direction, prevScrollTop: T } : { direction: T < m.prevScrollTop ? hn : on, prevScrollTop: T },
        { direction: on, prevScrollTop: 0 }
      ),
      R((m) => m.direction)
    ),
    v
  ), H(_(e, It(50), pt(ta)), v);
  const h = k(0);
  return H(
    _(
      y,
      U((m) => !m),
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      pt(0)
    ),
    h
  ), H(
    _(
      t,
      It(100),
      Z(y),
      U(([m, T]) => !!T),
      lt(([m, T], [E]) => [T, E], [0, 0]),
      R(([m, T]) => T - m)
    ),
    h
  ), {
    isScrolling: y,
    isAtTop: a,
    isAtBottom: i,
    atBottomState: M,
    atTopStateChange: u,
    atBottomStateChange: l,
    scrollDirection: v,
    atBottomThreshold: c,
    atTopThreshold: p,
    scrollVelocity: h,
    lastJumpDueToItemResize: d
  };
}, pe(Ne)), Yt = ie(
  ([{ log: e }]) => {
    const t = k(!1), n = Be(
      _(
        t,
        U((r) => r),
        me()
      )
    );
    return le(t, (r) => {
      r && ze(e)("props updated", {}, je.DEBUG);
    }), { propsReady: t, didMount: n };
  },
  pe(Rt),
  { singleton: !0 }
);
function Fr(e, t) {
  e == 0 ? t() : requestAnimationFrame(() => Fr(e - 1, t));
}
function Wr(e, t) {
  const n = t - 1;
  return typeof e == "number" ? e : e.index === "LAST" ? n : e.index;
}
const Sn = ie(
  ([{ sizes: e, listRefresh: t, defaultItemSize: n }, { scrollTop: r }, { scrollToIndex: s }, { didMount: o }]) => {
    const i = k(!0), a = k(0), l = k(!1);
    return H(
      _(
        o,
        Z(a),
        U(([u, c]) => !!c),
        pt(!1)
      ),
      i
    ), le(
      _(
        ke(t, o),
        Z(i, e, n, l),
        U(([[, u], c, { sizeTree: p }, y, D]) => u && (!ce(p) || Er(y)) && !c && !D),
        Z(a)
      ),
      ([, u]) => {
        j(l, !0), Fr(3, () => {
          wt(r, () => j(i, !0)), j(s, u);
        });
      }
    ), {
      scrolledToInitialItem: i,
      initialTopMostItemIndex: a
    };
  },
  pe(ct, Ne, _n, Yt),
  { singleton: !0 }
);
function vs(e) {
  return e ? e === "smooth" ? "smooth" : "auto" : !1;
}
const sa = (e, t) => typeof e == "function" ? vs(e(t)) : t && vs(e), oa = ie(
  ([
    { totalCount: e, listRefresh: t },
    { isAtBottom: n, atBottomState: r },
    { scrollToIndex: s },
    { scrolledToInitialItem: o },
    { propsReady: i, didMount: a },
    { log: l },
    { scrollingInProgress: u }
  ]) => {
    const c = k(!1), p = ee();
    let y = null;
    function D(d) {
      j(s, {
        index: "LAST",
        align: "end",
        behavior: d
      });
    }
    le(
      _(
        ke(_(V(e), Kt(1)), a),
        Z(V(c), n, o, u),
        R(([[d, v], h, m, T, E]) => {
          let g = v && T, f = "auto";
          return g && (f = sa(h, m || E), g = g && !!f), { totalCount: d, shouldFollow: g, followOutputBehavior: f };
        }),
        U(({ shouldFollow: d }) => d)
      ),
      ({ totalCount: d, followOutputBehavior: v }) => {
        y && (y(), y = null), y = wt(t, () => {
          ze(l)("following output to ", { totalCount: d }, je.DEBUG), D(v), y = null;
        });
      }
    );
    function M(d) {
      const v = wt(r, (h) => {
        d && !h.atBottom && h.notAtBottomBecause === "SIZE_INCREASED" && !y && (ze(l)("scrolling to bottom due to increased size", {}, je.DEBUG), D("auto"));
      });
      setTimeout(v, 100);
    }
    return le(
      _(
        ke(V(c), e, i),
        U(([d, , v]) => d && v),
        lt(
          ({ value: d }, [, v]) => ({ refreshed: d === v, value: v }),
          { refreshed: !1, value: 0 }
        ),
        U(({ refreshed: d }) => d),
        Z(c, e)
      ),
      ([, d]) => {
        M(d !== !1);
      }
    ), le(p, () => {
      M(ze(c) !== !1);
    }), le(ke(V(c), r), ([d, v]) => {
      d && !v.atBottom && v.notAtBottomBecause === "VIEWPORT_HEIGHT_DECREASING" && D("auto");
    }), { followOutput: c, autoscrollToBottom: p };
  },
  pe(ct, wn, _n, Sn, Yt, Rt, Ne)
);
function ia(e) {
  return e.reduce(
    (t, n) => (t.groupIndices.push(t.totalCount), t.totalCount += n + 1, t),
    {
      totalCount: 0,
      groupIndices: []
    }
  );
}
const to = ie(([{ totalCount: e, groupIndices: t, sizes: n }, { scrollTop: r, headerHeight: s }]) => {
  const o = ee(), i = ee(), a = Be(_(o, R(ia)));
  return H(
    _(
      a,
      R((l) => l.totalCount)
    ),
    e
  ), H(
    _(
      a,
      R((l) => l.groupIndices)
    ),
    t
  ), H(
    _(
      ke(r, n, s),
      U(([l, u]) => Jn(u)),
      R(([l, u, c]) => nt(u.groupOffsetTree, Math.max(l - c, 0), "v")[0]),
      me(),
      R((l) => [l])
    ),
    i
  ), { groupCounts: o, topItemsIndexes: i };
}, pe(ct, Ne));
function mn(e, t) {
  return !!(e && e[0] === t[0] && e[1] === t[1]);
}
function no(e, t) {
  return !!(e && e.startIndex === t.startIndex && e.endIndex === t.endIndex);
}
const Wn = "top", An = "bottom", xs = "none";
function Ts(e, t, n) {
  return typeof e == "number" ? n === hn && t === Wn || n === on && t === An ? e : 0 : n === hn ? t === Wn ? e.main : e.reverse : t === An ? e.main : e.reverse;
}
function ks(e, t) {
  return typeof e == "number" ? e : e[t] || 0;
}
const Ar = ie(
  ([{ scrollTop: e, viewportHeight: t, deviation: n, headerHeight: r, fixedHeaderHeight: s }]) => {
    const o = ee(), i = k(0), a = k(0), l = k(0), u = Ee(
      _(
        ke(
          V(e),
          V(t),
          V(r),
          V(o, mn),
          V(l),
          V(i),
          V(s),
          V(n),
          V(a)
        ),
        R(
          ([
            c,
            p,
            y,
            [D, M],
            d,
            v,
            h,
            m,
            T
          ]) => {
            const E = c - m, g = v + h, f = Math.max(y - E, 0);
            let b = xs;
            const Y = ks(T, Wn), N = ks(T, An);
            return D -= m, D += y + h, M += y + h, M -= m, D > c + g - Y && (b = hn), M < c - f + p + N && (b = on), b !== xs ? [
              Math.max(E - y - Ts(d, Wn, b) - Y, 0),
              E - f - h + p + Ts(d, An, b) + N
            ] : null;
          }
        ),
        U((c) => c != null),
        me(mn)
      ),
      [0, 0]
    );
    return {
      // input
      listBoundary: o,
      overscan: l,
      topListHeight: i,
      increaseViewportBy: a,
      // output
      visibleRange: u
    };
  },
  pe(Ne),
  { singleton: !0 }
);
function aa(e, t, n) {
  if (Jn(t)) {
    const r = Xs(e, t);
    return [
      { index: nt(t.groupOffsetTree, r)[0], size: 0, offset: 0 },
      { index: r, size: 0, offset: 0, data: n && n[0] }
    ];
  }
  return [{ index: e, size: 0, offset: 0, data: n && n[0] }];
}
const mr = {
  items: [],
  topItems: [],
  offsetTop: 0,
  offsetBottom: 0,
  top: 0,
  bottom: 0,
  topListHeight: 0,
  totalCount: 0,
  firstItemIndex: 0
};
function Is(e, t, n) {
  if (e.length === 0)
    return [];
  if (!Jn(t))
    return e.map((u) => ({ ...u, index: u.index + n, originalIndex: u.index }));
  const r = e[0].index, s = e[e.length - 1].index, o = [], i = Kn(t.groupOffsetTree, r, s);
  let a, l = 0;
  for (const u of e) {
    (!a || a.end < u.index) && (a = i.shift(), l = t.groupIndices.indexOf(a.start));
    let c;
    u.index === a.start ? c = {
      type: "group",
      index: l
    } : c = {
      index: u.index - (l + 1) + n,
      groupIndex: l
    }, o.push({
      ...c,
      size: u.size,
      offset: u.offset,
      originalIndex: u.index,
      data: u.data
    });
  }
  return o;
}
function Nn(e, t, n, r, s, o) {
  const { lastSize: i, lastOffset: a, lastIndex: l } = s;
  let u = 0, c = 0;
  if (e.length > 0) {
    u = e[0].offset;
    const d = e[e.length - 1];
    c = d.offset + d.size;
  }
  const p = n - l, y = a + p * i + (p - 1) * r, D = u, M = y - c;
  return {
    items: Is(e, s, o),
    topItems: Is(t, s, o),
    topListHeight: t.reduce((d, v) => v.size + d, 0),
    offsetTop: u,
    offsetBottom: M,
    top: D,
    bottom: c,
    totalCount: n,
    firstItemIndex: o
  };
}
function ro(e, t, n, r, s, o) {
  let i = 0;
  if (n.groupIndices.length > 0)
    for (const c of n.groupIndices) {
      if (c - i >= e)
        break;
      i++;
    }
  const a = e + i, l = Wr(t, a), u = Array.from({ length: a }).map((c, p) => ({
    index: p + l,
    size: 0,
    offset: 0,
    data: o[p + l]
  }));
  return Nn(u, [], a, s, n, r);
}
const Ft = ie(
  ([
    { sizes: e, totalCount: t, data: n, firstItemIndex: r, gap: s },
    o,
    { visibleRange: i, listBoundary: a, topListHeight: l },
    { scrolledToInitialItem: u, initialTopMostItemIndex: c },
    { topListHeight: p },
    y,
    { didMount: D },
    { recalcInProgress: M }
  ]) => {
    const d = k([]), v = k(0), h = ee();
    H(o.topItemsIndexes, d);
    const m = Ee(
      _(
        ke(
          D,
          M,
          V(i, mn),
          V(t),
          V(e),
          V(c),
          u,
          V(d),
          V(r),
          V(s),
          n
        ),
        U(([f, b, , Y, , , , , , , N]) => {
          const F = N && N.length !== Y;
          return f && !b && !F;
        }),
        R(
          ([
            ,
            ,
            [f, b],
            Y,
            N,
            F,
            G,
            q,
            ae,
            A,
            z
          ]) => {
            const se = N, { sizeTree: _e, offsetTree: Pe } = se, Me = ze(v);
            if (Y === 0)
              return { ...mr, totalCount: Y };
            if (f === 0 && b === 0)
              return Me === 0 ? { ...mr, totalCount: Y } : ro(Me, F, N, ae, A, z || []);
            if (ce(_e))
              return Me > 0 ? null : Nn(
                aa(Wr(F, Y), se, z),
                [],
                Y,
                A,
                se,
                ae
              );
            const De = [];
            if (q.length > 0) {
              const Qe = q[0], x = q[q.length - 1];
              let K = 0;
              for (const te of Kn(_e, Qe, x)) {
                const oe = te.value, ve = Math.max(te.start, Qe), be = Math.min(te.end, x);
                for (let Se = ve; Se <= be; Se++)
                  De.push({ index: Se, size: oe, offset: K, data: z && z[Se] }), K += oe;
              }
            }
            if (!G)
              return Nn([], De, Y, A, se, ae);
            const We = q.length > 0 ? q[q.length - 1] + 1 : 0, Ae = Zi(Pe, f, b, We);
            if (Ae.length === 0)
              return null;
            const Tt = Y - 1, At = qn([], (Qe) => {
              for (const x of Ae) {
                const K = x.value;
                let te = K.offset, oe = x.start;
                const ve = K.size;
                if (K.offset < f) {
                  oe += Math.floor((f - K.offset + A) / (ve + A));
                  const Se = oe - x.start;
                  te += Se * ve + Se * A;
                }
                oe < We && (te += (We - oe) * ve, oe = We);
                const be = Math.min(x.end, Tt);
                for (let Se = oe; Se <= be && !(te >= b); Se++)
                  Qe.push({ index: Se, size: ve, offset: te, data: z && z[Se] }), te += ve + A;
              }
            });
            return Nn(At, De, Y, A, se, ae);
          }
        ),
        //@ts-expect-error filter needs to be fixed
        U((f) => f !== null),
        me()
      ),
      mr
    );
    H(
      _(
        n,
        U(Er),
        R((f) => f == null ? void 0 : f.length)
      ),
      t
    ), H(
      _(
        m,
        R((f) => f.topListHeight)
      ),
      p
    ), H(p, l), H(
      _(
        m,
        R((f) => [f.top, f.bottom])
      ),
      a
    ), H(
      _(
        m,
        R((f) => f.items)
      ),
      h
    );
    const T = Be(
      _(
        m,
        U(({ items: f }) => f.length > 0),
        Z(t, n),
        U(([{ items: f }, b]) => f[f.length - 1].originalIndex === b - 1),
        R(([, f, b]) => [f - 1, b]),
        me(mn),
        R(([f]) => f)
      )
    ), E = Be(
      _(
        m,
        It(200),
        U(({ items: f, topItems: b }) => f.length > 0 && f[0].originalIndex === b.length),
        R(({ items: f }) => f[0].index),
        me()
      )
    ), g = Be(
      _(
        m,
        U(({ items: f }) => f.length > 0),
        R(({ items: f }) => {
          let b = 0, Y = f.length - 1;
          for (; f[b].type === "group" && b < Y; )
            b++;
          for (; f[Y].type === "group" && Y > b; )
            Y--;
          return {
            startIndex: f[b].index,
            endIndex: f[Y].index
          };
        }),
        me(no)
      )
    );
    return { listState: m, topItemsIndexes: d, endReached: T, startReached: E, rangeChanged: g, itemsRendered: h, initialItemCount: v, ...y };
  },
  pe(
    ct,
    to,
    Ar,
    Sn,
    _n,
    wn,
    Yt,
    Lr
  ),
  { singleton: !0 }
), la = ie(
  ([{ sizes: e, firstItemIndex: t, data: n, gap: r }, { initialTopMostItemIndex: s }, { initialItemCount: o, listState: i }, { didMount: a }]) => (H(
    _(
      a,
      Z(o),
      U(([, l]) => l !== 0),
      Z(s, e, t, r, n),
      R(([[, l], u, c, p, y, D = []]) => ro(l, u, c, p, y, D))
    ),
    i
  ), {}),
  pe(ct, Sn, Ft, Yt),
  { singleton: !0 }
), so = ie(
  ([{ scrollVelocity: e }]) => {
    const t = k(!1), n = ee(), r = k(!1);
    return H(
      _(
        e,
        Z(r, t, n),
        U(([s, o]) => !!o),
        R(([s, o, i, a]) => {
          const { exit: l, enter: u } = o;
          if (i) {
            if (l(s, a))
              return !1;
          } else if (u(s, a))
            return !0;
          return i;
        }),
        me()
      ),
      t
    ), le(
      _(ke(t, e, n), Z(r)),
      ([[s, o, i], a]) => s && a && a.change && a.change(o, i)
    ), { isSeeking: t, scrollSeekConfiguration: r, scrollVelocity: e, scrollSeekRangeChanged: n };
  },
  pe(wn),
  { singleton: !0 }
), ua = ie(([{ topItemsIndexes: e }]) => {
  const t = k(0);
  return H(
    _(
      t,
      U((n) => n > 0),
      R((n) => Array.from({ length: n }).map((r, s) => s))
    ),
    e
  ), { topItemCount: t };
}, pe(Ft)), oo = ie(
  ([{ footerHeight: e, headerHeight: t, fixedHeaderHeight: n, fixedFooterHeight: r }, { listState: s }]) => {
    const o = ee(), i = Ee(
      _(
        ke(e, r, t, n, s),
        R(([a, l, u, c, p]) => a + l + u + c + p.offsetBottom + p.bottom)
      ),
      0
    );
    return H(V(i), o), { totalListHeight: i, totalListHeightChanged: o };
  },
  pe(Ne, Ft),
  { singleton: !0 }
);
function io(e) {
  let t = !1, n;
  return () => (t || (t = !0, n = e()), n);
}
const ca = io(() => /iP(ad|od|hone)/i.test(navigator.userAgent) && /WebKit/i.test(navigator.userAgent)), da = ie(
  ([
    { scrollBy: e, scrollTop: t, deviation: n, scrollingInProgress: r },
    { isScrolling: s, isAtBottom: o, scrollDirection: i, lastJumpDueToItemResize: a },
    { listState: l },
    { beforeUnshiftWith: u, shiftWithOffset: c, sizes: p, gap: y },
    { log: D },
    { recalcInProgress: M }
  ]) => {
    const d = Be(
      _(
        l,
        Z(a),
        lt(
          ([, h, m, T], [{ items: E, totalCount: g, bottom: f, offsetBottom: b }, Y]) => {
            const N = f + b;
            let F = 0;
            return m === g && h.length > 0 && E.length > 0 && (E[0].originalIndex === 0 && h[0].originalIndex === 0 || (F = N - T, F !== 0 && (F += Y))), [F, E, g, N];
          },
          [0, [], 0, 0]
        ),
        U(([h]) => h !== 0),
        Z(t, i, r, o, D, M),
        U(([, h, m, T, , , E]) => !E && !T && h !== 0 && m === hn),
        R(([[h], , , , , m]) => (m("Upward scrolling compensation", { amount: h }, je.DEBUG), h))
      )
    );
    function v(h) {
      h > 0 ? (j(e, { top: -h, behavior: "auto" }), j(n, 0)) : (j(n, 0), j(e, { top: -h, behavior: "auto" }));
    }
    return le(_(d, Z(n, s)), ([h, m, T]) => {
      T && ca() ? j(n, m - h) : v(-h);
    }), le(
      _(
        ke(Ee(s, !1), n, M),
        U(([h, m, T]) => !h && !T && m !== 0),
        R(([h, m]) => m),
        It(1)
      ),
      v
    ), H(
      _(
        c,
        R((h) => ({ top: -h }))
      ),
      e
    ), le(
      _(
        u,
        Z(p, y),
        R(([h, { lastSize: m, groupIndices: T, sizeTree: E }, g]) => {
          function f(b) {
            return b * (m + g);
          }
          if (T.length === 0)
            return f(h);
          {
            let b = 0;
            const Y = dn(E, 0);
            let N = 0, F = 0;
            for (; N < h; ) {
              N++, b += Y;
              let G = T.length === F + 1 ? 1 / 0 : T[F + 1] - T[F] - 1;
              N + G > h && (b -= Y, G = h - N + 1), N += G, b += f(G), F++;
            }
            return b;
          }
        })
      ),
      (h) => {
        j(n, h), requestAnimationFrame(() => {
          j(e, { top: h }), requestAnimationFrame(() => {
            j(n, 0), j(M, !1);
          });
        });
      }
    ), { deviation: n };
  },
  pe(Ne, wn, Ft, ct, Rt, Lr)
), fa = ie(
  ([{ didMount: e }, { scrollTo: t }, { listState: n }]) => {
    const r = k(0);
    return le(
      _(
        e,
        Z(r),
        U(([, s]) => s !== 0),
        R(([, s]) => ({ top: s }))
      ),
      (s) => {
        wt(
          _(
            n,
            Kt(1),
            U((o) => o.items.length > 1)
          ),
          () => {
            requestAnimationFrame(() => {
              j(t, s);
            });
          }
        );
      }
    ), {
      initialScrollTop: r
    };
  },
  pe(Yt, Ne, Ft),
  { singleton: !0 }
), ha = ie(
  ([{ viewportHeight: e }, { totalListHeight: t }]) => {
    const n = k(!1), r = Ee(
      _(
        ke(n, e, t),
        U(([s]) => s),
        R(([, s, o]) => Math.max(0, s - o)),
        It(0),
        me()
      ),
      0
    );
    return { alignToBottom: n, paddingTopAddition: r };
  },
  pe(Ne, oo),
  { singleton: !0 }
), Vr = ie(([{ scrollTo: e, scrollContainerState: t }]) => {
  const n = ee(), r = ee(), s = ee(), o = k(!1), i = k(void 0);
  return H(
    _(
      ke(n, r),
      R(([{ viewportHeight: a, scrollTop: l, scrollHeight: u }, { offsetTop: c }]) => ({
        scrollTop: Math.max(0, l - c),
        scrollHeight: u,
        viewportHeight: a
      }))
    ),
    t
  ), H(
    _(
      e,
      Z(r),
      R(([a, { offsetTop: l }]) => ({
        ...a,
        top: a.top + l
      }))
    ),
    s
  ), {
    // config
    useWindowScroll: o,
    customScrollParent: i,
    // input
    windowScrollContainerState: n,
    windowViewportRect: r,
    // signals
    windowScrollTo: s
  };
}, pe(Ne)), ma = ({
  itemTop: e,
  itemBottom: t,
  viewportTop: n,
  viewportBottom: r,
  locationParams: { behavior: s, align: o, ...i }
}) => e < n ? { ...i, behavior: s, align: o ?? "start" } : t > r ? { ...i, behavior: s, align: o ?? "end" } : null, pa = ie(
  ([
    { sizes: e, totalCount: t, gap: n },
    { scrollTop: r, viewportHeight: s, headerHeight: o, fixedHeaderHeight: i, fixedFooterHeight: a, scrollingInProgress: l },
    { scrollToIndex: u }
  ]) => {
    const c = ee();
    return H(
      _(
        c,
        Z(e, s, t, o, i, a, r),
        Z(n),
        R(([[p, y, D, M, d, v, h, m], T]) => {
          const { done: E, behavior: g, align: f, calculateViewLocation: b = ma, ...Y } = p, N = Qs(p, y, M - 1), F = fn(N, y.offsetTree, T) + d + v, G = F + nt(y.sizeTree, N)[1], q = m + v, ae = m + D - h, A = b({
            itemTop: F,
            itemBottom: G,
            viewportTop: q,
            viewportBottom: ae,
            locationParams: { behavior: g, align: f, ...Y }
          });
          return A ? E && wt(
            _(
              l,
              U((z) => z === !1),
              // skips the initial publish of false, and the cleanup call.
              // but if scrollingInProgress is true, we skip the initial publish.
              Kt(ze(l) ? 1 : 2)
            ),
            E
          ) : E && E(), A;
        }),
        U((p) => p !== null)
      ),
      u
    ), {
      scrollIntoView: c
    };
  },
  pe(ct, Ne, _n, Ft, Rt),
  { singleton: !0 }
), ga = ie(
  ([
    { sizes: e, sizeRanges: t },
    { scrollTop: n },
    { initialTopMostItemIndex: r },
    { didMount: s },
    { useWindowScroll: o, windowScrollContainerState: i, windowViewportRect: a }
  ]) => {
    const l = ee(), u = k(void 0), c = k(null), p = k(null);
    return H(i, c), H(a, p), le(
      _(l, Z(e, n, o, c, p)),
      ([y, D, M, d, v, h]) => {
        const m = Ji(D.sizeTree);
        d && v !== null && h !== null && (M = v.scrollTop - h.offsetTop), y({ ranges: m, scrollTop: M });
      }
    ), H(_(u, U(Er), R(ya)), r), H(
      _(
        s,
        Z(u),
        U(([, y]) => y !== void 0),
        me(),
        R(([, y]) => y.ranges)
      ),
      t
    ), {
      getState: l,
      restoreStateFrom: u
    };
  },
  pe(ct, Ne, Sn, Yt, Vr)
);
function ya(e) {
  return { offset: e.scrollTop, index: 0, align: "start" };
}
const _a = ie(
  ([
    e,
    t,
    n,
    r,
    s,
    o,
    i,
    a,
    l,
    u
  ]) => ({
    ...e,
    ...t,
    ...n,
    ...r,
    ...s,
    ...o,
    ...i,
    ...a,
    ...l,
    ...u
  }),
  pe(
    Ar,
    la,
    Yt,
    so,
    oo,
    fa,
    ha,
    Vr,
    pa,
    Rt
  )
), wa = ie(
  ([
    {
      totalCount: e,
      sizeRanges: t,
      fixedItemSize: n,
      defaultItemSize: r,
      trackItemSizes: s,
      itemSize: o,
      data: i,
      firstItemIndex: a,
      groupIndices: l,
      statefulTotalCount: u,
      gap: c,
      sizes: p
    },
    { initialTopMostItemIndex: y, scrolledToInitialItem: D },
    M,
    d,
    v,
    { listState: h, topItemsIndexes: m, ...T },
    { scrollToIndex: E },
    g,
    { topItemCount: f },
    { groupCounts: b },
    Y
  ]) => (H(T.rangeChanged, Y.scrollSeekRangeChanged), H(
    _(
      Y.windowViewportRect,
      R((N) => N.visibleHeight)
    ),
    M.viewportHeight
  ), {
    // input
    totalCount: e,
    data: i,
    firstItemIndex: a,
    sizeRanges: t,
    initialTopMostItemIndex: y,
    scrolledToInitialItem: D,
    topItemsIndexes: m,
    topItemCount: f,
    groupCounts: b,
    fixedItemHeight: n,
    defaultItemHeight: r,
    gap: c,
    ...v,
    // output
    statefulTotalCount: u,
    listState: h,
    scrollToIndex: E,
    trackItemSizes: s,
    itemSize: o,
    groupIndices: l,
    // exported from stateFlagsSystem
    ...T,
    // the bag of IO from featureGroup1System
    ...Y,
    ...M,
    sizes: p,
    ...d
  }),
  pe(
    ct,
    Sn,
    Ne,
    ga,
    oa,
    Ft,
    _n,
    da,
    ua,
    to,
    _a
  )
), pr = "-webkit-sticky", Ms = "sticky", ao = io(() => {
  if (typeof document > "u")
    return Ms;
  const e = document.createElement("div");
  return e.style.position = pr, e.style.position === pr ? pr : Ms;
});
function lo(e, t) {
  const n = S.useRef(null), r = S.useCallback(
    (a) => {
      if (a === null || !a.offsetParent)
        return;
      const l = a.getBoundingClientRect(), u = l.width;
      let c, p;
      if (t) {
        const y = t.getBoundingClientRect(), D = l.top - y.top;
        c = y.height - Math.max(0, D), p = D + t.scrollTop;
      } else
        c = window.innerHeight - Math.max(0, l.top), p = l.top + window.pageYOffset;
      n.current = {
        offsetTop: p,
        visibleHeight: c,
        visibleWidth: u
      }, e(n.current);
    },
    [e, t]
  ), { callbackRef: s, ref: o } = Pr(r), i = S.useCallback(() => {
    r(o.current);
  }, [r, o]);
  return S.useEffect(() => {
    if (t) {
      t.addEventListener("scroll", i);
      const a = new ResizeObserver(i);
      return a.observe(t), () => {
        t.removeEventListener("scroll", i), a.unobserve(t);
      };
    } else
      return window.addEventListener("scroll", i), window.addEventListener("resize", i), () => {
        window.removeEventListener("scroll", i), window.removeEventListener("resize", i);
      };
  }, [i, t]), s;
}
const uo = S.createContext(void 0), co = S.createContext(void 0);
function fo(e) {
  return e;
}
const Sa = /* @__PURE__ */ ie(() => {
  const e = k((l) => `Item ${l}`), t = k(null), n = k((l) => `Group ${l}`), r = k({}), s = k(fo), o = k("div"), i = k(Qt), a = (l, u = null) => Ee(
    _(
      r,
      R((c) => c[l]),
      me()
    ),
    u
  );
  return {
    context: t,
    itemContent: e,
    groupContent: n,
    components: r,
    computeItemKey: s,
    headerFooterTag: o,
    scrollerRef: i,
    FooterComponent: a("Footer"),
    HeaderComponent: a("Header"),
    TopItemListComponent: a("TopItemList"),
    ListComponent: a("List", "div"),
    ItemComponent: a("Item", "div"),
    GroupComponent: a("Group", "div"),
    ScrollerComponent: a("Scroller", "div"),
    EmptyPlaceholder: a("EmptyPlaceholder"),
    ScrollSeekPlaceholder: a("ScrollSeekPlaceholder")
  };
}), va = /* @__PURE__ */ ie(([e, t]) => ({ ...e, ...t }), pe(wa, Sa)), xa = ({ height: e }) => /* @__PURE__ */ S.createElement("div", { style: { height: e } }), Ta = { position: ao(), zIndex: 1, overflowAnchor: "none" }, ka = { overflowAnchor: "none" }, Ds = /* @__PURE__ */ S.memo(function({ showTopList: t = !1 }) {
  const n = Q("listState"), r = qe("sizeRanges"), s = Q("useWindowScroll"), o = Q("customScrollParent"), i = qe("windowScrollContainerState"), a = qe("scrollContainerState"), l = o || s ? i : a, u = Q("itemContent"), c = Q("context"), p = Q("groupContent"), y = Q("trackItemSizes"), D = Q("itemSize"), M = Q("log"), d = qe("gap"), { callbackRef: v } = Fi(
    r,
    D,
    y,
    t ? Qt : l,
    M,
    d,
    o
  ), [h, m] = S.useState(0);
  Ur("deviation", (A) => {
    h !== A && m(A);
  });
  const T = Q("EmptyPlaceholder"), E = Q("ScrollSeekPlaceholder") || xa, g = Q("ListComponent"), f = Q("ItemComponent"), b = Q("GroupComponent"), Y = Q("computeItemKey"), N = Q("isSeeking"), F = Q("groupIndices").length > 0, G = Q("paddingTopAddition"), q = Q("scrolledToInitialItem"), ae = t ? {} : {
    boxSizing: "border-box",
    paddingTop: n.offsetTop + G,
    paddingBottom: n.offsetBottom,
    marginTop: h,
    ...q ? {} : { visibility: "hidden" }
  };
  return !t && n.totalCount === 0 && T ? S.createElement(T, Fe(T, c)) : S.createElement(
    g,
    {
      ...Fe(g, c),
      ref: v,
      style: ae,
      "data-test-id": t ? "virtuoso-top-item-list" : "virtuoso-item-list"
    },
    (t ? n.topItems : n.items).map((A) => {
      const z = A.originalIndex, se = Y(z + n.firstItemIndex, A.data, c);
      return N ? S.createElement(E, {
        ...Fe(E, c),
        key: se,
        index: A.index,
        height: A.size,
        type: A.type || "item",
        ...A.type === "group" ? {} : { groupIndex: A.groupIndex }
      }) : A.type === "group" ? S.createElement(
        b,
        {
          ...Fe(b, c),
          key: se,
          "data-index": z,
          "data-known-size": A.size,
          "data-item-index": A.index,
          style: Ta
        },
        p(A.index, c)
      ) : S.createElement(
        f,
        {
          ...Fe(f, c),
          ...Da(f, A.data),
          key: se,
          "data-index": z,
          "data-known-size": A.size,
          "data-item-index": A.index,
          "data-item-group-index": A.groupIndex,
          style: ka
        },
        F ? u(A.index, A.groupIndex, A.data, c) : u(A.index, A.data, c)
      );
    })
  );
}), Ia = {
  height: "100%",
  outline: "none",
  overflowY: "auto",
  position: "relative",
  WebkitOverflowScrolling: "touch"
}, Qn = {
  width: "100%",
  height: "100%",
  position: "absolute",
  top: 0
}, Ma = {
  width: "100%",
  position: ao(),
  top: 0,
  zIndex: 1
};
function Fe(e, t) {
  if (typeof e != "string")
    return { context: t };
}
function Da(e, t) {
  return { item: typeof e == "string" ? void 0 : t };
}
const ba = /* @__PURE__ */ S.memo(function() {
  const t = Q("HeaderComponent"), n = qe("headerHeight"), r = Q("headerFooterTag"), s = Ht((i) => n(Ot(i, "height"))), o = Q("context");
  return t ? S.createElement(r, { ref: s }, S.createElement(t, Fe(t, o))) : null;
}), Oa = /* @__PURE__ */ S.memo(function() {
  const t = Q("FooterComponent"), n = qe("footerHeight"), r = Q("headerFooterTag"), s = Ht((i) => n(Ot(i, "height"))), o = Q("context");
  return t ? S.createElement(r, { ref: s }, S.createElement(t, Fe(t, o))) : null;
});
function ho({ usePublisher: e, useEmitter: t, useEmitterValue: n }) {
  return S.memo(function({ style: o, children: i, ...a }) {
    const l = e("scrollContainerState"), u = n("ScrollerComponent"), c = e("smoothScrollTargetReached"), p = n("scrollerRef"), y = n("context"), { scrollerRef: D, scrollByCallback: M, scrollToCallback: d } = Bs(
      l,
      c,
      u,
      p
    );
    return t("scrollTo", d), t("scrollBy", M), S.createElement(
      u,
      {
        ref: D,
        style: { ...Ia, ...o },
        "data-test-id": "virtuoso-scroller",
        "data-virtuoso-scroller": !0,
        tabIndex: 0,
        ...a,
        ...Fe(u, y)
      },
      i
    );
  });
}
function mo({ usePublisher: e, useEmitter: t, useEmitterValue: n }) {
  return S.memo(function({ style: o, children: i, ...a }) {
    const l = e("windowScrollContainerState"), u = n("ScrollerComponent"), c = e("smoothScrollTargetReached"), p = n("totalListHeight"), y = n("deviation"), D = n("customScrollParent"), M = n("context"), { scrollerRef: d, scrollByCallback: v, scrollToCallback: h } = Bs(
      l,
      c,
      u,
      Qt,
      D
    );
    return Pi(() => (d.current = D || window, () => {
      d.current = null;
    }), [d, D]), t("windowScrollTo", h), t("scrollBy", v), S.createElement(
      u,
      {
        style: { position: "relative", ...o, ...p !== 0 ? { height: p + y } : {} },
        "data-virtuoso-scroller": !0,
        ...a,
        ...Fe(u, M)
      },
      i
    );
  });
}
const Ca = ({ children: e }) => {
  const t = S.useContext(uo), n = qe("viewportHeight"), r = qe("fixedItemHeight"), s = Ht(As(n, (o) => Ot(o, "height")));
  return S.useEffect(() => {
    t && (n(t.viewportHeight), r(t.itemHeight));
  }, [t, n, r]), /* @__PURE__ */ S.createElement("div", { style: Qn, ref: s, "data-viewport-type": "element" }, e);
}, Ra = ({ children: e }) => {
  const t = S.useContext(uo), n = qe("windowViewportRect"), r = qe("fixedItemHeight"), s = Q("customScrollParent"), o = lo(n, s);
  return S.useEffect(() => {
    t && (r(t.itemHeight), n({ offsetTop: 0, visibleHeight: t.viewportHeight, visibleWidth: 100 }));
  }, [t, n, r]), /* @__PURE__ */ S.createElement("div", { ref: o, style: Qn, "data-viewport-type": "window" }, e);
}, Ya = ({ children: e }) => {
  const t = Q("TopItemListComponent"), n = Q("headerHeight"), r = { ...Ma, marginTop: `${n}px` }, s = Q("context");
  return S.createElement(t || "div", { style: r, context: s }, e);
}, Ea = /* @__PURE__ */ S.memo(function(t) {
  const n = Q("useWindowScroll"), r = Q("topItemsIndexes").length > 0, s = Q("customScrollParent"), o = s || n ? La : Pa, i = s || n ? Ra : Ca;
  return /* @__PURE__ */ S.createElement(o, { ...t }, r && /* @__PURE__ */ S.createElement(Ya, null, /* @__PURE__ */ S.createElement(Ds, { showTopList: !0 })), /* @__PURE__ */ S.createElement(i, null, /* @__PURE__ */ S.createElement(ba, null), /* @__PURE__ */ S.createElement(Ds, null), /* @__PURE__ */ S.createElement(Oa, null)));
}), {
  Component: Na,
  usePublisher: qe,
  useEmitterValue: Q,
  useEmitter: Ur
} = /* @__PURE__ */ Gs(
  va,
  {
    required: {},
    optional: {
      restoreStateFrom: "restoreStateFrom",
      context: "context",
      followOutput: "followOutput",
      itemContent: "itemContent",
      groupContent: "groupContent",
      overscan: "overscan",
      increaseViewportBy: "increaseViewportBy",
      totalCount: "totalCount",
      groupCounts: "groupCounts",
      topItemCount: "topItemCount",
      firstItemIndex: "firstItemIndex",
      initialTopMostItemIndex: "initialTopMostItemIndex",
      components: "components",
      atBottomThreshold: "atBottomThreshold",
      atTopThreshold: "atTopThreshold",
      computeItemKey: "computeItemKey",
      defaultItemHeight: "defaultItemHeight",
      fixedItemHeight: "fixedItemHeight",
      itemSize: "itemSize",
      scrollSeekConfiguration: "scrollSeekConfiguration",
      headerFooterTag: "headerFooterTag",
      data: "data",
      initialItemCount: "initialItemCount",
      initialScrollTop: "initialScrollTop",
      alignToBottom: "alignToBottom",
      useWindowScroll: "useWindowScroll",
      customScrollParent: "customScrollParent",
      scrollerRef: "scrollerRef",
      logLevel: "logLevel"
    },
    methods: {
      scrollToIndex: "scrollToIndex",
      scrollIntoView: "scrollIntoView",
      scrollTo: "scrollTo",
      scrollBy: "scrollBy",
      autoscrollToBottom: "autoscrollToBottom",
      getState: "getState"
    },
    events: {
      isScrolling: "isScrolling",
      endReached: "endReached",
      startReached: "startReached",
      rangeChanged: "rangeChanged",
      atBottomStateChange: "atBottomStateChange",
      atTopStateChange: "atTopStateChange",
      totalListHeightChanged: "totalListHeightChanged",
      itemsRendered: "itemsRendered",
      groupIndices: "groupIndices"
    }
  },
  Ea
), Pa = /* @__PURE__ */ ho({ usePublisher: qe, useEmitterValue: Q, useEmitter: Ur }), La = /* @__PURE__ */ mo({ usePublisher: qe, useEmitterValue: Q, useEmitter: Ur }), Ha = Na, bs = {
  items: [],
  offsetBottom: 0,
  offsetTop: 0,
  top: 0,
  bottom: 0,
  itemHeight: 0,
  itemWidth: 0
}, Fa = {
  items: [{ index: 0 }],
  offsetBottom: 0,
  offsetTop: 0,
  top: 0,
  bottom: 0,
  itemHeight: 0,
  itemWidth: 0
}, { round: Os, ceil: Cs, floor: Vn, min: gr, max: an } = Math;
function Wa(e) {
  return {
    ...Fa,
    items: e
  };
}
function Rs(e, t, n) {
  return Array.from({ length: t - e + 1 }).map((r, s) => {
    const o = n === null ? null : n[s + e];
    return { index: s + e, data: o };
  });
}
function Aa(e, t) {
  return e && e.column === t.column && e.row === t.row;
}
function Cn(e, t) {
  return e && e.width === t.width && e.height === t.height;
}
const Va = /* @__PURE__ */ ie(
  ([
    { overscan: e, visibleRange: t, listBoundary: n },
    { scrollTop: r, viewportHeight: s, scrollBy: o, scrollTo: i, smoothScrollTargetReached: a, scrollContainerState: l, footerHeight: u, headerHeight: c },
    p,
    y,
    { propsReady: D, didMount: M },
    { windowViewportRect: d, useWindowScroll: v, customScrollParent: h, windowScrollContainerState: m, windowScrollTo: T },
    E
  ]) => {
    const g = k(0), f = k(0), b = k(bs), Y = k({ height: 0, width: 0 }), N = k({ height: 0, width: 0 }), F = ee(), G = ee(), q = k(0), ae = k(null), A = k({ row: 0, column: 0 }), z = ee(), se = ee(), _e = k(!1), Pe = k(0), Me = k(!0), De = k(!1);
    le(
      _(
        M,
        Z(Pe),
        U(([x, K]) => !!K)
      ),
      () => {
        j(Me, !1), j(f, 0);
      }
    ), le(
      _(
        ke(M, Me, N, Y, Pe, De),
        U(([x, K, te, oe, , ve]) => x && !K && te.height !== 0 && oe.height !== 0 && !ve)
      ),
      ([, , , , x]) => {
        j(De, !0), Fr(1, () => {
          j(F, x);
        }), wt(_(r), () => {
          j(n, [0, 0]), j(Me, !0);
        });
      }
    ), H(
      _(
        se,
        U((x) => x != null && x.scrollTop > 0),
        pt(0)
      ),
      f
    ), le(
      _(
        M,
        Z(se),
        U(([, x]) => x != null)
      ),
      ([, x]) => {
        x && (j(Y, x.viewport), j(N, x == null ? void 0 : x.item), j(A, x.gap), x.scrollTop > 0 && (j(_e, !0), wt(_(r, Kt(1)), (K) => {
          j(_e, !1);
        }), j(i, { top: x.scrollTop })));
      }
    ), H(
      _(
        Y,
        R(({ height: x }) => x)
      ),
      s
    ), H(
      _(
        ke(
          V(Y, Cn),
          V(N, Cn),
          V(A, (x, K) => x && x.column === K.column && x.row === K.row),
          V(r)
        ),
        R(([x, K, te, oe]) => ({
          viewport: x,
          item: K,
          gap: te,
          scrollTop: oe
        }))
      ),
      z
    ), H(
      _(
        ke(
          V(g),
          t,
          V(A, Aa),
          V(N, Cn),
          V(Y, Cn),
          V(ae),
          V(f),
          V(_e),
          V(Me),
          V(Pe)
        ),
        U(([, , , , , , , x]) => !x),
        R(
          ([
            x,
            [K, te],
            oe,
            ve,
            be,
            Se,
            kt,
            ,
            Vt,
            it
          ]) => {
            const { row: Le, column: In } = oe, { height: O, width: L } = ve, { width: W } = be;
            if (kt === 0 && (x === 0 || W === 0))
              return bs;
            if (L === 0) {
              const dr = Wr(it, x), ui = dr === 0 ? Math.max(kt - 1, 0) : dr;
              return Wa(Rs(dr, ui, Se));
            }
            const J = po(W, L, In);
            let Ie, Ye;
            Vt ? K === 0 && te === 0 && kt > 0 ? (Ie = 0, Ye = kt - 1) : (Ie = J * Vn((K + Le) / (O + Le)), Ye = J * Cs((te + Le) / (O + Le)) - 1, Ye = gr(x - 1, an(Ye, J - 1)), Ie = gr(Ye, an(0, Ie))) : (Ie = 0, Ye = -1);
            const Ut = Rs(Ie, Ye, Se), { top: us, bottom: cs } = Ys(be, oe, ve, Ut), ds = Cs(x / J), li = ds * O + (ds - 1) * Le - cs;
            return { items: Ut, offsetTop: us, offsetBottom: li, top: us, bottom: cs, itemHeight: O, itemWidth: L };
          }
        )
      ),
      b
    ), H(
      _(
        ae,
        U((x) => x !== null),
        R((x) => x.length)
      ),
      g
    ), H(
      _(
        ke(Y, N, b, A),
        U(([x, K, { items: te }]) => te.length > 0 && K.height !== 0 && x.height !== 0),
        R(([x, K, { items: te }, oe]) => {
          const { top: ve, bottom: be } = Ys(x, oe, K, te);
          return [ve, be];
        }),
        me(mn)
      ),
      n
    );
    const We = k(!1);
    H(
      _(
        r,
        Z(We),
        R(([x, K]) => K || x !== 0)
      ),
      We
    );
    const Ae = Be(
      _(
        V(b),
        U(({ items: x }) => x.length > 0),
        Z(g, We),
        U(([{ items: x }, K, te]) => te && x[x.length - 1].index === K - 1),
        R(([, x]) => x - 1),
        me()
      )
    ), Tt = Be(
      _(
        V(b),
        U(({ items: x }) => x.length > 0 && x[0].index === 0),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        pt(0),
        me()
      )
    ), At = Be(
      _(
        V(b),
        Z(_e),
        U(([{ items: x }, K]) => x.length > 0 && !K),
        R(([{ items: x }]) => ({
          startIndex: x[0].index,
          endIndex: x[x.length - 1].index
        })),
        me(no),
        It(0)
      )
    );
    H(At, y.scrollSeekRangeChanged), H(
      _(
        F,
        Z(Y, N, g, A),
        R(([x, K, te, oe, ve]) => {
          const be = eo(x), { align: Se, behavior: kt, offset: Vt } = be;
          let it = be.index;
          it === "LAST" && (it = oe - 1), it = an(0, it, gr(oe - 1, it));
          let Le = Ir(K, ve, te, it);
          return Se === "end" ? Le = Os(Le - K.height + te.height) : Se === "center" && (Le = Os(Le - K.height / 2 + te.height / 2)), Vt && (Le += Vt), { top: Le, behavior: kt };
        })
      ),
      i
    );
    const Qe = Ee(
      _(
        b,
        R((x) => x.offsetBottom + x.bottom)
      ),
      0
    );
    return H(
      _(
        d,
        R((x) => ({ width: x.visibleWidth, height: x.visibleHeight }))
      ),
      Y
    ), {
      // input
      data: ae,
      totalCount: g,
      viewportDimensions: Y,
      itemDimensions: N,
      scrollTop: r,
      scrollHeight: G,
      overscan: e,
      scrollBy: o,
      scrollTo: i,
      scrollToIndex: F,
      smoothScrollTargetReached: a,
      windowViewportRect: d,
      windowScrollTo: T,
      useWindowScroll: v,
      customScrollParent: h,
      windowScrollContainerState: m,
      deviation: q,
      scrollContainerState: l,
      footerHeight: u,
      headerHeight: c,
      initialItemCount: f,
      gap: A,
      restoreStateFrom: se,
      ...y,
      initialTopMostItemIndex: Pe,
      // output
      gridState: b,
      totalListHeight: Qe,
      ...p,
      startReached: Tt,
      endReached: Ae,
      rangeChanged: At,
      stateChanged: z,
      propsReady: D,
      stateRestoreInProgress: _e,
      ...E
    };
  },
  pe(Ar, Ne, wn, so, Yt, Vr, Rt)
);
function Ys(e, t, n, r) {
  const { height: s } = n;
  if (s === void 0 || r.length === 0)
    return { top: 0, bottom: 0 };
  const o = Ir(e, t, n, r[0].index), i = Ir(e, t, n, r[r.length - 1].index) + s;
  return { top: o, bottom: i };
}
function Ir(e, t, n, r) {
  const s = po(e.width, n.width, t.column), o = Vn(r / s), i = o * n.height + an(0, o - 1) * t.row;
  return i > 0 ? i + t.row : i;
}
function po(e, t, n) {
  return an(1, Vn((e + n) / (Vn(t) + n)));
}
const Ua = /* @__PURE__ */ ie(() => {
  const e = k((u) => `Item ${u}`), t = k({}), n = k(null), r = k("virtuoso-grid-item"), s = k("virtuoso-grid-list"), o = k(fo), i = k("div"), a = k(Qt), l = (u, c = null) => Ee(
    _(
      t,
      R((p) => p[u]),
      me()
    ),
    c
  );
  return {
    context: n,
    itemContent: e,
    components: t,
    computeItemKey: o,
    itemClassName: r,
    listClassName: s,
    headerFooterTag: i,
    scrollerRef: a,
    FooterComponent: l("Footer"),
    HeaderComponent: l("Header"),
    ListComponent: l("List", "div"),
    ItemComponent: l("Item", "div"),
    ScrollerComponent: l("Scroller", "div"),
    ScrollSeekPlaceholder: l("ScrollSeekPlaceholder", "div")
  };
}), Ga = /* @__PURE__ */ ie(([e, t]) => ({ ...e, ...t }), pe(Va, Ua)), za = /* @__PURE__ */ S.memo(function() {
  const t = ye("gridState"), n = ye("listClassName"), r = ye("itemClassName"), s = ye("itemContent"), o = ye("computeItemKey"), i = ye("isSeeking"), a = tt("scrollHeight"), l = ye("ItemComponent"), u = ye("ListComponent"), c = ye("ScrollSeekPlaceholder"), p = ye("context"), y = tt("itemDimensions"), D = tt("gap"), M = ye("log"), d = ye("stateRestoreInProgress"), v = Ht((h) => {
    const m = h.parentElement.parentElement.scrollHeight;
    a(m);
    const T = h.firstChild;
    if (T) {
      const { width: E, height: g } = T.getBoundingClientRect();
      y({ width: E, height: g });
    }
    D({
      row: Es("row-gap", getComputedStyle(h).rowGap, M),
      column: Es("column-gap", getComputedStyle(h).columnGap, M)
    });
  });
  return d ? null : S.createElement(
    u,
    {
      ref: v,
      className: n,
      ...Fe(u, p),
      style: { paddingTop: t.offsetTop, paddingBottom: t.offsetBottom },
      "data-test-id": "virtuoso-item-list"
    },
    t.items.map((h) => {
      const m = o(h.index, h.data, p);
      return i ? S.createElement(c, {
        key: m,
        ...Fe(c, p),
        index: h.index,
        height: t.itemHeight,
        width: t.itemWidth
      }) : S.createElement(
        l,
        { ...Fe(l, p), className: r, "data-index": h.index, key: m },
        s(h.index, h.data, p)
      );
    })
  );
}), Ba = S.memo(function() {
  const t = ye("HeaderComponent"), n = tt("headerHeight"), r = ye("headerFooterTag"), s = Ht((i) => n(Ot(i, "height"))), o = ye("context");
  return t ? S.createElement(r, { ref: s }, S.createElement(t, Fe(t, o))) : null;
}), ja = S.memo(function() {
  const t = ye("FooterComponent"), n = tt("footerHeight"), r = ye("headerFooterTag"), s = Ht((i) => n(Ot(i, "height"))), o = ye("context");
  return t ? S.createElement(r, { ref: s }, S.createElement(t, Fe(t, o))) : null;
}), $a = ({ children: e }) => {
  const t = S.useContext(co), n = tt("itemDimensions"), r = tt("viewportDimensions"), s = Ht((o) => {
    r(o.getBoundingClientRect());
  });
  return S.useEffect(() => {
    t && (r({ height: t.viewportHeight, width: t.viewportWidth }), n({ height: t.itemHeight, width: t.itemWidth }));
  }, [t, r, n]), /* @__PURE__ */ S.createElement("div", { style: Qn, ref: s }, e);
}, Za = ({ children: e }) => {
  const t = S.useContext(co), n = tt("windowViewportRect"), r = tt("itemDimensions"), s = ye("customScrollParent"), o = lo(n, s);
  return S.useEffect(() => {
    t && (r({ height: t.itemHeight, width: t.itemWidth }), n({ offsetTop: 0, visibleHeight: t.viewportHeight, visibleWidth: t.viewportWidth }));
  }, [t, n, r]), /* @__PURE__ */ S.createElement("div", { ref: o, style: Qn }, e);
}, qa = /* @__PURE__ */ S.memo(function({ ...t }) {
  const n = ye("useWindowScroll"), r = ye("customScrollParent"), s = r || n ? Ja : Ka, o = r || n ? Za : $a;
  return /* @__PURE__ */ S.createElement(s, { ...t }, /* @__PURE__ */ S.createElement(o, null, /* @__PURE__ */ S.createElement(Ba, null), /* @__PURE__ */ S.createElement(za, null), /* @__PURE__ */ S.createElement(ja, null)));
}), {
  Component: th,
  usePublisher: tt,
  useEmitterValue: ye,
  useEmitter: go
} = /* @__PURE__ */ Gs(
  Ga,
  {
    optional: {
      context: "context",
      totalCount: "totalCount",
      overscan: "overscan",
      itemContent: "itemContent",
      components: "components",
      computeItemKey: "computeItemKey",
      data: "data",
      initialItemCount: "initialItemCount",
      scrollSeekConfiguration: "scrollSeekConfiguration",
      headerFooterTag: "headerFooterTag",
      listClassName: "listClassName",
      itemClassName: "itemClassName",
      useWindowScroll: "useWindowScroll",
      customScrollParent: "customScrollParent",
      scrollerRef: "scrollerRef",
      logLevel: "logLevel",
      restoreStateFrom: "restoreStateFrom",
      initialTopMostItemIndex: "initialTopMostItemIndex"
    },
    methods: {
      scrollTo: "scrollTo",
      scrollBy: "scrollBy",
      scrollToIndex: "scrollToIndex"
    },
    events: {
      isScrolling: "isScrolling",
      endReached: "endReached",
      startReached: "startReached",
      rangeChanged: "rangeChanged",
      atBottomStateChange: "atBottomStateChange",
      atTopStateChange: "atTopStateChange",
      stateChanged: "stateChanged"
    }
  },
  qa
), Ka = /* @__PURE__ */ ho({ usePublisher: tt, useEmitterValue: ye, useEmitter: go }), Ja = /* @__PURE__ */ mo({ usePublisher: tt, useEmitterValue: ye, useEmitter: go });
function Es(e, t, n) {
  return t !== "normal" && !(t != null && t.endsWith("px")) && n(`${e} was not resolved to pixel value correctly`, t, je.WARN), t === "normal" ? 0 : parseInt(t ?? "0", 10);
}
//! moment.js
//! version : 2.29.4
//! authors : Tim Wood, Iskren Chernev, Moment.js contributors
//! license : MIT
//! momentjs.com
var yo;
function I() {
  return yo.apply(null, arguments);
}
function Qa(e) {
  yo = e;
}
function rt(e) {
  return e instanceof Array || Object.prototype.toString.call(e) === "[object Array]";
}
function Lt(e) {
  return e != null && Object.prototype.toString.call(e) === "[object Object]";
}
function ne(e, t) {
  return Object.prototype.hasOwnProperty.call(e, t);
}
function Gr(e) {
  if (Object.getOwnPropertyNames)
    return Object.getOwnPropertyNames(e).length === 0;
  var t;
  for (t in e)
    if (ne(e, t))
      return !1;
  return !0;
}
function He(e) {
  return e === void 0;
}
function St(e) {
  return typeof e == "number" || Object.prototype.toString.call(e) === "[object Number]";
}
function vn(e) {
  return e instanceof Date || Object.prototype.toString.call(e) === "[object Date]";
}
function _o(e, t) {
  var n = [], r, s = e.length;
  for (r = 0; r < s; ++r)
    n.push(t(e[r], r));
  return n;
}
function Mt(e, t) {
  for (var n in t)
    ne(t, n) && (e[n] = t[n]);
  return ne(t, "toString") && (e.toString = t.toString), ne(t, "valueOf") && (e.valueOf = t.valueOf), e;
}
function dt(e, t, n, r) {
  return Uo(e, t, n, r, !0).utc();
}
function Xa() {
  return {
    empty: !1,
    unusedTokens: [],
    unusedInput: [],
    overflow: -2,
    charsLeftOver: 0,
    nullInput: !1,
    invalidEra: null,
    invalidMonth: null,
    invalidFormat: !1,
    userInvalidated: !1,
    iso: !1,
    parsedDateParts: [],
    era: null,
    meridiem: null,
    rfc2822: !1,
    weekdayMismatch: !1
  };
}
function B(e) {
  return e._pf == null && (e._pf = Xa()), e._pf;
}
var Mr;
Array.prototype.some ? Mr = Array.prototype.some : Mr = function(e) {
  var t = Object(this), n = t.length >>> 0, r;
  for (r = 0; r < n; r++)
    if (r in t && e.call(this, t[r], r, t))
      return !0;
  return !1;
};
function zr(e) {
  if (e._isValid == null) {
    var t = B(e), n = Mr.call(t.parsedDateParts, function(s) {
      return s != null;
    }), r = !isNaN(e._d.getTime()) && t.overflow < 0 && !t.empty && !t.invalidEra && !t.invalidMonth && !t.invalidWeekday && !t.weekdayMismatch && !t.nullInput && !t.invalidFormat && !t.userInvalidated && (!t.meridiem || t.meridiem && n);
    if (e._strict && (r = r && t.charsLeftOver === 0 && t.unusedTokens.length === 0 && t.bigHour === void 0), Object.isFrozen == null || !Object.isFrozen(e))
      e._isValid = r;
    else
      return r;
  }
  return e._isValid;
}
function Xn(e) {
  var t = dt(NaN);
  return e != null ? Mt(B(t), e) : B(t).userInvalidated = !0, t;
}
var Ns = I.momentProperties = [], yr = !1;
function Br(e, t) {
  var n, r, s, o = Ns.length;
  if (He(t._isAMomentObject) || (e._isAMomentObject = t._isAMomentObject), He(t._i) || (e._i = t._i), He(t._f) || (e._f = t._f), He(t._l) || (e._l = t._l), He(t._strict) || (e._strict = t._strict), He(t._tzm) || (e._tzm = t._tzm), He(t._isUTC) || (e._isUTC = t._isUTC), He(t._offset) || (e._offset = t._offset), He(t._pf) || (e._pf = B(t)), He(t._locale) || (e._locale = t._locale), o > 0)
    for (n = 0; n < o; n++)
      r = Ns[n], s = t[r], He(s) || (e[r] = s);
  return e;
}
function xn(e) {
  Br(this, e), this._d = new Date(e._d != null ? e._d.getTime() : NaN), this.isValid() || (this._d = /* @__PURE__ */ new Date(NaN)), yr === !1 && (yr = !0, I.updateOffset(this), yr = !1);
}
function st(e) {
  return e instanceof xn || e != null && e._isAMomentObject != null;
}
function wo(e) {
  I.suppressDeprecationWarnings === !1 && typeof console < "u" && console.warn && console.warn("Deprecation warning: " + e);
}
function Ke(e, t) {
  var n = !0;
  return Mt(function() {
    if (I.deprecationHandler != null && I.deprecationHandler(null, e), n) {
      var r = [], s, o, i, a = arguments.length;
      for (o = 0; o < a; o++) {
        if (s = "", typeof arguments[o] == "object") {
          s += `
[` + o + "] ";
          for (i in arguments[0])
            ne(arguments[0], i) && (s += i + ": " + arguments[0][i] + ", ");
          s = s.slice(0, -2);
        } else
          s = arguments[o];
        r.push(s);
      }
      wo(
        e + `
Arguments: ` + Array.prototype.slice.call(r).join("") + `
` + new Error().stack
      ), n = !1;
    }
    return t.apply(this, arguments);
  }, t);
}
var Ps = {};
function So(e, t) {
  I.deprecationHandler != null && I.deprecationHandler(e, t), Ps[e] || (wo(t), Ps[e] = !0);
}
I.suppressDeprecationWarnings = !1;
I.deprecationHandler = null;
function ft(e) {
  return typeof Function < "u" && e instanceof Function || Object.prototype.toString.call(e) === "[object Function]";
}
function el(e) {
  var t, n;
  for (n in e)
    ne(e, n) && (t = e[n], ft(t) ? this[n] = t : this["_" + n] = t);
  this._config = e, this._dayOfMonthOrdinalParseLenient = new RegExp(
    (this._dayOfMonthOrdinalParse.source || this._ordinalParse.source) + "|" + /\d{1,2}/.source
  );
}
function Dr(e, t) {
  var n = Mt({}, e), r;
  for (r in t)
    ne(t, r) && (Lt(e[r]) && Lt(t[r]) ? (n[r] = {}, Mt(n[r], e[r]), Mt(n[r], t[r])) : t[r] != null ? n[r] = t[r] : delete n[r]);
  for (r in e)
    ne(e, r) && !ne(t, r) && Lt(e[r]) && (n[r] = Mt({}, n[r]));
  return n;
}
function jr(e) {
  e != null && this.set(e);
}
var br;
Object.keys ? br = Object.keys : br = function(e) {
  var t, n = [];
  for (t in e)
    ne(e, t) && n.push(t);
  return n;
};
var tl = {
  sameDay: "[Today at] LT",
  nextDay: "[Tomorrow at] LT",
  nextWeek: "dddd [at] LT",
  lastDay: "[Yesterday at] LT",
  lastWeek: "[Last] dddd [at] LT",
  sameElse: "L"
};
function nl(e, t, n) {
  var r = this._calendar[e] || this._calendar.sameElse;
  return ft(r) ? r.call(t, n) : r;
}
function ut(e, t, n) {
  var r = "" + Math.abs(e), s = t - r.length, o = e >= 0;
  return (o ? n ? "+" : "" : "-") + Math.pow(10, Math.max(0, s)).toString().substr(1) + r;
}
var $r = /(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g, Rn = /(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g, _r = {}, $t = {};
function P(e, t, n, r) {
  var s = r;
  typeof r == "string" && (s = function() {
    return this[r]();
  }), e && ($t[e] = s), t && ($t[t[0]] = function() {
    return ut(s.apply(this, arguments), t[1], t[2]);
  }), n && ($t[n] = function() {
    return this.localeData().ordinal(
      s.apply(this, arguments),
      e
    );
  });
}
function rl(e) {
  return e.match(/\[[\s\S]/) ? e.replace(/^\[|\]$/g, "") : e.replace(/\\/g, "");
}
function sl(e) {
  var t = e.match($r), n, r;
  for (n = 0, r = t.length; n < r; n++)
    $t[t[n]] ? t[n] = $t[t[n]] : t[n] = rl(t[n]);
  return function(s) {
    var o = "", i;
    for (i = 0; i < r; i++)
      o += ft(t[i]) ? t[i].call(s, e) : t[i];
    return o;
  };
}
function Pn(e, t) {
  return e.isValid() ? (t = vo(t, e.localeData()), _r[t] = _r[t] || sl(t), _r[t](e)) : e.localeData().invalidDate();
}
function vo(e, t) {
  var n = 5;
  function r(s) {
    return t.longDateFormat(s) || s;
  }
  for (Rn.lastIndex = 0; n >= 0 && Rn.test(e); )
    e = e.replace(
      Rn,
      r
    ), Rn.lastIndex = 0, n -= 1;
  return e;
}
var ol = {
  LTS: "h:mm:ss A",
  LT: "h:mm A",
  L: "MM/DD/YYYY",
  LL: "MMMM D, YYYY",
  LLL: "MMMM D, YYYY h:mm A",
  LLLL: "dddd, MMMM D, YYYY h:mm A"
};
function il(e) {
  var t = this._longDateFormat[e], n = this._longDateFormat[e.toUpperCase()];
  return t || !n ? t : (this._longDateFormat[e] = n.match($r).map(function(r) {
    return r === "MMMM" || r === "MM" || r === "DD" || r === "dddd" ? r.slice(1) : r;
  }).join(""), this._longDateFormat[e]);
}
var al = "Invalid date";
function ll() {
  return this._invalidDate;
}
var ul = "%d", cl = /\d{1,2}/;
function dl(e) {
  return this._ordinal.replace("%d", e);
}
var fl = {
  future: "in %s",
  past: "%s ago",
  s: "a few seconds",
  ss: "%d seconds",
  m: "a minute",
  mm: "%d minutes",
  h: "an hour",
  hh: "%d hours",
  d: "a day",
  dd: "%d days",
  w: "a week",
  ww: "%d weeks",
  M: "a month",
  MM: "%d months",
  y: "a year",
  yy: "%d years"
};
function hl(e, t, n, r) {
  var s = this._relativeTime[n];
  return ft(s) ? s(e, t, n, r) : s.replace(/%d/i, e);
}
function ml(e, t) {
  var n = this._relativeTime[e > 0 ? "future" : "past"];
  return ft(n) ? n(t) : n.replace(/%s/i, t);
}
var ln = {};
function Ce(e, t) {
  var n = e.toLowerCase();
  ln[n] = ln[n + "s"] = ln[t] = e;
}
function Je(e) {
  return typeof e == "string" ? ln[e] || ln[e.toLowerCase()] : void 0;
}
function Zr(e) {
  var t = {}, n, r;
  for (r in e)
    ne(e, r) && (n = Je(r), n && (t[n] = e[r]));
  return t;
}
var xo = {};
function Re(e, t) {
  xo[e] = t;
}
function pl(e) {
  var t = [], n;
  for (n in e)
    ne(e, n) && t.push({ unit: n, priority: xo[n] });
  return t.sort(function(r, s) {
    return r.priority - s.priority;
  }), t;
}
function er(e) {
  return e % 4 === 0 && e % 100 !== 0 || e % 400 === 0;
}
function Ze(e) {
  return e < 0 ? Math.ceil(e) || 0 : Math.floor(e);
}
function $(e) {
  var t = +e, n = 0;
  return t !== 0 && isFinite(t) && (n = Ze(t)), n;
}
function Xt(e, t) {
  return function(n) {
    return n != null ? (To(this, e, n), I.updateOffset(this, t), this) : Un(this, e);
  };
}
function Un(e, t) {
  return e.isValid() ? e._d["get" + (e._isUTC ? "UTC" : "") + t]() : NaN;
}
function To(e, t, n) {
  e.isValid() && !isNaN(n) && (t === "FullYear" && er(e.year()) && e.month() === 1 && e.date() === 29 ? (n = $(n), e._d["set" + (e._isUTC ? "UTC" : "") + t](
    n,
    e.month(),
    ir(n, e.month())
  )) : e._d["set" + (e._isUTC ? "UTC" : "") + t](n));
}
function gl(e) {
  return e = Je(e), ft(this[e]) ? this[e]() : this;
}
function yl(e, t) {
  if (typeof e == "object") {
    e = Zr(e);
    var n = pl(e), r, s = n.length;
    for (r = 0; r < s; r++)
      this[n[r].unit](e[n[r].unit]);
  } else if (e = Je(e), ft(this[e]))
    return this[e](t);
  return this;
}
var ko = /\d/, $e = /\d\d/, Io = /\d{3}/, qr = /\d{4}/, tr = /[+-]?\d{6}/, fe = /\d\d?/, Mo = /\d\d\d\d?/, Do = /\d\d\d\d\d\d?/, nr = /\d{1,3}/, Kr = /\d{1,4}/, rr = /[+-]?\d{1,6}/, en = /\d+/, sr = /[+-]?\d+/, _l = /Z|[+-]\d\d:?\d\d/gi, or = /Z|[+-]\d\d(?::?\d\d)?/gi, wl = /[+-]?\d+(\.\d{1,3})?/, Tn = /[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i, Gn;
Gn = {};
function C(e, t, n) {
  Gn[e] = ft(t) ? t : function(r, s) {
    return r && n ? n : t;
  };
}
function Sl(e, t) {
  return ne(Gn, e) ? Gn[e](t._strict, t._locale) : new RegExp(vl(e));
}
function vl(e) {
  return Ue(
    e.replace("\\", "").replace(
      /\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,
      function(t, n, r, s, o) {
        return n || r || s || o;
      }
    )
  );
}
function Ue(e) {
  return e.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
}
var Or = {};
function ue(e, t) {
  var n, r = t, s;
  for (typeof e == "string" && (e = [e]), St(t) && (r = function(o, i) {
    i[t] = $(o);
  }), s = e.length, n = 0; n < s; n++)
    Or[e[n]] = r;
}
function kn(e, t) {
  ue(e, function(n, r, s, o) {
    s._w = s._w || {}, t(n, s._w, s, o);
  });
}
function xl(e, t, n) {
  t != null && ne(Or, e) && Or[e](t, n._a, n, e);
}
var Oe = 0, gt = 1, at = 2, Te = 3, et = 4, yt = 5, Pt = 6, Tl = 7, kl = 8;
function Il(e, t) {
  return (e % t + t) % t;
}
var we;
Array.prototype.indexOf ? we = Array.prototype.indexOf : we = function(e) {
  var t;
  for (t = 0; t < this.length; ++t)
    if (this[t] === e)
      return t;
  return -1;
};
function ir(e, t) {
  if (isNaN(e) || isNaN(t))
    return NaN;
  var n = Il(t, 12);
  return e += (t - n) / 12, n === 1 ? er(e) ? 29 : 28 : 31 - n % 7 % 2;
}
P("M", ["MM", 2], "Mo", function() {
  return this.month() + 1;
});
P("MMM", 0, 0, function(e) {
  return this.localeData().monthsShort(this, e);
});
P("MMMM", 0, 0, function(e) {
  return this.localeData().months(this, e);
});
Ce("month", "M");
Re("month", 8);
C("M", fe);
C("MM", fe, $e);
C("MMM", function(e, t) {
  return t.monthsShortRegex(e);
});
C("MMMM", function(e, t) {
  return t.monthsRegex(e);
});
ue(["M", "MM"], function(e, t) {
  t[gt] = $(e) - 1;
});
ue(["MMM", "MMMM"], function(e, t, n, r) {
  var s = n._locale.monthsParse(e, r, n._strict);
  s != null ? t[gt] = s : B(n).invalidMonth = e;
});
var Ml = "January_February_March_April_May_June_July_August_September_October_November_December".split(
  "_"
), bo = "Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"), Oo = /D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/, Dl = Tn, bl = Tn;
function Ol(e, t) {
  return e ? rt(this._months) ? this._months[e.month()] : this._months[(this._months.isFormat || Oo).test(t) ? "format" : "standalone"][e.month()] : rt(this._months) ? this._months : this._months.standalone;
}
function Cl(e, t) {
  return e ? rt(this._monthsShort) ? this._monthsShort[e.month()] : this._monthsShort[Oo.test(t) ? "format" : "standalone"][e.month()] : rt(this._monthsShort) ? this._monthsShort : this._monthsShort.standalone;
}
function Rl(e, t, n) {
  var r, s, o, i = e.toLocaleLowerCase();
  if (!this._monthsParse)
    for (this._monthsParse = [], this._longMonthsParse = [], this._shortMonthsParse = [], r = 0; r < 12; ++r)
      o = dt([2e3, r]), this._shortMonthsParse[r] = this.monthsShort(
        o,
        ""
      ).toLocaleLowerCase(), this._longMonthsParse[r] = this.months(o, "").toLocaleLowerCase();
  return n ? t === "MMM" ? (s = we.call(this._shortMonthsParse, i), s !== -1 ? s : null) : (s = we.call(this._longMonthsParse, i), s !== -1 ? s : null) : t === "MMM" ? (s = we.call(this._shortMonthsParse, i), s !== -1 ? s : (s = we.call(this._longMonthsParse, i), s !== -1 ? s : null)) : (s = we.call(this._longMonthsParse, i), s !== -1 ? s : (s = we.call(this._shortMonthsParse, i), s !== -1 ? s : null));
}
function Yl(e, t, n) {
  var r, s, o;
  if (this._monthsParseExact)
    return Rl.call(this, e, t, n);
  for (this._monthsParse || (this._monthsParse = [], this._longMonthsParse = [], this._shortMonthsParse = []), r = 0; r < 12; r++) {
    if (s = dt([2e3, r]), n && !this._longMonthsParse[r] && (this._longMonthsParse[r] = new RegExp(
      "^" + this.months(s, "").replace(".", "") + "$",
      "i"
    ), this._shortMonthsParse[r] = new RegExp(
      "^" + this.monthsShort(s, "").replace(".", "") + "$",
      "i"
    )), !n && !this._monthsParse[r] && (o = "^" + this.months(s, "") + "|^" + this.monthsShort(s, ""), this._monthsParse[r] = new RegExp(o.replace(".", ""), "i")), n && t === "MMMM" && this._longMonthsParse[r].test(e))
      return r;
    if (n && t === "MMM" && this._shortMonthsParse[r].test(e))
      return r;
    if (!n && this._monthsParse[r].test(e))
      return r;
  }
}
function Co(e, t) {
  var n;
  if (!e.isValid())
    return e;
  if (typeof t == "string") {
    if (/^\d+$/.test(t))
      t = $(t);
    else if (t = e.localeData().monthsParse(t), !St(t))
      return e;
  }
  return n = Math.min(e.date(), ir(e.year(), t)), e._d["set" + (e._isUTC ? "UTC" : "") + "Month"](t, n), e;
}
function Ro(e) {
  return e != null ? (Co(this, e), I.updateOffset(this, !0), this) : Un(this, "Month");
}
function El() {
  return ir(this.year(), this.month());
}
function Nl(e) {
  return this._monthsParseExact ? (ne(this, "_monthsRegex") || Yo.call(this), e ? this._monthsShortStrictRegex : this._monthsShortRegex) : (ne(this, "_monthsShortRegex") || (this._monthsShortRegex = Dl), this._monthsShortStrictRegex && e ? this._monthsShortStrictRegex : this._monthsShortRegex);
}
function Pl(e) {
  return this._monthsParseExact ? (ne(this, "_monthsRegex") || Yo.call(this), e ? this._monthsStrictRegex : this._monthsRegex) : (ne(this, "_monthsRegex") || (this._monthsRegex = bl), this._monthsStrictRegex && e ? this._monthsStrictRegex : this._monthsRegex);
}
function Yo() {
  function e(i, a) {
    return a.length - i.length;
  }
  var t = [], n = [], r = [], s, o;
  for (s = 0; s < 12; s++)
    o = dt([2e3, s]), t.push(this.monthsShort(o, "")), n.push(this.months(o, "")), r.push(this.months(o, "")), r.push(this.monthsShort(o, ""));
  for (t.sort(e), n.sort(e), r.sort(e), s = 0; s < 12; s++)
    t[s] = Ue(t[s]), n[s] = Ue(n[s]);
  for (s = 0; s < 24; s++)
    r[s] = Ue(r[s]);
  this._monthsRegex = new RegExp("^(" + r.join("|") + ")", "i"), this._monthsShortRegex = this._monthsRegex, this._monthsStrictRegex = new RegExp(
    "^(" + n.join("|") + ")",
    "i"
  ), this._monthsShortStrictRegex = new RegExp(
    "^(" + t.join("|") + ")",
    "i"
  );
}
P("Y", 0, 0, function() {
  var e = this.year();
  return e <= 9999 ? ut(e, 4) : "+" + e;
});
P(0, ["YY", 2], 0, function() {
  return this.year() % 100;
});
P(0, ["YYYY", 4], 0, "year");
P(0, ["YYYYY", 5], 0, "year");
P(0, ["YYYYYY", 6, !0], 0, "year");
Ce("year", "y");
Re("year", 1);
C("Y", sr);
C("YY", fe, $e);
C("YYYY", Kr, qr);
C("YYYYY", rr, tr);
C("YYYYYY", rr, tr);
ue(["YYYYY", "YYYYYY"], Oe);
ue("YYYY", function(e, t) {
  t[Oe] = e.length === 2 ? I.parseTwoDigitYear(e) : $(e);
});
ue("YY", function(e, t) {
  t[Oe] = I.parseTwoDigitYear(e);
});
ue("Y", function(e, t) {
  t[Oe] = parseInt(e, 10);
});
function un(e) {
  return er(e) ? 366 : 365;
}
I.parseTwoDigitYear = function(e) {
  return $(e) + ($(e) > 68 ? 1900 : 2e3);
};
var Eo = Xt("FullYear", !0);
function Ll() {
  return er(this.year());
}
function Hl(e, t, n, r, s, o, i) {
  var a;
  return e < 100 && e >= 0 ? (a = new Date(e + 400, t, n, r, s, o, i), isFinite(a.getFullYear()) && a.setFullYear(e)) : a = new Date(e, t, n, r, s, o, i), a;
}
function pn(e) {
  var t, n;
  return e < 100 && e >= 0 ? (n = Array.prototype.slice.call(arguments), n[0] = e + 400, t = new Date(Date.UTC.apply(null, n)), isFinite(t.getUTCFullYear()) && t.setUTCFullYear(e)) : t = new Date(Date.UTC.apply(null, arguments)), t;
}
function zn(e, t, n) {
  var r = 7 + t - n, s = (7 + pn(e, 0, r).getUTCDay() - t) % 7;
  return -s + r - 1;
}
function No(e, t, n, r, s) {
  var o = (7 + n - r) % 7, i = zn(e, r, s), a = 1 + 7 * (t - 1) + o + i, l, u;
  return a <= 0 ? (l = e - 1, u = un(l) + a) : a > un(e) ? (l = e + 1, u = a - un(e)) : (l = e, u = a), {
    year: l,
    dayOfYear: u
  };
}
function gn(e, t, n) {
  var r = zn(e.year(), t, n), s = Math.floor((e.dayOfYear() - r - 1) / 7) + 1, o, i;
  return s < 1 ? (i = e.year() - 1, o = s + _t(i, t, n)) : s > _t(e.year(), t, n) ? (o = s - _t(e.year(), t, n), i = e.year() + 1) : (i = e.year(), o = s), {
    week: o,
    year: i
  };
}
function _t(e, t, n) {
  var r = zn(e, t, n), s = zn(e + 1, t, n);
  return (un(e) - r + s) / 7;
}
P("w", ["ww", 2], "wo", "week");
P("W", ["WW", 2], "Wo", "isoWeek");
Ce("week", "w");
Ce("isoWeek", "W");
Re("week", 5);
Re("isoWeek", 5);
C("w", fe);
C("ww", fe, $e);
C("W", fe);
C("WW", fe, $e);
kn(
  ["w", "ww", "W", "WW"],
  function(e, t, n, r) {
    t[r.substr(0, 1)] = $(e);
  }
);
function Fl(e) {
  return gn(e, this._week.dow, this._week.doy).week;
}
var Wl = {
  dow: 0,
  // Sunday is the first day of the week.
  doy: 6
  // The week that contains Jan 6th is the first week of the year.
};
function Al() {
  return this._week.dow;
}
function Vl() {
  return this._week.doy;
}
function Ul(e) {
  var t = this.localeData().week(this);
  return e == null ? t : this.add((e - t) * 7, "d");
}
function Gl(e) {
  var t = gn(this, 1, 4).week;
  return e == null ? t : this.add((e - t) * 7, "d");
}
P("d", 0, "do", "day");
P("dd", 0, 0, function(e) {
  return this.localeData().weekdaysMin(this, e);
});
P("ddd", 0, 0, function(e) {
  return this.localeData().weekdaysShort(this, e);
});
P("dddd", 0, 0, function(e) {
  return this.localeData().weekdays(this, e);
});
P("e", 0, 0, "weekday");
P("E", 0, 0, "isoWeekday");
Ce("day", "d");
Ce("weekday", "e");
Ce("isoWeekday", "E");
Re("day", 11);
Re("weekday", 11);
Re("isoWeekday", 11);
C("d", fe);
C("e", fe);
C("E", fe);
C("dd", function(e, t) {
  return t.weekdaysMinRegex(e);
});
C("ddd", function(e, t) {
  return t.weekdaysShortRegex(e);
});
C("dddd", function(e, t) {
  return t.weekdaysRegex(e);
});
kn(["dd", "ddd", "dddd"], function(e, t, n, r) {
  var s = n._locale.weekdaysParse(e, r, n._strict);
  s != null ? t.d = s : B(n).invalidWeekday = e;
});
kn(["d", "e", "E"], function(e, t, n, r) {
  t[r] = $(e);
});
function zl(e, t) {
  return typeof e != "string" ? e : isNaN(e) ? (e = t.weekdaysParse(e), typeof e == "number" ? e : null) : parseInt(e, 10);
}
function Bl(e, t) {
  return typeof e == "string" ? t.weekdaysParse(e) % 7 || 7 : isNaN(e) ? null : e;
}
function Jr(e, t) {
  return e.slice(t, 7).concat(e.slice(0, t));
}
var jl = "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"), Po = "Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"), $l = "Su_Mo_Tu_We_Th_Fr_Sa".split("_"), Zl = Tn, ql = Tn, Kl = Tn;
function Jl(e, t) {
  var n = rt(this._weekdays) ? this._weekdays : this._weekdays[e && e !== !0 && this._weekdays.isFormat.test(t) ? "format" : "standalone"];
  return e === !0 ? Jr(n, this._week.dow) : e ? n[e.day()] : n;
}
function Ql(e) {
  return e === !0 ? Jr(this._weekdaysShort, this._week.dow) : e ? this._weekdaysShort[e.day()] : this._weekdaysShort;
}
function Xl(e) {
  return e === !0 ? Jr(this._weekdaysMin, this._week.dow) : e ? this._weekdaysMin[e.day()] : this._weekdaysMin;
}
function eu(e, t, n) {
  var r, s, o, i = e.toLocaleLowerCase();
  if (!this._weekdaysParse)
    for (this._weekdaysParse = [], this._shortWeekdaysParse = [], this._minWeekdaysParse = [], r = 0; r < 7; ++r)
      o = dt([2e3, 1]).day(r), this._minWeekdaysParse[r] = this.weekdaysMin(
        o,
        ""
      ).toLocaleLowerCase(), this._shortWeekdaysParse[r] = this.weekdaysShort(
        o,
        ""
      ).toLocaleLowerCase(), this._weekdaysParse[r] = this.weekdays(o, "").toLocaleLowerCase();
  return n ? t === "dddd" ? (s = we.call(this._weekdaysParse, i), s !== -1 ? s : null) : t === "ddd" ? (s = we.call(this._shortWeekdaysParse, i), s !== -1 ? s : null) : (s = we.call(this._minWeekdaysParse, i), s !== -1 ? s : null) : t === "dddd" ? (s = we.call(this._weekdaysParse, i), s !== -1 || (s = we.call(this._shortWeekdaysParse, i), s !== -1) ? s : (s = we.call(this._minWeekdaysParse, i), s !== -1 ? s : null)) : t === "ddd" ? (s = we.call(this._shortWeekdaysParse, i), s !== -1 || (s = we.call(this._weekdaysParse, i), s !== -1) ? s : (s = we.call(this._minWeekdaysParse, i), s !== -1 ? s : null)) : (s = we.call(this._minWeekdaysParse, i), s !== -1 || (s = we.call(this._weekdaysParse, i), s !== -1) ? s : (s = we.call(this._shortWeekdaysParse, i), s !== -1 ? s : null));
}
function tu(e, t, n) {
  var r, s, o;
  if (this._weekdaysParseExact)
    return eu.call(this, e, t, n);
  for (this._weekdaysParse || (this._weekdaysParse = [], this._minWeekdaysParse = [], this._shortWeekdaysParse = [], this._fullWeekdaysParse = []), r = 0; r < 7; r++) {
    if (s = dt([2e3, 1]).day(r), n && !this._fullWeekdaysParse[r] && (this._fullWeekdaysParse[r] = new RegExp(
      "^" + this.weekdays(s, "").replace(".", "\\.?") + "$",
      "i"
    ), this._shortWeekdaysParse[r] = new RegExp(
      "^" + this.weekdaysShort(s, "").replace(".", "\\.?") + "$",
      "i"
    ), this._minWeekdaysParse[r] = new RegExp(
      "^" + this.weekdaysMin(s, "").replace(".", "\\.?") + "$",
      "i"
    )), this._weekdaysParse[r] || (o = "^" + this.weekdays(s, "") + "|^" + this.weekdaysShort(s, "") + "|^" + this.weekdaysMin(s, ""), this._weekdaysParse[r] = new RegExp(o.replace(".", ""), "i")), n && t === "dddd" && this._fullWeekdaysParse[r].test(e))
      return r;
    if (n && t === "ddd" && this._shortWeekdaysParse[r].test(e))
      return r;
    if (n && t === "dd" && this._minWeekdaysParse[r].test(e))
      return r;
    if (!n && this._weekdaysParse[r].test(e))
      return r;
  }
}
function nu(e) {
  if (!this.isValid())
    return e != null ? this : NaN;
  var t = this._isUTC ? this._d.getUTCDay() : this._d.getDay();
  return e != null ? (e = zl(e, this.localeData()), this.add(e - t, "d")) : t;
}
function ru(e) {
  if (!this.isValid())
    return e != null ? this : NaN;
  var t = (this.day() + 7 - this.localeData()._week.dow) % 7;
  return e == null ? t : this.add(e - t, "d");
}
function su(e) {
  if (!this.isValid())
    return e != null ? this : NaN;
  if (e != null) {
    var t = Bl(e, this.localeData());
    return this.day(this.day() % 7 ? t : t - 7);
  } else
    return this.day() || 7;
}
function ou(e) {
  return this._weekdaysParseExact ? (ne(this, "_weekdaysRegex") || Qr.call(this), e ? this._weekdaysStrictRegex : this._weekdaysRegex) : (ne(this, "_weekdaysRegex") || (this._weekdaysRegex = Zl), this._weekdaysStrictRegex && e ? this._weekdaysStrictRegex : this._weekdaysRegex);
}
function iu(e) {
  return this._weekdaysParseExact ? (ne(this, "_weekdaysRegex") || Qr.call(this), e ? this._weekdaysShortStrictRegex : this._weekdaysShortRegex) : (ne(this, "_weekdaysShortRegex") || (this._weekdaysShortRegex = ql), this._weekdaysShortStrictRegex && e ? this._weekdaysShortStrictRegex : this._weekdaysShortRegex);
}
function au(e) {
  return this._weekdaysParseExact ? (ne(this, "_weekdaysRegex") || Qr.call(this), e ? this._weekdaysMinStrictRegex : this._weekdaysMinRegex) : (ne(this, "_weekdaysMinRegex") || (this._weekdaysMinRegex = Kl), this._weekdaysMinStrictRegex && e ? this._weekdaysMinStrictRegex : this._weekdaysMinRegex);
}
function Qr() {
  function e(c, p) {
    return p.length - c.length;
  }
  var t = [], n = [], r = [], s = [], o, i, a, l, u;
  for (o = 0; o < 7; o++)
    i = dt([2e3, 1]).day(o), a = Ue(this.weekdaysMin(i, "")), l = Ue(this.weekdaysShort(i, "")), u = Ue(this.weekdays(i, "")), t.push(a), n.push(l), r.push(u), s.push(a), s.push(l), s.push(u);
  t.sort(e), n.sort(e), r.sort(e), s.sort(e), this._weekdaysRegex = new RegExp("^(" + s.join("|") + ")", "i"), this._weekdaysShortRegex = this._weekdaysRegex, this._weekdaysMinRegex = this._weekdaysRegex, this._weekdaysStrictRegex = new RegExp(
    "^(" + r.join("|") + ")",
    "i"
  ), this._weekdaysShortStrictRegex = new RegExp(
    "^(" + n.join("|") + ")",
    "i"
  ), this._weekdaysMinStrictRegex = new RegExp(
    "^(" + t.join("|") + ")",
    "i"
  );
}
function Xr() {
  return this.hours() % 12 || 12;
}
function lu() {
  return this.hours() || 24;
}
P("H", ["HH", 2], 0, "hour");
P("h", ["hh", 2], 0, Xr);
P("k", ["kk", 2], 0, lu);
P("hmm", 0, 0, function() {
  return "" + Xr.apply(this) + ut(this.minutes(), 2);
});
P("hmmss", 0, 0, function() {
  return "" + Xr.apply(this) + ut(this.minutes(), 2) + ut(this.seconds(), 2);
});
P("Hmm", 0, 0, function() {
  return "" + this.hours() + ut(this.minutes(), 2);
});
P("Hmmss", 0, 0, function() {
  return "" + this.hours() + ut(this.minutes(), 2) + ut(this.seconds(), 2);
});
function Lo(e, t) {
  P(e, 0, 0, function() {
    return this.localeData().meridiem(
      this.hours(),
      this.minutes(),
      t
    );
  });
}
Lo("a", !0);
Lo("A", !1);
Ce("hour", "h");
Re("hour", 13);
function Ho(e, t) {
  return t._meridiemParse;
}
C("a", Ho);
C("A", Ho);
C("H", fe);
C("h", fe);
C("k", fe);
C("HH", fe, $e);
C("hh", fe, $e);
C("kk", fe, $e);
C("hmm", Mo);
C("hmmss", Do);
C("Hmm", Mo);
C("Hmmss", Do);
ue(["H", "HH"], Te);
ue(["k", "kk"], function(e, t, n) {
  var r = $(e);
  t[Te] = r === 24 ? 0 : r;
});
ue(["a", "A"], function(e, t, n) {
  n._isPm = n._locale.isPM(e), n._meridiem = e;
});
ue(["h", "hh"], function(e, t, n) {
  t[Te] = $(e), B(n).bigHour = !0;
});
ue("hmm", function(e, t, n) {
  var r = e.length - 2;
  t[Te] = $(e.substr(0, r)), t[et] = $(e.substr(r)), B(n).bigHour = !0;
});
ue("hmmss", function(e, t, n) {
  var r = e.length - 4, s = e.length - 2;
  t[Te] = $(e.substr(0, r)), t[et] = $(e.substr(r, 2)), t[yt] = $(e.substr(s)), B(n).bigHour = !0;
});
ue("Hmm", function(e, t, n) {
  var r = e.length - 2;
  t[Te] = $(e.substr(0, r)), t[et] = $(e.substr(r));
});
ue("Hmmss", function(e, t, n) {
  var r = e.length - 4, s = e.length - 2;
  t[Te] = $(e.substr(0, r)), t[et] = $(e.substr(r, 2)), t[yt] = $(e.substr(s));
});
function uu(e) {
  return (e + "").toLowerCase().charAt(0) === "p";
}
var cu = /[ap]\.?m?\.?/i, du = Xt("Hours", !0);
function fu(e, t, n) {
  return e > 11 ? n ? "pm" : "PM" : n ? "am" : "AM";
}
var Fo = {
  calendar: tl,
  longDateFormat: ol,
  invalidDate: al,
  ordinal: ul,
  dayOfMonthOrdinalParse: cl,
  relativeTime: fl,
  months: Ml,
  monthsShort: bo,
  week: Wl,
  weekdays: jl,
  weekdaysMin: $l,
  weekdaysShort: Po,
  meridiemParse: cu
}, he = {}, rn = {}, yn;
function hu(e, t) {
  var n, r = Math.min(e.length, t.length);
  for (n = 0; n < r; n += 1)
    if (e[n] !== t[n])
      return n;
  return r;
}
function Ls(e) {
  return e && e.toLowerCase().replace("_", "-");
}
function mu(e) {
  for (var t = 0, n, r, s, o; t < e.length; ) {
    for (o = Ls(e[t]).split("-"), n = o.length, r = Ls(e[t + 1]), r = r ? r.split("-") : null; n > 0; ) {
      if (s = ar(o.slice(0, n).join("-")), s)
        return s;
      if (r && r.length >= n && hu(o, r) >= n - 1)
        break;
      n--;
    }
    t++;
  }
  return yn;
}
function pu(e) {
  return e.match("^[^/\\\\]*$") != null;
}
function ar(e) {
  var t = null, n;
  if (he[e] === void 0 && typeof module < "u" && module && module.exports && pu(e))
    try {
      t = yn._abbr, n = require, n("./locale/" + e), bt(t);
    } catch {
      he[e] = null;
    }
  return he[e];
}
function bt(e, t) {
  var n;
  return e && (He(t) ? n = vt(e) : n = es(e, t), n ? yn = n : typeof console < "u" && console.warn && console.warn(
    "Locale " + e + " not found. Did you forget to load it?"
  )), yn._abbr;
}
function es(e, t) {
  if (t !== null) {
    var n, r = Fo;
    if (t.abbr = e, he[e] != null)
      So(
        "defineLocaleOverride",
        "use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."
      ), r = he[e]._config;
    else if (t.parentLocale != null)
      if (he[t.parentLocale] != null)
        r = he[t.parentLocale]._config;
      else if (n = ar(t.parentLocale), n != null)
        r = n._config;
      else
        return rn[t.parentLocale] || (rn[t.parentLocale] = []), rn[t.parentLocale].push({
          name: e,
          config: t
        }), null;
    return he[e] = new jr(Dr(r, t)), rn[e] && rn[e].forEach(function(s) {
      es(s.name, s.config);
    }), bt(e), he[e];
  } else
    return delete he[e], null;
}
function gu(e, t) {
  if (t != null) {
    var n, r, s = Fo;
    he[e] != null && he[e].parentLocale != null ? he[e].set(Dr(he[e]._config, t)) : (r = ar(e), r != null && (s = r._config), t = Dr(s, t), r == null && (t.abbr = e), n = new jr(t), n.parentLocale = he[e], he[e] = n), bt(e);
  } else
    he[e] != null && (he[e].parentLocale != null ? (he[e] = he[e].parentLocale, e === bt() && bt(e)) : he[e] != null && delete he[e]);
  return he[e];
}
function vt(e) {
  var t;
  if (e && e._locale && e._locale._abbr && (e = e._locale._abbr), !e)
    return yn;
  if (!rt(e)) {
    if (t = ar(e), t)
      return t;
    e = [e];
  }
  return mu(e);
}
function yu() {
  return br(he);
}
function ts(e) {
  var t, n = e._a;
  return n && B(e).overflow === -2 && (t = n[gt] < 0 || n[gt] > 11 ? gt : n[at] < 1 || n[at] > ir(n[Oe], n[gt]) ? at : n[Te] < 0 || n[Te] > 24 || n[Te] === 24 && (n[et] !== 0 || n[yt] !== 0 || n[Pt] !== 0) ? Te : n[et] < 0 || n[et] > 59 ? et : n[yt] < 0 || n[yt] > 59 ? yt : n[Pt] < 0 || n[Pt] > 999 ? Pt : -1, B(e)._overflowDayOfYear && (t < Oe || t > at) && (t = at), B(e)._overflowWeeks && t === -1 && (t = Tl), B(e)._overflowWeekday && t === -1 && (t = kl), B(e).overflow = t), e;
}
var _u = /^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/, wu = /^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/, Su = /Z|[+-]\d\d(?::?\d\d)?/, Yn = [
  ["YYYYYY-MM-DD", /[+-]\d{6}-\d\d-\d\d/],
  ["YYYY-MM-DD", /\d{4}-\d\d-\d\d/],
  ["GGGG-[W]WW-E", /\d{4}-W\d\d-\d/],
  ["GGGG-[W]WW", /\d{4}-W\d\d/, !1],
  ["YYYY-DDD", /\d{4}-\d{3}/],
  ["YYYY-MM", /\d{4}-\d\d/, !1],
  ["YYYYYYMMDD", /[+-]\d{10}/],
  ["YYYYMMDD", /\d{8}/],
  ["GGGG[W]WWE", /\d{4}W\d{3}/],
  ["GGGG[W]WW", /\d{4}W\d{2}/, !1],
  ["YYYYDDD", /\d{7}/],
  ["YYYYMM", /\d{6}/, !1],
  ["YYYY", /\d{4}/, !1]
], wr = [
  ["HH:mm:ss.SSSS", /\d\d:\d\d:\d\d\.\d+/],
  ["HH:mm:ss,SSSS", /\d\d:\d\d:\d\d,\d+/],
  ["HH:mm:ss", /\d\d:\d\d:\d\d/],
  ["HH:mm", /\d\d:\d\d/],
  ["HHmmss.SSSS", /\d\d\d\d\d\d\.\d+/],
  ["HHmmss,SSSS", /\d\d\d\d\d\d,\d+/],
  ["HHmmss", /\d\d\d\d\d\d/],
  ["HHmm", /\d\d\d\d/],
  ["HH", /\d\d/]
], vu = /^\/?Date\((-?\d+)/i, xu = /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/, Tu = {
  UT: 0,
  GMT: 0,
  EDT: -4 * 60,
  EST: -5 * 60,
  CDT: -5 * 60,
  CST: -6 * 60,
  MDT: -6 * 60,
  MST: -7 * 60,
  PDT: -7 * 60,
  PST: -8 * 60
};
function Wo(e) {
  var t, n, r = e._i, s = _u.exec(r) || wu.exec(r), o, i, a, l, u = Yn.length, c = wr.length;
  if (s) {
    for (B(e).iso = !0, t = 0, n = u; t < n; t++)
      if (Yn[t][1].exec(s[1])) {
        i = Yn[t][0], o = Yn[t][2] !== !1;
        break;
      }
    if (i == null) {
      e._isValid = !1;
      return;
    }
    if (s[3]) {
      for (t = 0, n = c; t < n; t++)
        if (wr[t][1].exec(s[3])) {
          a = (s[2] || " ") + wr[t][0];
          break;
        }
      if (a == null) {
        e._isValid = !1;
        return;
      }
    }
    if (!o && a != null) {
      e._isValid = !1;
      return;
    }
    if (s[4])
      if (Su.exec(s[4]))
        l = "Z";
      else {
        e._isValid = !1;
        return;
      }
    e._f = i + (a || "") + (l || ""), rs(e);
  } else
    e._isValid = !1;
}
function ku(e, t, n, r, s, o) {
  var i = [
    Iu(e),
    bo.indexOf(t),
    parseInt(n, 10),
    parseInt(r, 10),
    parseInt(s, 10)
  ];
  return o && i.push(parseInt(o, 10)), i;
}
function Iu(e) {
  var t = parseInt(e, 10);
  return t <= 49 ? 2e3 + t : t <= 999 ? 1900 + t : t;
}
function Mu(e) {
  return e.replace(/\([^()]*\)|[\n\t]/g, " ").replace(/(\s\s+)/g, " ").replace(/^\s\s*/, "").replace(/\s\s*$/, "");
}
function Du(e, t, n) {
  if (e) {
    var r = Po.indexOf(e), s = new Date(
      t[0],
      t[1],
      t[2]
    ).getDay();
    if (r !== s)
      return B(n).weekdayMismatch = !0, n._isValid = !1, !1;
  }
  return !0;
}
function bu(e, t, n) {
  if (e)
    return Tu[e];
  if (t)
    return 0;
  var r = parseInt(n, 10), s = r % 100, o = (r - s) / 100;
  return o * 60 + s;
}
function Ao(e) {
  var t = xu.exec(Mu(e._i)), n;
  if (t) {
    if (n = ku(
      t[4],
      t[3],
      t[2],
      t[5],
      t[6],
      t[7]
    ), !Du(t[1], n, e))
      return;
    e._a = n, e._tzm = bu(t[8], t[9], t[10]), e._d = pn.apply(null, e._a), e._d.setUTCMinutes(e._d.getUTCMinutes() - e._tzm), B(e).rfc2822 = !0;
  } else
    e._isValid = !1;
}
function Ou(e) {
  var t = vu.exec(e._i);
  if (t !== null) {
    e._d = /* @__PURE__ */ new Date(+t[1]);
    return;
  }
  if (Wo(e), e._isValid === !1)
    delete e._isValid;
  else
    return;
  if (Ao(e), e._isValid === !1)
    delete e._isValid;
  else
    return;
  e._strict ? e._isValid = !1 : I.createFromInputFallback(e);
}
I.createFromInputFallback = Ke(
  "value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",
  function(e) {
    e._d = /* @__PURE__ */ new Date(e._i + (e._useUTC ? " UTC" : ""));
  }
);
function zt(e, t, n) {
  return e ?? t ?? n;
}
function Cu(e) {
  var t = new Date(I.now());
  return e._useUTC ? [
    t.getUTCFullYear(),
    t.getUTCMonth(),
    t.getUTCDate()
  ] : [t.getFullYear(), t.getMonth(), t.getDate()];
}
function ns(e) {
  var t, n, r = [], s, o, i;
  if (!e._d) {
    for (s = Cu(e), e._w && e._a[at] == null && e._a[gt] == null && Ru(e), e._dayOfYear != null && (i = zt(e._a[Oe], s[Oe]), (e._dayOfYear > un(i) || e._dayOfYear === 0) && (B(e)._overflowDayOfYear = !0), n = pn(i, 0, e._dayOfYear), e._a[gt] = n.getUTCMonth(), e._a[at] = n.getUTCDate()), t = 0; t < 3 && e._a[t] == null; ++t)
      e._a[t] = r[t] = s[t];
    for (; t < 7; t++)
      e._a[t] = r[t] = e._a[t] == null ? t === 2 ? 1 : 0 : e._a[t];
    e._a[Te] === 24 && e._a[et] === 0 && e._a[yt] === 0 && e._a[Pt] === 0 && (e._nextDay = !0, e._a[Te] = 0), e._d = (e._useUTC ? pn : Hl).apply(
      null,
      r
    ), o = e._useUTC ? e._d.getUTCDay() : e._d.getDay(), e._tzm != null && e._d.setUTCMinutes(e._d.getUTCMinutes() - e._tzm), e._nextDay && (e._a[Te] = 24), e._w && typeof e._w.d < "u" && e._w.d !== o && (B(e).weekdayMismatch = !0);
  }
}
function Ru(e) {
  var t, n, r, s, o, i, a, l, u;
  t = e._w, t.GG != null || t.W != null || t.E != null ? (o = 1, i = 4, n = zt(
    t.GG,
    e._a[Oe],
    gn(de(), 1, 4).year
  ), r = zt(t.W, 1), s = zt(t.E, 1), (s < 1 || s > 7) && (l = !0)) : (o = e._locale._week.dow, i = e._locale._week.doy, u = gn(de(), o, i), n = zt(t.gg, e._a[Oe], u.year), r = zt(t.w, u.week), t.d != null ? (s = t.d, (s < 0 || s > 6) && (l = !0)) : t.e != null ? (s = t.e + o, (t.e < 0 || t.e > 6) && (l = !0)) : s = o), r < 1 || r > _t(n, o, i) ? B(e)._overflowWeeks = !0 : l != null ? B(e)._overflowWeekday = !0 : (a = No(n, r, s, o, i), e._a[Oe] = a.year, e._dayOfYear = a.dayOfYear);
}
I.ISO_8601 = function() {
};
I.RFC_2822 = function() {
};
function rs(e) {
  if (e._f === I.ISO_8601) {
    Wo(e);
    return;
  }
  if (e._f === I.RFC_2822) {
    Ao(e);
    return;
  }
  e._a = [], B(e).empty = !0;
  var t = "" + e._i, n, r, s, o, i, a = t.length, l = 0, u, c;
  for (s = vo(e._f, e._locale).match($r) || [], c = s.length, n = 0; n < c; n++)
    o = s[n], r = (t.match(Sl(o, e)) || [])[0], r && (i = t.substr(0, t.indexOf(r)), i.length > 0 && B(e).unusedInput.push(i), t = t.slice(
      t.indexOf(r) + r.length
    ), l += r.length), $t[o] ? (r ? B(e).empty = !1 : B(e).unusedTokens.push(o), xl(o, r, e)) : e._strict && !r && B(e).unusedTokens.push(o);
  B(e).charsLeftOver = a - l, t.length > 0 && B(e).unusedInput.push(t), e._a[Te] <= 12 && B(e).bigHour === !0 && e._a[Te] > 0 && (B(e).bigHour = void 0), B(e).parsedDateParts = e._a.slice(0), B(e).meridiem = e._meridiem, e._a[Te] = Yu(
    e._locale,
    e._a[Te],
    e._meridiem
  ), u = B(e).era, u !== null && (e._a[Oe] = e._locale.erasConvertYear(u, e._a[Oe])), ns(e), ts(e);
}
function Yu(e, t, n) {
  var r;
  return n == null ? t : e.meridiemHour != null ? e.meridiemHour(t, n) : (e.isPM != null && (r = e.isPM(n), r && t < 12 && (t += 12), !r && t === 12 && (t = 0)), t);
}
function Eu(e) {
  var t, n, r, s, o, i, a = !1, l = e._f.length;
  if (l === 0) {
    B(e).invalidFormat = !0, e._d = /* @__PURE__ */ new Date(NaN);
    return;
  }
  for (s = 0; s < l; s++)
    o = 0, i = !1, t = Br({}, e), e._useUTC != null && (t._useUTC = e._useUTC), t._f = e._f[s], rs(t), zr(t) && (i = !0), o += B(t).charsLeftOver, o += B(t).unusedTokens.length * 10, B(t).score = o, a ? o < r && (r = o, n = t) : (r == null || o < r || i) && (r = o, n = t, i && (a = !0));
  Mt(e, n || t);
}
function Nu(e) {
  if (!e._d) {
    var t = Zr(e._i), n = t.day === void 0 ? t.date : t.day;
    e._a = _o(
      [t.year, t.month, n, t.hour, t.minute, t.second, t.millisecond],
      function(r) {
        return r && parseInt(r, 10);
      }
    ), ns(e);
  }
}
function Pu(e) {
  var t = new xn(ts(Vo(e)));
  return t._nextDay && (t.add(1, "d"), t._nextDay = void 0), t;
}
function Vo(e) {
  var t = e._i, n = e._f;
  return e._locale = e._locale || vt(e._l), t === null || n === void 0 && t === "" ? Xn({ nullInput: !0 }) : (typeof t == "string" && (e._i = t = e._locale.preparse(t)), st(t) ? new xn(ts(t)) : (vn(t) ? e._d = t : rt(n) ? Eu(e) : n ? rs(e) : Lu(e), zr(e) || (e._d = null), e));
}
function Lu(e) {
  var t = e._i;
  He(t) ? e._d = new Date(I.now()) : vn(t) ? e._d = new Date(t.valueOf()) : typeof t == "string" ? Ou(e) : rt(t) ? (e._a = _o(t.slice(0), function(n) {
    return parseInt(n, 10);
  }), ns(e)) : Lt(t) ? Nu(e) : St(t) ? e._d = new Date(t) : I.createFromInputFallback(e);
}
function Uo(e, t, n, r, s) {
  var o = {};
  return (t === !0 || t === !1) && (r = t, t = void 0), (n === !0 || n === !1) && (r = n, n = void 0), (Lt(e) && Gr(e) || rt(e) && e.length === 0) && (e = void 0), o._isAMomentObject = !0, o._useUTC = o._isUTC = s, o._l = n, o._i = e, o._f = t, o._strict = r, Pu(o);
}
function de(e, t, n, r) {
  return Uo(e, t, n, r, !1);
}
var Hu = Ke(
  "moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",
  function() {
    var e = de.apply(null, arguments);
    return this.isValid() && e.isValid() ? e < this ? this : e : Xn();
  }
), Fu = Ke(
  "moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",
  function() {
    var e = de.apply(null, arguments);
    return this.isValid() && e.isValid() ? e > this ? this : e : Xn();
  }
);
function Go(e, t) {
  var n, r;
  if (t.length === 1 && rt(t[0]) && (t = t[0]), !t.length)
    return de();
  for (n = t[0], r = 1; r < t.length; ++r)
    (!t[r].isValid() || t[r][e](n)) && (n = t[r]);
  return n;
}
function Wu() {
  var e = [].slice.call(arguments, 0);
  return Go("isBefore", e);
}
function Au() {
  var e = [].slice.call(arguments, 0);
  return Go("isAfter", e);
}
var Vu = function() {
  return Date.now ? Date.now() : +/* @__PURE__ */ new Date();
}, sn = [
  "year",
  "quarter",
  "month",
  "week",
  "day",
  "hour",
  "minute",
  "second",
  "millisecond"
];
function Uu(e) {
  var t, n = !1, r, s = sn.length;
  for (t in e)
    if (ne(e, t) && !(we.call(sn, t) !== -1 && (e[t] == null || !isNaN(e[t]))))
      return !1;
  for (r = 0; r < s; ++r)
    if (e[sn[r]]) {
      if (n)
        return !1;
      parseFloat(e[sn[r]]) !== $(e[sn[r]]) && (n = !0);
    }
  return !0;
}
function Gu() {
  return this._isValid;
}
function zu() {
  return ot(NaN);
}
function lr(e) {
  var t = Zr(e), n = t.year || 0, r = t.quarter || 0, s = t.month || 0, o = t.week || t.isoWeek || 0, i = t.day || 0, a = t.hour || 0, l = t.minute || 0, u = t.second || 0, c = t.millisecond || 0;
  this._isValid = Uu(t), this._milliseconds = +c + u * 1e3 + // 1000
  l * 6e4 + // 1000 * 60
  a * 1e3 * 60 * 60, this._days = +i + o * 7, this._months = +s + r * 3 + n * 12, this._data = {}, this._locale = vt(), this._bubble();
}
function Ln(e) {
  return e instanceof lr;
}
function Cr(e) {
  return e < 0 ? Math.round(-1 * e) * -1 : Math.round(e);
}
function Bu(e, t, n) {
  var r = Math.min(e.length, t.length), s = Math.abs(e.length - t.length), o = 0, i;
  for (i = 0; i < r; i++)
    (n && e[i] !== t[i] || !n && $(e[i]) !== $(t[i])) && o++;
  return o + s;
}
function zo(e, t) {
  P(e, 0, 0, function() {
    var n = this.utcOffset(), r = "+";
    return n < 0 && (n = -n, r = "-"), r + ut(~~(n / 60), 2) + t + ut(~~n % 60, 2);
  });
}
zo("Z", ":");
zo("ZZ", "");
C("Z", or);
C("ZZ", or);
ue(["Z", "ZZ"], function(e, t, n) {
  n._useUTC = !0, n._tzm = ss(or, e);
});
var ju = /([\+\-]|\d\d)/gi;
function ss(e, t) {
  var n = (t || "").match(e), r, s, o;
  return n === null ? null : (r = n[n.length - 1] || [], s = (r + "").match(ju) || ["-", 0, 0], o = +(s[1] * 60) + $(s[2]), o === 0 ? 0 : s[0] === "+" ? o : -o);
}
function os(e, t) {
  var n, r;
  return t._isUTC ? (n = t.clone(), r = (st(e) || vn(e) ? e.valueOf() : de(e).valueOf()) - n.valueOf(), n._d.setTime(n._d.valueOf() + r), I.updateOffset(n, !1), n) : de(e).local();
}
function Rr(e) {
  return -Math.round(e._d.getTimezoneOffset());
}
I.updateOffset = function() {
};
function $u(e, t, n) {
  var r = this._offset || 0, s;
  if (!this.isValid())
    return e != null ? this : NaN;
  if (e != null) {
    if (typeof e == "string") {
      if (e = ss(or, e), e === null)
        return this;
    } else
      Math.abs(e) < 16 && !n && (e = e * 60);
    return !this._isUTC && t && (s = Rr(this)), this._offset = e, this._isUTC = !0, s != null && this.add(s, "m"), r !== e && (!t || this._changeInProgress ? $o(
      this,
      ot(e - r, "m"),
      1,
      !1
    ) : this._changeInProgress || (this._changeInProgress = !0, I.updateOffset(this, !0), this._changeInProgress = null)), this;
  } else
    return this._isUTC ? r : Rr(this);
}
function Zu(e, t) {
  return e != null ? (typeof e != "string" && (e = -e), this.utcOffset(e, t), this) : -this.utcOffset();
}
function qu(e) {
  return this.utcOffset(0, e);
}
function Ku(e) {
  return this._isUTC && (this.utcOffset(0, e), this._isUTC = !1, e && this.subtract(Rr(this), "m")), this;
}
function Ju() {
  if (this._tzm != null)
    this.utcOffset(this._tzm, !1, !0);
  else if (typeof this._i == "string") {
    var e = ss(_l, this._i);
    e != null ? this.utcOffset(e) : this.utcOffset(0, !0);
  }
  return this;
}
function Qu(e) {
  return this.isValid() ? (e = e ? de(e).utcOffset() : 0, (this.utcOffset() - e) % 60 === 0) : !1;
}
function Xu() {
  return this.utcOffset() > this.clone().month(0).utcOffset() || this.utcOffset() > this.clone().month(5).utcOffset();
}
function ec() {
  if (!He(this._isDSTShifted))
    return this._isDSTShifted;
  var e = {}, t;
  return Br(e, this), e = Vo(e), e._a ? (t = e._isUTC ? dt(e._a) : de(e._a), this._isDSTShifted = this.isValid() && Bu(e._a, t.toArray()) > 0) : this._isDSTShifted = !1, this._isDSTShifted;
}
function tc() {
  return this.isValid() ? !this._isUTC : !1;
}
function nc() {
  return this.isValid() ? this._isUTC : !1;
}
function Bo() {
  return this.isValid() ? this._isUTC && this._offset === 0 : !1;
}
var rc = /^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/, sc = /^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;
function ot(e, t) {
  var n = e, r = null, s, o, i;
  return Ln(e) ? n = {
    ms: e._milliseconds,
    d: e._days,
    M: e._months
  } : St(e) || !isNaN(+e) ? (n = {}, t ? n[t] = +e : n.milliseconds = +e) : (r = rc.exec(e)) ? (s = r[1] === "-" ? -1 : 1, n = {
    y: 0,
    d: $(r[at]) * s,
    h: $(r[Te]) * s,
    m: $(r[et]) * s,
    s: $(r[yt]) * s,
    ms: $(Cr(r[Pt] * 1e3)) * s
    // the millisecond decimal point is included in the match
  }) : (r = sc.exec(e)) ? (s = r[1] === "-" ? -1 : 1, n = {
    y: Et(r[2], s),
    M: Et(r[3], s),
    w: Et(r[4], s),
    d: Et(r[5], s),
    h: Et(r[6], s),
    m: Et(r[7], s),
    s: Et(r[8], s)
  }) : n == null ? n = {} : typeof n == "object" && ("from" in n || "to" in n) && (i = oc(
    de(n.from),
    de(n.to)
  ), n = {}, n.ms = i.milliseconds, n.M = i.months), o = new lr(n), Ln(e) && ne(e, "_locale") && (o._locale = e._locale), Ln(e) && ne(e, "_isValid") && (o._isValid = e._isValid), o;
}
ot.fn = lr.prototype;
ot.invalid = zu;
function Et(e, t) {
  var n = e && parseFloat(e.replace(",", "."));
  return (isNaN(n) ? 0 : n) * t;
}
function Hs(e, t) {
  var n = {};
  return n.months = t.month() - e.month() + (t.year() - e.year()) * 12, e.clone().add(n.months, "M").isAfter(t) && --n.months, n.milliseconds = +t - +e.clone().add(n.months, "M"), n;
}
function oc(e, t) {
  var n;
  return e.isValid() && t.isValid() ? (t = os(t, e), e.isBefore(t) ? n = Hs(e, t) : (n = Hs(t, e), n.milliseconds = -n.milliseconds, n.months = -n.months), n) : { milliseconds: 0, months: 0 };
}
function jo(e, t) {
  return function(n, r) {
    var s, o;
    return r !== null && !isNaN(+r) && (So(
      t,
      "moment()." + t + "(period, number) is deprecated. Please use moment()." + t + "(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."
    ), o = n, n = r, r = o), s = ot(n, r), $o(this, s, e), this;
  };
}
function $o(e, t, n, r) {
  var s = t._milliseconds, o = Cr(t._days), i = Cr(t._months);
  e.isValid() && (r = r ?? !0, i && Co(e, Un(e, "Month") + i * n), o && To(e, "Date", Un(e, "Date") + o * n), s && e._d.setTime(e._d.valueOf() + s * n), r && I.updateOffset(e, o || i));
}
var ic = jo(1, "add"), ac = jo(-1, "subtract");
function Zo(e) {
  return typeof e == "string" || e instanceof String;
}
function lc(e) {
  return st(e) || vn(e) || Zo(e) || St(e) || cc(e) || uc(e) || e === null || e === void 0;
}
function uc(e) {
  var t = Lt(e) && !Gr(e), n = !1, r = [
    "years",
    "year",
    "y",
    "months",
    "month",
    "M",
    "days",
    "day",
    "d",
    "dates",
    "date",
    "D",
    "hours",
    "hour",
    "h",
    "minutes",
    "minute",
    "m",
    "seconds",
    "second",
    "s",
    "milliseconds",
    "millisecond",
    "ms"
  ], s, o, i = r.length;
  for (s = 0; s < i; s += 1)
    o = r[s], n = n || ne(e, o);
  return t && n;
}
function cc(e) {
  var t = rt(e), n = !1;
  return t && (n = e.filter(function(r) {
    return !St(r) && Zo(e);
  }).length === 0), t && n;
}
function dc(e) {
  var t = Lt(e) && !Gr(e), n = !1, r = [
    "sameDay",
    "nextDay",
    "lastDay",
    "nextWeek",
    "lastWeek",
    "sameElse"
  ], s, o;
  for (s = 0; s < r.length; s += 1)
    o = r[s], n = n || ne(e, o);
  return t && n;
}
function fc(e, t) {
  var n = e.diff(t, "days", !0);
  return n < -6 ? "sameElse" : n < -1 ? "lastWeek" : n < 0 ? "lastDay" : n < 1 ? "sameDay" : n < 2 ? "nextDay" : n < 7 ? "nextWeek" : "sameElse";
}
function hc(e, t) {
  arguments.length === 1 && (arguments[0] ? lc(arguments[0]) ? (e = arguments[0], t = void 0) : dc(arguments[0]) && (t = arguments[0], e = void 0) : (e = void 0, t = void 0));
  var n = e || de(), r = os(n, this).startOf("day"), s = I.calendarFormat(this, r) || "sameElse", o = t && (ft(t[s]) ? t[s].call(this, n) : t[s]);
  return this.format(
    o || this.localeData().calendar(s, this, de(n))
  );
}
function mc() {
  return new xn(this);
}
function pc(e, t) {
  var n = st(e) ? e : de(e);
  return this.isValid() && n.isValid() ? (t = Je(t) || "millisecond", t === "millisecond" ? this.valueOf() > n.valueOf() : n.valueOf() < this.clone().startOf(t).valueOf()) : !1;
}
function gc(e, t) {
  var n = st(e) ? e : de(e);
  return this.isValid() && n.isValid() ? (t = Je(t) || "millisecond", t === "millisecond" ? this.valueOf() < n.valueOf() : this.clone().endOf(t).valueOf() < n.valueOf()) : !1;
}
function yc(e, t, n, r) {
  var s = st(e) ? e : de(e), o = st(t) ? t : de(t);
  return this.isValid() && s.isValid() && o.isValid() ? (r = r || "()", (r[0] === "(" ? this.isAfter(s, n) : !this.isBefore(s, n)) && (r[1] === ")" ? this.isBefore(o, n) : !this.isAfter(o, n))) : !1;
}
function _c(e, t) {
  var n = st(e) ? e : de(e), r;
  return this.isValid() && n.isValid() ? (t = Je(t) || "millisecond", t === "millisecond" ? this.valueOf() === n.valueOf() : (r = n.valueOf(), this.clone().startOf(t).valueOf() <= r && r <= this.clone().endOf(t).valueOf())) : !1;
}
function wc(e, t) {
  return this.isSame(e, t) || this.isAfter(e, t);
}
function Sc(e, t) {
  return this.isSame(e, t) || this.isBefore(e, t);
}
function vc(e, t, n) {
  var r, s, o;
  if (!this.isValid())
    return NaN;
  if (r = os(e, this), !r.isValid())
    return NaN;
  switch (s = (r.utcOffset() - this.utcOffset()) * 6e4, t = Je(t), t) {
    case "year":
      o = Hn(this, r) / 12;
      break;
    case "month":
      o = Hn(this, r);
      break;
    case "quarter":
      o = Hn(this, r) / 3;
      break;
    case "second":
      o = (this - r) / 1e3;
      break;
    case "minute":
      o = (this - r) / 6e4;
      break;
    case "hour":
      o = (this - r) / 36e5;
      break;
    case "day":
      o = (this - r - s) / 864e5;
      break;
    case "week":
      o = (this - r - s) / 6048e5;
      break;
    default:
      o = this - r;
  }
  return n ? o : Ze(o);
}
function Hn(e, t) {
  if (e.date() < t.date())
    return -Hn(t, e);
  var n = (t.year() - e.year()) * 12 + (t.month() - e.month()), r = e.clone().add(n, "months"), s, o;
  return t - r < 0 ? (s = e.clone().add(n - 1, "months"), o = (t - r) / (r - s)) : (s = e.clone().add(n + 1, "months"), o = (t - r) / (s - r)), -(n + o) || 0;
}
I.defaultFormat = "YYYY-MM-DDTHH:mm:ssZ";
I.defaultFormatUtc = "YYYY-MM-DDTHH:mm:ss[Z]";
function xc() {
  return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ");
}
function Tc(e) {
  if (!this.isValid())
    return null;
  var t = e !== !0, n = t ? this.clone().utc() : this;
  return n.year() < 0 || n.year() > 9999 ? Pn(
    n,
    t ? "YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]" : "YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"
  ) : ft(Date.prototype.toISOString) ? t ? this.toDate().toISOString() : new Date(this.valueOf() + this.utcOffset() * 60 * 1e3).toISOString().replace("Z", Pn(n, "Z")) : Pn(
    n,
    t ? "YYYY-MM-DD[T]HH:mm:ss.SSS[Z]" : "YYYY-MM-DD[T]HH:mm:ss.SSSZ"
  );
}
function kc() {
  if (!this.isValid())
    return "moment.invalid(/* " + this._i + " */)";
  var e = "moment", t = "", n, r, s, o;
  return this.isLocal() || (e = this.utcOffset() === 0 ? "moment.utc" : "moment.parseZone", t = "Z"), n = "[" + e + '("]', r = 0 <= this.year() && this.year() <= 9999 ? "YYYY" : "YYYYYY", s = "-MM-DD[T]HH:mm:ss.SSS", o = t + '[")]', this.format(n + r + s + o);
}
function Ic(e) {
  e || (e = this.isUtc() ? I.defaultFormatUtc : I.defaultFormat);
  var t = Pn(this, e);
  return this.localeData().postformat(t);
}
function Mc(e, t) {
  return this.isValid() && (st(e) && e.isValid() || de(e).isValid()) ? ot({ to: this, from: e }).locale(this.locale()).humanize(!t) : this.localeData().invalidDate();
}
function Dc(e) {
  return this.from(de(), e);
}
function bc(e, t) {
  return this.isValid() && (st(e) && e.isValid() || de(e).isValid()) ? ot({ from: this, to: e }).locale(this.locale()).humanize(!t) : this.localeData().invalidDate();
}
function Oc(e) {
  return this.to(de(), e);
}
function qo(e) {
  var t;
  return e === void 0 ? this._locale._abbr : (t = vt(e), t != null && (this._locale = t), this);
}
var Ko = Ke(
  "moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",
  function(e) {
    return e === void 0 ? this.localeData() : this.locale(e);
  }
);
function Jo() {
  return this._locale;
}
var Bn = 1e3, Zt = 60 * Bn, jn = 60 * Zt, Qo = (365 * 400 + 97) * 24 * jn;
function qt(e, t) {
  return (e % t + t) % t;
}
function Xo(e, t, n) {
  return e < 100 && e >= 0 ? new Date(e + 400, t, n) - Qo : new Date(e, t, n).valueOf();
}
function ei(e, t, n) {
  return e < 100 && e >= 0 ? Date.UTC(e + 400, t, n) - Qo : Date.UTC(e, t, n);
}
function Cc(e) {
  var t, n;
  if (e = Je(e), e === void 0 || e === "millisecond" || !this.isValid())
    return this;
  switch (n = this._isUTC ? ei : Xo, e) {
    case "year":
      t = n(this.year(), 0, 1);
      break;
    case "quarter":
      t = n(
        this.year(),
        this.month() - this.month() % 3,
        1
      );
      break;
    case "month":
      t = n(this.year(), this.month(), 1);
      break;
    case "week":
      t = n(
        this.year(),
        this.month(),
        this.date() - this.weekday()
      );
      break;
    case "isoWeek":
      t = n(
        this.year(),
        this.month(),
        this.date() - (this.isoWeekday() - 1)
      );
      break;
    case "day":
    case "date":
      t = n(this.year(), this.month(), this.date());
      break;
    case "hour":
      t = this._d.valueOf(), t -= qt(
        t + (this._isUTC ? 0 : this.utcOffset() * Zt),
        jn
      );
      break;
    case "minute":
      t = this._d.valueOf(), t -= qt(t, Zt);
      break;
    case "second":
      t = this._d.valueOf(), t -= qt(t, Bn);
      break;
  }
  return this._d.setTime(t), I.updateOffset(this, !0), this;
}
function Rc(e) {
  var t, n;
  if (e = Je(e), e === void 0 || e === "millisecond" || !this.isValid())
    return this;
  switch (n = this._isUTC ? ei : Xo, e) {
    case "year":
      t = n(this.year() + 1, 0, 1) - 1;
      break;
    case "quarter":
      t = n(
        this.year(),
        this.month() - this.month() % 3 + 3,
        1
      ) - 1;
      break;
    case "month":
      t = n(this.year(), this.month() + 1, 1) - 1;
      break;
    case "week":
      t = n(
        this.year(),
        this.month(),
        this.date() - this.weekday() + 7
      ) - 1;
      break;
    case "isoWeek":
      t = n(
        this.year(),
        this.month(),
        this.date() - (this.isoWeekday() - 1) + 7
      ) - 1;
      break;
    case "day":
    case "date":
      t = n(this.year(), this.month(), this.date() + 1) - 1;
      break;
    case "hour":
      t = this._d.valueOf(), t += jn - qt(
        t + (this._isUTC ? 0 : this.utcOffset() * Zt),
        jn
      ) - 1;
      break;
    case "minute":
      t = this._d.valueOf(), t += Zt - qt(t, Zt) - 1;
      break;
    case "second":
      t = this._d.valueOf(), t += Bn - qt(t, Bn) - 1;
      break;
  }
  return this._d.setTime(t), I.updateOffset(this, !0), this;
}
function Yc() {
  return this._d.valueOf() - (this._offset || 0) * 6e4;
}
function Ec() {
  return Math.floor(this.valueOf() / 1e3);
}
function Nc() {
  return new Date(this.valueOf());
}
function Pc() {
  var e = this;
  return [
    e.year(),
    e.month(),
    e.date(),
    e.hour(),
    e.minute(),
    e.second(),
    e.millisecond()
  ];
}
function Lc() {
  var e = this;
  return {
    years: e.year(),
    months: e.month(),
    date: e.date(),
    hours: e.hours(),
    minutes: e.minutes(),
    seconds: e.seconds(),
    milliseconds: e.milliseconds()
  };
}
function Hc() {
  return this.isValid() ? this.toISOString() : null;
}
function Fc() {
  return zr(this);
}
function Wc() {
  return Mt({}, B(this));
}
function Ac() {
  return B(this).overflow;
}
function Vc() {
  return {
    input: this._i,
    format: this._f,
    locale: this._locale,
    isUTC: this._isUTC,
    strict: this._strict
  };
}
P("N", 0, 0, "eraAbbr");
P("NN", 0, 0, "eraAbbr");
P("NNN", 0, 0, "eraAbbr");
P("NNNN", 0, 0, "eraName");
P("NNNNN", 0, 0, "eraNarrow");
P("y", ["y", 1], "yo", "eraYear");
P("y", ["yy", 2], 0, "eraYear");
P("y", ["yyy", 3], 0, "eraYear");
P("y", ["yyyy", 4], 0, "eraYear");
C("N", is);
C("NN", is);
C("NNN", is);
C("NNNN", Qc);
C("NNNNN", Xc);
ue(
  ["N", "NN", "NNN", "NNNN", "NNNNN"],
  function(e, t, n, r) {
    var s = n._locale.erasParse(e, r, n._strict);
    s ? B(n).era = s : B(n).invalidEra = e;
  }
);
C("y", en);
C("yy", en);
C("yyy", en);
C("yyyy", en);
C("yo", ed);
ue(["y", "yy", "yyy", "yyyy"], Oe);
ue(["yo"], function(e, t, n, r) {
  var s;
  n._locale._eraYearOrdinalRegex && (s = e.match(n._locale._eraYearOrdinalRegex)), n._locale.eraYearOrdinalParse ? t[Oe] = n._locale.eraYearOrdinalParse(e, s) : t[Oe] = parseInt(e, 10);
});
function Uc(e, t) {
  var n, r, s, o = this._eras || vt("en")._eras;
  for (n = 0, r = o.length; n < r; ++n) {
    switch (typeof o[n].since) {
      case "string":
        s = I(o[n].since).startOf("day"), o[n].since = s.valueOf();
        break;
    }
    switch (typeof o[n].until) {
      case "undefined":
        o[n].until = 1 / 0;
        break;
      case "string":
        s = I(o[n].until).startOf("day").valueOf(), o[n].until = s.valueOf();
        break;
    }
  }
  return o;
}
function Gc(e, t, n) {
  var r, s, o = this.eras(), i, a, l;
  for (e = e.toUpperCase(), r = 0, s = o.length; r < s; ++r)
    if (i = o[r].name.toUpperCase(), a = o[r].abbr.toUpperCase(), l = o[r].narrow.toUpperCase(), n)
      switch (t) {
        case "N":
        case "NN":
        case "NNN":
          if (a === e)
            return o[r];
          break;
        case "NNNN":
          if (i === e)
            return o[r];
          break;
        case "NNNNN":
          if (l === e)
            return o[r];
          break;
      }
    else if ([i, a, l].indexOf(e) >= 0)
      return o[r];
}
function zc(e, t) {
  var n = e.since <= e.until ? 1 : -1;
  return t === void 0 ? I(e.since).year() : I(e.since).year() + (t - e.offset) * n;
}
function Bc() {
  var e, t, n, r = this.localeData().eras();
  for (e = 0, t = r.length; e < t; ++e)
    if (n = this.clone().startOf("day").valueOf(), r[e].since <= n && n <= r[e].until || r[e].until <= n && n <= r[e].since)
      return r[e].name;
  return "";
}
function jc() {
  var e, t, n, r = this.localeData().eras();
  for (e = 0, t = r.length; e < t; ++e)
    if (n = this.clone().startOf("day").valueOf(), r[e].since <= n && n <= r[e].until || r[e].until <= n && n <= r[e].since)
      return r[e].narrow;
  return "";
}
function $c() {
  var e, t, n, r = this.localeData().eras();
  for (e = 0, t = r.length; e < t; ++e)
    if (n = this.clone().startOf("day").valueOf(), r[e].since <= n && n <= r[e].until || r[e].until <= n && n <= r[e].since)
      return r[e].abbr;
  return "";
}
function Zc() {
  var e, t, n, r, s = this.localeData().eras();
  for (e = 0, t = s.length; e < t; ++e)
    if (n = s[e].since <= s[e].until ? 1 : -1, r = this.clone().startOf("day").valueOf(), s[e].since <= r && r <= s[e].until || s[e].until <= r && r <= s[e].since)
      return (this.year() - I(s[e].since).year()) * n + s[e].offset;
  return this.year();
}
function qc(e) {
  return ne(this, "_erasNameRegex") || as.call(this), e ? this._erasNameRegex : this._erasRegex;
}
function Kc(e) {
  return ne(this, "_erasAbbrRegex") || as.call(this), e ? this._erasAbbrRegex : this._erasRegex;
}
function Jc(e) {
  return ne(this, "_erasNarrowRegex") || as.call(this), e ? this._erasNarrowRegex : this._erasRegex;
}
function is(e, t) {
  return t.erasAbbrRegex(e);
}
function Qc(e, t) {
  return t.erasNameRegex(e);
}
function Xc(e, t) {
  return t.erasNarrowRegex(e);
}
function ed(e, t) {
  return t._eraYearOrdinalRegex || en;
}
function as() {
  var e = [], t = [], n = [], r = [], s, o, i = this.eras();
  for (s = 0, o = i.length; s < o; ++s)
    t.push(Ue(i[s].name)), e.push(Ue(i[s].abbr)), n.push(Ue(i[s].narrow)), r.push(Ue(i[s].name)), r.push(Ue(i[s].abbr)), r.push(Ue(i[s].narrow));
  this._erasRegex = new RegExp("^(" + r.join("|") + ")", "i"), this._erasNameRegex = new RegExp("^(" + t.join("|") + ")", "i"), this._erasAbbrRegex = new RegExp("^(" + e.join("|") + ")", "i"), this._erasNarrowRegex = new RegExp(
    "^(" + n.join("|") + ")",
    "i"
  );
}
P(0, ["gg", 2], 0, function() {
  return this.weekYear() % 100;
});
P(0, ["GG", 2], 0, function() {
  return this.isoWeekYear() % 100;
});
function ur(e, t) {
  P(0, [e, e.length], 0, t);
}
ur("gggg", "weekYear");
ur("ggggg", "weekYear");
ur("GGGG", "isoWeekYear");
ur("GGGGG", "isoWeekYear");
Ce("weekYear", "gg");
Ce("isoWeekYear", "GG");
Re("weekYear", 1);
Re("isoWeekYear", 1);
C("G", sr);
C("g", sr);
C("GG", fe, $e);
C("gg", fe, $e);
C("GGGG", Kr, qr);
C("gggg", Kr, qr);
C("GGGGG", rr, tr);
C("ggggg", rr, tr);
kn(
  ["gggg", "ggggg", "GGGG", "GGGGG"],
  function(e, t, n, r) {
    t[r.substr(0, 2)] = $(e);
  }
);
kn(["gg", "GG"], function(e, t, n, r) {
  t[r] = I.parseTwoDigitYear(e);
});
function td(e) {
  return ti.call(
    this,
    e,
    this.week(),
    this.weekday(),
    this.localeData()._week.dow,
    this.localeData()._week.doy
  );
}
function nd(e) {
  return ti.call(
    this,
    e,
    this.isoWeek(),
    this.isoWeekday(),
    1,
    4
  );
}
function rd() {
  return _t(this.year(), 1, 4);
}
function sd() {
  return _t(this.isoWeekYear(), 1, 4);
}
function od() {
  var e = this.localeData()._week;
  return _t(this.year(), e.dow, e.doy);
}
function id() {
  var e = this.localeData()._week;
  return _t(this.weekYear(), e.dow, e.doy);
}
function ti(e, t, n, r, s) {
  var o;
  return e == null ? gn(this, r, s).year : (o = _t(e, r, s), t > o && (t = o), ad.call(this, e, t, n, r, s));
}
function ad(e, t, n, r, s) {
  var o = No(e, t, n, r, s), i = pn(o.year, 0, o.dayOfYear);
  return this.year(i.getUTCFullYear()), this.month(i.getUTCMonth()), this.date(i.getUTCDate()), this;
}
P("Q", 0, "Qo", "quarter");
Ce("quarter", "Q");
Re("quarter", 7);
C("Q", ko);
ue("Q", function(e, t) {
  t[gt] = ($(e) - 1) * 3;
});
function ld(e) {
  return e == null ? Math.ceil((this.month() + 1) / 3) : this.month((e - 1) * 3 + this.month() % 3);
}
P("D", ["DD", 2], "Do", "date");
Ce("date", "D");
Re("date", 9);
C("D", fe);
C("DD", fe, $e);
C("Do", function(e, t) {
  return e ? t._dayOfMonthOrdinalParse || t._ordinalParse : t._dayOfMonthOrdinalParseLenient;
});
ue(["D", "DD"], at);
ue("Do", function(e, t) {
  t[at] = $(e.match(fe)[0]);
});
var ni = Xt("Date", !0);
P("DDD", ["DDDD", 3], "DDDo", "dayOfYear");
Ce("dayOfYear", "DDD");
Re("dayOfYear", 4);
C("DDD", nr);
C("DDDD", Io);
ue(["DDD", "DDDD"], function(e, t, n) {
  n._dayOfYear = $(e);
});
function ud(e) {
  var t = Math.round(
    (this.clone().startOf("day") - this.clone().startOf("year")) / 864e5
  ) + 1;
  return e == null ? t : this.add(e - t, "d");
}
P("m", ["mm", 2], 0, "minute");
Ce("minute", "m");
Re("minute", 14);
C("m", fe);
C("mm", fe, $e);
ue(["m", "mm"], et);
var cd = Xt("Minutes", !1);
P("s", ["ss", 2], 0, "second");
Ce("second", "s");
Re("second", 15);
C("s", fe);
C("ss", fe, $e);
ue(["s", "ss"], yt);
var dd = Xt("Seconds", !1);
P("S", 0, 0, function() {
  return ~~(this.millisecond() / 100);
});
P(0, ["SS", 2], 0, function() {
  return ~~(this.millisecond() / 10);
});
P(0, ["SSS", 3], 0, "millisecond");
P(0, ["SSSS", 4], 0, function() {
  return this.millisecond() * 10;
});
P(0, ["SSSSS", 5], 0, function() {
  return this.millisecond() * 100;
});
P(0, ["SSSSSS", 6], 0, function() {
  return this.millisecond() * 1e3;
});
P(0, ["SSSSSSS", 7], 0, function() {
  return this.millisecond() * 1e4;
});
P(0, ["SSSSSSSS", 8], 0, function() {
  return this.millisecond() * 1e5;
});
P(0, ["SSSSSSSSS", 9], 0, function() {
  return this.millisecond() * 1e6;
});
Ce("millisecond", "ms");
Re("millisecond", 16);
C("S", nr, ko);
C("SS", nr, $e);
C("SSS", nr, Io);
var Dt, ri;
for (Dt = "SSSS"; Dt.length <= 9; Dt += "S")
  C(Dt, en);
function fd(e, t) {
  t[Pt] = $(("0." + e) * 1e3);
}
for (Dt = "S"; Dt.length <= 9; Dt += "S")
  ue(Dt, fd);
ri = Xt("Milliseconds", !1);
P("z", 0, 0, "zoneAbbr");
P("zz", 0, 0, "zoneName");
function hd() {
  return this._isUTC ? "UTC" : "";
}
function md() {
  return this._isUTC ? "Coordinated Universal Time" : "";
}
var w = xn.prototype;
w.add = ic;
w.calendar = hc;
w.clone = mc;
w.diff = vc;
w.endOf = Rc;
w.format = Ic;
w.from = Mc;
w.fromNow = Dc;
w.to = bc;
w.toNow = Oc;
w.get = gl;
w.invalidAt = Ac;
w.isAfter = pc;
w.isBefore = gc;
w.isBetween = yc;
w.isSame = _c;
w.isSameOrAfter = wc;
w.isSameOrBefore = Sc;
w.isValid = Fc;
w.lang = Ko;
w.locale = qo;
w.localeData = Jo;
w.max = Fu;
w.min = Hu;
w.parsingFlags = Wc;
w.set = yl;
w.startOf = Cc;
w.subtract = ac;
w.toArray = Pc;
w.toObject = Lc;
w.toDate = Nc;
w.toISOString = Tc;
w.inspect = kc;
typeof Symbol < "u" && Symbol.for != null && (w[Symbol.for("nodejs.util.inspect.custom")] = function() {
  return "Moment<" + this.format() + ">";
});
w.toJSON = Hc;
w.toString = xc;
w.unix = Ec;
w.valueOf = Yc;
w.creationData = Vc;
w.eraName = Bc;
w.eraNarrow = jc;
w.eraAbbr = $c;
w.eraYear = Zc;
w.year = Eo;
w.isLeapYear = Ll;
w.weekYear = td;
w.isoWeekYear = nd;
w.quarter = w.quarters = ld;
w.month = Ro;
w.daysInMonth = El;
w.week = w.weeks = Ul;
w.isoWeek = w.isoWeeks = Gl;
w.weeksInYear = od;
w.weeksInWeekYear = id;
w.isoWeeksInYear = rd;
w.isoWeeksInISOWeekYear = sd;
w.date = ni;
w.day = w.days = nu;
w.weekday = ru;
w.isoWeekday = su;
w.dayOfYear = ud;
w.hour = w.hours = du;
w.minute = w.minutes = cd;
w.second = w.seconds = dd;
w.millisecond = w.milliseconds = ri;
w.utcOffset = $u;
w.utc = qu;
w.local = Ku;
w.parseZone = Ju;
w.hasAlignedHourOffset = Qu;
w.isDST = Xu;
w.isLocal = tc;
w.isUtcOffset = nc;
w.isUtc = Bo;
w.isUTC = Bo;
w.zoneAbbr = hd;
w.zoneName = md;
w.dates = Ke(
  "dates accessor is deprecated. Use date instead.",
  ni
);
w.months = Ke(
  "months accessor is deprecated. Use month instead",
  Ro
);
w.years = Ke(
  "years accessor is deprecated. Use year instead",
  Eo
);
w.zone = Ke(
  "moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",
  Zu
);
w.isDSTShifted = Ke(
  "isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",
  ec
);
function pd(e) {
  return de(e * 1e3);
}
function gd() {
  return de.apply(null, arguments).parseZone();
}
function si(e) {
  return e;
}
var re = jr.prototype;
re.calendar = nl;
re.longDateFormat = il;
re.invalidDate = ll;
re.ordinal = dl;
re.preparse = si;
re.postformat = si;
re.relativeTime = hl;
re.pastFuture = ml;
re.set = el;
re.eras = Uc;
re.erasParse = Gc;
re.erasConvertYear = zc;
re.erasAbbrRegex = Kc;
re.erasNameRegex = qc;
re.erasNarrowRegex = Jc;
re.months = Ol;
re.monthsShort = Cl;
re.monthsParse = Yl;
re.monthsRegex = Pl;
re.monthsShortRegex = Nl;
re.week = Fl;
re.firstDayOfYear = Vl;
re.firstDayOfWeek = Al;
re.weekdays = Jl;
re.weekdaysMin = Xl;
re.weekdaysShort = Ql;
re.weekdaysParse = tu;
re.weekdaysRegex = ou;
re.weekdaysShortRegex = iu;
re.weekdaysMinRegex = au;
re.isPM = uu;
re.meridiem = fu;
function $n(e, t, n, r) {
  var s = vt(), o = dt().set(r, t);
  return s[n](o, e);
}
function oi(e, t, n) {
  if (St(e) && (t = e, e = void 0), e = e || "", t != null)
    return $n(e, t, n, "month");
  var r, s = [];
  for (r = 0; r < 12; r++)
    s[r] = $n(e, r, n, "month");
  return s;
}
function ls(e, t, n, r) {
  typeof e == "boolean" ? (St(t) && (n = t, t = void 0), t = t || "") : (t = e, n = t, e = !1, St(t) && (n = t, t = void 0), t = t || "");
  var s = vt(), o = e ? s._week.dow : 0, i, a = [];
  if (n != null)
    return $n(t, (n + o) % 7, r, "day");
  for (i = 0; i < 7; i++)
    a[i] = $n(t, (i + o) % 7, r, "day");
  return a;
}
function yd(e, t) {
  return oi(e, t, "months");
}
function _d(e, t) {
  return oi(e, t, "monthsShort");
}
function wd(e, t, n) {
  return ls(e, t, n, "weekdays");
}
function Sd(e, t, n) {
  return ls(e, t, n, "weekdaysShort");
}
function vd(e, t, n) {
  return ls(e, t, n, "weekdaysMin");
}
bt("en", {
  eras: [
    {
      since: "0001-01-01",
      until: 1 / 0,
      offset: 1,
      name: "Anno Domini",
      narrow: "AD",
      abbr: "AD"
    },
    {
      since: "0000-12-31",
      until: -1 / 0,
      offset: 1,
      name: "Before Christ",
      narrow: "BC",
      abbr: "BC"
    }
  ],
  dayOfMonthOrdinalParse: /\d{1,2}(th|st|nd|rd)/,
  ordinal: function(e) {
    var t = e % 10, n = $(e % 100 / 10) === 1 ? "th" : t === 1 ? "st" : t === 2 ? "nd" : t === 3 ? "rd" : "th";
    return e + n;
  }
});
I.lang = Ke(
  "moment.lang is deprecated. Use moment.locale instead.",
  bt
);
I.langData = Ke(
  "moment.langData is deprecated. Use moment.localeData instead.",
  vt
);
var ht = Math.abs;
function xd() {
  var e = this._data;
  return this._milliseconds = ht(this._milliseconds), this._days = ht(this._days), this._months = ht(this._months), e.milliseconds = ht(e.milliseconds), e.seconds = ht(e.seconds), e.minutes = ht(e.minutes), e.hours = ht(e.hours), e.months = ht(e.months), e.years = ht(e.years), this;
}
function ii(e, t, n, r) {
  var s = ot(t, n);
  return e._milliseconds += r * s._milliseconds, e._days += r * s._days, e._months += r * s._months, e._bubble();
}
function Td(e, t) {
  return ii(this, e, t, 1);
}
function kd(e, t) {
  return ii(this, e, t, -1);
}
function Fs(e) {
  return e < 0 ? Math.floor(e) : Math.ceil(e);
}
function Id() {
  var e = this._milliseconds, t = this._days, n = this._months, r = this._data, s, o, i, a, l;
  return e >= 0 && t >= 0 && n >= 0 || e <= 0 && t <= 0 && n <= 0 || (e += Fs(Yr(n) + t) * 864e5, t = 0, n = 0), r.milliseconds = e % 1e3, s = Ze(e / 1e3), r.seconds = s % 60, o = Ze(s / 60), r.minutes = o % 60, i = Ze(o / 60), r.hours = i % 24, t += Ze(i / 24), l = Ze(ai(t)), n += l, t -= Fs(Yr(l)), a = Ze(n / 12), n %= 12, r.days = t, r.months = n, r.years = a, this;
}
function ai(e) {
  return e * 4800 / 146097;
}
function Yr(e) {
  return e * 146097 / 4800;
}
function Md(e) {
  if (!this.isValid())
    return NaN;
  var t, n, r = this._milliseconds;
  if (e = Je(e), e === "month" || e === "quarter" || e === "year")
    switch (t = this._days + r / 864e5, n = this._months + ai(t), e) {
      case "month":
        return n;
      case "quarter":
        return n / 3;
      case "year":
        return n / 12;
    }
  else
    switch (t = this._days + Math.round(Yr(this._months)), e) {
      case "week":
        return t / 7 + r / 6048e5;
      case "day":
        return t + r / 864e5;
      case "hour":
        return t * 24 + r / 36e5;
      case "minute":
        return t * 1440 + r / 6e4;
      case "second":
        return t * 86400 + r / 1e3;
      case "millisecond":
        return Math.floor(t * 864e5) + r;
      default:
        throw new Error("Unknown unit " + e);
    }
}
function Dd() {
  return this.isValid() ? this._milliseconds + this._days * 864e5 + this._months % 12 * 2592e6 + $(this._months / 12) * 31536e6 : NaN;
}
function xt(e) {
  return function() {
    return this.as(e);
  };
}
var bd = xt("ms"), Od = xt("s"), Cd = xt("m"), Rd = xt("h"), Yd = xt("d"), Ed = xt("w"), Nd = xt("M"), Pd = xt("Q"), Ld = xt("y");
function Hd() {
  return ot(this);
}
function Fd(e) {
  return e = Je(e), this.isValid() ? this[e + "s"]() : NaN;
}
function Wt(e) {
  return function() {
    return this.isValid() ? this._data[e] : NaN;
  };
}
var Wd = Wt("milliseconds"), Ad = Wt("seconds"), Vd = Wt("minutes"), Ud = Wt("hours"), Gd = Wt("days"), zd = Wt("months"), Bd = Wt("years");
function jd() {
  return Ze(this.days() / 7);
}
var mt = Math.round, Bt = {
  ss: 44,
  // a few seconds to seconds
  s: 45,
  // seconds to minute
  m: 45,
  // minutes to hour
  h: 22,
  // hours to day
  d: 26,
  // days to month/week
  w: null,
  // weeks to month
  M: 11
  // months to year
};
function $d(e, t, n, r, s) {
  return s.relativeTime(t || 1, !!n, e, r);
}
function Zd(e, t, n, r) {
  var s = ot(e).abs(), o = mt(s.as("s")), i = mt(s.as("m")), a = mt(s.as("h")), l = mt(s.as("d")), u = mt(s.as("M")), c = mt(s.as("w")), p = mt(s.as("y")), y = o <= n.ss && ["s", o] || o < n.s && ["ss", o] || i <= 1 && ["m"] || i < n.m && ["mm", i] || a <= 1 && ["h"] || a < n.h && ["hh", a] || l <= 1 && ["d"] || l < n.d && ["dd", l];
  return n.w != null && (y = y || c <= 1 && ["w"] || c < n.w && ["ww", c]), y = y || u <= 1 && ["M"] || u < n.M && ["MM", u] || p <= 1 && ["y"] || ["yy", p], y[2] = t, y[3] = +e > 0, y[4] = r, $d.apply(null, y);
}
function qd(e) {
  return e === void 0 ? mt : typeof e == "function" ? (mt = e, !0) : !1;
}
function Kd(e, t) {
  return Bt[e] === void 0 ? !1 : t === void 0 ? Bt[e] : (Bt[e] = t, e === "s" && (Bt.ss = t - 1), !0);
}
function Jd(e, t) {
  if (!this.isValid())
    return this.localeData().invalidDate();
  var n = !1, r = Bt, s, o;
  return typeof e == "object" && (t = e, e = !1), typeof e == "boolean" && (n = e), typeof t == "object" && (r = Object.assign({}, Bt, t), t.s != null && t.ss == null && (r.ss = t.s - 1)), s = this.localeData(), o = Zd(this, !n, r, s), n && (o = s.pastFuture(+this, o)), s.postformat(o);
}
var Sr = Math.abs;
function Gt(e) {
  return (e > 0) - (e < 0) || +e;
}
function cr() {
  if (!this.isValid())
    return this.localeData().invalidDate();
  var e = Sr(this._milliseconds) / 1e3, t = Sr(this._days), n = Sr(this._months), r, s, o, i, a = this.asSeconds(), l, u, c, p;
  return a ? (r = Ze(e / 60), s = Ze(r / 60), e %= 60, r %= 60, o = Ze(n / 12), n %= 12, i = e ? e.toFixed(3).replace(/\.?0+$/, "") : "", l = a < 0 ? "-" : "", u = Gt(this._months) !== Gt(a) ? "-" : "", c = Gt(this._days) !== Gt(a) ? "-" : "", p = Gt(this._milliseconds) !== Gt(a) ? "-" : "", l + "P" + (o ? u + o + "Y" : "") + (n ? u + n + "M" : "") + (t ? c + t + "D" : "") + (s || r || e ? "T" : "") + (s ? p + s + "H" : "") + (r ? p + r + "M" : "") + (e ? p + i + "S" : "")) : "P0D";
}
var X = lr.prototype;
X.isValid = Gu;
X.abs = xd;
X.add = Td;
X.subtract = kd;
X.as = Md;
X.asMilliseconds = bd;
X.asSeconds = Od;
X.asMinutes = Cd;
X.asHours = Rd;
X.asDays = Yd;
X.asWeeks = Ed;
X.asMonths = Nd;
X.asQuarters = Pd;
X.asYears = Ld;
X.valueOf = Dd;
X._bubble = Id;
X.clone = Hd;
X.get = Fd;
X.milliseconds = Wd;
X.seconds = Ad;
X.minutes = Vd;
X.hours = Ud;
X.days = Gd;
X.weeks = jd;
X.months = zd;
X.years = Bd;
X.humanize = Jd;
X.toISOString = cr;
X.toString = cr;
X.toJSON = cr;
X.locale = qo;
X.localeData = Jo;
X.toIsoString = Ke(
  "toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",
  cr
);
X.lang = Ko;
P("X", 0, 0, "unix");
P("x", 0, 0, "valueOf");
C("x", sr);
C("X", wl);
ue("X", function(e, t, n) {
  n._d = new Date(parseFloat(e) * 1e3);
});
ue("x", function(e, t, n) {
  n._d = new Date($(e));
});
//! moment.js
I.version = "2.29.4";
Qa(de);
I.fn = w;
I.min = Wu;
I.max = Au;
I.now = Vu;
I.utc = dt;
I.unix = pd;
I.months = yd;
I.isDate = vn;
I.locale = bt;
I.invalid = Xn;
I.duration = ot;
I.isMoment = st;
I.weekdays = wd;
I.parseZone = gd;
I.localeData = vt;
I.isDuration = Ln;
I.monthsShort = _d;
I.weekdaysMin = vd;
I.defineLocale = es;
I.updateLocale = gu;
I.locales = yu;
I.weekdaysShort = Sd;
I.normalizeUnits = Je;
I.relativeTimeRounding = qd;
I.relativeTimeThreshold = Kd;
I.calendarFormat = fc;
I.prototype = w;
I.HTML5_FMT = {
  DATETIME_LOCAL: "YYYY-MM-DDTHH:mm",
  // <input type="datetime-local" />
  DATETIME_LOCAL_SECONDS: "YYYY-MM-DDTHH:mm:ss",
  // <input type="datetime-local" step="1" />
  DATETIME_LOCAL_MS: "YYYY-MM-DDTHH:mm:ss.SSS",
  // <input type="datetime-local" step="0.001" />
  DATE: "YYYY-MM-DD",
  // <input type="date" />
  TIME: "HH:mm",
  // <input type="time" />
  TIME_SECONDS: "HH:mm:ss",
  // <input type="time" step="1" />
  TIME_MS: "HH:mm:ss.SSS",
  // <input type="time" step="0.001" />
  WEEK: "GGGG-[W]WW",
  // <input type="week" />
  MONTH: "YYYY-MM"
  // <input type="month" />
};
function nh() {
  var In;
  const e = fr(null), t = fr([]), [n, r] = di(-1), s = fr(null), {
    tokenBuy: o,
    tokenSell: i,
    setTokenSell: a,
    setTokenBuy: l,
    flipTokens: u,
    setSellAmount: c,
    setLimitPrice: p,
    setLimitMultiples: y,
    orderType: D
  } = Mn((O) => O.swapSlice), {
    open: M,
    searchInput: d,
    swapType: v,
    unverifiedTokenList: h,
    supportedTokens: m,
    setSearchInput: T,
    toggleModal: E,
    closeModal: g,
    setAdaUsdPrice: f,
    setTokenList: b,
    setUnverifiedTokenList: Y,
    callbackSelectedToken: N
  } = Mn((O) => O.tokenSearchSlice), {
    userAddress: F,
    addressList: G,
    setUserTokens: q,
    updateBalance: ae,
    setUserOrders: A,
    userTokens: z,
    balance: se
  } = Mn((O) => O.walletSlice), { isHideSmallBalances: _e, setIsHideSmallBalances: Pe } = Mn(
    (O) => O.globalSettingsSlice
  ), { saveSettings: Me } = wi(), { isMobile: De } = pi(), We = (O) => {
    if (O.target.value !== "/") {
      if (O.target.value === ".") {
        Ae();
        return;
      }
      T(O.target.value), r(-1);
    }
  }, Ae = () => {
    const O = !_e;
    Pe(O), Me({ isHideSmallBalances: O });
  }, Tt = (O) => /^[a-zA-Z0-9]{56}$/.test(O), At = (O) => O.length > 56, { isFetching: Qe } = tn({
    queryKey: ["tokenListUnverified", d],
    queryFn: async () => {
      try {
        const O = fi[d], L = O ? [O] : [];
        if (Tt(d) || At(d)) {
          const { data: W } = await nn.get(
            "/swap/tokens",
            {
              params: {
                verified: !1,
                query: d
              }
            }
          );
          Y(W), L.push(...W);
        }
        if (!O && d) {
          const { data: W } = await nn.get(
            "/swap/tokens",
            {
              params: { query: d }
            }
          );
          W && (Y(W), L.push(...W));
        }
        return L;
      } catch {
        return [];
      }
    },
    enabled: !!d,
    refetchOnWindowFocus: !1
  }), { data: x } = tn({
    queryKey: ["tokenList"],
    queryFn: async () => {
      const { data: O } = await nn.get("/swap/tokens");
      return b(O), O;
    },
    refetchInterval: 3e4
  });
  tn({
    queryKey: ["userBalance", G, F],
    queryFn: async () => {
      if (F && (G == null ? void 0 : G.length) > 0) {
        const { balance: O, tokens: L } = await _i(G);
        ae(O), q(L || []);
      }
      return !0;
    },
    refetchInterval: 5e3,
    enabled: !!F,
    refetchOnWindowFocus: !0
  }), tn({
    queryKey: ["userOrders", F],
    queryFn: async () => {
      const { data: O } = await nn.get(`/swap/orders/${F}`);
      return A(
        O.sort((L, W) => I(L.last_update).isBefore(W.last_update) ? 1 : I(L.last_update).isAfter(W.last_update) ? -1 : 0)
      ), O;
    },
    enabled: !!F,
    refetchInterval: 3e3,
    refetchOnWindowFocus: !0
  }), tn({
    queryKey: ["usdPrice"],
    queryFn: async () => {
      const { data: O } = await nn.get("/swap/adaValue");
      return console.log("usdPrice", O), f(O), O;
    },
    refetchInterval: 3e5
  }), fs(() => {
    r(-1);
  }, [v]);
  const te = Dn(() => {
    if (!x)
      return [];
    const O = x.map((W) => {
      const J = z == null ? void 0 : z.find(
        (Ie) => Ie.token_id === W.token_id
      );
      return J ? (J == null ? void 0 : J.amount) && J.ada_value < 5 && _e ? W : {
        ...W,
        amount: J.amount,
        ada_value: J.ada_value
      } : W;
    }).sort((W, J) => !W.ada_value && J.ada_value ? 1 : W.ada_value && !J.ada_value || W.ada_value > J.ada_value ? -1 : W.ada_value < J.ada_value ? 1 : bn[W.token_id] && !bn[J.token_id] ? -1 : bn[J.token_id] && !bn[W.token_id] ? 1 : W.token_ascii < J.token_ascii ? -1 : W.token_ascii > J.token_ascii ? 1 : 0), L = [
      { ...Xe, amount: se, ada_value: se },
      ...O
    ].filter((W) => {
      var J, Ie, Ye;
      return (((J = W == null ? void 0 : W.token_ascii) == null ? void 0 : J.toLowerCase().includes(d.toLowerCase())) || ((Ie = W == null ? void 0 : W.token_policy) == null ? void 0 : Ie.toLowerCase().includes(d.toLowerCase())) || ((Ye = W == null ? void 0 : W.ticker) == null ? void 0 : Ye.toLowerCase().includes(d.toLowerCase()))) && // If swapType basket exclude CARDAON_TOKEN from search
      !(v === "BASKET" && W.token_id === Xe.token_id);
    }).filter((W) => m.length === 0 ? !0 : m.includes(W.token_id)).sort((W, J) => {
      if (!d)
        return 0;
      const Ie = W.ticker.toLowerCase(), Ye = J.ticker.toLowerCase();
      return Ie === d.toLowerCase() ? -1 : Ye === d.toLowerCase() ? 1 : 0;
    });
    return L.length < 5 && L.forEach((W) => {
      const J = new URLSearchParams(window.location.search);
      J.set("tokenBuy", W.token_id), J.set("tokenSell", Xe.token_id), J.set("tokenSell", W.token_id), J.set("tokenBuy", Xe.token_id);
    }), h && h.length > 0 && Tt(d) ? h : L;
  }, [
    d,
    v,
    o,
    i,
    x,
    z,
    F,
    _e
  ]), oe = Dn(() => {
    if (Qe || !h)
      return te;
    const O = h.filter(
      (L) => !te.some((W) => W.token_id === L.token_id)
    );
    return [...te, ...O];
  }, [te, h, Qe]), ve = () => {
    u(), g();
  }, be = (O) => {
    if (D === "STOP_LOSS") {
      l(Xe), a(O), g();
      return;
    }
    try {
      switch (v) {
        case "SELL":
          Se(O);
          break;
        case "BUY":
          kt(O);
          break;
        case "BASKET":
          Vt(O);
          break;
        default:
          throw new Error("Invalid swap type");
      }
      g();
    } catch {
    }
  }, Se = (O) => {
    if ((o == null ? void 0 : o.token_id) === O.token_id)
      return ve();
    (o == null ? void 0 : o.token_id) !== Xe.token_id && O.token_id !== Xe.token_id && l(Xe), c(null), p(0), y(0), a(O);
  }, kt = (O) => {
    if ((i == null ? void 0 : i.token_id) === O.token_id)
      return ve();
    (i == null ? void 0 : i.token_id) !== Xe.token_id && O.token_id !== Xe.token_id && a(Xe), p(0), y(0), l(O);
  }, Vt = (O) => {
    N(O);
  };
  fs(() => {
    const O = (L) => {
      var W, J, Ie, Ye;
      if (L.key === "Escape") {
        g();
        return;
      }
      if (L.key === "/") {
        (W = e.current) == null || W.focus(), E();
        return;
      }
      if (M && L.key.match(/^[a-zA-Z0-9]$/)) {
        (J = e.current) == null || J.focus();
        return;
      }
      if (M && L.key === "Enter") {
        if (L.preventDefault(), n !== -1)
          return be(oe[n]);
        if (oe[0])
          return be(oe[0]);
      }
      M && (L.key === "ArrowDown" || L.key === "Tab" && !L.shiftKey) ? (L.preventDefault(), n >= oe.length - 1 ? r(0) : r((Ut) => Ut + 1), (Ie = t.current[n]) == null || Ie.scrollIntoView({ block: "nearest" })) : M && (L.key === "ArrowUp" || L.key === "Tab" && L.shiftKey) && (L.preventDefault(), n <= 0 ? r(oe.length - 1) : r((Ut) => Ut - 1), (Ye = t.current[n]) == null || Ye.scrollIntoView({ block: "nearest" }));
    };
    return document.addEventListener("keydown", O), () => {
      document.removeEventListener("keydown", O);
    };
  }, [M, oe, n, be, g]);
  const it = Dn(() => _e ? /* @__PURE__ */ ge(
    vi,
    {
      className: "dhs-top-3 dhs-text-mainText",
      width: 18,
      height: 18
    }
  ) : /* @__PURE__ */ ge(
    Si,
    {
      className: "dhs-top-3 dhs-text-mainText",
      width: 18,
      height: 18
    }
  ), [_e]), Le = Dn(() => {
    var L;
    return (((L = document.getElementById("dexhunter-root")) == null ? void 0 : L.offsetHeight) || 450) - 200;
  }, [(In = document.getElementById("dexhunter-root")) == null ? void 0 : In.offsetHeight]);
  return /* @__PURE__ */ ge(Ti, { open: M, onOpenChange: E, children: /* @__PURE__ */ Ve(
    ki,
    {
      side: "top",
      className: " dhs-mx-0 dhs-translate-y-0 dhs-bottom-0 dhs-rounded-[26px]",
      ref: s,
      children: [
        /* @__PURE__ */ Ve("div", { className: "dhs-px-3 @sm/appRoot:dhs-px-5 @md/appRoot:dhs-pb-6 @sm/appRoot:dhs-flex @sm/appRoot:dhs-flex-col", children: [
          /* @__PURE__ */ Ve("div", { className: "dhs-flex dhs-pb-3 dhs-pt-3 @md/appRoot:dhs-pb-5 @sm/appRoot:dhs-mx-5 @sm/appRoot:dhs-pb-[14px] dhs-justify-center dhs-items-center", children: [
            /* @__PURE__ */ ge(
              "div",
              {
                onClick: () => g(),
                className: "dhs-flex dhs-justify-center dhs-items-center dhs-w-8 dhs-h-8 dhs-rounded-xl dhs-bg-containers dhs-cursor-pointer dhs-absolute dhs-right-3 @md/appRoot:dhs-right-7 @sm/appRoot:dhs-right-5",
                children: /* @__PURE__ */ ge(gi, { className: "dhs-text-mainText dhs-flex dhs-w-[12px] dhs-h-[12px]" })
              }
            ),
            /* @__PURE__ */ ge("div", { className: "dhs-flex", children: /* @__PURE__ */ Ve("div", { className: "dhs-flex dhs-items-center dhs-text-base @md/appRoot:dhs-text-lg @sm/appRoot:dhs-text-base dhs-text-mainText dhs-font-proximaBold", children: [
              "Select a token",
              /* @__PURE__ */ Ve("span", { className: "sm:dhs-hidden dhs-inline", children: [
                " to ",
                v.toLowerCase()
              ] })
            ] }) }),
            /* @__PURE__ */ ge(
              "div",
              {
                onClick: Ae,
                className: "dhs-flex dhs-justify-center dhs-items-center dhs-w-8 dhs-h-8 dhs-rounded-xl dhs-bg-containers dhs-cursor-pointer dhs-absolute dhs-left-3 @md/appRoot:dhs-left-7 @sm/appRoot:dhs-left-5",
                children: /* @__PURE__ */ ge(
                  yi,
                  {
                    trigger: it,
                    content: _e ? "Show Small Balances" : "Hide Small Balances",
                    contentClass: "dhs-text-mainText"
                  }
                )
              }
            )
          ] }),
          /* @__PURE__ */ Ve("div", { className: "dhs-relative dhs-grid dhs-gap-4 sm:dhs-mx-5", children: [
            /* @__PURE__ */ ge("div", { className: "dhs-absolute dhs-inset-y-0 dhs-left-0 dhs-flex dhs-items-center dhs-pl-5 dhs-pointer-events-none", children: /* @__PURE__ */ ge(
              xi,
              {
                className: "dhs-top-3 dhs-text-mainText",
                width: 14,
                height: 14
              }
            ) }),
            /* @__PURE__ */ ge(
              ci,
              {
                type: "search",
                ref: e,
                id: "token",
                value: d,
                className: "dhs-col-span-3 dhs-block dhs-h-12 @md/appRoot:dhs-h-[60px] @sm/appRoot:dhs-h-[50px] dhs-p-4 dhs-pl-12 dhs-text-base sm:dhs-text-sm dhs-text-mainText dhs-font-proximaSemiBold dhs-border-2 focus-visible:dhs-ring-0 focus-visible:dhs-ring-offset-0 dhs-border-subText dhs-rounded-xl dhs-bg-containers focus:dhs-ring-accent focus:dhs-border-accent placeholder-shown:dhs-text-base placeholder:dhs-text-subText placeholder:dhs-font-proximaSemiBold",
                placeholder: "Search by name or policy",
                onChange: We,
                autoFocus: !De
              }
            )
          ] })
        ] }),
        /* @__PURE__ */ ge("div", { className: "dhs-pt-1 sm:dhs-pt-0 sm:dhs-border-t-[1px] sm:dhs-border-t-gray-105", children: /* @__PURE__ */ ge(
          Ha,
          {
            style: { height: Le },
            totalCount: oe.length,
            itemContent: (O) => {
              const L = oe[O];
              return Qe && O === te.length ? /* @__PURE__ */ ge("div", { className: "py-1 px-7 flex w-full justify-center", children: /* @__PURE__ */ ge(
                "img",
                {
                  src: "https://storage.googleapis.com/dexhunter-images/public/squared_spinner.svg",
                  width: 25,
                  height: 25,
                  alt: "spinner"
                }
              ) }) : /* @__PURE__ */ Ve(
                "div",
                {
                  ref: (W) => t.current[O] = W,
                  tabIndex: 0,
                  className: mi(
                    "dhs-px-3 dhs-py-1 @sm/appRoot:dhs-px-7 @sm/appRoot:dhs-py-2.5 dhs-flex dhs-items-center dhs-justify-between dark:dhs-border-gray-800 dhs-cursor-pointer hover:dhs-bg-containers",
                    O === n ? "dhs-bg-containers" : ""
                  ),
                  onFocus: () => r(O),
                  onBlur: () => r(-1),
                  onClick: () => be(L),
                  children: [
                    /* @__PURE__ */ Ve("div", { className: "dhs-flex dhs-items-center dhs-gap-2 @md/appRoot:dhs-gap-4", children: [
                      /* @__PURE__ */ ge(
                        ms,
                        {
                          token: L,
                          isVerified: o == null ? void 0 : o.is_verified,
                          className: "dhs-w-[28px] dhs-h-[28px] @md/appRoot:dhs-hidden",
                          size: 28,
                          verifiedClass: "dhs-w-2.5 dhs-h-2.5"
                        }
                      ),
                      /* @__PURE__ */ ge(
                        ms,
                        {
                          token: L,
                          isVerified: o == null ? void 0 : o.is_verified,
                          className: "dhs-hidden dhs-w-[36px] dhs-h-[36px] @md/appRoot:dhs-block",
                          size: 36
                        }
                      ),
                      /* @__PURE__ */ Ve("div", { className: "dhs-flex dhs-flex-col dhs-leading-none dhs-gap-1", children: [
                        /* @__PURE__ */ ge("span", { className: "dhs-text-base dhs-leading-none dhs-font-proximaSemiBold dhs-text-mainText sm:dhs-w-[125px]", children: L.token_ascii }),
                        /* @__PURE__ */ ge("span", { className: "dhs-text-sm dhs-leading-none dhs-text-subText dhs-font-proximaMedium", children: L == null ? void 0 : L.ticker })
                      ] })
                    ] }),
                    /* @__PURE__ */ ge("div", { className: "dhs-flex dhs-items-center dhs-relative", children: /* @__PURE__ */ Ve("div", { className: "dhs-flex dhs-flex-col dhs-leading-none @md/appRoot:dhs-gap-2", children: [
                      !!L.amount && /* @__PURE__ */ Ve("div", { className: "dhs-flex dhs-gap-1 dhs-text-md dhs-font-proximaSemiBold dhs-justify-end", children: [
                        /* @__PURE__ */ ge("span", { className: "dhs-text-mainText", children: hs(L.amount || 0) }),
                        /* @__PURE__ */ ge("span", { children: L == null ? void 0 : L.ticker })
                      ] }),
                      !!L.ada_value && /* @__PURE__ */ Ve("span", { className: "dhs-text-sm dhs-text-subText dhs-font-proximaMedium dhs-flex dhs-items-center dhs-justify-end", children: [
                        /* @__PURE__ */ ge(Ii, { width: 12, height: 12 }),
                        /* @__PURE__ */ Ve("span", { children: [
                          hs(L.ada_value || 0),
                          " ADA"
                        ] })
                      ] })
                    ] }) })
                  ]
                },
                L == null ? void 0 : L.token_ascii
              );
            }
          }
        ) })
      ]
    }
  ) });
}
export {
  nh as default
};
