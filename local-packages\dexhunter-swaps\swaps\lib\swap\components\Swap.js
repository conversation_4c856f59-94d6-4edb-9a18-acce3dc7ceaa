import { jsxs as e, jsx as o } from "react/jsx-runtime";
import s from "../../store/useStore.js";
import v from "./Details/SwapDetails.js";
import P from "./SwapConfim.js";
import { memo as _ } from "react";
import { s as A } from "../../shallow-27fd7e97.js";
import { u as m } from "../../useQuery-febd7967.js";
import { useEstimateSwap as N } from "../hooks/useSwapEstimation.js";
import { cn as O } from "../../lib/utils.js";
import q from "./Settings/SwapSettings.js";
import D from "./Main/SwapHeader.js";
import F from "./Main/SwapSelect.js";
import { useSwapShortcuts as L } from "./Main/useSwapShortcuts.js";
import { useEstimateLimit as I } from "../hooks/useLimitEstimation.js";
import { useEstimateStopLoss as K } from "../hooks/useStopLossEstimation.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "./tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../components/ui/skeleton.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../constants/dexes.js";
import "../../react-hotkeys-hook.esm-60f1c3b8.js";
import "./Details/SwapDetailSplit.js";
import "./Details/OrderPreview.js";
import "../../components/ui/progress.js";
import "../../index-1c873780.js";
import "../../index-563d1ed8.js";
import "../../index-c8f2666b.js";
import "../../utils/formatNumber.js";
import "../../components/ui/tooltipDialog.js";
import "../../hooks/useScreen.js";
import "../../components/ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-c7156e07.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../components/ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../assets/svg/IconSquareInfo.js";
import "../../x-9e07c78a.js";
import "../../createLucideIcon-7a477fa6.js";
import "./Details/SwapDetailOutput.js";
import "../../components/common/TokenPrice.js";
import "../../utils/formatToken.js";
import "../../trends/components/PriceFormatter.js";
import "../../assets/svg/IconTwoWay.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconDown.js";
import "../../components/common/RealtimePulse.js";
import "../../components/ui/separator.js";
import "../../components/ui/button.js";
import "../../index-1d6812f7.js";
import "../hooks/useSwapAction.js";
import "../../config/axios.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
import "../../hooks/useNotify.js";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../QueryClientProvider-6bcd4331.js";
import "../hooks/useLimitAction.js";
import "../hooks/useStopLossAction.js";
import "../../assets/svg/IconSquaredSpinner.js";
import "../hooks/useDCAAction.js";
import "../../query-013b86c3.js";
import "../../assets/svg/IconSetting.js";
import "../../components/ui/input.js";
import "../../components/ui/switchWithText.js";
import "../../assets/svg/IconRemove.js";
import "./Settings/Slippage.js";
import "../../assets/svg/IconRefresh.js";
import "./TokenSell.js";
import "../../assets/svg/IconChevronDown.js";
import "../../components/common/TokenImage.js";
import "../../utils/cardanoUtils.js";
import "../../hooks/useInputShortcuts.js";
import "../hooks/useUsdPrices.js";
import "../../index.esm-fb2f5862.js";
import "../../IconTilde-bf643edd.js";
import "../../createReactComponent-ec43b511.js";
import "../../assets/svg/IconArrowDown.js";
import "./TokenBuy.js";
import "./TokenLimit.js";
import "../../components/ui/slider.js";
import "../../index-1fe761a6.js";
import "../../index-6460524a.js";
import "../../index-bf605d8a.js";
import "./TokenDCA.js";
import "../../orders/components/Filters/DCAInterval.js";
import "../../components/ui/dropdown-menu.js";
import "../../assets/svg/IconCheck.js";
const W = () => {
  L();
  const { estimateSwap: u, estimatePrice: w } = N(), { estimateLimit: S } = I(), { estimateStopLoss: f } = K(), {
    sellAmount: d,
    tokenBuy: t,
    tokenSell: r,
    buyAmount: a,
    dexBlacklist: n,
    inputMode: x,
    orderType: p,
    limitPrice: g
  } = s(
    (i) => ({
      flipTokens: i.swapSlice.flipTokens,
      sellAmount: i.swapSlice.sellAmount,
      buyAmount: i.swapSlice.buyAmount,
      tokenSell: i.swapSlice.tokenSell,
      tokenBuy: i.swapSlice.tokenBuy,
      dexBlacklist: i.swapSlice.dexBlacklist,
      inputMode: i.swapSlice.inputMode,
      orderType: i.swapSlice.orderType,
      limitPrice: i.swapSlice.limitPrice,
      isSwapSubmitted: i.swapSlice.isSwapSubmitted
    }),
    A
  ), { slippage: y, isOpenSwapSetting: l, isOpenSwapOverview: b } = s(
    (i) => i.swapSettingsSlice
  ), { defaultBuySize: c } = s((i) => i.globalSettingsSlice), T = [
    "swapDetails",
    r == null ? void 0 : r.token_id,
    t == null ? void 0 : t.token_id,
    d,
    a,
    c,
    n,
    x,
    y,
    p
  ];
  m({
    queryKey: T,
    queryFn: ({ signal: i }) => u({ signal: i }),
    staleTime: 0,
    refetchOnWindowFocus: !0,
    refetchInterval: 5e3,
    // Polling every 5s
    enabled: p === "SWAP" || p === "DCA"
  });
  const h = [
    "limitDetails",
    r == null ? void 0 : r.token_id,
    t == null ? void 0 : t.token_id,
    d,
    a,
    c,
    n,
    g,
    p
  ];
  m({
    queryKey: h,
    queryFn: ({ signal: i }) => S({ signal: i }),
    staleTime: 0,
    refetchOnWindowFocus: !0,
    // refetchInterval: 5000, // Polling every 5s
    enabled: p === "LIMIT"
  }), m({
    queryKey: h,
    queryFn: ({ signal: i }) => f({ signal: i }),
    staleTime: 0,
    refetchOnWindowFocus: !0,
    // refetchInterval: 5000, // Polling every 5s
    enabled: p === "STOP_LOSS"
  });
  const k = [
    "swapPrice",
    r == null ? void 0 : r.token_id,
    t == null ? void 0 : t.token_id
  ];
  return m({
    queryKey: k,
    queryFn: ({ signal: i }) => w({ signal: i }),
    staleTime: 0,
    refetchOnWindowFocus: !0,
    // refetchInterval: 5000, // Polling every 5s
    refetchIntervalInBackground: !0
  }), /* @__PURE__ */ e("div", { className: "", children: [
    !l && /* @__PURE__ */ e("div", { className: "dhs-bg-background dhs-flex dhs-flex-col dhs-gap-1 @sm/appRoot:dhs-gap-2.5 dhs-p-2.5 dhs-pt-0 dhs-rounded-[26px]", children: [
      /* @__PURE__ */ e("div", { className: O(b && "dhs-hidden"), children: [
        /* @__PURE__ */ o(D, {}),
        /* @__PURE__ */ o(F, {})
      ] }),
      p === "SWAP" && /* @__PURE__ */ o(v, {}),
      /* @__PURE__ */ o(P, {}),
      /* @__PURE__ */ e(
        "div",
        {
          className: "dhs-text-mainText dhs-font-proximaMedium dhs-md:pr-[60px] dhs-lg:pr-[60px] dhs-gap-2 dhs-flex dhs-w-full dhs-justify-center dhs-flex dhs-cursor-pointer dhs-items-center",
          onClick: () => window.open(
            `https://app.dexhunter.io/swap?tokenIdSell=&tokenIdBuy=${(t == null ? void 0 : t.token_id) || (t == null ? void 0 : t.token_id)}`,
            "_blank"
          ),
          children: [
            /* @__PURE__ */ o("span", { className: "dhs-leading-none", children: "Powered by" }),
            /* @__PURE__ */ e("div", { className: "dhs-mr-3 dhs-sm:ml-0 dhs-flex dhs-items-center dhs-flex", children: [
              /* @__PURE__ */ o(
                "img",
                {
                  src: "https://storage.googleapis.com/dexhunter-images/public/hunt-token.svg",
                  className: "dhs-h-[25px] dhs-w-[25px] dhs-mr-1",
                  width: 25,
                  height: 25,
                  alt: "DexHunter Logo"
                }
              ),
              /* @__PURE__ */ o("span", { className: "dhs-font-abcBold dhs-leading-none dhs-pt-[2px]", children: "Dex" }),
              /* @__PURE__ */ o("span", { className: "dhs-font-abcLight dhs-leading-none dhs-pt-[2px]", children: "Hunter" })
            ] })
          ]
        }
      )
    ] }),
    l && /* @__PURE__ */ o(q, {})
  ] });
}, Qt = _(W);
export {
  Qt as default
};
