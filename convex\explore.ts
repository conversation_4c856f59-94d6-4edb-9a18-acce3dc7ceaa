import { v } from "convex/values";
import { query } from "./_generated/server";

export const explorePosts = query({
  args: {
    filter: v.optional(v.string()),
    page: v.optional(v.number()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
    profileUserId: v.optional(v.string()),
    panel: v.optional(v.string()),
    direction: v.optional(v.string()),
    mediaOnly: v.optional(v.boolean()),
    tag: v.optional(v.string()),
    gender: v.optional(v.string()),
    userId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const filter = args.filter ?? "recently-added";
    const limit = args.limit ?? 20;
    const cursor = args.cursor ?? null;
    const panel = args.panel ?? "all-posts";
    const direction = args.direction ?? "forward";
    const isAsc = direction === "forward";

    // 1. Start with base query using the appropriate index
    let query = ctx.db.query("Content").withIndex("by_created_at");
    if (args.profileUserId) {
      query = query.filter((q) => q.eq(q.field("creator_id"), args.profileUserId));
    }

    // 2. Apply sorting based on filter - simplified to avoid type issues
    if (filter === "oldest") {
      query = query.order("asc");
    } else {
      // Default to desc for all other cases
      query = query.order("desc");
    }

    // 3. Get initial results
    const result = await query.paginate({ numItems: limit, cursor });
    let posts = result.page;

    // 4. Tag filter (in-memory)
    if (args.tag) {
      const normalizedTag = args.tag.replace(/^#/, "").toLowerCase();
      const hashtagRows = await ctx.db
        .query("Hashtags")
        .withIndex("by_name")
        .filter((q) => q.eq(q.field("name"), normalizedTag))
        .collect();
      const contentIds = new Set(hashtagRows.map((r) => r.content_id));
      posts = posts.filter((p) => contentIds.has(p.id));
    }

    // 5. Panel/media-type filter (in-memory)
    if (panel !== "all-posts") {
      switch (panel) {
        case "videos":
          posts = posts.filter((p) => p.content_type?.startsWith("video/"));
          break;
        case "photos":
          posts = posts.filter((p) => p.content_type?.startsWith("image/") && p.content_type !== "image/gif");
          break;
        case "audio":
          posts = posts.filter((p) => p.content_type === "audio/mpeg");
          break;
        case "clips":
          posts = posts.filter((p) =>
            p.content_type?.startsWith("video/") && (p.metadata?.duration || 0) < 60
          );
          break;
        case "gifs":
          posts = posts.filter((p) => p.content_type === "image/gif");
          break;
      }
    }

    // 6. Following filter (in-memory)
    if (filter === "following" && args.userId) {
      const follows = await ctx.db
        .query("Follows")
        .filter((q) => q.eq(q.field("follower_id"), args.userId!))
        .collect();
      const followedIds = new Set(follows.map((f) => f.followed_id));
      posts = posts.filter((p) => followedIds.has(p.creator_id));
    }

    // 7. Gender filter (in-memory)
    let genderFilter: ((account: any) => boolean) | null = null;
    if (args.gender) {
      const genderLower = args.gender.toLowerCase();
      genderFilter = (account: any) => (account?.user_info?.account?.gender?.toLowerCase() === genderLower);
      // Fetch accounts for all posts
      const uniqueCreatorIds = Array.from(new Set(posts.map((p) => p.creator_id)));
      const accounts = await ctx.db
        .query("Accounts")
        .filter((q) => q.or(...uniqueCreatorIds.map((id) => q.eq(q.field("user_id"), id))))
        .collect();
      const accountMap: Record<string, any> = Object.fromEntries(accounts.map((a) => [a.user_id, a]));
      posts = posts.filter((p) => {
        try {
          return genderFilter && genderFilter(accountMap[p.creator_id]);
        } catch {
          return false;
        }
      });
    }

    // 8. Aggregate counts for metrics-based sorts / UI
    const postIds = posts.map((p) => p.id);
    const [likeCounts, viewCounts, commentCounts, saveCounts, purchaseCounts] = await Promise.all([
      ctx.db
        .query("Likes")
        .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
        .collect()
        .then((docs) => {
          const counts: Record<string, number> = {};
          docs.forEach((d) => {
            counts[d.content_id] = (counts[d.content_id] || 0) + 1;
          });
          return counts;
        }),
      ctx.db
        .query("ContentViews")
        .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
        .collect()
        .then((docs) => {
          const counts: Record<string, number> = {};
          docs.forEach((d) => {
            counts[d.content_id] = (counts[d.content_id] || 0) + 1;
          });
          return counts;
        }),
      ctx.db
        .query("Comments")
        .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
        .collect()
        .then((docs) => {
          const counts: Record<string, number> = {};
          docs.forEach((d) => {
            counts[d.content_id] = (counts[d.content_id] || 0) + 1;
          });
          return counts;
        }),
      ctx.db
        .query("SavedContent")
        .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
        .collect()
        .then((docs) => {
          const counts: Record<string, number> = {};
          docs.forEach((d) => {
            counts[d.content_id] = (counts[d.content_id] || 0) + 1;
          });
          return counts;
        }),
      ctx.db
        .query("Purchases")
        .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
        .collect()
        .then((docs) => {
          const counts: Record<string, number> = {};
          docs.forEach((d) => {
            counts[d.content_id] = (counts[d.content_id] || 0) + 1;
          });
          return counts;
        }),
    ]);
    const likedSet = args.userId ? new Set((await ctx.db
      .query("Likes")
      .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
      .filter((q) => q.eq(q.field("user_id"), args.userId!))
      .collect()).map((l) => l.content_id)) : new Set();
    const savedSet = args.userId ? new Set((await ctx.db
      .query("SavedContent")
      .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
      .filter((q) => q.eq(q.field("user_id"), args.userId!))
      .collect()).map((s) => s.content_id)) : new Set();

    // 9. Sorting (in-memory for count-based and special filters)
    if (filter === "most-viewed") {
      posts.sort((a, b) => (viewCounts[b.id] || 0) - (viewCounts[a.id] || 0));
    } else if (filter === "most-liked") {
      posts.sort((a, b) => (likeCounts[b.id] || 0) - (likeCounts[a.id] || 0));
    } else if (filter === "most-sold") {
      posts.sort((a, b) => (purchaseCounts[b.id] || 0) - (purchaseCounts[a.id] || 0));
    } else if (filter === "highest-price") {
      posts.sort((a, b) => (b.metadata?.price || 0) - (a.metadata?.price || 0));
    } else if (filter === "lowest-price") {
      posts.sort((a, b) => (a.metadata?.price || 0) - (b.metadata?.price || 0));
    } else if (filter === "free") {
      posts = posts.filter((post) => !post.metadata?.price || post.metadata?.price === 0);
    } else if (filter === "longest") {
      posts.sort((a, b) => (b.metadata?.duration || 0) - (a.metadata?.duration || 0));
    }

    // 10. Mentions and user info
    const [hashtagsDocs, mentionsDocs, creators] = await Promise.all([
      ctx.db
        .query("Hashtags")
        .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
        .collect(),
      ctx.db
        .query("ContentMentions")
        .filter((q) => q.or(...postIds.map((id) => q.eq(q.field("content_id"), id))))
        .collect(),
      ctx.db
        .query("Accounts")
        .filter((q) => q.or(...Array.from(new Set(posts.map((p) => p.creator_id))).map((id) => q.eq(q.field("user_id"), id))))
        .collect(),
    ]);
    const hashtagsMap: Record<string, string[]> = {};
    hashtagsDocs.forEach((h) => {
      if (!hashtagsMap[h.content_id]) hashtagsMap[h.content_id] = [];
      hashtagsMap[h.content_id].push(h.name);
    });
    const mentionedUserIds = Array.from(new Set(mentionsDocs.map((m) => m.mentioned_user_id)));
    const mentionedAccounts = mentionedUserIds.length > 0
      ? await ctx.db
        .query("Accounts")
        .filter((q) => q.or(...mentionedUserIds.map((id) => q.eq(q.field("user_id"), id))))
        .collect()
      : [];
    const mentionsMap: Record<string, any[]> = {};
    const mentionedAccountMap: Record<string, any> = Object.fromEntries(
      mentionedAccounts.map((a) => [a.user_id, a])
    );
    mentionsDocs.forEach((m) => {
      if (!mentionsMap[m.content_id]) mentionsMap[m.content_id] = [];
      const mentionedAcc = mentionedAccountMap[m.mentioned_user_id];
      if (mentionedAcc) {
        const ui = mentionedAcc.user_info || {};
        mentionsMap[m.content_id].push({
          id: mentionedAcc.user_id,
          name: ui.account?.displayName ?? "Anonymous",
          username: ui.account?.username ?? "",
          profilePhoto: ui.profilePhoto ?? "/images/user/default-avatar.webp",
          coverBanner: ui.coverBanner ?? "/images/user/default-banner.webp",
          coverBannerType: ui.coverBannerType ?? "image",
          verified: ui.account?.is_verified ?? false,
        });
      }
    });
    const creatorMap: Record<string, any> = Object.fromEntries(creators.map((a) => [a.user_id, a]));
    let transformed = posts.map((p) => {
      const acc = creatorMap[p.creator_id];
      const ui = acc?.user_info || {};
      const account = ui.account || {};
      return {
        id: p.id,
        userId: p.creator_id,
        content: p.description ?? '',
        media: (p.metadata?.media ?? []).map((m: any) => ({
          mediaUrl: m.media_url,
          thumbnailUrl: m.thumbnail_url,
          mediaDescription: m.media_description,
          mediaType: p.content_type,
        })),
        contentType: p.content_type,
        isPaid: p.is_paid,
        isEdited: !!p.is_edited,
        editedAt: p.edited_at ?? null,
        createdAt: p.created_at,
        likes: likeCounts[p.id] || 0,
        comments: commentCounts[p.id] || 0,
        saves: saveCounts[p.id] || 0,
        views: viewCounts[p.id] || 0,
        liked: likedSet.has(p.id),
        saved: savedSet.has(p.id),
        price: p.metadata?.price ?? null,
        duration: p.metadata?.duration ?? null,
        resolution: p.metadata?.resolution ?? null,
        visibility: p.visibility,
        hashtags: hashtagsMap[p.id] ?? [],
        mentions: mentionsMap[p.id] ?? [],
        rawState: p.metadata?.raw_state ?? null,
        user: {
          id: acc?.user_id ?? '',
          name: account.displayName ?? 'Anonymous',
          username: account.username ?? '',
          profileImage: ui.profilePhoto ?? '/images/user/default-avatar.webp',
          coverBanner: ui.coverBanner ?? '/images/user/default-banner.webp',
          coverBannerType: ui.coverBannerType ?? 'image',
          verified: account.is_verified ?? false,
          accountType: acc?.account_type,
          user_info: { account: { gender: account.gender ?? null } },
        },
      };
    });

    return {
      posts: transformed,
      cursor: result.continueCursor,
      isDone: result.isDone,
      total: undefined,
      pageSize: limit,
    };
  },
});