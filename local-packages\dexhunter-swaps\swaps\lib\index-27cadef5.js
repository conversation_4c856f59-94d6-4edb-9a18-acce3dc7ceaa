import { a as he, _ as pe } from "./index-1c873780.js";
import * as i from "react";
import { forwardRef as ge, useState as ae, useRef as V, useEffect as K, useCallback as be, createElement as ye } from "react";
import { $ as Ee } from "./index-c8f2666b.js";
import { c as G } from "./index-c7156e07.js";
const _ = "focusScope.autoFocusOnMount", B = "focusScope.autoFocusOnUnmount", z = {
  bubbles: !1,
  cancelable: !0
}, mt = /* @__PURE__ */ ge((e, t) => {
  const { loop: r = !1, trapped: n = !1, onMountAutoFocus: c, onUnmountAutoFocus: u, ...s } = e, [o, S] = ae(null), b = G(c), g = G(u), f = V(null), d = he(
    t,
    (a) => S(a)
  ), m = V({
    paused: !1,
    pause() {
      this.paused = !0;
    },
    resume() {
      this.paused = !1;
    }
  }).current;
  K(() => {
    if (n) {
      let a = function(h) {
        if (m.paused || !o)
          return;
        const p = h.target;
        o.contains(p) ? f.current = p : C(f.current, {
          select: !0
        });
      }, l = function(h) {
        if (m.paused || !o)
          return;
        const p = h.relatedTarget;
        p !== null && (o.contains(p) || C(f.current, {
          select: !0
        }));
      }, v = function(h) {
        if (document.activeElement === document.body)
          for (const y of h)
            y.removedNodes.length > 0 && C(o);
      };
      document.addEventListener("focusin", a), document.addEventListener("focusout", l);
      const E = new MutationObserver(v);
      return o && E.observe(o, {
        childList: !0,
        subtree: !0
      }), () => {
        document.removeEventListener("focusin", a), document.removeEventListener("focusout", l), E.disconnect();
      };
    }
  }, [
    n,
    o,
    m.paused
  ]), K(() => {
    if (o) {
      q.add(m);
      const a = document.activeElement;
      if (!o.contains(a)) {
        const v = new CustomEvent(_, z);
        o.addEventListener(_, b), o.dispatchEvent(v), v.defaultPrevented || (Se(Re(oe(o)), {
          select: !0
        }), document.activeElement === a && C(o));
      }
      return () => {
        o.removeEventListener(_, b), setTimeout(() => {
          const v = new CustomEvent(B, z);
          o.addEventListener(B, g), o.dispatchEvent(v), v.defaultPrevented || C(a ?? document.body, {
            select: !0
          }), o.removeEventListener(B, g), q.remove(m);
        }, 0);
      };
    }
  }, [
    o,
    b,
    g,
    m
  ]);
  const w = be((a) => {
    if (!r && !n || m.paused)
      return;
    const l = a.key === "Tab" && !a.altKey && !a.ctrlKey && !a.metaKey, v = document.activeElement;
    if (l && v) {
      const E = a.currentTarget, [h, p] = we(E);
      h && p ? !a.shiftKey && v === p ? (a.preventDefault(), r && C(h, {
        select: !0
      })) : a.shiftKey && v === h && (a.preventDefault(), r && C(p, {
        select: !0
      })) : v === E && a.preventDefault();
    }
  }, [
    r,
    n,
    m.paused
  ]);
  return /* @__PURE__ */ ye(Ee.div, pe({
    tabIndex: -1
  }, s, {
    ref: d,
    onKeyDown: w
  }));
});
function Se(e, { select: t = !1 } = {}) {
  const r = document.activeElement;
  for (const n of e)
    if (C(n, {
      select: t
    }), document.activeElement !== r)
      return;
}
function we(e) {
  const t = oe(e), r = Z(t, e), n = Z(t.reverse(), e);
  return [
    r,
    n
  ];
}
function oe(e) {
  const t = [], r = document.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, {
    acceptNode: (n) => {
      const c = n.tagName === "INPUT" && n.type === "hidden";
      return n.disabled || n.hidden || c ? NodeFilter.FILTER_SKIP : n.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;
    }
  });
  for (; r.nextNode(); )
    t.push(r.currentNode);
  return t;
}
function Z(e, t) {
  for (const r of e)
    if (!$e(r, {
      upTo: t
    }))
      return r;
}
function $e(e, { upTo: t }) {
  if (getComputedStyle(e).visibility === "hidden")
    return !0;
  for (; e; ) {
    if (t !== void 0 && e === t)
      return !1;
    if (getComputedStyle(e).display === "none")
      return !0;
    e = e.parentElement;
  }
  return !1;
}
function Ce(e) {
  return e instanceof HTMLInputElement && "select" in e;
}
function C(e, { select: t = !1 } = {}) {
  if (e && e.focus) {
    const r = document.activeElement;
    e.focus({
      preventScroll: !0
    }), e !== r && Ce(e) && t && e.select();
  }
}
const q = Te();
function Te() {
  let e = [];
  return {
    add(t) {
      const r = e[0];
      t !== r && (r == null || r.pause()), e = Q(e, t), e.unshift(t);
    },
    remove(t) {
      var r;
      e = Q(e, t), (r = e[0]) === null || r === void 0 || r.resume();
    }
  };
}
function Q(e, t) {
  const r = [
    ...e
  ], n = r.indexOf(t);
  return n !== -1 && r.splice(n, 1), r;
}
function Re(e) {
  return e.filter(
    (t) => t.tagName !== "A"
  );
}
let D = 0;
function ht() {
  K(() => {
    var e, t;
    const r = document.querySelectorAll("[data-radix-focus-guard]");
    return document.body.insertAdjacentElement("afterbegin", (e = r[0]) !== null && e !== void 0 ? e : J()), document.body.insertAdjacentElement("beforeend", (t = r[1]) !== null && t !== void 0 ? t : J()), D++, () => {
      D === 1 && document.querySelectorAll("[data-radix-focus-guard]").forEach(
        (n) => n.remove()
      ), D--;
    };
  }, []);
}
function J() {
  const e = document.createElement("span");
  return e.setAttribute("data-radix-focus-guard", ""), e.tabIndex = 0, e.style.cssText = "outline: none; opacity: 0; position: fixed; pointer-events: none", e;
}
var $ = function() {
  return $ = Object.assign || function(t) {
    for (var r, n = 1, c = arguments.length; n < c; n++) {
      r = arguments[n];
      for (var u in r)
        Object.prototype.hasOwnProperty.call(r, u) && (t[u] = r[u]);
    }
    return t;
  }, $.apply(this, arguments);
};
function ce(e, t) {
  var r = {};
  for (var n in e)
    Object.prototype.hasOwnProperty.call(e, n) && t.indexOf(n) < 0 && (r[n] = e[n]);
  if (e != null && typeof Object.getOwnPropertySymbols == "function")
    for (var c = 0, n = Object.getOwnPropertySymbols(e); c < n.length; c++)
      t.indexOf(n[c]) < 0 && Object.prototype.propertyIsEnumerable.call(e, n[c]) && (r[n[c]] = e[n[c]]);
  return r;
}
function ke(e, t, r) {
  if (r || arguments.length === 2)
    for (var n = 0, c = t.length, u; n < c; n++)
      (u || !(n in t)) && (u || (u = Array.prototype.slice.call(t, 0, n)), u[n] = t[n]);
  return e.concat(u || Array.prototype.slice.call(t));
}
var M = "right-scroll-bar-position", O = "width-before-scroll-bar", Ae = "with-scroll-bars-hidden", xe = "--removed-body-scroll-bar-size";
function Ne(e, t) {
  return typeof e == "function" ? e(t) : e && (e.current = t), e;
}
function Fe(e, t) {
  var r = ae(function() {
    return {
      // value
      value: e,
      // last callback
      callback: t,
      // "memoized" public interface
      facade: {
        get current() {
          return r.value;
        },
        set current(n) {
          var c = r.value;
          c !== n && (r.value = n, r.callback(n, c));
        }
      }
    };
  })[0];
  return r.callback = t, r.facade;
}
function Pe(e, t) {
  return Fe(t || null, function(r) {
    return e.forEach(function(n) {
      return Ne(n, r);
    });
  });
}
function Le(e) {
  return e;
}
function Me(e, t) {
  t === void 0 && (t = Le);
  var r = [], n = !1, c = {
    read: function() {
      if (n)
        throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");
      return r.length ? r[r.length - 1] : e;
    },
    useMedium: function(u) {
      var s = t(u, n);
      return r.push(s), function() {
        r = r.filter(function(o) {
          return o !== s;
        });
      };
    },
    assignSyncMedium: function(u) {
      for (n = !0; r.length; ) {
        var s = r;
        r = [], s.forEach(u);
      }
      r = {
        push: function(o) {
          return u(o);
        },
        filter: function() {
          return r;
        }
      };
    },
    assignMedium: function(u) {
      n = !0;
      var s = [];
      if (r.length) {
        var o = r;
        r = [], o.forEach(u), s = r;
      }
      var S = function() {
        var g = s;
        s = [], g.forEach(u);
      }, b = function() {
        return Promise.resolve().then(S);
      };
      b(), r = {
        push: function(g) {
          s.push(g), b();
        },
        filter: function(g) {
          return s = s.filter(g), r;
        }
      };
    }
  };
  return c;
}
function Oe(e) {
  e === void 0 && (e = {});
  var t = Me(null);
  return t.options = $({ async: !0, ssr: !1 }, e), t;
}
var ue = function(e) {
  var t = e.sideCar, r = ce(e, ["sideCar"]);
  if (!t)
    throw new Error("Sidecar: please provide `sideCar` property to import the right car");
  var n = t.read();
  if (!n)
    throw new Error("Sidecar medium not found");
  return i.createElement(n, $({}, r));
};
ue.isSideCarExport = !0;
function Ie(e, t) {
  return e.useMedium(t), ue;
}
var ie = Oe(), U = function() {
}, I = i.forwardRef(function(e, t) {
  var r = i.useRef(null), n = i.useState({
    onScrollCapture: U,
    onWheelCapture: U,
    onTouchMoveCapture: U
  }), c = n[0], u = n[1], s = e.forwardProps, o = e.children, S = e.className, b = e.removeScrollBar, g = e.enabled, f = e.shards, d = e.sideCar, m = e.noIsolation, w = e.inert, a = e.allowPinchZoom, l = e.as, v = l === void 0 ? "div" : l, E = ce(e, ["forwardProps", "children", "className", "removeScrollBar", "enabled", "shards", "sideCar", "noIsolation", "inert", "allowPinchZoom", "as"]), h = d, p = Pe([r, t]), y = $($({}, E), c);
  return i.createElement(
    i.Fragment,
    null,
    g && i.createElement(h, { sideCar: ie, removeScrollBar: b, shards: f, noIsolation: m, inert: w, setCallbacks: u, allowPinchZoom: !!a, lockRef: r }),
    s ? i.cloneElement(i.Children.only(o), $($({}, y), { ref: p })) : i.createElement(v, $({}, y, { className: S, ref: p }), o)
  );
});
I.defaultProps = {
  enabled: !0,
  removeScrollBar: !0,
  inert: !1
};
I.classNames = {
  fullWidth: O,
  zeroRight: M
};
var ee, We = function() {
  if (ee)
    return ee;
  if (typeof __webpack_nonce__ < "u")
    return __webpack_nonce__;
};
function _e() {
  if (!document)
    return null;
  var e = document.createElement("style");
  e.type = "text/css";
  var t = We();
  return t && e.setAttribute("nonce", t), e;
}
function Be(e, t) {
  e.styleSheet ? e.styleSheet.cssText = t : e.appendChild(document.createTextNode(t));
}
function De(e) {
  var t = document.head || document.getElementsByTagName("head")[0];
  t.appendChild(e);
}
var Ue = function() {
  var e = 0, t = null;
  return {
    add: function(r) {
      e == 0 && (t = _e()) && (Be(t, r), De(t)), e++;
    },
    remove: function() {
      e--, !e && t && (t.parentNode && t.parentNode.removeChild(t), t = null);
    }
  };
}, je = function() {
  var e = Ue();
  return function(t, r) {
    i.useEffect(function() {
      return e.add(t), function() {
        e.remove();
      };
    }, [t && r]);
  };
}, le = function() {
  var e = je(), t = function(r) {
    var n = r.styles, c = r.dynamic;
    return e(n, c), null;
  };
  return t;
}, He = {
  left: 0,
  top: 0,
  right: 0,
  gap: 0
}, j = function(e) {
  return parseInt(e || "", 10) || 0;
}, Ke = function(e) {
  var t = window.getComputedStyle(document.body), r = t[e === "padding" ? "paddingLeft" : "marginLeft"], n = t[e === "padding" ? "paddingTop" : "marginTop"], c = t[e === "padding" ? "paddingRight" : "marginRight"];
  return [j(r), j(n), j(c)];
}, Xe = function(e) {
  if (e === void 0 && (e = "margin"), typeof window > "u")
    return He;
  var t = Ke(e), r = document.documentElement.clientWidth, n = window.innerWidth;
  return {
    left: t[0],
    top: t[1],
    right: t[2],
    gap: Math.max(0, n - r + t[2] - t[0])
  };
}, Ye = le(), Ve = function(e, t, r, n) {
  var c = e.left, u = e.top, s = e.right, o = e.gap;
  return r === void 0 && (r = "margin"), `
  .`.concat(Ae, ` {
   overflow: hidden `).concat(n, `;
   padding-right: `).concat(o, "px ").concat(n, `;
  }
  body {
    overflow: hidden `).concat(n, `;
    overscroll-behavior: contain;
    `).concat([
    t && "position: relative ".concat(n, ";"),
    r === "margin" && `
    padding-left: `.concat(c, `px;
    padding-top: `).concat(u, `px;
    padding-right: `).concat(s, `px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(o, "px ").concat(n, `;
    `),
    r === "padding" && "padding-right: ".concat(o, "px ").concat(n, ";")
  ].filter(Boolean).join(""), `
  }
  
  .`).concat(M, ` {
    right: `).concat(o, "px ").concat(n, `;
  }
  
  .`).concat(O, ` {
    margin-right: `).concat(o, "px ").concat(n, `;
  }
  
  .`).concat(M, " .").concat(M, ` {
    right: 0 `).concat(n, `;
  }
  
  .`).concat(O, " .").concat(O, ` {
    margin-right: 0 `).concat(n, `;
  }
  
  body {
    `).concat(xe, ": ").concat(o, `px;
  }
`);
}, Ge = function(e) {
  var t = e.noRelative, r = e.noImportant, n = e.gapMode, c = n === void 0 ? "margin" : n, u = i.useMemo(function() {
    return Xe(c);
  }, [c]);
  return i.createElement(Ye, { styles: Ve(u, !t, c, r ? "" : "!important") });
}, X = !1;
if (typeof window < "u")
  try {
    var N = Object.defineProperty({}, "passive", {
      get: function() {
        return X = !0, !0;
      }
    });
    window.addEventListener("test", N, N), window.removeEventListener("test", N, N);
  } catch {
    X = !1;
  }
var T = X ? { passive: !1 } : !1, ze = function(e) {
  return e.tagName === "TEXTAREA";
}, se = function(e, t) {
  var r = window.getComputedStyle(e);
  return (
    // not-not-scrollable
    r[t] !== "hidden" && // contains scroll inside self
    !(r.overflowY === r.overflowX && !ze(e) && r[t] === "visible")
  );
}, Ze = function(e) {
  return se(e, "overflowY");
}, qe = function(e) {
  return se(e, "overflowX");
}, te = function(e, t) {
  var r = t;
  do {
    typeof ShadowRoot < "u" && r instanceof ShadowRoot && (r = r.host);
    var n = fe(e, r);
    if (n) {
      var c = de(e, r), u = c[1], s = c[2];
      if (u > s)
        return !0;
    }
    r = r.parentNode;
  } while (r && r !== document.body);
  return !1;
}, Qe = function(e) {
  var t = e.scrollTop, r = e.scrollHeight, n = e.clientHeight;
  return [
    t,
    r,
    n
  ];
}, Je = function(e) {
  var t = e.scrollLeft, r = e.scrollWidth, n = e.clientWidth;
  return [
    t,
    r,
    n
  ];
}, fe = function(e, t) {
  return e === "v" ? Ze(t) : qe(t);
}, de = function(e, t) {
  return e === "v" ? Qe(t) : Je(t);
}, et = function(e, t) {
  return e === "h" && t === "rtl" ? -1 : 1;
}, tt = function(e, t, r, n, c) {
  var u = et(e, window.getComputedStyle(t).direction), s = u * n, o = r.target, S = t.contains(o), b = !1, g = s > 0, f = 0, d = 0;
  do {
    var m = de(e, o), w = m[0], a = m[1], l = m[2], v = a - l - u * w;
    (w || v) && fe(e, o) && (f += v, d += w), o = o.parentNode;
  } while (
    // portaled content
    !S && o !== document.body || // self content
    S && (t.contains(o) || t === o)
  );
  return (g && (c && f === 0 || !c && s > f) || !g && (c && d === 0 || !c && -s > d)) && (b = !0), b;
}, F = function(e) {
  return "changedTouches" in e ? [e.changedTouches[0].clientX, e.changedTouches[0].clientY] : [0, 0];
}, re = function(e) {
  return [e.deltaX, e.deltaY];
}, ne = function(e) {
  return e && "current" in e ? e.current : e;
}, rt = function(e, t) {
  return e[0] === t[0] && e[1] === t[1];
}, nt = function(e) {
  return `
  .block-interactivity-`.concat(e, ` {pointer-events: none;}
  .allow-interactivity-`).concat(e, ` {pointer-events: all;}
`);
}, at = 0, R = [];
function ot(e) {
  var t = i.useRef([]), r = i.useRef([0, 0]), n = i.useRef(), c = i.useState(at++)[0], u = i.useState(function() {
    return le();
  })[0], s = i.useRef(e);
  i.useEffect(function() {
    s.current = e;
  }, [e]), i.useEffect(function() {
    if (e.inert) {
      document.body.classList.add("block-interactivity-".concat(c));
      var a = ke([e.lockRef.current], (e.shards || []).map(ne), !0).filter(Boolean);
      return a.forEach(function(l) {
        return l.classList.add("allow-interactivity-".concat(c));
      }), function() {
        document.body.classList.remove("block-interactivity-".concat(c)), a.forEach(function(l) {
          return l.classList.remove("allow-interactivity-".concat(c));
        });
      };
    }
  }, [e.inert, e.lockRef.current, e.shards]);
  var o = i.useCallback(function(a, l) {
    if ("touches" in a && a.touches.length === 2)
      return !s.current.allowPinchZoom;
    var v = F(a), E = r.current, h = "deltaX" in a ? a.deltaX : E[0] - v[0], p = "deltaY" in a ? a.deltaY : E[1] - v[1], y, W = a.target, A = Math.abs(h) > Math.abs(p) ? "h" : "v";
    if ("touches" in a && A === "h" && W.type === "range")
      return !1;
    var x = te(A, W);
    if (!x)
      return !0;
    if (x ? y = A : (y = A === "v" ? "h" : "v", x = te(A, W)), !x)
      return !1;
    if (!n.current && "changedTouches" in a && (h || p) && (n.current = y), !y)
      return !0;
    var Y = n.current || y;
    return tt(Y, l, a, Y === "h" ? h : p, !0);
  }, []), S = i.useCallback(function(a) {
    var l = a;
    if (!(!R.length || R[R.length - 1] !== u)) {
      var v = "deltaY" in l ? re(l) : F(l), E = t.current.filter(function(y) {
        return y.name === l.type && y.target === l.target && rt(y.delta, v);
      })[0];
      if (E && E.should) {
        l.cancelable && l.preventDefault();
        return;
      }
      if (!E) {
        var h = (s.current.shards || []).map(ne).filter(Boolean).filter(function(y) {
          return y.contains(l.target);
        }), p = h.length > 0 ? o(l, h[0]) : !s.current.noIsolation;
        p && l.cancelable && l.preventDefault();
      }
    }
  }, []), b = i.useCallback(function(a, l, v, E) {
    var h = { name: a, delta: l, target: v, should: E };
    t.current.push(h), setTimeout(function() {
      t.current = t.current.filter(function(p) {
        return p !== h;
      });
    }, 1);
  }, []), g = i.useCallback(function(a) {
    r.current = F(a), n.current = void 0;
  }, []), f = i.useCallback(function(a) {
    b(a.type, re(a), a.target, o(a, e.lockRef.current));
  }, []), d = i.useCallback(function(a) {
    b(a.type, F(a), a.target, o(a, e.lockRef.current));
  }, []);
  i.useEffect(function() {
    return R.push(u), e.setCallbacks({
      onScrollCapture: f,
      onWheelCapture: f,
      onTouchMoveCapture: d
    }), document.addEventListener("wheel", S, T), document.addEventListener("touchmove", S, T), document.addEventListener("touchstart", g, T), function() {
      R = R.filter(function(a) {
        return a !== u;
      }), document.removeEventListener("wheel", S, T), document.removeEventListener("touchmove", S, T), document.removeEventListener("touchstart", g, T);
    };
  }, []);
  var m = e.removeScrollBar, w = e.inert;
  return i.createElement(
    i.Fragment,
    null,
    w ? i.createElement(u, { styles: nt(c) }) : null,
    m ? i.createElement(Ge, { gapMode: "margin" }) : null
  );
}
const ct = Ie(ie, ot);
var ve = i.forwardRef(function(e, t) {
  return i.createElement(I, $({}, e, { ref: t, sideCar: ct }));
});
ve.classNames = I.classNames;
const pt = ve;
var ut = function(e) {
  if (typeof document > "u")
    return null;
  var t = Array.isArray(e) ? e[0] : e;
  return t.ownerDocument.body;
}, k = /* @__PURE__ */ new WeakMap(), P = /* @__PURE__ */ new WeakMap(), L = {}, H = 0, me = function(e) {
  return e && (e.host || me(e.parentNode));
}, it = function(e, t) {
  return t.map(function(r) {
    if (e.contains(r))
      return r;
    var n = me(r);
    return n && e.contains(n) ? n : (console.error("aria-hidden", r, "in not contained inside", e, ". Doing nothing"), null);
  }).filter(function(r) {
    return !!r;
  });
}, lt = function(e, t, r, n) {
  var c = it(t, Array.isArray(e) ? e : [e]);
  L[r] || (L[r] = /* @__PURE__ */ new WeakMap());
  var u = L[r], s = [], o = /* @__PURE__ */ new Set(), S = new Set(c), b = function(f) {
    !f || o.has(f) || (o.add(f), b(f.parentNode));
  };
  c.forEach(b);
  var g = function(f) {
    !f || S.has(f) || Array.prototype.forEach.call(f.children, function(d) {
      if (o.has(d))
        g(d);
      else {
        var m = d.getAttribute(n), w = m !== null && m !== "false", a = (k.get(d) || 0) + 1, l = (u.get(d) || 0) + 1;
        k.set(d, a), u.set(d, l), s.push(d), a === 1 && w && P.set(d, !0), l === 1 && d.setAttribute(r, "true"), w || d.setAttribute(n, "true");
      }
    });
  };
  return g(t), o.clear(), H++, function() {
    s.forEach(function(f) {
      var d = k.get(f) - 1, m = u.get(f) - 1;
      k.set(f, d), u.set(f, m), d || (P.has(f) || f.removeAttribute(n), P.delete(f)), m || f.removeAttribute(r);
    }), H--, H || (k = /* @__PURE__ */ new WeakMap(), k = /* @__PURE__ */ new WeakMap(), P = /* @__PURE__ */ new WeakMap(), L = {});
  };
}, gt = function(e, t, r) {
  r === void 0 && (r = "data-aria-hidden");
  var n = Array.from(Array.isArray(e) ? e : [e]), c = t || ut(e);
  return c ? (n.push.apply(n, Array.from(c.querySelectorAll("[aria-live]"))), lt(n, c, r, "aria-hidden")) : function() {
    return null;
  };
};
export {
  pt as $,
  ht as a,
  mt as b,
  gt as h
};
