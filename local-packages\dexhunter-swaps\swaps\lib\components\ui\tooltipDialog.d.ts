import * as React from "react";
interface ITooltipDialogProps {
    trigger: React.ReactNode;
    content: React.ReactNode;
    activeMobile?: boolean;
    triggerClass?: string;
    contentClass?: string;
    delayDuration?: number;
    side?: "top" | "bottom" | "left" | "right";
    triggerAsChild?: boolean;
    open?: boolean;
    disable?: boolean;
}
declare const TooltipDialog: ({ trigger, content, activeMobile, triggerClass, contentClass, delayDuration, side, triggerAsChild, open, disable, }: ITooltipDialogProps) => import("react/jsx-runtime").JSX.Element;
export default TooltipDialog;
