import d from "../../store/useStore.js";
import { server as g } from "../../config/axios.js";
import { useNotify as re } from "../../hooks/useNotify.js";
import { getHighestPriceImpact as ae } from "../../utils/formatNumber.js";
import { CARDANO_TOKEN_IDENTIFIER as R } from "../components/tokens.js";
import { a as se } from "../../axios-ddd885c5.js";
import { u as ie } from "../../QueryClientProvider-6bcd4331.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../index-ca8eb9e1.js";
import "react/jsx-runtime";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../hooks/useScreen.js";
const Ke = () => {
  const { notify: x } = re(), V = ie(), {
    tokenBuy: i,
    tokenSell: r,
    sellAmount: n,
    setIsTransactionLoading: f,
    estimationError: M,
    dexBlacklist: W,
    setBuyAmount: v,
    setSellAmount: L,
    setSwapDetails: B,
    swapDetails: o,
    bonusOutput: m,
    setDexBlacklist: K,
    setIsSwapSubmitted: X,
    setBonusOutput: Y,
    inputMode: z,
    onViewOrder: h
  } = d((e) => e.swapSlice), { swapType: j } = d((e) => e.tokenSearchSlice), {
    api: p,
    userAddress: y
  } = d((e) => e.walletSlice), { slippage: q, isDexSplitting: G, isAutomaticSlippage: Q } = d((e) => e.swapSettingsSlice), { setUpcomingOrders: H, setPendingOrdersCount: J, pendingOrdersCount: Z } = d((e) => e.userOrdersSlice), { balance: S } = d((e) => e.walletSlice), { partnerName: $, partnerCode: D, inputs: ee } = d((e) => e.globalSettingsSlice), te = () => {
    const e = ((o == null ? void 0 : o.total_fee) + (o == null ? void 0 : o.partner_fee)) / 1e6, u = parseFloat(n);
    return z === "BUY" ? parseFloat(o.total_input_without_slippage) : u + e > S && (r == null ? void 0 : r.token_id) === "" ? parseFloat(u - e) : parseFloat(u);
  };
  return { buyToken: async () => {
    var u, A, I, T, w, C, E, N, P, k, O;
    if (f(!0), n === 0 || M) {
      f(!1);
      return;
    }
    let e = {
      sign: null,
      tx: "",
      payload: null,
      ada_balance: S,
      err: null,
      step: "pre-swap",
      signatures: null
    };
    try {
      console.time("swap");
      let a = Q ? ae(o, !0) + 1 : q;
      ((u = o == null ? void 0 : o.splits) == null ? void 0 : u.length) === 0 && ((I = (A = o == null ? void 0 : o.splits) == null ? void 0 : A[0]) == null ? void 0 : I.dex) === "MUESLISWAP" && (a = 0), ((r == null ? void 0 : r.token_id) === "e52964af4fffdb54504859875b1827b60ba679074996156461143dc14f5054494d" || (i == null ? void 0 : i.token_id) === "e52964af4fffdb54504859875b1827b60ba679074996156461143dc14f5054494d") && (a = Math.min(a, 50));
      let _ = W;
      ((i == null ? void 0 : i.token_id) === "8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa4d494c4b" || (r == null ? void 0 : r.token_id) === "8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa4d494c4b") && ((r == null ? void 0 : r.token_id) === "" && n && n <= 500 && (_ = ["WINGRIDER", "SUNDAESWAP", "SPECTRUM", "VYFI"]), (r == null ? void 0 : r.token_id) === "8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa4d494c4b" && n && n <= 50 && (_ = ["WINGRIDER", "SUNDAESWAP", "SPECTRUM", "VYFI"]));
      const F = {
        buyer_address: y,
        token_in: r == null ? void 0 : r.token_id,
        token_out: (i == null ? void 0 : i.token_id) || "",
        slippage: a,
        amount_in: te(),
        tx_optimization: G,
        blacklisted_dexes: _,
        referrer: $,
        inputs: ee
      };
      e.payload = F;
      const { data: c } = await g.post("/swap/build", F, {
        headers: {
          "X-Partner-Id": D
        }
      });
      e.swap = c, e.step = "pre-sign";
      const U = await (p == null ? void 0 : p.signTx(c == null ? void 0 : c.cbor, !0)), { data: s } = await g.post("/swap/sign", {
        txCbor: c == null ? void 0 : c.cbor,
        signatures: U
      });
      e.sign = s, e.signatures = U, e.step = "pre-submit";
      const l = await (p == null ? void 0 : p.submitTx(s == null ? void 0 : s.cbor));
      e.tx = l, e.step = "after-submit";
      const oe = "Success" + (parseFloat(m == null ? void 0 : m.replace("+", "").replace(" ADA", "").trim()) !== 0 && ((T = o == null ? void 0 : o.splits) == null ? void 0 : T.length) > 1 ? `. Bonus: ${m}` : "");
      try {
        await g.post("/marking/submit", {
          tx_hash: l,
          order_type: "SWAP",
          cbor: s == null ? void 0 : s.cbor
        });
      } catch (t) {
        console.log(t), e.marking_err = ((w = t == null ? void 0 : t.response) == null ? void 0 : w.data) || (t == null ? void 0 : t.message) || (t == null ? void 0 : t.info);
      }
      if (s != null && s.strat_id)
        try {
          await se.put("https://api.axo.trade/notify", {
            tx_id: l,
            strat_id: s == null ? void 0 : s.strat_id
          }, { headers: { "X-Api-Key": "zIXsxTvPrmu7VstLXf2UvAZVTf64zK9t" } });
        } catch (t) {
          console.log(t), e.marking_err = ((C = t == null ? void 0 : t.response) == null ? void 0 : C.data) || (t == null ? void 0 : t.message) || (t == null ? void 0 : t.info);
        }
      X(!0);
      const b = (E = o == null ? void 0 : o.splits) == null ? void 0 : E.map((t) => ({
        ...t,
        tx_hash: l,
        status: "SUBMITTED",
        amount_in: n,
        token_id_in: (r == null ? void 0 : r.token_id) || R,
        token_id_out: (i == null ? void 0 : i.token_id) || R,
        expected_out_amount: t == null ? void 0 : t.expected_output,
        submission_time: (/* @__PURE__ */ new Date()).toISOString(),
        user_address: y,
        upcoming: !0,
        type: j,
        is_dexhunter: !0
      }));
      x({
        type: "success",
        title: oe,
        desc: "Your order has been placed successfully",
        actionName: "View order",
        dataCallback: b,
        actionCallback: () => {
          var t;
          if (h) {
            h(b);
            return;
          }
          (t = window.open("https://app.dexhunter.io/orders", "_blank")) == null || t.focus(), console.log(b, "callback");
        }
      }), H(b), J(Z + ((N = o == null ? void 0 : o.splits) == null ? void 0 : N.length)), L(null), v(0), B(null), K([]), Y("");
    } catch (a) {
      if (console.log(a), console.log(a.message), e.err = ((P = a.response) == null ? void 0 : P.data) || a.message || a.info, (k = a.message) != null && k.toLowerCase().includes("declined") || (O = a.info) != null && O.toLowerCase().includes("declined"))
        return;
      x({
        type: "error",
        title: "Error placing order",
        desc: "There was an error placing your order",
        actionCallback: () => {
          navigator.clipboard.writeText(JSON.stringify(e));
        }
      });
    } finally {
      f(!1), V.invalidateQueries({
        predicate: (a) => a.queryKey[0] === "userBalance"
      });
    }
  } };
};
export {
  Ke as useSwapAction
};
