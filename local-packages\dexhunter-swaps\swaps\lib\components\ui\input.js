import { jsx as o } from "react/jsx-runtime";
import * as i from "react";
import { cn as t } from "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const h = i.forwardRef(
  ({ className: s, type: d, ...e }, r) => /* @__PURE__ */ o(
    "input",
    {
      type: d,
      className: t(
        "dhs-flex dhs-h-10 dhs-w-full dhs-rounded-md dhs-border dhs-border-input dhs-bg-background dhs-px-3 dhs-py-2 dhs-text-sm dhs-ring-offset-background file:dhs-border-0 file:dhs-bg-transparent file:dhs-text-sm file:dhs-font-medium placeholder:dhs-text-muted-foreground focus-visible:dhs-outline-none focus-visible:dhs-ring-2 focus-visible:dhs-ring-ring focus-visible:dhs-ring-offset-2 disabled:dhs-cursor-not-allowed disabled:dhs-opacity-50",
        s
      ),
      ref: r,
      ...e
    }
  )
);
h.displayName = "Input";
export {
  h as Input
};
