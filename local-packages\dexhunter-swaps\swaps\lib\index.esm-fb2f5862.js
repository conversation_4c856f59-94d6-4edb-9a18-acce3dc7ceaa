import Se, { forwardRef as je, use<PERSON>em<PERSON> as <PERSON>, useState as W, useRef as U<PERSON>, useImper<PERSON><PERSON><PERSON><PERSON> as Be, useEffect as Ge } from "react";
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, <PERSON><PERSON><PERSON> OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var l = function() {
  return l = Object.assign || function(r) {
    for (var a, t = 1, u = arguments.length; t < u; t++) {
      a = arguments[t];
      for (var n in a)
        Object.prototype.hasOwnProperty.call(a, n) && (r[n] = a[n]);
    }
    return r;
  }, l.apply(this, arguments);
};
function Me(e, r) {
  var a = {};
  for (var t in e)
    Object.prototype.hasOwnProperty.call(e, t) && r.indexOf(t) < 0 && (a[t] = e[t]);
  if (e != null && typeof Object.getOwnPropertySymbols == "function")
    for (var u = 0, t = Object.getOwnPropertySymbols(e); u < t.length; u++)
      r.indexOf(t[u]) < 0 && Object.prototype.propertyIsEnumerable.call(e, t[u]) && (a[t[u]] = e[t[u]]);
  return a;
}
function V(e, r, a) {
  if (a || arguments.length === 2)
    for (var t = 0, u = r.length, n; t < u; t++)
      (n || !(t in r)) && (n || (n = Array.prototype.slice.call(r, 0, t)), n[t] = r[t]);
  return e.concat(n || r);
}
var N = function(e) {
  return e.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
}, $e = { k: 1e3, m: 1e6, b: 1e9 }, Te = function(e, r) {
  r === void 0 && (r = ".");
  var a = new RegExp("(\\d+(" + N(r) + "\\d*)?)([kmb])$", "i"), t = e.match(a);
  if (t) {
    var u = t[1], n = t[3], i = $e[n.toLowerCase()];
    return Number(u.replace(r, ".")) * i;
  }
}, We = function(e, r) {
  r === void 0 && (r = ",");
  var a = new RegExp(N(r), "g");
  return e.replace(a, "");
}, He = function(e, r) {
  var a = N(r.join("")), t = new RegExp("[^\\d" + a + "]", "gi");
  return e.replace(t, "");
}, oe = function(e) {
  var r = e.value, a = e.groupSeparator, t = a === void 0 ? "," : a, u = e.decimalSeparator, n = u === void 0 ? "." : u, i = e.allowDecimals, f = i === void 0 ? !0 : i, o = e.decimalsLimit, C = o === void 0 ? 2 : o, D = e.allowNegativeValue, p = D === void 0 ? !0 : D, v = e.disableAbbreviations, A = v === void 0 ? !1 : v, s = e.prefix, c = s === void 0 ? "" : s, m = e.transformRawValue, k = m === void 0 ? function(M) {
    return M;
  } : m, x = k(r);
  if (x === "-")
    return x;
  var E = A ? [] : ["k", "m", "b"], P = new RegExp("((^|\\D)-\\d)|(-" + N(c) + ")"), ae = P.test(x), I = RegExp("(\\d+)-?" + N(c)).exec(r) || [], O = I[0], q = I[1], z = c ? O ? x.replace(O, "").concat(q) : x.replace(c, "") : x, J = We(z, t), K = He(J, V([
    t,
    n
  ], E)), j = K;
  if (!A) {
    if (E.some(function(M) {
      return M === K.toLowerCase().replace(n, "");
    }))
      return "";
    var Q = Te(K, n);
    Q && (j = String(Q));
  }
  var L = ae && p ? "-" : "";
  if (n && j.includes(n)) {
    var U = K.split(n), X = U[0], B = U[1], G = C && B ? B.slice(0, C) : B, Y = f ? "" + n + G : "";
    return "" + L + X + Y;
  }
  return "" + L + j;
}, qe = function(e, r, a) {
  if (a && e.length > 1) {
    if (e.includes(r)) {
      var t = e.split(r), u = t[0], n = t[1];
      if (n.length > a)
        return "" + u + r + n.slice(0, a);
    }
    var i = e.length > a ? new RegExp("(\\d+)(\\d{" + a + "})") : new RegExp("(\\d)(\\d+)"), f = e.match(i);
    if (f) {
      var u = f[1], n = f[2];
      return "" + u + r + n;
    }
  }
  return e;
}, ye = function(e, r) {
  var a = r.groupSeparator, t = a === void 0 ? "," : a, u = r.decimalSeparator, n = u === void 0 ? "." : u, i = new RegExp("\\d([^" + N(t) + N(n) + "0-9]+)"), f = e.match(i);
  return f ? f[1] : void 0;
}, H = function(e) {
  var r = e.value, a = e.decimalSeparator, t = e.intlConfig, u = e.decimalScale, n = e.prefix, i = n === void 0 ? "" : n, f = e.suffix, o = f === void 0 ? "" : f;
  if (r === "" || r === void 0)
    return "";
  if (r === "-")
    return "-";
  var C = new RegExp("^\\d?-" + (i ? N(i) + "?" : "") + "\\d").test(r), D = a !== "." ? ze(r, a, C) : r, p = {
    minimumFractionDigits: u || 0,
    maximumFractionDigits: 20
  }, v = t ? new Intl.NumberFormat(t.locale, t.currency ? l(l({}, p), { style: "currency", currency: t.currency }) : p) : new Intl.NumberFormat(void 0, p), A = v.formatToParts(Number(D)), s = Je(A, e), c = ye(s, l({}, e)), m = r.slice(-1) === a ? a : "", k = D.match(RegExp("\\d+\\.(\\d+)")) || [], x = k[1];
  return u === void 0 && x && a && (s.includes(a) ? s = s.replace(RegExp("(\\d+)(" + N(a) + ")(\\d+)", "g"), "$1$2" + x) : c && !o ? s = s.replace(c, "" + a + x + c) : s = "" + s + a + x), o && m ? "" + s + m + o : c && m ? s.replace(c, "" + m + c) : c && o ? s.replace(c, "" + m + o) : [s, m, o].join("");
}, ze = function(e, r, a) {
  var t = e;
  return r && r !== "." && (t = t.replace(RegExp(N(r), "g"), "."), a && r === "-" && (t = "-" + t.slice(1))), t;
}, Je = function(e, r) {
  var a = r.prefix, t = r.groupSeparator, u = r.decimalSeparator, n = r.decimalScale, i = r.disableGroupSeparators, f = i === void 0 ? !1 : i;
  return e.reduce(function(o, C, D) {
    var p = C.type, v = C.value;
    return D === 0 && a ? p === "minusSign" ? [v, a] : p === "currency" ? V(V([], o), [a]) : [a, v] : p === "currency" ? a ? o : V(V([], o), [v]) : p === "group" ? f ? o : V(V([], o), [t !== void 0 ? t : v]) : p === "decimal" ? n !== void 0 && n === 0 ? o : V(V([], o), [u !== void 0 ? u : v]) : p === "fraction" ? V(V([], o), [n !== void 0 ? v.slice(0, n) : v]) : V(V([], o), [v]);
  }, [""]).join("");
}, Qe = {
  currencySymbol: "",
  groupSeparator: "",
  decimalSeparator: "",
  prefix: "",
  suffix: ""
}, Xe = function(e) {
  var r = e || {}, a = r.locale, t = r.currency, u = a ? new Intl.NumberFormat(a, t ? { currency: t, style: "currency" } : void 0) : new Intl.NumberFormat();
  return u.formatToParts(1000.1).reduce(function(n, i, f) {
    return i.type === "currency" ? f === 0 ? l(l({}, n), { currencySymbol: i.value, prefix: i.value }) : l(l({}, n), { currencySymbol: i.value, suffix: i.value }) : i.type === "group" ? l(l({}, n), { groupSeparator: i.value }) : i.type === "decimal" ? l(l({}, n), { decimalSeparator: i.value }) : n;
  }, Qe);
}, be = function(e) {
  return RegExp(/\d/, "gi").test(e);
}, Ye = function(e, r, a) {
  if (r === void 0 && (r = "."), a === void 0 || e === "" || e === void 0)
    return e;
  if (!e.match(/\d/g))
    return "";
  var t = e.split(r), u = t[0], n = t[1];
  if (a === 0)
    return u;
  var i = n || "";
  if (i.length < a)
    for (; i.length < a; )
      i += "0";
  else
    i = i.slice(0, a);
  return "" + u + r + i;
}, Ze = function(e) {
  var r = e.selectionStart, a = e.value, t = e.lastKeyStroke, u = e.stateValue, n = e.groupSeparator, i = r, f = a;
  if (u && i) {
    var o = a.split("");
    return t === "Backspace" && u[i] === n && (o.splice(i - 1, 1), i -= 1), t === "Delete" && u[i] === n && (o.splice(i, 1), i += 1), f = o.join(""), { modifiedValue: f, cursorPosition: i };
  }
  return { modifiedValue: f, cursorPosition: r };
}, _e = je(function(e, r) {
  var a = e.allowDecimals, t = a === void 0 ? !0 : a, u = e.allowNegativeValue, n = u === void 0 ? !0 : u, i = e.id, f = e.name, o = e.className, C = e.customInput, D = e.decimalsLimit, p = e.defaultValue, v = e.disabled, A = v === void 0 ? !1 : v, s = e.maxLength, c = e.value, m = e.onValueChange, k = e.fixedDecimalLength, x = e.placeholder, E = e.decimalScale, P = e.prefix, ae = e.suffix, I = e.intlConfig, O = e.step, q = e.min, z = e.max, J = e.disableGroupSeparators, K = J === void 0 ? !1 : J, j = e.disableAbbreviations, Q = j === void 0 ? !1 : j, L = e.decimalSeparator, U = e.groupSeparator, X = e.onChange, B = e.onFocus, G = e.onBlur, Y = e.onKeyDown, M = e.onKeyUp, he = e.transformRawValue, we = Me(e, ["allowDecimals", "allowNegativeValue", "id", "name", "className", "customInput", "decimalsLimit", "defaultValue", "disabled", "maxLength", "value", "onValueChange", "fixedDecimalLength", "placeholder", "decimalScale", "prefix", "suffix", "intlConfig", "step", "min", "max", "disableGroupSeparators", "disableAbbreviations", "decimalSeparator", "groupSeparator", "onChange", "onFocus", "onBlur", "onKeyDown", "onKeyUp", "transformRawValue"]);
  if (L && be(L))
    throw new Error("decimalSeparator cannot be a number");
  if (U && be(U))
    throw new Error("groupSeparator cannot be a number");
  var Z = Le(function() {
    return Xe(I);
  }, [I]), g = L || Z.decimalSeparator || "", $ = U || Z.groupSeparator || "";
  if (g && $ && g === $ && K === !1)
    throw new Error("decimalSeparator cannot be the same as groupSeparator");
  var T = {
    decimalSeparator: g,
    groupSeparator: $,
    disableGroupSeparators: K,
    intlConfig: I,
    prefix: P || Z.prefix,
    suffix: ae
  }, te = {
    decimalSeparator: g,
    groupSeparator: $,
    allowDecimals: t,
    decimalsLimit: D || k || 2,
    allowNegativeValue: n,
    disableAbbreviations: Q,
    prefix: P || Z.prefix,
    transformRawValue: he
  }, xe = p != null ? H(l(l({}, T), { decimalScale: E, value: String(p) })) : c != null ? H(l(l({}, T), { decimalScale: E, value: String(c) })) : "", fe = W(xe), b = fe[0], _ = fe[1], ce = W(!1), ne = ce[0], Ve = ce[1], de = W(0), ie = de[0], ue = de[1], se = W(0), pe = se[0], Re = se[1], ve = W(null), Ce = ve[0], Ne = ve[1], F = Ue(null);
  Be(r, function() {
    return F.current;
  });
  var me = function(d, y) {
    Ve(!0);
    var S = Ze({
      selectionStart: y,
      value: d,
      lastKeyStroke: Ce,
      stateValue: b,
      groupSeparator: $
    }), h = S.modifiedValue, w = S.cursorPosition, R = oe(l({ value: h }, te));
    if (!(s && R.replace(/-/g, "").length > s)) {
      if (R === "" || R === "-" || R === g) {
        m && m(void 0, f, { float: null, formatted: "", value: "" }), _(R), ue(1);
        return;
      }
      var ee = g ? R.replace(g, ".") : R, Pe = parseFloat(ee), le = H(l({ value: R }, T));
      if (w != null) {
        var re = w + (le.length - d.length);
        re = re <= 0 ? P ? P.length : 0 : re, ue(re), Re(pe + 1);
      }
      if (_(le), m) {
        var Ie = {
          float: Pe,
          formatted: le,
          value: R
        };
        m(R, f, Ie);
      }
    }
  }, De = function(d) {
    var y = d.target, S = y.value, h = y.selectionStart;
    me(S, h), X && X(d);
  }, Ee = function(d) {
    return B && B(d), b ? b.length : 0;
  }, Oe = function(d) {
    var y = d.target.value, S = oe(l({ value: y }, te));
    if (S === "-" || S === g || !S) {
      _(""), G && G(d);
      return;
    }
    var h = qe(S, g, k), w = Ye(h, g, E !== void 0 ? E : k), R = parseFloat(w.replace(g, ".")), ee = H(l(l({}, T), { value: w }));
    m && m(w, f, {
      float: R,
      formatted: ee,
      value: w
    }), _(ee), G && G(d);
  }, Fe = function(d) {
    var y = d.key;
    if (Ne(y), O && (y === "ArrowUp" || y === "ArrowDown")) {
      d.preventDefault(), ue(b.length);
      var S = parseFloat(c != null ? String(c).replace(g, ".") : oe(l({ value: b }, te))) || 0, h = y === "ArrowUp" ? S + O : S - O;
      if (q !== void 0 && h < q || z !== void 0 && h > z)
        return;
      var w = String(O).includes(".") ? Number(String(O).split(".")[1].length) : void 0;
      me(String(w ? h.toFixed(w) : h).replace(".", g));
    }
    Y && Y(d);
  }, ke = function(d) {
    var y = d.key, S = d.currentTarget.selectionStart;
    if (y !== "ArrowUp" && y !== "ArrowDown" && b !== "-") {
      var h = ye(b, { groupSeparator: $, decimalSeparator: g });
      if (h && S && S > b.length - h.length && F.current) {
        var w = b.length - h.length;
        F.current.setSelectionRange(w, w);
      }
    }
    M && M(d);
  };
  Ge(function() {
    ne && b !== "-" && F.current && document.activeElement === F.current && F.current.setSelectionRange(ie, ie);
  }, [b, ie, F, ne, pe]);
  var Ke = function() {
    return c != null && b !== "-" && (!g || b !== g) ? H(l(l({}, T), { decimalScale: ne ? void 0 : E, value: String(c) })) : b;
  }, ge = l({
    type: "text",
    inputMode: "decimal",
    id: i,
    name: f,
    className: o,
    onChange: De,
    onBlur: Oe,
    onFocus: Ee,
    onKeyDown: Fe,
    onKeyUp: ke,
    placeholder: x,
    disabled: A,
    value: Ke(),
    ref: F
  }, we);
  if (C) {
    var Ae = C;
    return Se.createElement(Ae, l({}, ge));
  }
  return Se.createElement("input", l({}, ge));
});
_e.displayName = "CurrencyInput";
export {
  _e as C
};
