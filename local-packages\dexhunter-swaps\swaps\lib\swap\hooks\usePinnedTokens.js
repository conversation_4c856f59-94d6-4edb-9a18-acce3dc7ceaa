import { useState as f, useEffect as s } from "react";
const i = "pinned_tokens", l = () => {
  const r = () => {
    const e = localStorage == null ? void 0 : localStorage.getItem(i);
    return e ? JSON.parse(e) : [];
  }, d = (e) => {
    localStorage == null || localStorage.setItem(i, JSON.stringify(e));
  }, [n, o] = f([]);
  s(() => {
    o(r());
  }, []);
  const c = (e) => {
    if (!(e != null && e.token_id))
      return;
    if (n.find((t) => t.token_id === e.token_id)) {
      o(
        (t) => t.filter((a) => a.token_id !== e.token_id)
      );
      return;
    }
    n.length < 8 && o((t) => [...t, e]);
  };
  return s(() => {
    d(n);
  }, [n]), { pinnedTokens: n, togglePinToken: c };
};
export {
  l as default
};
