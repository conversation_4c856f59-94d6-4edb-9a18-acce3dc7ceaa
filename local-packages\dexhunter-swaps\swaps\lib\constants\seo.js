const e = {
  title: "DexHunter - Smoothest Cardano trading.",
  defaultTitle: "DexHunter - Smoothest Cardano trading.",
  description: "DexHunter - Trade your favorite tokens with the best prices across Cardano.",
  canonical: "https://beta.dexhunter.io/",
  openGraph: {
    images: [
      {
        url: "https://storage.googleapis.com/dexhunter-images/seonotext.png",
        width: 1500,
        height: 1e3
      }
    ],
    title: "DexHunter - Smoothest Cardano trading.",
    description: "DexHunter - Trade your favorite tokens with the best prices across Cardano.",
    type: "website",
    locale: "en_US",
    url: "https://beta.dexhunter.io/",
    site_name: "dexhunter.io"
  },
  twitter: {
    handle: "@dexhunterio",
    site: "@dexhunterio",
    cardType: "summary_large_image",
    image: "https://storage.googleapis.com/dexhunter-images/seonotext.png"
  },
  icons: [
    {
      rel: "icon",
      type: "image/png",
      sizes: "any",
      url: "/favicon.ico"
    },
    {
      rel: "icon",
      type: "image/png",
      sizes: "32x32",
      url: "/favicon-32x32.png"
    },
    {
      rel: "icon",
      type: "image/png",
      sizes: "16x16",
      url: "/favicon-16x16.png"
    },
    {
      rel: "apple-touch-icon",
      sizes: "180x180",
      url: "/apple-touch-icon.png"
    }
  ]
};
export {
  e as defaultSeo
};
