import { p as d } from "../immer-548168ec.js";
const i = (s) => ({
  selectedOrdersIds: [],
  searchInput: "",
  isOrderCancelLoading: !1,
  pendingOrdersCount: 0,
  upcomingOrders: [],
  cancellingOrders: [],
  pendingDCAs: [],
  setSelectedOrdersIds: (r) => {
    s(
      d((e) => {
        e.userOrdersSlice.selectedOrdersIds = r;
      })
    );
  },
  toggleSelectedOrderId: (r) => {
    s(
      d((e) => {
        const c = e.userOrdersSlice.selectedOrdersIds.indexOf(r);
        c === -1 ? e.userOrdersSlice.selectedOrdersIds.push(r) : e.userOrdersSlice.selectedOrdersIds.splice(c, 1);
      })
    );
  },
  setSearchInput: (r) => {
    s(
      d((e) => {
        e.userOrdersSlice.searchInput = r;
      })
    );
  },
  setIsOrderCancelLoading: (r) => {
    s(
      d((e) => {
        e.userOrdersSlice.isOrderCancelLoading = r;
      })
    );
  },
  setPendingOrdersCount: (r) => {
    s(
      d((e) => {
        e.userOrdersSlice.pendingOrdersCount = r;
      })
    );
  },
  setUpcomingOrders: (r) => {
    s(
      d((e) => {
        e.userOrdersSlice.upcomingOrders = r;
      })
    );
  },
  setCancellingOrders: (r) => {
    s(
      d((e) => {
        e.userOrdersSlice.cancellingOrders = r;
      })
    );
  },
  setPendingDCAs: (r) => {
    s(
      d((e) => {
        e.userOrdersSlice.pendingDCAs = r;
      })
    );
  }
});
export {
  i as default
};
