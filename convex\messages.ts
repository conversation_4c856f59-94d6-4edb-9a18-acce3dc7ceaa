import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

async function findAccountByUserSubject(ctx: any, userSubject: string) {
  const parts = userSubject.split('|');
  for (const part of parts) {
    const account = await ctx.db
      .query('Accounts')
      .withIndex('by_auth_user_id', (q: any) => q.eq('auth_user_id', part))
      .first();
    if (account) return account;
  }
  // Fallback: try the full subject (in case some old records use it)
  const fallback = await ctx.db
    .query('Accounts')
    .withIndex('by_auth_user_id', (q: any) => q.eq('auth_user_id', userSubject))
    .first();
  return fallback;
}

export const sendMessage = mutation({
  args: {
    content: v.string(),
    receiverId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) return { success: false, message: 'Unauthorized' };

    // Look up the Accounts record for this user
    const account = await findAccountByUserSubject(ctx, user.subject);

    if (!account) return { success: false, message: 'Account not found' };

    const senderUserId = account.user_id; // Use the custom user_id from Accounts table

    const now = new Date().toISOString();
    const id = await ctx.db.insert('Messages', {
      id: crypto.randomUUID(),
      content: args.content,
      sender_id: senderUserId,
      receiver_id: args.receiverId,
      is_deleted: false,
      is_chat_deleted: null,
      created_at: now,
      is_edited: false,
      edited_at: null,
      is_read: false,
      media_url: null,
      media_type: null,
      sender_type: null,
      metadata: null,
    });

    // Optionally: trigger notification logic here (see below)
    // Optionally: trigger real-time logic here (see below)

    const message = await ctx.db.get(id);
    return { success: true, message };
  },
});

export const deleteMessage = mutation({
  args: {
    messageId: v.id('Messages'),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) return { success: false, message: 'Unauthorized' };

    const message = await ctx.db.get(args.messageId);
    if (!message || message.sender_id !== user.subject) {
      return { success: false, message: 'Message not found or not authorized' };
    }

    await ctx.db.patch(args.messageId, { is_deleted: true });

    // Optionally: trigger real-time logic here

    // After a message is soft-deleted, check if all messages in the conversation
    // are now deleted. If so, permanently delete them to clean up the database.
    const { sender_id, receiver_id } = message;

    // Find all messages in the conversation between the two users.
    const conversationMessages = await ctx.db
      .query('Messages')
      .filter(q =>
        q.or(
          q.and(
            q.eq(q.field('sender_id'), sender_id),
            q.eq(q.field('receiver_id'), receiver_id),
          ),
          q.and(
            q.eq(q.field('sender_id'), receiver_id),
            q.eq(q.field('receiver_id'), sender_id),
          ),
        ),
      )
      .collect();

    // Check if every message in the conversation is marked as deleted.
    const areAllMessagesDeleted = conversationMessages.every(
      msg => msg.is_deleted,
    );

    if (areAllMessagesDeleted) {
      // If all messages are soft-deleted, permanently delete them.
      await Promise.all(
        conversationMessages.map(msg => ctx.db.delete(msg._id)),
      );
    }

    return { success: true };
  },
});

export const editMessage = mutation({
  args: {
    messageId: v.id('Messages'),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) return { success: false, message: 'Unauthorized' };

    const message = await ctx.db.get(args.messageId);
    if (!message || message.sender_id !== user.subject) {
      return { success: false, message: 'Message not found or not authorized' };
    }

    const updatedAt = new Date().toISOString();
    await ctx.db.patch(args.messageId, {
      content: args.content,
      is_edited: true,
      edited_at: updatedAt,
    });

    // Optionally: trigger real-time logic here

    return { success: true };
  },
});

export const fetchMessages = query({
  args: {
    partnerId: v.string(),
    page: v.optional(v.number()),
    pageSize: v.optional(v.number()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) return { messages: [], cursor: null, isDone: true, totalMessages: 0, message: 'Unauthorized', success: false };
    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) return { messages: [], cursor: null, isDone: true, totalMessages: 0, message: 'Account not found', success: false };
    const userId = account.user_id;
    const conversationPartnerId = args.partnerId;
    const limit = args.limit ?? args.pageSize ?? 50;

    // Fetch messages sent by current user to partner
    const messagesFromCurrentUser = await ctx.db
      .query('Messages')
      .withIndex('by_sender_id', q => q.eq('sender_id', userId))
      .filter(q => q.eq(q.field('receiver_id'), conversationPartnerId))
      .order('desc')
      .collect();

    // Fetch messages sent by partner to current user
    const messagesFromPartner = await ctx.db
      .query('Messages')
      .withIndex('by_sender_id', q => q.eq('sender_id', conversationPartnerId))
      .filter(q => q.eq(q.field('receiver_id'), userId))
      .order('desc')
      .collect();

    // Combine and sort all messages by created_at descending
    const allMessages = [...messagesFromCurrentUser, ...messagesFromPartner]
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    const pagedMessages = allMessages.slice(0, limit);

    // Mark unread messages as read if current user is the receiver
    const unreadMessageIds = pagedMessages
      .filter(msg => msg.receiver_id === userId && !msg.is_read)
      .map(msg => msg._id);
    if (unreadMessageIds.length > 0) {
      try {
        await Promise.all(
          unreadMessageIds.map(async id => {
            await ctx.db.patch(id, { is_read: true });
          })
        );
      } catch (error) {
        console.error('Failed to mark messages as read:', unreadMessageIds, error);
      }
    }

    // --- ENRICH MESSAGES WITH USER INFO ---
    // Get all unique user_ids in this conversation
    const userIds = Array.from(
      new Set(pagedMessages.flatMap(msg => [msg.sender_id, msg.receiver_id]))
    );

    // Fetch all user info
    let users: any[] = [];
    if (userIds.length > 0) {
      users = await ctx.db
        .query('Accounts')
        .filter(q => {
          let filter = q.eq(q.field('user_id'), userIds[0]);
          for (let i = 1; i < userIds.length; i++)
            filter = q.or(filter, q.eq(q.field('user_id'), userIds[i]));
          return filter;
        })
        .collect();
    }

    // Map user_id to user info
    const userMap = new Map<string, any>();
    for (const u of users) {
      userMap.set(u.user_id, {
        user_id: u.user_id,
        username: u.user_info.account.username,
        profilePhoto: u.user_info.profilePhoto,
        displayName: u.user_info.account.displayName,
        isVerified: u.user_info.isVerified,
        status: u.user_info.status,
      });
    }

    // Attach user info to each message
    const messagesWithUserInfo = pagedMessages.reverse().map(msg => ({
      ...msg,
      sender: userMap.get(msg.sender_id) || { user_id: msg.sender_id },
      receiver: userMap.get(msg.receiver_id) || { user_id: msg.receiver_id },
    }));

    return {
      success: true,
      messages: messagesWithUserInfo,
      cursor: null, // Not available with in-memory pagination
      isDone: true,
      totalMessages: allMessages.length,
    };
  },
});

export const sendPoke = mutation({
  args: {
    recipientId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) return { success: false, message: 'Unauthorized' };
    if (!args.recipientId) return { success: false, message: 'Recipient ID is required' };

    // Record the poke in a new Pokes table (if it exists) or just log it
    // We'll use a Pokes table with sender_id, recipient_id, created_at
    await ctx.db.insert('Pokes', {
      sender_id: user.subject,
      recipient_id: args.recipientId,
      created_at: new Date().toISOString(),
    });

    return { success: true, message: 'Poke sent successfully' };
  },
});

export const getAllConversations = query({
  args: {},
  handler: async (ctx) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) return { conversations: [], message: 'Unauthorized', success: false };

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) return { conversations: [], message: 'Account not found', success: false };
    const userId = account.user_id;

    const allMessages = await ctx.db
      .query('Messages')
      .filter(q =>
        q.and(
          q.or(
            q.eq(q.field('sender_id'), userId),
            q.eq(q.field('receiver_id'), userId)
          ),
          q.eq(q.field('is_deleted'), false)
        )
      )
      .collect();

    const conversationMap = new Map<string, any>();

    for (const msg of allMessages) {
      const partnerId = msg.sender_id === userId ? msg.receiver_id : msg.sender_id;
      const existing = conversationMap.get(partnerId);

      if (!existing || new Date(msg.created_at) > new Date(existing.created_at)) {
        conversationMap.set(partnerId, msg);
      }
    }

    // Fetch user info for all partners
    const partnerIds = Array.from(conversationMap.keys());
    // Fetch partners by user_id using multiple .or(q.eq(...)) since .in is not available
    let partners: any[] = [];
    if (partnerIds.length > 0) {
      partners = await ctx.db
        .query('Accounts')
        .filter(q => {
          let filter = q.eq(q.field('user_id'), partnerIds[0]);
          for (let i = 1; i < partnerIds.length; i++)
            filter = q.or(filter, q.eq(q.field('user_id'), partnerIds[i]));
          return filter;
        })
        .collect();
    }

    // Map user_id to user info
    const partnerMap = new Map<string, any>();
    for (const partner of partners) {
      partnerMap.set(partner.user_id, {
        user_id: partner.user_id,
        username: partner.user_info.account.username,
        profilePhoto: partner.user_info.profilePhoto,
        displayName: partner.user_info.account.displayName,
        isVerified: partner.user_info.isVerified,
        status: partner.user_info.status,
      });
    }

    // Attach partner info to each conversation message
    const conversations = Array.from(conversationMap.values()).map(msg => {
      const partnerId = msg.sender_id === userId ? msg.receiver_id : msg.sender_id;
      return {
        ...msg,
        sender: msg.sender_id === userId
          ? { user_id: userId, ...account }
          : partnerMap.get(msg.sender_id) || { user_id: msg.sender_id },
        receiver: msg.receiver_id === userId
          ? { user_id: userId, ...account }
          : partnerMap.get(msg.receiver_id) || { user_id: msg.receiver_id },
      };
    }).sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    return { conversations };
  },
});