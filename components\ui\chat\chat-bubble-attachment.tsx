"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { Download, FileText, Film, Maximize2, File } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Lightbox from 'react-18-image-lightbox';
import 'react-18-image-lightbox/style.css';
import ReactPlayer from 'react-player/lazy';

interface ChatBubbleAttachmentProps {
  attachment: {
    url: string;
    type: string;
    name: string;
  };
}

export const ChatBubbleAttachment: React.FC<ChatBubbleAttachmentProps> = ({ 
  attachment 
}) => {
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  
  const isImage = attachment.type.startsWith('image/');
  const isVideo = attachment.type.startsWith('video/');
  const isPdf = attachment.type === 'application/pdf';
  
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  if (isImage) {
    return (
      <>
        <div className="relative group rounded-md overflow-hidden max-w-xs">
          <div className="relative w-full h-48">
            <Image 
              src={attachment.url} 
              alt={attachment.name || "Image attachment"}
              fill
              style={{ objectFit: 'cover' }}
              className="rounded-md"
            />
          </div>
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex gap-2">
              <Button 
                size="icon" 
                variant="ghost" 
                className="h-8 w-8 bg-white/80 hover:bg-white text-black rounded-full"
                onClick={() => setIsLightboxOpen(true)}
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
              <Button 
                size="icon" 
                variant="ghost" 
                className="h-8 w-8 bg-white/80 hover:bg-white text-black rounded-full"
                onClick={handleDownload}
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        {isLightboxOpen && (
          <Lightbox
            mainSrc={attachment.url}
            onCloseRequest={() => setIsLightboxOpen(false)}
            reactModalStyle={{ overlay: { zIndex: 1500 } }}
          />
        )}
      </>
    );
  }
  
  if (isVideo) {
    return (
      <div className="max-w-xs rounded-md overflow-hidden">
        <ReactPlayer
          url={attachment.url}
          controls
          width="100%"
          height="auto"
          light={true}
          pip={true}
        />
        <div className="flex justify-end p-2 bg-gray-100 dark:bg-gray-800">
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-8 text-xs"
            onClick={handleDownload}
          >
            <Download className="h-3 w-3 mr-1" />
            Download
          </Button>
        </div>
      </div>
    );
  }
  
  // For other file types (PDF, etc.)
  return (
    <div className="flex items-center p-3 bg-gray-100 dark:bg-gray-800 rounded-md max-w-xs">
      {isPdf ? (
        <FileText className="h-8 w-8 text-red-500 mr-2 flex-shrink-0" />
      ) : (
        <File className="h-8 w-8 text-blue-500 mr-2 flex-shrink-0" />
      )}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{attachment.name}</p>
        <p className="text-xs text-gray-500 truncate">
          {attachment.type.split('/')[1]?.toUpperCase() || 'FILE'}
        </p>
      </div>
      <Button 
        size="icon" 
        variant="ghost" 
        className="h-8 w-8 ml-2 flex-shrink-0"
        onClick={handleDownload}
      >
        <Download className="h-4 w-4" />
      </Button>
    </div>
  );
};