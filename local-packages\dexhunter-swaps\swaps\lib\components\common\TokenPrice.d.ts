type TokenPriceProps = {
    tokenPrice: number;
    className?: string;
    tokenSellTicker?: string;
    tokenBuyTicker?: string;
    classNameText?: string;
    isReverse?: boolean;
    isLoading?: boolean;
    onClick?: () => void;
};
declare const TokenPrice: ({ tokenPrice, className, tokenSellTicker, tokenBuyTicker, classNameText, isLoading, isReverse, onClick, }: TokenPriceProps) => import("react/jsx-runtime").JSX.Element;
export default TokenPrice;
