import { jsxs as r, jsx as e } from "react/jsx-runtime";
import { memo as t } from "react";
const C = (o) => /* @__PURE__ */ r(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 24 27",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...o,
    children: [
      /* @__PURE__ */ e(
        "path",
        {
          d: "M1 25.3903H23",
          stroke: "currentColor",
          strokeWidth: 2,
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }
      ),
      /* @__PURE__ */ e(
        "path",
        {
          fillRule: "evenodd",
          clipRule: "evenodd",
          d: "M1 11.9756C1 10.8259 1 10.251 1.35798 9.8938C1.71597 9.53662 2.29212 9.53662 3.44444 9.53662C4.59677 9.53662 5.17292 9.53662 5.53091 9.8938C5.88889 10.251 5.88889 10.8259 5.88889 11.9756V19.2927C5.88889 20.4425 5.88889 21.0174 5.53091 21.3745C5.17292 21.7317 4.59677 21.7317 3.44444 21.7317C2.29212 21.7317 1.71597 21.7317 1.35798 21.3745C1 21.0174 1 20.4425 1 19.2927V11.9756Z",
          stroke: "currentColor",
          strokeWidth: 2
        }
      ),
      /* @__PURE__ */ e(
        "path",
        {
          fillRule: "evenodd",
          clipRule: "evenodd",
          d: "M9.55566 7.09759C9.55566 5.94783 9.55566 5.37295 9.91365 5.01575C10.2716 4.65857 10.8478 4.65857 12.0001 4.65857C13.1524 4.65857 13.7286 4.65857 14.0866 5.01575C14.4446 5.37295 14.4446 5.94783 14.4446 7.09759V19.2927C14.4446 20.4425 14.4446 21.0173 14.0866 21.3745C13.7286 21.7317 13.1524 21.7317 12.0001 21.7317C10.8478 21.7317 10.2716 21.7317 9.91365 21.3745C9.55566 21.0173 9.55566 20.4425 9.55566 19.2927V7.09759Z",
          stroke: "currentColor",
          strokeWidth: 2
        }
      ),
      /* @__PURE__ */ e(
        "path",
        {
          fillRule: "evenodd",
          clipRule: "evenodd",
          d: "M18.1113 3.43902C18.1113 2.28926 18.1113 1.71438 18.4693 1.35718C18.8273 1 19.4035 1 20.5558 1C21.7081 1 22.2842 1 22.6422 1.35718C23.0002 1.71438 23.0002 2.28926 23.0002 3.43902V19.2927C23.0002 20.4424 23.0002 21.0173 22.6422 21.3745C22.2842 21.7317 21.7081 21.7317 20.5558 21.7317C19.4035 21.7317 18.8273 21.7317 18.4693 21.3745C18.1113 21.0173 18.1113 20.4424 18.1113 19.2927V3.43902Z",
          stroke: "currentColor",
          strokeWidth: 2
        }
      )
    ]
  }
), n = t(C);
export {
  n as default
};
