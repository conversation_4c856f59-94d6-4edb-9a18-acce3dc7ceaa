/* eslint-disable @next/next/no-img-element */
import React from 'react';

const NotificationMessage = ({ title, text, url }: { title: string, text: string, url: string }) => (
  <div className="toast-notification-container">
    <img className="toast-notification-image" src={url || '/images/user/default-avatar.webp'} alt={title} />
    <div className='toast-notification-info-container'>
      <p className="toast-notification-title">{title}</p>
      <p className="toast-notification-text">{text}</p>
    </div>
  </div>
);
export default NotificationMessage;