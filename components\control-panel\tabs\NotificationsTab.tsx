'use client';

import { useState, useEffect, useRef } from 'react';
import { updateUserSettings } from '@/lib/api/updateUserSettings';
import { motion } from 'framer-motion';
import { toast } from 'react-toastify';
import { SettingsSection, type SettingOption } from '@/components/ui/settings-section';

interface NotificationsTabProps {
  userData?: any;
  onFormChange?: (hasChanges: boolean, updatedData?: any, loading?: boolean) => void;
}

const NotificationsTab = ({ userData, onFormChange }: NotificationsTabProps) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingSetting, setLoadingSetting] = useState<string | null>(null);
  const initialSettings = useRef(userData?.notificationSettings || {});

  const handleSettingChange = async (setting: string, value: boolean | string) => {
    setLoadingSetting(setting);
    setIsLoading(true);
    onFormChange?.(true, undefined, true);

    try {
      let settingsToUpdate = { [setting]: value };

      if (setting === 'enableAllNotifications') {
        settingsToUpdate = {
          enableAllNotifications: value,
          pushNotifications: value,
          emailNotifications: value,
          smsNotifications: value,
          newSubscriberAlerts: value,
          subscriptionRenewals: value,
          tipsAndDonations: value,
          earningsUpdates: value,
          newLikes: value,
          newComments: value,
          mentionsAndTags: value,
          sharesAndReposts: value,
          newDirectMessages: value,
          messageRequests: value,
          liveStreamInvitations: value,
          storyReplies: value,
          loginAlerts: value,
          suspiciousActivity: value,
          accountUpdates: value,
          platformAnnouncements: value,
          doNotDisturb: value,
        };
      }

      const result = await updateUserSettings(
        userData.userId,
        userData.accountType,
        'notifications',
        settingsToUpdate
      );

      if (result.success) {
        const updatedUserData = {
          ...userData,
          notificationSettings: {
            ...userData.notificationSettings,
            ...settingsToUpdate
          }
        };

        initialSettings.current = {
          ...initialSettings.current,
          ...settingsToUpdate
        };

        onFormChange?.(false, updatedUserData, false);
        setHasUnsavedChanges(false);
        toast.success('Setting updated successfully');
      } else {
        throw new Error('Failed to update setting');
      }
    } catch (error) {
      toast.error('Failed to update setting');
      onFormChange?.(true, userData, false);
      throw error;
    } finally {
      setLoadingSetting(null);
      setIsLoading(false);
      onFormChange?.(hasUnsavedChanges, undefined, false);
    }
  };

  useEffect(() => {
    const currentSettings = userData?.notificationSettings || {};
    const hasChanges = JSON.stringify(initialSettings.current) !== JSON.stringify(currentSettings);
    setHasUnsavedChanges(hasChanges);
    onFormChange?.(hasChanges, undefined, isLoading);
  }, [userData?.notificationSettings, onFormChange, isLoading]);

  useEffect(() => {
    if (!userData?.notificationSettings) return;

    // Get all notification settings except enableAllNotifications
    const notificationValues = Object.entries(userData.notificationSettings)
      .filter(([key]) => key !== 'enableAllNotifications')
      .map(([, value]) => value);

    // If all settings are true, enableAllNotifications should be true
    // If any setting is false, enableAllNotifications should be false
    const shouldBeEnabled = notificationValues.every(value => value === true);

    // Only update if the current value is different
    if (userData.notificationSettings.enableAllNotifications !== shouldBeEnabled) {
      // Instead of calling handleSettingChange, just update the local state
      const updatedUserData = {
        ...userData,
        notificationSettings: {
          ...userData.notificationSettings,
          enableAllNotifications: shouldBeEnabled
        }
      };

      initialSettings.current = {
        ...initialSettings.current,
        enableAllNotifications: shouldBeEnabled
      };

      onFormChange?.(false, updatedUserData, false);
    }
  }, [userData?.notificationSettings, onFormChange]);

  const getOptionWithLoadingState = (option: SettingOption): SettingOption => ({
    ...option,
    disabled: loadingSetting === option.id || loadingSetting !== null,
    className: (loadingSetting === option.id || loadingSetting !== null) ? 'opacity-50 cursor-not-allowed' : '',
    loading: loadingSetting === option.id
  });

  const generalOptions = [
    {
      id: 'enableAllNotifications',
      label: 'Enable All Notifications',
      description: 'Master switch to control all notifications',
      type: 'switch' as const,
      value: userData?.notificationSettings?.enableAllNotifications ?? true,
      onChange: async (checked: boolean) => handleSettingChange('enableAllNotifications', checked)
    },
    {
      id: 'pushNotifications',
      label: 'Push Notifications',
      description: 'Receive notifications on your devices',
      type: 'switch' as const,
      value: userData?.notificationSettings?.pushNotifications ?? true,
      onChange: async (checked: boolean) => handleSettingChange('pushNotifications', checked)
    },
    {
      id: 'emailNotifications',
      label: 'Email Notifications',
      description: 'Receive updates via email',
      type: 'switch' as const,
      value: userData?.notificationSettings?.emailNotifications ?? true,
      onChange: async (checked: boolean) => handleSettingChange('emailNotifications', checked)
    },
    {
      id: 'smsNotifications',
      label: 'SMS Notifications',
      description: 'Receive notifications via SMS',
      type: 'switch' as const,
      value: userData?.notificationSettings?.smsNotifications ?? false,
      onChange: async (checked: boolean) => handleSettingChange('smsNotifications', checked)
    },
    {
      id: 'doNotDisturb',
      label: 'Do Not Disturb',
      description: 'Temporarily pause all notifications',
      type: 'switch' as const,
      value: userData?.notificationSettings?.doNotDisturb ?? false,
      onChange: async (checked: boolean) => handleSettingChange('doNotDisturb', checked)
    }
  ].map(getOptionWithLoadingState);

  const subscriptionOptions = [
    {
      id: 'newSubscriberAlerts',
      label: 'New Subscriber Alerts',
      description: 'Get notified when someone subscribes',
      type: 'switch' as const,
      value: userData?.notificationSettings?.newSubscriberAlerts ?? true,
      onChange: async (checked: boolean) => handleSettingChange('newSubscriberAlerts', checked)
    },
    {
      id: 'subscriptionRenewals',
      label: 'Subscription Renewals',
      description: 'Notify when users renew their subscription',
      type: 'switch' as const,
      value: userData?.notificationSettings?.subscriptionRenewals ?? true,
      onChange: async (checked: boolean) => handleSettingChange('subscriptionRenewals', checked)
    },
    {
      id: 'tipsAndDonations',
      label: 'Tips & Donations',
      description: 'Get notified when you receive tips',
      type: 'switch' as const,
      value: userData?.notificationSettings?.tipsAndDonations ?? true,
      onChange: async (checked: boolean) => handleSettingChange('tipsAndDonations', checked)
    },
    {
      id: 'earningsUpdates',
      label: 'Earnings Updates',
      description: 'Notifications about withdrawals and payouts',
      type: 'switch' as const,
      value: userData?.notificationSettings?.earningsUpdates ?? true,
      onChange: async (checked: boolean) => handleSettingChange('earningsUpdates', checked)
    }
  ].map(getOptionWithLoadingState);

  const engagementOptions = [
    {
      id: 'newLikes',
      label: 'New Likes/Reactions',
      description: 'Notify when someone likes your post',
      type: 'switch' as const,
      value: userData?.notificationSettings?.newLikes ?? true,
      onChange: async (checked: boolean) => handleSettingChange('newLikes', checked)
    },
    {
      id: 'newComments',
      label: 'New Comments',
      description: 'Alert for new comments on your posts',
      type: 'switch' as const,
      value: userData?.notificationSettings?.newComments ?? true,
      onChange: async (checked: boolean) => handleSettingChange('newComments', checked)
    },
    {
      id: 'mentionsAndTags',
      label: 'Mentions & Tags',
      description: 'Get notified when someone tags or mentions you',
      type: 'switch' as const,
      value: userData?.notificationSettings?.mentionsAndTags ?? true,
      onChange: async (checked: boolean) => handleSettingChange('mentionsAndTags', checked)
    },
    {
      id: 'sharesAndReposts',
      label: 'Shares/Reposts',
      description: 'Notify when someone shares your content',
      type: 'switch' as const,
      value: userData?.notificationSettings?.sharesAndReposts ?? true,
      onChange: async (checked: boolean) => handleSettingChange('sharesAndReposts', checked)
    }
  ].map(getOptionWithLoadingState);

  const messagingOptions = [
    {
      id: 'newDirectMessages',
      label: 'New Direct Messages',
      description: 'Alerts for new DMs',
      type: 'switch' as const,
      value: userData?.notificationSettings?.newDirectMessages ?? true,
      onChange: async (checked: boolean) => handleSettingChange('newDirectMessages', checked)
    },
    {
      id: 'messageRequests',
      label: 'Message Requests',
      description: 'Notification for new message requests',
      type: 'switch' as const,
      value: userData?.notificationSettings?.messageRequests ?? true,
      onChange: async (checked: boolean) => handleSettingChange('messageRequests', checked)
    },
    {
      id: 'liveStreamInvitations',
      label: 'Live Stream Invitations',
      description: 'Get notified for live stream invites',
      type: 'switch' as const,
      value: userData?.notificationSettings?.liveStreamInvitations ?? true,
      onChange: async (checked: boolean) => handleSettingChange('liveStreamInvitations', checked)
    },
    {
      id: 'storyReplies',
      label: 'Story Replies',
      description: 'Alerts when someone replies to your stories',
      type: 'switch' as const,
      value: userData?.notificationSettings?.storyReplies ?? true,
      onChange: async (checked: boolean) => handleSettingChange('storyReplies', checked)
    }
  ].map(getOptionWithLoadingState);

  const securityOptions = [
    {
      id: 'loginAlerts',
      label: 'Login Alerts',
      description: 'Notify of new device or location logins',
      type: 'switch' as const,
      value: userData?.notificationSettings?.loginAlerts ?? true,
      onChange: async (checked: boolean) => handleSettingChange('loginAlerts', checked)
    },
    {
      id: 'suspiciousActivity',
      label: 'Suspicious Activity Alerts',
      description: 'Get notified of unusual account activity',
      type: 'switch' as const,
      value: userData?.notificationSettings?.suspiciousActivity ?? true,
      onChange: async (checked: boolean) => handleSettingChange('suspiciousActivity', checked)
    },
    {
      id: 'accountUpdates',
      label: 'Account Updates',
      description: 'Notification for account setting changes',
      type: 'switch' as const,
      value: userData?.notificationSettings?.accountUpdates ?? true,
      onChange: async (checked: boolean) => handleSettingChange('accountUpdates', checked)
    },
    {
      id: 'platformAnnouncements',
      label: 'Platform Announcements',
      description: 'Updates about features and policies',
      type: 'switch' as const,
      value: userData?.notificationSettings?.platformAnnouncements ?? true,
      onChange: async (checked: boolean) => handleSettingChange('platformAnnouncements', checked)
    }
  ].map(getOptionWithLoadingState);

  const customizationOptions = [
    {
      id: 'digestFrequency',
      label: 'Notification Digest',
      description: 'Choose how often to receive notification summaries',
      type: 'select' as const,
      value: userData?.notificationSettings?.digestFrequency || 'realtime',
      options: [
        { value: 'realtime', label: 'Real-time' },
        { value: 'daily', label: 'Daily Digest' },
        { value: 'weekly', label: 'Weekly Digest' }
      ],
      onChange: async (value: string) => handleSettingChange('digestFrequency', value)
    },
    {
      id: 'priorityLevel',
      label: 'Priority Notifications',
      description: 'Set notification priority levels',
      type: 'select' as const,
      value: userData?.notificationSettings?.priorityLevel || 'all',
      options: [
        { value: 'all', label: 'All Notifications' },
        { value: 'important', label: 'Important Only' }
      ],
      onChange: async (value: string) => handleSettingChange('priorityLevel', value)
    }
  ].map(getOptionWithLoadingState);

  return (
    <motion.div
      key="notifications"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`space-y-6 ${isLoading ? 'pointer-events-none' : ''}`}
    >
      <SettingsSection
        title="General Notification Settings"
        options={generalOptions}
      />

      <SettingsSection
        title="Subscription & Earnings Notifications"
        options={subscriptionOptions}
      />

      <SettingsSection
        title="Engagement Notifications"
        options={engagementOptions}
      />

      <SettingsSection
        title="Direct Messaging & Interaction Controls"
        options={messagingOptions}
      />

      <SettingsSection
        title="Platform & Security Alerts"
        options={securityOptions}
      />

      <SettingsSection
        title="Customization & Frequency Controls"
        options={customizationOptions}
      />
    </motion.div>
  );
};
export default NotificationsTab;