import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { SelectDropdown } from '@/components/ui/select-dropdown';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { PrivacySettings } from './types';

interface PostFormHeaderProps {
  user: any;
  privacySettings: PrivacySettings;
  onPrivacyChange: (value: string) => void;
  onConfigurePrivacy: () => void;
  isVerifiedBadgeLoading: boolean;
  showConfigureButton: boolean;
}

export const PostFormHeader: React.FC<PostFormHeaderProps> = ({
  user,
  privacySettings,
  onPrivacyChange,
  onConfigurePrivacy,
  isVerifiedBadgeLoading,
  setVerifiedBadgeLoading,
  showConfigureButton,
}) => {
  const privacyOptions = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
  ];

  return (
    <div className="flex items-center justify-between">
      {/* User Avatar Section */}
      <div className="w-full flex items-center justify-start mb-4 gap-2">
        <Avatar className="h-10 w-10">
          <AvatarImage src={user?.profilePhoto || '/images/user/default-avatar.webp'} />
          <AvatarFallback>{user?.account.username?.charAt(0) || 'U'}</AvatarFallback>
        </Avatar>
        <div className="ml-2 flex-grow">
          <div className="flex items-center gap-1 text-sm font-medium text-gray-900 dark:text-white">
            {user?.account.displayName || 'User'}{' '}
            {user?.isVerified && (
              <Skeleton loading={isVerifiedBadgeLoading} width="15px" height="15px">
                <img
                  className="w-[15px] h-[15px] user-dropdown-verified-badge"
                  alt="Verified User"
                  src="/images/user/verified.png"
                  onLoad={() => setVerifiedBadgeLoading(false)}
                  onError={() => setVerifiedBadgeLoading(false)}
                />
              </Skeleton>
            )}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            @{user?.account.username || 'User'}
          </div>
        </div>
      </div>

      {/* Privacy Settings Section */}
      <div className="w-1/4 flex flex-col items-start justify-start gap-2 mb-3">
        <div className="w-full flex flex-col items-center justify-center gap-2 mb-3">
          <SelectDropdown
            options={privacyOptions}
            value={privacySettings.privacy === 'default' ? '' : privacySettings.privacy}
            onChange={onPrivacyChange}
            placeholder="Select Privacy"
            className="w-40 bg-transparent rounded-md text-sm text-gray-900 dark:text-white"
          />

          {showConfigureButton && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="text-xs py-1 px-2 h-auto"
              onClick={onConfigurePrivacy}
            >
              Configure Privacy Settings
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};