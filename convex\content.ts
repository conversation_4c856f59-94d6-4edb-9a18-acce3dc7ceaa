import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

async function findAccountByUserSubject(ctx: any, userSubject: string) {
  const parts = userSubject.split('|');
  for (const part of parts) {
    const account = await ctx.db
      .query('Accounts')
      .withIndex('by_auth_user_id', (q: any) => q.eq('auth_user_id', part))
      .first();
    if (account) return account;
  }
  // Fallback: try the full subject (in case some old records use it)
  const fallback = await ctx.db
    .query('Accounts')
    .withIndex('by_auth_user_id', (q: any) => q.eq('auth_user_id', userSubject))
    .first();
  return fallback;
}

export const createContent = mutation({
  args: {
    description: v.string(),
    content_type: v.string(),
    is_paid: v.boolean(),
    creator_id: v.string(),
    visibility: v.string(),
    metadata: v.optional(v.any()),
    media_id: v.optional(v.id("MediaLibrary")),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const userId = user.subject;

    // Check if user is creator
    const userAccount = await ctx.db
      .query("Accounts")
      .filter((q) => q.eq(q.field("user_id"), userId))
      .first();

    if (!userAccount) {
      throw new Error("Account not found");
    }

    // First, get the Convex ID for the account using the user_id
    const creatorAccount = await ctx.db
      .query("Accounts")
      .filter((q) => q.eq(q.field("user_id"), args.creator_id))
      .first();

    if (!creatorAccount) {
      throw new Error("Account not found");
    }

    // Now create the content with the proper Convex ID reference
    const contentId = await ctx.db.insert("Content", {
      id: crypto.randomUUID(),
      description: args.description,
      content_type: args.content_type ?? null,
      is_paid: args.is_paid,
      created_at: new Date().toISOString(),
      creator_id: creatorAccount._id,
      is_edited: false,
      edited_at: null,
      visibility: args.visibility,
      metadata: args.metadata,
      media_id: args.media_id ?? null,
    });

    return contentId;
  },
});

export const updatePost = mutation({
  args: {
    postId: v.string(),
    description: v.optional(v.string()),
    visibility: v.optional(v.string()),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) throw new Error('Account not found');

    const userId = account.user_id; // Use the custom user_id from Accounts table

    // Find the post and verify ownership
    const post = await ctx.db
      .query('Content')
      .filter((q) => q.and(
        q.eq(q.field('id'), args.postId),
        q.eq(q.field('creator_id'), userId)
      ))
      .first();

    if (!post) throw new Error('Post not found or not owned by user');

    // Update the post
    const updateData: any = {
      is_edited: true,
      edited_at: new Date().toISOString(),
    };

    if (args.description !== undefined) updateData.description = args.description;
    if (args.visibility !== undefined) updateData.visibility = args.visibility;
    if (args.metadata !== undefined) updateData.metadata = args.metadata;

    await ctx.db.patch(post._id, updateData);

    return { success: true };
  },
});

export const deletePost = mutation({
  args: {
    postId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) throw new Error('Account not found');

    const userId = account.user_id; // Use the custom user_id from Accounts table

    // Find the post and verify ownership
    const post = await ctx.db
      .query('Content')
      .filter((q) => q.and(
        q.eq(q.field('id'), args.postId),
        q.eq(q.field('creator_id'), userId)
      ))
      .first();

    if (!post) throw new Error('Post not found or not owned by user');

    // Delete the post
    await ctx.db.delete(post._id);

    return { success: true };
  },
});

export const reportPost = mutation({
  args: {
    postId: v.string(),
    reason: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) throw new Error('Account not found');

    const userId = account.user_id; // Use the custom user_id from Accounts table

    // Check if post exists
    const post = await ctx.db
      .query('Content')
      .filter((q) => q.eq(q.field('id'), args.postId))
      .first();

    if (!post) throw new Error('Post not found');

    // Insert report
    await ctx.db.insert('Reports', {
      reporter_id: userId,
      content_id: args.postId,
      reason: args.reason,
      status: 'pending',
      created_at: new Date().toISOString(),
    });

    return { success: true };
  },
});

export const fetchPostsFeed = query({
  args: {
    filter: v.optional(v.string()),
    page: v.optional(v.number()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
    profileUserId: v.optional(v.string()),
    panel: v.optional(v.string()),
    direction: v.optional(v.string()),
    tag: v.optional(v.string()),
    gender: v.optional(v.string()),
    userId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const {
      filter = "recently-added",
      limit = 20,
      cursor = null,
      panel = "all-posts",
      direction = "forward",
      tag,
      gender,
      profileUserId,
      userId,
    } = args;

    let q = ctx.db
      .query("Content")
      .withIndex("by_created_at")
      .order(filter === "oldest" ? "asc" : "desc");

    if (profileUserId) {
      q = q.filter((qq) =>
        qq.eq(qq.field("creator_id"), profileUserId)
      );
    }

    if (panel !== "all-posts") {
      if (panel === "gifs") {
        q = q.filter((qq) =>
          qq.eq(qq.field("content_type"), "image/gif")
        );
      } else {
        const prefix = panel === "videos"
          ? "video/"
          : panel === "photos"
            ? "image/"
            : panel === "audio"
              ? "audio/"
              : null;
        if (prefix) {
          // DB filter to narrow results; full logic will be client-side
          q = q.filter((qq) =>
            qq.eq(qq.field("content_type"), prefix)
          );
        }
      }
    }

    const res = await q.paginate({ numItems: limit, cursor });
    let posts = res.page;

    // Post-query filtering
    if (panel !== "all-posts") {
      posts = posts.filter((p) => {
        const type = p.content_type ?? '';
        if (panel === "gifs") return type === "image/gif";
        if (panel === "videos") return type.startsWith("video/");
        if (panel === "photos")
          return (
            type.startsWith("image/") &&
            type !== "image/gif"
          );
        if (panel === "audio") return type === "audio/mpeg";
        return panel === "clips"
          ? type.startsWith("video/") && (p.metadata?.duration ?? 0) < 60
          : true;
      });
    }

    if (filter === "following" && userId) {
      const follows = await ctx.db
        .query("Follows")
        .withIndex("by_follower")
        .filter((q2) => q2.eq(q2.field("follower_id"), userId))
        .collect();
      const followedSet = new Set(follows.map((f) => f.followed_id));
      posts = posts.filter((p) => followedSet.has(p.creator_id));
    }

    if (tag) {
      const normalized = tag.replace(/^#/, "").toLowerCase();
      const hts = await ctx.db
        .query("Hashtags")
        .withIndex("by_name")
        .filter((q2) => q2.eq(q2.field("name"), normalized))
        .collect();
      const tagSet = new Set(hts.map((h) => h.content_id));
      posts = posts.filter((p) => tagSet.has(p.id));
    }

    if (gender) {
      const creatorIds = Array.from(new Set(posts.map((p) => p.creator_id)));
      const accounts = await ctx.db
        .query("Accounts")
        .withIndex("by_user_id")
        .filter((q2) =>
          q2.or(...creatorIds.map((id) =>
            q2.eq(q2.field("user_id"), id)
          ))
        )
        .collect();
      const genderSet = new Set(
        accounts
          .filter((a) => a.user_info.account.gender?.toLowerCase() === gender.toLowerCase())
          .map((a) => a.user_id)
      );
      posts = posts.filter((p) => genderSet.has(p.creator_id));
    }

    const postIds = posts.map((p) => p.id);
    const creatorIds = Array.from(new Set(posts.map((p) => p.creator_id)));

    const [
      hashtagsDocs,
      mentionsDocs,
      creatorsDocs,
      likes,
      views,
      comments,
      saves,
      purchases,
      userLikes,
      userSaves,
    ] = await Promise.all([
      ctx.db.query("Hashtags").withIndex("by_content_id")
        .filter((q2) => q2.or(...postIds.map((id) =>
          q2.eq(q2.field("content_id"), id)
        )))
        .collect(),
      ctx.db.query("ContentMentions").withIndex("by_content_id")
        .filter((q2) => q2.or(...postIds.map((id) =>
          q2.eq(q2.field("content_id"), id)
        )))
        .collect(),
      ctx.db.query("Accounts").withIndex("by_user_id")
        .filter((q2) => q2.or(...creatorIds.map((id) =>
          q2.eq(q2.field("user_id"), id)
        )))
        .collect(),
      ctx.db.query("Likes").withIndex("by_content_id")
        .filter((q2) => q2.or(...postIds.map((id) =>
          q2.eq(q2.field("content_id"), id)
        )))
        .collect(),
      ctx.db.query("ContentViews").withIndex("by_content_id")
        .filter((q2) => q2.or(...postIds.map((id) =>
          q2.eq(q2.field("content_id"), id)
        )))
        .collect(),
      ctx.db.query("Comments").withIndex("by_content_id")
        .filter((q2) => q2.or(...postIds.map((id) =>
          q2.eq(q2.field("content_id"), id)
        )))
        .collect(),
      ctx.db.query("SavedContent").withIndex("by_content_id")
        .filter((q2) => q2.or(...postIds.map((id) =>
          q2.eq(q2.field("content_id"), id)
        )))
        .collect(),
      ctx.db.query("Purchases").withIndex("by_content_id")
        .filter((q2) => q2.or(...postIds.map((id) =>
          q2.eq(q2.field("content_id"), id)
        )))
        .collect(),
      userId
        ? ctx.db.query("Likes").withIndex("by_user_content")
          .filter((q2) =>
            q2.and(
              q2.eq(q2.field("user_id"), userId),
              q2.or(...postIds.map((id) =>
                q2.eq(q2.field("content_id"), id)
              ))
            )
          )
          .collect()
        : Promise.resolve([]),
      userId
        ? ctx.db.query("SavedContent").withIndex("by_user_content")
          .filter((q2) =>
            q2.and(
              q2.eq(q2.field("user_id"), userId),
              q2.or(...postIds.map((id) =>
                q2.eq(q2.field("content_id"), id)
              ))
            )
          )
          .collect()
        : Promise.resolve([]),
    ]);

    // Helper function to count occurrences by content_id
    const makeCountMap = (docs: { content_id: string }[]) => {
      return docs.reduce((acc: Record<string, number>, doc) => {
        acc[doc.content_id] = (acc[doc.content_id] || 0) + 1;
        return acc;
      }, {});
    };

    // Convert all counts to numbers and default to 0
    const getLikeCount = (postId: string, likesMap: Record<string, number>) =>
      Number(likesMap[postId]) || 0;

    const hashtagsMap = hashtagsDocs.reduce<Record<string, string[]>>((m, h) => {
      (m[h.content_id] ||= []).push(h.name);
      return m;
    }, {});

    const mentionsMap: Record<string, string[]> = {};
    mentionsDocs.forEach((m) => {
      (mentionsMap[m.content_id] ||= []).push(m.mentioned_user_id);
    });

    const creatorMap = Object.fromEntries(
      creatorsDocs.map((c) => {
        const userInfo = c.user_info || {};
        const account = userInfo.account || {};
        return [
          c.user_id,
          {
            id: c.user_id,
            name: account.displayName,
            username: account.username,
            profileImage: userInfo.profilePhoto,
            coverBanner: userInfo.coverBanner,
            coverBannerType: userInfo.coverBannerType,
            verified: account.is_verified ?? false,
          },
        ];
      })
    );

    const userLikedSet = new Set(userLikes.map((l) => l.content_id));
    const userSavedSet = new Set(userSaves.map((s) => s.content_id));

    const likesMap = makeCountMap(likes);
    const savesMap = makeCountMap(saves);
    const viewsMap = makeCountMap(views);
    const commentMap = makeCountMap(comments);
    const purchasesMap = makeCountMap(purchases);

    const transformed = posts.map((p) => {
      // Transform metadata.media array into the expected format
      const media = p.metadata?.media?.map((m: any) => ({
        mediaUrl: m.media_url || '',
        mediaType: p.content_type,
        mediaDescription: '',
        thumbnailUrl: m.thumbnail_url || '',
        duration: p.metadata?.duration || null,
        resolution: null
      })) || [];

      return {
        id: p.id,
        user: creatorMap[p.creator_id],
        content: p.description,
        media,
        contentType: p.content_type,
        isPaid: p.is_paid,
        isEdited: p.is_edited,
        editedAt: p.edited_at,
        createdAt: p.created_at,
        visibility: p.visibility,
        duration: typeof p.metadata?.duration === 'number' ? p.metadata.duration : null,
        price: typeof p.metadata?.price === 'number' ? p.metadata.price : null,
        likes: getLikeCount(p.id, likesMap),
        liked: userLikedSet.has(p.id),
        views: Number(viewsMap[p.id]) || 0,
        comments: Number(commentMap[p.id]) || 0,
        saves: Number(savesMap[p.id]) || 0,
        saved: userSavedSet.has(p.id),
        purchases: purchasesMap[p.id] ?? 0,
        hashtags: hashtagsMap[p.id] ?? [],
        mentions: (mentionsMap[p.id] || [])
          .map((uid) => creatorMap[uid])
          .filter((mention) => mention !== undefined),
      }
    });

    if (filter === "most-viewed") transformed.sort((a, b) => b.views - a.views);
    if (filter === "most-liked") transformed.sort((a, b) => b.likes - a.likes);
    if (filter === "most-sold") transformed.sort((a, b) => b.purchases - a.purchases);

    return {
      posts: transformed,
      cursor: res.continueCursor,
      isDone: res.isDone,
      pageSize: limit,
    };
  },
});

export const fetchComments = query({
  args: {
    postId: v.string(),
    userId: v.optional(v.string()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 30;
    const cursor = args.cursor ?? null;
    let q = ctx.db
      .query('Comments')
      .filter((q) => q.eq(q.field('content_id'), args.postId));
    if (args.userId) {
      q = q.filter((q) => q.eq(q.field('user_id'), args.userId));
    }
    // Pagination
    const result = await q.order('desc').paginate({ numItems: limit, cursor });
    const comments = result.page;
    // Attach user info
    const userIds = Array.from(new Set(comments.map((c) => c.user_id)));
    const users = await ctx.db
      .query('Accounts')
      .filter((q) => q.or(...userIds.map((id) => q.eq(q.field('user_id'), id))))
      .collect();
    const userMap: Record<string, any> = Object.fromEntries(users.map((u) => [u.user_id, u]));
    return {
      comments: comments.map((c) => ({
        ...c,
        user: userMap[c.user_id] || null,
      })),
      cursor: result.continueCursor,
      isDone: result.isDone,
      total: undefined,
      limit,
    };
  },
});

export const createComment = mutation({
  args: {
    postId: v.string(),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');
    if (!args.content) throw new Error('Comment content is required');

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) throw new Error('Account not found');

    const userId = account.user_id; // Use the custom user_id from Accounts table

    // Insert comment
    const commentId = await ctx.db.insert('Comments', {
      content_id: args.postId,
      user_id: userId,
      comment_text: args.content,
      created_at: new Date().toISOString(),
    });
    // Return comment with user info
    return {
      id: commentId,
      content_id: args.postId,
      user_id: userId,
      comment_text: args.content,
      created_at: new Date().toISOString(),
      user: account,
    };
  },
});

// In-memory cache to prevent duplicate requests within a short time window
const recentLikeOperations = new Map<string, { timestamp: number; result: any }>();

export const toggleLike = mutation({
  args: {
    contentId: v.string(),
    contentType: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) {
      throw new Error('Unauthorized');
    }

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) {
      throw new Error('Account not found');
    }

    const userId = account.user_id;
    const operationKey = `${userId}-${args.contentId}`;
    const now = Date.now();

    // Check for recent duplicate operation (within 2 seconds)
    const recentOperation = recentLikeOperations.get(operationKey);
    if (recentOperation && (now - recentOperation.timestamp) < 2000) {
      return recentOperation.result;
    }

    // Use a more robust query to check for existing like
    const existingLike = await ctx.db
      .query('Likes')
      .filter((q) =>
        q.and(
          q.eq(q.field('content_id'), args.contentId),
          q.eq(q.field('user_id'), userId)
        )
      )
      .first();

    let liked = false;

    if (existingLike) {
      // Unlike: Delete the existing like
      await ctx.db.delete(existingLike._id);
      liked = false;
    } else {
      // Like: Insert new like, but handle potential race condition
      try {
        await ctx.db.insert('Likes', {
          content_id: args.contentId,
          user_id: userId,
          created_at: new Date().toISOString(),
          content_type: args.contentType,
        });
        liked = true;
      } catch (error) {
        // If insert fails due to race condition, check current state
        const recheckLike = await ctx.db
          .query('Likes')
          .filter((q) =>
            q.and(
              q.eq(q.field('content_id'), args.contentId),
              q.eq(q.field('user_id'), userId)
            )
          )
          .first();

        liked = !!recheckLike;
      }
    }

    // Get final like count
    const likeCount = await ctx.db
      .query('Likes')
      .filter((q) => q.eq(q.field('content_id'), args.contentId))
      .collect()
      .then((docs) => docs.length);

    const result = {
      liked,
      likes: likeCount,
    };

    // Cache the result to prevent duplicate operations
    recentLikeOperations.set(operationKey, { timestamp: now, result });

    // Clean up old entries (older than 5 seconds)
    for (const [key, value] of recentLikeOperations.entries()) {
      if (now - value.timestamp > 5000) {
        recentLikeOperations.delete(key);
      }
    }

    return result;
  },
});

export const isPostLiked = query({
  args: {
    contentId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) throw new Error('Account not found');

    const userId = account.user_id; // Use the custom user_id from Accounts table

    const like = await ctx.db
      .query('Likes')
      .filter((q) => q.eq(q.field('content_id'), args.contentId))
      .filter((q) => q.eq(q.field('user_id'), userId))
      .first();

    return { liked: !!like };
  },
});

export const updatePostVisibility = mutation({
  args: {
    postId: v.string(),
    visibility: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');
    const allowed = ['public', 'followers', 'onlyme'];
    if (!allowed.includes(args.visibility)) throw new Error('Invalid visibility option');
    // Find the post
    const post = await ctx.db
      .query('Content')
      .filter((q) => q.eq(q.field('id'), args.postId))
      .filter((q) => q.eq(q.field('creator_id'), user.subject))
      .first();
    if (!post) throw new Error('Post not found or not owned by user');
    // Update visibility
    await ctx.db.patch(post._id, { visibility: args.visibility });
    const updated = await ctx.db.get(post._id);
    return { success: true, post: updated };
  },
});

export const toggleSave = mutation({
  args: {
    contentId: v.string(),
    contentType: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) throw new Error('Account not found');

    const userId = account.user_id; // Use the custom user_id from Accounts table

    // Check if already saved
    const existingSave = await ctx.db
      .query('SavedContent')
      .filter((q) => q.eq(q.field('content_id'), args.contentId))
      .filter((q) => q.eq(q.field('user_id'), userId))
      .first();
    if (existingSave) {
      // Unsave
      await ctx.db.delete(existingSave._id);
    } else {
      // Save
      await ctx.db.insert('SavedContent', {
        id: Date.now() + Math.floor(Math.random() * 10000),
        user_id: userId,
        content_id: args.contentId,
        content_type: args.contentType,
        created_at: new Date().toISOString(),
      });
    }
    const saveCount = await ctx.db
      .query('SavedContent')
      .filter((q) => q.eq(q.field('content_id'), args.contentId))
      .collect()
      .then((docs) => docs.length);
    return {
      saved: !existingSave,
      saves: saveCount,
    };
  },
});

export const isPostSaved = query({
  args: {
    contentId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) throw new Error('Account not found');

    const userId = account.user_id; // Use the custom user_id from Accounts table

    const save = await ctx.db
      .query('SavedContent')
      .filter((q) => q.eq(q.field('content_id'), args.contentId))
      .filter((q) => q.eq(q.field('user_id'), userId))
      .first();
    return { saved: !!save };
  },
});

export const trackContentView = mutation({
  args: {
    contentId: v.string(),
    contentType: v.string(),
    ipAddress: v.string(),
    userAgent: v.string(),
    sessionId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    let userId = null;

    // If user is authenticated, get the custom user_id from Accounts table
    if (user) {
      const account = await findAccountByUserSubject(ctx, user.subject);

      if (account) {
        userId = account.user_id; // Use the custom user_id from Accounts table
      }
    }

    // Check if a view already exists for this content and uniqueKey in the last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    let viewFilter;
    if (userId) {
      viewFilter = (q: any) => q.and(
        q.eq(q.field('content_id'), args.contentId),
        q.eq(q.field('content_type'), args.contentType),
        q.eq(q.field('user_id'), userId),
        q.gte(q.field('created_at'), oneHourAgo)
      );
    } else if (args.sessionId) {
      viewFilter = (q: any) => q.and(
        q.eq(q.field('content_id'), args.contentId),
        q.eq(q.field('content_type'), args.contentType),
        q.eq(q.field('session_id'), args.sessionId),
        q.gte(q.field('created_at'), oneHourAgo)
      );
    } else {
      viewFilter = (q: any) => q.and(
        q.eq(q.field('content_id'), args.contentId),
        q.eq(q.field('content_type'), args.contentType),
        q.eq(q.field('ip_address'), args.ipAddress),
        q.gte(q.field('created_at'), oneHourAgo)
      );
    }

    const existingView = await ctx.db
      .query('ContentViews')
      .filter(viewFilter)
      .first();

    if (!existingView) {
      // Only insert if not already viewed recently
      await ctx.db.insert('ContentViews', {
        content_id: args.contentId,
        user_id: userId,
        ip_address: args.ipAddress,
        user_agent: args.userAgent,
        session_id: args.sessionId ?? null,
        content_type: args.contentType,
        id: crypto.randomUUID(),
        viewed_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
      });
    }

    const views = await ctx.db
      .query('ContentViews')
      .filter((q) => q.eq(q.field('content_id'), args.contentId))
      .collect()
      .then((docs) => docs.length);

    return { success: true, views };
  },
});

export const getContentViewCount = query({
  args: {
    contentId: v.string(),
  },
  handler: async (ctx, args) => {
    const views = await ctx.db
      .query('ContentViews')
      .filter((q) => q.eq(q.field('content_id'), args.contentId))
      .collect()
      .then((docs) => docs.length);
    return { views };
  },
});

export const fetchPostById = query({
  args: {
    postId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const account = await findAccountByUserSubject(ctx, user.subject);
    if (!account) throw new Error('Account not found');

    const userId = account.user_id; // Use the custom user_id from Accounts table

    // Fetch the post
    const post = await ctx.db
      .query('Content')
      .filter((q) => q.eq(q.field('id'), args.postId))
      .first();
    if (!post) throw new Error('Post not found');

    // Fetch creator info
    const creator = await ctx.db
      .query('Accounts')
      .filter((q) => q.eq(q.field('user_id'), post.creator_id))
      .first();
    const ui = creator?.user_info || {};
    const creatorAccount = ui.account || {};

    const [likes, comments] = await Promise.all([
      ctx.db
        .query('Likes')
        .filter((q) => q.eq(q.field('content_id'), post.id))
        .collect(),
      ctx.db
        .query('Comments')
        .filter((q) => q.eq(q.field('content_id'), post.id))
        .collect(),
    ]);
    const likesCount = likes.length;
    const liked = likes.some((l) => l.user_id === userId);

    const saved = await ctx.db
      .query('SavedContent')
      .filter((q) => q.eq(q.field('content_id'), post.id))
      .filter((q) => q.eq(q.field('user_id'), userId))
      .first();

    // Transform for frontend
    return {
      id: post.id,
      content: post.description,
      rawState: post.metadata?.raw_state,
      mediaUrl: post.metadata?.media_url,
      mediaType: post.content_type,
      mediaDescription: post.metadata?.media_description,
      thumbnailUrl: post.metadata?.thumbnail_url,
      isPaid: post.is_paid,
      createdAt: post.created_at,
      isEdited: !!post.is_edited,
      editedAt: post.edited_at ?? null,
      user: {
        id: creator?.user_id ?? '',
        username: creatorAccount.username ?? '',
        name: creatorAccount.displayName ?? 'Anonymous',
        profileImage: ui.profilePhoto ?? '/images/user/default-avatar.webp',
        accountType: creator?.account_type,
      },
      likes: likesCount,
      comments: comments.length,
      liked,
      saved: !!saved,
    };
  },
});

export const getContentCounts = query({
  args: { profileUserId: v.string() },
  handler: async ({ db }, { profileUserId }) => {
    const panels = ["photos", "videos", "clips", "audio", "gifs"];
    const counts: Record<string, number> = {};

    // Fetch all posts for this user
    const allPosts = await db
      .query("Content")
      .withIndex("by_creator", (q) => q.eq("creator_id", profileUserId))
      .collect();

    // Count by panel
    for (const panel of panels) {
      let matchFn: (post: any) => boolean;
      if (panel === "photos") {
        matchFn = (p) => p.content_type?.startsWith("image/") && p.content_type !== "image/gif";
      } else if (panel === "videos") {
        matchFn = (p) => p.content_type?.startsWith("video/");
      } else if (panel === "clips") {
        matchFn = (p) => p.content_type?.startsWith("video/") && (p.metadata?.duration ?? 0) < 60;
      } else if (panel === "audio") {
        matchFn = (p) => p.content_type === "audio/mpeg";
      } else if (panel === "gifs") {
        matchFn = (p) => p.content_type === "image/gif";
      } else {
        matchFn = () => false;
      }
      const filtered = allPosts.filter(matchFn);
      counts[`${panel}Count`] = filtered.length;
      counts[`${panel}Likes`] = filtered.reduce((sum, post) => sum + (post.likes || 0), 0);
    }

    counts["allCount"] = allPosts.length;

    return counts;
  },
});