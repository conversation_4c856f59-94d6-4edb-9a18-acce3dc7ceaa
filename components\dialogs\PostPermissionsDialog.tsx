'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';
import { Plus, X, ChevronDown, Trash2, Clock, DollarSign, Users, Lock } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { elFormatter } from '@/public/main';

// Media permission types
type MediaPermissionType = 'price' | 'following' | 'subscribed' | 'limited-time';

// Timeline permission types
type TimelinePermissionType = 'public' | 'followers' | 'subscribers';

interface PermissionRule {
  id: string;
  conditions: {
    type: MediaPermissionType;
    price?: string;
    tierName?: string;
    duration?: string;
    subscriptionDuration?: string;
  }[];
}

interface PostPermissionsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  initialSettings: {
    timelinePermission: TimelinePermissionType;
    mediaPermissions: PermissionRule[];
    price?: string;
  };
  onSave: (settings: {
    timelinePermission: TimelinePermissionType;
    mediaPermissions: PermissionRule[];
    price?: string;
  }) => void;
}

// Add this memoized component at the top level
const PermissionRuleItem = React.memo(({ rule, onUpdateCondition, onAddCondition, onRemoveCondition, onRemoveRule, totalRules }: {
  rule: PermissionRule;
  onUpdateCondition: (id: string, conditionIndex: number, type: MediaPermissionType, tierName?: string, price?: string, duration?: string, subscriptionDuration?: string) => void;
  onAddCondition: (ruleId: string, type: MediaPermissionType, tierName?: string, price?: string, duration?: string, subscriptionDuration?: string) => void;
  onRemoveCondition: (ruleId: string, conditionIndex: number) => void;
  onRemoveRule: (id: string) => void;
  totalRules: number;
}) => {
  const [priceWarning, setPriceWarning] = useState<string | null>(null);
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Define stable callbacks for adding conditions
  const handleAddPrice = useCallback(() => {
    onAddCondition(rule.id, 'price', undefined, '0.99');
  }, [rule.id, onAddCondition]);

  const handleAddFollowing = useCallback(() => {
    onAddCondition(rule.id, 'following');
  }, [rule.id, onAddCondition]);

  const handleAddSubscribed = useCallback(() => {
    onAddCondition(rule.id, 'subscribed', undefined, undefined, undefined, undefined);
  }, [rule.id, onAddCondition]);

  const handleAddSubscribedAllTiers = useCallback(() => {
    onAddCondition(rule.id, 'subscribed', 'All Tiers');
  }, [rule.id, onAddCondition]);

  const handleAddLimitedTime = useCallback(() => {
    onAddCondition(rule.id, 'limited-time', undefined, undefined, '24 hours');
  }, [rule.id, onAddCondition]);

  // Define stable callbacks for updating conditions
  const handleUpdateSubscribedAllTiers = useCallback((index: number) => {
    onUpdateCondition(rule.id, index, 'subscribed', 'All Tiers');
  }, [rule.id, onUpdateCondition]);

  const handleUpdateSubscribedBronze = useCallback((index: number) => {
    onUpdateCondition(rule.id, index, 'subscribed', 'Bronze');
  }, [rule.id, onUpdateCondition]);

  const handleUpdateSubscribedSilver = useCallback((index: number) => {
    onUpdateCondition(rule.id, index, 'subscribed', 'Silver');
  }, [rule.id, onUpdateCondition]);

  const handleUpdateSubscribedGold = useCallback((index: number) => {
    onUpdateCondition(rule.id, index, 'subscribed', 'Gold');
  }, [rule.id, onUpdateCondition]);

  const handleUpdateSubscribedVIP = useCallback((index: number) => {
    onUpdateCondition(rule.id, index, 'subscribed', 'VIP');
  }, [rule.id, onUpdateCondition]);

  const handleUpdateSubscriptionDuration = useCallback((index: number, duration: string) => {
    onUpdateCondition(rule.id, index, 'subscribed', undefined, undefined, undefined, duration);
  }, [rule.id, onUpdateCondition]);

  return (
    <div className="space-y-2">
      <div className="bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-zinc-700 p-3">
        <div className="flex justify-between items-center">
          {/* Container for existing conditions/chips and new permission dropdown */}
          <div className="flex items-center flex-wrap gap-2">
            {rule.conditions.map((condition, index) => (
              <React.Fragment key={index}>
                {/* Chip for each condition */}
                <span className="flex items-center gap-1 bg-gray-100 dark:bg-zinc-700 rounded-full px-2 py-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                  {condition.type === 'price' && `Price: ${elFormatter(parseFloat(condition.price || '0'), 2)}`}
                  {condition.type === 'following' && 'Following'}
                  {condition.type === 'subscribed' && `Subscribed ${condition.tierName ? `(${condition.tierName})` : condition.subscriptionDuration ? `(${condition.subscriptionDuration})` : ''}`}
                  {condition.type === 'limited-time' && `Limited Time ${condition.duration ? `(${condition.duration})` : ''}`}
                  <X className="h-3 w-3 cursor-pointer ml-1" onClick={() => onRemoveCondition(rule.id, index)} />
                </span>
                {/* Add '&' if there are more conditions after this one, and it's not the last one */}
                {index < rule.conditions.length - 1 && <span className="text-gray-500 text-sm font-bold mx-1">&</span>}
              </React.Fragment>
            ))}

            {rule.conditions.length < 2 && (
              <DropdownMenu modal={true}>
                <DropdownMenuTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 bg-gray-200 dark:bg-zinc-600 text-gray-700 dark:text-gray-300 flex items-center gap-2"
                  >
                    New permission <ChevronDown className="ml-1 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="z-[1010]">
                  {!rule.conditions.some(c => c.type === 'price') && (
                    <DropdownMenuItem onSelect={handleAddPrice}>
                      <DollarSign className="h-4 w-4 mr-2" /> Add Price
                    </DropdownMenuItem>
                  )}
                  {!rule.conditions.some(c => c.type === 'following') && (
                    <DropdownMenuItem onSelect={handleAddFollowing}>
                      <Users className="h-4 w-4 mr-2" /> Add Following
                    </DropdownMenuItem>
                  )}
                  {!rule.conditions.some(c => c.type === 'subscribed') && (
                    <DropdownMenuItem onSelect={handleAddSubscribedAllTiers}>
                      <Lock className="h-4 w-4 mr-2" /> Add Subscribed (All Tiers)
                    </DropdownMenuItem>
                  )}
                  {!rule.conditions.some(c => c.type === 'subscribed') && (
                    <DropdownMenuItem onSelect={handleAddSubscribed}>
                      <Lock className="h-4 w-4 mr-2" /> Add Subscribed (More Options)
                    </DropdownMenuItem>
                  )}
                  {!rule.conditions.some(c => c.type === 'limited-time') && (
                    <DropdownMenuItem onSelect={handleAddLimitedTime}>
                      <Clock className="h-4 w-4 mr-2" /> Add Limited Time
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>

          {/* Button to remove the entire rule */}
          {totalRules > 1 && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onRemoveRule(rule.id)}
              className="h-8 w-8"
            >
              <Trash2 className="h-4 w-4 text-red-500" />
            </Button>
          )}
        </div>

        <div className="flex items-center justify-start gap-3">
          {/* Inputs for each condition type. Iterate over rule.conditions here */}
          {rule.conditions.map((condition, index) => (
            <React.Fragment key={`condition-${index}`}>
              {condition.type === 'price' && (
                <>
                  <div className="mt-3 w-32 relative">
                    <label className="text-xs text-gray-500 mb-1 block">Set Price</label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                      <input
                        type="text"
                        value={condition.price || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          // Allow empty string, digits, one decimal point, and partial decimal numbers
                          if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
                            const num = parseFloat(value);
                            if (value === '' || (!isNaN(num) && num <= 1000)) {
                              onUpdateCondition(rule.id, index, 'price', undefined, value);
                              setPriceWarning(null);
                            } else if (!isNaN(num) && num > 1000) {
                              onUpdateCondition(rule.id, index, 'price', undefined, value);
                              setPriceWarning('Maximum price is $1,000');

                              // Clear any existing timeout
                              if (warningTimeoutRef.current) {
                                clearTimeout(warningTimeoutRef.current);
                              }

                              // Set new timeout to correct the value
                              warningTimeoutRef.current = setTimeout(() => {
                                onUpdateCondition(rule.id, index, 'price', undefined, '1000.00');
                                setPriceWarning(null);
                              }, 2500);
                            }
                          }
                        }}
                        onBlur={(e) => {
                          const value = e.target.value;
                          if (value) {
                            const num = parseFloat(value);
                            if (!isNaN(num)) {
                              const clampedNum = Math.min(num, 1000);
                              onUpdateCondition(rule.id, index, 'price', undefined, clampedNum.toFixed(2));
                              setPriceWarning(null);
                            } else {
                              onUpdateCondition(rule.id, index, 'price', undefined, '0.00');
                            }
                          } else {
                            onUpdateCondition(rule.id, index, 'price', undefined, '0.00');
                          }
                        }}
                        className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md pl-7 pr-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                  {priceWarning && (
                    <p className="text-xs text-red-500 mt-8">{priceWarning}</p>
                  )}
                </>
              )}

              {condition.type === 'subscribed' && !condition.subscriptionDuration && (
                <div className="mt-3 w-32">
                  <label className="text-xs text-gray-500 mb-1 block">Subscription Tier</label>
                  <DropdownMenu modal={true}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-between flex h-10 border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
                      >
                        {condition.tierName || 'Select Tier'} <ChevronDown className="h-4 w-4 ml-2" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full z-[1010]">
                      <DropdownMenuItem onSelect={() => handleUpdateSubscribedAllTiers(index)}>
                        All Tiers
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => handleUpdateSubscribedBronze(index)}>
                        Bronze
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => handleUpdateSubscribedSilver(index)}>
                        Silver
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => handleUpdateSubscribedGold(index)}>
                        Gold
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => handleUpdateSubscribedVIP(index)}>
                        VIP
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}

              {condition.type === 'subscribed' && condition.subscriptionDuration && (
                <div className="mt-3 w-32">
                  <label className="text-xs text-gray-500 mb-1 block">Subscription Duration</label>
                  <DropdownMenu modal={true}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-between"
                      >
                        {condition.subscriptionDuration || 'Select Duration'} <ChevronDown className="h-4 w-4 ml-2" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full z-[1010]">
                      <DropdownMenuItem onSelect={() => handleUpdateSubscriptionDuration(index, '1 week')}>
                        1 week
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => handleUpdateSubscriptionDuration(index, '1 month')}>
                        1 month
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => handleUpdateSubscriptionDuration(index, '3 months')}>
                        3 months
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => handleUpdateSubscriptionDuration(index, '6 months')}>
                        6 months
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => handleUpdateSubscriptionDuration(index, '1 year')}>
                        1 year
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
});

// Add this at the top level, outside the component
let persistedPermissions: {
  timelinePermission: TimelinePermissionType;
  mediaPermissions: PermissionRule[];
  price?: string;
} | null = null;

export const clearPersistedPermissions = () => {
  persistedPermissions = null;
};

const PostPermissionsDialog = ({ isOpen, onClose, initialSettings, onSave }: PostPermissionsDialogProps) => {
  //console.log('PostPermissionsDialog received initialSettings:', initialSettings);

  const [timelinePermission, setTimelinePermission] = useState<TimelinePermissionType>(
    initialSettings.timelinePermission // Using initialSettings directly here
  );

  const [mediaPermissions, setMediaPermissions] = useState<PermissionRule[]>(() => {
    if (persistedPermissions?.mediaPermissions?.length) {
      return persistedPermissions.mediaPermissions;
    }
    if (initialSettings.mediaPermissions?.length) {
      return initialSettings.mediaPermissions.map(permission => ({
        id: permission.id || crypto.randomUUID(),
        conditions: permission.conditions.map(condition => ({
          type: condition.type as MediaPermissionType,
          price: condition.price,
          tierName: condition.tierName,
          duration: condition.duration,
          subscriptionDuration: condition.subscriptionDuration
        }))
      }));
    } else {
      return [
        {
          id: crypto.randomUUID(),
          conditions: [{ type: 'following' as MediaPermissionType }]
        }
      ];
    }
  });

  // When dialog closes, reset to initial settings (or persisted if available, though we clear it on save/cancel)
  useEffect(() => {
    if (!isOpen) {
      //console.log('Dialog closing. Resetting timelinePermission to:', initialSettings.timelinePermission);
      setTimelinePermission(initialSettings.timelinePermission);
      setMediaPermissions(() => {
        if (initialSettings.mediaPermissions?.length) {
          return initialSettings.mediaPermissions.map(permission => ({
            id: permission.id || crypto.randomUUID(),
            conditions: permission.conditions.map(condition => ({
              type: condition.type as MediaPermissionType,
              price: condition.price,
              tierName: condition.tierName,
              duration: condition.duration,
              subscriptionDuration: condition.subscriptionDuration
            }))
          }));
        } else {
          return [
            {
              id: crypto.randomUUID(),
              conditions: [{ type: 'following' as MediaPermissionType }]
            }
          ];
        }
      });
    }
  }, [isOpen, initialSettings]);

  const initializedRef = useRef(false);

  useEffect(() => {
    if (isOpen && !initializedRef.current) {
      initializedRef.current = true;
    } else if (!isOpen) {
      initializedRef.current = false;
    }
  }, [isOpen]);

  const addMediaPermissionRule = useCallback(() => {
    const newRule: PermissionRule = {
      id: Date.now().toString(),
      conditions: [{ type: 'following' }]
    };
    setMediaPermissions(prevRules => [...prevRules, newRule]);
  }, []);

  const removeMediaPermissionRule = useCallback((id: string) => {
    setMediaPermissions(prevRules => prevRules.filter(rule => rule.id !== id));
  }, []);

  const updateMediaPermissionRule = useCallback((
    ruleId: string,
    conditionIndex: number,
    type: MediaPermissionType,
    tierName?: string,
    price?: string,
    duration?: string,
    subscriptionDuration?: string
  ) => {
    setMediaPermissions(prevRules =>
      prevRules.map(rule => {
        if (rule.id === ruleId) {
          const newConditions = rule.conditions.map((condition, i) => {
            if (i === conditionIndex) {
              const newCondition = { type, tierName, price, duration, subscriptionDuration };
              // Perform a shallow comparison to avoid unnecessary object recreation
              if (
                condition.type !== newCondition.type ||
                condition.tierName !== newCondition.tierName ||
                condition.price !== newCondition.price ||
                condition.duration !== newCondition.duration ||
                condition.subscriptionDuration !== newCondition.subscriptionDuration
              ) {
                return newCondition;
              }
              return condition; // Return original object if no change
            }
            return condition;
          });
          // Only create a new rule object if the conditions array reference changed
          if (newConditions !== rule.conditions) {
            return { ...rule, conditions: newConditions };
          }
          return rule; // Return original rule object if no change in conditions
        }
        return rule;
      })
    );
  }, []);

  const addConditionToRule = useCallback((
    ruleId: string,
    type: MediaPermissionType,
    tierName?: string,
    price?: string,
    duration?: string,
    subscriptionDuration?: string
  ) => {
    setMediaPermissions(prevRules =>
      prevRules.map(rule =>
        rule.id === ruleId
          ? {
            ...rule,
            conditions: [...rule.conditions, { type, tierName, price, duration, subscriptionDuration }]
          }
          : rule
      )
    );
  }, []);

  const removeConditionFromRule = useCallback((
    ruleId: string,
    conditionIndex: number
  ) => {
    setMediaPermissions(prevRules =>
      prevRules.map(rule =>
        rule.id === ruleId
          ? {
            ...rule,
            conditions: rule.conditions.filter((_, i) => i !== conditionIndex)
          }
          : rule
      )
    );
  }, []);

  const saveAsPreset = useCallback(() => {
    toast.success('Permission preset saved successfully');
  }, []);

  const loadPreset = useCallback(() => {
    toast.info('Permission preset loaded');
  }, []);

  // Update persisted permissions when they change (only when dialog is open)
  useEffect(() => {
    if (isOpen) {
      persistedPermissions = {
        timelinePermission,
        mediaPermissions,
        price: initialSettings.price
      };
    }
  }, [timelinePermission, mediaPermissions, isOpen, initialSettings.price]);

  const handleSave = useCallback(() => {
    onSave({
      timelinePermission,
      mediaPermissions,
    });
    persistedPermissions = null; // Clear persisted permissions after saving
    onClose();
  }, [onSave, timelinePermission, mediaPermissions, onClose]);

  const handleCancel = useCallback(() => {
    persistedPermissions = null; // Clear persisted permissions on cancel
    onClose();
  }, [onClose]);

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={handleCancel}
      size="2xl"
      title="Post Permissions"
      footer={
        <div className="flex justify-between gap-4 items-center w-full">
          <Button
            onClick={handleSave}
            className="bg-turquoise hover:bg-turquoise-hover text-white w-full"
          >
            Save Settings
          </Button>
          <Button
            onClick={handleCancel}
            variant="outline"
            className="border-gray-200 dark:border-zinc-700 w-full"
          >
            Cancel
          </Button>
        </div>
      }
    >
      <div className="w-full p-4">
        <Tabs defaultValue="timeline" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4 bg-gray-200 dark:bg-zinc-600">
            <TabsTrigger value="timeline" className="text-sm font-medium">
              Timeline
            </TabsTrigger>
            <TabsTrigger value="media" className="text-sm font-medium">
              Media
            </TabsTrigger>
          </TabsList>
          <TabsContent value="timeline" className="space-y-4">
            <div className="bg-gray-50 dark:bg-zinc-700 p-4 rounded-lg">
              <h3 className="text-sm font-medium mb-4">Who can see this post?</h3>
              <RadioGroup
                value={timelinePermission}
                onValueChange={value => setTimelinePermission(value as TimelinePermissionType)}
                className="space-y-3"
              >
                <div className="flex items-center gap-2">
                  <RadioGroupItem value="public" id="public" />
                  <Label htmlFor="public">Public</Label>
                </div>
                <div className="flex items-center gap-2">
                  <RadioGroupItem value="followers" id="followers" />
                  <Label htmlFor="followers">Followers Only</Label>
                </div>
                <div className="flex items-center gap-2">
                  <RadioGroupItem value="subscribers" id="subscribers" />
                  <Label htmlFor="subscribers">Subscribers Only</Label>
                </div>
              </RadioGroup>
            </div>
          </TabsContent>
          <TabsContent value="media" className="space-y-4">
            <div className="bg-gray-50 dark:bg-zinc-700 p-4 rounded-lg">
              <h3 className="text-sm font-medium mb-4">Who can unlock media in this post?</h3>
              <div className="space-y-4">
                {mediaPermissions.map((rule, index) => (
                  <React.Fragment key={rule.id}>
                    <PermissionRuleItem
                      key={rule.id}
                      rule={rule}
                      onUpdateCondition={updateMediaPermissionRule}
                      onAddCondition={addConditionToRule}
                      onRemoveCondition={removeConditionFromRule}
                      onRemoveRule={removeMediaPermissionRule}
                      totalRules={mediaPermissions.length}
                    />
                    {index < mediaPermissions.length - 1 && (
                      <div className="flex items-center justify-center my-3">
                        <div className="bg-white dark:bg-zinc-700 rounded-full px-4 py-1">
                          <span className="text-sm font-medium text-blue-500">OR</span>
                        </div>
                      </div>
                    )}
                  </React.Fragment>
                ))}
                <Button
                  variant="ghost"
                  onClick={addMediaPermissionRule}
                  className="w-full mt-2 border border-dashed border-gray-300 dark:border-zinc-600 rounded-md py-3 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-zinc-700/50"
                >
                  <Plus className="h-4 w-4 mr-2" /> Add Another Option
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AnimatedModal>
  );
};
export default PostPermissionsDialog;