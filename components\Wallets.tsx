import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useWalletContext } from '@/context/Wallet/WalletContext';
//import { useSolWalletContext } from '@/context/Wallet/SolWalletContext';
import { useWalletList as cardanoWalletList } from '@meshsdk/react';
import { isInstalled } from '@gemwallet/api';
import { startsWithVowel } from '@/public/main';
import { useRouter } from 'next/navigation';
import blockchains from '@/data/blockchains.json';
import { useTheme } from 'next-themes';
import ConWalletItem from './WalletItem';

const Wallets = ({ authType, userType, onClose }: { authType: string, userType: string, onClose: () => void }) => {
    const { theme }: { theme: string } = useTheme() as any;

    const {
        cardanoLogin,
        cardanoSignup,
        bitcoinLogin,
        bitcoinSignup,
        vechainLogin,
        vechainSignup,
        seiLogin,
        seiSignup,
        evmLogin,
        evmSignup,
        algorandLogin,
        algorandSignup,
        xrpLogin,
        xrpSignup,
        polkadotLogin,
        polkadotSignup,
        connectors,
        availableWallets,
        algoWallets,
    }: {
        cardanoLogin: (walletName: string, userType: string) => void;
        cardanoSignup: (walletName: string, userType: string) => void;
        bitcoinLogin: (walletName: string, walletProvider: any, userType: string) => void;
        bitcoinSignup: (walletName: string, walletProvider: any, userType: string) => void;
        evmLogin: (walletName: string, walletProvider: any, userType: string) => void;
        evmSignup: (walletName: string, walletProvider: any, userType: string) => void;
        vechainLogin: (walletName: string, userType: string) => void;
        vechainSignup: (walletName: string, userType: string) => void;
        seiLogin: (walletName: string, userType: string) => void;
        seiSignup: (walletName: string, userType: string) => void;
        algorandLogin: (walletName: string, userType: string) => void;
        algorandSignup: (walletName: string, userType: string) => void;
        xrpLogin: (walletName: string, userType: string) => void;
        xrpSignup: (walletName: string, userType: string) => void;
        polkadotLogin: (walletName: string, userType: string) => void;
        polkadotSignup: (walletName: string, userType: string) => void;
        connectors: any;
        availableWallets: any[];
        algoWallets: any[];
    } = useWalletContext() as any;

    /* const {
        solanaLogin,
        solanaSignup,
        wallets: solanaWallets
    }: {
        solanaLogin: (walletName: string, accountType: string) => void;
        solanaSignup: (walletName: string, userType: string) => void;
        wallets: any[];
    } = useSolWalletContext() as any; */

    const {
        isConnecting,
        isSolConnecting
    }: {
        isConnecting: boolean;
        isSolConnecting: boolean;
    } = useSelector((state: any) => state.wallet);

    const [selectedBlockchain, setSelectedBlockchain] = useState<any | null>(null);
    const [bitcoinWallets, setBitcoinWallets] = useState<any[]>([]);
    const [xrpWallets, setXRPWallets] = useState<any[]>([]);
    const [polkadotWallets, setPolkadotWallets] = useState<any[]>([]);
    const [seiWallets, setSeiWallets] = useState<any[]>([]);
    const [connectingWallet, setConnectingWallet] = useState<string | null>(null);

    const dispatch = useDispatch();

    const router = useRouter();
    const cardanoWallets = cardanoWalletList();

    const getIsConnecting = (blockchainValue: string) => {
        return blockchainValue.toLowerCase() === "solana"
            ? isSolConnecting
            : isConnecting;
    };

    const handleBlockchainSelect = (blockchain: string) => {
        setSelectedBlockchain(blockchain);
        // Add smooth scroll to top functionality
        const container = document.querySelector('.overflow-auto');
        if (container) {
            container.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    };

    const handleBack = () => {
        setSelectedBlockchain(null);
    };

    const connectWallet = async (walletName: string, walletProvider?: any) => {
        if (!selectedBlockchain) return;

    setConnectingWallet(walletName);

        const actions = {
            bitcoin: { login: bitcoinLogin, signup: bitcoinSignup },
            ethereum: { login: evmLogin, signup: evmSignup },
            polygon: { login: evmLogin, signup: evmSignup },
            "bnb-smart-chain": { login: evmLogin, signup: evmSignup },
            base: { login: evmLogin, signup: evmSignup },
            linea: { login: evmLogin, signup: evmSignup },
            blast: { login: evmLogin, signup: evmSignup },
            optimism: { login: evmLogin, signup: evmSignup },
            avalanche: { login: evmLogin, signup: evmSignup },
            immutable: { login: evmLogin, signup: evmSignup },
            eos: { login: evmLogin, signup: evmSignup },
            vechain: { login: vechainLogin, signup: vechainSignup },
            arbitrum: { login: evmLogin, signup: evmSignup },
            cronos: { login: evmLogin, signup: evmSignup },
            //solana: { login: solanaLogin, signup: solanaSignup },
            algorand: { login: algorandLogin, signup: algorandSignup },
            polkadot: { login: polkadotLogin, signup: polkadotSignup },
            xrp: { login: xrpLogin, signup: xrpSignup },
            sei: { login: seiLogin, signup: seiSignup },
            cardano: { login: cardanoLogin, signup: cardanoSignup },
        };

        const action = actions[selectedBlockchain.value as keyof typeof actions];
        const chainName =
            selectedBlockchain.value.startsWith("evm-")
                ? selectedBlockchain.label
                : undefined;

        try {
            const { success } = await (authType === "login"
                ? action.login(walletName, chainName || userType, walletProvider)
                : action.signup(walletName, chainName || userType, walletProvider));
            if (success) {
                onClose();
            }
        } catch (error) {
            console.error("Wallet connection failed:", error);
        } finally {
            setConnectingWallet(null);
        }
    };

    const getInstalledWalletNames = (blockchainValue: string) => {
        switch (blockchainValue) {
            /* case 'solana':
                return solanaWallets.map(wallet => wallet.adapter.name); */
            case 'cardano':
                return cardanoWallets.map(wallet => wallet.id);
            case 'ethereum':
            case 'polygon':
            case 'bnb-smart-chain':
            case 'base':
            case 'linea':
            case 'blast':
            case 'arbitrum':
            case 'immutable':
            case 'optimism':
            case 'avalanche':
            case 'cronos':
            case 'eos':
                return connectors.map((wallet: any) => wallet.name);
            case 'algorand':
                return algoWallets.map((wallet: any) => wallet.metadata.name);
            case 'vechain':
                return [...connectors.map((wallet: any) => wallet.name), ...availableWallets];
            case 'sei':
                return seiWallets;
            case 'bitcoin':
                return bitcoinWallets;
            case 'xrp':
                return xrpWallets;
            case 'polkadot':
                return polkadotWallets;
            default:
                return [];
        }
    };

    /* XRP Wallets */
    useEffect(() => {
        const walletNames = [];
        if (window?.xrpl?.isCrossmark) {
            walletNames.push('Crossmark');
        }
        isInstalled().then((response) => {
            if (response.result?.isInstalled) walletNames.push('Gem');
        });
        // walletNames.push('Torus');
        setXRPWallets(walletNames);
    }, []);

    /* Polkadot Wallets */
    useEffect(() => {
        const walletNames = [];
        if (window?.injectedWeb3) {
            const injectedWallets = Object.keys(window.injectedWeb3);
            walletNames.push(...injectedWallets);
        }
        setPolkadotWallets(walletNames);
    }, []);

    /* Sei Wallets */
    useEffect(() => {
        const walletNames = [];
        if (window?.keplr) walletNames.push('Keplr');
        if (window?.leap) walletNames.push('Leap');
        if (window?.compass) walletNames.push('Compass');
        if (window?.fin) walletNames.push('Fin');

        setSeiWallets(walletNames);
    }, []);

    /* Bitcoin Wallets */
    useEffect(() => {
        if (window?.btc_providers) {
            const walletNames = window.btc_providers.map((provider) => provider.name);
            // Check if Phantom Bitcoin is installed
            if (window.phantom && window.phantom.bitcoin && window?.phantom?.bitcoin?.isPhantom) {
                walletNames.push('Phantom');
            }

            if (window.unisat) {
                walletNames.push('Unisat');
            }

            if (typeof window.okxwallet !== 'undefined') {
                walletNames.push('OKX');
            }

            setBitcoinWallets(walletNames);
        }
    }, []);

    return (
        <div className="w-full h-full bg-transparent py-1">
            <div className="container mx-auto">
                <div className="flex items-center justify-between w-full mb-6">
                    {selectedBlockchain && (
                        <button
                            className="flex items-center gap-2 w-[33%] text-[#121212] dark:text-white transition"
                            onClick={handleBack}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 512 512"
                                className="w-5 h-5 fill-current"
                            >
                                <path d="M512 256a256 256 0 10-512 0 256 256 0 10512 0zM231 127c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9l-71 71 182.1.1c13.3 0 24 10.7 24 24s-10.7 24-24 24H193.9l71 71c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0L119 273c-9.4-9.4-9.4-24.6 0-33.9L231 127z" />
                            </svg>
                            Select a Blockchain
                        </button>
                    )}
                    <h2 className="w-[33%] text-md text-center font-bold text-[#121212] dark:text-white mx-auto">
                        {selectedBlockchain
                            ? `Select ${startsWithVowel(selectedBlockchain.label) ? 'an' : 'a'} ${selectedBlockchain.label} wallet`
                            : 'Connect Wallet'}
                    </h2>
                    {/* Placeholder for spacing */}
                    {selectedBlockchain && <div className="w-[33%]" />}
                </div>

                <div className="grid grid-cols-1 py-2 px-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-2 overflow-auto max-h-72 rounded-lg border-2 border-solid border-white/20">
                    {selectedBlockchain ? (
                        selectedBlockchain.supported_wallets.length > 0 ? (
                            selectedBlockchain.supported_wallets.map((wallet: any, index: number) => {
                                const installedWalletNames = getInstalledWalletNames(selectedBlockchain.value);

                                return (
                                    <ConWalletItem
                                        key={index}
                                        image={wallet.icon}
                                        handleWalletSelection={connectWallet}
                                        handleBlockchainSelection={handleBlockchainSelect}
                                        blockchain={selectedBlockchain.value}
                                        isBlockchain={false}
                                        walletName={wallet.id}
                                        walletProvider={wallet.provider}
                                        isClickable={installedWalletNames.includes(wallet.id)}
                                        isConnecting={connectingWallet == wallet.id}
                                        isWalletConnecting={getIsConnecting(selectedBlockchain.value)}
                                        title={wallet.name}
                                        desc={`Connect your ${wallet.name} wallet.`}
                                    />
                                );
                            })
                        ) : (
                            <div className="col-span-full text-center text-white text-md">
                                <span>
                                    There are currently no supported wallets available for {selectedBlockchain.label}. Please check back later.
                                </span>
                            </div>
                        )
                    ) : (
                        blockchains.map((blockchain, index) => (
                            <ConWalletItem
                                key={index}
                                image={theme === 'dark' ? blockchain.icon_dark : blockchain.icon_light}
                                handleBlockchainSelection={handleBlockchainSelect}
                                handleWalletSelection={connectWallet}
                                isBlockchain={true}
                                walletName='blockchain'
                                isWalletConnecting={false}
                                blockchain={blockchain}
                                isClickable={true}
                                isConnecting={false}
                                title={blockchain.label}
                                desc={`Click here to select ${startsWithVowel(blockchain.label) ? 'an' : 'a'} ${blockchain.label} wallet to log in with.`}
                            />
                        ))
                    )}
                </div>
            </div>
        </div>
    );
};
export default Wallets;