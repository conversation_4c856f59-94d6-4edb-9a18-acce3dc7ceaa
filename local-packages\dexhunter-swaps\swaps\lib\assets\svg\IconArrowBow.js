import { jsx as L } from "react/jsx-runtime";
import { memo as e } from "react";
const l = (o) => /* @__PURE__ */ L(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...o,
    children: /* @__PURE__ */ L(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M10.4068 1.9769L12.8025 1.19669L12.0223 3.5924L11.5681 3.1382L11.0952 3.61113C12.2983 4.94847 13.0561 6.56134 13.2427 8.20146C13.4509 10.032 12.9273 11.6785 11.7684 12.8374L11.5367 13.0691L10.8296 12.362C10.8296 12.362 11.2643 11.895 11.3415 11.7548L5.27768 9.81292L5.65939 10.9325L3.49223 13.0996L2.83299 11.1662L0.899508 10.5069L3.06665 8.33979L4.18622 8.7215L2.24435 2.65771C2.10418 2.73485 1.63718 3.1696 1.63718 3.1696L0.930079 2.4625L1.16176 2.23082C2.32072 1.07186 3.96712 0.548292 5.79769 0.756541C7.4378 0.943126 9.05068 1.70083 10.388 2.90403L10.861 2.4311L10.4068 1.9769ZM3.90972 11.2679L4.52273 10.6549L4.22307 9.77608L3.34422 9.47643L2.73121 10.0894L3.61007 10.3891L3.90972 11.2679ZM11.5941 10.7998L5.7712 8.93507L9.01419 5.69207C10.8976 7.69116 11.6599 9.51937 11.5941 10.7998ZM3.19935 2.4051L5.06409 8.22796L8.30709 4.98496C6.308 3.10153 4.47979 2.33928 3.19935 2.4051Z",
        fill: "currentColor"
      }
    )
  }
), i = e(l);
export {
  i as default
};
