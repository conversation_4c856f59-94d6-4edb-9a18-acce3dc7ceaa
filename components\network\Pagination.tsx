export const Pagination = ({
  currentPage,
  totalPages,
  onPageChange
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}) => {
  // Calculate which page numbers to show
  const getPageNumbers = () => {
    const delta = 2; // Number of pages to show before and after current page
    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  return (
    <div className="w-full flex items-center justify-between gap-2">
      <div className="flex gap-2 w-full items-center justify-start">
        <button
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          className="px-4 py-2 text-sm font-medium text-white bg-turquoise rounded-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
        >
          ← First
        </button>

        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="px-4 py-2 text-sm font-medium text-white bg-turquoise rounded-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
        >
          ← Previous
        </button>
      </div>

      <div className="flex gap-2 w-full items-center justify-center">
        {getPageNumbers().map((pageNumber, index) => (
          pageNumber === '...' ? (
            <span
              key={`dots-${index}`}
              className="px-3 py-2 text-gray-500"
            >
              ...
            </span>
          ) : (
            <button
              key={pageNumber}
              onClick={() => onPageChange(Number(pageNumber))}
              className={`px-3 py-2 rounded-md ${
                currentPage === pageNumber
                  ? 'bg-turquoise text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
            >
              {pageNumber}
            </button>
          )
        ))}
      </div>

      <div className="flex gap-2 w-full items-center justify-end">
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="px-4 py-2 text-sm font-medium text-white bg-turquoise rounded-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
        >
          Next →
        </button>

        <button
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          className="px-4 py-2 text-sm font-medium text-white bg-turquoise rounded-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
        >
          Last ≫
        </button>
      </div>
    </div>
  );
};