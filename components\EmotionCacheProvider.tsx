'use client';

import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import { ReactNode } from 'react';

// Create a custom emotion cache that doesn't inject styles
const emotionCache = createCache({
  key: 'sugar-club',
  insertionPoint: typeof window !== 'undefined' ? document.head : undefined,
  speedy: true,
  // Set to true to disable style injection
  nonce: 'sugar-club',
  stylisPlugins: []
});

export function EmotionCacheProvider({ children }: { children: ReactNode }) {
  return (
    <CacheProvider value={emotionCache}>
      {children}
    </CacheProvider>
  );
}