import { <PERSON><PERSON><PERSON> } from 'next';
import Link from 'next/link';
import { ChevronLeft, ThumbsUp, ThumbsDown } from 'lucide-react';
import { notFound } from 'next/navigation';

// Define help categories and articles (same structure as before)
const helpCategories = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    icon: '🚀',
    articles: [
      { 
        id: 'create-account', 
        title: 'How to create an account',
        lastUpdated: 'June 15, 2023',
        content: `
          <h2>Creating your Sugar Club account</h2>
          <p>Getting started with Sugar Club is easy! Follow these simple steps to create your account:</p>
          
          <h3>Step 1: Visit the signup page</h3>
          <p>Go to the Sugar Club homepage and click on the "Sign Up" button in the top right corner.</p>
          
          <h3>Step 2: Choose your account type</h3>
          <p>Select whether you want to create a fan account or a creator account. Each account type has different features and capabilities.</p>
          
          <h3>Step 3: Enter your information</h3>
          <p>Fill in the required fields, including:</p>
          <ul>
            <li>Email address</li>
            <li>Username</li>
            <li>Password</li>
            <li>Date of birth (you must be at least 18 years old)</li>
          </ul>
          
          <h3>Step 4: Verify your email</h3>
          <p>Check your email inbox for a verification link from Sugar Club. Click the link to verify your email address.</p>
          
          <h3>Step 5: Complete your profile</h3>
          <p>After verifying your email, you'll be prompted to complete your profile by adding a profile picture, bio, and other details.</p>
          
          <h2>Account verification</h2>
          <p>To access all features and ensure platform safety, we recommend verifying your identity. This process involves submitting a government-issued ID and a selfie.</p>
          
          <h2>Troubleshooting</h2>
          <p>If you encounter any issues during the signup process, try the following:</p>
          <ul>
            <li>Clear your browser cache and cookies</li>
            <li>Try using a different browser</li>
            <li>Check if your email address is entered correctly</li>
            <li>Make sure your password meets our security requirements</li>
          </ul>
          
          <p>If you still have trouble creating an account, please contact our support team for assistance.</p>
        `,
        relatedArticles: ['profile-setup', 'navigation'],
      },
      // Other articles...
    ],
  },
  // Other categories...
];

export async function generateMetadata({ params }: { params: { category: string, article: string } }): Promise<Metadata> {
  const category = helpCategories.find(cat => cat.id === params.category);
  const article = category?.articles.find(art => art.id === params.article);
  
  if (!category || !article) {
    return {
      title: 'Article Not Found | Help Center',
      description: 'The requested help article could not be found.',
    };
  }
  
  return {
    title: `${article.title} | Help Center`,
    description: `Learn about ${article.title.toLowerCase()} on Sugar Club.`,
  };
}

export default function ArticlePage({ params }: { params: { category: string, article: string } }) {
  const category = helpCategories.find(cat => cat.id === params.category);
  const articleData = category?.articles.find(art => art.id === params.article);
  
  if (!category || !articleData) {
    notFound();
  }
  
  // Find related articles
  const relatedArticles = articleData.relatedArticles?.map(relId => {
    const relArticle = category.articles.find(art => art.id === relId);
    return relArticle ? { ...relArticle, categoryId: category.id } : null;
  }).filter(Boolean) || [];
  
  return (
    <div className="min-h-screen bg-white dark:bg-[#18181b] py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Breadcrumb */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <Link href="/help-support/help-center" className="hover:text-turquoise">
              Help Center
            </Link>
            <span>/</span>
            <Link href={`/help-support/help-center/${category.id}`} className="hover:text-turquoise">
              {category.title}
            </Link>
            <span>/</span>
            <span className="text-gray-700 dark:text-gray-300">{articleData.title}</span>
          </div>
        </div>
        
        {/* Article header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-[#18181b] dark:text-white mb-4">
            {articleData.title}
          </h1>
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <span>Last updated: {articleData.lastUpdated}</span>
          </div>
        </div>
        
        {/* Article content */}
        <div className="prose prose-lg dark:prose-invert max-w-none mb-12">
          <div dangerouslySetInnerHTML={{ __html: articleData.content }} />
        </div>
        
        {/* Feedback section */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-8 mb-12">
          <h3 className="text-lg font-medium text-[#18181b] dark:text-white mb-4">
            Was this article helpful?
          </h3>
          <div className="flex space-x-4">
            <button className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <ThumbsUp size={18} className="mr-2" />
              <span>Yes, it helped</span>
            </button>
            <button className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <ThumbsDown size={18} className="mr-2" />
              <span>No, I need more help</span>
            </button>
          </div>
        </div>
        
        {/* Related articles */}
        {relatedArticles.length > 0 && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
            <h3 className="text-xl font-semibold text-[#18181b] dark:text-white mb-6">
              Related Articles
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {relatedArticles.map((article: any) => (
                <Link 
                  key={article.id}
                  href={`/help-support/help-center/${article.categoryId}/${article.id}`}
                  className="p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-turquoise dark:hover:border-turquoise transition-colors"
                >
                  <h4 className="font-medium text-[#18181b] dark:text-white mb-1">
                    {article.title}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Read article →
                  </p>
                </Link>
              ))}
            </div>
          </div>
        )}
        
        {/* Back to category */}
        <div className="mt-12">
          <Link 
            href={`/help-support/help-center/${category.id}`}
            className="flex items-center text-turquoise hover:underline"
          >
            <ChevronLeft size={16} className="mr-1" />
            Back to {category.title}
          </Link>
        </div>
        
        {/* Contact support */}
        <div className="mt-12 text-center p-6 rounded-lg bg-gray-100 dark:bg-[#242427]">
          <h3 className="text-lg font-medium text-[#18181b] dark:text-white mb-3">
            Still have questions?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            If you couldn't find the answer you were looking for, our support team is here to help.
          </p>
          <Link 
            href="/help-support/contact"
            className="inline-flex items-center justify-center px-5 py-2 bg-turquoise text-white rounded-full hover:bg-cyan-600 transition-colors font-medium"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </div>
  );
}