import { jsx as f } from "react/jsx-runtime";
import M from "../ui/tooltipDialog.js";
import S from "../../store/useStore.js";
import A, { useMemo as D } from "react";
import { c as a } from "../../_commonjsHelpers-10dfc225.js";
import "../../hooks/useScreen.js";
import "../ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../swap/components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
var h = {}, o = {};
Object.defineProperty(o, "__esModule", { value: !0 });
o.cssValue = o.parseLengthAndUnit = void 0;
var L = {
  cm: !0,
  mm: !0,
  in: !0,
  px: !0,
  pt: !0,
  pc: !0,
  em: !0,
  ex: !0,
  ch: !0,
  rem: !0,
  vw: !0,
  vh: !0,
  vmin: !0,
  vmax: !0,
  "%": !0
};
function O(e) {
  if (typeof e == "number")
    return {
      value: e,
      unit: "px"
    };
  var t, n = (e.match(/^[0-9.]*/) || "").toString();
  n.includes(".") ? t = parseFloat(n) : t = parseInt(n, 10);
  var r = (e.match(/[^0-9]*$/) || "").toString();
  return L[r] ? {
    value: t,
    unit: r
  } : (console.warn("React Spinners: ".concat(e, " is not a valid css value. Defaulting to ").concat(t, "px.")), {
    value: t,
    unit: "px"
  });
}
o.parseLengthAndUnit = O;
function V(e) {
  var t = O(e);
  return "".concat(t.value).concat(t.unit);
}
o.cssValue = V;
var p = {};
Object.defineProperty(p, "__esModule", { value: !0 });
p.createAnimation = void 0;
var E = function(e, t, n) {
  var r = "react-spinners-".concat(e, "-").concat(n);
  if (typeof window > "u" || !window.document)
    return r;
  var i = document.createElement("style");
  document.head.appendChild(i);
  var u = i.sheet, m = `
    @keyframes `.concat(r, ` {
      `).concat(t, `
    }
  `);
  return u && u.insertRule(m, 0), r;
};
p.createAnimation = E;
var s = a && a.__assign || function() {
  return s = Object.assign || function(e) {
    for (var t, n = 1, r = arguments.length; n < r; n++) {
      t = arguments[n];
      for (var i in t)
        Object.prototype.hasOwnProperty.call(t, i) && (e[i] = t[i]);
    }
    return e;
  }, s.apply(this, arguments);
}, R = a && a.__createBinding || (Object.create ? function(e, t, n, r) {
  r === void 0 && (r = n);
  var i = Object.getOwnPropertyDescriptor(t, n);
  (!i || ("get" in i ? !t.__esModule : i.writable || i.configurable)) && (i = { enumerable: !0, get: function() {
    return t[n];
  } }), Object.defineProperty(e, r, i);
} : function(e, t, n, r) {
  r === void 0 && (r = n), e[r] = t[n];
}), C = a && a.__setModuleDefault || (Object.create ? function(e, t) {
  Object.defineProperty(e, "default", { enumerable: !0, value: t });
} : function(e, t) {
  e.default = t;
}), F = a && a.__importStar || function(e) {
  if (e && e.__esModule)
    return e;
  var t = {};
  if (e != null)
    for (var n in e)
      n !== "default" && Object.prototype.hasOwnProperty.call(e, n) && R(t, e, n);
  return C(t, e), t;
}, U = a && a.__rest || function(e, t) {
  var n = {};
  for (var r in e)
    Object.prototype.hasOwnProperty.call(e, r) && t.indexOf(r) < 0 && (n[r] = e[r]);
  if (e != null && typeof Object.getOwnPropertySymbols == "function")
    for (var i = 0, r = Object.getOwnPropertySymbols(e); i < r.length; i++)
      t.indexOf(r[i]) < 0 && Object.prototype.propertyIsEnumerable.call(e, r[i]) && (n[r[i]] = e[r[i]]);
  return n;
};
Object.defineProperty(h, "__esModule", { value: !0 });
var d = F(A), l = o, b = p, _ = [
  (0, b.createAnimation)("PuffLoader", "0% {transform: scale(0)} 100% {transform: scale(1.0)}", "puff-1"),
  (0, b.createAnimation)("PuffLoader", "0% {opacity: 1} 100% {opacity: 0}", "puff-2")
];
function I(e) {
  var t = e.loading, n = t === void 0 ? !0 : t, r = e.color, i = r === void 0 ? "#000000" : r, u = e.speedMultiplier, m = u === void 0 ? 1 : u, v = e.cssOverride, w = v === void 0 ? {} : v, g = e.size, c = g === void 0 ? 60 : g, j = U(e, ["loading", "color", "speedMultiplier", "cssOverride", "size"]), x = s({ display: "inherit", position: "relative", width: (0, l.cssValue)(c), height: (0, l.cssValue)(c) }, w), y = function(P) {
    return {
      position: "absolute",
      height: (0, l.cssValue)(c),
      width: (0, l.cssValue)(c),
      border: "thick solid ".concat(i),
      borderRadius: "50%",
      opacity: "1",
      top: "0",
      left: "0",
      animationFillMode: "both",
      animation: "".concat(_[0], ", ").concat(_[1]),
      animationDuration: "".concat(2 / m, "s"),
      animationIterationCount: "infinite",
      animationTimingFunction: "cubic-bezier(0.165, 0.84, 0.44, 1), cubic-bezier(0.3, 0.61, 0.355, 1)",
      animationDelay: P === 1 ? "-1s" : "0s"
    };
  };
  return n ? d.createElement(
    "span",
    s({ style: x }, j),
    d.createElement("span", { style: y(1) }),
    d.createElement("span", { style: y(2) })
  ) : null;
}
var T = h.default = I;
const _e = () => {
  const { isAnimationsDisabled: e } = S(
    (n) => n.globalSettingsSlice
  ), t = D(() => e ? /* @__PURE__ */ f("div", { className: "w-2 h-2 sm:w-[8px] sm:h-[8px] [bg-accent rounded-full sm:w-4 sm:h-4 sm:rounded-[7px] sm:flex sm:justify-center sm:items-center" }) : /* @__PURE__ */ f(T, { size: 20, color: "var(--dhs-accent)", speedMultiplier: 0.75 }), [e]);
  return /* @__PURE__ */ f(
    M,
    {
      trigger: t,
      content: "Data updating real-time",
      contentClass: "dhs-text-mainText"
    }
  );
};
export {
  _e as default
};
