'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { useUser } from '@/hooks/useUser';
import { useDispatch } from 'react-redux';
import {
  handleSocketPostSave,
  handleSocketPostLike,
  handleSocketPostComment,
  handleSocketPostView
} from '@/redux/slices/postsSlice';

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  error: string | null;
  joinPostRoom: (postId: any) => void;
  leavePostRoom: (postId: any) => void;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  error: null,
  joinPostRoom: () => { },
  leavePostRoom: () => { }
});

export const useSocket = () => useContext(SocketContext);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();
  const dispatch = useDispatch();

  const joinPostRoom = (postId: string) => {
    if (socket && isConnected) {
      console.log(`[Client] Joining post room: ${postId}`);
      socket.emit('joinRoom', postId);
    }
  };

  const leavePostRoom = (postId: string) => {
    if (socket && isConnected) {
      console.log(`[Client] Leaving post room: ${postId}`);
      socket.emit('leaveRoom', postId);
    }
  };

  useEffect(() => {
    let socketInstance: Socket | null = null;

    const initSocket = () => {
      if (socketInstance) return;

      try {
        const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';
        console.log('[SocketContext] Initializing socket connection to:', socketUrl);

        socketInstance = io(socketUrl, {
          path: '/socket.io',
          transports: ['websocket'],
          reconnection: true,
          reconnectionAttempts: 5, // Limit reconnection attempts
          reconnectionDelay: 1000, // Wait 1 second between attempts
          withCredentials: true,
          upgrade: true,
          forceBase64: false
        });

        // Set socket instance to state only after successful connection
        socketInstance.on('connect', () => {
          console.log('[SocketContext] Connected with ID:', socketInstance?.id);
          setSocket(socketInstance);
          setIsConnected(true);
          setError(null);

          if (user?.id) {
            socketInstance?.emit('userConnected', user.id);
          }
        });

        // Post-related events
        socketInstance.on('postUpdate', (data: {
          room: string;
          type: string;
          data: {
            postId: string;
            saved?: boolean;
            saves?: number;
            liked?: boolean;
            likes?: number;
            views?: number;
            comment?: any;
            comments?: number;
          }
        }) => {
          console.log('[SocketContext] Received postUpdate:', data);

          switch (data.type) {
            case 'save':
              dispatch(handleSocketPostSave({
                postId: data.data.postId,
                saved: data.data.saved || false,
                saves: data.data.saves || 0
              }));
              break;
            case 'like':
              dispatch(handleSocketPostLike({
                postId: data.data.postId,
                likes: data.data.likes || 0,
                liked: data.data.liked || false
              }));
              break;
            case 'view':
              dispatch(handleSocketPostView({
                postId: data.data.postId,
                views: data.data.views || 0
              }));
              break;
            case 'comment':
              if (data.data.comment && data.data.comments !== undefined) {
                dispatch(handleSocketPostComment({
                  postId: data.data.postId,
                  comment: data.data.comment,
                  comments: data.data.comments
                }));
              }
              break;
          }
        });

        // Error handling
        socketInstance.on('disconnect', (reason) => {
          console.log('[SocketContext] Disconnected:', reason);
          setIsConnected(false);
          // Don't automatically reconnect on certain disconnect reasons
          if (reason === 'io server disconnect' || reason === 'io client disconnect') {
            setSocket(null);
          }
        });

        socketInstance.on('connect_error', (err) => {
          console.error('[SocketContext] Connection error:', err.message);
          setIsConnected(false);
          setError(err.message);
        });

        socketInstance.on('error', (err) => {
          console.error('[SocketContext] Socket error:', err);
        });

      } catch (err: any) {
        console.error('[SocketContext] Error initializing socket:', err);
        setError(err.message);
      }
    };

    initSocket();

    // Cleanup function
    return () => {
      console.log('[SocketContext] Cleaning up socket connection...');
      if (socketInstance) {
        socketInstance.off();
        socketInstance.disconnect();
        setSocket(null);
        setIsConnected(false);
      }
    };
  }, [user?.id]); // Only reinitialize if user ID changes

  // Update user identification when user changes
  useEffect(() => {
    if (socket && isConnected && user?.id) {
      socket.emit('userConnected', user.id);
    }
  }, [socket, isConnected, user?.id]);

  return (
    <SocketContext.Provider value={{
      socket,
      isConnected,
      error,
      joinPostRoom,
      leavePostRoom
    }}>
      {children}
    </SocketContext.Provider>
  );
};
