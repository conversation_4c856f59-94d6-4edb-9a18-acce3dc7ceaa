import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import * as MainJS from '@/public/main';
import Swal from 'sweetalert2';
import Tippy from "@tippyjs/react";
import { Skeleton } from '@/components/ui/skeleton';
import { createPopup } from '@picmo/popup-picker';
import { buttonVariants } from "@/components/ui/button";
import { useSelector } from 'react-redux';
import { FileUpload, FileWithPreview } from '@/components/ui/chat/file-upload';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';

interface SendMessagePopupProps {
    recipient: any;
    DarkTheme: boolean;
    isOpen: boolean;
    address: string;
    username: string;
    profilePhoto: string;
    onSendMessage: () => void;
    onClose: () => void;
}

const SendMessagePopup = ({ isOpen, onClose, address, DarkTheme, username, profilePhoto, onSendMessage }: SendMessagePopupProps) => {

    const { walletAddress } = useSelector((state: any) => state.wallet);

    const [isLoading, setIsLoading] = useState(true);
    const [message, setMessage] = useState('');
    const [attachment, setAttachment] = useState<FileWithPreview | null>(null);
    const [loadingImage, setLoadingImage] = useState(true);
    const messageRef = useRef(null) as any;
    const fileInputRef = useRef(null) as any;
    const pickerRef = useRef(null) as any;
    const triggerButtonRef = useRef(null) as any;

    const MAX_CHARS = 4000;
    const FILE_SIZE_LIMIT = 10 * 1024 * 1024;
    const ACCEPTED_FILE_TYPES = ['image/', 'video/'];
    const YELLOW_THRESHOLD = 50;
    const ORANGE_THRESHOLD = 25;
    const RED_THRESHOLD = 10;

    const getColorForCharacterCount = (charactersLeft: number) => {
        if (charactersLeft <= RED_THRESHOLD) return '#f80000';
        if (charactersLeft <= ORANGE_THRESHOLD) return '#ffb42a';
        if (charactersLeft <= YELLOW_THRESHOLD) return '#feda03';
        return DarkTheme ? '#fff' : 'var(--text-6)';
    };

    const handleLimitedInputChange = (e: React.ChangeEvent<HTMLInputElement>, setter: any, maxChars: number) => {
        e.preventDefault();
        const value = e.target.value;
        if (value.length <= maxChars) {
            setter(value);
        }
    };

    const uploadFileMutation = useMutation(api.media.uploadFile);

    const uploadFile = async (file: any) => {
        setIsLoading(true);
        // Convert file to base64
        const fileAsBase64 = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve((reader.result as string).split(',')[1]);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
        try {
            const result = await uploadFileMutation({
                userId: walletAddress, // or actual userId if available
                file: fileAsBase64,
                fileType: file.type,
                fileName: file.name,
                privacy: 'public',
                saveToLibrary: true,
            });
            if (result.success) {
                return result;
            } else {
                throw new Error(result.message || 'File upload failed');
            }
        } catch (error: any) {
            throw error;
        }
    };

    const sendMessage = async () => {
        if (message.trim() === "" && !attachment) {
            return;
        }

        let attachmentData = null;

        if (attachment) {
            try {
                attachmentData = await uploadFile(attachment.file);
            } catch (error: any) {
                Swal.fire({
                    html: error.message,
                    icon: "error",
                    confirmButtonText: "Close"
                });
                setAttachment(null);
                setIsLoading(false);
                return;
            }
        }

        setIsLoading(true);

        try {
            const body = {
                senderAddress: walletAddress,
                recipientAddress: address,
                messageData: message,
                attachment_url: attachmentData?.url || null,
                attachment_type: attachment?.type || null,
                attachment_name: attachmentData?.name || null,
                attachment_id: attachmentData?.id || null,
            };

            const response = await fetch('/api/conversations', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SECRET}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(body),
            });

            if (response.ok) {
                Swal.fire({
                    html: 'Message successfully sent!',
                    icon: "success",
                    confirmButtonText: "Close"
                });
                setMessage('');
                setAttachment(null);
                onClose();
                onSendMessage();
                onSendMessage();
                onSendMessage();
                onSendMessage();
            } else {
                throw new Error('Failed to send message');
            }
        } catch (error: any) {
            console.error('Error sending message:', error);
            Swal.fire({
                html: 'Error sending message!',
                icon: 'error',
                confirmButtonText: 'Close'
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Function to handle the attachment click.
    const handleAttachClick = () => {
        fileInputRef?.current?.click();
    };

    // Function to preview selected file.
    const handleFilePreview = (event: any) => {
        const file = event.target.files[0];

        if (file) {
            if (file.size > FILE_SIZE_LIMIT) {
                Swal.fire({
                    icon: 'error',
                    text: 'The file size should not exceed 10MB.',
                    confirmButtonText: 'Close'
                });
                return;
            }

            const isAcceptedType = ACCEPTED_FILE_TYPES.some(type => file.type.startsWith(type));
            if (!isAcceptedType) {
                Swal.fire({
                    icon: 'error',
                    text: 'This file type is not allowed, only image and video files are accepted!',
                    confirmButtonText: 'Close'
                });
                return;
            }

            const previewUrl = URL.createObjectURL(file);
            setAttachment({
                file,
                previewUrl,
                type: file.type,
                name: file.name,
            });
        }
    };

    // Function to remove attachment preview.
    const removeAttachmentPreview = () => {
        setAttachment(null);
    };

    // Function to get file preview.
    const getFilePreview = () => {
        if (!attachment) return null;

        const { previewUrl, type, name } = attachment;

        if (type.startsWith("image/")) {
            return (
                <div className="relative">
                    <img src={previewUrl} alt="Attachment Preview" className="max-w-full" />
                    <button className="absolute" onClick={removeAttachmentPreview}>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                            <path d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3l105.4 105.3c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256l105.3-105.4z" />
                        </svg>
                    </button>
                </div>
            );
        }

        if (type.startsWith("video/")) {
            return (
                <div className="relative">
                    <video src={previewUrl} className="max-w-full" controls />
                    <button className="absolute" onClick={removeAttachmentPreview}>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                            <path d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3l105.4 105.3c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256l105.3-105.4z" />
                        </svg>
                    </button>
                </div>
            );
        }

        return (
            <div className="file-icon-preview relative">
                <span className={`message-attachment-icon message-attachment-icon-generic`}></span>
                <span>{name}</span>
                <button className="absolute" onClick={removeAttachmentPreview}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                        <path d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3l105.4 105.3c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256l105.3-105.4z" />
                    </svg>
                </button>
            </div>
        );
    };

    useEffect(() => {
        if (isOpen) {
            const triggerButton = document.querySelector('.emoji-btn');
            triggerButtonRef.current = triggerButton;

            // Clean up previous picker if it exists
            if (pickerRef.current) {
                pickerRef.current.picker?.destroy();
                pickerRef.current = null;
            }

            // Create the picker
            pickerRef.current = createPopup({
                showPreview: false,
                showRecents: true,
                theme: DarkTheme ? 'dark' : 'light',
                className: 'message-emoji-picker',
                emojiSize: '30px',
                autoFocus: 'auto'
            }, {
                position: 'top-start',
                className: 'contact-owner-popup-emojis'
            });

            // Toggle picker on trigger button click
            const togglePicker = () => {
                if (pickerRef.current) {
                    pickerRef.current.toggle({
                        triggerElement: triggerButtonRef.current,
                        referenceElement: triggerButtonRef.current
                    });
                }
            };
            triggerButtonRef?.current?.addEventListener('click', togglePicker);

            // Handle emoji selection
            const handleEmojiClick = (event: any) => {
                const emoji = event.emoji;
                const text = messageRef?.current?.value;
                const cursorPosition = messageRef?.current?.selectionStart;
                const start = text.slice(0, cursorPosition);
                const end = text.slice(cursorPosition);

                setMessage(start + emoji + end);

                setTimeout(() => {
                    if (messageRef.current) {
                        messageRef.current.selectionStart = messageRef.current.selectionEnd = cursorPosition + emoji.length;
                    }
                }, 0);
            };
            pickerRef?.current?.addEventListener('emoji:select', handleEmojiClick);

            // Cleanup on component unmount or when isOpen/DarkTheme changes
            return () => {
                if (pickerRef.current) {
                    pickerRef.current.removeEventListener('emoji:select', handleEmojiClick);
                    pickerRef.current.picker?.destroy();
                    pickerRef.current = null;
                }
                if (triggerButtonRef.current) {
                    triggerButtonRef.current.removeEventListener('click', togglePicker);
                }
            };
        }
    }, [isOpen, DarkTheme]);

    useEffect(() => {
        if (isOpen) {
            setIsLoading(true);
            setMessage('');
            setAttachment(null);
            setTimeout(() => setIsLoading(false), 900);
        }
    }, [isOpen]);

    return (
        <Modal centered={true} animation={true} className="contact-owner-popup" dialogClassName="card" show={isOpen} onHide={onClose} backdrop="static">
            <Modal.Header closeButton>
                <Modal.Title></Modal.Title>
                <svg version="1.1" onClick={onClose} className="close-popup" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" x={0} y={0} width={20} height={20} viewBox="0 0 23.922 23.916" enableBackground="new 0 0 23.922 23.916" xmlSpace="preserve">
                    <rect x={2.604} y={14.698} transform="matrix(0.7071 0.7071 -0.7071 0.7071 14.9741 2.3277)" fill="#feda03" width={4.146} height={9.083} />
                    <rect x={17.166} y={0.135} transform="matrix(0.7072 0.707 -0.707 0.7072 8.9391 -12.2324)" fill="#feda03" width={4.145} height={9.083} />
                    <rect x={2.61} y={0.141} transform="matrix(-0.7071 0.7071 -0.7071 -0.7071 11.3045 4.6818)" fill="#feda03" width={4.145} height={9.083} />
                    <rect x={17.172} y={14.703} transform="matrix(-0.7071 0.7071 -0.7071 -0.7071 46.4606 19.2446)" fill="#feda03" width={4.146} height={9.083} />
                </svg>

                <div className="recipient-icon-container">
                    <Skeleton width="100%" height="100%" borderRadius="50%" loading={loadingImage && isLoading}><img onLoad={() => setLoadingImage(false)} className="recipient-icon" src={profilePhoto || '/images/user/default-avatar.png'} alt={username || MainJS.truncateAddress(address)} /></Skeleton>
                </div>

                <span className="recipient-name">{username || MainJS.truncateAddress(address)}</span>
            </Modal.Header>
            <Modal.Body>

                <div className="message-popup-text-container">
                    <textarea
                        id="message"
                        className="contact-owner-input dark:bg-jacarta-700 border-jacarta-100 hover:ring-accent/10 focus:ring-accent dark:border-jacarta-600 dark:placeholder:text-jacarta-300 w-full rounded-lg hover:ring-2 dark:text-white"
                        value={message}
                        placeholder="Write message"
                        disabled={isLoading}
                        onChange={(e: any) => handleLimitedInputChange(e, setMessage, MAX_CHARS)}
                        maxLength={MAX_CHARS}
                        ref={messageRef}
                    ></textarea>

                    <div className="contact-owner-functions">
                        {attachment && (
                            <div className="attachment-preview">
                                {getFilePreview()}
                            </div>
                        )}
                        <div className="icon-container">
                            <Tippy
                                content="Upload Image or Video"
                                animation="shift-toward-subtle"
                                followCursor={false}
                                placement="top"
                                arrow={true}
                                theme="kongs"
                            >
                                <button
                                    className={buttonVariants({ variant: "ghost", size: "icon" }) + ' upload-file-button'}
                                    onClick={handleAttachClick}
                                    disabled={isLoading}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                        <path d="M448 80c8.8 0 16 7.2 16 16v319.8l-5-6.5-136-176c-4.5-5.9-11.6-9.3-19-9.3s-14.4 3.4-19 9.3l-83 107.4-30.5-42.7c-4.5-6.3-11.7-10-19.5-10s-15 3.7-19.5 10.1l-80 112-4.5 6.2V96c0-8.8 7.2-16 16-16h384zM64 32C28.7 32 0 60.7 0 96v320c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zm80 192a48 48 0 100-96 48 48 0 100 96z" />
                                    </svg>
                                </button>
                            </Tippy>
                            <input
                                type="file"
                                ref={fileInputRef}
                                style={{ display: 'none' }}
                                onChange={handleFilePreview}
                                disabled={isLoading}
                            />
                            <div>
                                <Tippy
                                    content="Insert Emoji"
                                    animation="shift-toward-subtle"
                                    followCursor={false}
                                    placement="top"
                                    arrow={true}
                                    theme="kongs"
                                >
                                    <button className={buttonVariants({ variant: "ghost", size: "icon" }) + " emoji-btn-container"}>
                                        <svg className="emoji-btn" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                            <path d="M464 256a208 208 0 10-416 0 208 208 0 10416 0zM0 256a256 256 0 11512 0 256 256 0 11-512 0zm349.5 52.4c18.7-4.4 35.9 12 25.5 28.1-24.6 38.1-68.7 63.5-119.1 63.5s-94.5-25.4-119.1-63.5c-10.4-16.1 6.8-32.5 25.5-28.1 28.9 6.8 60.5 10.5 93.6 10.5s64.7-3.7 93.6-10.5zM144.4 208a32 32 0 1164 0 32 32 0 11-64 0zm192-32a32 32 0 110 64 32 32 0 110-64z" />
                                        </svg>
                                    </button>
                                </Tippy>
                                <Tippy
                                    content="Voice Note Coming Soon"
                                    animation="shift-toward-subtle"
                                    followCursor={false}
                                    placement="top"
                                    arrow={true}
                                    theme="kongs"
                                >
                                    <button className={buttonVariants({ variant: "ghost", size: "icon" }) + " voice-note-button"}>
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                                            <path d="M96 96v160c0 53 43 96 96 96s96-43 96-96h-80c-8.8 0-16-7.2-16-16s7.2-16 16-16h80v-32h-80c-8.8 0-16-7.2-16-16s7.2-16 16-16h80v-32h-80c-8.8 0-16-7.2-16-16s7.2-16 16-16h80c0-53-43-96-96-96S96 43 96 96zm224 144v16c0 70.7-57.3 128-128 128S64 326.7 64 256v-40c0-13.3-10.7-24-24-24s-24 10.7-24 24v40c0 89.1 66.2 162.7 152 174.4V464h-48c-13.3 0-24 10.7-24 24s10.7 24 24 24h144c13.3 0 24-10.7 24-24s-10.7-24-24-24h-48v-33.6c85.8-11.7 152-85.3 152-174.4v-40c0-13.3-10.7-24-24-24s-24 10.7-24 24v24z" />
                                        </svg>
                                    </button>
                                </Tippy>
                            </div>
                        </div>
                    </div>
                </div>

                <p className="characters-left" style={{ color: getColorForCharacterCount(MAX_CHARS - message.length) }}>
                    {MAX_CHARS - message.length} characters left
                </p>

                <button disabled={isLoading} onClick={sendMessage} className="save-changes-button">
                    {isLoading ? <Spinner animation="border" /> : "Send Message"}
                </button>
            </Modal.Body>
            <Modal.Footer></Modal.Footer>
        </Modal>
    );
};
export default SendMessagePopup;
