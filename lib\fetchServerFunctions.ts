import { setUserInfo } from "@/redux/slices/userInfoSlice";
import { api } from "@/convex/_generated/api";
import { fetchQuery } from "convex/nextjs";

export async function fetchNotifications(userId: string) {
    const headers = new Headers();
    headers.append('Content-Type', 'application/json');

    try {
        const response = await fetch(
            `/api/notifications/?userId=${userId}`,
            {
                method: "GET",
                headers,
                credentials: 'include'
            }
        );
        const data = await response.json();
        return data.success ? { notifications: data.notifications, unread: data.unread } : { notifications: [], unread: 0 };
    } catch (error) {
        console.error("Error fetching notifications:", error);
        return { notifications: [], unread: 0 };
    }
};

export async function fetchMessages(userId: string) {
    const headers = new Headers();
    headers.append('Content-Type', 'application/json');

    try {
        const response = await fetch(`/api/messages?userId=${userId}`, {
            method: "GET",
            headers,
            credentials: 'include'
        });
        const data = await response.json();
        return data.success ? { messages: data.messages, unread: data.unread } : { messages: [], unread: 0 };
    } catch (error) {
        console.error("Error fetching messages:", error);
        return { messages: [], unread: 0 };
    }
};

export const getUserInfo = async (user: string, accountType: string, dispatch: any) => {
    const headers = new Headers();
    headers.append('Content-Type', 'application/json');

    try {
        const response = await fetch(`/api/user-details/?user=${user}&accountType=${accountType}`, {
            method: "GET",
            headers,
            credentials: 'include'
        });

        if (response.ok) {
            const data = await response.json();

            if (data.success && data.data) {
                const { username, displayName, profilePhoto, subscribers } = data.data?.user_info;
                dispatch(setUserInfo({
                    username,
                    displayName,
                    profilePhoto,
                    hasProfilePhoto: !!profilePhoto,
                    subscribers
                }));
            }
        }
    } catch (error) {
        console.error("Error fetching user info:", error);
    }
};

export const getUserProfileInfo = async (user: string) => {
    const headers = new Headers();
    headers.append('Content-Type', 'application/json');

    // Ensure we are using an absolute URL
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

    try {
        const response = await fetch(`${apiBaseUrl}/api/user-profile/?user=${encodeURIComponent(user)}`, {
            method: "GET",
            headers,
            credentials: 'include'
        });

        if (response.ok) {
            const data = await response.json();

            if (data.success && data.data) {
                return data.data;
            } else {
                return { error: 'User not found', status: 404 };
            }
        } else {
            return { error: 'Failed to fetch user data', status: response.status };
        }
    } catch (error) {
        console.error("Error fetching user info:", error);
        return { error: 'Failed to fetch user data', status: 500 };
    }
};

export const getUserSettingsInfo = async (userId: string) => {
    // Use Convex fetchQuery to call the getUserSettings query
    try {
        // fetchQuery expects the API function and args (empty object for getUserSettings)
        const data = await fetchQuery(api.users.getUserSettings, {});
        if (data) {
            return data;
        } else {
            return { error: 'User not found', status: 404 };
        }
    } catch (error: any) {
        console.error("Error fetching user info from Convex:", error);
        return { error: error?.message || 'Failed to fetch user data', status: 500 };
    }
};

export const fetchReports = async () => {
    try {
        const response = await fetch('/api/reports', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error('Failed to fetch reports');
        }

        const data = await response.json();
        return data.reports;
    } catch (error) {
        console.error('Error fetching reports:', error);
        return [];
    }
};

export const submitReport = async ({ postId, reason }: any) => {
    try {
        const response = await fetch('/api/reports', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ postId, reason }),
        });

        if (!response.ok) {
            throw new Error('Failed to submit report');
        }

        return await response.json();
    } catch (error) {
        console.error('Error submitting report:', error);
        throw error;
    }
};