'use client';

import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import MessagesSidebar from '@/components/messages/MessagesSidebar';
import { MessagesProvider } from '@/context/Message/MessageContext';

export default function MessagesSearchClient({ children }: { children: React.ReactNode }) {
  // Fetch all conversations for the current user
  const allConversations = useQuery(api.messages.getAllConversations, {});

  const isLoading = allConversations === undefined;
  const error = allConversations && 'error' in allConversations ? allConversations.error : null;

  if (isLoading) {
    return <div className="text-center w-full h-screen flex items-center justify-center">Loading...</div>;
  }
  if (error) {
    return <div className="text-center w-full h-screen flex items-center justify-center">Failed to load messages</div>;
  }

  return (
    <MessagesProvider messages={allConversations || []}>
      <div className="max-w-[90.7%] container mx-auto px-0 pt-2 gap-0 flex h-screen text-[#18181b] dark:text-white border border-solid border-r border-l border-b-0 border-t-0 border-[#18181b]/30 dark:border-white/30">
        <MessagesSidebar messages={allConversations?.conversations || []} />
        {children}
      </div>
    </MessagesProvider>
  );
}
