import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { User } from "@/types/user";

export async function getCurrentUser() {
  const session = await getServerSession(authOptions);
  if (!session?.user) return null;

  // Fetch additional user data from your database
  // This is a placeholder - replace with your actual user fetching logic
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    include: {
      // Include any relations you need
    },
  });

  if (!user) return null;

  return {
    ...session.user,
    ...user,
  };
}

export async function getSession() {
  return await getServerSession(authOptions);
}
