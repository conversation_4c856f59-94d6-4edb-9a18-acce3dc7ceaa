import { a as W, _ as O } from "./index-1c873780.js";
import { useEffect as f, forwardRef as m, useContext as B, useState as F, createElement as D, useRef as h, createContext as M } from "react";
import { c as P, $ as p } from "./index-c7156e07.js";
import { $ as g, a as X, R as j } from "./index-c8f2666b.js";
function q(o, e = globalThis == null ? void 0 : globalThis.document) {
  const t = P(o);
  f(() => {
    const s = (n) => {
      n.key === "Escape" && t(n);
    };
    return e.addEventListener("keydown", s), () => e.removeEventListener("keydown", s);
  }, [
    t,
    e
  ]);
}
const y = "dismissableLayer.update", G = "dismissableLayer.pointerDownOutside", J = "dismissableLayer.focusOutside";
let R;
const k = /* @__PURE__ */ M({
  layers: /* @__PURE__ */ new Set(),
  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),
  branches: /* @__PURE__ */ new Set()
}), Q = /* @__PURE__ */ m((o, e) => {
  var t;
  const { disableOutsidePointerEvents: s = !1, onEscapeKeyDown: n, onPointerDownOutside: r, onFocusOutside: b, onInteractOutside: d, onDismiss: l, ...v } = o, a = B(k), [c, S] = F(null), u = (t = c == null ? void 0 : c.ownerDocument) !== null && t !== void 0 ? t : globalThis == null ? void 0 : globalThis.document, [, U] = F({}), z = W(
    e,
    (i) => S(i)
  ), T = Array.from(a.layers), [A] = [
    ...a.layersWithOutsidePointerEventsDisabled
  ].slice(-1), K = T.indexOf(A), w = c ? T.indexOf(c) : -1, N = a.layersWithOutsidePointerEventsDisabled.size > 0, x = w >= K, H = Y((i) => {
    const $ = i.target, L = [
      ...a.branches
    ].some(
      (E) => E.contains($)
    );
    !x || L || (r == null || r(i), d == null || d(i), i.defaultPrevented || l == null || l());
  }, u), C = Z((i) => {
    const $ = i.target;
    [
      ...a.branches
    ].some(
      (E) => E.contains($)
    ) || (b == null || b(i), d == null || d(i), i.defaultPrevented || l == null || l());
  }, u);
  return q((i) => {
    w === a.layers.size - 1 && (n == null || n(i), !i.defaultPrevented && l && (i.preventDefault(), l()));
  }, u), f(() => {
    if (c)
      return s && (a.layersWithOutsidePointerEventsDisabled.size === 0 && (R = u.body.style.pointerEvents, u.body.style.pointerEvents = "none"), a.layersWithOutsidePointerEventsDisabled.add(c)), a.layers.add(c), I(), () => {
        s && a.layersWithOutsidePointerEventsDisabled.size === 1 && (u.body.style.pointerEvents = R);
      };
  }, [
    c,
    u,
    s,
    a
  ]), f(() => () => {
    c && (a.layers.delete(c), a.layersWithOutsidePointerEventsDisabled.delete(c), I());
  }, [
    c,
    a
  ]), f(() => {
    const i = () => U({});
    return document.addEventListener(y, i), () => document.removeEventListener(y, i);
  }, []), /* @__PURE__ */ D(g.div, O({}, v, {
    ref: z,
    style: {
      pointerEvents: N ? x ? "auto" : "none" : void 0,
      ...o.style
    },
    onFocusCapture: p(o.onFocusCapture, C.onFocusCapture),
    onBlurCapture: p(o.onBlurCapture, C.onBlurCapture),
    onPointerDownCapture: p(o.onPointerDownCapture, H.onPointerDownCapture)
  }));
}), V = /* @__PURE__ */ m((o, e) => {
  const t = B(k), s = h(null), n = W(e, s);
  return f(() => {
    const r = s.current;
    if (r)
      return t.branches.add(r), () => {
        t.branches.delete(r);
      };
  }, [
    t.branches
  ]), /* @__PURE__ */ D(g.div, O({}, o, {
    ref: n
  }));
});
function Y(o, e = globalThis == null ? void 0 : globalThis.document) {
  const t = P(o), s = h(!1), n = h(() => {
  });
  return f(() => {
    const r = (d) => {
      if (d.target && !s.current) {
        let v = function() {
          _(G, t, l, {
            discrete: !0
          });
        };
        const l = {
          originalEvent: d
        };
        d.pointerType === "touch" ? (e.removeEventListener("click", n.current), n.current = v, e.addEventListener("click", n.current, {
          once: !0
        })) : v();
      } else
        e.removeEventListener("click", n.current);
      s.current = !1;
    }, b = window.setTimeout(() => {
      e.addEventListener("pointerdown", r);
    }, 0);
    return () => {
      window.clearTimeout(b), e.removeEventListener("pointerdown", r), e.removeEventListener("click", n.current);
    };
  }, [
    e,
    t
  ]), {
    // ensures we check React component tree (not just DOM tree)
    onPointerDownCapture: () => s.current = !0
  };
}
function Z(o, e = globalThis == null ? void 0 : globalThis.document) {
  const t = P(o), s = h(!1);
  return f(() => {
    const n = (r) => {
      r.target && !s.current && _(J, t, {
        originalEvent: r
      }, {
        discrete: !1
      });
    };
    return e.addEventListener("focusin", n), () => e.removeEventListener("focusin", n);
  }, [
    e,
    t
  ]), {
    onFocusCapture: () => s.current = !0,
    onBlurCapture: () => s.current = !1
  };
}
function I() {
  const o = new CustomEvent(y);
  document.dispatchEvent(o);
}
function _(o, e, t, { discrete: s }) {
  const n = t.originalEvent.target, r = new CustomEvent(o, {
    bubbles: !1,
    cancelable: !0,
    detail: t
  });
  e && n.addEventListener(o, e, {
    once: !0
  }), s ? X(n, r) : n.dispatchEvent(r);
}
const oe = Q, ie = V, re = /* @__PURE__ */ m((o, e) => {
  var t;
  const { container: s = globalThis == null || (t = globalThis.document) === null || t === void 0 ? void 0 : t.body, ...n } = o;
  return s ? /* @__PURE__ */ j.createPortal(/* @__PURE__ */ D(g.div, O({}, n, {
    ref: e
  })), s) : null;
});
export {
  re as $,
  Q as a,
  ie as b,
  oe as c
};
