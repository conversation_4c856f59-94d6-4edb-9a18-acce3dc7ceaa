/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @next/next/no-img-element */
import Image from 'next/image'

const VideoGrid = () => {
  const videos = [
    { id: 1, thumbnail: 'https://picsum.photos', title: 'Video 1', author: 'Author 1' },
    { id: 2, thumbnail: 'https://picsum.photos', title: 'Video 2', author: 'Author 2' },
    { id: 3, thumbnail: 'https://picsum.photos', title: 'Video 3', author: 'Author 3' },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {videos.map((video) => (
        <div key={video.id} className="bg-gray-900 rounded-lg overflow-hidden group">
          <div className="relative aspect-video">
            <img
              src={video.thumbnail}
              alt={video.title}
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <button className="px-4 py-2 bg-cyan-500 rounded-full text-sm hover:bg-cyan-600 transition-colors">
                Watch Now
              </button>
            </div>
          </div>
          <div className="p-4">
            <h3 className="font-semibold mb-1">{video.title}</h3>
            <p className="text-sm text-gray-400">{video.author}</p>
          </div>
        </div>
      ))}
    </div>
  )
}

export default VideoGrid
