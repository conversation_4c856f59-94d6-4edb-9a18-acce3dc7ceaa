// convex/auth.ts
import { convexAuth, authTables } from "@convex-dev/auth/server";
import { ConvexCredentials } from "@convex-dev/auth/providers/ConvexCredentials";
import { query, action } from "./_generated/server";
//import Google from "@auth/core/providers/google";
//import Facebook from "@auth/core/providers/facebook";
//import Twitter from "@auth/core/providers/twitter";
//import Discord from "@auth/core/providers/discord";
//import LinkedIn from "@auth/core/providers/linkedin";
//import Reddit from "@auth/core/providers/reddit";
//import Twitch from "@auth/core/providers/twitch";
//import Resend from "@auth/core/providers/resend";
//import Apple from "@auth/core/providers/apple";
//import { Anonymous } from "@convex-dev/auth/providers/Anonymous";
//import { Password } from "@convex-dev/auth/providers/Password";
//import { ResendOTP } from "./otp/ResendOTP";
//import { TwilioOTP } from "./otp/TwilioOTP";
//import { TwilioVerify } from "./otp/TwilioVerify";
//import { ResendOTPPasswordReset } from "./passwordReset/ResendOTPPasswordReset";
import { v } from "convex/values";
import { api } from "./_generated/api";
export { generateNonce } from "./walletAuth";

// Define INVALID_PASSWORD constant
const INVALID_PASSWORD = "Password must be at least 6 characters and include one number, one uppercase, and one lowercase letter.";

// Wallet provider
const WalletProvider: any = {
  id: "wallet",
  async signIn(params: any, ctx: any) {
    const { address, blockchain, signature, accountType } = params as {
      address: string;
      blockchain: string;
      signature: string;
      accountType: string;
    };

    const result = await ctx.runAction(api.auth.walletSignIn, {
      address,
      blockchain,
      signature,
      accountType,
    });

    if (!result.success) {
      throw new Error(result.message);
    }

    return {
      userId: result.userId,
      user: {
        walletAddress: result.address,
        blockchain: result.blockchain,
        accountType: result.accountType,
      },
    };
  },
};

// -----------------------
// Custom Credentials Provider
// -----------------------
const customCredentialProvider = ConvexCredentials({
  id: "custom-provider",
  options: [
    { label: "Email", name: "email", type: "email", required: true },
    { label: "Password", name: "password", type: "password", required: true },
    { label: "User Type", name: "userType", type: "text", required: true }
  ],
  async authorize(credentials: any, ctx: any) {
    const { email, password, userType } = credentials as {
      email: string;
      password: string;
      userType: string;
    };

    // Validate userType
    if (userType !== "creator" && userType !== "user") {
      throw new Error("Invalid user type");
    }

    // Login via your mutation
    const result = userType === "creator"
      ? await ctx.runMutation(api.accounts.creatorLogin, { email, password, userType })
      : await ctx.runMutation(api.accounts.userLogin, { email, password, userType });

    if (!result.success) {
      throw new Error(result.message || "Invalid credentials");
    }

    // Upsert auth linkage
    await ctx.runMutation(api.linkAuthAccount.upsertAuthAccount, {
      provider: "custom-provider",
      providerAccountId: result.user.userId,
    });

    // Ensure user exists in `users` table (Convex Auth)
    const authUser = await ctx.runMutation(api.accounts.createAuthUser, {
      accountId: result.user.userId,
      email: result.user.email,
      accountType: result.user.accountType,
    });

    return { userId: authUser.userId }; // Must return { userId: string }
  },
});

// Wallet sign-in action
export const walletSignIn = action({
  args: {
    address: v.string(),
    blockchain: v.string(),
    signature: v.string(),
    accountType: v.string(),
  },
  handler: async (ctx: any, args: any) => {
    return await ctx.runAction(api.verifyWallet.verifyWalletSignIn, args);
  },
});

// Get user info
export const getUser = query({
  args: { userId: v.string() }, // userId is the Convex Auth users _id (from JWT sub)
  handler: async (ctx: any, args: any) => {

    // 1. Get the users record
    const user = await ctx.db.get(args.userId);

    if (!user) return null;

    // 2. Get the Accounts record using accountId
    const account = await ctx.db.get(user.accountId);

    if (!account) return null;

    // 3. Optionally, get subscribers count if creator
    let subscribers = undefined;
    if (account.account_type === 'creator') {
      subscribers = await ctx.db
        .query("Subscriptions")
        .filter((q: any) => q.eq(q.field("creator_id"), account.user_id))
        .collect();
      subscribers = subscribers.length;
    }

    // 4. Return merged info
    return {
      convexUserId: user._id, // Convex Auth users._id
      accountId: user.accountId, // Accounts._id
      email: account.email,
      wallet_address: account.wallet_address,
      blockchain: account.blockchain,
      account_type: account.account_type,
      registration_date: account.registration_date,
      user_info: account.user_info,
      user_id: account.user_id,
      subscribers, // Only present if creator
      status: account.user_info.status,
      lastActive: account.user_info.last_active,
    };
  },
});

// Get user by username
export const getUserByUsername = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    const account = await ctx.db
      .query("Accounts")
      .withIndex("by_username", (q) => q.eq("user_info.account.username", args.username.toLowerCase()))
      .first();

    if (!account) return null;

    return {
      _id: account._id,
      user_id: account.user_id,
      email: account.email,
      wallet_address: account.wallet_address,
      blockchain: account.blockchain,
      account_type: account.account_type,
      registration_date: account.registration_date,
      user_info: account.user_info,
    };
  },
});

// -----------------------
// Expose Auth Context to Actions/Queries
// -----------------------
export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  ...authTables,
  providers: [
    //Google({}),
    //Facebook({}),
    //Twitter({}),
    //Discord({}),
    //LinkedIn({}),
    //Reddit({}),
    //Twitch({}),
    /* Resend({
      from: process.env.AUTH_EMAIL ?? "Sugar Club <<EMAIL>>",
    }),
    Apple({
      clientSecret: process.env.AUTH_APPLE_SECRET!,
      client: {
        token_endpoint_auth_method: "client_secret_post",
      },
      profile: undefined,
    }), */
    //ResendOTP,
    //TwilioVerify,
    //TwilioOTP,
    //Password,

    //Anonymous,
    WalletProvider,
    customCredentialProvider,
  ],
});