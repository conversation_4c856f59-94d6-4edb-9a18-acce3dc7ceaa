{"cSpell.words": ["agoralabs", "algorand", "algorandfoundation", "algosdk", "apexcharts", "arcjet", "azzie", "bbox", "<PERSON><PERSON>", "bitcore", "blake<PERSON>s", "blockshake", "cmdk", "connex", "contenteditable", "cosmjs", "Cronos", "crossmarkio", "cubensis", "defly", "e<PERSON>io", "e<PERSON><PERSON><PERSON>", "filerobot", "galium", "gemwallet", "Gifs", "giphy", "greymass", "headlessui", "<PERSON><PERSON>", "hexxagon", "jvectormap", "keystonehq", "konva", "lightbox", "linea", "Linea", "mapbox", "microalgos", "Microalgos", "multiformats", "napi", "noanimation", "notif", "onesignal", "onlyme", "orangu", "<PERSON><PERSON><PERSON><PERSON>", "PGRST", "picmo", "polkadot", "postbuild", "<PERSON><PERSON><PERSON>", "Reposts", "rubus", "<PERSON><PERSON>", "satoshis", "scure", "sidan", "solana", "stargate", "supabase", "tabler", "Tagify", "tendermint", "thorify", "tiktok", "tippy<PERSON>s", "toruslabs", "tronwallet", "tronweb", "TURBOPACK", "txnlab", "uploadthing", "utapi", "vechain", "vidstack", "viem", "wagmi", "wharfkit", "xrpl", "yaireo", "zxcvbn"], "Codegeex.GenerationPreference": "automatic", "Codegeex.Chat.LanguagePreference": "English", "Codegeex.SidebarUI.LanguagePreference": "English", "Codegeex.CommitMessage.LanguagePreference": "English", "Codegeex.Comment.LanguagePreference": "English", "Codegeex.RepoIndex": true}