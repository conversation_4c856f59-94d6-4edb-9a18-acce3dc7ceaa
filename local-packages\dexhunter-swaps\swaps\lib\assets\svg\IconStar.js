import { jsxs as e, jsx as C } from "react/jsx-runtime";
import { memo as o } from "react";
const l = (L) => /* @__PURE__ */ e(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 17 17",
    fill: "none",
    ...L,
    children: [
      /* @__PURE__ */ C(
        "path",
        {
          fillRule: "evenodd",
          clipRule: "evenodd",
          d: "M15.3571 2.49908C15.305 2.52278 15.2668 2.57916 15.2668 2.64639C15.2668 2.71507 15.2951 2.76801 15.3355 2.81338C15.3689 2.85078 15.4093 2.88177 15.4486 2.912L15.4486 2.912L15.4486 2.912L15.4764 2.93348L15.4473 2.9703L15.4213 2.95025C15.3817 2.91991 15.3369 2.88556 15.3003 2.84452C15.2546 2.79326 15.2197 2.7297 15.2197 2.64639C15.2197 2.56162 15.2679 2.4881 15.3375 2.45643C15.4034 2.42646 15.4846 2.4355 15.5572 2.50016C15.5515 2.50525 15.5459 2.51068 15.5403 2.51647L15.5572 2.53272L15.5403 2.54898C15.4757 2.48215 15.4077 2.47605 15.3571 2.49908ZM15.666 2.96879L15.638 2.93348L15.6658 2.91199C15.7052 2.88177 15.7456 2.85078 15.7789 2.81338C15.8194 2.76801 15.8476 2.71507 15.8476 2.64639C15.8476 2.57916 15.8095 2.52278 15.7574 2.49908C15.7068 2.47605 15.6388 2.48215 15.5742 2.54898L15.5572 2.53272L15.5742 2.51647C15.5686 2.51068 15.5629 2.50525 15.5572 2.50016C15.6298 2.4355 15.7111 2.42646 15.777 2.45643C15.8465 2.4881 15.8947 2.56162 15.8947 2.64639C15.8947 2.7297 15.8599 2.79326 15.8141 2.84452C15.782 2.88056 15.7435 2.91144 15.7078 2.93897L15.7042 2.93348C15.6909 2.94603 15.6782 2.95789 15.666 2.96879ZM15.6022 3.01691C15.6084 3.0135 15.6148 3.00944 15.6215 3.00471C15.6359 2.99466 15.6506 2.98249 15.666 2.96879L15.6672 2.9703L15.6932 2.95024L15.6932 2.95024L15.6932 2.95024C15.698 2.94654 15.7029 2.94279 15.7078 2.93897L15.7572 3.01394C15.728 3.04152 15.6966 3.07093 15.6648 3.09317C15.6591 3.09715 15.6532 3.10099 15.6473 3.10463V3.03102C15.6342 3.03102 15.6195 3.02653 15.6022 3.01691ZM15.5572 3.10463C15.5513 3.10099 15.5454 3.09715 15.5397 3.09317C15.5079 3.07093 15.4765 3.04152 15.4473 3.01394L15.5003 2.93348C15.5303 2.9618 15.5571 2.98666 15.5829 3.00471C15.5897 3.00944 15.5961 3.0135 15.6022 3.01691C15.585 3.02653 15.5703 3.03102 15.5572 3.03102V3.10463ZM15.5572 3.10463C15.5715 3.1133 15.5865 3.12077 15.6022 3.12593C15.5878 3.13068 15.5727 3.13348 15.5572 3.13348V3.10463ZM15.6022 3.12593C15.618 3.12077 15.633 3.1133 15.6473 3.10463V3.13348C15.6317 3.13348 15.6167 3.13068 15.6022 3.12593ZM15.6403 2.55382L15.5403 2.65784C15.5664 2.68722 15.6025 2.70382 15.6403 2.70382C15.678 2.70382 15.7141 2.68722 15.7403 2.65784L15.6403 2.55382Z",
          fill: "currentColor"
        }
      ),
      /* @__PURE__ */ C(
        "path",
        {
          fillRule: "evenodd",
          clipRule: "evenodd",
          d: "M6.36486 3.55629C7.31484 1.8521 7.78982 1 8.49999 1C9.21017 1 9.68514 1.85209 10.6351 3.55628L10.8809 3.99718C11.1509 4.48145 11.2858 4.7236 11.4963 4.88336C11.7068 5.04313 11.9688 5.10243 12.4931 5.22104L12.9704 5.32903C14.8151 5.74643 15.7375 5.95512 15.9569 6.66081C16.1764 7.36646 15.5476 8.10184 14.2899 9.57244L13.9646 9.95291C13.6072 10.3708 13.4285 10.5798 13.3481 10.8383C13.2677 11.0968 13.2947 11.3756 13.3487 11.9332L13.3979 12.4408C13.5881 14.403 13.6832 15.384 13.1087 15.8202C12.5341 16.2563 11.6705 15.8586 9.94322 15.0634L9.49637 14.8576C9.00557 14.6316 8.76017 14.5186 8.49999 14.5186C8.23982 14.5186 7.99442 14.6316 7.50362 14.8576L7.05677 15.0634C5.32951 15.8586 4.46588 16.2563 3.89136 15.8202C3.31684 15.384 3.41191 14.403 3.60205 12.4408L3.65123 11.9332C3.70527 11.3756 3.73229 11.0968 3.65189 10.8383C3.57151 10.5798 3.39282 10.3708 3.03544 9.95291L2.71007 9.57244C1.45244 8.10184 0.823617 7.36646 1.04307 6.66081C1.26252 5.95512 2.18489 5.74643 4.02965 5.32903L4.50691 5.22104C5.03113 5.10243 5.29324 5.04313 5.5037 4.88336C5.71416 4.7236 5.84914 4.48146 6.1191 3.99718L6.36486 3.55629Z",
          stroke: "#4D5259",
          strokeWidth: 1.5
        }
      )
    ]
  }
), i = o(l);
export {
  i as default
};
