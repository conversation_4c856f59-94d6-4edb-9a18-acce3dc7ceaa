import React from "react";

export const CreditCardIcon = ({ className = "w-10 h-8" }: { className?: string }) => (
  <div className={`${className} bg-purple-600 rounded-md flex flex-col items-center justify-center overflow-hidden`}>
    <div className="w-full h-1/3 bg-purple-500"></div>
    <div className="w-8/12 h-1 bg-white/50 rounded-sm my-1 mx-auto"></div>
    <div className="flex space-x-1 mx-auto">
      <div className="w-1.5 h-1 bg-white/30 rounded-full"></div>
      <div className="w-1.5 h-1 bg-white/30 rounded-full"></div>
    </div>
  </div>
);

export const PayPalIcon = ({ className = "w-10 h-8" }: { className?: string }) => (
  <div className={`${className} bg-yellow-400 rounded-md flex items-center justify-center`}>
    <div className="flex items-center">
      <span className="text-blue-700 font-bold text-lg">P</span>
      <span className="text-blue-800 font-bold text-lg">P</span>
    </div>
  </div>
);

export const CryptoIcon = ({ className = "w-10 h-8" }: { className?: string }) => (
  <div className={`${className} bg-orange-400 rounded-md flex items-center justify-center`}>
    <span className="text-white font-bold text-lg">₿</span>
  </div>
);