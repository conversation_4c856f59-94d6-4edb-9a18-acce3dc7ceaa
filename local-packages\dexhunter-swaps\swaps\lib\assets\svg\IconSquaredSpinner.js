import { jsxs as t, jsx as e } from "react/jsx-runtime";
import { memo as o } from "react";
const i = (r) => /* @__PURE__ */ t(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    id: "Rectangle",
    width: "1em",
    height: "1em",
    viewBox: "0 0 43 43",
    fill: "none",
    ...r,
    children: [
      /* @__PURE__ */ e(
        "rect",
        {
          x: 21.6891,
          y: 1,
          width: 29.2589,
          height: 29.2589,
          rx: 5,
          transform: "rotate(45 21.6891 1)",
          stroke: "url(#paint0_linear_0_3)",
          strokeWidth: 5,
          strokeDasharray: "0,0,98.48400806389233,9.16626049079517",
          children: /* @__PURE__ */ e(
            "animate",
            {
              attributeType: "XML",
              attributeName: "stroke-dasharray",
              repeatCount: "indefinite",
              dur: "2s",
              values: "0,0,98.48400806389233,9.16626049079517;              0,9.16626049079517,98.48400806389233,0;              98.48400806389233,9.16626049079517,0,0",
              keyTimes: "0; 0.08514851485148511; 1",
              begin: "0.09s"
            }
          )
        }
      ),
      /* @__PURE__ */ e("defs", { children: /* @__PURE__ */ t(
        "linearGradient",
        {
          id: "paint0_linear_0_3",
          x1: 21.6891,
          y1: 1,
          x2: 21.6891,
          y2: 30.2589,
          gradientUnits: "userSpaceOnUse",
          children: [
            /* @__PURE__ */ e("stop", { stopColor: "currentColor" }),
            /* @__PURE__ */ e("stop", { offset: 1, stopColor: "currentColor" })
          ]
        }
      ) })
    ]
  }
), a = o(i);
export {
  a as default
};
