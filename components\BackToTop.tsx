/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useEffect, useState, useRef } from 'react';

const BackToTop = () => {
    const toTopRef = useRef(null) as any;
    const [isVisible, setIsVisible] = useState(false);

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 500) { // adjust the threshold value as needed
                setIsVisible(true);
            } else {
                setIsVisible(false);
            }
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    useEffect(() => {
        const interval = setInterval(() => {
            if (toTopRef.current) {
                toTopRef.current?.classList.add('hover');
                setTimeout(() => {
                    toTopRef?.current?.classList.remove('hover');
                }, 1000); // Duration of the shine animation
            }
        }, 5000); // Interval between automatic shines

        return () => clearInterval(interval);
    }, [toTopRef]);

    return (
        <div id="to-top" onClick={scrollToTop} className={`to-top ${isVisible ? 'visible' : 'hidden'}`}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style={{ fill: '#fff', width: '20px', height: '20px' }}>
                <path d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z" />
            </svg>
        </div>
    );
};
export default BackToTop;