import { useState, useEffect } from 'react';
import { useSocket } from '@/context/SocketContext';

export function usePing() {
  const { socket, isConnected } = useSocket();
  const [lastPongTime, setLastPongTime] = useState<Date | null>(null);
  const [isPinging, setIsPinging] = useState(false);

  useEffect(() => {
    const handlePong = () => {
      setLastPongTime(new Date());
      setIsPinging(false);
    };

    // Listen for the socket.io event instead of DOM event
    if (socket) {
      socket.on('client-pong', handlePong);

      return () => {
        socket.off('client-pong', handlePong);
      };
    }
  }, [socket]);

  const sendPing = () => {
    if (socket && isConnected) {
      console.log('Sending ping to server');
      setIsPinging(true);
      socket.emit('client-ping');
      return true;
    }
    return false;
  };

  return {
    sendPing,
    lastPongTime,
    isPinging,
    isConnected
  };
}
