import { orderTypesProps } from '../store/createSwapSlice';
import { supportedTokensType } from './components/tokens';
import { SelectedWallet } from '../typescript/cardano-api';
export type defaultSettingsProps = {
    isCustomSlippage?: boolean;
    isAutomaticSlippage?: boolean;
    slippage?: number;
};
interface SwapPageProps {
    defaultToken?: string;
    tokenIdSell?: string;
    tokenIdBuy?: string;
    orderTypes?: orderTypesProps;
    supportedTokens?: supportedTokensType;
    partnerName: string;
    partnerCode: string;
    onSwapSuccess?: (data: any) => void;
    onSwapError?: (err: any) => void;
    selectedWallet?: SelectedWallet;
    inputs?: string[];
    onWalletConnect?: (data: any) => void;
    onClickWalletConnect?: () => void;
    onViewOrder?: (data: any) => void;
    orderTypeOnButtonClick?: 'SWAP' | 'LIMIT' | 'DCA';
    defaultSettings?: defaultSettingsProps;
    autoFocus?: boolean;
}
declare const SwapPage: ({ defaultToken, tokenIdSell, tokenIdBuy, orderTypes, supportedTokens, partnerName, partnerCode, onSwapSuccess, onSwapError, selectedWallet, inputs, onWalletConnect, onClickWalletConnect, onViewOrder, orderTypeOnButtonClick, defaultSettings, autoFocus }: SwapPageProps) => import("react/jsx-runtime").JSX.Element;
export default SwapPage;
