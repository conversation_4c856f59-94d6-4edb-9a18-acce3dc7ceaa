'use client';

import React, { useState, useRef, useEffect } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';
import { cn } from '@/lib/utils';
import { Plus, X, Move, Pencil } from 'lucide-react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import dynamic from 'next/dynamic';
import { Progress } from '@/components/ui/progress';
import { MediaItem } from '@/types/post';
import Tippy from '@tippyjs/react';
import ImageEditDialog from './ImageEditDialog';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';

// Maximum number of images allowed
const MAX_IMAGES = 10;

interface MultiImageUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialImages?: MediaItem[];
  onSave: (images: MediaItem[]) => void;
}

type DragItem = {
  index: number;
  id: string;
  type: string;
};

interface DraggableImageItemProps {
  image: MediaItem;
  index: number;
  moveImage: (dragIndex: number, hoverIndex: number) => void;
  onRemove: () => void;
  onEdit: () => void;
};

const DraggableImageItem: React.FC<DraggableImageItemProps> = ({
  image,
  index,
  moveImage,
  onRemove,
  onEdit
}) => {
  const ref = useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'IMAGE_ITEM',
    item: { index, id: image.id, type: 'IMAGE_ITEM' },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'IMAGE_ITEM',
    hover(item: DragItem, monitor) {
      if (!ref.current) return;

      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) return;

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;

      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      const hoverClientX = clientOffset.x - hoverBoundingRect.left;

      // Only perform the move when the mouse has crossed half of the items height/width
      // When dragging downward, only move when the cursor is below 50%
      // When dragging upward, only move when the cursor is above 50%
      // Same logic for left/right movement

      // Time to actually perform the action
      moveImage(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  // Initialize drag and drop refs
  drag(drop(ref));

  return (
    <div
      ref={ref}
      className={cn(
        "relative border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden h-52",
        isDragging ? "opacity-50" : "opacity-100",
        image.edited ? "ring-2 ring-turquoise" : ""
      )}
    >
      <img
        src={image.editedUrl || image.mediaUrl}
        alt={`Upload preview ${index + 1}`}
        className="w-full h-full object-cover"
      />

      {/* Drag handle */}
      <div className="absolute top-2 left-2 bg-black/50 rounded-full p-1">
        <Tippy content="Drag to reorder" placement="top" arrow={true} theme="sugar">
          <Move className="h-4 w-4 text-white cursor-move" />
        </Tippy>
      </div>

      {/* Edit Button */}
      <Tippy content="Edit Image" placement="top" arrow={true} theme="sugar">
        <div className="absolute top-2 right-12">
          <Button
            onClick={onEdit}
            size="sm"
            className="bg-black/50 hover:bg-black/55 rounded-full h-6 w-6 p-0"
          >
            <Pencil width={16} height={16} className="h-4 w-4 text-white" />
          </Button>
        </div>
      </Tippy>

      {/* Cancel Button */}
      <Tippy content="Remove Image" placement="top" arrow={true} theme="sugar">
        <div className="absolute top-2 right-2">
          <Button
            onClick={onRemove}
            size="sm"
            className="bg-black/50 hover:bg-black/55 rounded-full h-6 w-6 p-0"
          >
            <X width={16} height={16} className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      </Tippy>

      {/* Edited badge */}
      {image.edited && (
        <div className="absolute bottom-2 left-2 bg-turquoise text-white text-xs px-2 py-0.5 rounded-full">
          Edited
        </div>
      )}
    </div>
  );
};

const MultiImageUploadModal: React.FC<MultiImageUploadModalProps> = ({
  isOpen,
  onClose,
  initialImages = [],
  onSave,
}) => {
  const [images, setImages] = useState<MediaItem[]>(initialImages);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const uploadFileMutation = useMutation(api.media.uploadFile);

  const handleAddMoreImages = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Check if adding these files would exceed the limit
    if (images.length + files.length > MAX_IMAGES) {
      toast.error(`You can only upload a maximum of ${MAX_IMAGES} images`);
      return;
    }

    // Process each file
    Array.from(files).forEach(file => {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error(`File ${file.name} is not an image`);
        return;
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        toast.error(`File ${file.name} exceeds 10MB limit`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const url = e.target?.result as string;
        // Use a more unique ID with timestamp and random string
        const uniqueId = `image-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

        setImages(prev => [
          ...prev,
          {
            id: uniqueId,
            file,
            mediaUrl: url,
            mediaType: file.type
          }
        ]);
      };
      reader.readAsDataURL(file);
    });

    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleEditImage = (index: number) => {
    setSelectedImageIndex(index);
    setIsEditModalOpen(true);
  };

  const handleEditedImage = (editedImageData: string, imageIndex: number) => {
    fetch(editedImageData)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], `edited-image-${imageIndex}.jpg`, { type: 'image/jpeg' });
        setImages(prev =>
          prev.map((item, i) =>
            i === imageIndex
              ? { ...item, editedUrl: editedImageData, file, edited: true }
              : item
          )
        );
      })
      .catch(err => {
        console.error("Error converting edited image:", err);
        toast.error("Failed to process edited image");
      });
  };

  const moveImage = (dragIndex: number, hoverIndex: number) => {
    const draggedImage = images[dragIndex];
    setImages(prev => {
      const newImages = [...prev];
      newImages.splice(dragIndex, 1);
      newImages.splice(hoverIndex, 0, draggedImage);
      return newImages;
    });
  };

  const uploadImageToServer = async (file: File): Promise<{ url: string, thumbnailUrl: string, tempMediaId: string } | null> => {
    try {
      // Convert file to base64
      const fileAsBase64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve((reader.result as string).split(',')[1]);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
      // Call Convex mutation
      const result = await uploadFileMutation({
        userId: /* get userId from context or props */ '',
        file: fileAsBase64,
        fileType: file.type,
        fileName: file.name,
        privacy: 'public',
        saveToLibrary: true,
      });

      if (result.success) {
        return {
          url: result.url || '',
          thumbnailUrl: result.thumbnailUrl || '',
          tempMediaId: result.tempMediaId || ''
        };
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(`Upload failed: ${error.message}`);
      return null;
    }
  };

  const handleSave = async () => {
    if (images.length === 0) {
      toast.error('Please add at least one image');
      return;
    }

    setIsUploading(true);

    try {
      // Upload all images that haven't been uploaded yet
      const uploadPromises = images.map(async (image) => {
        // Skip already uploaded images (those that have a tempMediaId)
        if (image.tempMediaId) {
          return image;
        }

        // Upload the image
        const result = await uploadImageToServer(image.file!);

        if (result) {
          // Return a new MediaItem with the server URLs
          return {
            ...image,
            mediaUrl: result.url,
            thumbnailUrl: result.thumbnailUrl,
            tempMediaId: result.tempMediaId,
            // Keep the local URL for display purposes
            localUrl: image.editedUrl || image.mediaUrl
          };
        }

        // If upload failed, return null
        return null;
      });

      // Wait for all uploads to complete
      const uploadedImages = await Promise.all(uploadPromises);

      // Filter out any failed uploads
      const successfulUploads = uploadedImages.filter((img): img is MediaItem => img !== null);

      if (successfulUploads.length === 0) {
        toast.error('All uploads failed');
        setIsUploading(false);
        return;
      }

      if (successfulUploads.length < images.length) {
        toast.warning(`${images.length - successfulUploads.length} images failed to upload`);
      }

      // Pass the uploaded images back to the parent component
      onSave(successfulUploads);
      setIsUploading(false);
      onClose();
    } catch (error: any) {
      console.error('Save error:', error);
      toast.error(`Failed to save images: ${error.message}`);
      setIsUploading(false);
    }
  };

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setImages(initialImages);
      setUploadProgress({});
    } else {
      setSelectedImageIndex(null);
      setIsEditModalOpen(false);
    }
  }, [isOpen, initialImages]);

  return (
    <>
      <AnimatedModal
        open={isOpen}
        onOpenChange={onClose}
        size="5xl"
        title="Upload Media"
        footer={
          <div className="flex flex-col items-center justify-between w-full">
            <div className="w-full flex flex-col items-center justify-center gap-2 mb-4">
              <span className="w-full text-sm text-gray-500 dark:text-white font-medium capitalize">
                {images.length} of {MAX_IMAGES} images selected
              </span>
              <Progress value={(images.length / MAX_IMAGES) * 100} className="w-full h-1" />
            </div>

            {/* Show overall upload progress if uploading */}
            {isUploading && Object.keys(uploadProgress).length > 0 && (
              <div className="w-full mb-4">
                <div className="flex justify-between text-sm mb-1">
                  <span>Uploading images...</span>
                  <span>
                    {Math.round(
                      Object.values(uploadProgress).reduce((sum, val) => sum + val, 0) /
                      Math.max(Object.keys(uploadProgress).length, 1)
                    )}%
                  </span>
                </div>
                <Progress
                  value={
                    Object.values(uploadProgress).reduce((sum, val) => sum + val, 0) /
                    Math.max(Object.keys(uploadProgress).length, 1)
                  }
                  className="w-full h-2"
                />
              </div>
            )}

            <div className="w-full flex items-center justify-between gap-2 mt-2">
              <Button
                onClick={handleSave}
                disabled={images.length === 0 || isUploading}
                className="bg-turquoise hover:bg-turquoise/80 text-white w-full"
              >
                {isUploading ? 'Uploading...' : 'Save'}
              </Button>
              <Button
                onClick={onClose}
                variant="outline"
                className="border-gray-200 dark:border-zinc-700 w-full"
                disabled={isUploading}
              >
                Cancel
              </Button>
            </div>
          </div>
        }
      >
        <DndProvider backend={HTML5Backend}>
          <div className="w-full p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {images.map((image, index) => (
                <DraggableImageItem
                  key={image.id}
                  image={image}
                  index={index}
                  moveImage={moveImage}
                  onRemove={() => handleRemoveImage(index)}
                  onEdit={() => handleEditImage(index)}
                />
              ))}

              {/* Add more images button */}
              {images.length < MAX_IMAGES && (
                <div
                  className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg flex flex-col items-center justify-center p-4 h-52 cursor-pointer hover:border-turquoise dark:hover:border-turquoise transition-colors"
                  onClick={handleAddMoreImages}
                >
                  <Plus className="h-8 w-8 text-gray-400 dark:text-gray-600 mb-2" />
                  <span className="text-sm text-gray-500 dark:text-gray-400">Add More Images</span>
                </div>
              )}
            </div>

            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              multiple
              className="hidden"
            />
          </div>
        </DndProvider>
      </AnimatedModal>

      {/* Image Edit Modal */}
      {selectedImageIndex !== null && (
        <ImageEditDialog
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleEditedImage}
          imageUrl={images[selectedImageIndex]?.editedUrl || images[selectedImageIndex]?.mediaUrl || ''}
          imageIndex={selectedImageIndex}
        />
      )}
    </>
  );
};
export default MultiImageUploadModal;