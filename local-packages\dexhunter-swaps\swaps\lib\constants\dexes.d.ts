export declare const dexes: {
    SUNDAESWAPV3: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    SPLASH: {
        isEnabled: boolean;
        name: string;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    MINSWAPV2: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    MINSWAP: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    AXO: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    WINGRIDER: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    WINGRIDERV2: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    SNEKFUN: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    SPECTRUM: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    SUNDAESWAP: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    VYFI: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
    MUESLISWAP: {
        name: string;
        isEnabled: boolean;
        url: string;
        logo: string;
        code: string;
        image: boolean;
        isStopLossEnabled: boolean;
        isLimitEnabled: boolean;
        isDCAEnabled: boolean;
        isV2: boolean;
    };
};
export declare const DEXHUNTER_DEX_INFO: {
    name: string;
    url: string;
    logo: string;
    code: string;
};
export declare const DEX_NAMES: ("SUNDAESWAPV3" | "SPLASH" | "MINSWAPV2" | "MINSWAP" | "AXO" | "WINGRIDER" | "WINGRIDERV2" | "SNEKFUN" | "SPECTRUM" | "SUNDAESWAP" | "VYFI" | "MUESLISWAP")[];
export type DexName = keyof typeof dexes;
