type DexSplit = {
    amount_in: number;
    price_impact: number;
    dex: string;
    fee: number;
    total_output: number;
    total_output_without_slippage: number;
    pool_fee: number;
};
type SwapDetails = {
    splits: DexSplit[];
    average_price: number;
    total_output: number;
    total_output_without_slippage: number;
    total_input: number;
    total_input_without_slippage: number;
    total_fee: number;
    possible_routes: any;
    batcher_fee: number;
    partner_fee: number;
    price_ab: number;
    price_ba: number;
    deposits: number;
    net_price: number;
    net_price_reverse: number;
    expected_output?: number;
    dexhunter_fee?: number;
};
type orderTypesValue = "SWAP" | "LIMIT" | "DCA";
export type orderTypesProps = [orderTypesValue, ...orderTypesValue[]];
export interface SwapSlice {
    tokenSell: any;
    tokenBuy: any;
    tokenPrice: {
        average_price: number;
        price_ab: number;
        price_ba: number;
    };
    isTokenPriceLoading: boolean;
    sellAmount: number | null;
    dexSplits: DexSplit[];
    swapDetails: SwapDetails;
    isTransactionLoading: boolean;
    isSwapDetailsLoading: boolean;
    isSwapSubmitted: boolean;
    estimationError: string;
    buyAmount: number;
    inputMode: string;
    dexBlacklist: string[];
    bonusOutput: string;
    orderType: string;
    limitPrice: number;
    limitMultiples: number;
    fixedToken?: string;
    orderTypes: orderTypesProps;
    orderTypeOnButtonClick?: 'SWAP' | 'LIMIT' | 'DCA';
    timesAmount: number;
    buyInterval: string;
    intervalLength: number;
    autoFocus: boolean;
    onSwapSuccess?: (data: any) => void;
    onViewOrder?: (data: any) => void;
    onSwapError?: (error: any) => void;
    setTokenSell: (token: any) => void;
    setTokenBuy: (token: any) => void;
    setTokenPrice: (price: number) => void;
    setIsTokenPriceLoading: (loading: boolean) => void;
    flipTokens: () => void;
    setSellAmount: (amount: number | null) => void;
    setDexSplits: (splits: DexSplit[]) => void;
    setIsTransactionLoading: (isTransactionLoading: boolean) => void;
    setSwapDetails: (swapDetails: any) => void;
    setIsSwapDetailsLoading: (isSwapDetailsLoading: boolean) => void;
    setEstimationError: (error: string) => void;
    setBuyAmount: (amount: number) => void;
    setInputMode: (mode: string) => void;
    toggleDexBlacklist: (dexName: string) => void;
    setBonusOutput: (output: string) => void;
    setOrderType: (orderType: string) => void;
    setLimitPrice: (price: number) => void;
    setLimitMultiples: (multiples: number) => void;
    setDexBlacklist: (blacklist: string[]) => void;
    setIsSwapSubmitted: (isSwapSubmitted: boolean) => void;
    resetTokenPrice: () => void;
    setFixedToken: (token: string) => void;
    setOrderTypes: (orderTypes: orderTypesProps) => void;
    setOnSwapSuccess: (callback?: (data: any) => void) => void;
    setOnViewOrder: (callback?: (data: any) => void) => void;
    setOnSwapError: (callback?: (err: any) => void) => void;
    setTimesAmout: (timesAmount: number) => void;
    setBuyInterval: (buyInterval: string) => void;
    setIntervalLength: (intervalLength: number) => void;
    setAutoFocus: (autoFocus: boolean) => void;
}
declare const createSwapSlice: (set: any) => {
    open: boolean;
    searchInput: string;
    inputMode: string;
    tokenSell: null;
    tokenBuy: null;
    tokenPrice: {
        average_price: number;
        price_ab: number;
        price_ba: number;
    };
    sellAmount: null;
    buyAmount: number;
    dexSplits: never[];
    isTokenPriceLoading: boolean;
    isTransactionLoading: boolean;
    isSwapDetailsLoading: boolean;
    isOrderCancelLoading: boolean;
    isSwapSubmitted: boolean;
    swapDetails: SwapDetails;
    estimationError: string;
    dexBlacklist: never[];
    bonusOutput: string;
    orderType: string;
    limitPrice: number;
    limitMultiples: number;
    fixedToken: undefined;
    orderTypes: orderTypesProps;
    onSwapSuccess: undefined;
    onSwapError: undefined;
    onViewOrder: undefined;
    timesAmount: number;
    buyInterval: string;
    intervalLength: number;
    autoFocus: boolean;
    setTokenSell: (token: any) => void;
    setTokenBuy: (token: any) => void;
    setTokenPrice: (price: any) => void;
    setIsTokenPriceLoading: (loading: boolean) => void;
    flipTokens: () => void;
    setSellAmount: (amount: number | null) => void;
    setDexSplits: (splits: DexSplit[]) => void;
    setIsTransactionLoading: (isTransactionLoading: boolean) => void;
    setSwapDetails: (swapDetails: any) => void;
    setIsSwapDetailsLoading: (isSwapDetailsLoading: boolean) => void;
    setEstimationError: (error: string) => void;
    setBuyAmount: (amount: number) => void;
    setInputMode: (mode: string) => void;
    toggleDexBlacklist: (dexName: string) => void;
    setBonusOutput: (output: string) => void;
    setOrderType: (orderType: string) => void;
    setLimitPrice: (price: number) => void;
    setLimitMultiples: (multiples: number) => void;
    setDexBlacklist: (blacklist: string[]) => void;
    setIsSwapSubmitted: (isSwapSubmitted: boolean) => void;
    resetTokenPrice: () => void;
    setFixedToken: (token: string) => void;
    setOrderTypes: (orderTypes: orderTypesProps) => void;
    setOnSwapSuccess: (callback?: ((data: any) => void) | undefined) => void;
    setOnSwapError: (callback?: ((err: any) => void) | undefined) => void;
    setOnViewOrder: (callback?: ((data: any) => void) | undefined) => void;
    setTimesAmout: (timesAmount: number) => void;
    setBuyInterval: (buyInterval: string) => void;
    setIntervalLength: (intervalLength: number) => void;
    setAutoFocus: (autoFocus: boolean) => void;
};
export default createSwapSlice;
