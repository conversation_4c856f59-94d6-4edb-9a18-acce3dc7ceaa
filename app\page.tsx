import { Suspense } from 'react';
import { fetchQuery } from 'convex/nextjs';
import { api } from '@/convex/_generated/api';
import HomeContent from '@/components/home/<USER>';
import { Skeleton } from '@/components/ui/skeleton';
import { Profile } from '@/types/user';

// Fetch initial data from Convex
const getInitialData = async () => {
  // Fetch creators (accounts)
  const { creators } = await fetchQuery(api.accounts.listCreators, { limit: 50, cursor: null });

  // Fetch latest posts from all creators
  const { posts } = await fetchQuery(api.explore.explorePosts, { limit: 50 });

  // Transform creators to Profile[]
  const transformedAccounts = creators?.map((creator: any) => ({
    id: creator.user_id,
    name: creator.user_info?.account?.displayName || 'Anonymous',
    username: creator.user_info?.account?.username || '',
    profilePhoto: creator.user_info?.profilePhoto || '/images/user/default-avatar.webp',
    coverBanner: creator.user_info?.coverBanner || '/images/user/default-banner.webp',
    coverBannerType: creator.user_info?.coverBannerType || 'image',
    stats: {
      followers: creator.followers || 0,
      posts: creator.posts || 0,
    },
    accountType: creator.account_type,
    isVerified: creator.user_info?.account?.is_verified || false,
    online: creator.user_info?.status,
  })) || [];

  // Map posts to ensure contentType is string | undefined (not null)
  const mappedPosts = (posts || []).map((post: any) => ({
    ...post,
    contentType: post.contentType ?? undefined,
  }));

  // Posts are already in the correct shape for HomeContent
  return {
    accounts: transformedAccounts,
    posts: mappedPosts,
  };
};

export default async function HomePage() {
  const { accounts, posts } = await getInitialData();

  return (
    <main className="min-h-screen bg-white dark:bg-[#18181b]">
      <Suspense fallback={<LoadingSkeleton />}>
        <HomeContent initialProfiles={accounts as unknown as Profile[]} initialPosts={posts} />
      </Suspense>
    </main>
  );
};

function LoadingSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex space-x-4">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-8 w-24" />
          ))}
        </div>
        <Skeleton className="h-8 w-32" />
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {[...Array(12)].map((_, i) => (
          <Skeleton key={i} className="h-[300px] w-full" />
        ))}
      </div>
    </div>
  );
};