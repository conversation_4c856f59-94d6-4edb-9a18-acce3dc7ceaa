import { d } from "../index-ca8eb9e1.js";
async function y(e, n) {
  const t = d.<PERSON>uff<PERSON>.from(n, "base64"), r = t.slice(0, 12), a = t.slice(12, t.length - 16), c = t.slice(t.length - 16), o = await window.crypto.subtle.importKey(
    "raw",
    e,
    { name: "AES-GCM" },
    !1,
    ["decrypt"]
  ), i = await window.crypto.subtle.decrypt(
    {
      name: "AES-GCM",
      iv: r,
      additionalData: new Uint8Array(0),
      tagLength: 128
    },
    o,
    new Uint8Array([...a, ...c])
  );
  return new TextDecoder().decode(new Uint8Array(i));
}
export {
  y as decrypt
};
