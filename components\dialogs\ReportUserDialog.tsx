import { useState } from 'react';
import { AnimatedModal } from "@/components/ui/animated-modal";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { SelectDropdown } from "@/components/ui/select-dropdown";
import { toast } from 'react-toastify';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';

interface ReportUserDialogProps {
    isOpen: boolean;
    onClose: () => void;
    userId: string;
    profilePhoto?: string;
}

const ReportUserDialog = ({ isOpen, onClose, userId, profilePhoto }: ReportUserDialogProps) => {
    const [reason, setReason] = useState('');
    const [details, setDetails] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const reportUser = useMutation(api.users.reportUser);

    const handleSubmit = async () => {
        if (!reason) {
            toast.error('Please select a reason for reporting');
            return;
        }

        setIsSubmitting(true);
        try {
            await reportUser({
                userId,
                reason,
                details,
            });
            toast.success('Report submitted successfully');
            onClose();
        } catch (error) {
            toast.error('Failed to submit report');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <AnimatedModal
            open={isOpen}
            onOpenChange={onClose}
            size="md"
            closeButton={true}
            header={null}
            footer={null}
        >
            <div className="flex flex-col items-center justify-center pt-8 pb-2 px-6">
                {/* Profile Picture */}
                <div className="w-32 h-32 rounded-full border-solid border-4 border-white [box-shadow:0_0_2px_rgba(0,_0,_0,_.60)] mb-2 bg-white flex items-center justify-center overflow-hidden">
                    <img
                        src={profilePhoto || '/images/user/default-avatar.webp'}
                        alt="User profile"
                        className="w-full h-full object-cover rounded-full"
                    />
                </div>
                <div className="w-full text-center mb-2">
                    <div className="text-2xl font-bold text-gray-800 dark:text-white mt-2">Report User</div>
                </div>
                <div className="w-full mt-2">
                    <SelectDropdown
                        value={reason}
                        onChange={setReason}
                        options={[
                            { value: 'spam', label: 'Spam' },
                            { value: 'harassment', label: 'Harassment' },
                            { value: 'self-harm', label: 'Self-harm' },
                            { value: 'copyright', label: 'Copyright Infringement' },
                            { value: 'violence', label: 'Violence' },
                            { value: 'impersonation', label: 'Impersonation' },
                            { value: 'other', label: 'Other' },
                        ]}
                        placeholder="Select a reason"
                        className="w-full"
                        buttonClassName="w-full"
                        dropdownClassName="w-full"
                    />
                </div>
                <div className="w-full mt-4">
                    <Textarea
                        placeholder="Tell us why you are reporting this user..."
                        value={details}
                        onChange={(e) => setDetails(e.target.value)}
                        rows={4}
                        className="w-full resize-none"
                    />
                </div>
                <div className="w-full flex flex-row items-center justify-center gap-2 mt-6 mb-2">
                    <Button
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className="w-1/2 bg-turquoise hover:bg-turquoise-hover text-white"
                    >
                        {isSubmitting ? 'Submitting...' : 'Send Report'}
                    </Button>
                    <Button variant="outline" onClick={onClose} className="w-1/2 bg-red-500 hover:bg-red-600 text-white dark:hover:text-white">Cancel</Button>
                </div>
            </div>
        </AnimatedModal>
    );
};
export default ReportUserDialog;

