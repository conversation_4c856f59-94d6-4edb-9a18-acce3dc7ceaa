import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Search for hashtags
export const searchHashtags = query({
    args: {
        query: v.string(),
        limit: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const searchQuery = args.query.toLowerCase().replace('#', '').trim();
        const limit = args.limit || 15;

        if (!searchQuery || searchQuery.length < 2) {
            return { results: { tags: [] } };
        }

        try {
            // Search for hashtags that start and end with the query
        const hashtags = await ctx.db
            .query("Hashtags")
                .filter((q: any) => q.gte(q.field("name"), searchQuery)) // Start with the query
                .filter((q: any) => q.lt(q.field("name"), searchQuery + '\uffff')) // End with the query
                .order("post_count", "desc")
            .take(limit);

            const searchResults = {
                tags: hashtags.map((tag) => ({
                    id: tag.name, // Using the tag name as the ID
                    type: 'tag',
                    name: tag.name,
                    postCount: tag.post_count || 0
                }))
            };

            // If no results found and query is valid, suggest it as a new tag
            if (searchResults.tags.length === 0 && searchQuery.length >= 2) {
                searchResults.tags.push({
                    id: 'new',
                    type: 'tag',
                    name: searchQuery,
                    postCount: 0
                });
            }

            return { results: searchResults };
        } catch (error) {
            console.error('Tag search error:', error);
            throw new Error('Tag search failed');
        }
    },
});

// Get all hashtags (for autocomplete or suggestions)
export const getAllHashtags = query({
    args: {
        limit: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const limit = args.limit || 50;

        try {
            const hashtags = await ctx.db
                .query("Hashtags")
                .order("post_count", "desc")
                .take(limit);

            return {
                tags: hashtags.map((tag) => ({
                    id: tag.name,
                    type: 'tag',
                    name: tag.name,
                    postCount: tag.post_count || 0
                }))
            };
        } catch (error) {
            console.error('Get hashtags error:', error);
            throw new Error('Failed to get hashtags');
        }
    },
});

// Add or update a hashtag
export const addHashtag = mutation({
    args: {
        name: v.string(),
        post_count: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const tagName = args.name.toLowerCase().replace('#', '').trim();

        if (!tagName || tagName.length < 2) {
            throw new Error('Invalid hashtag name');
        }

        try {
            // Check if hashtag already exists
            const existing = await ctx.db
                .query("Hashtags")
                .filter((q: any) => q.eq(q.field("name"), tagName))
                .first();

            if (existing) {
                // Update post count if provided
                if (args.post_count !== undefined) {
                    await ctx.db.patch(existing._id, {
                        post_count: args.post_count
                    });
                }
                return { success: true, id: existing._id, updated: true };
            } else {
                // Create new hashtag
                const id = await ctx.db.insert("Hashtags", {
                    name: tagName,
                    post_count: args.post_count || 1,
                    created_at: new Date().toISOString(),
                    content_id: ""
                });
                return { success: true, id, updated: false };
            }
        } catch (error) {
            console.error('Add hashtag error:', error);
            throw new Error('Failed to add hashtag');
        }
    },
});

// Increment post count for a hashtag
export const incrementHashtagCount = mutation({
    args: {
        name: v.string(),
    },
    handler: async (ctx, args) => {
        const tagName = args.name.toLowerCase().replace('#', '').trim();

        if (!tagName || tagName.length < 2) {
            throw new Error('Invalid hashtag name');
        }

        try {
            // Check if hashtag exists
            const existing = await ctx.db
                .query("Hashtags")
                .filter((q: any) => q.eq(q.field("name"), tagName))
                .first();

            if (existing) {
                // Increment post count
                await ctx.db.patch(existing._id, {
                    post_count: (existing.post_count || 0) + 1
                });
                return { success: true, id: existing._id };
            } else {
                // Create new hashtag with count 1
                const id = await ctx.db.insert("Hashtags", {
                    name: tagName,
                    post_count: 1,
                    created_at: new Date().toISOString(),
                    content_id: ""
                });
                return { success: true, id };
            }
        } catch (error) {
            console.error('Increment hashtag count error:', error);
            throw new Error('Failed to increment hashtag count');
        }
    },
});

// Decrement post count for a hashtag
export const decrementHashtagCount = mutation({
    args: {
        name: v.string(),
    },
    handler: async (ctx, args) => {
        const tagName = args.name.toLowerCase().replace('#', '').trim();

        if (!tagName || tagName.length < 2) {
            throw new Error('Invalid hashtag name');
        }

        try {
            // Check if hashtag exists
            const existing = await ctx.db
                .query("Hashtags")
                .filter((q: any) => q.eq(q.field("name"), tagName))
                .first();

            if (existing) {
                const newCount = Math.max(0, (existing.post_count || 0) - 1);

                if (newCount === 0) {
                    // Delete hashtag if count reaches 0
                    await ctx.db.delete(existing._id);
                    return { success: true, deleted: true };
                } else {
                    // Decrement post count
                    await ctx.db.patch(existing._id, {
                        post_count: newCount
                    });
                    return { success: true, id: existing._id };
                }
            } else {
                return { success: false, error: 'Hashtag not found' };
            }
        } catch (error) {
            console.error('Decrement hashtag count error:', error);
            throw new Error('Failed to decrement hashtag count');
        }
    },
});
