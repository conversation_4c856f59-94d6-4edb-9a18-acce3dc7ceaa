'use client';

import React from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { toast } from 'react-toastify';
import { generateVideoThumbnails } from '@rajesh896/video-thumbnails-generator';

interface ThumbnailSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  thumbnailOptions: string[];
  selectedThumbnailIndex: number;
  setSelectedThumbnailIndex: (index: number) => void;
  onConfirm: () => void;
  tempVideoFile: {
    file: File;
    videoUrl: string;
    thumbnails: string[];
    selectedThumbnail: string;
    thumbnailFile: File | null;
  } | null;
  setThumbnailOptions: React.Dispatch<React.SetStateAction<string[]>>;
}

const ThumbnailSelectionModal: React.FC<ThumbnailSelectionModalProps> = ({
  isOpen,
  onClose,
  thumbnailOptions,
  selectedThumbnailIndex,
  setSelectedThumbnailIndex,
  onConfirm,
  tempVideoFile,
  setThumbnailOptions
}) => {
  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      title="Select Video Thumbnail"
      size="lg"
      footer={
        <div className="flex justify-end gap-2 w-full">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={onConfirm}>
            Confirm Selection
          </Button>
        </div>
      }
    >
      <div className="p-4">
        <p className="text-sm text-gray-500 mb-4">
          Select a thumbnail for your video or use the default (first) thumbnail.
        </p>

        <div className="grid grid-cols-3 gap-3">
          {thumbnailOptions.map((thumbnail, index) => (
            <div
              key={index}
              className={`relative cursor-pointer rounded-md overflow-hidden border-2 transition-all ${selectedThumbnailIndex === index
                ? 'border-turquoise scale-[0.98]'
                : 'border-transparent hover:border-gray-300'
                }`}
              onClick={() => setSelectedThumbnailIndex(index)}
            >
              <img
                src={thumbnail}
                alt={`Thumbnail option ${index + 1}`}
                className="w-full aspect-video object-cover"
              />
              {selectedThumbnailIndex === index && (
                <div className="absolute top-2 right-2 bg-turquoise rounded-full p-1">
                  <Check className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-4">
          <p className="text-sm font-medium">Generate more options:</p>
          <div className="flex items-center gap-2 mt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                if (!tempVideoFile) return;

                try {
                  const newThumbnails = await generateVideoThumbnails(tempVideoFile.file, 6, { type: 'base64' });
                  setThumbnailOptions(prev => [...prev, ...newThumbnails]);
                } catch (error) {
                  console.error('Error generating additional thumbnails:', error);
                  toast.error('Failed to generate additional thumbnails');
                }
              }}
            >
              Generate More Thumbnails
            </Button>
          </div>
        </div>
      </div>
    </AnimatedModal>
  );
};

export default ThumbnailSelectionModal;