const SearchBar = () => {
  return (
    <div className="relative max-w-2xl mx-auto mt-4 mb-16 border border-solid border-slate-200 rounded-full p-4 bg-transparent shadow-md">
      <div className="flex items-center space-x-4">
        <select
          aria-label="Select category"
          className="h-10 w-40 rounded-full border border-slate-200 px-4 text-sm text-slate-500 outline-none"
        >
          <option value="">Select Category</option>
          <option value="category1">Category 1</option>
          <option value="category2">Category 2</option>
          <option value="category3">Category 3</option>
        </select>
        <div className="relative flex-1">
          <input
            id="search-input"
            type="search"
            name="search"
            placeholder="Search here"
            aria-label="Search content"
            className="peer relative h-10 w-full rounded-full border border-slate-200 px-4 pr-12 text-sm text-slate-500 outline-none transition-all autofill:bg-white invalid:border-pink-500 invalid:text-pink-500 focus:border-[#5CC6D0] focus:outline-none invalid:focus:border-pink-500 focus-visible:outline-none disabled:cursor-not-allowed disabled:bg-slate-50 disabled:text-slate-400"
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="absolute right-4 top-2.5 h-5 w-5 cursor-pointer stroke-slate-400 peer-disabled:cursor-not-allowed"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="1.5"
            aria-hidden="true"
            aria-label="Search icon"
            role="graphics-symbol"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"
            />
          </svg>
        </div>
      </div>
    </div>
  )
}
export default SearchBar;