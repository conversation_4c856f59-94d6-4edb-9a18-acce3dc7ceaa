export interface TokenSearchSlice {
    open: boolean;
    searchInput: string;
    openModal: () => void;
    closeModal: (callback?: () => void) => void;
    toggleModal: () => void;
    setSearchInput: (input: string) => void;
}
declare const createTokenSearchSlice: (set: any) => {
    open: boolean;
    searchInput: string;
    openModal: () => void;
    closeModal: (callback?: () => void) => void;
    toggleModal: () => void;
    setSearchInput: (input: string) => void;
};
export default createTokenSearchSlice;
