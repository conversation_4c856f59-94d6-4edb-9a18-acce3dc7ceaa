import { forwardRef as w, createElement as s } from "react";
var p = {
  xmlns: "http://www.w3.org/2000/svg",
  width: 24,
  height: 24,
  viewBox: "0 0 24 24",
  fill: "none",
  stroke: "currentColor",
  strokeWidth: 2,
  strokeLinecap: "round",
  strokeLinejoin: "round"
};
const g = (e) => e.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase(), h = (e, n) => {
  const o = w(
    ({ color: c = "currentColor", size: r = 24, strokeWidth: a = 2, absoluteStrokeWidth: i, children: t, ...u }, d) => s(
      "svg",
      {
        ref: d,
        ...p,
        width: r,
        height: r,
        stroke: c,
        strokeWidth: i ? Number(a) * 24 / Number(r) : a,
        className: `lucide lucide-${g(e)}`,
        ...u
      },
      [
        ...n.map(([l, m]) => s(l, m)),
        ...(Array.isArray(t) ? t : [t]) || []
      ]
    )
  );
  return o.displayName = `${e}`, o;
};
var f = h;
export {
  f as c
};
