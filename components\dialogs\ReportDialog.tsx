import React, { useState } from 'react';
import { useReportPost } from '@/hooks/convexHooks';
import { DialogRoot as Dialog } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';

const ReportDialog = ({ open, onClose, postId }: any) => {
  const reportPost = useReportPost();
  const [reason, setReason] = useState('');

  const handleSubmit = async () => {
    try {
      await reportPost({ postId, reason });
      toast.success('Report submitted successfully!');
      onClose();
    } catch (error: any) {
      toast.error(`Failed to submit report: ${error.message}`);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <div className="p-4">
        <h2 className="text-lg font-bold">Report Post</h2>
        <textarea
          className="w-full h-24 p-2 border rounded"
          placeholder="Please provide a reason for reporting this post..."
          value={reason}
          onChange={(e) => setReason(e.target.value)}
        />
        <div className="flex justify-end mt-4">
          <Button onClick={onClose} variant="outline" className="mr-2">
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!reason}>
            Submit Report
          </Button>
        </div>
      </div>
    </Dialog>
  );
};
export default ReportDialog;