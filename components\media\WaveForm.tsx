'use client';

import { useRef, useEffect } from 'react';
import useSize from '@/hooks/useSize';

interface AnalyzerData {
  analyser: AnalyserNode;
  bufferLength: number;
  dataArray: Uint8Array;
}

interface WaveFormProps {
  analyzerData: AnalyzerData;
  isPlaying: boolean;
}

const WaveForm: React.FC<WaveFormProps> = ({ analyzerData, isPlaying }) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const { dataArray, analyser, bufferLength } = analyzerData;
  const [width, height] = useSize();

  const draw = () => {
    const canvas = canvasRef.current;
    if (!canvas || !analyser) return;

    const canvasCtx = canvas.getContext('2d') as CanvasRenderingContext2D;
    canvasCtx.clearRect(0, 0, canvas.width, canvas.height);

    if (!isPlaying) return;

    analyser.getByteFrequencyData(dataArray);

    const HEIGHT = canvas.height / 2;
    const barWidth = Math.ceil(canvas.width / bufferLength) * 2.5;
    let x = 0;

    for (let i = 0; i < bufferLength; i++) {
      const barHeight = (dataArray[i] / 255) * HEIGHT;
      const intensity = dataArray[i] / 255;
      const turquoiseShade = `rgba(0, 180, 216, ${Math.max(0.3, intensity)})`;
      canvasCtx.fillStyle = turquoiseShade;
      canvasCtx.fillRect(x, HEIGHT - barHeight, barWidth, barHeight);
      x += barWidth + 1;
    }
  };

  const animate = () => {
    draw();
    if (isPlaying) {
      animationFrameRef.current = requestAnimationFrame(animate);
    }
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      canvas.width = width || canvas.parentElement?.clientWidth || 300;
      canvas.height = height || 64;
    }

    animate();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [width, height, isPlaying, dataArray, analyser, bufferLength]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 0,
      }}
    />
  );
};
export default WaveForm;