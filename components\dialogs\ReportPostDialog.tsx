import React, { useState } from 'react';
import { useReportPost } from '@/hooks/convexHooks';
import { DialogRoot as Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import {
  RadioGroup,
  RadioGroupItem
} from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';

const ReportDialog = ({ open, onClose, postId }: any) => {
  const reportPost = useReportPost();
  const [reportReason, setReportReason] = useState('spam');
  const [isReporting, setIsReporting] = useState(false);

  const handleReport = async () => {
    setIsReporting(true);
    try {
      await reportPost({ postId, reason: reportReason });
      toast.success('Report submitted successfully!');
      onClose();
    } catch (error: any) {
      toast.error(`Failed to submit report: ${error.message}`);
    } finally {
      setIsReporting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Report Post</DialogTitle>
          <DialogDescription>
            Please select a reason for reporting this post.
          </DialogDescription>
        </DialogHeader>

        <DialogContent className="space-y-4">
          <RadioGroup value={reportReason} onValueChange={setReportReason}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="spam" id="spam" />
              <Label htmlFor="spam">Spam</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="harassment" id="harassment" />
              <Label htmlFor="harassment">Harassment</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="inappropriate" id="inappropriate" />
              <Label htmlFor="inappropriate">Inappropriate Content</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="other" id="other" />
              <Label htmlFor="other">Other</Label>
            </div>
          </RadioGroup>
        </DialogContent>

        <DialogFooter>
          <Button
            onClick={handleReport}
            disabled={isReporting}
          >
            {isReporting ? 'Reporting...' : 'Submit Report'}
          </Button>
          <Button
            variant="outline"
            onClick={() => onClose()}
            disabled={isReporting}
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default ReportDialog;