export declare const formatBalance: (balance: string | number | null, digits?: number) => string;
export declare const formatUsd: (value: number) => string;
export declare const roundNumber: (value: any) => any;
export declare const formatInput: (value: any) => any;
export declare const formatZeros: (value: any) => any;
export declare const formatNumber: (num: number) => string;
/**
 * Returns a lovelace formatted as a number, for UI porpuses for logic porpuses use simpleFormatLovelace.
 *
 * @param {number | string} lovelaceNum The number to format.
 * @param {boolean} fullPrice Returns the full price without abreviations / for logic porpuses.
 * @param {boolean} noAbreviation Returns the full price with decimals.
 * @return {number} The number formatted if none of the above options are selected it returns it abreviated and wihtout decimals.
 */
export declare const formatLovelace: (lovelace: string | number | BigInt | null, fullPrice?: boolean, noAbreviation?: boolean) => string;
export declare const toLovelace: (num: string | number) => number;
export declare const fromLoveLace: (num: string | number) => number;
export declare const LovelaceFormatter: {
    formatNumber: (num: number) => string;
    formatLovelace: (lovelace: string | number | BigInt | null, fullPrice?: boolean, noAbreviation?: boolean) => string;
    toLovelace: (num: string | number) => number;
};
export declare const getBonusOutput: (output: any) => string;
export declare const getHighestPriceImpact: (swapDetails: any, isRounded: boolean) => any;
export declare const round: (value: number, precision: number) => number;
export declare const calculateScale: (tickSize: number) => {
    pricescale: number;
    minmov: number;
};
export declare const candleRounding: (num: number) => number;
export declare const convertFromPercentage: (percentage: number) => number;
