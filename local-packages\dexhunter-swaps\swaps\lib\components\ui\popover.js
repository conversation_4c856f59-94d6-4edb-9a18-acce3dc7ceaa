import { jsx as C } from "react/jsx-runtime";
import * as y from "react";
import { createElement as d, forwardRef as v, useRef as $, useEffect as T, useState as k, useCallback as h } from "react";
import { _ as f, a as _, $ as N } from "../../index-1c873780.js";
import { $ as P, a as S } from "../../index-c7156e07.js";
import { $ as K } from "../../index-563d1ed8.js";
import { $ as z, a as G } from "../../index-67500cd3.js";
import { h as L, $ as j, a as B, b as H } from "../../index-27cadef5.js";
import { $ as U } from "../../index-4914f99c.js";
import { $ as A, a as V, b as Z, c as q } from "../../index-0ce202b9.js";
import { $ as R } from "../../index-5116e957.js";
import { $ as J } from "../../index-c8f2666b.js";
import { cn as Q } from "../../lib.js";
import "../../index-bcfeaad9.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const F = "Popover", [E, Re] = K(F, [
  A
]), O = A(), [W, l] = E(F), X = (e) => {
  const { __scopePopover: a, children: n, open: s, defaultOpen: t, onOpenChange: o, modal: c = !1 } = e, r = O(a), i = $(null), [u, m] = k(!1), [g = !1, p] = S({
    prop: s,
    defaultProp: t,
    onChange: o
  });
  return /* @__PURE__ */ d(Z, r, /* @__PURE__ */ d(W, {
    scope: a,
    contentId: U(),
    triggerRef: i,
    open: g,
    onOpenChange: p,
    onOpenToggle: h(
      () => p(
        (x) => !x
      ),
      [
        p
      ]
    ),
    hasCustomAnchor: u,
    onCustomAnchorAdd: h(
      () => m(!0),
      []
    ),
    onCustomAnchorRemove: h(
      () => m(!1),
      []
    ),
    modal: c
  }, n));
}, Y = "PopoverTrigger", ee = /* @__PURE__ */ v((e, a) => {
  const { __scopePopover: n, ...s } = e, t = l(Y, n), o = O(n), c = _(a, t.triggerRef), r = /* @__PURE__ */ d(J.button, f({
    type: "button",
    "aria-haspopup": "dialog",
    "aria-expanded": t.open,
    "aria-controls": t.contentId,
    "data-state": I(t.open)
  }, s, {
    ref: c,
    onClick: P(e.onClick, t.onOpenToggle)
  }));
  return t.hasCustomAnchor ? r : /* @__PURE__ */ d(q, f({
    asChild: !0
  }, o), r);
}), w = "PopoverPortal", [oe, te] = E(w, {
  forceMount: void 0
}), ce = (e) => {
  const { __scopePopover: a, forceMount: n, children: s, container: t } = e, o = l(w, a);
  return /* @__PURE__ */ d(oe, {
    scope: a,
    forceMount: n
  }, /* @__PURE__ */ d(R, {
    present: n || o.open
  }, /* @__PURE__ */ d(z, {
    asChild: !0,
    container: t
  }, s)));
}, b = "PopoverContent", re = /* @__PURE__ */ v((e, a) => {
  const n = te(b, e.__scopePopover), { forceMount: s = n.forceMount, ...t } = e, o = l(b, e.__scopePopover);
  return /* @__PURE__ */ d(R, {
    present: s || o.open
  }, o.modal ? /* @__PURE__ */ d(ne, f({}, t, {
    ref: a
  })) : /* @__PURE__ */ d(ae, f({}, t, {
    ref: a
  })));
}), ne = /* @__PURE__ */ v((e, a) => {
  const n = l(b, e.__scopePopover), s = $(null), t = _(a, s), o = $(!1);
  return T(() => {
    const c = s.current;
    if (c)
      return L(c);
  }, []), /* @__PURE__ */ d(j, {
    as: N,
    allowPinchZoom: !0
  }, /* @__PURE__ */ d(D, f({}, e, {
    ref: t,
    trapFocus: n.open,
    disableOutsidePointerEvents: !0,
    onCloseAutoFocus: P(e.onCloseAutoFocus, (c) => {
      var r;
      c.preventDefault(), o.current || (r = n.triggerRef.current) === null || r === void 0 || r.focus();
    }),
    onPointerDownOutside: P(e.onPointerDownOutside, (c) => {
      const r = c.detail.originalEvent, i = r.button === 0 && r.ctrlKey === !0, u = r.button === 2 || i;
      o.current = u;
    }, {
      checkForDefaultPrevented: !1
    }),
    onFocusOutside: P(
      e.onFocusOutside,
      (c) => c.preventDefault(),
      {
        checkForDefaultPrevented: !1
      }
    )
  })));
}), ae = /* @__PURE__ */ v((e, a) => {
  const n = l(b, e.__scopePopover), s = $(!1), t = $(!1);
  return /* @__PURE__ */ d(D, f({}, e, {
    ref: a,
    trapFocus: !1,
    disableOutsidePointerEvents: !1,
    onCloseAutoFocus: (o) => {
      var c;
      if ((c = e.onCloseAutoFocus) === null || c === void 0 || c.call(e, o), !o.defaultPrevented) {
        var r;
        s.current || (r = n.triggerRef.current) === null || r === void 0 || r.focus(), o.preventDefault();
      }
      s.current = !1, t.current = !1;
    },
    onInteractOutside: (o) => {
      var c, r;
      (c = e.onInteractOutside) === null || c === void 0 || c.call(e, o), o.defaultPrevented || (s.current = !0, o.detail.originalEvent.type === "pointerdown" && (t.current = !0));
      const i = o.target;
      ((r = n.triggerRef.current) === null || r === void 0 ? void 0 : r.contains(i)) && o.preventDefault(), o.detail.originalEvent.type === "focusin" && t.current && o.preventDefault();
    }
  }));
}), D = /* @__PURE__ */ v((e, a) => {
  const { __scopePopover: n, trapFocus: s, onOpenAutoFocus: t, onCloseAutoFocus: o, disableOutsidePointerEvents: c, onEscapeKeyDown: r, onPointerDownOutside: i, onFocusOutside: u, onInteractOutside: m, ...g } = e, p = l(b, n), x = O(n);
  return B(), /* @__PURE__ */ d(H, {
    asChild: !0,
    loop: !0,
    trapped: s,
    onMountAutoFocus: t,
    onUnmountAutoFocus: o
  }, /* @__PURE__ */ d(G, {
    asChild: !0,
    disableOutsidePointerEvents: c,
    onInteractOutside: m,
    onEscapeKeyDown: r,
    onPointerDownOutside: i,
    onFocusOutside: u,
    onDismiss: () => p.onOpenChange(!1)
  }, /* @__PURE__ */ d(V, f({
    "data-state": I(p.open),
    role: "dialog",
    id: p.contentId
  }, x, g, {
    ref: a,
    style: {
      ...g.style,
      "--radix-popover-content-transform-origin": "var(--radix-popper-transform-origin)",
      "--radix-popover-content-available-width": "var(--radix-popper-available-width)",
      "--radix-popover-content-available-height": "var(--radix-popper-available-height)",
      "--radix-popover-trigger-width": "var(--radix-popper-anchor-width)",
      "--radix-popover-trigger-height": "var(--radix-popper-anchor-height)"
    }
  }))));
});
function I(e) {
  return e ? "open" : "closed";
}
const se = X, de = ee, ie = ce, M = re, Fe = se, Ee = de, pe = y.forwardRef(({ className: e, align: a = "center", sideOffset: n = 4, ...s }, t) => /* @__PURE__ */ C(ie, { children: /* @__PURE__ */ C(
  M,
  {
    ref: t,
    align: a,
    sideOffset: n,
    className: Q(
      "z-50 max-w-72 sm:w-auto rounded-2xl bg-gray-106 p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 rounded-2xl backdrop-blur-[13.5px]",
      e
    ),
    style: {
      background: "linear-gradient(to right, #16161D9A, #3B3D4980)"
    },
    ...s
  }
) }));
pe.displayName = M.displayName;
export {
  Fe as Popover,
  pe as PopoverContent,
  Ee as PopoverTrigger
};
