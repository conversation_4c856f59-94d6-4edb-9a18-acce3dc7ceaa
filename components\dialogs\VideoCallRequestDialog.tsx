import React, { useState } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';
import { Phone } from 'lucide-react';
import Image from 'next/image';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface VideoCallRequestDialogProps {
  isOpen: boolean;
  onClose: () => void;
  recipientName: string;
  recipientId: string;
}

const durations = [
  { value: 5, label: '5 Mins' },
  { value: 10, label: '10 Mins' },
  { value: 15, label: '15 Mins' },
  { value: 30, label: '30 Mins' },
];

const VideoCallRequestDialog: React.FC<VideoCallRequestDialogProps> = ({
  isOpen,
  onClose,
  recipientName,
  recipientId,
}) => {
  const [selectedDuration, setSelectedDuration] = useState(5);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSendRequest = async () => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Here you would implement the actual API call to send a video call request
      // const response = await fetch('/api/messages/video-call-request', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ recipientId, duration: selectedDuration }),
      // });

      toast.success(`Video call request sent to ${recipientName}!`);
      onClose();
    } catch (error) {
      toast.error('Failed to send video call request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="md"
      closeButton={false}
      header={null}
      footer={null}
    >
      <div className="relative bg-neutral-100 dark:bg-neutral-800 text-gray-900 dark:text-gray-100 rounded-lg shadow-lg p-6 max-w-md mx-auto">
        {/* Close button */}
        <button
          onClick={onClose}
          className={cn(
            "absolute top-4 right-4 h-9 w-9 rounded-full",
            "bg-neutral-200 dark:bg-neutral-700 text-gray-500 dark:text-gray-300",
            "flex items-center justify-center hover:bg-neutral-300 dark:hover:bg-neutral-600 transition"
          )}
        >
          <X size={22} />
        </button>

        {/* Content */}
        <div className="text-center space-y-6">
          <h2 className="text-lg font-semibold mb-12">
            Send Video Call Request
          </h2>

          {/* Duration selector */}
          <div className="flex rounded-lg overflow-hidden bg-transparent gap-1">
            {durations.map((duration) => (
              <button
                key={duration.value}
                className={cn(
                  "flex-1 py-2 text-center transition-colors",
                  selectedDuration === duration.value
                    ? "bg-gray-500 dark:bg-zinc-500 text-white"
                    : "bg-gray-600 dark:bg-zinc-700 text-gray-300 hover:bg-gray-500/80"
                )}
                onClick={() => setSelectedDuration(duration.value)}
              >
                <div className="flex flex-col items-center justify-center">
                  <div className="relative w-6 h-6 mb-1">
                    {duration.value === 5 && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 10 10"
                        className="injected-svg"
                        data-src="/images/icons/micro-solid/time-three.svg"
                        xmlnsXlink="http://www.w3.org/1999/xlink"
                      >
                        <path
                          fill="currentcolor"
                          fillRule="evenodd"
                          d="M4.646 0.146A0.5 0.5 0 0 1 5 0a5.006 5.006 0 0 1 5 5 0.5 0.5 0 0 1 -0.5 0.5H5a0.5 0.5 0 0 1 -0.5 -0.5V0.5a0.5 0.5 0 0 1 0.146 -0.354ZM3.5 2.5a1 1 0 1 1 -2 0 1 1 0 0 1 2 0Zm-1 6a1 1 0 1 0 0 -2 1 1 0 0 0 0 2ZM6 9a1 1 0 1 1 -2 0 1 1 0 0 1 2 0Zm1.5 -0.5a1 1 0 1 0 0 -2 1 1 0 0 0 0 2ZM2 5a1 1 0 1 1 -2 0 1 1 0 0 1 2 0Z"
                          clipRule="evenodd"
                          strokeWidth={1}
                        />
                      </svg>
                    )}
                    {duration.value === 10 && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 10 10"
                        className="injected-svg"
                        data-src="/images/icons/micro-solid/time-six.svg"
                        xmlnsXlink="http://www.w3.org/1999/xlink"
                      >
                        <path
                          fill="currentcolor"
                          fillRule="evenodd"
                          d="M4.646 0.146A0.5 0.5 0 0 1 5 0a5 5 0 1 1 0 10 0.5 0.5 0 0 1 -0.5 -0.5v-9a0.5 0.5 0 0 1 0.146 -0.354ZM3.5 2.505a1 1 0 1 1 -2 0 1 1 0 0 1 2 0Zm-1 6a1 1 0 1 0 0 -2 1 1 0 0 0 0 2ZM2 5a1 1 0 1 1 -2 0 1 1 0 0 1 2 0Z"
                          clipRule="evenodd"
                          strokeWidth={1}
                        />
                      </svg>
                    )}
                    {duration.value === 15 && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 10 10"
                        className="injected-svg"
                        data-src="/images/icons/micro-solid/time-nine.svg"
                        xmlnsXlink="http://www.w3.org/1999/xlink"
                      >
                        <path
                          fill="currentcolor"
                          fillRule="evenodd"
                          d="M4.646 0.146A0.5 0.5 0 0 1 5 0a5 5 0 1 1 -5 5 0.5 0.5 0 0 1 0.5 -0.5h3.75a0.25 0.25 0 0 0 0.25 -0.25V0.5a0.5 0.5 0 0 1 0.146 -0.354ZM3.25 2.25a1 1 0 1 1 -2 0 1 1 0 0 1 2 0Z"
                          clipRule="evenodd"
                          strokeWidth={1}
                        />
                      </svg>
                    )}
                    {duration.value === 30 && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 10 10"
                        className="injected-svg"
                        data-src="/images/icons/micro-solid/time-midnight.svg"
                        xmlnsXlink="http://www.w3.org/1999/xlink"
                      >
                        <path
                          fill="currentcolor"
                          d="M5 10A5 5 0 1 0 5 0a5 5 0 0 0 0 10Z"
                          strokeWidth={1}
                        />
                      </svg>
                    )}
                  </div>
                  <span className="text-base">{duration.label}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Send button */}
        <div className="mt-[53px]">
          <Button
            onClick={handleSendRequest}
            disabled={isSubmitting}
            className={cn(
              "w-full py-3 rounded-full text-base font-semibold",
              "bg-turquoise hover:bg-turquoise/90 text-white dark:bg-turquoise/90 dark:hover:bg-turquoise/90",
              isSubmitting && "opacity-50 cursor-not-allowed"
            )}
          >
           <Phone className="w-4 h-4 text-muted-foreground dark:text-white mr-1" /> {isSubmitting ? "Requesting..." : "Request Video Call"}
          </Button>
        </div>
      </div>
    </AnimatedModal>
  );
};
export default VideoCallRequestDialog;