import { query, mutation } from './_generated/server';
import { v } from 'convex/values';

const isAddress = (address: any) => {
  // Regular expressions for different blockchains
  const regexes = {
    bitcoin: /^(bc1|[13])[a-zA-HJ-NP-Z0-9]{25,42}$/, // Supports legacy (1, 3) and Bech32 (bc1)
    ethereum: /^0x[a-fA-F0-9]{40}$/, // Same for Ethereum, Polygon, BNB, Base, Arbitrum, etc.
    xrp: /^r[1-9A-HJ-NP-Za-km-z]{25,34}$/, // XRP format (starting with 'r')
    cardano: /^addr1[0-9a-zA-Z]{98}$/, // Shelley-style addresses for Cardano
    solana: /^[1-9A-HJ-NP-Za-km-z]{32,44}$/, // Solana addresses are base58
    polygon: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    bnb: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum (BEP-20)
    base: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    blast: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    polkadot: /^(1|5)[a-km-zA-HJ-NP-Z1-9]{47,49}$/, // Polkadot addresses start with '1' or '5'
    arbitrum: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    eos: /^[a-z1-5]{12}$/, // EOS uses lowercase letters and digits from 1 to 5
    avalanche: /^0x[a-fA-F0-9]{40}$/, // C-Chain (Ethereum-like)
    optimism: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    vechain: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    immutable: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    algorand: /^[A-Z2-7]{58}$/, // Algorand addresses are base32
    linea: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    cronos: /^0x[a-fA-F0-9]{40}$/, // Same as Ethereum
    sei: /^sei[0-9a-z]{39}$/, // Sei addresses start with "sei" and are followed by alphanumeric characters
  } as any;

  // Check each blockchain regex pattern
  for (const blockchain in regexes) {
    if (regexes[blockchain].test(address)) {
      return true;
    }
  }
  return false;
};

async function findAccountByUserSubject(ctx: any, userSubject: string) {
  const parts = userSubject.split('|');
  for (const part of parts) {
    const account = await ctx.db
      .query('Accounts')
      .withIndex('by_auth_user_id', (q: any) => q.eq('auth_user_id', part))
      .first();
    if (account) return account;
  }
  // Fallback: try the full subject (in case some old records use it)
  const fallback = await ctx.db
    .query('Accounts')
    .withIndex('by_auth_user_id', (q: any) => q.eq('auth_user_id', userSubject))
    .first();
  return fallback;
};

export const searchUsers = query({
  args: {
    query: v.string(),
    userId: v.optional(v.string()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
    creatorType: v.optional(v.string()),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const q = args.query.trim().toLowerCase();
    if (q.length < 1) {
      return { success: true, data: [], cursor: null, isDone: true };
    }

    const limit = args.limit ?? 15;
    const cursor = args.cursor ?? null;

    const result = await ctx.db
      .query("Accounts")
      .withIndex("by_username")
      .filter(fb => {
        let fil = fb.and(
          fb.gte(fb.field("user_info.account.username"), q),
          fb.lt(fb.field("user_info.account.username"), q + "\uffff")
        );
        if (args.creatorType) {
          fil = fb.and(fil, fb.eq(fb.field("account_type"), args.creatorType));
        }
        if (args.userId) {
          fil = fb.and(fil, fb.neq(fb.field("user_id"), args.userId));
        }
        return fil;
      })
      .paginate({ numItems: limit, cursor });

    const users = await Promise.all(result.page.map(async a => {
      // Fetch content for this user
      const contents = await ctx.db
        .query("Content")
        .filter(q => q.eq(q.field("creator_id"), a.user_id))
        .collect();

      // Count stats
      let photos = 0, videos = 0, clips = 0, gifs = 0, audio = 0;
      for (const c of contents) {
        if (c.content_type?.startsWith("image/") && c.content_type !== "image/gif") {
          photos++;
        } else if (c.content_type === "image/gif") {
          gifs++;
        } else if (c.content_type?.startsWith("audio/")) {
          audio++;
        } else if (c.content_type?.startsWith("video/")) {
          // Check if it's a clip (short video) or regular video
          const duration = c.metadata?.duration;
          if (typeof duration === "number" && duration < 60) {
            clips++;
          } else {
            videos++;
          }
        }
      }

      return {
        id: a.user_id,
        username: a.user_info.account.username,
        display_name: a.user_info.account.displayName,
        email: a.email,
        wallet_address: a.wallet_address,
        type: a.account_type,
        profilePhoto: a.user_info.profilePhoto,
        stats: {
          photos,
          videos,
          clips,
          gifs,
          audio
        }
      };
    }));

    return {
      success: true,
      data: users,
      cursor: result.continueCursor,
      isDone: result.isDone
    };
  }
});

export const getUserSettings = query({
  args: {},
  handler: async (ctx) => {
    console.log("server identity", await ctx.auth.getUserIdentity());
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      throw new Error("User is not authenticated.");
    }

    const account = await findAccountByUserSubject(ctx, identity.subject);

    if (!account) {
      return null;
    }

    // Get follower count
    const followerCount = await ctx.db
      .query("Follows")
      .filter(q => q.eq(q.field("followed_id"), account.user_id))
      .collect()
      .then(follows => follows.length);

    return {
      ...account,
      subscribers: account.account_type === 'creator' ? followerCount : 0
    };
  },
});

export const updateUserSettings = mutation({
  args: {
    section: v.string(),
    data: v.any(),
  },
  handler: async (ctx, { section, data }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("User is not authenticated.");
    const userId = identity.subject;
    const account = await ctx.db
      .query("Accounts")
      .filter((q) => q.eq(q.field("user_id"), userId))
      .unique();
    if (!account) throw new Error("Account not found");
    let updatedUserInfo = { ...account.user_info };

    // Validate allowed keys for privacy and notifications
    if (section === 'privacy') {
      const validPrivacySettings = [
        'accountVisibility', 'profilePrivacy', 'hideSubscribers', 'locationPrivacy',
        'searchVisibility', 'directMessages', 'commentSettings', 'taggingMentions',
        'storyVisibility', 'sensitiveContent'
      ];
      const invalidSettings = Object.keys(data).filter(key => !validPrivacySettings.includes(key));
      if (invalidSettings.length > 0) {
        return { success: false, message: `Invalid privacy settings: ${invalidSettings.join(', ')}` };
      }
    }
    if (section === 'notifications') {
      const validNotificationSettings = [
        'enableAllNotifications', 'pushNotifications', 'emailNotifications', 'smsNotifications',
        'newSubscriberAlerts', 'subscriptionRenewals', 'tipsAndDonations', 'earningsUpdates',
        'newLikes', 'newComments', 'mentionsAndTags', 'sharesAndReposts', 'newDirectMessages',
        'messageRequests', 'liveStreamInvitations', 'storyReplies', 'loginAlerts',
        'suspiciousActivity', 'accountUpdates', 'platformAnnouncements', 'doNotDisturb'
      ];
      const invalidSettings = Object.keys(data).filter(key => !validNotificationSettings.includes(key));
      if (invalidSettings.length > 0) {
        return { success: false, message: `Invalid notification settings: ${invalidSettings.join(', ')}` };
      }
    }

    if (section === 'account') {
      updatedUserInfo.account = { ...updatedUserInfo.account, ...data };
    } else {
      updatedUserInfo[section] = { ...updatedUserInfo[section], ...data };
    }
    await ctx.db.patch(account._id, { user_info: updatedUserInfo });
    return { success: true, data: updatedUserInfo };
  }
});

export const fetchNetworkCreators = query({
  args: {
    page: v.optional(v.number()),
    limit: v.optional(v.number()),
    sortBy: v.optional(v.string()),
    country: v.optional(v.string()),
    gender: v.optional(v.string()),
    niche: v.optional(v.string()),
    ethnicity: v.optional(v.string()),
    age: v.optional(v.string()),
    language: v.optional(v.string()),
    height: v.optional(v.string()),
    hairColor: v.optional(v.string()),
    eyeColor: v.optional(v.string()),
    waist: v.optional(v.string()),
    hips: v.optional(v.string()),
    butt: v.optional(v.string()),
    shoeSize: v.optional(v.string()),
    bodyType: v.optional(v.string()),
    breastType: v.optional(v.string()),
    breastSize: v.optional(v.string()),
    piercings: v.optional(v.array(v.string())),
    tattoos: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const page = args.page ?? 1;
    const limit = args.limit ?? 52;
    const sortBy = args.sortBy ?? 'recently-joined';
    const offset = (page - 1) * limit;

    // Fetch all creators
    let creators = await ctx.db
      .query('Accounts')
      .filter(q => q.eq(q.field('account_type'), 'creator'))
      .collect();

    // Apply filters
    function matchesFilter(val: string | undefined, filter: string | undefined) {
      if (!filter || filter === 'all') return true;
      return (val?.toLowerCase() ?? '') === filter.toLowerCase();
    }
    creators = creators.filter(c =>
      matchesFilter(c.user_info?.account?.location, args.country) &&
      matchesFilter(c.user_info?.account?.gender, args.gender) &&
      matchesFilter(c.user_info?.account?.niche, args.niche) &&
      matchesFilter(c.user_info?.account?.ethnicity, args.ethnicity) &&
      matchesFilter(c.user_info?.account?.age, args.age) &&
      matchesFilter(c.user_info?.account?.language, args.language) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.height, args.height) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.hairColor, args.hairColor) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.eyeColor, args.eyeColor) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.waist, args.waist) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.hips, args.hips) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.butt, args.butt) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.shoeSize, args.shoeSize) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.bodyType, args.bodyType) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.breastType, args.breastType) &&
      matchesFilter(c.user_info?.account?.physicalAttributes?.breastSize, args.breastSize)
    );
    if (args.piercings && args.piercings.length > 0) {
      creators = creators.filter(c => {
        const userPiercings = c.user_info?.account?.piercings || [];
        return args.piercings?.every(p => userPiercings.includes(p));
      });
    }
    if (args.tattoos && args.tattoos.length > 0) {
      creators = creators.filter(c => {
        const userTattoos = c.user_info?.account?.tattoos || [];
        return args.tattoos?.every(t => userTattoos.includes(t));
      });
    }

    // Fetch followers and posts count for each creator
    const creatorsWithCounts = await Promise.all(
      creators.map(async c => {
        const followers = await ctx.db
          .query('Follows')
          .filter(q => q.eq(q.field('followed_id'), c.user_id))
          .collect();
        const posts = await ctx.db
          .query('Content')
          .filter(q => q.eq(q.field('creator_id'), c.user_id))
          .collect();
        return {
          ...c,
          followersCount: followers.length,
          postsCount: posts.length,
        };
      })
    );

    // Sort
    if (sortBy === 'recently-joined') {
      creatorsWithCounts.sort((a, b) => {
        const aDate = new Date(a.registration_date ?? 0).getTime();
        const bDate = new Date(b.registration_date ?? 0).getTime();
        return bDate - aDate;
      });
    } else if (sortBy === 'most-viewed') {
      creatorsWithCounts.sort((a, b) => b.followersCount - a.followersCount);
    } else if (sortBy === 'highest-rated') {
      creatorsWithCounts.sort((a, b) => b.postsCount - a.postsCount);
    }

    // Pagination
    const paged = creatorsWithCounts.slice(offset, offset + limit);

    // Format response
    const formattedCreators = paged.map(creator => ({
      id: creator.user_id,
      username: creator.user_info?.account?.username || '',
      displayName: creator.user_info?.account?.displayName || '',
      profilePhoto: creator.user_info?.profilePhoto || '/images/user/default-avatar.webp',
      niche: creator.user_info?.account?.niche || '',
      location: creator.user_info?.account?.location || '',
      latitude: creator.user_info?.account?.latitude || 0,
      longitude: creator.user_info?.account?.longitude || 0,
      followers: creator.followersCount,
      posts: creator.postsCount,
      subscribed: false,
      physicalAttributes: creator.user_info?.account?.physicalAttributes || {},
      ethnicity: creator.user_info?.account?.ethnicity || '',
      dateOfBirth: creator.user_info?.account?.dateOfBirth || null,
      coverBanner: creator.user_info?.coverBanner || '/images/user/default-banner.webp',
      coverBannerType: creator.user_info?.coverBannerType || 'image',
      isVerified: creator.user_info?.account?.is_verified || false,
      accountType: creator.account_type,
    }));

    return {
      creators: formattedCreators,
      pagination: {
        total: creatorsWithCounts.length,
        page,
        limit,
        totalPages: Math.ceil(creatorsWithCounts.length / limit),
      },
      totalUnpaginated: creatorsWithCounts.length,
    };
  },
});

export const getUserById = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, { userId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated.");
    }

    const account = await ctx.db
      .query("Accounts")
      .filter((q) => q.eq(q.field("user_id"), userId))
      .unique();

    if (!account) {
      return null;
    }

    // Format the user data to match the legacy API response
    const formattedUser = {
      id: account.user_id,
      username: account.user_info?.account?.username || account.user_id.slice(0, 8),
      display_name: account.user_info?.account?.displayName || '',
      wallet_address: account.wallet_address,
      profilePhoto: account.user_info?.profilePhoto || '',
      account_type: account.account_type
    };

    return formattedUser;
  },
});

export const reportUser = mutation({
  args: {
    userId: v.string(),
    reason: v.string(),
    details: v.optional(v.string()),
  },
  handler: async (ctx, { userId, reason, details }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated.");
    }

    if (!userId || !reason) {
      throw new Error("Missing required fields: userId and reason");
    }

    // Insert the report into the database
    const reportId = await ctx.db.insert("UserReports", {
      reported_user_id: userId,
      reporter_user_id: identity.subject,
      reason,
      details: details || "",
      status: "pending",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    return {
      success: true,
      message: "Report submitted successfully",
      data: { reportId }
    };
  },
});

export const blockUser = mutation({
  args: {
    blockedUserId: v.string(),
  },
  handler: async (ctx, { blockedUserId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User is not authenticated.");
    }

    const user = await findAccountByUserSubject(ctx, identity.subject);

    if (!user) {
      throw new Error("User not found");
    }
    const blockerId = user?.user_id;

    // Check if the block already exists
    const existingBlock = await ctx.db
      .query("UserBlocks")
      .filter(q =>
        q.and(
          q.eq(q.field("blocker_id"), blockerId),
          q.eq(q.field("blocked_id"), blockedUserId)
        )
      )
      .first();

    if (existingBlock) {
      // Block already exists, do not insert duplicate
      return { success: true, alreadyBlocked: true };
    }

    // Insert the block into the database
    await ctx.db.insert("UserBlocks", {
      blocker_id: blockerId,
      blocked_id: blockedUserId,
      created_at: new Date().toISOString(),
    });

    return { success: true, alreadyBlocked: false };
  },
});

export const getUserStatus = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const account = await ctx.db
      .query("Accounts")
      .filter(q => q.eq(q.field("user_id"), args.userId))
      .first();
    return {
      status: account?.user_info?.status || 'offline',
      lastActive: account?.user_info?.last_active || null,
    };
  }
});

export const isUserBlocked = query({
  args: {
    viewerId: v.string(),
    profileUserId: v.string(),
  },
  handler: async (ctx, { viewerId, profileUserId }) => {
    // Check if viewer has blocked profile user
    const block = await ctx.db
      .query('UserBlocks')
      .filter(q =>
        q.or(
          q.and(q.eq(q.field('blocker_id'), viewerId), q.eq(q.field('blocked_id'), profileUserId)),
          q.and(q.eq(q.field('blocker_id'), profileUserId), q.eq(q.field('blocked_id'), viewerId))
        )
      )
      .first();
    return { blocked: !!block };
  }
});

export const getUserInfo = query({
  args: { userId: v.string() },
  handler: async (ctx, { userId }) => {
    const account = await ctx.db
      .query("Accounts")
      .filter(q => q.eq(q.field("user_id"), userId))
      .first();

    if (!account) return null;

    return {
      user_id: account.user_id,
      username: account.user_info?.account?.username || '',
      displayName: account.user_info?.account?.displayName || '',
      profilePhoto: account.user_info?.profilePhoto || '',
      bio: account.user_info?.account?.bio || '',
      isVerified: account.user_info?.account?.is_verified || false,
      accountType: account.account_type,
      // Add more fields as needed
    };
  }
});