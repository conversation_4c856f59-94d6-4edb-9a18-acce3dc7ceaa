import { useState, useEffect, useCallback, useRef } from 'react';

interface ProgressiveImageLoaderOptions {
  lowQualityUrl?: string;
  highQualityUrl: string;
  placeholder?: string;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

interface ProgressiveImageState {
  src: string;
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  quality: 'placeholder' | 'low' | 'high';
}

export const useProgressiveImageLoader = (options: ProgressiveImageLoaderOptions) => {
  const {
    lowQualityUrl,
    highQualityUrl,
    placeholder,
    onLoad,
    onError
  } = options;

  const [state, setState] = useState<ProgressiveImageState>({
    src: placeholder || '',
    isLoading: true,
    isLoaded: false,
    hasError: false,
    quality: 'placeholder'
  });

  const highQualityImageRef = useRef<HTMLImageElement>();
  const lowQualityImageRef = useRef<HTMLImageElement>();

  const loadImage = useCallback((url: string, quality: 'low' | 'high') => {
    return new Promise<void>((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        setState(prev => ({
          ...prev,
          src: url,
          quality,
          isLoading: quality === 'low', // Still loading if this is just the low quality
          isLoaded: quality === 'high'
        }));

        if (quality === 'high') {
          onLoad?.();
        }

        resolve();
      };

      img.onerror = () => {
        const error = new Error(`Failed to load ${quality} quality image: ${url}`);

        setState(prev => ({
          ...prev,
          hasError: true,
          isLoading: false
        }));

        onError?.(error);
        reject(error);
      };

      img.src = url;

      // Store reference for cleanup
      if (quality === 'high') {
        highQualityImageRef.current = img;
      } else {
        lowQualityImageRef.current = img;
      }
    });
  }, [onLoad, onError]);

  const loadProgressively = useCallback(async () => {
    try {
      // Start with placeholder if available
      if (placeholder) {
        setState(prev => ({
          ...prev,
          src: placeholder,
          quality: 'placeholder',
          isLoading: true
        }));
      }

      // Load low quality first if available
      if (lowQualityUrl) {
        await loadImage(lowQualityUrl, 'low');
      }

      // Then load high quality
      await loadImage(highQualityUrl, 'high');

    } catch (error) {
      // Error handling is done in loadImage
      console.warn('Progressive image loading failed:', error);
    }
  }, [placeholder, lowQualityUrl, highQualityUrl, loadImage]);

  // Start loading when component mounts or URLs change
  useEffect(() => {
    loadProgressively();

    // Cleanup function
    return () => {
      if (highQualityImageRef.current) {
        highQualityImageRef.current.onload = null;
        highQualityImageRef.current.onerror = null;
      }
      if (lowQualityImageRef.current) {
        lowQualityImageRef.current.onload = null;
        lowQualityImageRef.current.onerror = null;
      }
    };
  }, [loadProgressively]);

  const retry = useCallback(() => {
    setState(prev => ({
      ...prev,
      hasError: false,
      isLoading: true
    }));
    loadProgressively();
  }, [loadProgressively]);

  return {
    ...state,
    retry
  };
};

// Hook for creating progressive image URLs (e.g., with different quality parameters)
export const useProgressiveImageUrls = (baseUrl: string) => {
  const createProgressiveUrls = useCallback((url: string) => {
    if (!url) return { highQualityUrl: '', lowQualityUrl: undefined };

    // For images that support quality parameters (like Cloudinary, ImageKit, etc.)
    const hasQueryParams = url.includes('?');
    const separator = hasQueryParams ? '&' : '?';

    // Create low quality version (smaller size, lower quality)
    const lowQualityUrl = `${url}${separator}w_400&q_30&f_auto`;

    // High quality version
    const highQualityUrl = `${url}${separator}w_800&q_80&f_auto`;

    return {
      highQualityUrl,
      lowQualityUrl
    };
  }, []);

  return createProgressiveUrls(baseUrl);
};

// Component wrapper for progressive images
export interface ProgressiveImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  lowQualitySrc?: string;
  placeholder?: string;
  onImageLoad?: () => void;
  onImageError?: (error: Error) => void;
  className?: string;
}

export const ProgressiveImage: React.FC<ProgressiveImageProps> = ({
  src,
  lowQualitySrc,
  placeholder,
  onImageLoad,
  onImageError,
  className = '',
  alt = '',
  ...props
}) => {
  const { src: currentSrc, isLoading, hasError, quality, retry } = useProgressiveImageLoader({
    highQualityUrl: src,
    lowQualityUrl: lowQualitySrc,
    placeholder,
    onLoad: onImageLoad,
    onError: onImageError
  });

  if (hasError) {
    return (
      <div className={`flex items-center justify-center bg-gray-200 dark:bg-gray-800 ${className}`}>
        <button
          onClick={retry}
          className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          Failed to load. Click to retry.
        </button>
      </div>
    );
  }

  return (
    <img
      {...props}
      src={currentSrc}
      alt={alt}
      className={`${className} ${isLoading ? 'opacity-75' : 'opacity-100'} ${
        quality === 'low' ? 'filter blur-sm' : ''
      } transition-all duration-300`}
      loading="lazy"
    />
  );
};
