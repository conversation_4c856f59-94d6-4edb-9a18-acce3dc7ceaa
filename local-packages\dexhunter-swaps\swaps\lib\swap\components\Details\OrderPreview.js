import { jsx as d, jsxs as s } from "react/jsx-runtime";
import { useMemo as v } from "react";
import { Progress as M } from "../../../components/ui/progress.js";
import N from "../../../store/useStore.js";
import { formatBalance as a, formatNumber as A, fromLoveLace as S } from "../../../utils/formatNumber.js";
import { cn as n } from "../../../lib/utils.js";
import b from "../../../components/ui/tooltipDialog.js";
import E from "../../../assets/svg/IconSquareInfo.js";
import L from "../../../hooks/useScreen.js";
import { X as w } from "../../../x-9e07c78a.js";
import "../../../index-1c873780.js";
import "../../../index-563d1ed8.js";
import "../../../index-c8f2666b.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../lib.js";
import "../../../extend-tailwind-merge-e63b2b56.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../tokens.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
import "../../../components/ui/dialog.js";
import "../../../index-840f2930.js";
import "../../../index-c7156e07.js";
import "../../../index-4914f99c.js";
import "../../../index-67500cd3.js";
import "../../../index-27cadef5.js";
import "../../../index-5116e957.js";
import "../../../components/ui/tooltip.js";
import "../../../index-0ce202b9.js";
import "../../../index-bcfeaad9.js";
import "../../../index-f7426637.js";
import "../../../createLucideIcon-7a477fa6.js";
const we = ({
  split: e,
  dex: c,
  totalAmount: y,
  possibleSplit: u,
  side: T = "left"
}) => {
  const { isMobile: p } = L(), { tokenSell: h, tokenBuy: t, dexBlacklist: j, toggleDexBlacklist: g } = N(
    (i) => i.swapSlice
  ), { slippage: l, isAutomaticSlippage: _ } = N(
    (i) => i.swapSettingsSlice
  ), D = (e == null ? void 0 : e.amount_in) < 1e4 ? a((e == null ? void 0 : e.amount_in) || 0) : A((e == null ? void 0 : e.amount_in) || 0), C = e ? (e == null ? void 0 : e.amount_in) / y * 100 : 0, m = j.includes(c.code), r = !e || (e == null ? void 0 : e.amount_in) === 0 || m, x = e == null ? void 0 : e.price_impact, f = x > 1 ? a(x) : "<1", o = l === -1 || _ ? !1 : x > l, I = v(() => r ? /* @__PURE__ */ d("div", { className: "dhs-flex dhs-flex-col dhs-gap-1.5 dhs-text-subText dhs-text-sm dhs-p-2", children: /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
    /* @__PURE__ */ d("div", { children: "Possible output" }),
    /* @__PURE__ */ s("div", { className: "dhs-text-mainText", children: [
      a(u),
      " ",
      t == null ? void 0 : t.ticker
    ] })
  ] }) }) : o ? /* @__PURE__ */ s("div", { className: "dhs-flex dhs-flex-col dhs-gap-1.5 dhs-text-subText dhs-text-sm dhs-p-2", children: [
    "Warning: Price impact is ",
    f,
    "%, your slippage tolerance is ",
    l,
    "%"
  ] }) : /* @__PURE__ */ s("div", { className: "dhs-flex dhs-flex-col dhs-gap-1.5 dhs-text-subText dhs-text-sm dhs-p-2", children: [
    /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
      /* @__PURE__ */ d("div", { children: "Amount in" }),
      /* @__PURE__ */ s("div", { className: "dhs-text-mainText", children: [
        a(e == null ? void 0 : e.amount_in),
        " ",
        h == null ? void 0 : h.ticker
      ] })
    ] }),
    /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
      /* @__PURE__ */ d("div", { children: "Expected output" }),
      /* @__PURE__ */ s("div", { className: "dhs-text-mainText", children: [
        a(e == null ? void 0 : e.expected_output_without_slippage),
        " ",
        t == null ? void 0 : t.ticker
      ] })
    ] }),
    /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
      /* @__PURE__ */ d("div", { children: "Min. receive" }),
      /* @__PURE__ */ s("div", { className: "dhs-text-mainText", children: [
        a(e == null ? void 0 : e.expected_output),
        " ",
        t == null ? void 0 : t.ticker
      ] })
    ] }),
    /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
      /* @__PURE__ */ d("div", { children: "Dex Fee" }),
      /* @__PURE__ */ s("div", { className: "dhs-text-mainText", children: [
        S(e == null ? void 0 : e.fee),
        " ADA"
      ] })
    ] }),
    /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
      /* @__PURE__ */ d("div", { children: "Price impact" }),
      /* @__PURE__ */ s("div", { className: "dhs-text-mainText", children: [
        f,
        "%"
      ] })
    ] })
  ] }), [
    r,
    u,
    e,
    t,
    f,
    o
  ]), P = v(() => {
    const i = /* @__PURE__ */ s(
      "div",
      {
        className: "dhs-relative",
        onClick: () => g(c.code),
        children: [
          /* @__PURE__ */ d(
            "img",
            {
              src: c.logo,
              className: n(
                "dhs-w-6 dhs-h-6 dhs-min-w-[24px] dhs-min-h-[24px] dhs-rounded-full dhs-bg-gray-104 !dhs-cursor-pointer",
                r && "dhs-opacity-40",
                m && "dhs-opacity-100 dhs-border-2 dhs-border-red-101"
              ),
              width: 20,
              height: 20,
              alt: ""
            }
          ),
          m && /* @__PURE__ */ d("div", { className: "dhs-absolute dhs-inset-0 dhs-flex dhs-items-center dhs-justify-center", children: /* @__PURE__ */ d(w, { className: "dhs-h-6 dhs-w-6 dhs-text-red-101" }) }),
          /* @__PURE__ */ d("div", { className: "dhs-absolute dhs-inset-0 dhs-flex dhs-rounded-full dhs-w-6 dhs-h-6 dhs-items-center dhs-justify-center dhs-opacity-0 hover:dhs-opacity-100 dhs-border-2 dhs-border-red-101", children: /* @__PURE__ */ d(w, { className: "dhs-h-6 dhs-w-6 dhs-text-red-101" }) })
        ]
      }
    );
    return p ? i : /* @__PURE__ */ d(
      b,
      {
        activeMobile: !1,
        delayDuration: 500,
        trigger: i,
        content: /* @__PURE__ */ s("span", { children: [
          m ? "Include" : "Exclude",
          " ",
          c.name
        ] }),
        triggerClass: "dhs-relative",
        contentClass: "dhs-text-mainText"
      }
    );
  }, [c, m, r, g, p]);
  return /* @__PURE__ */ s("div", { className: "dhs-flex dhs-gap-[10px] dhs-items-center", children: [
    P,
    /* @__PURE__ */ d(
      b,
      {
        trigger: /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-justify-start dhs-gap-2 w-full", children: [
          /* @__PURE__ */ d(
            "div",
            {
              className: n(
                "dhs-flex dhs-gap-2 dhs-items-center dhs-text-mainText dhs-font-proximaSemiBold dhs-text-xs leading-normal",
                r && "dhs-text-subText",
                o && "dhs-text-yellow-101"
              ),
              children: /* @__PURE__ */ d(E, { className: "dhs-hidden dhs-text-subText dhs-text-[14px]" })
            }
          ),
          /* @__PURE__ */ d("div", { className: "dhs-flex dhs-items-center", children: /* @__PURE__ */ s("div", { className: "dhs-flex dhs-flex-col dhs-items-start dhs-justify-center ", children: [
            /* @__PURE__ */ s(
              "span",
              {
                className: n(
                  "dhs-font-proximaSemiBold dhs-text-xs dhs-text-green-101",
                  r && "dhs-text-subText",
                  o && "dhs-text-yellow-101"
                ),
                children: [
                  /* @__PURE__ */ d("span", { children: D }),
                  " ",
                  h == null ? void 0 : h.ticker
                ]
              }
            ),
            /* @__PURE__ */ d(
              M,
              {
                value: C,
                className: n(
                  "dhs-h-1 dhs-w-[40px] @sm/appRoot:dhs-w-[50px] @md/appRoot:dhs-w-[65px] dhs-rounded-md",
                  r && "dhs-bg-subText"
                ),
                color: o ? "dhs-bg-yellow-101" : e && !r ? "dhs-bg-green-101" : "dhs-bg-gray-106"
              }
            )
          ] }) })
        ] }),
        content: I,
        triggerClass: "dhs-w-full",
        contentClass: "dhs-text-mainText dhs-text-white",
        side: T === "left" ? "top" : "left"
      }
    )
  ] });
};
export {
  we as default
};
