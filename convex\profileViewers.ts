import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Get all profile viewers for the authenticated user
export const getProfileViewers = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Unauthorized");
    const userId = identity.subject;

    // Fetch all viewers for this user
    const viewers = await ctx.db
      .query("ProfileViewers")
      .filter(q => q.eq(q.field("user_id"), userId))
      .collect();

    // Fetch account info for each viewer
    const results = await Promise.all(
      viewers.map(async (viewer) => {
        const account = await ctx.db
          .query("Accounts")
          .filter(q => q.eq(q.field("user_id"), viewer.viewer_id))
          .first();
        const userInfo = account?.user_info || {};
        const accountType = account?.account_type;
        return {
          id: viewer.viewer_id,
          type: viewer.viewer_type,
          username: userInfo.account?.username,
          accountType,
          display_name: userInfo.account?.displayName,
        };
      })
    );

    return { success: true, data: results };
  },
});

// Replace all profile viewers for the authenticated user
export const setProfileViewers = mutation({
  args: {
    viewers: v.array(
      v.object({
        id: v.string(),
        type: v.string(),
      })
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Unauthorized");
    const userId = identity.subject;

    // Delete all existing viewers for this user
    const existing = await ctx.db
      .query("ProfileViewers")
      .filter(q => q.eq(q.field("user_id"), userId))
      .collect();
    for (const viewer of existing) {
      await ctx.db.delete(viewer._id);
    }

    // Insert new viewers
    const now = new Date().toISOString();
    for (const viewer of args.viewers) {
      await ctx.db.insert("ProfileViewers", {
        user_id: userId,
        viewer_id: viewer.id,
        viewer_type: viewer.type,
        created_at: now,
      });
    }

    return { success: true, message: "Profile viewers updated" };
  },
});
