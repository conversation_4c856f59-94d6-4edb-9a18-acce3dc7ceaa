import { p as s } from "../immer-548168ec.js";
const n = (l) => ({
  isHideSmallBalances: !1,
  defaultBuySize: 0,
  isOpenShortcuts: !1,
  isGlobalSettingsOpen: !1,
  isAdvancedMode: !1,
  isShortcutsDisabled: !1,
  isExactTime: !1,
  isPriceChangeInverted: !1,
  isAnimationsDisabled: !1,
  isMyOrdersOnTrends: !1,
  isPricesFlipped: !1,
  partnerName: void 0,
  partnerCode: void 0,
  inputs: [],
  setIsPriceChangeInverted: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.isPriceChangeInverted = e;
      })
    );
  },
  setIsAdvancedMode: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.isAdvancedMode = e;
      })
    );
  },
  setIsHideSmallBalances: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.isHideSmallBalances = e;
      })
    );
  },
  setDefaultBuySize: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.defaultBuySize = e;
      })
    );
  },
  toggleOpenShortcuts: () => {
    l(
      s((e) => {
        e.globalSettingsSlice.isOpenShortcuts = !e.globalSettingsSlice.isOpenShortcuts;
      })
    );
  },
  toggleIsGlobalSettingsOpen: () => {
    l(
      s((e) => {
        e.globalSettingsSlice.isGlobalSettingsOpen = !e.globalSettingsSlice.isGlobalSettingsOpen;
      })
    );
  },
  setIsShortcutsDisabled: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.isShortcutsDisabled = e;
      })
    );
  },
  setIsExactTime: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.isExactTime = e;
      })
    );
  },
  setIsAnimationsDisabled: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.isAnimationsDisabled = e;
      })
    );
  },
  setIsMyOrdersOnTrends: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.isMyOrdersOnTrends = e;
      })
    );
  },
  setIsPricesFlipped: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.isPricesFlipped = e;
      })
    );
  },
  setPartner: (e, i) => {
    l(
      s((t) => {
        t.globalSettingsSlice.partnerName = e, t.globalSettingsSlice.partnerCode = i, localStorage.setItem("partnerCode", i);
      })
    );
  },
  setInputs: (e) => {
    l(
      s((i) => {
        i.globalSettingsSlice.inputs = e;
      })
    );
  }
});
export {
  n as default
};
