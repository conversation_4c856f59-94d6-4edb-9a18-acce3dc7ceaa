"use client";

import { useEffect, useState, useMemo, useRef } from "react";
import Link from "next/link";
import { Search, Plus, ArrowLeft, X, MoreVertical, Trash, ChevronDown, Tag, User } from "lucide-react";
import { cn } from "@/lib/utils";
import { usePathname, useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/chat/ui/avatar";
import { useDebounce } from "@/hooks/useDebounce";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Tippy from '@tippyjs/react';
import { Checkbox } from "@/components/ui/checkbox";
import LabelDialog from "@/components/dialogs/LabelDialog";
import NewMessageDialog from "@/components/dialogs/NewMessageDialog";
import { toast } from "react-toastify";
import { Skeleton } from "@/components/ui/skeleton";
import { UserInfo, Message } from "@/types/message";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useUser } from '@/hooks/useUser'; // or your actual user hook

interface MessagesSidebarProps {
    messages: Message[] | undefined;
}

export default function MessagesSidebar({ messages }: MessagesSidebarProps) {
    const { user } = useUser(); // or however you get the current user
    // Use the correct user_id from Accounts table
    const userId = user?.user_id; // <-- this is the canonical user id
    const pathname = usePathname();
    const router = useRouter();

    const [searchQuery, setSearchQuery] = useState('');
    const [isSearchExpanded, setIsSearchExpanded] = useState(false);
    const [isNewMessageOpen, setIsNewMessageOpen] = useState(false);
    const [userSearchQuery, setUserSearchQuery] = useState('');
    const [userSearchResults, setUserSearchResults] = useState<any[]>([]);
    const [isUserSearching, setIsUserSearching] = useState(false);
    const [selectedConversations, setSelectedConversations] = useState<string[]>([]);
    const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(null);
    const [activeTab, setActiveTab] = useState<string>("recent");
    const [isLabelModalOpen, setIsLabelModalOpen] = useState<boolean>(false);
    const [selectedLabel, setSelectedLabel] = useState<string | null>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const [isVerifiedBadgeLoading, setIsVerifiedBadgeLoading] = useState(true);

    // Define tab options
    const tabOptions = [
        { value: "recent", label: "Recent" },
        { value: "unread", label: "Unread" },
        { value: "active", label: "Active" },
    ];

    const labelOptions = [
        { value: "high-rollers", label: "High Rollers", color: 'bg-purple-500' },
        { value: "spam", label: "Spam", color: 'bg-orange-500' },
        { value: "cheap", label: "Cheap", color: 'bg-red-500' },
    ];

    // Debounce search queries
    const debouncedSearchQuery = useDebounce(searchQuery, 500);
    const debouncedUserSearchQuery = useDebounce(userSearchQuery, 500);

    // Convex search query for conversations (replaces REST API search)
    const searchResultsConvex = useQuery(
        api.messageSearch.searchMessages,
        debouncedSearchQuery && debouncedSearchQuery.length >= 3
            ? { query: debouncedSearchQuery, type: 'direct', limit: 20 }
            : "skip"
    );

    const isSearching = debouncedSearchQuery && debouncedSearchQuery.length >= 3 && searchResultsConvex === undefined;
    const searchResults = searchResultsConvex || { conversations: [] };

    const userSearchConvex = useQuery(
        api.users.searchUsers,
        debouncedUserSearchQuery && debouncedUserSearchQuery.length >= 3
            ? { query: debouncedUserSearchQuery, reason: "message" }
            : "skip"
    );

    useEffect(() => {
        if (!debouncedUserSearchQuery || debouncedUserSearchQuery.length < 3) {
            setUserSearchResults([]);
            setIsUserSearching(false);
            return;
        }
        setIsUserSearching(userSearchConvex === undefined);
        if (userSearchConvex && userSearchConvex.success) {
            setUserSearchResults(userSearchConvex.data || []);
        }
    }, [debouncedUserSearchQuery, userSearchConvex]);

    // Handle conversation selection
    const handleSelectConversation = (conversationId: string, index: number, event: React.MouseEvent) => {
        event.preventDefault();

        if (event.shiftKey && lastSelectedIndex !== null) {
            const currentConversations = debouncedSearchQuery && debouncedSearchQuery.length >= 3
                ? searchResults.conversations
                : conversations;

            const startIdx = Math.min(lastSelectedIndex, index);
            const endIdx = Math.max(lastSelectedIndex, index);

            const conversationsInRange = currentConversations
                .slice(startIdx, endIdx + 1)
                .map((msg: any) => msg.sender_id === userId ? msg.receiver_id : msg.sender_id);

            setSelectedConversations(prev => {
                const newSelection = [...prev];
                conversationsInRange.forEach(id => {
                    if (!newSelection.includes(id)) {
                        newSelection.push(id);
                    }
                });
                return newSelection;
            });
        } else {
            setSelectedConversations(prev => {
                if (prev.includes(conversationId)) {
                    return prev.filter(id => id !== conversationId);
                } else {
                    return [...prev, conversationId];
                }
            });
        }

        setLastSelectedIndex(index);
    };

    // Select all conversations
    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            const allConversationIds = conversations.map((msg: any) =>
                msg.sender_id === userId ? msg.receiver_id : msg.sender_id
            );
            setSelectedConversations(allConversationIds);
        } else {
            setSelectedConversations([]);
        }
    };

    // Handle back button functionality
    const isConversationView = pathname.startsWith('/messages/') && pathname !== '/messages';

    const handleBack = () => {
        router.push('/messages');
    };

    // Expand search and focus input
    const expandSearch = () => {
        setIsSearchExpanded(true);
        setTimeout(() => {
            searchInputRef.current?.focus();
        }, 100);
    };

    // Collapse search if empty
    const collapseSearch = () => {
        if (!searchQuery) {
            setIsSearchExpanded(false);
        }
    };

    // Process conversations to show only the most recent message per user
    const conversations = useMemo(() => {
        if (debouncedSearchQuery && debouncedSearchQuery.length >= 3) {
            return searchResults.conversations || [];
        }

        const conversationMap = new Map();

        messages?.forEach((message: any) => {
            // Use userId from Accounts table for all comparisons
            const partnerId = message.sender_id === userId
                ? message.receiver_id
                : message.sender_id;

            const existingMessage = conversationMap.get(partnerId);

            if (!existingMessage || new Date(message.created_at) > new Date(existingMessage.created_at)) {
                conversationMap.set(partnerId, message);
            }
        });

        let result = Array.from(conversationMap.values())
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        if (activeTab === 'unread') {
            result = result.filter((message: any) => {
                return !message.read; // Assuming a 'read' field indicates unread status
            });
        } else if (activeTab === 'active') {
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            result = result.filter((message: any) => {
                return new Date(message.created_at) > oneWeekAgo;
            });
        }

        return result;
    }, [messages, userId, debouncedSearchQuery, searchResults.conversations, activeTab]);

    const formatMessageDate = (dateString: string) => {
        const messageDate = new Date(dateString);
        const today = new Date();

        if (messageDate.toDateString() === today.toDateString()) {
            return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else {
            return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    };

    const handleDeleteSelected = () => {
        console.log('Deleting conversations:', selectedConversations);
        setSelectedConversations([]);
    };

    const handleLabelSelect = (labelValue: string) => {
        setSelectedLabel(labelValue);
    };

    const handleApplyLabel = (label: string, customLabel?: string) => {
        if (label && selectedConversations.length > 0) {
            console.log('Adding label:', label, 'to conversations:', selectedConversations);
            // Placeholder: Implement API call to update message labels

            // Here you would make an API call to apply the label
            // Example:
            // const response = await fetch('/api/messages/label', {
            //   method: 'POST',
            //   headers: { 'Content-Type': 'application/json' },
            //   body: JSON.stringify({
            //     conversationIds: selectedConversations,
            //     label: selectedLabel
            //   })
            // });

            // Close modal after applying
            setIsLabelModalOpen(false);
            setSelectedLabel(null);

            // Optional: Show success message
            toast.success(`Label applied to ${selectedConversations.length} conversation(s)`);
        }
    };

    const openLabelModal = () => {
        if (selectedConversations.length > 0) {
            setIsLabelModalOpen(true);
        } else {
            toast.error('Please select at least one conversation');
        }
    };

    const renderConversationItem = (message: any, index: number, isSearchResult = false) => {
        // Use userId from Accounts table for all comparisons
        const otherUser = message.sender_id === userId ? message.receiver : message.sender;
        const conversationId = message.sender_id === userId ? message.receiver_id : message.sender_id;
        const isSelected = selectedConversations.includes(conversationId);

        return (
            <div key={conversationId} className="relative border-b border-[#18181b]/30 dark:border-white/30">
                <div className="flex items-center">
                    {/* Selection checkbox - always visible and interactive */}
                    <div className="pl-3 pr-2">
                        <Checkbox
                            checked={isSelected}
                            onCheckedChange={(checked) => {
                                handleSelectConversation(conversationId, index, { preventDefault: () => { } } as any);
                            }}
                            className={cn(
                                "w-5 h-5 rounded-sm !border-gray-400 dark:!border-white cursor-pointer",
                                isSelected && "!border-gray-400 dark:!border-white text-white"
                            )}
                        />
                    </div>

                    <Link
                        href={`/messages/${conversationId}`}
                        className={cn(
                            "flex flex-1 items-center p-3 cursor-pointer hover:bg-gray-200 hover:dark:bg-[#2a2a2a]",
                            pathname === `/messages/${conversationId}` ? "bg-gray-200 dark:bg-[#2a2a2a]" : ""
                        )}
                        onClick={(e) => selectedConversations.length > 0 && isSearchResult ? e.preventDefault() : null}
                    >
                        <Avatar className="h-10 w-10 mr-3 relative overflow-visible">
                            <AvatarImage className="rounded-full" src={otherUser?.profilePhoto || '/images/user/default-avatar.webp'} />
                            <AvatarFallback>
                                {(otherUser.username || otherUser.user_id?.slice(0, 2) || '?').charAt(0)}
                            </AvatarFallback>

                            {otherUser.status === 'online' ?
                                <div className="w-3 h-3 rounded-full bg-green-500 border-2 border-solid border-white absolute bottom-0 right-0 z-[1]"></div>
                                :
                                <div className="w-3 h-3 rounded-full bg-gray-400 border-2 border-solid border-white absolute bottom-0 right-0 z-[1]"></div>
                            }
                        </Avatar>
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                                <h3 className="font-medium truncate max-w-[120px]">
                                    {otherUser.displayName || otherUser.username || otherUser.user_id?.slice(0, 8) || 'User'} {otherUser.isVerified && (
                                        <Skeleton loading={isVerifiedBadgeLoading} width="16px" height="16px" className="inline-block">
                                            <img className="w-4 h-4 user-post-verified-badge" alt="Verified User" src="/images/user/verified.png" onError={() => setIsVerifiedBadgeLoading(false)} onLoad={() => setIsVerifiedBadgeLoading(false)} />
                                        </Skeleton>
                                    )}
                                </h3>
                                <div className="flex items-center justify-center gap-2">
                                    <span className="text-xs text-gray-400">
                                        {formatMessageDate(message.created_at)}
                                    </span>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <button className="focus:outline-none">
                                                <MoreVertical size={22} className="text-gray-500 dark:text-white" />
                                            </button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent className="w-48 left-0 bg-white/60 dark:!bg-[#18181b]/60 backdrop-blur border border-solid border-gray-200/30 max-h-52 overflow-auto">
                                            <DropdownMenuItem
                                                className={cn(
                                                    'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                                                )}
                                                onClick={() => console.log('Delete conversation:', conversationId)}
                                            >
                                                Delete
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white/20" />
                                            <DropdownMenuItem
                                                className={cn(
                                                    'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                                                )}
                                                onClick={() => console.log('Archive conversation:', conversationId)}
                                            >
                                                Archive
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </div>
                            <div className="flex items-center justify-between w-full mt-2">
                                <div className="flex flex-col">
                                    <p className="text-sm text-gray-400 truncate">
                                        {message.content}
                                    </p>
                                </div>
                                <span className="py-1 px-2 rounded-lg bg-turquoise capitalize flex items-center justify-center text-sm">Your Turn</span>
                            </div>
                        </div>
                    </Link>
                </div>
            </div>
        );
    };

    // Sync tab with URL parameters
    useEffect(() => {
        const searchParams = new URLSearchParams(window.location.search);
        const tabParam = searchParams.get('tab');
        if (tabParam && tabOptions.some(tab => tab.value === tabParam)) {
            setActiveTab(tabParam);
        }
    }, [pathname]);

    return (
        <div className="w-[560px] border-r border-t-0 border-b-0 border-l-0 border-[#18181b]/30 dark:border-white/30 flex flex-col border-solid px-0">
            {/* Search and New Message */}
            <div className="flex items-center justify-between p-3 border-b border-[#18181b]/30 dark:border-white/30 gap-4">
                <DropdownMenu modal={false}>
                    <DropdownMenuTrigger className="flex w-48 items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white focus-visible:!border focus-visible:!border-solid focus-visible:!border-gray-400 dark:focus-visible:!border-white">
                        <span>
                            {tabOptions.find(tab => tab.value === activeTab)?.label || "Recent"}
                        </span>
                        <ChevronDown size={24} className="ml-2 text-gray-300 dark:text-white w-6 h-6" />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-48 left-0 border-gray-400 dark:border-white max-h-52 overflow-auto">
                        {tabOptions.map((option, index) => (
                            <>
                                <DropdownMenuItem
                                    key={option.value}
                                    className={cn(
                                        activeTab === option.value && 'bg-transparent hover:bg-transparent text-gray-600 dark:text-white',
                                        'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                                    )}
                                    onClick={() => {
                                        setActiveTab(option.value);
                                        router.push(`/messages?tab=${option.value}`);
                                    }}
                                >
                                    {option.label}
                                </DropdownMenuItem>
                                {index < tabOptions.length - 1 && <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />}
                            </>
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Back button - only show in conversation view */}
                {isConversationView && (
                    <Tippy content="Back to messages" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                        <button
                            onClick={handleBack}
                            className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-[#2a2a2a] mr-1"
                        >
                            <ArrowLeft size={20} className="text-gray-500" />
                        </button>
                    </Tippy>
                )}

                <div className={cn(
                    "relative flex items-center transition-all duration-300",
                    isSearchExpanded ? "w-full" : "w-10"
                )}>
                    {isSearchExpanded ? (
                        <>
                            <input
                                ref={searchInputRef}
                                type="text"
                                placeholder="Search conversations..."
                                className="w-full h-10 bg-gray-200 dark:bg-[#2a2a2a] text-sm rounded-md py-2 pl-3 pr-9 focus:outline-none border border-solid border-[#18181b]/30 dark:border-white/30"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                onBlur={collapseSearch}
                            />
                            {searchQuery && (
                                <button
                                    className="absolute right-8 top-2.5"
                                    onClick={() => setSearchQuery('')}
                                >
                                    <X size={16} className="text-gray-400" />
                                </button>
                            )}
                            {isSearching ? (
                                <div className="absolute right-3 top-2.5 h-4 w-4">
                                    <span className="message-search-loader"></span>
                                </div>
                            ) : (
                                <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                            )}
                        </>
                    ) : (
                        <Tippy content="Search Messages" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                            <button
                                onClick={expandSearch}
                                className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-[#2a2a2a]"
                            >
                                <Search size={20} className="text-gray-500" />
                            </button>
                        </Tippy>
                    )}
                </div>

                {!isSearchExpanded && (
                    <Tippy content="New Message" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                        <button
                            onClick={() => setIsNewMessageOpen(true)}
                            className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-[#2a2a2a]"
                        >
                            <Plus size={20} className="text-gray-500" />
                        </button>
                    </Tippy>
                )}
            </div>

            {/* Selection actions bar */}
            <div className="flex items-center justify-between p-3 bg-gray-200 dark:bg-zinc-800">
                {/* Master Checkbox */}
                <Checkbox
                    checked={selectedConversations.length === conversations.length}
                    onCheckedChange={handleSelectAll}
                    className={cn(
                        "w-5 h-5 rounded-sm !border-gray-400 dark:!border-white cursor-pointer"
                    )}
                />

                {/* Actions */}
                <div className="flex items-center gap-4">
                    <span className="text-sm font-medium">{selectedConversations.length} selected</span>

                    {/* Inline Action Icons */}
                    <button
                        onClick={handleDeleteSelected}
                        disabled={selectedConversations.length === 0}
                        className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-500 dark:text-white"
                        title="Delete"
                    >
                        <Trash size={18} />
                    </button>
                    <button
                        onClick={openLabelModal}
                        disabled={selectedConversations.length === 0}
                        className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-500 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Add Label"
                    >
                        <Tag size={18} />
                    </button>
                </div>
            </div>

            {/* Conversation List */}
            <div className="flex-1 overflow-y-auto">
                {pathname.startsWith('/messages/') ? (
                    isSearching ? (
                        <div className="flex items-center justify-center h-32">
                            <div className="h-6 w-6">
                                <span className="message-search-loader"></span>
                            </div>
                            <span className="ml-2 text-gray-400">Searching...</span>
                        </div>
                    ) : debouncedSearchQuery && debouncedSearchQuery.length >= 3 ? (
                        searchResults.conversations && searchResults.conversations.length > 0 ? (
                            searchResults.conversations.map((message: any, index: number) =>
                                renderConversationItem(message, index, true)
                            )
                        ) : (
                            <div className="flex items-center justify-center h-32 text-gray-400">
                                No results found for "{debouncedSearchQuery}"
                            </div>
                        )
                    ) : conversations && conversations.length > 0 ? (
                        conversations.map((message: any, index: number) =>
                            renderConversationItem(message, index)
                        )
                    ) : (
                        <div className="flex items-center justify-center h-32 text-gray-400 text-center">
                            No direct messages yet
                        </div>
                    )
                ) : (
                    isSearching ? (
                        <div className="flex items-center justify-center h-32">
                            <div className="h-6 w-6">
                                <span className="message-search-loader"></span>
                            </div>
                            <span className="ml-2 text-gray-400">Searching...</span>
                        </div>
                    ) : debouncedSearchQuery && debouncedSearchQuery.length >= 3 ? (
                        searchResults.conversations && searchResults.conversations.length > 0 ? (
                            searchResults.conversations.map((message: any) => {
                                const otherUser = message.sender_id === userId ? message.receiver : message.sender;
                                const conversationId = message.sender_id === userId ? message.receiver_id : message.sender_id;

                                return (
                                    <Link
                                        key={conversationId}
                                        href={`/messages/${conversationId}`}
                                        className={cn(
                                            "flex items-center p-3 border-b border-[#18181b]/30 dark:border-white/30 cursor-pointer hover:bg-gray-200 hover:dark:bg-[#2a2a2a]"
                                        )}
                                    >
                                        <Avatar className="h-10 w-10 mr-3">
                                            <AvatarImage src={otherUser.profilePhoto} />
                                            <AvatarFallback>
                                                {(otherUser.username || otherUser.user_id?.slice(0, 2) || '?').charAt(0)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                                <h3 className="font-medium truncate max-w-[120px]">
                                                    {otherUser.username || otherUser.user_id?.slice(0, 8) || 'Unknown User'}
                                                </h3>
                                                <span className="text-xs text-gray-400">
                                                    {formatMessageDate(message.created_at)}
                                                </span>
                                            </div>
                                            <p className="text-sm text-gray-400 truncate">
                                                {message.content}
                                            </p>
                                        </div>
                                    </Link>
                                );
                            })
                        ) : (
                            <div className="flex items-center justify-center h-32 text-gray-400">
                                No results found for "{debouncedSearchQuery}"
                            </div>
                        )
                    ) : conversations && conversations.length > 0 ? (
                        conversations.map((message: any, index: number) => {
                            const otherUser = message.sender_id === userId ? message.receiver : message.sender;
                            const conversationId = message.sender_id === userId ? message.receiver_id : message.sender_id;

                            return (
                                renderConversationItem(message, index)
                            );
                        })
                    ) : (
                        <div className="flex items-center justify-center h-32 text-gray-400">
                            Select a conversation to start messaging
                        </div>
                    )
                )}
            </div>

            {/* New Message Dialog */}
            <NewMessageDialog
                isOpen={isNewMessageOpen}
                onClose={() => setIsNewMessageOpen(false)}
            />

            <LabelDialog
                isOpen={isLabelModalOpen}
                onClose={() => setIsLabelModalOpen(false)}
                onApply={handleApplyLabel}
                labelOptions={labelOptions}
                selectedConversations={selectedConversations}
            />
        </div>
    );
};