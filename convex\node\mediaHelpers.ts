"use node";

// Helper to generate a random filename for media uploads
export function randomFilenameGenerator(mimeType: string): string {
    const extMap: Record<string, string> = {
        // images
        'image/jpeg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/bmp': '.bmp',
        'image/webp': '.webp',
        // videos
        'video/mp4': '.mp4',
        'video/webm': '.webm',
        'video/ogg': '.ogv',
        'video/quicktime': '.mov',
        // audio
        'audio/mpeg': '.mp3',
        'audio/wav': '.wav',
        'audio/ogg': '.ogg',
        'audio/webm': '.webm',
        'audio/mp4': '.m4a',
        'audio/x-m4a': '.m4a',
        'audio/aac': '.aac',
        'audio/flac': '.flac',
        'audio/vnd.wave': '.wav',
        // fallback
        'mpeg': '.mp3',
        'mp3': '.mp3',
        'wav': '.wav',
        'ogg': '.ogg',
        'webm': '.webm',
        'mp4': '.mp4',
        'x-m4a': '.m4a',
    };
    const [major] = mimeType.split('/');
    const prefix = major === 'video' ? 'video'
        : major === 'audio' ? 'audio'
            : 'file';
    const ext = extMap[mimeType] ?? `.${mimeType.split('/')[1] || 'bin'}`;
    const randomNumber = Math.floor(Math.random() * 100000);
    const timestamp = Date.now();
    return `${prefix}-${randomNumber}-${timestamp}${ext}`;
};

// Helper to get file extension from mime type
export function getFileExtension(mimeType: string): string {
    const extensionMap: Record<string, string> = {
        'image/jpeg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/bmp': '.bmp',
        'image/webp': '.webp',
        'video/mp4': '.mp4',
        'video/webm': '.webm',
        'video/ogg': '.ogv',
        'audio/mpeg': '.mp3',
        'audio/wav': '.wav',
        'audio/ogg': '.ogg',
        'audio/webm': '.webm',
        'audio/mp4': '.m4a',
        'audio/x-m4a': '.m4a',
        'audio/aac': '.aac',
        'audio/flac': '.flac',
        'audio/vnd.wave': '.wav',
    };
    if (extensionMap[mimeType]) {
        return extensionMap[mimeType];
    } else {
        const parts = mimeType.split('/');
        if (parts.length === 2) {
            return '.' + parts[1].replace(/[^a-z0-9]+/gi, '');
        }
        return '';
    }
};