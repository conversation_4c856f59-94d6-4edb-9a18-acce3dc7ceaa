import React, { FC, useState } from 'react';
import { Ripple } from 'primereact/ripple';
import { Skeleton } from '@/components/ui/skeleton';

const walletDownloadUrls = {
    "Backpack": "https://backpack.app",
    "Block": "https://blockwallet.io",
    "Coinbase Wallet": "https://www.coinbase.com/wallet",
    "Compass": "https://compasswallet.io",
    "Crossmark": "https://crossmark.io",
    "Defly": "https://defly.app",
    "Enkrypt": "https://www.enkrypt.com",
    "enkrypt": "https://www.enkrypt.com",
    "eternl": "https://eternl.io/",
    "Exodus": "https://www.exodus.com",
    "Fearless": "https://fearlesswallet.io",
    "fearless-wallet": "https://fearlesswallet.io",
    "Fin": "https://finwallet.com",
    "flint": "https://flint-wallet.com/",
    "Gem": "https://gemwallet.app",
    "gerowallet": "https://gerowallet.io/",
    "Glow": "https://glow.app",
    "Keplr": "https://www.keplr.app",
    "lace": "https://www.lace.io/",
    "Leap": "https://www.leapwallet.io",
    "Leather": "https://leather.io",
    "Lute": "https://lute.app",
    "MetaMask": "https://metamask.io",
    "nami": "https://namiwallet.io/",
    "nufi": "https://nu.fi/",
    "OKX": "https://www.okx.com/web3",
    "OKX Wallet": "https://www.okx.com/web3",
    "Pera": "https://perawallet.app",
    "Phantom": "https://phantom.app/",
    "PolkaGate": "https://polkagate.xyz",
    "polkagate": "https://polkagate.xyz",
    "Rabby Wallet": "https://rabby.io",
    "Rainbow": "https://rainbow.me",
    "Solflare": "https://solflare.com",
    "Subwallet": "https://www.subwallet.app",
    "subwallet-js": "https://www.subwallet.app",
    "Talisman": "https://www.talisman.xyz",
    "talisman": "https://www.talisman.xyz",
    "Trust Wallet": "https://trustwallet.com/",
    "typhoncip30": 'https://typhonwallet.io/',
    "Unisat": "https://unisat.io",
    "vespr": "https://vespr.xyz/",
    "veworld": "https://www.veworld.net",
    "Xverse Wallet": "https://www.xverse.app",
} as any;

type WalletName =
    | 'blockchain'
    | "Backpack"
    | "Block"
    | "Coinbase Wallet"
    | "Compass"
    | "Crossmark"
    | "Defly"
    | "Enkrypt"
    | "enkrypt"
    | "eternl"
    | "Exodus"
    | "Fearless"
    | "fearless-wallet"
    | "Fin"
    | "Flint Wallet"
    | "Gem"
    | "GeroWallet"
    | "Glow"
    | "Keplr"
    | "lace"
    | "Leap"
    | "Leather"
    | "Lute"
    | "MetaMask"
    | "Nami"
    | "NuFi"
    | "OKX"
    | "OKX Wallet"
    | "Pera"
    | "Phantom"
    | "PolkaGate"
    | "polkagate"
    | "Rabby Wallet"
    | "Rainbow"
    | "Solflare"
    | "Subwallet"
    | "subwallet-js"
    | "Talisman"
    | "talisman"
    | "Trust Wallet"
    | "Typhon Wallet"
    | "Unisat"
    | "VESPR"
    | "veworld"
    | "Xverse Wallet";

interface WalletItemProps {
    isBlockchain: boolean;
    blockchain: any;
    walletName: WalletName;
    walletProvider?: string;
    isClickable: boolean;
    isWalletConnecting: boolean;
    isConnecting: boolean;
    title: string;
    desc: string;
    image: string;
    handleBlockchainSelection: (blockchain: string) => void;
    handleWalletSelection: (walletName: string, walletProvider?: string) => Promise<void>;
}

const WalletItem: FC<WalletItemProps> = (props) => {
    const [loaded, setLoaded] = useState(false);

    const handleClick = async (): Promise<void> => {
        if (props.isBlockchain) {
            props.handleBlockchainSelection(props.blockchain);
        }
    };

    const handleDownload = (e: React.MouseEvent<HTMLButtonElement>): void => {
        e.stopPropagation();
        window.open(walletDownloadUrls[props.walletName], '_blank');
    };

    const handleConnect = async (e: React.MouseEvent<HTMLButtonElement>): Promise<void> => {
        e.preventDefault();
        e.stopPropagation();
        if (props.isClickable) {
            if (props.walletProvider) {
                await props.handleWalletSelection(props.walletName, props.walletProvider);
            } else {
                await props.handleWalletSelection(props.walletName);
            }
        }
    };

    return (
        <div className={`w-full h-full rounded-[10px] bg-gray-200 dark:bg-zinc-700 ${props.isBlockchain ? 'blockchains' : ''}`} onClick={handleClick}>
            <div
                className="wallet_card__style"
                style={{
                    pointerEvents: props.isWalletConnecting ? 'none' : undefined,
                }}
            >
                <Skeleton width="90px" height="90px" borderRadius="50%" loading={!loaded}>
                    <img className="w-full h-full rounded-lg aspect-square wallet_icon" id={props.title.toLowerCase()} src={props.image} alt={props.title} onLoad={() => setLoaded(true)} />
                </Skeleton>
                <div className="w-full flex flex-col gap-2 p-2">
                    <h5 className="text-md font-bold text-[#121212] dark:text-white">{props.title}</h5>
                    <p className="text-xs text-[#121212] dark:text-white">{props.desc}</p>
                </div>
                {!props.isBlockchain && (
                    props.isClickable ? (
                        <button
                            className="p-ripple connect-button action__btn"
                            disabled={props.isWalletConnecting}
                            onClick={handleConnect}
                            style={{ pointerEvents: props.isWalletConnecting ? 'none' : undefined }}
                        >
                            {props.isConnecting ? <div className="button-loader-container"><span className="button-loader"></span></div> : 'Connect'}
                            <Ripple />
                        </button>
                    ) : (
                        <button
                            className="p-ripple download-button action__btn"
                            disabled={props.isWalletConnecting}
                            onClick={handleDownload}
                        >
                            Download
                            <Ripple />
                        </button>
                    )
                )}
            </div>
        </div>
    );
};
export default WalletItem;