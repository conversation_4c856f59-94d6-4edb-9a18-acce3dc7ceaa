'use client';

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Folder,
  Image as ImageIcon,
  Video,
  Search,
  Plus,
  ChevronLeft,
  MoreVertical,
  Trash2,
  Edit,
  Download,
  Lock,
  Eye,
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'react-toastify';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Label } from '@/components/ui/label';
import { Upload } from 'lucide-react';
import { debounce } from 'lodash';
import { MediaUploader } from './MediaUploader';

// Define interfaces for TypeScript
interface MediaItem {
  media_id: string;
  file_path: string;
  file_type: string;
  thumbnail_url: string;
  created_at: string;
  privacy_settings: 'public' | 'private' | 'unlisted';
  metadata: Record<string, unknown>;
  tags: string[];
}

interface Folder {
  folder_id: string;
  folder_name: string;
  parent_folder_id: string | null;
}

interface MediaUploaderProps {
  onMediaSelected: (mediaUrl: string, mediaType: string, thumbnailUrl: string) => void;
  saveToLibrary: boolean;
  allowedTypes: string[];
  maxSize: number;
}

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const MediaGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
`;

const FolderGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
`;

const MediaCard = styled.div`
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
  transition: transform 0.2s;
  &:hover {
    transform: scale(1.02);
  }
`;

const PrivacyButton = styled(Button)`
  width: 100%;
  justify-content: space-between;
  text-align: left;
`;

export default function MediaLibraryDashboard({
  onMediaSelected,
  saveToLibrary,
  allowedTypes,
  maxSize,
}: MediaUploaderProps) {
  const [activeTab, setActiveTab] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState<Folder[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [newFolderName, setNewFolderName] = useState<string>('');
  const [showNewFolderInput, setShowNewFolderInput] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [editingMedia, setEditingMedia] = useState<MediaItem | null>(null);
  const [editTags, setEditTags] = useState<string>('');
  const [editPrivacy, setEditPrivacy] = useState<'public' | 'private' | 'unlisted'>('public');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [mediaToDelete, setMediaToDelete] = useState<MediaItem | null>(null);

  // Fetch media items
  const fetchMediaItems = useCallback(
    async (reset: boolean = false) => {
      if (isLoading) return;

      try {
        setIsLoading(true);
        const newPage = reset ? 1 : page;

        let url = `/api/media-library?page=${newPage}&limit=20`;
        if (currentFolder) url += `&folderId=${currentFolder}`;
        if (activeTab !== 'all') url += `&fileType=${activeTab}`;
        if (searchQuery) url += `&search=${encodeURIComponent(searchQuery)}`;

        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP error ${response.status}`);

        const data: { success: boolean; media: MediaItem[]; message?: string } = await response.json();

        if (!data.success) throw new Error(data.message || 'Failed to load media');

        if (reset || newPage === 1) {
          setMediaItems(data.media);
        } else {
          setMediaItems((prev) => [...prev, ...data.media]);
        }

        setHasMore(data.media.length === 20);
        setPage(newPage + 1);
      } catch (error: any) {
        toast.error(error.message || 'Failed to load media');
      } finally {
        setIsLoading(false);
      }
    },
    [isLoading, page, currentFolder, activeTab, searchQuery]
  );

  // Fetch folders
  const fetchFolders = useCallback(async () => {
    try {
      const url = `/api/media-library/folders${currentFolder ? `?parentFolderId=${currentFolder}` : ''}`;
      const response = await fetch(url);
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);

      const data: { success: boolean; folders: Folder[]; message?: string } = await response.json();

      if (!data.success) throw new Error(data.message || 'Failed to load folders');

      setFolders(data.folders);
    } catch (error: any) {
      toast.error(error.message || 'Failed to load folders');
    }
  }, [currentFolder]);

  // Create new folder
  const createFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error('Folder name cannot be empty');
      return;
    }

    try {
      const response = await fetch('/api/media-library/folders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          folderName: newFolderName,
          parentFolderId: currentFolder,
        }),
      });

      if (!response.ok) throw new Error(`HTTP error ${response.status}`);

      const data: { success: boolean; folder: Folder; message?: string } = await response.json();

      if (!data.success) throw new Error(data.message || 'Failed to create folder');

      setFolders((prev) => [...prev, data.folder]);
      setNewFolderName('');
      setShowNewFolderInput(false);
      toast.success('Folder created successfully');
    } catch (error: any) {
      toast.error(error.message || 'Failed to create folder');
    }
  };

  // Navigate to folder
  const navigateToFolder = (folder: Folder) => {
    setCurrentFolder(folder.folder_id);
    setFolderPath((prev) => [...prev, folder]);
    setPage(1);
  };

  // Navigate back
  const navigateBack = () => {
    if (folderPath.length === 0) {
      setCurrentFolder(null);
    } else {
      const newPath = [...folderPath];
      newPath.pop();
      setFolderPath(newPath);
      setCurrentFolder(newPath.length > 0 ? newPath[newPath.length - 1].folder_id : null);
    }
    setPage(1);
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchMediaItems(true);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setPage(1);
    fetchMediaItems(true);
  };

  // Debounced scroll handler
  const handleScroll = useCallback(
    debounce((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      if (scrollHeight - scrollTop <= clientHeight * 1.5 && !isLoading && hasMore) {
        fetchMediaItems();
      }
    }, 200),
    [fetchMediaItems, isLoading, hasMore]
  );

  // Open edit modal
  const openEditModal = (media: MediaItem) => {
    setEditingMedia(media);
    setEditTags(media.tags?.join(', ') || '');
    setEditPrivacy(media.privacy_settings || 'public');
    setIsEditModalOpen(true);
  };

  // Save media edits
  const saveMediaEdits = async () => {
    if (!editingMedia) return;

    try {
      const response = await fetch(`/api/media-library/${editingMedia.media_id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tags: editTags.split(',').map((tag) => tag.trim()).filter(Boolean),
          privacy_settings: editPrivacy,
        }),
      });

      if (!response.ok) throw new Error(`HTTP error ${response.status}`);

      const data: { success: boolean; media: MediaItem; message?: string } = await response.json();

      if (!data.success) throw new Error(data.message || 'Failed to update media');

      setMediaItems((prev) =>
        prev.map((item) =>
          item.media_id === editingMedia.media_id
            ? { ...item, tags: data.media.tags, privacy_settings: data.media.privacy_settings }
            : item
        )
      );

      setIsEditModalOpen(false);
      toast.success('Media updated successfully');
    } catch (error: any) {
      toast.error(error.message || 'Failed to update media');
    }
  };

  // Open delete confirmation
  const openDeleteConfirmation = (media: MediaItem) => {
    setMediaToDelete(media);
    setIsDeleteModalOpen(true);
  };

  // Delete media
  const deleteMedia = async () => {
    if (!mediaToDelete) return;

    try {
      const response = await fetch(`/api/media-library/${mediaToDelete.media_id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error(`HTTP error ${response.status}`);

      const data: { success: boolean; message?: string } = await response.json();

      if (!data.success) throw new Error(data.message || 'Failed to delete media');

      setMediaItems((prev) => prev.filter((item) => item.media_id !== mediaToDelete.media_id));
      setIsDeleteModalOpen(false);
      toast.success('Media deleted successfully');
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete media');
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchMediaItems(true);
    fetchFolders();
  }, [fetchMediaItems, fetchFolders]);

  return (
    <Container>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <h1 className="text-2xl font-bold">Media Library</h1>
          <div className="flex items-center gap-2">
            <Button onClick={() => setIsUploadModalOpen(true)}>
              <Upload className="h-4 w-4 mr-2" />
              Upload Media
            </Button>
            <Button variant="outline" onClick={() => setShowNewFolderInput(!showNewFolderInput)}>
              <Folder className="h-4 w-4 mr-2" />
              New Folder
            </Button>
          </div>
        </div>
        {showNewFolderInput && (
          <div className="mt-4 flex items-center gap-2">
            <Input
              placeholder="Folder name"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              autoFocus
            />
            <Button onClick={createFolder}>Create</Button>
            <Button variant="ghost" onClick={() => setShowNewFolderInput(false)}>
              Cancel
            </Button>
          </div>
        )}
      </div>

      {/* Search and filters */}
      <div className="p-4 border-b">
        <div className="flex flex-col md:flex-row gap-4">
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search media..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </form>
          <Tabs value={activeTab} onValueChange={handleTabChange} className="flex-shrink-0">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="image">Images</TabsTrigger>
              <TabsTrigger value="video">Videos</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Folder navigation */}
      {(currentFolder || folderPath.length > 0) && (
        <div className="px-4 py-2 border-b flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={navigateBack} className="p-1">
            <ChevronLeft size={16} />
          </Button>
          <div className="flex items-center gap-1 text-sm">
            <Button
              variant="ghost"
              size="sm"
              className="p-1"
              onClick={() => {
                setCurrentFolder(null);
                setFolderPath([]);
              }}
            >
              Root
            </Button>
            {folderPath.map((folder, index) => (
              <React.Fragment key={folder.folder_id}>
                <span>/</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-1"
                  onClick={() => {
                    const newPath = folderPath.slice(0, index + 1);
                    setFolderPath(newPath);
                    setCurrentFolder(folder.folder_id);
                  }}
                >
                  {folder.folder_name}
                </Button>
              </React.Fragment>
            ))}
          </div>
        </div>
      )}

      {/* Content area */}
      <div className="flex-1 overflow-y-auto p-4" onScroll={handleScroll}>
        {/* Folders */}
        {folders.length > 0 && (
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-3">Folders</h3>
            <FolderGrid>
              {folders.map((folder) => (
                <div
                  key={folder.folder_id}
                  className="flex flex-col items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-800"
                  onClick={() => navigateToFolder(folder)}
                >
                  <Folder className="h-12 w-12 text-yellow-500 mb-2" />
                  <span className="text-sm text-center truncate w-full">{folder.folder_name}</span>
                </div>
              ))}
            </FolderGrid>
          </div>
        )}

        {/* Media items */}
        <div>
          <h3 className="text-sm font-medium mb-3">Media</h3>
          {isLoading && mediaItems.length === 0 ? (
            <MediaGrid>
              {Array.from({ length: 12 }).map((_, i) => (
                <div key={i} className="flex flex-col">
                  <Skeleton className="aspect-square w-full rounded-md" />
                  <Skeleton className="h-5 w-3/4 mt-2" />
                </div>
              ))}
            </MediaGrid>
          ) : mediaItems.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No media found</p>
            </div>
          ) : (
            <MediaGrid>
              {mediaItems.map((item) => (
                <MediaCard key={item.media_id}>
                  <div className="aspect-square relative">
                    <img
                      src={item.thumbnail_url || item.file_path}
                      alt="Media"
                      className="object-cover w-full h-full"
                    />
                    {item.file_type.startsWith('video/') && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                        <Video className="w-8 h-8 text-white" />
                      </div>
                    )}
                    {item.privacy_settings !== 'public' && (
                      <div className="absolute top-2 left-2">
                        <Lock className="h-4 w-4 text-white bg-black/50 p-0.5 rounded-full" />
                      </div>
                    )}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <Button
                        variant="secondary"
                        size="sm"
                        className="mr-2"
                        onClick={() => window.open(item.file_path, '_blank')}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <DropdownMenu modal={false}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="secondary" size="sm">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[160px]">
                          <DropdownMenuItem onClick={() => openEditModal(item)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>>
                          <DropdownMenuItem onClick={() => window.open(item.file_path, '_blank')}>
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </DropdownMenuItem>>
                          <DropdownMenuItem
                            onClick={() => openDeleteConfirmation(item)}
                            className="text-red-500 focus:text-red-500"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </MediaCard>
              ))}
            </MediaGrid>
          )}
        </div>
      </div>

      {/* Edit Media Modal */}
      <AnimatedModal
        open={isEditModalOpen}
        onOpenChange={(open) => setIsEditModalOpen(open)}
        size="md"
        title="Edit Media"
        footer={
          <div className="flex justify-end gap-2 w-full">
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveMediaEdits}>Save Changes</Button>
          </div>
        }
      >
        <div className="space-y-4 p-1">
          <div>
            <Label htmlFor="tags">Tags (comma separated)</Label>
            <Input
              id="tags"
              value={editTags}
              onChange={(e) => setEditTags(e.target.value)}
              placeholder="tag1, tag2, tag3"
            />
          </div>
          <div>
            <Label htmlFor="privacy">Privacy Settings</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <PrivacyButton variant="outline" id="privacy">
                  {editPrivacy.charAt(0).toUpperCase() + editPrivacy.slice(1)}
                  <ChevronLeft className="h-4 w-4" style={{ transform: 'rotate(-90deg)' }} />
                </PrivacyButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[200px]">
                <DropdownMenuItem onClick={() => setEditPrivacy('public')}>
                  Public
                </DropdownMenuItem>>
                <DropdownMenuItem onClick={() => setEditPrivacy('private')}>
                  Private
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setEditPrivacy('unlisted')}>
                  Unlisted
                </DropdownMenuItem>>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </AnimatedModal>

      {/* Delete Confirmation Modal */}
      <AnimatedModal
        open={isDeleteModalOpen}
        onOpenChange={(open) => setIsDeleteModalOpen(open)}
        size="sm"
        title="Delete Media"
        footer={
          <div className="flex justify-end gap-2 w-full">
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={deleteMedia}>
              Delete
            </Button>
          </div>
        }
      >
        <div className="p-1">
          <p>Are you sure you want to delete this media? This action cannot be undone.</p>
          {mediaToDelete && (
            <div className="mt-4 flex items-center gap-4">
              <div className="w-16 h-16 rounded overflow-hidden">
                <img
                  src={mediaToDelete.thumbnail_url || mediaToDelete.file_path}
                  alt="Media to delete"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <p className="text-sm font-medium">
                  {mediaToDelete.metadata?.filename || 'Untitled'}
                </p>
                <p className="text-xs text-gray-500">
                  {new Date(mediaToDelete.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
          )}
        </div>
      </AnimatedModal>

      {/* Upload Media Modal */}
      <AnimatedModal
        open={isUploadModalOpen}
        onOpenChange={(open) => setIsUploadModalOpen(open)}
        size="lg"
        title="Upload Media"
        closeButton={true}
      >
        <div className="p-1">
          <MediaUploader
            onMediaSelected={(mediaUrl, mediaType, thumbnailUrl) => {
              fetchMediaItems(true);
              setIsUploadModalOpen(false);
              toast.success('Media uploaded successfully');
            }}
            saveToLibrary={saveToLibrary}
            allowedTypes={allowedTypes}
            maxSize={maxSize}
          />
        </div>
      </AnimatedModal>
    </Container>
  );
};