"use client";

import React, { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { motion, AnimatePresence } from "framer-motion";
import { X, ChevronLeft, ChevronRight, Heart, MapPin, MessageSquareText, DollarSign, MoreHorizontal, Send, Pause, Play, Volume2, VolumeX, Volume1 } from "lucide-react";
import Image from "next/image";
import { StoryMediaDisplay } from "@/components/social/StoryMediaDisplay";
import { EmojiPicker } from '@/components/ui/chat/emoji-picker';

export interface StoryMedia {
  id: string;
  mediaUrl: string;
  thumbnailUrl: string;
  mediaType: "image" | "video";
  duration?: number; // in seconds, default for images
}

export interface StoryUser {
  id: string;
  username: string;
  profileImage: string;
  isVerified?: boolean;
  timeAgo?: string;
}

export interface Story {
  id: string;
  userId: string;
  username: string;
  profileImage: string;
  media: StoryMedia[];
  user: StoryUser;
  createdAt: string;
  expiresAt: string;
  isLive: boolean;
  timeAgo: string;
  isViewed?: boolean;
}

interface StoryViewerProps {
  stories: Story[];
  initialStoryId: string;
  initialUserId: string;
  isOpen: boolean;
  onClose: () => void;
  onStoryComplete?: (storyId: string) => void;
  onStoryViewed?: (storyId: string) => void;
  onStoryExpired?: (storyId: string) => void;
}

const StoryViewer: React.FC<StoryViewerProps> = ({
  stories,
  initialStoryId,
  initialUserId,
  isOpen,
  onClose,
  onStoryComplete,
  onStoryViewed,
  onStoryExpired,
}) => {
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState<number[]>([]);
  const [videoProgress, setVideoProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [commentText, setCommentText] = useState('');
  const [commentsOpen, setCommentsOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [isMuted, setIsMuted] = useState(true);
  const [volume, setVolume] = useState(0);
  const [previousVolume, setPreviousVolume] = useState(0.5);
  const [isVolumeExpanded, setIsVolumeExpanded] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messageInputRef = useRef<HTMLInputElement>(null);
  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null);
  const isRemovedRef = useRef(false);

  // Determine current user and story after all hooks
  const currentUser = stories[currentUserIndex];
  const currentStory = currentUser?.media[currentStoryIndex];

  // Find initial user and story indices
  useEffect(() => {
    if (stories.length > 0 && isOpen) {
      const userIndex = stories.findIndex((user) => user.userId === initialUserId);
      const validUserIndex = userIndex >= 0 ? userIndex : 0;
      setCurrentUserIndex(validUserIndex);

      if (validUserIndex >= 0) {
        const storyIndex = stories[validUserIndex].media.findIndex((story) => story.id === initialStoryId);
        setCurrentStoryIndex(storyIndex >= 0 ? storyIndex : 0);
        setProgress(new Array(stories[validUserIndex].media.length).fill(0));
      }
    }
  }, [stories, initialStoryId, initialUserId, isOpen]);

  // Sync video progress and control playback
  useEffect(() => {
    const video = videoRef.current;
    const currentStory = stories[currentUserIndex]?.media[currentStoryIndex];
    if (!video || !isOpen || currentStory?.mediaType !== "video" || !(video instanceof HTMLVideoElement)) {
      //console.log("Skipping video useEffect:", { video, isOpen, mediaType: currentStory?.mediaType, isVideoElement: video instanceof HTMLVideoElement });
      return;
    }

    //console.log("Video ref:", video, "Media type:", currentStory.mediaType, "Story ID:", currentStory.id, "TagName:", video.tagName);

    const updateProgress = () => {
      const duration = video.duration || currentStory.duration || 10;
      setVideoProgress(video.currentTime);
      setProgress((prev) => {
        const newProgress = [...prev];
        newProgress[currentStoryIndex] = (video.currentTime / duration) * 100;
        return newProgress;
      });
    };

    const handleEnded = () => {
      if (onStoryComplete && currentStory) {
        onStoryComplete(currentStory.id);
      }
      goToNextStory();
    };

    video.addEventListener("timeupdate", updateProgress);
    video.addEventListener("ended", handleEnded);

    // Auto-play video
    if (!isPaused) {
      video.play().catch((e) => console.warn("Video playback failed:", e));
    } else {
      video.pause();
    }

    return () => {
      video.removeEventListener("timeupdate", updateProgress);
      video.removeEventListener("ended", handleEnded);
    };
  }, [currentUserIndex, currentStoryIndex, isOpen, isPaused, stories, onStoryComplete]);

  // Handle story progression for images
  useEffect(() => {
    const currentStory = stories[currentUserIndex]?.media[currentStoryIndex];
    if (!isOpen || isPaused || currentStory?.mediaType === "video" || !currentStory) return;

    if (onStoryViewed) {
      onStoryViewed(currentStory.id);
    }

    setIsLoading(true);

    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }

    const duration = Math.min(currentStory.duration || 5, 20) * 1000;
    const interval = 100;
    let elapsed = 0;

    progressIntervalRef.current = setInterval(() => {
      elapsed += interval;
      setProgress((prev) => {
        const newProgress = [...prev];
        newProgress[currentStoryIndex] = Math.min(100, (elapsed / duration) * 100);
        return newProgress;
      });

      if (elapsed >= duration) {
        clearInterval(progressIntervalRef.current!);
        progressIntervalRef.current = null;
        if (onStoryComplete) {
          onStoryComplete(currentStory.id);
        }
        goToNextStory();
      }
    }, interval);

    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    };
  }, [currentUserIndex, currentStoryIndex, isOpen, isPaused, stories, onStoryComplete, onStoryViewed]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  // Set up and clean up portal node
  useEffect(() => {
    if (isOpen) {
      let node = document.getElementById("story-viewer-portal");
      if (!node) {
        node = document.createElement("div");
        node.id = "story-viewer-portal";
        node.style.position = "fixed";
        node.style.top = "0";
        node.style.left = "0";
        node.style.width = "100%";
        node.style.height = "100%";
        node.style.zIndex = "9999";
        document.body.appendChild(node);
        isRemovedRef.current = false;
      }
      setPortalNode(node);
    } else if (portalNode && !isRemovedRef.current) {
      if (document.body.contains(portalNode)) {
        document.body.removeChild(portalNode);
        isRemovedRef.current = true;
      }
      setPortalNode(null);
    }

    return () => {
      if (portalNode && !isRemovedRef.current && document.body.contains(portalNode)) {
        document.body.removeChild(portalNode);
        isRemovedRef.current = true;
      }
      setPortalNode(null);
    };
  }, [isOpen]);

  const goToNextStory = () => {
    const currentUserStories = stories[currentUserIndex]?.media || [];
    if (currentStoryIndex < currentUserStories.length - 1) {
      setCurrentStoryIndex((prev) => prev + 1);
      setProgress((prev) => {
        const newProgress = [...prev];
        newProgress[currentStoryIndex] = 0;
        return newProgress;
      });
      setVideoProgress(0);
    } else if (currentUserIndex < stories.length - 1) {
      setCurrentUserIndex((prev) => prev + 1);
      setCurrentStoryIndex(0);
      setProgress(new Array(stories[currentUserIndex + 1]?.media.length || 0).fill(0));
      setVideoProgress(0);
    } else {
      onClose();
    }
  };

  const goToPrevStory = () => {
    if (currentStoryIndex > 0) {
      setCurrentStoryIndex((prev) => prev - 1);
      setProgress((prev) => {
        const newProgress = [...prev];
        newProgress[currentStoryIndex] = 0;
        return newProgress;
      });
      setVideoProgress(0);
    } else if (currentUserIndex > 0) {
      setCurrentUserIndex((prev) => prev - 1);
      const prevUserStories = stories[currentUserIndex - 1]?.media || [];
      setCurrentStoryIndex(prevUserStories.length - 1);
      setProgress(new Array(prevUserStories.length).fill(0));
      setVideoProgress(0);
    }
  };

  const handleMediaLoad = () => {
    setIsLoading(false);
    const video = videoRef.current;
    const currentStory = stories[currentUserIndex]?.media[currentStoryIndex];
    if (video && currentStory?.mediaType === "video" && video instanceof HTMLVideoElement) {
      video.muted = isMuted;
      video.volume = volume;
      if (!isPaused) {
        video.play().catch((e) => console.warn("Video playback failed in handleMediaLoad:", e));
      }
    } else {
      /* console.log("Media loaded:", {
        isVideo: currentStory?.mediaType === "video",
        videoRef: video,
        isVideoElement: video instanceof HTMLVideoElement,
        tagName: video?.tagName,
      }); */
    }
  };

  const handleMediaClick = (e: React.MouseEvent) => {
    const { clientX } = e;
    const { innerWidth } = window;

    if (clientX < innerWidth / 3) {
      goToPrevStory();
    } else if (clientX > (innerWidth * 2) / 3) {
      goToNextStory();
    } else {
      setIsPaused((prev) => {
        const newPaused = !prev;
        const video = videoRef.current;
        const currentStory = stories[currentUserIndex]?.media[currentStoryIndex];
        if (video && currentStory?.mediaType === "video" && video instanceof HTMLVideoElement) {
          newPaused ? video.pause() : video.play().catch((e) => console.warn("Video playback failed in handleMediaClick:", e));
        }
        return newPaused;
      });
    }
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      //console.log(`Sending message to ${stories[currentUserIndex]?.username}: ${message}`);
      setMessage("");
      messageInputRef.current?.focus();
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setCommentText((prev) => prev + emoji);
  };

  const toggleMute = () => {
    setIsMuted((prev) => !prev);
    const video = videoRef.current;
    const currentStory = stories[currentUserIndex]?.media[currentStoryIndex];
    if (video && currentStory?.mediaType === "video" && video instanceof HTMLVideoElement) {
      if (isMuted) {
        setVolume(previousVolume);
        video.muted = false;
        video.volume = previousVolume;
      } else {
        setPreviousVolume(volume);
        setVolume(0);
        video.muted = true;
        video.volume = 0;
      }
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
    const video = videoRef.current;
    const currentStory = stories[currentUserIndex]?.media[currentStoryIndex];
    if (video && currentStory?.mediaType === "video" && video instanceof HTMLVideoElement) {
      video.volume = newVolume;
      video.muted = newVolume === 0;
    }
    if (newVolume > 0) {
      setPreviousVolume(newVolume);
    }
  };

  const handleScrub = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTime = parseFloat(e.target.value);
    const video = videoRef.current;
    const currentStory = stories[currentUserIndex]?.media[currentStoryIndex];
    if (video && currentStory?.mediaType === "video" && video instanceof HTMLVideoElement) {
      video.currentTime = newTime;
      setVideoProgress(newTime);
      setProgress((prev) => {
        const newProgress = [...prev];
        const duration = video.duration || currentStory.duration || 10;
        newProgress[currentStoryIndex] = (newTime / duration) * 100;
        return newProgress;
      });
      if (!isPaused) {
        video.play().catch((e) => console.warn("Video playback failed in handleScrub:", e));
      }
    }
  };

  const VolumeIcon = () => {
    if (isMuted || volume === 0) return <VolumeX size={24} />;
    if (volume <= 0.5) return <Volume1 size={24} />;
    return <Volume2 size={24} />;
  };

  // Portal content
  const storyViewerContent = (
    <AnimatePresence>
      {isOpen && stories.length > 0 && currentUser && currentStory && (
        <motion.div
          className="fixed inset-0 z-[9999] bg-black/70 backdrop-blur-md flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close */}
          <button className="absolute top-4 left-4 text-white p-2 z-[10000]" onClick={onClose}>
            <X className="h-6 w-6" />
          </button>

          {/* STORY + RAIL wrapper */}
          <div className="flex items-center space-x-4">
            {/* 350px story container */}
            <div className="relative w-[380px] h-[95vh] rounded-lg overflow-hidden">
              {/* PROGRESS BARS FOR MULTIPLE MEDIA ITEMS */}
              <div className="absolute inset-x-0 top-2 px-4 z-20 flex gap-2">
                {currentUser.media.map((media, index) => (
                  <div
                    key={media.id}
                    className="flex-1 h-1 bg-gray-300/50 rounded-full"
                  >
                    <div
                      className="h-full bg-white rounded-full"
                      style={{
                        width: `${
                          index < currentStoryIndex
                            ? 100
                            : index === currentStoryIndex
                            ? progress[currentStoryIndex] || 0
                            : 0
                        }%`,
                      }}
                    />
                  </div>
                ))}
              </div>

              {/* HEADER OVERLAY */}
              <div className="absolute inset-x-0 top-0 p-4 opacity-0 hover:opacity-100 z-10 flex items-center justify-start gap-2">
                <button
                  onClick={() => setIsPaused((p) => {
                    const newPaused = !p;
                    const video = videoRef.current;
                    if (video && currentStory?.mediaType === "video" && video instanceof HTMLVideoElement) {
                      newPaused ? video.pause() : video.play().catch((e) => console.warn("Video playback failed in pause button:", e));
                    }
                    return newPaused;
                  })}
                  className="bg-black/40 backdrop-blur-sm rounded-full p-2 text-white"
                >
                  {isPaused ? <Play size={24} /> : <Pause size={24} />}
                </button>

                {/* Volume Control with Inline Slider */}
                <div
                  className="relative flex items-center justify-center gap-2 bg-black/40 backdrop-blur-sm rounded-full p-2 text-white"
                  onMouseEnter={() => setIsVolumeExpanded(true)}
                  onMouseLeave={() => setIsVolumeExpanded(false)}
                >
                  <button onClick={toggleMute}>
                    {VolumeIcon()}
                  </button>
                  <AnimatePresence>
                    {isVolumeExpanded && (
                      <motion.div
                        initial={{ width: 0, opacity: 0 }}
                        animate={{ width: 100, opacity: 1 }}
                        exit={{ width: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="relative top-full z-20"
                      >
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={volume}
                          onChange={handleVolumeChange}
                          className="w-full"
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              {/* PREV/NEXT ARROWS */}
              <button
                onClick={goToPrevStory}
                className="absolute left-0 top-1/2 transform -translate-y-1/2 p-2 text-white opacity-75 hover:opacity-100 z-20"
              >
                <ChevronLeft size={32} />
              </button>

              <button
                onClick={goToNextStory}
                className="absolute right-0 top-1/2 transform -translate-y-1/2 p-2 text-white opacity-75 hover:opacity-100 z-20"
              >
                <ChevronRight size={32} />
              </button>

              {/* MEDIA */}
              <StoryMediaDisplay
                url={currentStory.mediaUrl}
                thumbnail={currentStory.thumbnailUrl}
                type={currentStory.mediaType}
                isMuted={isMuted}
                onLoaded={handleMediaLoad}
                ref={videoRef}
              />

              {/* PROGRESS/SCROLL BAR OVERLAY */}
              <div className="absolute inset-x-0 bottom-0 p-4 z-10">
                {currentStory.mediaType === "video" ? (
                  <input
                    type="range"
                    min="0"
                    max={videoRef.current?.duration || currentStory.duration || 10}
                    step="0.1"
                    value={videoProgress}
                    onChange={handleScrub}
                    className="w-full h-1 bg-gray-300/50 rounded-full appearance-none cursor-pointer"
                    style={{
                      background: `linear-gradient(to right, white 0%, white ${(videoProgress / (videoRef.current?.duration || currentStory.duration || 10)) * 100}%, #6B7280 ${(videoProgress / (videoRef.current?.duration || currentStory.duration || 10)) * 100}%, #6B7280 100%)`,
                    }}
                  />
                ) : (
                  <div className="w-full h-1 bg-gray-300/50 rounded-full">
                    <div
                      className="h-full bg-white rounded-full"
                      style={{ width: `${progress[currentStoryIndex] || 0}%` }}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* ACTION RAIL */}
            <div className="flex flex-col items-center justify-end gap-6 h-[90vh]">
              <button className="flex flex-col items-center text-white hover:text-gray-300">
                <Heart size={28} />
                <span className="text-sm mt-1">2</span>
              </button>
              <button onClick={() => setCommentsOpen((o) => !o)} className="flex flex-col items-center text-white hover:text-gray-300">
                <MessageSquareText size={28} />
                <span className="text-sm mt-1">33</span>
              </button>
              <button className="flex flex-col items-center text-white hover:text-gray-300">
                <MapPin size={28} />
                <span className="text-sm mt-1">Join</span>
              </button>
              <button className="flex flex-col items-center text-white hover:text-gray-300">
                <DollarSign size={28} />
                <span className="text-sm mt-1">Tip</span>
              </button>
              <button className="flex flex-col items-center text-white hover:text-gray-300">
                <MoreHorizontal size={28} />
              </button>
              <button>
                <Image
                  src={currentUser.profileImage}
                  alt={currentUser.username}
                  width={40}
                  height={40}
                  className="rounded-full border-2 border-solid border-white"
                />
              </button>
            </div>
          </div>

          {/* COMMENTS SLIDE-IN PANEL */}
          <AnimatePresence>
            {commentsOpen && (
              <motion.aside
                initial={{ x: "100%" }}
                animate={{ x: 0 }}
                exit={{ x: "100%" }}
                transition={{ type: "tween", duration: 0.3 }}
                className="absolute top-0 right-0 h-full w-[350px] bg-black/90 backdrop-blur-md z-30 flex flex-col"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Panel header */}
                <div className="flex items-center justify-between p-4 border-b border-white/20">
                  <div className="text-white font-bold">Comments 2.4K</div>
                  <button onClick={() => setCommentsOpen(false)} className="text-white">
                    <X size={20} />
                  </button>
                </div>

                {/* Comments list (mocked) */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  <div className="text-white">
                    <span className="font-semibold">@TROLL-CLIPX</span> Hey hello support me…
                  </div>
                  <div className="text-white">
                    <span className="font-semibold">@AntoniaRikki</span> Fun fact…
                  </div>
                </div>

                {/* Add comment input */}
                <div className="p-4 border-t border-white/20">
                  <form className="flex items-center gap-2 w-full">
                    <div className="relative flex items-center gap-2 bg-white/10 text-white rounded-full px-4 py-2">
                      <input
                        type="text"
                        placeholder="Add a comment…"
                        className="bg-transparent border-none shadow-none focus:outline-none w-full"
                      />
                      <button type="submit" className="text-white p-2">
                        <Send size={20} />
                      </button>
                    </div>
                    <EmojiPicker
                      onChange={handleEmojiSelect}
                      className="h-6 w-6 text-blue-gray hover:text-turquoise dark:text-white dark:hover:text-turquoise"
                    />
                  </form>
                </div>
              </motion.aside>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return portalNode ? createPortal(storyViewerContent, portalNode) : null;
};
export default StoryViewer;