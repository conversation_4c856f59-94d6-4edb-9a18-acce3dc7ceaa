export interface GlobalSettingsSlice {
    isHideSmallBalances: boolean;
    defaultBuySize: number;
    isOpenShortcuts: boolean;
    isGlobalSettingsOpen: boolean;
    isAdvancedMode: boolean;
    isShortcutsDisabled: boolean;
    isExactTime: boolean;
    isPriceChangeInverted: boolean;
    isAnimationsDisabled: boolean;
    isMyOrdersOnTrends: boolean;
    isPricesFlipped: boolean;
    partnerName?: string;
    partnerCode?: string;
    inputs?: string[];
    setIsHideSmallBalances: (isHideSmallBalances: boolean) => void;
    setDefaultBuySize: (defaultBuySize: number) => void;
    toggleOpenShortcuts: () => void;
    toggleIsGlobalSettingsOpen: () => void;
    setIsAdvancedMode: (isAdvancedMode: boolean) => void;
    setIsShortcutsDisabled: (isShortcutsDisabled: boolean) => void;
    setIsExactTime: (isExactTime: boolean) => void;
    setIsPriceChangeInverted: (isPriceChangeInverted: boolean) => void;
    setIsAnimationsDisabled: (isAnimationsDisabled: boolean) => void;
    setIsMyOrdersOnTrends: (isMyOrdersOnTrends: boolean) => void;
    setIsPricesFlipped: (isPricesFlipped: boolean) => void;
    setPartner: (partnerName: string, partnerCode: string) => void;
    setInputs: (inputs: string[]) => void;
}
declare const createGlobalSettingsSlice: (set: any) => {
    isHideSmallBalances: boolean;
    defaultBuySize: number;
    isOpenShortcuts: boolean;
    isGlobalSettingsOpen: boolean;
    isAdvancedMode: boolean;
    isShortcutsDisabled: boolean;
    isExactTime: boolean;
    isPriceChangeInverted: boolean;
    isAnimationsDisabled: boolean;
    isMyOrdersOnTrends: boolean;
    isPricesFlipped: boolean;
    partnerName: undefined;
    partnerCode: undefined;
    inputs: never[];
    setIsPriceChangeInverted: (isPriceChangeInverted: boolean) => void;
    setIsAdvancedMode: (isAdvancedMode: boolean) => void;
    setIsHideSmallBalances: (isHideSmallBalances: boolean) => void;
    setDefaultBuySize: (defaultBuySize: number) => void;
    toggleOpenShortcuts: () => void;
    toggleIsGlobalSettingsOpen: () => void;
    setIsShortcutsDisabled: (isShortcutsDisabled: boolean) => void;
    setIsExactTime: (isExactTime: boolean) => void;
    setIsAnimationsDisabled: (isAnimationsDisabled: boolean) => void;
    setIsMyOrdersOnTrends: (isMyOrdersOnTrends: boolean) => void;
    setIsPricesFlipped: (isPricesFlipped: boolean) => void;
    setPartner: (partnerName: string, partnerCode: string) => void;
    setInputs: (inputs: string[]) => void;
};
export default createGlobalSettingsSlice;
