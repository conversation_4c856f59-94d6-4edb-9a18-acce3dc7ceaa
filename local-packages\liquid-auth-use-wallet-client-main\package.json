{"name": "@algorandfoundation/liquid-auth-use-wallet-client", "version": "1.2.0", "description": "A TypeScript library for Liquid Auth's Use-Wallet Client", "main": "lib/index.js", "type": "module", "types": "lib/types/index.d.ts", "scripts": {"build": "tsc && tsc-alias -p tsconfig.json -f", "lint": "eslint 'src/**/*.ts'", "test": "vitest", "clean": "rm -rf lib", "coverage": "vitest run --coverage"}, "exports": {".": {"types": "./lib/types/index.d.ts", "default": "./lib/index.js"}, "./interfaces": {"types": "./lib/types/interfaces.d.ts", "default": "./lib/interfaces.js"}}, "keywords": [], "author": "Algorand Foundation", "license": "AGPL-3.0", "dependencies": {"@algorandfoundation/liquid-client": "github:algorandfoundation/liquid-auth-js", "@algorandfoundation/provider": "github:algorandfoundation/wallet-provider-ts", "cbor-x": "^1.6.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^12.0.1", "@vitest/coverage-v8": "^2.1.5", "algosdk": "^3.1.0", "eslint": "^9.15.0", "globals": "^15.12.0", "jsdom": "^25.0.1", "semantic-release": "^24.2.0", "tsc-alias": "^1.8.10", "typescript": "^4.9.5", "typescript-eslint": "^8.16.0", "vitest": "^2.1.5"}, "repository": "**************:algorandfoundation/liquid-auth-use-wallet-client", "files": ["lib", "README.md", "LICENSE"], "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json"], "message": "chore(release): Liquid Auth Use-Wallet Client \n\n${nextRelease.notes}"}], ["@semantic-release/github", {"successComment": false}]], "branches": ["release/*", {"name": "main", "prerelease": "canary"}]}}