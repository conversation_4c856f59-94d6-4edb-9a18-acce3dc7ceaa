import React, { useState, useRef } from 'react';
import { useCreateComment } from '@/hooks/convexHooks';
import { toast } from 'react-toastify';
import { Button } from '@/components/ui/button';
import { EmojiPicker } from '@/components/ui/chat/emoji-picker';
import { Image as ImageIcon, Smile, Mic, SendHorizontal } from 'lucide-react';
import Tippy from '@tippyjs/react';

interface CommentFormProps {
  postId: string;
  onCommentAdded: (comment: any) => void;
}

const CommentForm: React.FC<CommentFormProps> = ({ postId, onCommentAdded }) => {
  const createCommentMutation = useCreateComment();
  const [commentText, setCommentText] = useState('');
  // Media upload is not supported in Convex comment mutation yet

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!commentText.trim()) return;
    try {
      const newComment = await createCommentMutation({ contentId: postId, content: commentText });
      onCommentAdded(newComment);
      setCommentText('');
    } catch (error: any) {
      toast.error('Failed to create comment');
      console.error('Failed to create comment:', error);
    }
  };

  // Media upload handler (not supported yet)
  // const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => { ... }

  const handleEmojiSelect = (emoji: string) => {
    setCommentText(prev => prev + emoji);
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="flex items-center justify-center gap-2 w-full pb-2 bg-transparent rounded-lg">
        <div className="flex items-center gap-3 w-full bg-transparent rounded-full px-4 py-2 border border-solid border-gray-300 dark:border-white">
          <input
            value={commentText}
            onChange={(e) => setCommentText(e.target.value)}
            placeholder="Write Comment..."
            className="flex-grow bg-transparent border-none focus:outline-none text-gorilla-gray dark:text-white placeholder-gray-400 dark:placeholder-gray-400 placeholder:text-sm"
          />

          <Tippy content="Insert Emoji" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
            <EmojiPicker onChange={handleEmojiSelect} className="h-6 w-6 text-blue-gray hover:text-turquoise dark:text-white dark:hover:text-turquoise" />
          </Tippy>

          <button
            type="submit"
            disabled={!commentText.trim()}
            className="text-blue-gray hover:text-turquoise dark:text-white dark:hover:text-turquoise disabled:opacity-50"
          >
            <SendHorizontal className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Media upload not supported in Convex comment mutation yet */}
    </form>
  );
};
export default CommentForm;
