import { jsx as s, jsxs as o } from "react/jsx-runtime";
import { Skeleton as I } from "../../../components/ui/skeleton.js";
import x from "../../../store/useStore.js";
import { s as L } from "../../../shallow-27fd7e97.js";
import { memo as M, useEffect as F, useState as $, useRef as l, useMemo as f } from "react";
import { dexes as H } from "../../../constants/dexes.js";
import { cn as U } from "../../../lib/utils.js";
import { u as V } from "../../../react-hotkeys-hook.esm-60f1c3b8.js";
import Y from "./SwapDetailSplit.js";
import q from "./SwapDetailOutput.js";
import z from "../../../assets/svg/IconX.js";
import D from "../../../assets/svg/IconDown.js";
import G from "../../../trends/components/PriceFormatter.js";
import C from "../../../components/common/RealtimePulse.js";
import { Separator as J } from "../../../components/ui/separator.js";
import "../../../lib.js";
import "../../../extend-tailwind-merge-e63b2b56.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../tokens.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
import "./OrderPreview.js";
import "../../../components/ui/progress.js";
import "../../../index-1c873780.js";
import "../../../index-563d1ed8.js";
import "../../../index-c8f2666b.js";
import "../../../utils/formatNumber.js";
import "../../../components/ui/tooltipDialog.js";
import "../../../hooks/useScreen.js";
import "../../../components/ui/dialog.js";
import "../../../index-840f2930.js";
import "../../../index-c7156e07.js";
import "../../../index-4914f99c.js";
import "../../../index-67500cd3.js";
import "../../../index-27cadef5.js";
import "../../../index-5116e957.js";
import "../../../components/ui/tooltip.js";
import "../../../index-0ce202b9.js";
import "../../../index-bcfeaad9.js";
import "../../../index-f7426637.js";
import "../../../assets/svg/IconSquareInfo.js";
import "../../../x-9e07c78a.js";
import "../../../createLucideIcon-7a477fa6.js";
import "../../../components/common/TokenPrice.js";
import "../../../utils/formatToken.js";
import "../../../assets/svg/IconTwoWay.js";
function K() {
  const {
    tokenBuy: i,
    tokenSell: r,
    tokenPrice: t,
    isTokenPriceLoading: S,
    sellAmount: p,
    swapDetails: n,
    swapType: m,
    estimationError: w,
    bonusOutput: O
  } = x(
    (e) => ({
      tokenBuy: e.swapSlice.tokenBuy,
      tokenSell: e.swapSlice.tokenSell,
      tokenPrice: e.swapSlice.tokenPrice,
      isTokenPriceLoading: e.swapSlice.isTokenPriceLoading,
      sellAmount: e.swapSlice.sellAmount,
      swapDetails: e.swapSlice.swapDetails,
      swapType: e.tokenSearchSlice.swapType,
      estimationError: e.swapSlice.estimationError,
      bonusOutput: e.swapSlice.bonusOutput
    }),
    L
  ), { isDetailsOpen: g, isOpenSwapOverview: _, setIsOpenSwapOverview: a } = x(
    (e) => e.swapSettingsSlice
  ), { isPricesFlipped: c, setIsPricesFlipped: b } = x(
    (e) => e.globalSettingsSlice
  ), v = () => {
    const e = !c;
    b(e), localStorage.setItem("priceFlipSetting", e.toString());
  };
  V("p", v), F(() => {
    const e = localStorage.getItem("priceFlipSetting");
    e !== null && b(e === "true");
  }, []);
  const [y, h] = $(""), N = l(t == null ? void 0 : t.price_ba), k = l(), T = l(), P = l();
  F(() => {
    const e = Math.round((t == null ? void 0 : t.price_ba) * 1e4), d = Math.round(N.current * 1e4), u = k.current !== r || T.current !== i, E = P.current !== p;
    if (N.current = t == null ? void 0 : t.price_ba, k.current = r, T.current = i, P.current = p, e !== d && p !== 0 && !u && !E) {
      d < e ? h("dhs-animate-fade-green") : d > e && h("dhs-animate-fade-red");
      const A = setTimeout(() => {
        h("");
      }, 2e3);
      return () => clearTimeout(A);
    }
  }, [t, p, r, i]);
  const R = f(() => m === "BUY" ? {
    tokenBuySymbol: r == null ? void 0 : r.ticker,
    tokenSellSymbol: i == null ? void 0 : i.ticker,
    tokenSellPrice: t.price_ba
  } : m === "SELL" ? {
    tokenBuySymbol: i == null ? void 0 : i.ticker,
    tokenSellSymbol: r == null ? void 0 : r.ticker,
    tokenSellPrice: t.price_ba
  } : {
    tokenBuySymbol: i == null ? void 0 : i.ticker,
    tokenSellSymbol: r == null ? void 0 : r.ticker,
    tokenSellPrice: t.price_ba
  }, [m, t, i, r]), B = f(() => {
    if (w === "Pool Not Found")
      return /* @__PURE__ */ s("div", { children: "No Pool Found" });
    if (!(t != null && t.price_ba))
      return /* @__PURE__ */ s(I, { className: "dhs-p-2 dhs-h-4 dhs-w-32 dhs-bg-inherit" });
    const { tokenBuySymbol: e, tokenSellSymbol: d, tokenSellPrice: u } = R;
    return /* @__PURE__ */ o(
      "div",
      {
        className: U(
          "dhs-flex dhs-items-center dhs-gap-2 hover:dhs-text-gray-400 dhs-transition-all dhs-duration-100"
        ),
        children: [
          /* @__PURE__ */ s(C, {}),
          /* @__PURE__ */ o(
            "div",
            {
              className: `dhs-flex dhs-items-center dhs-gap-1 dhs-cursor-pointer ${y} dhs-leading-none`,
              onClick: v,
              children: [
                /* @__PURE__ */ o("span", { className: "sm:dhs-text-mainText sm:dhs-font-proximaSemiBold sm:dhs-text-sm", children: [
                  "1 ",
                  d
                ] }),
                /* @__PURE__ */ s("span", { className: "dhs-text-subText sm:dhs-font-proximaSemiBold sm:dhs-text-sm", children: "=" }),
                /* @__PURE__ */ o("span", { className: "sm:dhs-text-mainText sm:dhs-font-proximaSemiBold sm:dhs-text-sm", children: [
                  /* @__PURE__ */ s(G, { price: u }),
                  " ",
                  e,
                  " "
                ] })
              ]
            }
          )
        ]
      }
    );
  }, [
    t,
    c,
    m,
    y,
    S,
    R,
    w
  ]), j = f(() => g ? /* @__PURE__ */ o("div", { className: "dhs-flex dhs-items-center dhs-text-md dhs-gap-2 dhs-text-mainText dhs-font-proximaSemiBold dhs-ml-2", children: [
    /* @__PURE__ */ s("span", { children: "Swap Preview " }),
    /* @__PURE__ */ s(C, {})
  ] }) : /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-text-sm dhs-gap-2 dhs-text-mainText dhs-font-proximaSemiBold dhs-w-full", children: B }), [
    i,
    r,
    t,
    S,
    n,
    g,
    c,
    B,
    O
  ]);
  return _ ? /* @__PURE__ */ s("div", { className: "dhs-bg-containers dhs-py-0 dhs-px-3 @sm/appRoot:dhs-px-5 dhs-pt-0 dhs-rounded-[20px] dhs-h-[338px] @md/appRoot:dhs-h-[348px] @sm/appRoot:dhs-h-[352px] dhs-mt-2.5", children: /* @__PURE__ */ o("div", { className: "dhs-h-full dhs-flex dhs-flex-col dhs-justify-evenly", children: [
    /* @__PURE__ */ o("div", { className: "dhs-flex dhs-justify-between dhs-gap-2 dhs-items-center dhs-py-5 dhs-pb-0", children: [
      /* @__PURE__ */ s("div", { className: "dhs-flex dhs-gap-4 dhs-items-center", children: /* @__PURE__ */ s("span", { className: "dhs-text-mainText dhs-text-md dhs-font-proximaBold dhs-leading-none", children: "Swap Preview" }) }),
      /* @__PURE__ */ s("div", { className: "dhs-flex dhs-gap-4 dhs-items-center", children: /* @__PURE__ */ s(
        z,
        {
          className: "dhs-text-subText dhs-cursor-pointer dhs-w-[15px] dhs-h-[15px]",
          onClick: () => a(!1)
        }
      ) })
    ] }),
    /* @__PURE__ */ o("div", { className: "@md/appRoot:dhs-pb-4 dhs-py-2 dhs-grid", children: [
      /* @__PURE__ */ s(q, {}),
      /* @__PURE__ */ s(J, { className: "dhs-bg-subText @md/appRoot:dhs-my-3.5 dhs-my-2" }),
      /* @__PURE__ */ s(
        Y,
        {
          dexes: H,
          dexSplits: n.splits,
          possibleSplits: n.possible_routes
        }
      )
    ] }),
    /* @__PURE__ */ s(
      "div",
      {
        className: "dhs-flex dhs-justify-center dhs-items-center dhs-w-full dhs-cursor-pointer",
        onClick: () => a(!1),
        children: /* @__PURE__ */ o("div", { className: "dhs-flex dhs-items-center dhs-gap-[6px]", children: [
          /* @__PURE__ */ s("span", { className: "dhs-text-accent dhs-font-proximaBold dhs-text-sm", children: "HIDE" }),
          /* @__PURE__ */ s(D, { className: "dhs-text-accent dhs-w-2" })
        ] })
      }
    )
  ] }) }) : /* @__PURE__ */ o("div", { className: "dhs-flex dhs-w-full dhs-py-[2px] @md/appRoot:dhs-py-[2px] @md/appRoot:dhs-px-4 @sm/appRoot:dhs-px-5 dhs-justify-between dhs-items-center dhs-gap-2 dhs-h-[30px]", children: [
    /* @__PURE__ */ s("div", { className: "dhs-flex dhs-justify-between dhs-w-full", children: j }),
    /* @__PURE__ */ o(
      "div",
      {
        className: "dhs-flex dhs-items-center dhs-cursor-pointer dhs-gap-[6px]",
        onClick: () => a(!0),
        children: [
          /* @__PURE__ */ s("span", { className: "dhs-text-accent dhs-text-sm dhs-font-proximaBold", children: "DETAILS" }),
          /* @__PURE__ */ s(D, { className: "dhs-text-accent dhs-w-2 dhs-rotate-180" })
        ]
      }
    )
  ] });
}
const We = M(K);
export {
  We as default
};
