# Liquid Auth - Use-Wallet Client/Provider/Wallet Library

This contains code abstracted out of the Use-Wallet Liquid Auth Wallet provider in [Use-Wallet](https://github.com/TxnLab/use-wallet/tree/main).

# Installation

This module uses pnpm, so [install it first](https://pnpm.io).

Then use `pnpm run build` to build the lib, and `pnpm run test` or `pnpm run coverage` to run vitest in trailing mode or to produce test coverage.

# NPM

This package can be found on NPM and downloaded as such:

```bash
npm i @algorandfoundation/liquid-auth-use-wallet-client
```
