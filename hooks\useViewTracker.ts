'use client';

import { useEffect, useRef, useCallback } from 'react';

interface ViewTrackerOptions {
  threshold?: number;
  rootMargin?: string;
  trackOnce?: boolean;
}

interface ViewTrackerEntry {
  postId: string;
  onView: () => void;
  tracked?: boolean;
}

class ViewTrackerManager {
  private static instance: ViewTrackerManager;
  private observer: IntersectionObserver | null = null;
  private entries = new Map<string, ViewTrackerEntry>();
  private options: ViewTrackerOptions = {
    threshold: 0.5,
    rootMargin: '0px',
    trackOnce: true
  };

  static getInstance(): ViewTrackerManager {
    if (!ViewTrackerManager.instance) {
      ViewTrackerManager.instance = new ViewTrackerManager();
    }
    return ViewTrackerManager.instance;
  }

  private constructor() {
    this.initializeObserver();
  }

  private initializeObserver() {
    if (typeof window === 'undefined') return;

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const postId = entry.target.id.replace('post-', '');
            const trackerEntry = this.entries.get(postId);

            if (trackerEntry && (!trackerEntry.tracked || !this.options.trackOnce)) {
              trackerEntry.onView();

              if (this.options.trackOnce) {
                trackerEntry.tracked = true;
                // Unobserve the element after tracking once
                this.observer?.unobserve(entry.target);
              }
            }
          }
        });
      },
      {
        threshold: this.options.threshold,
        rootMargin: this.options.rootMargin
      }
    );
  }

  observe(postId: string, onView: () => void) {
    if (!this.observer) return;

    const element = document.getElementById(`post-${postId}`);
    if (!element) return;

    // Store the entry
    this.entries.set(postId, { postId, onView, tracked: false });

    // Start observing
    this.observer.observe(element);
  }

  unobserve(postId: string) {
    if (!this.observer) return;

    const element = document.getElementById(`post-${postId}`);
    if (element) {
      this.observer.unobserve(element);
    }

    // Remove the entry
    this.entries.delete(postId);
  }

  updateOptions(newOptions: Partial<ViewTrackerOptions>) {
    this.options = { ...this.options, ...newOptions };

    // Recreate observer with new options
    if (this.observer) {
      this.observer.disconnect();
      this.initializeObserver();

      // Re-observe all current entries
      this.entries.forEach((entry, postId) => {
        if (!entry.tracked || !this.options.trackOnce) {
          const element = document.getElementById(`post-${postId}`);
          if (element && this.observer) {
            this.observer.observe(element);
          }
        }
      });
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
      this.entries.clear();
    }
  }
}

export const useViewTracker = (
  postId: string,
  onView: () => void,
  options?: ViewTrackerOptions
) => {
  const managerRef = useRef<ViewTrackerManager | undefined>(undefined);
  const onViewRef = useRef(onView);
  const trackedRef = useRef(false);

  // Update the callback ref when it changes
  onViewRef.current = onView;

  // Memoized view handler to prevent unnecessary re-observations
  const handleView = useCallback(() => {
    if (trackedRef.current) return;
    trackedRef.current = true;
    onViewRef.current();
  }, []);

  useEffect(() => {
    if (!postId) return;

    // Get or create manager instance
    managerRef.current = ViewTrackerManager.getInstance();

    // Update options if provided
    if (options) {
      managerRef.current.updateOptions(options);
    }

    // Small delay to ensure DOM element exists
    const timeoutId = setTimeout(() => {
      managerRef.current?.observe(postId, handleView);
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      managerRef.current?.unobserve(postId);
    };
  }, [postId, handleView, options]);

  // Reset tracked state when postId changes
  useEffect(() => {
    trackedRef.current = false;
  }, [postId]);

  return {
    isTracked: trackedRef.current,
    resetTracking: () => {
      trackedRef.current = false;
    }
  };
};

// Hook for batch view tracking (useful for virtualized lists)
export const useBatchViewTracker = (
  posts: Array<{ id: string; onView: () => void }>,
  options?: ViewTrackerOptions
) => {
  const managerRef = useRef<ViewTrackerManager | undefined>(undefined);

  useEffect(() => {
    if (!posts.length) return;

    managerRef.current = ViewTrackerManager.getInstance();

    if (options) {
      managerRef.current.updateOptions(options);
    }

    // Observe all posts
    const timeoutId = setTimeout(() => {
      posts.forEach(({ id, onView }) => {
        managerRef.current?.observe(id, onView);
      });
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      posts.forEach(({ id }) => {
        managerRef.current?.unobserve(id);
      });
    };
  }, [posts, options]);
};

export default useViewTracker;