'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSocket } from '@/context/SocketContext';
import { useUser } from '@/hooks/useUser';
import { toast } from 'react-toastify';
import { api } from '@/convex/_generated/api';
import { useMutation, useQuery } from 'convex/react';

interface Notification {
  _id: string;
  title?: string;
  message: string;
  type: string;
  created_at: string;
  status: string;
  link?: string;
  icon?: string;
  metadata?: any;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  clearNotifications: () => Promise<void>;
  addNotification: (notification: Omit<Notification, '_id' | 'created_at' | 'status'>) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const { token: user } = useUser();

  // Convex mutations
  const addNotificationMutation = useMutation(api.notifications.addNotification);
  const markNotificationAsReadMutation = useMutation(api.notifications.markNotificationAsRead);
  const markMessagesAsReadMutation = useMutation(api.notifications.markMessagesAsRead);
  const clearNotificationsMutation = useMutation(api.notifications.clearNotifications);

  // Convex query for notifications
  const notificationsData = useQuery(
    api.notifications.getNotifications,
    user ? { user_id: user } : "skip"
  );

  // Update local state when Convex data changes
  useEffect(() => {
    if (notificationsData) {
      setNotifications(notificationsData.notifications || []);
      setUnreadCount(notificationsData.unread?.length || 0);
    }
  }, [notificationsData]);

  // Listen for new notifications
  useEffect(() => {
    const handleNewNotification = (event: CustomEvent) => {
      const newNotification = event.detail;

      // Add notification to state
      setNotifications(prev => [newNotification, ...prev]);
      setUnreadCount(prev => prev + 1);

      // Show toast notification
      toast.info(
        <div>
          <strong>{newNotification.title}</strong>
          <p>{newNotification.message}</p>
        </div>,
        {
          onClick: () => {
            if (newNotification.url) {
              window.location.href = newNotification.url;
            }
          },
          autoClose: 5000,
        }
      );
    };

    // Add event listener
    window.addEventListener('new-notification' as any, handleNewNotification as EventListener);

    // Clean up
    return () => {
      window.removeEventListener('new-notification' as any, handleNewNotification as EventListener);
    };
  }, []);

  // Mark a notification as read
  const markAsRead = async (notificationId: string) => {
    if (!user) return;

    try {
      await markNotificationAsReadMutation({ notification_id: notificationId as any });

      // Update local state
      setNotifications(prev =>
        prev.map(n => n._id === notificationId ? { ...n, status: "read" } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user) return;

    try {
      await markMessagesAsReadMutation({ user_id: user });

      // Update local state
      setNotifications(prev => prev.map(n => ({ ...n, status: "read" })));
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Clear all notifications
  const clearNotifications = async () => {
    if (!user) return;

    try {
      await clearNotificationsMutation({ user_id: user });

      // Update local state
      setNotifications([]);
      setUnreadCount(0);
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  };

  // Add notification
  const addNotification = async (notification: Omit<Notification, '_id' | 'created_at' | 'status'>) => {
    if (!user) return;

    try {
      await addNotificationMutation({
        user_id: user,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        link: notification.link,
        icon: notification.icon,
        metadata: notification.metadata,
      });
    } catch (error) {
      console.error('Error adding notification:', error);
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        clearNotifications,
        addNotification,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};