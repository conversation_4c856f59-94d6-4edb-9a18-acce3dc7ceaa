var Y = (e, t, s) => {
  if (!t.has(e))
    throw TypeError("Cannot " + s);
};
var a = (e, t, s) => (Y(e, t, "read from private field"), s ? s.call(e) : t.get(e)), g = (e, t, s) => {
  if (t.has(e))
    throw TypeError("Cannot add the same private member more than once");
  t instanceof WeakSet ? t.add(e) : t.set(e, s);
}, h = (e, t, s, i) => (Y(e, t, "write to private field"), i ? i.call(e, s) : t.set(e, s), s);
var V = (e, t, s, i) => ({
  set _(r) {
    h(e, t, r, s);
  },
  get _() {
    return a(e, t, i);
  }
}), H = (e, t, s) => (Y(e, t, "access private method"), s);
import { jsx as Z } from "react/jsx-runtime";
import * as W from "react";
import yt from "react";
import { S as dt, h as ft, Q as gt, n as v, m as $, R as pt, c as mt, a as tt, b as E, e as vt, d as Pt, f as Qt, g as bt, o as et, r as st, i as Ot, j as it, p as at, s as wt } from "../query-013b86c3.js";
import { u as qt, Q as Mt } from "../QueryClientProvider-6bcd4331.js";
var C, ot, Dt = (ot = class extends dt {
  constructor(t = {}) {
    super();
    g(this, C, void 0);
    this.config = t, h(this, C, /* @__PURE__ */ new Map());
  }
  build(t, s, i) {
    const r = s.queryKey, n = s.queryHash ?? ft(r, s);
    let l = this.get(n);
    return l || (l = new gt({
      cache: this,
      queryKey: r,
      queryHash: n,
      options: t.defaultQueryOptions(s),
      state: i,
      defaultOptions: t.getQueryDefaults(r)
    }), this.add(l)), l;
  }
  add(t) {
    a(this, C).has(t.queryHash) || (a(this, C).set(t.queryHash, t), this.notify({
      type: "added",
      query: t
    }));
  }
  remove(t) {
    const s = a(this, C).get(t.queryHash);
    s && (t.destroy(), s === t && a(this, C).delete(t.queryHash), this.notify({ type: "removed", query: t }));
  }
  clear() {
    v.batch(() => {
      this.getAll().forEach((t) => {
        this.remove(t);
      });
    });
  }
  get(t) {
    return a(this, C).get(t);
  }
  getAll() {
    return [...a(this, C).values()];
  }
  find(t) {
    const s = { exact: !0, ...t };
    return this.getAll().find(
      (i) => $(s, i)
    );
  }
  findAll(t = {}) {
    const s = this.getAll();
    return Object.keys(t).length > 0 ? s.filter((i) => $(t, i)) : s;
  }
  notify(t) {
    v.batch(() => {
      this.listeners.forEach((s) => {
        s(t);
      });
    });
  }
  onFocus() {
    v.batch(() => {
      this.getAll().forEach((t) => {
        t.onFocus();
      });
    });
  }
  onOnline() {
    v.batch(() => {
      this.getAll().forEach((t) => {
        t.onOnline();
      });
    });
  }
}, C = new WeakMap(), ot), A, p, _, F, T, ht, Ct = (ht = class extends pt {
  constructor(t) {
    super();
    g(this, F);
    g(this, A, void 0);
    g(this, p, void 0);
    g(this, _, void 0);
    this.mutationId = t.mutationId, h(this, p, t.mutationCache), h(this, A, []), this.state = t.state || At(), this.setOptions(t.options), this.scheduleGc();
  }
  setOptions(t) {
    this.options = t, this.updateGcTime(this.options.gcTime);
  }
  get meta() {
    return this.options.meta;
  }
  addObserver(t) {
    a(this, A).includes(t) || (a(this, A).push(t), this.clearGcTimeout(), a(this, p).notify({
      type: "observerAdded",
      mutation: this,
      observer: t
    }));
  }
  removeObserver(t) {
    h(this, A, a(this, A).filter((s) => s !== t)), this.scheduleGc(), a(this, p).notify({
      type: "observerRemoved",
      mutation: this,
      observer: t
    });
  }
  optionalRemove() {
    a(this, A).length || (this.state.status === "pending" ? this.scheduleGc() : a(this, p).remove(this));
  }
  continue() {
    var t;
    return ((t = a(this, _)) == null ? void 0 : t.continue()) ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before
    this.execute(this.state.variables);
  }
  async execute(t) {
    var r, n, l, m, w, M, P, c, o, Q, D, d, q, k, S, y, b, R, j, J;
    h(this, _, mt({
      fn: () => this.options.mutationFn ? this.options.mutationFn(t) : Promise.reject(new Error("No mutationFn found")),
      onFail: (f, U) => {
        H(this, F, T).call(this, { type: "failed", failureCount: f, error: U });
      },
      onPause: () => {
        H(this, F, T).call(this, { type: "pause" });
      },
      onContinue: () => {
        H(this, F, T).call(this, { type: "continue" });
      },
      retry: this.options.retry ?? 0,
      retryDelay: this.options.retryDelay,
      networkMode: this.options.networkMode,
      canRun: () => a(this, p).canRun(this)
    }));
    const s = this.state.status === "pending", i = !a(this, _).canStart();
    try {
      if (!s) {
        H(this, F, T).call(this, { type: "pending", variables: t, isPaused: i }), await ((n = (r = a(this, p).config).onMutate) == null ? void 0 : n.call(
          r,
          t,
          this
        ));
        const U = await ((m = (l = this.options).onMutate) == null ? void 0 : m.call(l, t));
        U !== this.state.context && H(this, F, T).call(this, {
          type: "pending",
          context: U,
          variables: t,
          isPaused: i
        });
      }
      const f = await a(this, _).start();
      return await ((M = (w = a(this, p).config).onSuccess) == null ? void 0 : M.call(
        w,
        f,
        t,
        this.state.context,
        this
      )), await ((c = (P = this.options).onSuccess) == null ? void 0 : c.call(P, f, t, this.state.context)), await ((Q = (o = a(this, p).config).onSettled) == null ? void 0 : Q.call(
        o,
        f,
        null,
        this.state.variables,
        this.state.context,
        this
      )), await ((d = (D = this.options).onSettled) == null ? void 0 : d.call(D, f, null, t, this.state.context)), H(this, F, T).call(this, { type: "success", data: f }), f;
    } catch (f) {
      try {
        throw await ((k = (q = a(this, p).config).onError) == null ? void 0 : k.call(
          q,
          f,
          t,
          this.state.context,
          this
        )), await ((y = (S = this.options).onError) == null ? void 0 : y.call(
          S,
          f,
          t,
          this.state.context
        )), await ((R = (b = a(this, p).config).onSettled) == null ? void 0 : R.call(
          b,
          void 0,
          f,
          this.state.variables,
          this.state.context,
          this
        )), await ((J = (j = this.options).onSettled) == null ? void 0 : J.call(
          j,
          void 0,
          f,
          t,
          this.state.context
        )), f;
      } finally {
        H(this, F, T).call(this, { type: "error", error: f });
      }
    } finally {
      a(this, p).runNext(this);
    }
  }
}, A = new WeakMap(), p = new WeakMap(), _ = new WeakMap(), F = new WeakSet(), T = function(t) {
  const s = (i) => {
    switch (t.type) {
      case "failed":
        return {
          ...i,
          failureCount: t.failureCount,
          failureReason: t.error
        };
      case "pause":
        return {
          ...i,
          isPaused: !0
        };
      case "continue":
        return {
          ...i,
          isPaused: !1
        };
      case "pending":
        return {
          ...i,
          context: t.context,
          data: void 0,
          failureCount: 0,
          failureReason: null,
          error: null,
          isPaused: t.isPaused,
          status: "pending",
          variables: t.variables,
          submittedAt: Date.now()
        };
      case "success":
        return {
          ...i,
          data: t.data,
          failureCount: 0,
          failureReason: null,
          error: null,
          status: "success",
          isPaused: !1
        };
      case "error":
        return {
          ...i,
          data: void 0,
          error: t.error,
          failureCount: i.failureCount + 1,
          failureReason: t.error,
          isPaused: !1,
          status: "error"
        };
    }
  };
  this.state = s(this.state), v.batch(() => {
    a(this, A).forEach((i) => {
      i.onMutationUpdate(t);
    }), a(this, p).notify({
      mutation: this,
      type: "updated",
      action: t
    });
  });
}, ht);
function At() {
  return {
    context: void 0,
    data: void 0,
    error: null,
    failureCount: 0,
    failureReason: null,
    isPaused: !1,
    status: "idle",
    variables: void 0,
    submittedAt: 0
  };
}
var O, L, ct, Ft = (ct = class extends dt {
  constructor(t = {}) {
    super();
    g(this, O, void 0);
    g(this, L, void 0);
    this.config = t, h(this, O, /* @__PURE__ */ new Map()), h(this, L, Date.now());
  }
  build(t, s, i) {
    const r = new Ct({
      mutationCache: this,
      mutationId: ++V(this, L)._,
      options: t.defaultMutationOptions(s),
      state: i
    });
    return this.add(r), r;
  }
  add(t) {
    const s = X(t), i = a(this, O).get(s) ?? [];
    i.push(t), a(this, O).set(s, i), this.notify({ type: "added", mutation: t });
  }
  remove(t) {
    var i;
    const s = X(t);
    if (a(this, O).has(s)) {
      const r = (i = a(this, O).get(s)) == null ? void 0 : i.filter((n) => n !== t);
      r && (r.length === 0 ? a(this, O).delete(s) : a(this, O).set(s, r));
    }
    this.notify({ type: "removed", mutation: t });
  }
  canRun(t) {
    var i;
    const s = (i = a(this, O).get(X(t))) == null ? void 0 : i.find((r) => r.state.status === "pending");
    return !s || s === t;
  }
  runNext(t) {
    var i;
    const s = (i = a(this, O).get(X(t))) == null ? void 0 : i.find((r) => r !== t && r.state.isPaused);
    return (s == null ? void 0 : s.continue()) ?? Promise.resolve();
  }
  clear() {
    v.batch(() => {
      this.getAll().forEach((t) => {
        this.remove(t);
      });
    });
  }
  getAll() {
    return [...a(this, O).values()].flat();
  }
  find(t) {
    const s = { exact: !0, ...t };
    return this.getAll().find(
      (i) => tt(s, i)
    );
  }
  findAll(t = {}) {
    return this.getAll().filter((s) => tt(t, s));
  }
  notify(t) {
    v.batch(() => {
      this.listeners.forEach((s) => {
        s(t);
      });
    });
  }
  resumePausedMutations() {
    const t = this.getAll().filter((s) => s.state.isPaused);
    return v.batch(
      () => Promise.all(
        t.map((s) => s.continue().catch(E))
      )
    );
  }
}, O = new WeakMap(), L = new WeakMap(), ct);
function X(e) {
  var t;
  return ((t = e.options.scope) == null ? void 0 : t.id) ?? String(e.mutationId);
}
function rt(e) {
  return {
    onFetch: (t, s) => {
      var P, c, o, Q, D;
      const i = t.options, r = (o = (c = (P = t.fetchOptions) == null ? void 0 : P.meta) == null ? void 0 : c.fetchMore) == null ? void 0 : o.direction, n = ((Q = t.state.data) == null ? void 0 : Q.pages) || [], l = ((D = t.state.data) == null ? void 0 : D.pageParams) || [];
      let m = { pages: [], pageParams: [] }, w = 0;
      const M = async () => {
        let d = !1;
        const q = (y) => {
          Object.defineProperty(y, "signal", {
            enumerable: !0,
            get: () => (t.signal.aborted ? d = !0 : t.signal.addEventListener("abort", () => {
              d = !0;
            }), t.signal)
          });
        }, k = vt(t.options, t.fetchOptions), S = async (y, b, R) => {
          if (d)
            return Promise.reject();
          if (b == null && y.pages.length)
            return Promise.resolve(y);
          const j = {
            queryKey: t.queryKey,
            pageParam: b,
            direction: R ? "backward" : "forward",
            meta: t.options.meta
          };
          q(j);
          const J = await k(
            j
          ), { maxPages: f } = t.options, U = R ? Pt : Qt;
          return {
            pages: U(y.pages, J, f),
            pageParams: U(y.pageParams, b, f)
          };
        };
        if (r && n.length) {
          const y = r === "backward", b = y ? Et : nt, R = {
            pages: n,
            pageParams: l
          }, j = b(i, R);
          m = await S(R, j, y);
        } else {
          const y = e ?? n.length;
          do {
            const b = w === 0 ? l[0] ?? i.initialPageParam : nt(i, m);
            if (w > 0 && b == null)
              break;
            m = await S(m, b), w++;
          } while (w < y);
        }
        return m;
      };
      t.options.persister ? t.fetchFn = () => {
        var d, q;
        return (q = (d = t.options).persister) == null ? void 0 : q.call(
          d,
          M,
          {
            queryKey: t.queryKey,
            meta: t.options.meta,
            signal: t.signal
          },
          s
        );
      } : t.fetchFn = M;
    }
  };
}
function nt(e, { pages: t, pageParams: s }) {
  const i = t.length - 1;
  return t.length > 0 ? e.getNextPageParam(
    t[i],
    t,
    s[i],
    s
  ) : void 0;
}
function Et(e, { pages: t, pageParams: s }) {
  var i;
  return t.length > 0 ? (i = e.getPreviousPageParam) == null ? void 0 : i.call(e, t[0], t, s[0], s) : void 0;
}
var u, x, I, N, B, K, G, z, lt, St = (lt = class {
  constructor(e = {}) {
    g(this, u, void 0);
    g(this, x, void 0);
    g(this, I, void 0);
    g(this, N, void 0);
    g(this, B, void 0);
    g(this, K, void 0);
    g(this, G, void 0);
    g(this, z, void 0);
    h(this, u, e.queryCache || new Dt()), h(this, x, e.mutationCache || new Ft()), h(this, I, e.defaultOptions || {}), h(this, N, /* @__PURE__ */ new Map()), h(this, B, /* @__PURE__ */ new Map()), h(this, K, 0);
  }
  mount() {
    V(this, K)._++, a(this, K) === 1 && (h(this, G, bt.subscribe(async (e) => {
      e && (await this.resumePausedMutations(), a(this, u).onFocus());
    })), h(this, z, et.subscribe(async (e) => {
      e && (await this.resumePausedMutations(), a(this, u).onOnline());
    })));
  }
  unmount() {
    var e, t;
    V(this, K)._--, a(this, K) === 0 && ((e = a(this, G)) == null || e.call(this), h(this, G, void 0), (t = a(this, z)) == null || t.call(this), h(this, z, void 0));
  }
  isFetching(e) {
    return a(this, u).findAll({ ...e, fetchStatus: "fetching" }).length;
  }
  isMutating(e) {
    return a(this, x).findAll({ ...e, status: "pending" }).length;
  }
  getQueryData(e) {
    var s;
    const t = this.defaultQueryOptions({ queryKey: e });
    return (s = a(this, u).get(t.queryHash)) == null ? void 0 : s.state.data;
  }
  ensureQueryData(e) {
    const t = this.getQueryData(e.queryKey);
    if (t === void 0)
      return this.fetchQuery(e);
    {
      const s = this.defaultQueryOptions(e), i = a(this, u).build(this, s);
      return e.revalidateIfStale && i.isStaleByTime(st(s.staleTime, i)) && this.prefetchQuery(s), Promise.resolve(t);
    }
  }
  getQueriesData(e) {
    return a(this, u).findAll(e).map(({ queryKey: t, state: s }) => {
      const i = s.data;
      return [t, i];
    });
  }
  setQueryData(e, t, s) {
    const i = this.defaultQueryOptions({ queryKey: e }), r = a(this, u).get(
      i.queryHash
    ), n = r == null ? void 0 : r.state.data, l = Ot(t, n);
    if (l !== void 0)
      return a(this, u).build(this, i).setData(l, { ...s, manual: !0 });
  }
  setQueriesData(e, t, s) {
    return v.batch(
      () => a(this, u).findAll(e).map(({ queryKey: i }) => [
        i,
        this.setQueryData(i, t, s)
      ])
    );
  }
  getQueryState(e) {
    var s;
    const t = this.defaultQueryOptions({ queryKey: e });
    return (s = a(this, u).get(t.queryHash)) == null ? void 0 : s.state;
  }
  removeQueries(e) {
    const t = a(this, u);
    v.batch(() => {
      t.findAll(e).forEach((s) => {
        t.remove(s);
      });
    });
  }
  resetQueries(e, t) {
    const s = a(this, u), i = {
      type: "active",
      ...e
    };
    return v.batch(() => (s.findAll(e).forEach((r) => {
      r.reset();
    }), this.refetchQueries(i, t)));
  }
  cancelQueries(e = {}, t = {}) {
    const s = { revert: !0, ...t }, i = v.batch(
      () => a(this, u).findAll(e).map((r) => r.cancel(s))
    );
    return Promise.all(i).then(E).catch(E);
  }
  invalidateQueries(e = {}, t = {}) {
    return v.batch(() => {
      if (a(this, u).findAll(e).forEach((i) => {
        i.invalidate();
      }), e.refetchType === "none")
        return Promise.resolve();
      const s = {
        ...e,
        type: e.refetchType ?? e.type ?? "active"
      };
      return this.refetchQueries(s, t);
    });
  }
  refetchQueries(e = {}, t) {
    const s = {
      ...t,
      cancelRefetch: (t == null ? void 0 : t.cancelRefetch) ?? !0
    }, i = v.batch(
      () => a(this, u).findAll(e).filter((r) => !r.isDisabled()).map((r) => {
        let n = r.fetch(void 0, s);
        return s.throwOnError || (n = n.catch(E)), r.state.fetchStatus === "paused" ? Promise.resolve() : n;
      })
    );
    return Promise.all(i).then(E);
  }
  fetchQuery(e) {
    const t = this.defaultQueryOptions(e);
    t.retry === void 0 && (t.retry = !1);
    const s = a(this, u).build(this, t);
    return s.isStaleByTime(
      st(t.staleTime, s)
    ) ? s.fetch(t) : Promise.resolve(s.state.data);
  }
  prefetchQuery(e) {
    return this.fetchQuery(e).then(E).catch(E);
  }
  fetchInfiniteQuery(e) {
    return e.behavior = rt(e.pages), this.fetchQuery(e);
  }
  prefetchInfiniteQuery(e) {
    return this.fetchInfiniteQuery(e).then(E).catch(E);
  }
  ensureInfiniteQueryData(e) {
    return e.behavior = rt(e.pages), this.ensureQueryData(e);
  }
  resumePausedMutations() {
    return et.isOnline() ? a(this, x).resumePausedMutations() : Promise.resolve();
  }
  getQueryCache() {
    return a(this, u);
  }
  getMutationCache() {
    return a(this, x);
  }
  getDefaultOptions() {
    return a(this, I);
  }
  setDefaultOptions(e) {
    h(this, I, e);
  }
  setQueryDefaults(e, t) {
    a(this, N).set(it(e), {
      queryKey: e,
      defaultOptions: t
    });
  }
  getQueryDefaults(e) {
    const t = [...a(this, N).values()];
    let s = {};
    return t.forEach((i) => {
      at(e, i.queryKey) && (s = { ...s, ...i.defaultOptions });
    }), s;
  }
  setMutationDefaults(e, t) {
    a(this, B).set(it(e), {
      mutationKey: e,
      defaultOptions: t
    });
  }
  getMutationDefaults(e) {
    const t = [...a(this, B).values()];
    let s = {};
    return t.forEach((i) => {
      at(e, i.mutationKey) && (s = { ...s, ...i.defaultOptions });
    }), s;
  }
  defaultQueryOptions(e) {
    if (e._defaulted)
      return e;
    const t = {
      ...a(this, I).queries,
      ...this.getQueryDefaults(e.queryKey),
      ...e,
      _defaulted: !0
    };
    return t.queryHash || (t.queryHash = ft(
      t.queryKey,
      t
    )), t.refetchOnReconnect === void 0 && (t.refetchOnReconnect = t.networkMode !== "always"), t.throwOnError === void 0 && (t.throwOnError = !!t.suspense), !t.networkMode && t.persister && (t.networkMode = "offlineFirst"), t.enabled !== !0 && t.queryFn === wt && (t.enabled = !1), t;
  }
  defaultMutationOptions(e) {
    return e != null && e._defaulted ? e : {
      ...a(this, I).mutations,
      ...(e == null ? void 0 : e.mutationKey) && this.getMutationDefaults(e.mutationKey),
      ...e,
      _defaulted: !0
    };
  }
  clear() {
    a(this, u).clear(), a(this, x).clear();
  }
}, u = new WeakMap(), x = new WeakMap(), I = new WeakMap(), N = new WeakMap(), B = new WeakMap(), K = new WeakMap(), G = new WeakMap(), z = new WeakMap(), lt);
function Rt(e) {
  return e;
}
function ut(e, t, s) {
  var w, M;
  if (typeof t != "object" || t === null)
    return;
  const i = e.getMutationCache(), r = e.getQueryCache(), n = ((w = s == null ? void 0 : s.defaultOptions) == null ? void 0 : w.deserializeData) ?? ((M = e.getDefaultOptions().hydrate) == null ? void 0 : M.deserializeData) ?? Rt, l = t.mutations || [], m = t.queries || [];
  l.forEach(({ state: P, ...c }) => {
    var o, Q;
    i.build(
      e,
      {
        ...(o = e.getDefaultOptions().hydrate) == null ? void 0 : o.mutations,
        ...(Q = s == null ? void 0 : s.defaultOptions) == null ? void 0 : Q.mutations,
        ...c
      },
      P
    );
  }), m.forEach(({ queryKey: P, state: c, queryHash: o, meta: Q, promise: D }) => {
    var k, S;
    let d = r.get(o);
    const q = c.data === void 0 ? c.data : n(c.data);
    if (d) {
      if (d.state.dataUpdatedAt < c.dataUpdatedAt) {
        const { fetchStatus: y, ...b } = c;
        d.setState({
          ...b,
          data: q
        });
      }
    } else
      d = r.build(
        e,
        {
          ...(k = e.getDefaultOptions().hydrate) == null ? void 0 : k.queries,
          ...(S = s == null ? void 0 : s.defaultOptions) == null ? void 0 : S.queries,
          queryKey: P,
          queryHash: o,
          meta: Q
        },
        // Reset fetch status to idle to avoid
        // query being stuck in fetching state upon hydration
        {
          ...c,
          data: q,
          fetchStatus: "idle"
        }
      );
    if (D) {
      const y = Promise.resolve(D).then(n);
      d.fetch(void 0, { initialPromise: y });
    }
  });
}
var Ht = ({
  children: e,
  options: t = {},
  state: s,
  queryClient: i
}) => {
  const r = qt(i), [n, l] = W.useState(), m = W.useRef(t);
  return m.current = t, W.useMemo(() => {
    if (s) {
      if (typeof s != "object")
        return;
      const w = r.getQueryCache(), M = s.queries || [], P = [], c = [];
      for (const o of M) {
        const Q = w.get(o.queryHash);
        if (!Q)
          P.push(o);
        else {
          const D = o.state.dataUpdatedAt > Q.state.dataUpdatedAt, d = n == null ? void 0 : n.find(
            (q) => q.queryHash === o.queryHash
          );
          D && (!d || o.state.dataUpdatedAt > d.state.dataUpdatedAt) && c.push(o);
        }
      }
      P.length > 0 && ut(r, { queries: P }, m.current), c.length > 0 && l(
        (o) => o ? [...o, ...c] : c
      );
    }
  }, [r, n, s]), W.useEffect(() => {
    n && (ut(r, { queries: n }, m.current), l(void 0));
  }, [r, n]), e;
};
function jt({ children: e }) {
  const [t] = yt.useState(new St());
  return /* @__PURE__ */ Z(Mt, { client: t, children: /* @__PURE__ */ Z(Ht, { children: e }) });
}
export {
  jt as default
};
