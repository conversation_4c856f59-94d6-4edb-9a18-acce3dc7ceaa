"use client";

import { ConvexProvider, ConvexReactClient } from "convex/react";
import { ConvexAuthProvider } from "@convex-dev/auth/react";
import { ReactNode } from "react";

interface ConvexClientProviderProps {
  children: ReactNode;
}

export function ConvexClientProvider({ children }: ConvexClientProviderProps) {
  const convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;
  if (!convexUrl) {
    if (typeof window !== "undefined") {
      // Only log on client to avoid noisy SSR logs
      // eslint-disable-next-line no-console
      console.error("Missing NEXT_PUBLIC_CONVEX_URL environment variable.");
    }
    return (
      <div className="p-4 text-red-600 bg-red-50 rounded">
        Error: Missing Convex URL configuration.
      </div>
    );
  }

  const client = new ConvexReactClient(convexUrl, {
    verbose: process.env.NODE_ENV === 'development'
  });

  return (
    <ConvexProvider client={client}>
      <ConvexAuthProvider client={client}>
        {children}
      </ConvexAuthProvider>
    </ConvexProvider>
  );
};