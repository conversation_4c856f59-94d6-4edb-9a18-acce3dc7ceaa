'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Play } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { HiOutlineGif } from "react-icons/hi2";

interface GifPlayerWithBlurProps {
    gifUrl: string;
    onClick?: () => void;
}

const GifPlayerWithBlur: React.FC<GifPlayerWithBlurProps> = ({ gifUrl, onClick }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [isPortrait, setIsPortrait] = useState<boolean | null>(null);
    const [thumbUrl, setThumbUrl] = useState<string | null>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);

    const handleClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        onClick?.();
    };

    // Load the GIF into an offscreen image, draw its first frame into canvas
    useEffect(() => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.src = gifUrl;
        img.onload = () => {
            const { naturalWidth: w, naturalHeight: h } = img;
            setIsPortrait(h > w);

            if (canvasRef.current) {
                const canvas = canvasRef.current;
                canvas.width = w;
                canvas.height = h;
                const ctx = canvas.getContext('2d')!;
                ctx.drawImage(img, 0, 0, w, h);

                // capture thumbnail for blurred bg
                const dataUrl = canvas.toDataURL('image/png');
                setThumbUrl(dataUrl);
            }

            setIsLoading(false);
        };
        img.onerror = () => {
            setIsLoading(false);
        };
    }, [gifUrl]);

    return (
        <div
            className={`
            relative rounded-md overflow-hidden bg-black
            ${isPortrait === null
                    ? 'h-[415px]'
                    : isPortrait
                        ? 'h-[clamp(200px,88vh,100vh)] w-full'
                        : 'w-full aspect-video'
                }
            `}
            onClick={handleClick}
        >
            {/* Blurred background */}
            {isPortrait && (
                <div
                    className="absolute inset-0 bg-center bg-cover filter blur-2xl scale-105"
                    style={{ backgroundImage: `url(${thumbUrl})` }}
                />
            )}

            {/* GIF icon overlay in top-left */}
            <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 pt-[3px] pb-[2.2px] rounded-md z-[1] flex items-center gap-1">
                <HiOutlineGif className="h-auto w-5 dark:text-white" /> 1
            </div>

            {/* Centered first frame */}
            <div className="absolute inset-0 flex items-center justify-center">
                <Skeleton loading={isLoading} width="100%" height="100%" className="rounded-lg flex items-center justify-center">
                    <canvas
                        ref={canvasRef}
                        className={
                            isPortrait
                                ? 'h-full w-auto object-contain'
                                : 'w-full h-full object-cover'
                        }
                    />
                </Skeleton>

                {/* Play GIF button overlay */}
                <div
                    className="absolute inset-0 flex items-center justify-center cursor-pointer"
                    onClick={handleClick}
                >
                    <div className="relative">
                        <div className="absolute inset-0 bg-turquoise rounded-full opacity-30 animate-ping-slow" />
                        <button className="relative w-16 h-16 bg-turquoise rounded-full flex items-center justify-center hover:bg-turquoise-hover transition-colors">
                            <Play className="h-8 w-8 text-white fill-white ml-1" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};
export default GifPlayerWithBlur;