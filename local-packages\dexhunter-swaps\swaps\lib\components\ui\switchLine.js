import { jsx as e, jsxs as i } from "react/jsx-runtime";
import { memo as l } from "react";
import { cn as a } from "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const m = ({
  initValue: t = !1,
  onChange: c,
  color: r,
  keyId: o = "toogleA"
}) => {
  const n = (s) => {
    c(s);
  };
  return /* @__PURE__ */ e("label", { htmlFor: o, className: "flex items-center cursor-pointer h-4", children: /* @__PURE__ */ i("div", { className: "relative", children: [
    /* @__PURE__ */ e(
      "input",
      {
        id: o,
        type: "checkbox",
        className: "sr-only",
        checked: t,
        onChange: (s) => n(s.target.checked)
      },
      o
    ),
    /* @__PURE__ */ e(
      "div",
      {
        className: a(
          "w-6 h-[4px] bg-gray-400 rounded-[5px] shadow-inner",
          t && `${r} opacity-50`
        )
      }
    ),
    /* @__PURE__ */ e(
      "div",
      {
        className: a(
          "absolute w-4 h-4 bg-gray-101 rounded-[5px] shadow -left-1 -top-[6px] transition",
          t && `translate-x-full ${r}`
        )
      }
    )
  ] }) });
}, f = l(m);
export {
  f as default
};
