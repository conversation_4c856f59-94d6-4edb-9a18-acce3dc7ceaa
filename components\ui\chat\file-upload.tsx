"use client";

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { X, FileText, Image as ImageIcon, Film, File } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { cn } from '@/lib/utils';

// File size limit: 10MB
const FILE_SIZE_LIMIT = 10 * 1024 * 1024;
const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const ACCEPTED_VIDEO_TYPES = ['video/mp4', 'video/webm', 'video/ogg'];
const ACCEPTED_FILE_TYPES = [...ACCEPTED_IMAGE_TYPES, ...ACCEPTED_VIDEO_TYPES, 'application/pdf', 'text/plain'];

interface FileUploadProps {
  onFileSelect: (file: FileWithPreview | null) => void;
  selectedFile: FileWithPreview | null;
  className?: string;
}

export interface FileWithPreview {
  file: File;
  previewUrl: string;
  type: string;
  name: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({ 
  onFileSelect, 
  selectedFile,
  className 
}) => {
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);
    
    if (acceptedFiles.length === 0) return;
    
    const file = acceptedFiles[0];
    
    // Check file size
    if (file.size > FILE_SIZE_LIMIT) {
      setError('File size exceeds 10MB limit');
      return;
    }
    
    // Check file type
    if (!ACCEPTED_FILE_TYPES.some(type => file.type.startsWith(type.split('/')[0]))) {
      setError('File type not supported');
      return;
    }
    
    const previewUrl = URL.createObjectURL(file);
    onFileSelect({
      file,
      previewUrl,
      type: file.type,
      name: file.name,
    });
  }, [onFileSelect]);
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop,
    maxFiles: 1,
    accept: {
      'image/*': ACCEPTED_IMAGE_TYPES,
      'video/*': ACCEPTED_VIDEO_TYPES,
      'application/pdf': ['.pdf'],
      'text/plain': ['.txt'],
    }
  });
  
  const handleRemoveFile = () => {
    if (selectedFile) {
      URL.revokeObjectURL(selectedFile.previewUrl);
    }
    onFileSelect(null);
  };
  
  const renderPreview = () => {
    if (!selectedFile) return null;
    
    if (selectedFile.type.startsWith('image/')) {
      return (
        <div className="relative w-full h-40 rounded-md overflow-hidden">
          <Image 
            src={selectedFile.previewUrl} 
            alt={selectedFile.name}
            fill
            style={{ objectFit: 'contain' }}
          />
        </div>
      );
    }
    
    if (selectedFile.type.startsWith('video/')) {
      return (
        <div className="relative w-full rounded-md overflow-hidden">
          <video 
            src={selectedFile.previewUrl} 
            controls 
            className="max-h-40 w-full"
          />
        </div>
      );
    }
    
    // For other file types
    return (
      <div className="flex items-center p-3 bg-gray-100 dark:bg-gray-800 rounded-md">
        {selectedFile.type === 'application/pdf' ? (
          <FileText className="h-8 w-8 text-red-500 mr-2" />
        ) : (
          <File className="h-8 w-8 text-blue-500 mr-2" />
        )}
        <span className="text-sm truncate flex-1">{selectedFile.name}</span>
      </div>
    );
  };
  
  return (
    <div className={cn("w-full", className)}>
      {selectedFile ? (
        <div className="space-y-2">
          {renderPreview()}
          <div className="flex justify-end">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleRemoveFile}
              className="text-red-500 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900/20"
            >
              <X className="h-4 w-4 mr-1" />
              Remove
            </Button>
          </div>
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={cn(
            "border-2 border-dashed rounded-md p-4 text-center cursor-pointer transition-colors",
            isDragActive 
              ? "border-primary bg-primary/10" 
              : "border-gray-300 hover:border-primary dark:border-gray-700"
          )}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center justify-center gap-2 text-sm">
            <div className="flex gap-2">
              <ImageIcon className="h-5 w-5 text-gray-400" />
              <Film className="h-5 w-5 text-gray-400" />
              <FileText className="h-5 w-5 text-gray-400" />
            </div>
            {isDragActive ? (
              <p>Drop the file here...</p>
            ) : (
              <p>Drag & drop a file, or click to select</p>
            )}
            <p className="text-xs text-gray-500">
              Supports images, videos, PDFs (Max: 10MB)
            </p>
          </div>
        </div>
      )}
      {error && (
        <p className="text-sm text-red-500 mt-1">{error}</p>
      )}
    </div>
  );
};