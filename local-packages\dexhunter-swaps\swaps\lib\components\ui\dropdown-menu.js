import { jsxs as ve, jsx as y } from "react/jsx-runtime";
import * as G from "react";
import { forwardRef as g, createElement as s, useRef as M, useState as k, useEffect as N, useCallback as P, Fragment as $o } from "react";
import { _ as $, a as X, c as Re, $ as bo } from "../../index-1c873780.js";
import { a as xe, c as se, $ as m } from "../../index-c7156e07.js";
import { $ as _e } from "../../index-563d1ed8.js";
import { $ as K, a as ho } from "../../index-c8f2666b.js";
import { $ as Te } from "../../index-bf605d8a.js";
import { $ as Oe } from "../../index-1fe761a6.js";
import { $ as mo, a as go } from "../../index-67500cd3.js";
import { h as vo, a as xo, b as _o, $ as wo } from "../../index-27cadef5.js";
import { $ as Q } from "../../index-4914f99c.js";
import { $ as Fe, a as Mo, b as Ne, c as Co } from "../../index-0ce202b9.js";
import { $ as ae } from "../../index-5116e957.js";
import { cn as T } from "../../lib.js";
import Io from "../../assets/svg/IconCheck.js";
import { c as Do } from "../../createLucideIcon-7a477fa6.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-bcfeaad9.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const yo = Do("ChevronRight", [
  ["path", { d: "m9 18 6-6-6-6", key: "mthhwq" }]
]), be = "rovingFocusGroup.onEntryFocus", Po = {
  bubbles: !1,
  cancelable: !0
}, we = "RovingFocusGroup", [he, Ae, So] = Te(we), [Eo, ke] = _e(we, [
  So
]), [Ro, To] = Eo(we), Oo = /* @__PURE__ */ g((e, n) => /* @__PURE__ */ s(he.Provider, {
  scope: e.__scopeRovingFocusGroup
}, /* @__PURE__ */ s(he.Slot, {
  scope: e.__scopeRovingFocusGroup
}, /* @__PURE__ */ s(Fo, $({}, e, {
  ref: n
}))))), Fo = /* @__PURE__ */ g((e, n) => {
  const { __scopeRovingFocusGroup: o, orientation: t, loop: r = !1, dir: c, currentTabStopId: d, defaultCurrentTabStopId: i, onCurrentTabStopIdChange: u, onEntryFocus: p, ...f } = e, a = M(null), b = X(n, a), v = Oe(c), [h = null, x] = xe({
    prop: d,
    defaultProp: i,
    onChange: u
  }), [R, C] = k(!1), I = se(p), Y = Ae(o), z = M(!1), [ue, W] = k(0);
  return N(() => {
    const _ = a.current;
    if (_)
      return _.addEventListener(be, I), () => _.removeEventListener(be, I);
  }, [
    I
  ]), /* @__PURE__ */ s(Ro, {
    scope: o,
    orientation: t,
    dir: v,
    loop: r,
    currentTabStopId: h,
    onItemFocus: P(
      (_) => x(_),
      [
        x
      ]
    ),
    onItemShiftTab: P(
      () => C(!0),
      []
    ),
    onFocusableItemAdd: P(
      () => W(
        (_) => _ + 1
      ),
      []
    ),
    onFocusableItemRemove: P(
      () => W(
        (_) => _ - 1
      ),
      []
    )
  }, /* @__PURE__ */ s(K.div, $({
    tabIndex: R || ue === 0 ? -1 : 0,
    "data-orientation": t
  }, f, {
    ref: b,
    style: {
      outline: "none",
      ...e.style
    },
    onMouseDown: m(e.onMouseDown, () => {
      z.current = !0;
    }),
    onFocus: m(e.onFocus, (_) => {
      const fe = !z.current;
      if (_.target === _.currentTarget && fe && !R) {
        const U = new CustomEvent(be, Po);
        if (_.currentTarget.dispatchEvent(U), !U.defaultPrevented) {
          const A = Y().filter(
            (O) => O.focusable
          ), le = A.find(
            (O) => O.active
          ), H = A.find(
            (O) => O.id === h
          ), q = [
            le,
            H,
            ...A
          ].filter(Boolean).map(
            (O) => O.ref.current
          );
          Ge(q);
        }
      }
      z.current = !1;
    }),
    onBlur: m(
      e.onBlur,
      () => C(!1)
    )
  })));
}), No = "RovingFocusGroupItem", Ao = /* @__PURE__ */ g((e, n) => {
  const { __scopeRovingFocusGroup: o, focusable: t = !0, active: r = !1, tabStopId: c, ...d } = e, i = Q(), u = c || i, p = To(No, o), f = p.currentTabStopId === u, a = Ae(o), { onFocusableItemAdd: b, onFocusableItemRemove: v } = p;
  return N(() => {
    if (t)
      return b(), () => v();
  }, [
    t,
    b,
    v
  ]), /* @__PURE__ */ s(he.ItemSlot, {
    scope: o,
    id: u,
    focusable: t,
    active: r
  }, /* @__PURE__ */ s(K.span, $({
    tabIndex: f ? 0 : -1,
    "data-orientation": p.orientation
  }, d, {
    ref: n,
    onMouseDown: m(e.onMouseDown, (h) => {
      t ? p.onItemFocus(u) : h.preventDefault();
    }),
    onFocus: m(
      e.onFocus,
      () => p.onItemFocus(u)
    ),
    onKeyDown: m(e.onKeyDown, (h) => {
      if (h.key === "Tab" && h.shiftKey) {
        p.onItemShiftTab();
        return;
      }
      if (h.target !== h.currentTarget)
        return;
      const x = Ko(h, p.orientation, p.dir);
      if (x !== void 0) {
        h.preventDefault();
        let C = a().filter(
          (I) => I.focusable
        ).map(
          (I) => I.ref.current
        );
        if (x === "last")
          C.reverse();
        else if (x === "prev" || x === "next") {
          x === "prev" && C.reverse();
          const I = C.indexOf(h.currentTarget);
          C = p.loop ? Lo(C, I + 1) : C.slice(I + 1);
        }
        setTimeout(
          () => Ge(C)
        );
      }
    })
  })));
}), ko = {
  ArrowLeft: "prev",
  ArrowUp: "prev",
  ArrowRight: "next",
  ArrowDown: "next",
  PageUp: "first",
  Home: "first",
  PageDown: "last",
  End: "last"
};
function Go(e, n) {
  return n !== "rtl" ? e : e === "ArrowLeft" ? "ArrowRight" : e === "ArrowRight" ? "ArrowLeft" : e;
}
function Ko(e, n, o) {
  const t = Go(e.key, o);
  if (!(n === "vertical" && [
    "ArrowLeft",
    "ArrowRight"
  ].includes(t)) && !(n === "horizontal" && [
    "ArrowUp",
    "ArrowDown"
  ].includes(t)))
    return ko[t];
}
function Ge(e) {
  const n = document.activeElement;
  for (const o of e)
    if (o === n || (o.focus(), document.activeElement !== n))
      return;
}
function Lo(e, n) {
  return e.map(
    (o, t) => e[(n + t) % e.length]
  );
}
const Uo = Oo, Bo = Ao, me = [
  "Enter",
  " "
], Vo = [
  "ArrowDown",
  "PageUp",
  "Home"
], Ke = [
  "ArrowUp",
  "PageDown",
  "End"
], Yo = [
  ...Vo,
  ...Ke
], zo = {
  ltr: [
    ...me,
    "ArrowRight"
  ],
  rtl: [
    ...me,
    "ArrowLeft"
  ]
}, jo = {
  ltr: [
    "ArrowLeft"
  ],
  rtl: [
    "ArrowRight"
  ]
}, de = "Menu", [ee, Xo, Wo] = Te(de), [V, Le] = _e(de, [
  Wo,
  Fe,
  ke
]), ie = Fe(), Ue = ke(), [Be, L] = V(de), [Ho, ne] = V(de), qo = (e) => {
  const { __scopeMenu: n, open: o = !1, children: t, dir: r, onOpenChange: c, modal: d = !0 } = e, i = ie(n), [u, p] = k(null), f = M(!1), a = se(c), b = Oe(r);
  return N(() => {
    const v = () => {
      f.current = !0, document.addEventListener("pointerdown", h, {
        capture: !0,
        once: !0
      }), document.addEventListener("pointermove", h, {
        capture: !0,
        once: !0
      });
    }, h = () => f.current = !1;
    return document.addEventListener("keydown", v, {
      capture: !0
    }), () => {
      document.removeEventListener("keydown", v, {
        capture: !0
      }), document.removeEventListener("pointerdown", h, {
        capture: !0
      }), document.removeEventListener("pointermove", h, {
        capture: !0
      });
    };
  }, []), /* @__PURE__ */ s(Ne, i, /* @__PURE__ */ s(Be, {
    scope: n,
    open: o,
    onOpenChange: a,
    content: u,
    onContentChange: p
  }, /* @__PURE__ */ s(Ho, {
    scope: n,
    onClose: P(
      () => a(!1),
      [
        a
      ]
    ),
    isUsingKeyboardRef: f,
    dir: b,
    modal: d
  }, t)));
}, Ve = /* @__PURE__ */ g((e, n) => {
  const { __scopeMenu: o, ...t } = e, r = ie(o);
  return /* @__PURE__ */ s(Co, $({}, r, t, {
    ref: n
  }));
}), Ye = "MenuPortal", [Zo, ze] = V(Ye, {
  forceMount: void 0
}), Jo = (e) => {
  const { __scopeMenu: n, forceMount: o, children: t, container: r } = e, c = L(Ye, n);
  return /* @__PURE__ */ s(Zo, {
    scope: n,
    forceMount: o
  }, /* @__PURE__ */ s(ae, {
    present: o || c.open
  }, /* @__PURE__ */ s(mo, {
    asChild: !0,
    container: r
  }, t)));
}, E = "MenuContent", [Qo, Me] = V(E), en = /* @__PURE__ */ g((e, n) => {
  const o = ze(E, e.__scopeMenu), { forceMount: t = o.forceMount, ...r } = e, c = L(E, e.__scopeMenu), d = ne(E, e.__scopeMenu);
  return /* @__PURE__ */ s(ee.Provider, {
    scope: e.__scopeMenu
  }, /* @__PURE__ */ s(ae, {
    present: t || c.open
  }, /* @__PURE__ */ s(ee.Slot, {
    scope: e.__scopeMenu
  }, d.modal ? /* @__PURE__ */ s(on, $({}, r, {
    ref: n
  })) : /* @__PURE__ */ s(nn, $({}, r, {
    ref: n
  })))));
}), on = /* @__PURE__ */ g((e, n) => {
  const o = L(E, e.__scopeMenu), t = M(null), r = X(n, t);
  return N(() => {
    const c = t.current;
    if (c)
      return vo(c);
  }, []), /* @__PURE__ */ s(Ce, $({}, e, {
    ref: r,
    trapFocus: o.open,
    disableOutsidePointerEvents: o.open,
    disableOutsideScroll: !0,
    onFocusOutside: m(
      e.onFocusOutside,
      (c) => c.preventDefault(),
      {
        checkForDefaultPrevented: !1
      }
    ),
    onDismiss: () => o.onOpenChange(!1)
  }));
}), nn = /* @__PURE__ */ g((e, n) => {
  const o = L(E, e.__scopeMenu);
  return /* @__PURE__ */ s(Ce, $({}, e, {
    ref: n,
    trapFocus: !1,
    disableOutsidePointerEvents: !1,
    disableOutsideScroll: !1,
    onDismiss: () => o.onOpenChange(!1)
  }));
}), Ce = /* @__PURE__ */ g((e, n) => {
  const { __scopeMenu: o, loop: t = !1, trapFocus: r, onOpenAutoFocus: c, onCloseAutoFocus: d, disableOutsidePointerEvents: i, onEntryFocus: u, onEscapeKeyDown: p, onPointerDownOutside: f, onFocusOutside: a, onInteractOutside: b, onDismiss: v, disableOutsideScroll: h, ...x } = e, R = L(E, o), C = ne(E, o), I = ie(o), Y = Ue(o), z = Xo(o), [ue, W] = k(null), _ = M(null), fe = X(n, _, R.onContentChange), U = M(0), A = M(""), le = M(0), H = M(null), pe = M("right"), q = M(0), O = h ? wo : $o, lo = h ? {
    as: bo,
    allowPinchZoom: !0
  } : void 0, po = (l) => {
    var w, S;
    const B = A.current + l, Z = z().filter(
      (F) => !F.disabled
    ), $e = document.activeElement, ye = (w = Z.find(
      (F) => F.ref.current === $e
    )) === null || w === void 0 ? void 0 : w.textValue, te = Z.map(
      (F) => F.textValue
    ), J = wn(te, B, ye), Pe = (S = Z.find(
      (F) => F.textValue === J
    )) === null || S === void 0 ? void 0 : S.ref.current;
    (function F(Se) {
      A.current = Se, window.clearTimeout(U.current), Se !== "" && (U.current = window.setTimeout(
        () => F(""),
        1e3
      ));
    })(B), Pe && setTimeout(
      () => Pe.focus()
    );
  };
  N(() => () => window.clearTimeout(U.current), []), xo();
  const j = P((l) => {
    var w, S;
    return pe.current === ((w = H.current) === null || w === void 0 ? void 0 : w.side) && Cn(l, (S = H.current) === null || S === void 0 ? void 0 : S.area);
  }, []);
  return /* @__PURE__ */ s(Qo, {
    scope: o,
    searchRef: A,
    onItemEnter: P((l) => {
      j(l) && l.preventDefault();
    }, [
      j
    ]),
    onItemLeave: P((l) => {
      var w;
      j(l) || ((w = _.current) === null || w === void 0 || w.focus(), W(null));
    }, [
      j
    ]),
    onTriggerLeave: P((l) => {
      j(l) && l.preventDefault();
    }, [
      j
    ]),
    pointerGraceTimerRef: le,
    onPointerGraceIntentChange: P((l) => {
      H.current = l;
    }, [])
  }, /* @__PURE__ */ s(O, lo, /* @__PURE__ */ s(_o, {
    asChild: !0,
    trapped: r,
    onMountAutoFocus: m(c, (l) => {
      var w;
      l.preventDefault(), (w = _.current) === null || w === void 0 || w.focus();
    }),
    onUnmountAutoFocus: d
  }, /* @__PURE__ */ s(go, {
    asChild: !0,
    disableOutsidePointerEvents: i,
    onEscapeKeyDown: p,
    onPointerDownOutside: f,
    onFocusOutside: a,
    onInteractOutside: b,
    onDismiss: v
  }, /* @__PURE__ */ s(Uo, $({
    asChild: !0
  }, Y, {
    dir: C.dir,
    orientation: "vertical",
    loop: t,
    currentTabStopId: ue,
    onCurrentTabStopIdChange: W,
    onEntryFocus: m(u, (l) => {
      C.isUsingKeyboardRef.current || l.preventDefault();
    })
  }), /* @__PURE__ */ s(Mo, $({
    role: "menu",
    "aria-orientation": "vertical",
    "data-state": Je(R.open),
    "data-radix-menu-content": "",
    dir: C.dir
  }, I, x, {
    ref: fe,
    style: {
      outline: "none",
      ...x.style
    },
    onKeyDown: m(x.onKeyDown, (l) => {
      const S = l.target.closest("[data-radix-menu-content]") === l.currentTarget, B = l.ctrlKey || l.altKey || l.metaKey, Z = l.key.length === 1;
      S && (l.key === "Tab" && l.preventDefault(), !B && Z && po(l.key));
      const $e = _.current;
      if (l.target !== $e || !Yo.includes(l.key))
        return;
      l.preventDefault();
      const te = z().filter(
        (J) => !J.disabled
      ).map(
        (J) => J.ref.current
      );
      Ke.includes(l.key) && te.reverse(), xn(te);
    }),
    onBlur: m(e.onBlur, (l) => {
      l.currentTarget.contains(l.target) || (window.clearTimeout(U.current), A.current = "");
    }),
    onPointerMove: m(e.onPointerMove, oe((l) => {
      const w = l.target, S = q.current !== l.clientX;
      if (l.currentTarget.contains(w) && S) {
        const B = l.clientX > q.current ? "right" : "left";
        pe.current = B, q.current = l.clientX;
      }
    }))
  })))))));
}), je = /* @__PURE__ */ g((e, n) => {
  const { __scopeMenu: o, ...t } = e;
  return /* @__PURE__ */ s(K.div, $({
    role: "group"
  }, t, {
    ref: n
  }));
}), tn = /* @__PURE__ */ g((e, n) => {
  const { __scopeMenu: o, ...t } = e;
  return /* @__PURE__ */ s(K.div, $({}, t, {
    ref: n
  }));
}), ge = "MenuItem", Ee = "menu.itemSelect", Ie = /* @__PURE__ */ g((e, n) => {
  const { disabled: o = !1, onSelect: t, ...r } = e, c = M(null), d = ne(ge, e.__scopeMenu), i = Me(ge, e.__scopeMenu), u = X(n, c), p = M(!1), f = () => {
    const a = c.current;
    if (!o && a) {
      const b = new CustomEvent(Ee, {
        bubbles: !0,
        cancelable: !0
      });
      a.addEventListener(
        Ee,
        (v) => t == null ? void 0 : t(v),
        {
          once: !0
        }
      ), ho(a, b), b.defaultPrevented ? p.current = !1 : d.onClose();
    }
  };
  return /* @__PURE__ */ s(Xe, $({}, r, {
    ref: u,
    disabled: o,
    onClick: m(e.onClick, f),
    onPointerDown: (a) => {
      var b;
      (b = e.onPointerDown) === null || b === void 0 || b.call(e, a), p.current = !0;
    },
    onPointerUp: m(e.onPointerUp, (a) => {
      var b;
      p.current || (b = a.currentTarget) === null || b === void 0 || b.click();
    }),
    onKeyDown: m(e.onKeyDown, (a) => {
      const b = i.searchRef.current !== "";
      o || b && a.key === " " || me.includes(a.key) && (a.currentTarget.click(), a.preventDefault());
    })
  }));
}), Xe = /* @__PURE__ */ g((e, n) => {
  const { __scopeMenu: o, disabled: t = !1, textValue: r, ...c } = e, d = Me(ge, o), i = Ue(o), u = M(null), p = X(n, u), [f, a] = k(!1), [b, v] = k("");
  return N(() => {
    const h = u.current;
    if (h) {
      var x;
      v(((x = h.textContent) !== null && x !== void 0 ? x : "").trim());
    }
  }, [
    c.children
  ]), /* @__PURE__ */ s(ee.ItemSlot, {
    scope: o,
    disabled: t,
    textValue: r ?? b
  }, /* @__PURE__ */ s(Bo, $({
    asChild: !0
  }, i, {
    focusable: !t
  }), /* @__PURE__ */ s(K.div, $({
    role: "menuitem",
    "data-highlighted": f ? "" : void 0,
    "aria-disabled": t || void 0,
    "data-disabled": t ? "" : void 0
  }, c, {
    ref: p,
    onPointerMove: m(e.onPointerMove, oe((h) => {
      t ? d.onItemLeave(h) : (d.onItemEnter(h), h.defaultPrevented || h.currentTarget.focus());
    })),
    onPointerLeave: m(e.onPointerLeave, oe(
      (h) => d.onItemLeave(h)
    )),
    onFocus: m(
      e.onFocus,
      () => a(!0)
    ),
    onBlur: m(
      e.onBlur,
      () => a(!1)
    )
  }))));
}), rn = /* @__PURE__ */ g((e, n) => {
  const { checked: o = !1, onCheckedChange: t, ...r } = e;
  return /* @__PURE__ */ s(He, {
    scope: e.__scopeMenu,
    checked: o
  }, /* @__PURE__ */ s(Ie, $({
    role: "menuitemcheckbox",
    "aria-checked": ce(o) ? "mixed" : o
  }, r, {
    ref: n,
    "data-state": De(o),
    onSelect: m(
      r.onSelect,
      () => t == null ? void 0 : t(ce(o) ? !0 : !o),
      {
        checkForDefaultPrevented: !1
      }
    )
  })));
}), cn = "MenuRadioGroup", [sn, an] = V(cn, {
  value: void 0,
  onValueChange: () => {
  }
}), dn = /* @__PURE__ */ g((e, n) => {
  const { value: o, onValueChange: t, ...r } = e, c = se(t);
  return /* @__PURE__ */ s(sn, {
    scope: e.__scopeMenu,
    value: o,
    onValueChange: c
  }, /* @__PURE__ */ s(je, $({}, r, {
    ref: n
  })));
}), un = "MenuRadioItem", fn = /* @__PURE__ */ g((e, n) => {
  const { value: o, ...t } = e, r = an(un, e.__scopeMenu), c = o === r.value;
  return /* @__PURE__ */ s(He, {
    scope: e.__scopeMenu,
    checked: c
  }, /* @__PURE__ */ s(Ie, $({
    role: "menuitemradio",
    "aria-checked": c
  }, t, {
    ref: n,
    "data-state": De(c),
    onSelect: m(t.onSelect, () => {
      var d;
      return (d = r.onValueChange) === null || d === void 0 ? void 0 : d.call(r, o);
    }, {
      checkForDefaultPrevented: !1
    })
  })));
}), We = "MenuItemIndicator", [He, ln] = V(We, {
  checked: !1
}), pn = /* @__PURE__ */ g((e, n) => {
  const { __scopeMenu: o, forceMount: t, ...r } = e, c = ln(We, o);
  return /* @__PURE__ */ s(ae, {
    present: t || ce(c.checked) || c.checked === !0
  }, /* @__PURE__ */ s(K.span, $({}, r, {
    ref: n,
    "data-state": De(c.checked)
  })));
}), $n = /* @__PURE__ */ g((e, n) => {
  const { __scopeMenu: o, ...t } = e;
  return /* @__PURE__ */ s(K.div, $({
    role: "separator",
    "aria-orientation": "horizontal"
  }, t, {
    ref: n
  }));
}), qe = "MenuSub", [bn, Ze] = V(qe), hn = (e) => {
  const { __scopeMenu: n, children: o, open: t = !1, onOpenChange: r } = e, c = L(qe, n), d = ie(n), [i, u] = k(null), [p, f] = k(null), a = se(r);
  return N(() => (c.open === !1 && a(!1), () => a(!1)), [
    c.open,
    a
  ]), /* @__PURE__ */ s(Ne, d, /* @__PURE__ */ s(Be, {
    scope: n,
    open: t,
    onOpenChange: a,
    content: p,
    onContentChange: f
  }, /* @__PURE__ */ s(bn, {
    scope: n,
    contentId: Q(),
    triggerId: Q(),
    trigger: i,
    onTriggerChange: u
  }, o)));
}, re = "MenuSubTrigger", mn = /* @__PURE__ */ g((e, n) => {
  const o = L(re, e.__scopeMenu), t = ne(re, e.__scopeMenu), r = Ze(re, e.__scopeMenu), c = Me(re, e.__scopeMenu), d = M(null), { pointerGraceTimerRef: i, onPointerGraceIntentChange: u } = c, p = {
    __scopeMenu: e.__scopeMenu
  }, f = P(() => {
    d.current && window.clearTimeout(d.current), d.current = null;
  }, []);
  return N(
    () => f,
    [
      f
    ]
  ), N(() => {
    const a = i.current;
    return () => {
      window.clearTimeout(a), u(null);
    };
  }, [
    i,
    u
  ]), /* @__PURE__ */ s(Ve, $({
    asChild: !0
  }, p), /* @__PURE__ */ s(Xe, $({
    id: r.triggerId,
    "aria-haspopup": "menu",
    "aria-expanded": o.open,
    "aria-controls": r.contentId,
    "data-state": Je(o.open)
  }, e, {
    ref: Re(n, r.onTriggerChange),
    onClick: (a) => {
      var b;
      (b = e.onClick) === null || b === void 0 || b.call(e, a), !(e.disabled || a.defaultPrevented) && (a.currentTarget.focus(), o.open || o.onOpenChange(!0));
    },
    onPointerMove: m(e.onPointerMove, oe((a) => {
      c.onItemEnter(a), !a.defaultPrevented && !e.disabled && !o.open && !d.current && (c.onPointerGraceIntentChange(null), d.current = window.setTimeout(() => {
        o.onOpenChange(!0), f();
      }, 100));
    })),
    onPointerLeave: m(e.onPointerLeave, oe((a) => {
      var b;
      f();
      const v = (b = o.content) === null || b === void 0 ? void 0 : b.getBoundingClientRect();
      if (v) {
        var h;
        const x = (h = o.content) === null || h === void 0 ? void 0 : h.dataset.side, R = x === "right", C = R ? -5 : 5, I = v[R ? "left" : "right"], Y = v[R ? "right" : "left"];
        c.onPointerGraceIntentChange({
          area: [
            // consistently within polygon bounds
            {
              x: a.clientX + C,
              y: a.clientY
            },
            {
              x: I,
              y: v.top
            },
            {
              x: Y,
              y: v.top
            },
            {
              x: Y,
              y: v.bottom
            },
            {
              x: I,
              y: v.bottom
            }
          ],
          side: x
        }), window.clearTimeout(i.current), i.current = window.setTimeout(
          () => c.onPointerGraceIntentChange(null),
          300
        );
      } else {
        if (c.onTriggerLeave(a), a.defaultPrevented)
          return;
        c.onPointerGraceIntentChange(null);
      }
    })),
    onKeyDown: m(e.onKeyDown, (a) => {
      const b = c.searchRef.current !== "";
      if (!(e.disabled || b && a.key === " ") && zo[t.dir].includes(a.key)) {
        var v;
        o.onOpenChange(!0), (v = o.content) === null || v === void 0 || v.focus(), a.preventDefault();
      }
    })
  })));
}), gn = "MenuSubContent", vn = /* @__PURE__ */ g((e, n) => {
  const o = ze(E, e.__scopeMenu), { forceMount: t = o.forceMount, ...r } = e, c = L(E, e.__scopeMenu), d = ne(E, e.__scopeMenu), i = Ze(gn, e.__scopeMenu), u = M(null), p = X(n, u);
  return /* @__PURE__ */ s(ee.Provider, {
    scope: e.__scopeMenu
  }, /* @__PURE__ */ s(ae, {
    present: t || c.open
  }, /* @__PURE__ */ s(ee.Slot, {
    scope: e.__scopeMenu
  }, /* @__PURE__ */ s(Ce, $({
    id: i.contentId,
    "aria-labelledby": i.triggerId
  }, r, {
    ref: p,
    align: "start",
    side: d.dir === "rtl" ? "left" : "right",
    disableOutsidePointerEvents: !1,
    disableOutsideScroll: !1,
    trapFocus: !1,
    onOpenAutoFocus: (f) => {
      var a;
      d.isUsingKeyboardRef.current && ((a = u.current) === null || a === void 0 || a.focus()), f.preventDefault();
    },
    onCloseAutoFocus: (f) => f.preventDefault(),
    onFocusOutside: m(e.onFocusOutside, (f) => {
      f.target !== i.trigger && c.onOpenChange(!1);
    }),
    onEscapeKeyDown: m(e.onEscapeKeyDown, (f) => {
      d.onClose(), f.preventDefault();
    }),
    onKeyDown: m(e.onKeyDown, (f) => {
      const a = f.currentTarget.contains(f.target), b = jo[d.dir].includes(f.key);
      if (a && b) {
        var v;
        c.onOpenChange(!1), (v = i.trigger) === null || v === void 0 || v.focus(), f.preventDefault();
      }
    })
  })))));
});
function Je(e) {
  return e ? "open" : "closed";
}
function ce(e) {
  return e === "indeterminate";
}
function De(e) {
  return ce(e) ? "indeterminate" : e ? "checked" : "unchecked";
}
function xn(e) {
  const n = document.activeElement;
  for (const o of e)
    if (o === n || (o.focus(), document.activeElement !== n))
      return;
}
function _n(e, n) {
  return e.map(
    (o, t) => e[(n + t) % e.length]
  );
}
function wn(e, n, o) {
  const r = n.length > 1 && Array.from(n).every(
    (p) => p === n[0]
  ) ? n[0] : n, c = o ? e.indexOf(o) : -1;
  let d = _n(e, Math.max(c, 0));
  r.length === 1 && (d = d.filter(
    (p) => p !== o
  ));
  const u = d.find(
    (p) => p.toLowerCase().startsWith(r.toLowerCase())
  );
  return u !== o ? u : void 0;
}
function Mn(e, n) {
  const { x: o, y: t } = e;
  let r = !1;
  for (let c = 0, d = n.length - 1; c < n.length; d = c++) {
    const i = n[c].x, u = n[c].y, p = n[d].x, f = n[d].y;
    u > t != f > t && o < (p - i) * (t - u) / (f - u) + i && (r = !r);
  }
  return r;
}
function Cn(e, n) {
  if (!n)
    return !1;
  const o = {
    x: e.clientX,
    y: e.clientY
  };
  return Mn(o, n);
}
function oe(e) {
  return (n) => n.pointerType === "mouse" ? e(n) : void 0;
}
const In = qo, Dn = Ve, yn = Jo, Pn = en, Sn = je, En = tn, Rn = Ie, Tn = rn, On = dn, Fn = fn, Nn = pn, An = $n, kn = hn, Gn = mn, Kn = vn, Qe = "DropdownMenu", [Ln, Gt] = _e(Qe, [
  Le
]), D = Le(), [Un, eo] = Ln(Qe), Bn = (e) => {
  const { __scopeDropdownMenu: n, children: o, dir: t, open: r, defaultOpen: c, onOpenChange: d, modal: i = !0 } = e, u = D(n), p = M(null), [f = !1, a] = xe({
    prop: r,
    defaultProp: c,
    onChange: d
  });
  return /* @__PURE__ */ s(Un, {
    scope: n,
    triggerId: Q(),
    triggerRef: p,
    contentId: Q(),
    open: f,
    onOpenChange: a,
    onOpenToggle: P(
      () => a(
        (b) => !b
      ),
      [
        a
      ]
    ),
    modal: i
  }, /* @__PURE__ */ s(In, $({}, u, {
    open: f,
    onOpenChange: a,
    dir: t,
    modal: i
  }), o));
}, Vn = "DropdownMenuTrigger", Yn = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, disabled: t = !1, ...r } = e, c = eo(Vn, o), d = D(o);
  return /* @__PURE__ */ s(Dn, $({
    asChild: !0
  }, d), /* @__PURE__ */ s(K.button, $({
    type: "button",
    id: c.triggerId,
    "aria-haspopup": "menu",
    "aria-expanded": c.open,
    "aria-controls": c.open ? c.contentId : void 0,
    "data-state": c.open ? "open" : "closed",
    "data-disabled": t ? "" : void 0,
    disabled: t
  }, r, {
    ref: Re(n, c.triggerRef),
    onPointerDown: m(e.onPointerDown, (i) => {
      !t && i.button === 0 && i.ctrlKey === !1 && (c.onOpenToggle(), c.open || i.preventDefault());
    }),
    onKeyDown: m(e.onKeyDown, (i) => {
      t || ([
        "Enter",
        " "
      ].includes(i.key) && c.onOpenToggle(), i.key === "ArrowDown" && c.onOpenChange(!0), [
        "Enter",
        " ",
        "ArrowDown"
      ].includes(i.key) && i.preventDefault());
    })
  })));
}), zn = (e) => {
  const { __scopeDropdownMenu: n, ...o } = e, t = D(n);
  return /* @__PURE__ */ s(yn, $({}, t, o));
}, jn = "DropdownMenuContent", Xn = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = eo(jn, o), c = D(o), d = M(!1);
  return /* @__PURE__ */ s(Pn, $({
    id: r.contentId,
    "aria-labelledby": r.triggerId
  }, c, t, {
    ref: n,
    onCloseAutoFocus: m(e.onCloseAutoFocus, (i) => {
      var u;
      d.current || (u = r.triggerRef.current) === null || u === void 0 || u.focus(), d.current = !1, i.preventDefault();
    }),
    onInteractOutside: m(e.onInteractOutside, (i) => {
      const u = i.detail.originalEvent, p = u.button === 0 && u.ctrlKey === !0, f = u.button === 2 || p;
      (!r.modal || f) && (d.current = !0);
    }),
    style: {
      ...e.style,
      "--radix-dropdown-menu-content-transform-origin": "var(--radix-popper-transform-origin)",
      "--radix-dropdown-menu-content-available-width": "var(--radix-popper-available-width)",
      "--radix-dropdown-menu-content-available-height": "var(--radix-popper-available-height)",
      "--radix-dropdown-menu-trigger-width": "var(--radix-popper-anchor-width)",
      "--radix-dropdown-menu-trigger-height": "var(--radix-popper-anchor-height)"
    }
  }));
}), Wn = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(Sn, $({}, r, t, {
    ref: n
  }));
}), Hn = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(En, $({}, r, t, {
    ref: n
  }));
}), qn = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(Rn, $({}, r, t, {
    ref: n
  }));
}), Zn = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(Tn, $({}, r, t, {
    ref: n
  }));
}), Jn = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(On, $({}, r, t, {
    ref: n
  }));
}), Qn = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(Fn, $({}, r, t, {
    ref: n
  }));
}), et = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(Nn, $({}, r, t, {
    ref: n
  }));
}), ot = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(An, $({}, r, t, {
    ref: n
  }));
}), nt = (e) => {
  const { __scopeDropdownMenu: n, children: o, open: t, onOpenChange: r, defaultOpen: c } = e, d = D(n), [i = !1, u] = xe({
    prop: t,
    defaultProp: c,
    onChange: r
  });
  return /* @__PURE__ */ s(kn, $({}, d, {
    open: i,
    onOpenChange: u
  }), o);
}, tt = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(Gn, $({}, r, t, {
    ref: n
  }));
}), rt = /* @__PURE__ */ g((e, n) => {
  const { __scopeDropdownMenu: o, ...t } = e, r = D(o);
  return /* @__PURE__ */ s(Kn, $({}, r, t, {
    ref: n,
    style: {
      ...e.style,
      "--radix-dropdown-menu-content-transform-origin": "var(--radix-popper-transform-origin)",
      "--radix-dropdown-menu-content-available-width": "var(--radix-popper-available-width)",
      "--radix-dropdown-menu-content-available-height": "var(--radix-popper-available-height)",
      "--radix-dropdown-menu-trigger-width": "var(--radix-popper-anchor-width)",
      "--radix-dropdown-menu-trigger-height": "var(--radix-popper-anchor-height)"
    }
  }));
}), ct = Bn, st = Yn, oo = zn, no = Xn, at = Wn, to = Hn, ro = qn, co = Zn, dt = Jn, so = Qn, ao = et, io = ot, it = nt, uo = tt, fo = rt, Kt = ct, Lt = st, Ut = at, Bt = oo, Vt = it, Yt = dt, ut = G.forwardRef(({ className: e, inset: n, children: o, ...t }, r) => /* @__PURE__ */ ve(
  uo,
  {
    ref: r,
    className: T(
      "dhs-flex dhs-cursor-default dhs-select-none dhs-items-center dhs-rounded-sm dhs-px-2 dhs-py-1.5 dhs-text-sm dhs-outline-none focus:dhs-bg-accent data-[state=open]:dhs-bg-accent",
      n && "dhs-pl-8",
      e
    ),
    ...t,
    children: [
      o,
      /* @__PURE__ */ y(yo, { className: "dhs-ml-auto dhs-h-4 dhs-w-4" })
    ]
  }
));
ut.displayName = uo.displayName;
const ft = G.forwardRef(({ className: e, ...n }, o) => /* @__PURE__ */ y(
  fo,
  {
    ref: o,
    className: T(
      "dhs-z-50 dhs-min-w-[8rem] odhs-verflow-hidden dhs-rounded-md dhs-border dhs-bg-popover dhs-p-1 dhs-text-popover-foreground dhs-shadow-lg data-[state=open]:dhs-animate-in data-[state=closed]:dhs-animate-out data-[state=closed]:dhs-fade-out-0 data-[state=open]:dhs-fade-in-0 data-[state=closed]:dhs-zoom-out-95 data-[state=open]:dhs-zoom-in-95 data-[side=bottom]:dhs-slide-in-from-top-2 data-[side=left]:dhs-slide-in-from-right-2 data-[side=right]:dhs-slide-in-from-left-2 data-[side=top]:dhs-slide-in-from-bottom-2",
      e
    ),
    ...n
  }
));
ft.displayName = fo.displayName;
const lt = G.forwardRef(({ className: e, sideOffset: n = 4, portalContainer: o, ...t }, r) => /* @__PURE__ */ y(oo, { container: o ?? document.getElementById("swap-main"), children: /* @__PURE__ */ y(
  no,
  {
    ref: r,
    sideOffset: n,
    className: T(
      "dhs-z-50 dhs-min-w-[8rem] dhs-bg-containers dhs-bg-opacity-50 dhs-backdrop-blur-[10px] dhs-overflow-hidden dhs-rounded-xl dhs-p-1 dhs-text-popover-foreground dhs-shadow-md",
      "data-[state=open]:dhs-animate-in data-[state=closed]:dhs-animate-out data-[state=closed]:dhs-fade-out-0 data-[state=open]:dhs-fade-in-0 data-[state=closed]:dhs-zoom-out-95 data-[state=open]:dhs-zoom-in-95 data-[side=bottom]:dhs-slide-in-from-top-2 data-[side=left]:dhs-slide-in-from-right-2 data-[side=right]:dhs-slide-in-from-left-2 data-[side=top]:dhs-slide-in-from-bottom-2",
      e
    ),
    ...t
  }
) }));
lt.displayName = no.displayName;
const pt = G.forwardRef(({ className: e, inset: n, ...o }, t) => /* @__PURE__ */ y(
  ro,
  {
    ref: t,
    className: T(
      "dhs-relative dhs-flex dhs-bg-background dhs-cursor-default dhs-select-none dhs-items-center dhs-rounded-sm dhs-px-2 dhs-py-1.5 dhs-text-sm dhs-outline-none dhs-transition-colors focus:dhs-bg-accent focus:dhs-text-accent-foreground data-[disabled]:dhs-pointer-events-none data-[disabled]:dhs-opacity-50",
      n && "dhs-pl-8",
      e
    ),
    ...o
  }
));
pt.displayName = ro.displayName;
const $t = G.forwardRef(({ className: e, children: n, checked: o, ...t }, r) => /* @__PURE__ */ ve(
  co,
  {
    ref: r,
    className: T(
      "dhs-relative dhs-flex dhs-cursor-default dhs-text-xs dhs-font-proximaBold dhs-text-gray-101 dhs-select-none dhs-items-center dhs-justify-between dhs-rounded-sm dhs-py-2 dhs-pl-4 dhs-pr-3 dhs-text-sm dhs-outline-none dhs-transition-colors focus:dhs-bg-accent focus:dhs-text-accent-foreground data-[disabled]:dhs-pointer-events-none data-[disabled]:dhs-opacity-50",
      e
    ),
    checked: o,
    ...t,
    children: [
      n,
      /* @__PURE__ */ y(
        "span",
        {
          className: T(
            "dhs-flex dhs-w-5 dhs-h-5 dhs-items-center dhs-justify-center dhs-border-2 dhs-rounded-[7px]",
            o ? "dhs-border-blue-101" : "dhs-border-gray-111"
          ),
          children: /* @__PURE__ */ y(ao, { children: /* @__PURE__ */ y(Io, { className: "dhs-h-2.5 dhs-w-2.5 dhs-text-white" }) })
        }
      )
    ]
  }
));
$t.displayName = co.displayName;
const bt = G.forwardRef(({ className: e, children: n, ...o }, t) => /* @__PURE__ */ ve(
  so,
  {
    ref: t,
    className: T(
      "dhs-relative dhs-flex dhs-cursor-default dhs-select-none dhs-items-center dhs-rounded-sm dhs-py-1.5 dhs-pl-8 dhs-pr-2 dhs-text-sm dhs-outline-none dhs-transition-colors focus:dhs-bg-accent focus:dhs-text-accent-foreground data-[disabled]:dhs-pointer-events-none data-[disabled]:dhs-opacity-50",
      e
    ),
    ...o,
    children: [
      /* @__PURE__ */ y("span", { className: "dhs-absolute dhs-left-2 dhs-flex dhs-h-3.5 dhs-w-3.5 dhs-items-center dhs-justify-center", children: /* @__PURE__ */ y(ao, {}) }),
      n
    ]
  }
));
bt.displayName = so.displayName;
const ht = G.forwardRef(({ className: e, inset: n, ...o }, t) => /* @__PURE__ */ y(
  to,
  {
    ref: t,
    className: T(
      "dhs-px-2 dhs-py-1.5 dhs-text-sm dhs-font-semibold",
      n && "dhs-pl-8",
      e
    ),
    ...o
  }
));
ht.displayName = to.displayName;
const mt = G.forwardRef(({ className: e, ...n }, o) => /* @__PURE__ */ y(
  io,
  {
    ref: o,
    className: T("-dhs-mx-1 dhs-my-1 dhs-h-px dhs-bg-muted", e),
    ...n
  }
));
mt.displayName = io.displayName;
const gt = ({
  className: e,
  ...n
}) => /* @__PURE__ */ y(
  "span",
  {
    className: T("dhs-ml-auto dhs-text-xs dhs-tracking-widest dhs-opacity-60", e),
    ...n
  }
);
gt.displayName = "DropdownMenuShortcut";
export {
  Kt as DropdownMenu,
  $t as DropdownMenuCheckboxItem,
  lt as DropdownMenuContent,
  Ut as DropdownMenuGroup,
  pt as DropdownMenuItem,
  ht as DropdownMenuLabel,
  Bt as DropdownMenuPortal,
  Yt as DropdownMenuRadioGroup,
  bt as DropdownMenuRadioItem,
  mt as DropdownMenuSeparator,
  gt as DropdownMenuShortcut,
  Vt as DropdownMenuSub,
  ft as DropdownMenuSubContent,
  ut as DropdownMenuSubTrigger,
  Lt as DropdownMenuTrigger
};
