// filepath: social-feed/hooks/usePosts.ts
import { useFetchPostsFeed, useCreatePost, useUpdatePost, useDeletePost } from '@/hooks/convexHooks';
import { useMemo } from 'react';
import { PostData } from '@/types/post';

const usePosts = () => {
  // Fetch posts feed (customize args as needed)
  const postsFeed = useFetchPostsFeed({});
  const createPost = useCreatePost();
  const updatePost = useUpdatePost();
  const deletePost = useDeletePost();

  const posts = postsFeed?.posts || [];
  const loading = postsFeed === undefined;
  const error = postsFeed && (postsFeed?.error || null);

  const handleCreatePost = async (postData: PostData) => {
    try {
      await createPost(postData);
    } catch (err) {
      console.error('Failed to create post:', err);
    }
  };

  const handleUpdatePost = async (postId: string, updatedData: Partial<PostData>) => {
    try {
      await updatePost({ postId, ...updatedData });
    } catch (err) {
      console.error('Failed to update post:', err);
    }
  };

  const handleDeletePost = async (postId: string) => {
    try {
      await deletePost({ postId });
    } catch (err) {
      console.error('Failed to delete post:', err);
    }
  };

  return {
    posts,
    loading,
    error,
    handleCreatePost,
    handleUpdatePost,
    handleDeletePost,
  };
};
export default usePosts;