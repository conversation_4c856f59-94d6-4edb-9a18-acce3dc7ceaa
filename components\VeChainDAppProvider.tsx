// DAppKitProvider.tsx
'use client';

import React, { ReactNode, useEffect } from 'react';
import { DAppKitProvider as VeChainProviderOriginal } from '@vechain/dapp-kit-react';
import useVechainWalletIntegration from '@/context/Wallet/VechainWallet';

interface DAppKitProviderProps {
  children: ReactNode;
  isDarkTheme: boolean;
}

export default function DAppKitProvider({ children, isDarkTheme }: DAppKitProviderProps) {
  // Initialize VeChain wallet integration on client-side
  const {
    account,
    source,
    connect,
    availableWallets,
    disconnect,
    connectionCertificate,
  } = useVechainWalletIntegration();

  // Only render provider once client hook is ready
  if (!connectionCertificate) {
    // Optionally show loading indicator or return children
    return <>{children}</>;
  }

  return (
    <VeChainProviderOriginal
      account={account}
      source={source}
      connectionCertificate={connectionCertificate}
      connect={connect}
      availableWallets={availableWallets}
      disconnect={disconnect}
      theme={isDarkTheme ? 'dark' : 'light'}
    >
      {children}
    </VeChainProviderOriginal>
  );
}