import { p as i } from "../immer-548168ec.js";
const c = (a) => ({
  open: !1,
  api: void 0,
  selectedWallet: null,
  balance: null,
  userAddress: null,
  stakeKey: null,
  desiredWallet: null,
  isLoadingWallet: !0,
  userOrders: [],
  userTokens: [],
  availableWallets: [],
  addressList: [],
  isOpenWallet: !1,
  onClickWalletConnect: void 0,
  setIsLoadingWallet: (l) => {
    a(
      i((e) => {
        e.walletSlice.isLoadingWallet = l;
      })
    );
  },
  onWalletConnect: void 0,
  resetWallet: () => {
    a(
      i((l) => {
        l.walletSlice.api = void 0, l.walletSlice.selectedWallet = null, l.walletSlice.balance = null, l.walletSlice.userAddress = null, l.walletSlice.stakeKey = null, l.walletSlice.desiredWallet = null, l.walletSlice.isLoadingWallet = !1, l.walletSlice.userOrders = [], l.walletSlice.userTokens = [], l.walletSlice.addressList = [], localStorage.removeItem("dexhunter-selected-wallet");
      })
    );
  },
  updateSelectedWallet: (l) => {
    a(
      i((e) => {
        e.walletSlice.selectedWallet = l.selectedWallet, e.walletSlice.api = l.api, e.walletSlice.balance = l.balance, e.walletSlice.userAddress = l.userAddress, e.walletSlice.stakeKey = l.stakeKey, e.walletSlice.addressList = l.addressList;
      })
    );
  },
  openModal: () => {
    a(
      i((l) => {
        l.walletSlice.open = !0;
      })
    );
  },
  closeModal: (l) => {
    a(
      i((e) => {
        e.walletSlice.open = !1, e.walletSlice.isLoadingWallet = !1;
      })
    ), l && l();
  },
  toggleModal: () => {
    a(
      i((l) => {
        l.walletSlice.open = !l.walletSlice.open;
      })
    );
  },
  setUserOrders: (l) => {
    a(
      i((e) => {
        e.walletSlice.userOrders = l;
      })
    );
  },
  setUserTokens: (l) => {
    a(
      i((e) => {
        e.walletSlice.userTokens = l;
      })
    );
  },
  setAvailableWallets: (l) => {
    a(
      i((e) => {
        e.walletSlice.availableWallets = l;
      })
    );
  },
  updateBalance: (l) => {
    a(
      i((e) => {
        e.walletSlice.balance = l;
      })
    );
  },
  setAddressList: (l) => {
    a(
      i((e) => {
        e.walletSlice.addressList = l;
      })
    );
  },
  setIsOpenWallet: (l) => {
    a(
      i((e) => {
        e.walletSlice.isOpenWallet = l;
      })
    );
  },
  setOnWalletConnect: (l) => {
    a(
      i((e) => {
        e.walletSlice.onWalletConnect = l;
      })
    );
  },
  setOnClickWalletConnect: (l) => {
    a(
      i((e) => {
        e.walletSlice.onClickWalletConnect = l;
      })
    );
  }
});
export {
  c as default
};
