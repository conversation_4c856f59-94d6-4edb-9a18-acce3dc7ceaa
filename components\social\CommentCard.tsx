import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { deleteComment } from '@/redux/slices/commentsSlice';
import { Button } from '@/components/ui/button';

interface CommentCardProps {
  comment: {
    id: string;
    user: {
      username: string;
      avatarUrl: string;
    };
    text: string;
    createdAt: string;
  };
}

const CommentCard: React.FC<CommentCardProps> = ({ comment }: any) => {
  const dispatch = useDispatch();

  const handleDelete = () => {
    (dispatch as any)(deleteComment(comment.id)).unwrap()
      .then(() => {
        // Handle successful deletion if needed
      })
      .catch((error: any) => {
        console.error('Failed to delete comment:', error);
      });
  };

  return (
    <div className="flex items-start space-x-4 p-4 border-b">
      <img src={comment.user.avatarUrl} alt={`${comment.user.username}'s avatar`} className="h-10 w-10 rounded-full" />
      <div className="flex-1">
        <div className="flex justify-between">
          <span className="font-semibold">{comment.user.username}</span>
          <span className="text-gray-500 text-sm">{new Date(comment.createdAt).toLocaleString()}</span>
        </div>
        <p className="mt-1">{comment.text}</p>
        <Button onClick={handleDelete} className="mt-2 text-red-500">
          Delete
        </Button>
      </div>
    </div>
  );
};
export default CommentCard;