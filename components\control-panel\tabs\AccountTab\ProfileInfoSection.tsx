import React from 'react';
import { LabelInputContainer } from '@/components/ui/label-input-container';
import { Label } from '@/components/ui/label';
import { AccountFormData } from '@/components/account/settings/tabs/AccountTab/AccountTab';

interface LocationSuggestion {
  display_name: string;
  place_id: number;
  lat: string;
  lon: string;
  type: string;
}

interface ProfileInfoSectionProps {
  formData: AccountFormData;
  setFormData: React.Dispatch<React.SetStateAction<AccountFormData>>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  locationSuggestions: LocationSuggestion[];
  showLocationSuggestions: boolean;
  isLoadingLocationSuggestions: boolean;
  handleLocationSelect: (suggestion: LocationSuggestion, field: 'location') => void;
  amazonUrlError: string;
  websiteError: string | null;
  locationSuggestionsRef: React.RefObject<HTMLDivElement>;
}

const ProfileInfoSection: React.FC<ProfileInfoSectionProps> = ({
  formData,
  handleInputChange,
  locationSuggestions,
  showLocationSuggestions,
  isLoadingLocationSuggestions,
  handleLocationSelect,
  amazonUrlError,
  websiteError,
  locationSuggestionsRef,
}) => {
  return (
    <div className="space-y-6">
      {/* Profile Info Section */}
      <LabelInputContainer className="mb-6 mt-10">
        <h3 className="text-xl font-semibold text-[#121212] dark:text-white mb-1">Profile Info</h3>
        <div className="mb-6 border border-solid border-b border-l-0 border-r-0 border-t-0 border-black/60 dark:border-white/60"></div>
      </LabelInputContainer>

      {/* Display Name */}
      <LabelInputContainer>
        <Label htmlFor="displayName">Display Name</Label>
        <input
          id="displayName"
          name="displayName"
          placeholder="Your display name"
          value={formData.displayName}
          onChange={handleInputChange}
          className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
        />
      </LabelInputContainer>

      {/* Bio */}
      <LabelInputContainer>
        <Label htmlFor="bio">Bio</Label>
        <textarea
          id="bio"
          name="bio"
          placeholder="Tell us about yourself"
          value={formData.bio}
          onChange={handleInputChange}
          style={{ resize: 'none' }}
          className="flex w-full h-40 border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
        />
      </LabelInputContainer>

      {/* Location */}
      <LabelInputContainer>
        <Label htmlFor="location">Location</Label>
        <div className="relative">
          <input
            id="location"
            name="location"
            placeholder="Enter your city or country"
            value={formData.location}
            onChange={handleInputChange}
            autoComplete="off"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
          {showLocationSuggestions && (
            <div
              ref={locationSuggestionsRef}
              className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto"
            >
              {isLoadingLocationSuggestions ? (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  Loading suggestions...
                </div>
              ) : locationSuggestions.length > 0 ? (
                locationSuggestions.map((suggestion) => (
                  <div
                    key={suggestion.place_id}
                    className="px-4 py-2 hover:bg-gray-300 dark:hover:bg-gray-700 cursor-pointer text-sm"
                    onClick={() => handleLocationSelect(suggestion, 'location')}
                  >
                    {suggestion.display_name}
                  </div>
                ))
              ) : (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  No locations found. Try typing something different.
                </div>
              )}
            </div>
          )}
        </div>
      </LabelInputContainer>

      {/* Amazon Wishlist URL */}
      <LabelInputContainer>
        <Label htmlFor="amazonWishlist">Amazon Wishlist URL</Label>
        <input
          id="amazonWishlist"
          name="amazonWishlist"
          placeholder="https://amazon.com/wishlist/..."
          value={formData.amazonWishlist}
          onChange={handleInputChange}
          className={`flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400 ${amazonUrlError ? 'ring-[2px] ring-red-500' : ''}`}
        />
        {amazonUrlError && <p className="text-red-500 text-xs mt-1">{amazonUrlError}</p>}
      </LabelInputContainer>

      {/* Official Website */}
      <LabelInputContainer>
        <Label htmlFor="officialWebsite">Official Website</Label>
        <input
          id="officialWebsite"
          name="officialWebsite"
          placeholder="https://yourwebsite.com"
          value={formData.officialWebsite}
          onChange={handleInputChange}
          className={`flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400 ${websiteError ? 'ring-[2px] ring-red-500' : ''}`}
        />
        {websiteError && <p className="text-red-500 text-xs mt-1">{websiteError}</p>}
      </LabelInputContainer>
    </div>
  );
};
export default ProfileInfoSection;