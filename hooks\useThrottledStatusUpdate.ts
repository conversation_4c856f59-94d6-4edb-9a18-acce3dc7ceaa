import { useRef } from "react";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";

export function useThrottledStatusUpdate(userId: string | undefined) {
    const updateUserStatus = useMutation(api.accounts.updateUserStatus);
    const lastStatus = useRef<string | null>(null);
    const lastUpdate = useRef<number>(0);

    return async (status: string) => {
        const now = Date.now();
        if (
            userId &&
            (status !== lastStatus.current || now - lastUpdate.current > 30000)
        ) {
            await updateUserStatus({
                user_id: userId,
                status,
                last_active: new Date().toISOString(),
            });
            lastStatus.current = status;
            lastUpdate.current = now;
        }
    };
};