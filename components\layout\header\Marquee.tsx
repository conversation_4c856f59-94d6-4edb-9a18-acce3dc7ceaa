'use client';

import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import FastMarquee from 'react-fast-marquee';
import Link from 'next/link';

interface MarqueeProps {
  items: {
    text: string;
    underscore?: boolean;
    color?: string;
    link?: string;
    linkText?: string;
  }[];
  speed?: number;
  className?: string;
}

export default function Marquee({ items, speed = 30, className }: MarqueeProps) {
  const [duplicatedItems, setDuplicatedItems] = useState<typeof items>([]);

  useEffect(() => {
    setDuplicatedItems([...items, ...items]);
  }, [items]);

  return (
    <div className={cn(
      "relative w-full overflow-hidden bg-black/5 dark:bg-white/5",
      className
    )}>
      <FastMarquee
        speed={speed}
        gradient={false}
        pauseOnHover={true}
        className="py-2"
      >
        {duplicatedItems.map((item, idx) => (
          <div
            key={idx}
            className="mx-4 mr-0 flex items-center"
          >
            {item.link ? (
              <a
                href={item.link}
                className={cn(
                  "hover:underline",
                  item.color || "text-foreground",
                  "dark:text-white"
                )}
                target="_blank"
                rel="noopener noreferrer"
              >
                {item.text}
                {item.linkText && <span className="ml-1">{item.linkText}</span>}
              </a>
            ) : (
              <span className={cn(item.color || "text-foreground dark:text-white", item.underscore ? "underline" : "dark:text-white")}>
                {item.text}
                {item.linkText && <a href={item?.link} className="ml-1 underline">{item.linkText}</a>}
              </span>
            )}
            <span className="mx-4 mr-0 text-foreground/50">•</span>
          </div>
        ))}
      </FastMarquee>
    </div>
  );
};