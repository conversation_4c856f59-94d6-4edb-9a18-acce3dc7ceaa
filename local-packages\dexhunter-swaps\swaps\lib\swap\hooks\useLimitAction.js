import m from "../../store/useStore.js";
import { server as f } from "../../config/axios.js";
import { useNotify as R } from "../../hooks/useNotify.js";
import { findHighestLiquidity as Y } from "../../utils/formatToken.js";
import { CARDANO_TOKEN_IDENTIFIER as L } from "../components/tokens.js";
import { a as H } from "../../axios-ddd885c5.js";
import { u as J } from "../../QueryClientProvider-6bcd4331.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "react/jsx-runtime";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../hooks/useScreen.js";
import "../../index-ca8eb9e1.js";
const Ot = () => {
  const { notify: g } = R(), S = J(), {
    tokenBuy: a,
    tokenSell: i,
    sellAmount: y,
    setIsTransactionLoading: c,
    estimationError: N,
    dexBlacklist: E,
    setBuyAmount: F,
    setSellAmount: v,
    setSwapDetails: P,
    swapDetails: o,
    limitPrice: b,
    limitMultiples: w,
    setLimitMultiples: K,
    setIsSwapSubmitted: M,
    onViewOrder: x
  } = m((t) => t.swapSlice), {
    api: n,
    userAddress: h,
    balance: U
  } = m((t) => t.walletSlice), { poolInfo: V } = m((t) => t.chartSlice), {
    setUpcomingOrders: X,
    setPendingOrdersCount: q,
    pendingOrdersCount: z
  } = m((t) => t.userOrdersSlice), { partnerCode: B, partnerName: D } = m((t) => t.globalSettingsSlice), d = () => {
    const t = (o.total_fee + o.partner_fee) / 1e6, p = parseFloat(y);
    return p + t > U && (i == null ? void 0 : i.token_id) === "" ? parseFloat(p - t) : parseFloat(p);
  };
  return { limitToken: async () => {
    var p, T, C, I, k, A;
    if (c(!0), y === 0 || N) {
      c(!1);
      return;
    }
    let t = {
      sign: null,
      tx: "",
      err: null,
      step: "pre-swap",
      payload: null
    };
    try {
      const r = Y(V), O = {
        buyer_address: h,
        token_in: i == null ? void 0 : i.token_id,
        token_out: (a == null ? void 0 : a.token_id) || "",
        amount_in: d(),
        blacklisted_dexes: E,
        wanted_price: parseFloat(b),
        to_split: !(r != null && r.dexName),
        multiples: w,
        dex: r == null ? void 0 : r.dexName,
        referrer: D
      };
      t.payload = O;
      const { data: u } = await f.post("/swap/limit/build", O, {
        headers: {
          "X-Partner-Id": B
        }
      });
      t.swap = u, t.step = "pre-sign";
      const Q = await (n == null ? void 0 : n.signTx(u.cbor, !0)), { data: s } = await f.post("/swap/sign", {
        txCbor: u.cbor,
        signatures: Q
      });
      t.sign = s, t.step = "pre-submit";
      const l = await (n == null ? void 0 : n.submitTx(s.cbor));
      t.tx = l, t.step = "after-submit";
      try {
        await f.post("/marking/submit", {
          tx_hash: l,
          order_type: "LIMIT"
        });
      } catch (e) {
        console.log(e), t.marking_err = ((p = e == null ? void 0 : e.response) == null ? void 0 : p.data) || (e == null ? void 0 : e.message) || (e == null ? void 0 : e.info);
      }
      if (s != null && s.strat_id)
        try {
          await H.put("https://api.axo.trade/notify", {
            tx_id: l,
            strat_id: s == null ? void 0 : s.strat_id
          }, { headers: { "X-Api-Key": "zIXsxTvPrmu7VstLXf2UvAZVTf64zK9t" } });
        } catch (e) {
          console.log(e), t.marking_err = ((T = e == null ? void 0 : e.response) == null ? void 0 : T.data) || (e == null ? void 0 : e.message) || (e == null ? void 0 : e.info);
        }
      const _ = o == null ? void 0 : o.splits.map((e) => ({
        ...e,
        tx_hash: l,
        status: "SUBMITTED",
        amount_in: d(),
        token_id_in: (i == null ? void 0 : i.token_id) || L,
        token_id_out: (a == null ? void 0 : a.token_id) || L,
        expected_out_amount: d() / parseFloat(b),
        submission_time: (/* @__PURE__ */ new Date()).toISOString(),
        user_address: h,
        upcoming: !0,
        type: (i == null ? void 0 : i.token_id) === "" ? "BUY" : "SELL"
      }));
      M(!0), g({
        type: "success",
        title: "Order placed",
        desc: "Your order has been placed successfully",
        actionName: "View order",
        dataCallback: _,
        actionCallback: () => {
          if (x) {
            x(_);
            return;
          }
          console.log("callback");
        }
      }), X(_), q(z + (w || ((C = o == null ? void 0 : o.splits) == null ? void 0 : C.length))), v(null), F(0), K(0), P(null);
    } catch (r) {
      if (console.log(r), console.log(r.message), t.err = ((I = r.response) == null ? void 0 : I.data) || r.message || r.info, (k = r.message) != null && k.toLowerCase().includes("declined") || (A = r.info) != null && A.toLowerCase().includes("declined"))
        return;
      g({
        type: "error",
        title: "Error placing order",
        desc: "There was an error placing your order",
        actionCallback: () => {
          navigator.clipboard.writeText(JSON.stringify(t));
        }
      });
    } finally {
      c(!1), S.invalidateQueries({
        predicate: (r) => r.queryKey[0] === "userBalance"
      });
    }
  } };
};
export {
  Ot as useLimitAction
};
