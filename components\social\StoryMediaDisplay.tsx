// components/StoryMediaDisplay.tsx
'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';

interface StoryMediaDisplayProps {
  url: string;
  thumbnail?: string;
  type: 'image' | 'video';
  isMuted: boolean;
  onLoaded: () => void;
  ref?: React.Ref<HTMLVideoElement>;
}

export const StoryMediaDisplay: React.FC<StoryMediaDisplayProps> = ({
  url,
  thumbnail,
  type,
  isMuted,
  onLoaded,
  ref,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isBackgroundLoaded, setIsBackgroundLoaded] = useState(false);
  // we need a ref to the video so we can listen for loadeddata
  const videoRef = useRef<HTMLVideoElement>(null);

  return (
    <div className="relative w-full h-full rounded-lg overflow-hidden bg-black">
      {/* 1) Blurred background */}
      <Skeleton loading={!isBackgroundLoaded} width="100%" height="100%" borderRadius="lg">
        <Image
          src={thumbnail}
          alt=""
          fill
          className="object-cover filter blur-lg scale-110"
          onLoad={() => {
            onLoaded();
            setIsBackgroundLoaded(true);
          }}
          unoptimized

        />
      </Skeleton>

      {/* 2) Foreground “real” media */}
      <div className="absolute inset-0 flex items-center justify-center p-0">
        {type === 'image' ? (
          <Skeleton loading={isLoading} width="100%" height="100%" borderRadius="lg">
            <Image
              src={url}
              alt=""
              width={1} // width=1 + height=1 + style objectFit => perfect aspect
              height={1}
              style={{ objectFit: 'contain' }}
              className="max-h-full max-w-full rounded-lg shadow-lg w-full h-full"
              onLoad={() => {
                onLoaded();
                setIsLoading(false);
              }}
              unoptimized
              ref={ref}
            />
          </Skeleton>
        ) : (
          <Skeleton loading={isLoading} width="100%" height="100%" borderRadius="lg">
            <video
              ref={videoRef}
              src={url}
              className="max-h-full max-w-full object-contain rounded-lg shadow-lg w-full h-full"
              muted={isMuted}
              controls={false}
              autoPlay
              onLoadedData={() => {
                onLoaded();
                setIsLoading(false);
              }}
              ref={ref}
            />
          </Skeleton>
        )}
      </div>
    </div >
  );
};