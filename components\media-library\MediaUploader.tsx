'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Upload, Image, Film, X, Library, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { toast } from 'react-toastify';
import MediaLibraryModal from './MediaLibraryModal';
import { generateVideoThumbnails, importFileandPreview } from "@rajesh896/video-thumbnails-generator";
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/react-scroll-area';

interface MediaUploaderProps {
  onMediaSelected: (mediaUrl: string, mediaType: string, thumbnailUrl?: string) => void;
  onMediaRemoved?: () => void;
  allowedTypes?: string[];
  maxSize?: number; // in MB
  saveToLibrary?: boolean;
  onSaveToLibraryChange?: (save: boolean) => void;
}

export function MediaUploader({
  onMediaSelected,
  onMediaRemoved,
  allowedTypes = ['image/*', 'video/*'],
  maxSize = 10, // Default 10MB
  saveToLibrary = true,
  onSaveToLibraryChange
}: MediaUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [shouldSaveToLibrary, setShouldSaveToLibrary] = useState(saveToLibrary);
  const [isLibraryModalOpen, setIsLibraryModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Thumbnail generation states
  const [thumbnails, setThumbnails] = useState<string[]>([]);
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(null);
  const [isGeneratingThumbnails, setIsGeneratingThumbnails] = useState(false);
  const [thumbnailCount, setThumbnailCount] = useState(6); // Default number of thumbnails
  const videoRef = useRef<HTMLVideoElement>(null);

  // Handle file selection
  const handleFileSelect = async (file: File) => {
    // Check file type
    const isAllowedType = allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        const mainType = type.split('/')[0];
        return file.type.startsWith(`${mainType}/`);
      }
      return file.type === type;
    });

    if (!isAllowedType) {
      toast.error(`File type not allowed. Please upload ${allowedTypes.join(', ')}`);
      return;
    }

    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      toast.error(`File too large. Maximum size is ${maxSize}MB`);
      return;
    }

    setSelectedFile(file);

    // Create preview URL
    try {
      const url = await importFileandPreview(file);
      setPreviewUrl(url);

      // Generate thumbnails automatically if it's a video
      if (file.type.startsWith('video/')) {
        generateThumbnails(file);
      }
    } catch (error) {
      console.error("Error creating preview:", error);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  // Generate thumbnails for video
  const generateThumbnails = async (videoFile: File) => {
    if (!videoFile || !videoFile.type.startsWith('video/')) return;

    try {
      setIsGeneratingThumbnails(true);
      setThumbnails([]);

      const thumbs = await generateVideoThumbnails(videoFile, thumbnailCount);
      setThumbnails(thumbs);

      // Select the first thumbnail by default
      if (thumbs.length > 0) {
        setSelectedThumbnail(thumbs[0]);
      }
    } catch (error) {
      console.error("Error generating thumbnails:", error);
      toast.error("Failed to generate video thumbnails");
    } finally {
      setIsGeneratingThumbnails(false);
    }
  };

  // Handle drag events
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileSelect(e.target.files[0]);
    }
  };

  // Handle click on dropzone
  const handleDropzoneClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle remove file
  const handleRemoveFile = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedFile(null);
    setPreviewUrl(null);
    setThumbnails([]);
    setSelectedThumbnail(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (onMediaRemoved) {
      onMediaRemoved();
    }
  };

  // Handle save to library change
  const handleSaveToLibraryChange = (checked: boolean) => {
    setShouldSaveToLibrary(checked);
    if (onSaveToLibraryChange) {
      onSaveToLibraryChange(checked);
    }
  };

  // Handle thumbnail selection
  const handleThumbnailSelect = (thumbnail: string) => {
    setSelectedThumbnail(thumbnail);

    // Update video poster if video ref exists
    if (videoRef.current) {
      videoRef.current.poster = thumbnail;
    }
  };

  // Handle regenerate thumbnails
  const handleRegenerateThumbnails = () => {
    if (selectedFile && selectedFile.type.startsWith('video/')) {
      generateThumbnails(selectedFile);
    }
  };

  // Handle upload
  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setIsUploading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('saveToLibrary', shouldSaveToLibrary.toString());

      // If we have a selected thumbnail for video, convert it to a file and append
      if (selectedFile.type.startsWith('video/') && selectedThumbnail) {
        // Convert data URL to Blob
        const response = await fetch(selectedThumbnail);
        const blob = await response.blob();
        const thumbnailFile = new File([blob], 'thumbnail.jpg', { type: 'image/jpeg' });
        formData.append('thumbnail', thumbnailFile);
      }

      // Create XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);

          if (response.success) {
            onMediaSelected(
              response.url,
              selectedFile.type,
              response.thumbnailUrl
            );

            // Reset state
            setSelectedFile(null);
            setPreviewUrl(null);
            setThumbnails([]);
            setSelectedThumbnail(null);
            setIsUploading(false);
            if (fileInputRef.current) {
              fileInputRef.current.value = '';
            }
          } else {
            throw new Error(response.message || 'Upload failed');
          }
        } else {
          throw new Error('Upload failed');
        }
      });

      xhr.addEventListener('error', () => {
        throw new Error('Network error');
      });

      xhr.open('POST', '/api/media-library/upload');
      xhr.send(formData);
    } catch (error: any) {
      toast.error(error.message || 'Failed to upload media');
      setIsUploading(false);
    }
  };

  // Handle media selected from library
  const handleMediaSelected = (media: any) => {
    onMediaSelected(
      media.file_path,
      media.file_type,
      media.thumbnail_url
    );
    setIsLibraryModalOpen(false);
  };

  // Clean up URLs on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Upload Media</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsLibraryModalOpen(true)}
        >
          <Library className="h-4 w-4 mr-2" />
          Media Library
        </Button>
      </div>

      {!selectedFile ? (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragging
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-300 hover:border-gray-400 dark:border-gray-700 dark:hover:border-gray-600'
          }`}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={handleDropzoneClick}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInputChange}
            accept={allowedTypes.join(',')}
            className="hidden"
          />

          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
              <Upload className="h-6 w-6 text-gray-500 dark:text-gray-400" />
            </div>

            <div>
              <p className="text-sm font-medium">
                Drag and drop or click to upload
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {allowedTypes.includes('image/*') && allowedTypes.includes('video/*') ? (
                  <>
                    <span className="inline-flex items-center mr-2">
                      <Image className="h-3 w-3 mr-1" /> Images
                    </span>
                    <span className="inline-flex items-center">
                      <Film className="h-3 w-3 mr-1" /> Videos
                    </span>
                  </>
                ) : allowedTypes.includes('image/*') ? (
                  <span className="inline-flex items-center">
                    <Image className="h-3 w-3 mr-1" /> Images only
                  </span>
                ) : allowedTypes.includes('video/*') ? (
                  <span className="inline-flex items-center">
                    <Film className="h-3 w-3 mr-1" /> Videos only
                  </span>
                ) : (
                  `Allowed: ${allowedTypes.join(', ')}`
                )}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Max size: {maxSize}MB
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="border rounded-lg overflow-hidden">
          <div className="relative">
            {previewUrl && (
              <div className="aspect-video w-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                {selectedFile.type.startsWith('image/') ? (
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="max-h-full max-w-full object-contain"
                  />
                ) : selectedFile.type.startsWith('video/') ? (
                  <video
                    ref={videoRef}
                    src={previewUrl}
                    poster={selectedThumbnail || undefined}
                    controls
                    className="max-h-full max-w-full"
                  />
                ) : (
                  <div className="text-center p-4">
                    <p className="text-sm font-medium">{selectedFile.name}</p>
                    <p className="text-xs text-gray-500">{selectedFile.type}</p>
                  </div>
                )}
              </div>
            )}

            <div className="absolute top-2 right-2">
              <Button
                variant="destructive"
                size="icon"
                onClick={handleRemoveFile}
                className="h-8 w-8 rounded-full"
              >
                <X size={16} />
              </Button>
            </div>
          </div>

          {/* Video Thumbnails Section */}
          {selectedFile?.type.startsWith('video/') && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium">Video Thumbnails</h4>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    min="1"
                    max="20"
                    value={thumbnailCount}
                    onChange={(e) => setThumbnailCount(Math.min(20, Math.max(1, parseInt(e.target.value || "6"))))}
                    className="w-16 h-8 px-2 text-sm border rounded"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleRegenerateThumbnails}
                    disabled={isGeneratingThumbnails}
                  >
                    Regenerate
                  </Button>
                </div>
              </div>

              {isGeneratingThumbnails ? (
                <div className="flex space-x-2 overflow-x-auto py-2">
                  {Array.from({ length: thumbnailCount }).map((_, i) => (
                    <Skeleton key={i} className="w-24 h-16 rounded" />
                  ))}
                </div>
              ) : thumbnails.length > 0 ? (
                <ScrollArea className="w-full" orientation="horizontal">
                  <div className="flex space-x-2 py-2">
                    {thumbnails.map((thumbnail, index) => (
                      <div
                        key={index}
                        className={`relative cursor-pointer rounded overflow-hidden transition-all ${
                          selectedThumbnail === thumbnail
                            ? 'ring-2 ring-blue-500 scale-95'
                            : 'hover:ring-1 hover:ring-gray-400'
                        }`}
                        onClick={() => handleThumbnailSelect(thumbnail)}
                      >
                        <img
                          src={thumbnail}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-24 h-16 object-cover"
                        />
                        {selectedThumbnail === thumbnail && (
                          <div className="absolute top-1 right-1 bg-blue-500 rounded-full p-0.5">
                            <Check className="h-3 w-3 text-white" />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center py-4 text-sm text-gray-500">
                  No thumbnails generated yet
                </div>
              )}
            </div>
          )}

          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium truncate">
                {selectedFile?.name}
              </p>
              <p className="text-xs text-gray-500">
                {selectedFile && (selectedFile.size / (1024 * 1024)).toFixed(2)} MB
              </p>
            </div>

            {isUploading ? (
              <div className="space-y-2">
                <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-500 transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-center text-gray-500">
                  {uploadProgress < 100 ? 'Uploading...' : 'Processing...'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="save-to-library"
                    checked={shouldSaveToLibrary}
                    onCheckedChange={(checked) => handleSaveToLibraryChange(!!checked)}
                  />
                  <Label htmlFor="save-to-library">Save to media library</Label>
                </div>

                <Button
                  onClick={handleUpload}
                  className="w-full"
                >
                  Upload
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      <MediaLibraryModal
        isOpen={isLibraryModalOpen}
        onClose={() => setIsLibraryModalOpen(false)}
        onSelectMedia={handleMediaSelected}
      />
    </div>
  );
}
