import { jsxs as i, jsx as r } from "react/jsx-runtime";
import e from "../TokenSell.js";
import s from "../../../assets/svg/IconArrowDown.js";
import d from "../TokenBuy.js";
import { u as l } from "../../../QueryClientProvider-6bcd4331.js";
import n from "../../../store/useStore.js";
import { s as h } from "../../../shallow-27fd7e97.js";
import a from "../TokenLimit.js";
import c from "../TokenDCA.js";
import "react";
import "../../../utils/formatNumber.js";
import "../../../assets/svg/IconChevronDown.js";
import "../../../components/common/TokenImage.js";
import "../../../components/ui/skeleton.js";
import "../../../lib.js";
import "../../../extend-tailwind-merge-e63b2b56.js";
import "../../../lib/utils.js";
import "../../../utils/cardanoUtils.js";
import "../../../index-ca8eb9e1.js";
import "../../../config/axios.js";
import "../../../axios-ddd885c5.js";
import "../../../components/ui/tooltipDialog.js";
import "../../../hooks/useScreen.js";
import "../../../components/ui/dialog.js";
import "../../../index-840f2930.js";
import "../../../index-1c873780.js";
import "../../../index-c7156e07.js";
import "../../../index-563d1ed8.js";
import "../../../index-4914f99c.js";
import "../../../index-67500cd3.js";
import "../../../index-c8f2666b.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../index-27cadef5.js";
import "../../../index-5116e957.js";
import "../../../components/ui/tooltip.js";
import "../../../index-0ce202b9.js";
import "../../../index-bcfeaad9.js";
import "../../../index-f7426637.js";
import "../../../useQuery-febd7967.js";
import "../../../query-013b86c3.js";
import "../../../hooks/useInputShortcuts.js";
import "../../hooks/useSwapAction.js";
import "../../../hooks/useNotify.js";
import "../../../react-toastify.esm-a636d9b1.js";
import "../../../assets/svg/IconCopy.js";
import "../../../assets/svg/IconX.js";
import "../../../assets/svg/IconCheckNotify.js";
import "../../../assets/svg/IconAlertTriangleNotify.js";
import "../../../assets/svg/IconArrowUpRightNotify.js";
import "../tokens.js";
import "../../hooks/useUsdPrices.js";
import "../../../index.esm-fb2f5862.js";
import "../../../IconTilde-bf643edd.js";
import "../../../createReactComponent-ec43b511.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
import "../../../components/ui/slider.js";
import "../../../index-1fe761a6.js";
import "../../../index-6460524a.js";
import "../../../index-bf605d8a.js";
import "../../../components/ui/switchWithText.js";
import "../../../trends/components/PriceFormatter.js";
import "../../../orders/components/Filters/DCAInterval.js";
import "../../../components/ui/dropdown-menu.js";
import "../../../assets/svg/IconCheck.js";
import "../../../createLucideIcon-7a477fa6.js";
import "../../../components/ui/input.js";
const _o = () => {
  const p = l(), { flipTokens: m, orderType: t } = n(
    (o) => ({
      flipTokens: o.swapSlice.flipTokens,
      tokenSell: o.swapSlice.tokenSell,
      tokenBuy: o.swapSlice.tokenBuy,
      orderType: o.swapSlice.orderType
    }),
    h
  );
  return /* @__PURE__ */ i("div", { className: "dhs-flex dhs-flex-col dhs-gap-2.5", children: [
    /* @__PURE__ */ i("div", { className: "dhs-flex dhs-flex-col dhs-items-center dhs-gap-1 @sm/appRoot:dhs-gap-2.5 dhs-relative", children: [
      /* @__PURE__ */ r(e, {}),
      /* @__PURE__ */ r(
        "div",
        {
          className: "dhs-flex dhs-justify-center dhs-items-center dhs-w-10 dhs-h-10 sm:dhs-w-[35px] sm:dhs-h-[35px] dhs-bg-containers dhs-rounded-full dhs-border-4 dhs-border-background dhs-absolute dhs-top-1/2 dhs-left-1/2 dhs-transform -dhs-translate-x-1/2 -dhs-translate-y-1/2 dhs-cursor-pointer",
          onClick: () => {
            t !== "STOP_LOSS" && (m(), p.invalidateQueries({
              predicate: (o) => o.queryKey[0] === "swapDetails"
            }));
          },
          children: /* @__PURE__ */ r(s, { size: 20, className: "dhs-text-subText" })
        }
      ),
      /* @__PURE__ */ r(d, {})
    ] }),
    (t === "LIMIT" || t === "STOP_LOSS") && /* @__PURE__ */ r(a, {}),
    t === "DCA" && /* @__PURE__ */ r(c, {})
  ] });
};
export {
  _o as default
};
