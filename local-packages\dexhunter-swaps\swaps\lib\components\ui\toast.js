import { jsx as I } from "react/jsx-runtime";
import * as O from "react";
import { useState as X, useRef as E, createElement as c, useCallback as q, forwardRef as P, useEffect as A, useMemo as Ce, Fragment as ie } from "react";
import { a as ce, _ as C } from "../../index-1c873780.js";
import { $ as M, r as _e, a as Re } from "../../index-c8f2666b.js";
import { a as Se, c as z, $ as h, b as Ne } from "../../index-c7156e07.js";
import { $ as De } from "../../index-bf605d8a.js";
import { $ as Ae } from "../../index-563d1ed8.js";
import { b as Ie, c as Fe, $ as Le } from "../../index-67500cd3.js";
import { $ as Oe } from "../../index-5116e957.js";
import { a as de } from "../../index-f7426637.js";
import { c as Me } from "../../index-1d6812f7.js";
import { cn as k } from "../../lib.js";
import { X as ke } from "../../x-9e07c78a.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../createLucideIcon-7a477fa6.js";
const ue = "ToastProvider", [re, Ke, Ve] = De("Toast"), [le, Dt] = Ae("Toast", [
  Ve
]), [We, G] = le(ue), pe = (e) => {
  const { __scopeToast: t, label: o = "Notification", duration: r = 5e3, swipeDirection: i = "right", swipeThreshold: l = 50, children: p } = e, [f, b] = X(null), [s, y] = X(0), T = E(!1), K = E(!1);
  return /* @__PURE__ */ c(re.Provider, {
    scope: t
  }, /* @__PURE__ */ c(We, {
    scope: t,
    label: o,
    duration: r,
    swipeDirection: i,
    swipeThreshold: l,
    toastCount: s,
    viewport: f,
    onViewportChange: b,
    onToastAdd: q(
      () => y(
        (S) => S + 1
      ),
      []
    ),
    onToastRemove: q(
      () => y(
        (S) => S - 1
      ),
      []
    ),
    isFocusedToastEscapeKeyDownRef: T,
    isClosePausedRef: K
  }, p));
};
pe.propTypes = {
  label(e) {
    if (e.label && typeof e.label == "string" && !e.label.trim()) {
      const t = `Invalid prop \`label\` supplied to \`${ue}\`. Expected non-empty \`string\`.`;
      return new Error(t);
    }
    return null;
  }
};
const He = "ToastViewport", Xe = [
  "F8"
], te = "toast.viewportPause", oe = "toast.viewportResume", Ue = /* @__PURE__ */ P((e, t) => {
  const { __scopeToast: o, hotkey: r = Xe, label: i = "Notifications ({hotkey})", ...l } = e, p = G(He, o), f = Ke(o), b = E(null), s = E(null), y = E(null), T = E(null), K = ce(t, T, p.onViewportChange), S = r.join("+").replace(/Key/g, "").replace(/Digit/g, ""), N = p.toastCount > 0;
  A(() => {
    const a = (x) => {
      var u;
      r.every(
        ($) => x[$] || x.code === $
      ) && ((u = T.current) === null || u === void 0 || u.focus());
    };
    return document.addEventListener("keydown", a), () => document.removeEventListener("keydown", a);
  }, [
    r
  ]), A(() => {
    const a = b.current, x = T.current;
    if (N && a && x) {
      const u = () => {
        if (!p.isClosePausedRef.current) {
          const w = new CustomEvent(te);
          x.dispatchEvent(w), p.isClosePausedRef.current = !0;
        }
      }, m = () => {
        if (p.isClosePausedRef.current) {
          const w = new CustomEvent(oe);
          x.dispatchEvent(w), p.isClosePausedRef.current = !1;
        }
      }, $ = (w) => {
        !a.contains(w.relatedTarget) && m();
      }, v = () => {
        a.contains(document.activeElement) || m();
      };
      return a.addEventListener("focusin", u), a.addEventListener("focusout", $), a.addEventListener("pointermove", u), a.addEventListener("pointerleave", v), window.addEventListener("blur", u), window.addEventListener("focus", m), () => {
        a.removeEventListener("focusin", u), a.removeEventListener("focusout", $), a.removeEventListener("pointermove", u), a.removeEventListener("pointerleave", v), window.removeEventListener("blur", u), window.removeEventListener("focus", m);
      };
    }
  }, [
    N,
    p.isClosePausedRef
  ]);
  const d = q(({ tabbingDirection: a }) => {
    const u = f().map((m) => {
      const $ = m.ref.current, v = [
        $,
        ...at($)
      ];
      return a === "forwards" ? v : v.reverse();
    });
    return (a === "forwards" ? u.reverse() : u).flat();
  }, [
    f
  ]);
  return A(() => {
    const a = T.current;
    if (a) {
      const x = (u) => {
        const m = u.altKey || u.ctrlKey || u.metaKey;
        if (u.key === "Tab" && !m) {
          const V = document.activeElement, F = u.shiftKey;
          if (u.target === a && F) {
            var v;
            (v = s.current) === null || v === void 0 || v.focus();
            return;
          }
          const D = d({
            tabbingDirection: F ? "backwards" : "forwards"
          }), U = D.findIndex(
            (n) => n === V
          );
          if (ee(D.slice(U + 1)))
            u.preventDefault();
          else {
            var w, _;
            F ? (w = s.current) === null || w === void 0 || w.focus() : (_ = y.current) === null || _ === void 0 || _.focus();
          }
        }
      };
      return a.addEventListener("keydown", x), () => a.removeEventListener("keydown", x);
    }
  }, [
    f,
    d
  ]), /* @__PURE__ */ c(Ie, {
    ref: b,
    role: "region",
    "aria-label": i.replace("{hotkey}", S),
    tabIndex: -1,
    style: {
      pointerEvents: N ? void 0 : "none"
    }
  }, N && /* @__PURE__ */ c(se, {
    ref: s,
    onFocusFromOutsideViewport: () => {
      const a = d({
        tabbingDirection: "forwards"
      });
      ee(a);
    }
  }), /* @__PURE__ */ c(re.Slot, {
    scope: o
  }, /* @__PURE__ */ c(M.ol, C({
    tabIndex: -1
  }, l, {
    ref: K
  }))), N && /* @__PURE__ */ c(se, {
    ref: y,
    onFocusFromOutsideViewport: () => {
      const a = d({
        tabbingDirection: "backwards"
      });
      ee(a);
    }
  }));
}), Ye = "ToastFocusProxy", se = /* @__PURE__ */ P((e, t) => {
  const { __scopeToast: o, onFocusFromOutsideViewport: r, ...i } = e, l = G(Ye, o);
  return /* @__PURE__ */ c(de, C({
    "aria-hidden": !0,
    tabIndex: 0
  }, i, {
    ref: t,
    style: {
      position: "fixed"
    },
    onFocus: (p) => {
      var f;
      const b = p.relatedTarget;
      !((f = l.viewport) !== null && f !== void 0 && f.contains(b)) && r();
    }
  }));
}), J = "Toast", je = "toast.swipeStart", Be = "toast.swipeMove", qe = "toast.swipeCancel", ze = "toast.swipeEnd", Ge = /* @__PURE__ */ P((e, t) => {
  const { forceMount: o, open: r, defaultOpen: i, onOpenChange: l, ...p } = e, [f = !0, b] = Se({
    prop: r,
    defaultProp: i,
    onChange: l
  });
  return /* @__PURE__ */ c(Oe, {
    present: o || f
  }, /* @__PURE__ */ c(fe, C({
    open: f
  }, p, {
    ref: t,
    onClose: () => b(!1),
    onPause: z(e.onPause),
    onResume: z(e.onResume),
    onSwipeStart: h(e.onSwipeStart, (s) => {
      s.currentTarget.setAttribute("data-swipe", "start");
    }),
    onSwipeMove: h(e.onSwipeMove, (s) => {
      const { x: y, y: T } = s.detail.delta;
      s.currentTarget.setAttribute("data-swipe", "move"), s.currentTarget.style.setProperty("--radix-toast-swipe-move-x", `${y}px`), s.currentTarget.style.setProperty("--radix-toast-swipe-move-y", `${T}px`);
    }),
    onSwipeCancel: h(e.onSwipeCancel, (s) => {
      s.currentTarget.setAttribute("data-swipe", "cancel"), s.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"), s.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"), s.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"), s.currentTarget.style.removeProperty("--radix-toast-swipe-end-y");
    }),
    onSwipeEnd: h(e.onSwipeEnd, (s) => {
      const { x: y, y: T } = s.detail.delta;
      s.currentTarget.setAttribute("data-swipe", "end"), s.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"), s.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"), s.currentTarget.style.setProperty("--radix-toast-swipe-end-x", `${y}px`), s.currentTarget.style.setProperty("--radix-toast-swipe-end-y", `${T}px`), b(!1);
    })
  })));
}), [Je, Qe] = le(J, {
  onClose() {
  }
}), fe = /* @__PURE__ */ P((e, t) => {
  const { __scopeToast: o, type: r = "foreground", duration: i, open: l, onClose: p, onEscapeKeyDown: f, onPause: b, onResume: s, onSwipeStart: y, onSwipeMove: T, onSwipeCancel: K, onSwipeEnd: S, ...N } = e, d = G(J, o), [a, x] = X(null), u = ce(
    t,
    (n) => x(n)
  ), m = E(null), $ = E(null), v = i || d.duration, w = E(0), _ = E(v), V = E(0), { onToastAdd: F, onToastRemove: Q } = d, L = z(() => {
    var n;
    (a == null ? void 0 : a.contains(document.activeElement)) && ((n = d.viewport) === null || n === void 0 || n.focus()), p();
  }), D = q((n) => {
    !n || n === 1 / 0 || (window.clearTimeout(V.current), w.current = (/* @__PURE__ */ new Date()).getTime(), V.current = window.setTimeout(L, n));
  }, [
    L
  ]);
  A(() => {
    const n = d.viewport;
    if (n) {
      const g = () => {
        D(_.current), s == null || s();
      }, R = () => {
        const W = (/* @__PURE__ */ new Date()).getTime() - w.current;
        _.current = _.current - W, window.clearTimeout(V.current), b == null || b();
      };
      return n.addEventListener(te, R), n.addEventListener(oe, g), () => {
        n.removeEventListener(te, R), n.removeEventListener(oe, g);
      };
    }
  }, [
    d.viewport,
    v,
    b,
    s,
    D
  ]), A(() => {
    l && !d.isClosePausedRef.current && D(v);
  }, [
    l,
    v,
    d.isClosePausedRef,
    D
  ]), A(() => (F(), () => Q()), [
    F,
    Q
  ]);
  const U = Ce(() => a ? ve(a) : null, [
    a
  ]);
  return d.viewport ? /* @__PURE__ */ c(ie, null, U && /* @__PURE__ */ c(Ze, {
    __scopeToast: o,
    role: "status",
    "aria-live": r === "foreground" ? "assertive" : "polite",
    "aria-atomic": !0
  }, U), /* @__PURE__ */ c(Je, {
    scope: o,
    onClose: L
  }, /* @__PURE__ */ _e.createPortal(/* @__PURE__ */ c(re.ItemSlot, {
    scope: o
  }, /* @__PURE__ */ c(Fe, {
    asChild: !0,
    onEscapeKeyDown: h(f, () => {
      d.isFocusedToastEscapeKeyDownRef.current || L(), d.isFocusedToastEscapeKeyDownRef.current = !1;
    })
  }, /* @__PURE__ */ c(M.li, C({
    // Ensure toasts are announced as status list or status when focused
    role: "status",
    "aria-live": "off",
    "aria-atomic": !0,
    tabIndex: 0,
    "data-state": l ? "open" : "closed",
    "data-swipe-direction": d.swipeDirection
  }, N, {
    ref: u,
    style: {
      userSelect: "none",
      touchAction: "none",
      ...e.style
    },
    onKeyDown: h(e.onKeyDown, (n) => {
      n.key === "Escape" && (f == null || f(n.nativeEvent), n.nativeEvent.defaultPrevented || (d.isFocusedToastEscapeKeyDownRef.current = !0, L()));
    }),
    onPointerDown: h(e.onPointerDown, (n) => {
      n.button === 0 && (m.current = {
        x: n.clientX,
        y: n.clientY
      });
    }),
    onPointerMove: h(e.onPointerMove, (n) => {
      if (!m.current)
        return;
      const g = n.clientX - m.current.x, R = n.clientY - m.current.y, W = !!$.current, H = [
        "left",
        "right"
      ].includes(d.swipeDirection), Y = [
        "left",
        "up"
      ].includes(d.swipeDirection) ? Math.min : Math.max, he = H ? Y(0, g) : 0, Pe = H ? 0 : Y(0, R), Z = n.pointerType === "touch" ? 10 : 2, j = {
        x: he,
        y: Pe
      }, ne = {
        originalEvent: n,
        delta: j
      };
      W ? ($.current = j, B(Be, T, ne, {
        discrete: !1
      })) : ae(j, d.swipeDirection, Z) ? ($.current = j, B(je, y, ne, {
        discrete: !1
      }), n.target.setPointerCapture(n.pointerId)) : (Math.abs(g) > Z || Math.abs(R) > Z) && (m.current = null);
    }),
    onPointerUp: h(e.onPointerUp, (n) => {
      const g = $.current, R = n.target;
      if (R.hasPointerCapture(n.pointerId) && R.releasePointerCapture(n.pointerId), $.current = null, m.current = null, g) {
        const W = n.currentTarget, H = {
          originalEvent: n,
          delta: g
        };
        ae(g, d.swipeDirection, d.swipeThreshold) ? B(ze, S, H, {
          discrete: !0
        }) : B(qe, K, H, {
          discrete: !0
        }), W.addEventListener(
          "click",
          (Y) => Y.preventDefault(),
          {
            once: !0
          }
        );
      }
    })
  })))), d.viewport))) : null;
});
fe.propTypes = {
  type(e) {
    if (e.type && ![
      "foreground",
      "background"
    ].includes(e.type)) {
      const t = `Invalid prop \`type\` supplied to \`${J}\`. Expected \`foreground | background\`.`;
      return new Error(t);
    }
    return null;
  }
};
const Ze = (e) => {
  const { __scopeToast: t, children: o, ...r } = e, i = G(J, t), [l, p] = X(!1), [f, b] = X(!1);
  return nt(
    () => p(!0)
  ), A(() => {
    const s = window.setTimeout(
      () => b(!0),
      1e3
    );
    return () => window.clearTimeout(s);
  }, []), f ? null : /* @__PURE__ */ c(Le, {
    asChild: !0
  }, /* @__PURE__ */ c(de, r, l && /* @__PURE__ */ c(ie, null, i.label, " ", o)));
}, et = /* @__PURE__ */ P((e, t) => {
  const { __scopeToast: o, ...r } = e;
  return /* @__PURE__ */ c(M.div, C({}, r, {
    ref: t
  }));
}), tt = /* @__PURE__ */ P((e, t) => {
  const { __scopeToast: o, ...r } = e;
  return /* @__PURE__ */ c(M.div, C({}, r, {
    ref: t
  }));
}), ot = "ToastAction", be = /* @__PURE__ */ P((e, t) => {
  const { altText: o, ...r } = e;
  return o ? /* @__PURE__ */ c($e, {
    altText: o,
    asChild: !0
  }, /* @__PURE__ */ c(me, C({}, r, {
    ref: t
  }))) : null;
});
be.propTypes = {
  altText(e) {
    return e.altText ? null : new Error(`Missing prop \`altText\` expected on \`${ot}\``);
  }
};
const rt = "ToastClose", me = /* @__PURE__ */ P((e, t) => {
  const { __scopeToast: o, ...r } = e, i = Qe(rt, o);
  return /* @__PURE__ */ c($e, {
    asChild: !0
  }, /* @__PURE__ */ c(M.button, C({
    type: "button"
  }, r, {
    ref: t,
    onClick: h(e.onClick, i.onClose)
  })));
}), $e = /* @__PURE__ */ P((e, t) => {
  const { __scopeToast: o, altText: r, ...i } = e;
  return /* @__PURE__ */ c(M.div, C({
    "data-radix-toast-announce-exclude": "",
    "data-radix-toast-announce-alt": r || void 0
  }, i, {
    ref: t
  }));
});
function ve(e) {
  const t = [];
  return Array.from(e.childNodes).forEach((r) => {
    if (r.nodeType === r.TEXT_NODE && r.textContent && t.push(r.textContent), st(r)) {
      const i = r.ariaHidden || r.hidden || r.style.display === "none", l = r.dataset.radixToastAnnounceExclude === "";
      if (!i)
        if (l) {
          const p = r.dataset.radixToastAnnounceAlt;
          p && t.push(p);
        } else
          t.push(...ve(r));
    }
  }), t;
}
function B(e, t, o, { discrete: r }) {
  const i = o.originalEvent.currentTarget, l = new CustomEvent(e, {
    bubbles: !0,
    cancelable: !0,
    detail: o
  });
  t && i.addEventListener(e, t, {
    once: !0
  }), r ? Re(i, l) : i.dispatchEvent(l);
}
const ae = (e, t, o = 0) => {
  const r = Math.abs(e.x), i = Math.abs(e.y), l = r > i;
  return t === "left" || t === "right" ? l && r > o : !l && i > o;
};
function nt(e = () => {
}) {
  const t = z(e);
  Ne(() => {
    let o = 0, r = 0;
    return o = window.requestAnimationFrame(
      () => r = window.requestAnimationFrame(t)
    ), () => {
      window.cancelAnimationFrame(o), window.cancelAnimationFrame(r);
    };
  }, [
    t
  ]);
}
function st(e) {
  return e.nodeType === e.ELEMENT_NODE;
}
function at(e) {
  const t = [], o = document.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, {
    acceptNode: (r) => {
      const i = r.tagName === "INPUT" && r.type === "hidden";
      return r.disabled || r.hidden || i ? NodeFilter.FILTER_SKIP : r.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;
    }
  });
  for (; o.nextNode(); )
    t.push(o.currentNode);
  return t;
}
function ee(e) {
  const t = document.activeElement;
  return e.some((o) => o === t ? !0 : (o.focus(), document.activeElement !== t));
}
const it = pe, we = Ue, Te = Ge, xe = et, ye = tt, Ee = be, ge = me, At = it, ct = O.forwardRef(({ className: e, ...t }, o) => /* @__PURE__ */ I(
  we,
  {
    ref: o,
    className: k(
      "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",
      e
    ),
    ...t
  }
));
ct.displayName = we.displayName;
const dt = Me(
  "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full rounded-21 bg-gradient-to-r from-gray-#16161D9A to-gray-#3B3D4980 bg-opacity-60 backdrop-blur-[13.591408729553223px] bg-gray-106",
  {
    variants: {
      variant: {
        default: "text-white",
        success: "text-success",
        destructive: "text-destructive"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
), ut = O.forwardRef(({ className: e, variant: t, ...o }, r) => /* @__PURE__ */ I(
  Te,
  {
    ref: r,
    className: k(dt({ variant: t }), e),
    ...o
  }
));
ut.displayName = Te.displayName;
const lt = O.forwardRef(({ className: e, ...t }, o) => /* @__PURE__ */ I(
  Ee,
  {
    ref: o,
    className: k(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",
      e
    ),
    ...t
  }
));
lt.displayName = Ee.displayName;
const pt = O.forwardRef(({ className: e, ...t }, o) => /* @__PURE__ */ I(
  ge,
  {
    ref: o,
    className: k(
      "absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",
      e
    ),
    "toast-close": "",
    ...t,
    children: /* @__PURE__ */ I(ke, { className: "h-4 w-4" })
  }
));
pt.displayName = ge.displayName;
const ft = O.forwardRef(({ className: e, ...t }, o) => /* @__PURE__ */ I(xe, { ref: o, className: k("text-sm font-semibold", e), ...t }));
ft.displayName = xe.displayName;
const bt = O.forwardRef(({ className: e, ...t }, o) => /* @__PURE__ */ I(
  ye,
  {
    ref: o,
    className: k("text-sm opacity-90", e),
    ...t
  }
));
bt.displayName = ye.displayName;
export {
  ut as Toast,
  lt as ToastAction,
  pt as ToastClose,
  bt as ToastDescription,
  At as ToastProvider,
  ft as ToastTitle,
  ct as ToastViewport
};
