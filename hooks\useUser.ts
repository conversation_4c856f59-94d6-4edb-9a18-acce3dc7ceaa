"use client";

import { useAuthToken, useAuthActions } from "@convex-dev/auth/react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { setUserInfo } from "@/redux/slices/userInfoSlice"; // adjust path as needed

/**
 * Hook to get the current authenticated user's details
 * @returns {Object} Object containing authentication state and user data
 * @returns {boolean} isAuthenticated - Whether the user is authenticated
 * @returns {Object} user - Complete user data from the Accounts table
 * @returns {Function} signIn - Function to sign in
 * @returns {Function} signOut - Function to sign out
 * @returns {boolean} isLoading - Whether user data is being loaded
 */
export function useUser() {
  const token = useAuthToken();
  const { signIn, signOut } = useAuthActions();
  const isAuthenticated = Boolean(token);
  const dispatch = useDispatch();

  // Decode the JWT to get the userId (sub)
  const payload = token ? decodeJwt(token) : null;
  let userId = payload?.sub;

  // Workaround: If userId contains a pipe, use only the first part
  if (typeof userId === "string" && userId.includes("|")) {
    userId = userId.split("|")[0];
  }

  // Fetch user details from the database if authenticated
  const user = useQuery(
    api.auth.getUser,
    isAuthenticated && userId ? { userId } : "skip"
  );

  useEffect(() => {
    if (user && isAuthenticated) {
      dispatch(setUserInfo({
        username: user.user_info.account.username,
        profilePhoto: user.user_info.profilePhoto,
        displayName: user.user_info.account.displayName,
        isAuthenticated: isAuthenticated,
        user
      }));
    }
  }, [user, isAuthenticated, dispatch]);

  // Loading state should be true when:
  // 1. We're authenticated but haven't received user data yet (initial load)
  // 2. Or when we're in the process of authenticating
  const isLoading = (isAuthenticated && user === undefined) || (token && !user);

  return {
    isAuthenticated,
    token,
    user,
    signIn,
    signOut,
    isLoading,
  }
}

function decodeJwt(token: string): any {
  try {
    const payload = token.split('.')[1];
    return JSON.parse(atob(payload));
  } catch {
    return null;
  }
}

/**
 * Hook to get user details by username (for profile pages)
 * @param {string | null} username - The username to look up
 * @returns {Object} Object containing user data and loading states
 * @returns {Object} user - Complete user data from the Accounts table
 * @returns {boolean} isLoading - Whether user data is being loaded
 * @returns {boolean} isNotFound - Whether the user was not found
 */
export function useUserByUsername(username: string | null) {
  const user = useQuery(
    api.auth.getUserByUsername,
    username ? { username } : "skip"
  );

  return {
    user,
    isLoading: username !== null && user === undefined,
    isNotFound: username !== null && user === null,
  };
}