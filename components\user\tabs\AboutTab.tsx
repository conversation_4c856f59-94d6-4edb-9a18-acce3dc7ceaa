'use client';

import React, { useState, forwardRef, useRef, useEffect } from 'react';
import { getLanguageName } from '@/public/main';
import { Skeleton } from '@/components/ui/skeleton';
import { getCountryFlagPath } from '@/public/main';
import Tippy from '@tippyjs/react';
import { CollabsCarousel } from '@/components/user/CollabsCarousel';

interface AboutTabProps {
  userData: any;
};

const AboutTab = forwardRef<HTMLParagraphElement, AboutTabProps>(({ userData }, ref) => {
  const accountData = userData?.account || {};
  const [isFlagLoading, setIsFlagLoading] = useState(true);

  // Format height for display
  const formatHeight = (height: { feet?: number; inches?: number; centimeters?: number }) => {
    if (height?.feet || height?.inches) {
      return `${height.feet || 0} ft ${height.inches || 0} in`;
    } else if (height?.centimeters) {
      return `${height.centimeters} cm`;
    }
    return "Not specified";
  };

  // Format weight for display
  const formatWeight = (weight: { pounds?: number; kilograms?: number }) => {
    if (weight?.pounds) {
      return `${weight.pounds} lbs`;
    } else if (weight?.kilograms) {
      return `${weight.kilograms} kg`;
    }
    return "Not specified";
  };

  // Format mixed eye colors for display
  const formatMixedEyeColors = (mixedEyeColors: { left?: string; right?: string }) => {
    if (mixedEyeColors?.left && mixedEyeColors?.right) {
      return `Left: ${mixedEyeColors.left}, Right: ${mixedEyeColors.right}`;
    }
    return "Not specified";
  };

  // Capitalize first letter of each word
  const capitalize = (str: any): string => {
    if (!str || typeof str !== 'string') return 'Not specified';
    return str.replace(/\b\w/g, char => char.toUpperCase());
  };

  // Add this helper function to format tattoo locations
  const formatTattooLocation = (location: string): string => {
    // First split by camel case
    const words = location.replace(/([A-Z])/g, ' $1')
      // Then split the string that might have multiple words
      .split(' ')
      // Filter out empty strings
      .filter(word => word.length > 0)
      // Capitalize each word
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      // Join back with spaces
      .join(' ');

    return words;
  };

  const handleSocialClick = (platform: string) => {
    if (platform.startsWith('twitter')) {
      window.open(`https://twitter.com/${platform.replace('twitter', '')}`, '_blank');
    } else if (platform.startsWith('instagram')) {
      window.open(`https://instagram.com/${platform.replace('instagram', '')}`, '_blank');
    } else if (platform.startsWith('youtube')) {
      window.open(`https://youtube.com/${platform.replace('youtube', '')}`, '_blank');
    } else if (platform.startsWith('tiktok')) {
      window.open(`https://tiktok.com/${platform.replace('tiktok', '')}`, '_blank');
    } else if (platform.startsWith('discord')) {
      window.open(`https://discord.com/${platform.replace('discord', '')}`, '_blank');
    } else if (platform.startsWith('snapchat')) {
      window.open(`https://snapchat.com/${platform.replace('snapchat', '')}`, '_blank');
    } else if (platform.startsWith('whatsapp')) {
      window.open(`https://whatsapp.com/${platform.replace('whatsapp', '')}`, '_blank');
    } else if (platform.startsWith('facebook')) {
      window.open(`https://facebook.com/${platform.replace('facebook', '')}`, '_blank');
    }
    // Add more social media platforms here as needed
  };

  const handleBackupAccountClick = (platform: string, username: string) => {
    if (platform.startsWith('sugar_club')) {
      window.open(`https://sugarclub.com/user/${username}`, '_blank');
    } else if (platform.startsWith('onlyfans')) {
      window.open(`https://onlyfans.com/${username}`, '_blank');
    } else if (platform.startsWith('fansly')) {
      window.open(`https://fansly.com/${username}`, '_blank');
    }
    // Add more backup account platforms here as needed
  };

  // Physical Attributes Grid Data
  const physicalAttributes = accountData.physicalAttributes || {};

  const physicalAttributesData = [
    { label: "Height", value: formatHeight(physicalAttributes.height) },
    { label: "Weight", value: formatWeight(physicalAttributes.weight) },
    { label: "Measurements", value: physicalAttributes.measurements || "Not specified" },
    // Only show regular eye color if mixed eye colors are not specified
    ...(!(physicalAttributes.mixedEyeColors?.left || physicalAttributes.mixedEyeColors?.right) ? [
      { label: "Eye Color", value: physicalAttributes.eyeColor || "Not specified" }
    ] : []),
    // Only show mixed eye colors if they are specified
    ...(physicalAttributes.mixedEyeColors?.left || physicalAttributes.mixedEyeColors?.right ? [
      { label: "Mixed Eye Colors", value: formatMixedEyeColors(physicalAttributes.mixedEyeColors) }
    ] : []),
    { label: "Body Type", value: physicalAttributes.bodyType || "Not specified" },
    { label: "Breast Size", value: physicalAttributes.breastSize || "Not specified" },
    { label: "Breast Type", value: physicalAttributes.breastType || "Not specified" },
    { label: "Hair Color", value: physicalAttributes.hairColor || "Not specified" },
    { label: "Shoe Size", value: physicalAttributes.shoeSize || "Not specified" },
    { label: "Cup Size", value: physicalAttributes.cupSize || "Not specified" },
    { label: "Bra Size", value: physicalAttributes.braSize || "Not specified" },
    { label: "Waist", value: physicalAttributes.waist || "Not specified" },
    { label: "Hips", value: physicalAttributes.hips || "Not specified" },
    { label: "Butt", value: physicalAttributes.butt || "Not specified" },
    { label: "Bust", value: physicalAttributes.bustSize || "Not specified" },
  ];

  // Move these items to their own array (first row)
  const FIRST_ROW_INFO = [
    'gender',
    'ethnicity',
    'dateOfBirth',
    'birthplace',
  ];

  // Move these items to their own array (second row)
  const SECOND_ROW_INFO = [
    'languages',
    'relationshipStatus',
    'niche',
    'interestedIn',
  ];

  // Move these items to their own array (third row)
  const THIRD_ROW_INFO = [
    'height',
    'hairColor',
    'eyeColor',
    'breastSize',
  ];

  // Move these items to their own array (fourth row)
  const FOURTH_ROW_INFO = [
    'waist',
    'hips',
    'butt',
    'shoeSize',
  ];

  // Move these items to their own array (fifth row)
  const FIFTH_ROW_INFO = [
    'bodyType',
    'breastType',
    'piercings',
    'tattoos',
  ];

  // Helper function to get nested value
  const getNestedValue = (obj: any, field: string) => {
    // Check in physicalAttributes
    if (obj.physicalAttributes?.[field] !== undefined) {
      return obj.physicalAttributes[field];
    }
    // Check in preferences
    if (obj.preferences?.[field] !== undefined) {
      return obj.preferences[field];
    }
    // Check in top level and explicitly handle boolean values
    if (typeof obj[field] === 'boolean' || obj[field] !== undefined) {
      return obj[field];
    }
    return null;
  };

  // Create a mapping function to get display properties for each field
  const getFieldDisplay = (field: string, value: any): { label: string; value: any } | null => {
    const displays: { [key: string]: () => { label: string; value: any } | null } = {
      dateOfBirth: () => ({
        label: "Date of Birth",
        value: (() => {
          const date = new Date(value);
          return !isNaN(date.getTime())
            ? date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
            : 'Not specified';
        })()
      }),
      birthplace: () => ({
        label: "Birthplace",
        value: (() => {
          const birthplace = accountData.birthplace || '';
          const flagPath = getCountryFlagPath(birthplace);

          return (
            <span className="flex items-center gap-2">
              {birthplace ? (
                <>
                  {flagPath && (
                    <Skeleton loading={isFlagLoading} width="26px" height="26px" lineHeight={1}>
                      <img
                        src={flagPath}
                        alt={birthplace}
                        onError={() => setIsFlagLoading(false)}
                        onLoad={() => setIsFlagLoading(false)}
                        className="inline-block w-[26px] h-[26px] user-profile-flag"
                      />
                    </Skeleton>
                  )}
                  <span className="capitalize">{birthplace}</span>
                </>
              ) : (
                <span>Not specified</span>
              )}
            </span>
          );
        })()
      }),
      countryOfOrigin: () => ({
        label: "Country of Origin",
        value: value ? capitalize(value) : 'Not specified'
      }),
      languages: () => ({
        label: "Languages",
        value: Array.isArray(value) && value.length > 0
          ? value.map((lang: any) => getLanguageName(lang)).join(", ")
          : 'Not specified'
      }),
      gender: () => ({
        label: "Gender",
        value: value ? capitalize(value) : 'Not specified'
      }),
      ethnicity: () => ({
        label: "Ethnicity",
        value: value ? capitalize(value) : 'Not specified'
      }),
      interestedIn: () => ({
        label: "Interested In",
        value: value ? capitalize(value) : 'Not specified'
      }),
      amazonWishlist: () => ({
        label: "Amazon Wishlist",
        value: <a href={value} target="_blank" rel="noopener noreferrer" className="text-turquoise dark:text-turquoise underline">{value}</a>
      }),
      relationshipStatus: () => ({
        label: "Relationship Status",
        value: value ? capitalize(value) : 'Not specified'
      }),
      niche: () => ({
        label: "Niche",
        value: Array.isArray(value) && value.length > 0
          ? value.map((item: string) => capitalize(item)).join(", ")
          : 'Not specified'
      }),
      piercings: () => ({
        label: "Piercings",
        value: Array.isArray(value) && value.length > 0
          ? value.map((item: string) => capitalize(item)).join(", ")
          : 'Not specified'
      }),
      tattoos: () => ({
        label: "Tattoos",
        value: Array.isArray(value) && value.length > 0
          ? value.map((location: string) => formatTattooLocation(location)).join(", ")
          : 'Not specified'
      }),
      location: () => ({
        label: "Location",
        value: value ? capitalize(value) : 'Not specified'
      }),
      ...physicalAttributesData.reduce((acc, item) => ({ ...acc, [item.label.toLowerCase()]: () => item }), {})
    };

    return displays[field] ? displays[field]() : {
      label: field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1'),
      value: typeof value === 'string' ? capitalize(value) : 'Not specified'
    };
  };

  const memberSince = userData?.memberSince ? new Date(userData.memberSince) : null;

  const getSocialIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case "website":
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="24" height="24" className="fill-white">
          <path d="M57.7 193l9.4 16.4c8.3 14.5 21.9 25.2 38 29.8L163 255.7c17.2 4.9 29 20.6 29 38.5l0 39.9c0 11 6.2 21 16 25.9s16 14.9 16 25.9l0 39c0 15.6 14.9 26.9 29.9 22.6c16.1-4.6 28.6-17.5 32.7-33.8l2.8-11.2c4.2-16.9 15.2-31.4 30.3-40l8.1-4.6c15-8.5 24.2-24.5 24.2-41.7l0-8.3c0-12.7-5.1-24.9-14.1-33.9l-3.9-3.9c-9-9-21.2-14.1-33.9-14.1L257 256c-11.1 0-22.1-2.9-31.8-8.4l-34.5-19.7c-4.3-2.5-7.6-6.5-9.2-11.2c-3.2-9.6 1.1-20 10.2-24.5l5.9-3c6.6-3.3 14.3-3.9 21.3-1.5l23.2 7.7c8.2 2.7 17.2-.4 21.9-7.5c4.7-7 4.2-16.3-1.2-22.8l-13.6-16.3c-10-12-9.9-29.5 .3-41.3l15.7-18.3c8.8-10.3 10.2-25 3.5-36.7l-2.4-4.2c-3.5-.2-6.9-.3-10.4-.3C163.1 48 84.4 108.9 57.7 193zM464 256c0-36.8-9.6-71.4-26.4-101.5L412 164.8c-15.7 6.3-23.8 23.8-18.5 39.8l16.9 50.7c3.5 10.4 12 18.3 22.6 20.9l29.1 7.3c1.2-9 1.8-18.2 1.8-27.5zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z" />
        </svg>;
      case "twitter":
      case "x":
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="24" height="24" className="fill-white">
          <path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z" />
        </svg>;
      case "instagram":
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="24" height="24" className="fill-white">
          <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" />
        </svg>;
      case "youtube":
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" width="24" height="24" className="fill-white">
          <path d="M549.7 124.1c-6.3-23.7-24.8-42.3-48.3-48.6C458.8 64 288 64 288 64S117.2 64 74.6 75.5c-23.5 6.3-42 24.9-48.3 48.6-11.4 42.9-11.4 132.3-11.4 132.3s0 89.4 11.4 132.3c6.3 23.7 24.8 41.5 48.3 47.8C117.2 448 288 448 288 448s170.8 0 213.4-11.5c23.5-6.3 42-24.2 48.3-47.8 11.4-42.9 11.4-132.3 11.4-132.3s0-89.4-11.4-132.3zm-317.5 213.5V175.2l142.7 81.2-142.7 81.2z" />
        </svg>;
      case "tiktok":
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="24" height="24" className="fill-white">
          <path d="M448 209.9a210.1 210.1 0 0 1 -122.8-39.3V349.4A162.6 162.6 0 1 1 185 188.3V278.2a74.6 74.6 0 1 0 52.2 71.2V0l88 0a121.2 121.2 0 0 0 1.9 22.2h0A122.2 122.2 0 0 0 381 102.4a121.4 121.4 0 0 0 67 20.1z" />
        </svg>;
      case "discord":
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" width="24" height="24" className="fill-white">
          <path d="M524.5 69.8a1.5 1.5 0 0 0 -.8-.7A485.1 485.1 0 0 0 404.1 32a1.8 1.8 0 0 0 -1.9 .9 337.5 337.5 0 0 0 -14.9 30.6 447.8 447.8 0 0 0 -134.4 0 309.5 309.5 0 0 0 -15.1-30.6 1.9 1.9 0 0 0 -1.9-.9A483.7 483.7 0 0 0 116.1 69.1a1.7 1.7 0 0 0 -.8 .7C39.1 183.7 18.2 294.7 28.4 404.4a2 2 0 0 0 .8 1.4A487.7 487.7 0 0 0 176 479.9a1.9 1.9 0 0 0 2.1-.7A348.2 348.2 0 0 0 208.1 430.4a1.9 1.9 0 0 0 -1-2.6 321.2 321.2 0 0 1 -45.9-21.9 1.9 1.9 0 0 1 -.2-3.1c3.1-2.3 6.2-4.7 9.1-7.1a1.8 1.8 0 0 1 1.9-.3c96.2 43.9 200.4 43.9 295.5 0a1.8 1.8 0 0 1 1.9 .2c2.9 2.4 6 4.9 9.1 7.2a1.9 1.9 0 0 1 -.2 3.1 301.4 301.4 0 0 1 -45.9 21.8 1.9 1.9 0 0 0 -1 2.6 391.1 391.1 0 0 0 30 48.8 1.9 1.9 0 0 0 2.1 .7A486 486 0 0 0 610.7 405.7a1.9 1.9 0 0 0 .8-1.4C623.7 277.6 590.9 167.5 524.5 69.8zM222.5 337.6c-29 0-52.8-26.6-52.8-59.2S193.1 219.1 222.5 219.1c29.7 0 53.3 26.8 52.8 59.2C275.3 311 251.9 337.6 222.5 337.6zm195.4 0c-29 0-52.8-26.6-52.8-59.2S388.4 219.1 417.9 219.1c29.7 0 53.3 26.8 52.8 59.2C470.7 311 447.5 337.6 417.9 337.6z" />
        </svg>;
      case "snapchat":
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="24" height="24" className="fill-white">
          <path d="M496.9 366.6c-3.4-9.2-9.8-14.1-17.1-18.2-1.4-.8-2.6-1.5-3.7-1.9-2.2-1.1-4.4-2.2-6.6-3.4-22.8-12.1-40.6-27.3-53-45.4a102.9 102.9 0 0 1 -9.1-16.1c-1.1-3-1-4.7-.2-6.3a10.2 10.2 0 0 1 2.9-3c3.9-2.6 8-5.2 10.7-7 4.9-3.2 8.8-5.7 11.2-7.4 9.4-6.5 15.9-13.5 20-21.3a42.4 42.4 0 0 0 2.1-35.2c-6.2-16.3-21.6-26.4-40.3-26.4a55.5 55.5 0 0 0 -11.7 1.2c-1 .2-2.1 .5-3.1 .7 .2-11.2-.1-22.9-1.1-34.5-3.5-40.8-17.8-62.1-32.7-79.2A130.2 130.2 0 0 0 332.1 36.4C309.5 23.5 283.9 17 256 17S202.6 23.5 180 36.4a129.7 129.7 0 0 0 -33.3 26.8c-14.9 17-29.2 38.4-32.7 79.2-1 11.6-1.2 23.4-1.1 34.5-1-.3-2-.5-3.1-.7a55.5 55.5 0 0 0 -11.7-1.2c-18.7 0-34.1 10.1-40.3 26.4a42.4 42.4 0 0 0 2 35.2c4.1 7.8 10.7 14.7 20 21.3 2.5 1.7 6.4 4.2 11.2 7.4 2.6 1.7 6.5 4.2 10.3 6.7a11.1 11.1 0 0 1 3.3 3.3c.8 1.6 .8 3.4-.4 6.6a102 102 0 0 1 -8.9 15.8c-12.1 17.7-29.4 32.6-51.4 44.6C32.4 348.6 20.2 352.8 15.1 366.7c-3.9 10.5-1.3 22.5 8.5 32.6a49.1 49.1 0 0 0 12.4 9.4 134.3 134.3 0 0 0 30.3 12.1 20 20 0 0 1 6.1 2.7c3.6 3.1 3.1 7.9 7.8 14.8a34.5 34.5 0 0 0 9 9.1c10 6.9 21.3 7.4 33.2 7.8 10.8 .4 23 .9 36.9 5.5 5.8 1.9 11.8 5.6 18.7 9.9C194.8 481 217.7 495 256 495s61.3-14.1 78.1-24.4c6.9-4.2 12.9-7.9 18.5-9.8 13.9-4.6 26.2-5.1 36.9-5.5 11.9-.5 23.2-.9 33.2-7.8a34.6 34.6 0 0 0 10.2-11.2c3.4-5.8 3.3-9.9 6.6-12.8a19 19 0 0 1 5.8-2.6A134.9 134.9 0 0 0 476 408.7a48.3 48.3 0 0 0 13-10.2l.1-.1C498.4 388.5 500.7 376.9 496.9 366.6zm-34 18.3c-20.7 11.5-34.5 10.2-45.3 17.1-9.1 5.9-3.7 18.5-10.3 23.1-8.1 5.6-32.2-.4-63.2 9.9-25.6 8.5-42 32.8-88 32.8s-62-24.3-88.1-32.9c-31-10.3-55.1-4.2-63.2-9.9-6.6-4.6-1.2-17.2-10.3-23.1-10.7-6.9-24.5-5.7-45.3-17.1-13.2-7.3-5.7-11.8-1.3-13.9 75.1-36.4 87.1-92.6 87.7-96.7 .6-5 1.4-9-4.2-14.1-5.4-5-29.2-19.7-35.8-24.3-10.9-7.6-15.7-15.3-12.2-24.6 2.5-6.5 8.5-8.9 14.9-8.9a27.6 27.6 0 0 1 6 .7c12 2.6 23.7 8.6 30.4 10.2a10.7 10.7 0 0 0 2.5 .3c3.6 0 4.9-1.8 4.6-5.9-.8-13.1-2.6-38.7-.6-62.6 2.8-32.9 13.4-49.2 26-63.6 6.1-6.9 34.5-37 88.9-37s82.9 29.9 88.9 36.8c12.6 14.4 23.2 30.7 26 63.6 2.1 23.9 .3 49.5-.6 62.6-.3 4.3 1 5.9 4.6 5.9a10.6 10.6 0 0 0 2.5-.3c6.7-1.6 18.4-7.6 30.4-10.2a27.6 27.6 0 0 1 6-.7c6.4 0 12.4 2.5 14.9 8.9 3.5 9.4-1.2 17-12.2 24.6-6.6 4.6-30.4 19.3-35.8 24.3-5.6 5.1-4.8 9.1-4.2 14.1 .5 4.2 12.5 60.4 87.7 96.7C468.6 373 476.1 377.5 462.9 384.9z" />
        </svg>;
      case "whatsapp":
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="24" height="24" className="fill-white">
          <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7 .9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z" />
        </svg>;

      default:
        return null;
    }
  };

  const getBackupAccountIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case "sugar_club":
        return <img src="/images/backup-accounts/sugar-club.webp" alt="Sugar Club" className="w-4 h-4" />;
      case "onlyfans":
        return <img src="/images/backup-accounts/onlyfans.webp" alt="OnlyFans" className="w-4 h-4" />;
      case "fansly":
        return <img src="/images/backup-accounts/fansly.webp" alt="Fansly" className="w-4 h-4" />;

      default:
        return null;
    }
  };

  // Mock online schedule data: 7 days, 24 hours each
  const mockSchedule = [
    // Each array is 24 hours, values are 0-10 (activity level)
    [8, 8, 8, 5, 3, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Sunday
    [2, 2, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Monday
    [1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Tuesday
    [3, 3, 3, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Wednesday
    [4, 4, 4, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Thursday
    [6, 6, 6, 4, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Friday
    [9, 9, 9, 7, 5, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Saturday
  ];
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const [selectedDay, setSelectedDay] = useState(new Date().getDay());
  const now = new Date();
  const currentHour = now.getHours();

  function prevDay() {
    setSelectedDay((d) => (d === 0 ? 6 : d - 1));
  }

  function nextDay() {
    setSelectedDay((d) => (d === 6 ? 0 : d + 1));
  }

  // For bar chart: 24 hours, label every 3 hours
  const hourLabels = [
    '12AM', '', '', '3AM', '', '', '6AM', '', '', '9AM', '', '',
    '12PM', '', '', '3PM', '', '', '6PM', '', '', '9PM', '', ''
  ];

  return (
    <div className="py-6 pt-[11px] text-gray-800 dark:text-gray-200 border border-solid rounded-lg border-gray-300 dark:border-gray-400">

      {/* First Row Info */}
      <div className="px-4 pt-1 py-[15px]">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {FIRST_ROW_INFO.map((field) => {
            const value = getNestedValue(accountData, field);
            if (value === undefined || value === null) return null;
            const displayInfo = getFieldDisplay(field, value);
            if (!displayInfo) return null;

            return (
              <div key={`top-${field}`} className="flex flex-col items-start justify-start">
                <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">
                  {displayInfo.label}:
                </span>
                <span className="text-gorillaGray dark:text-white">
                  {displayInfo.value}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Second Row Info */}
      <div className="bg-whisper-white px-4 py-[15px]">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {SECOND_ROW_INFO.map((field) => {
            const value = getNestedValue(accountData, field);
            if (value === undefined || value === null) return null;
            const displayInfo = getFieldDisplay(field, value);
            if (!displayInfo) return null;

            return (
              <div key={`top-${field}`} className="flex flex-col items-start justify-start">
                <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">
                  {displayInfo.label}:
                </span>
                <span className="text-gorillaGray dark:text-white">
                  {displayInfo.value}
                </span>
              </div>
            );
          })}

          <div className="flex flex-col items-start justify-start">
            <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">
              Niche:
            </span>
            <span className="text-gorillaGray dark:text-white">
              Not Specified
            </span>
          </div>

        </div>
      </div>

      {/* Third Row Info */}
      <div className="px-4 py-[15px]">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {THIRD_ROW_INFO.map((field) => {
            const value = getNestedValue(accountData, field);
            if (value === undefined || value === null) return null;
            const displayInfo = getFieldDisplay(field, value);
            if (!displayInfo) return null;

            return (
              <div key={`top-${field}`} className="flex flex-col items-start justify-start">
                <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">
                  {displayInfo.label}:
                </span>
                <span className="text-gorillaGray dark:text-white">
                  {displayInfo.value}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Fourth Row Info */}
      <div className="bg-whisper-white px-4 py-[15px]">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {FOURTH_ROW_INFO.map((field) => {
            const value = getNestedValue(accountData, field);
            if (value === undefined || value === null) return null;
            const displayInfo = getFieldDisplay(field, value);
            if (!displayInfo) return null;

            return (
              <div key={`top-${field}`} className="flex flex-col items-start justify-start">
                <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">
                  {displayInfo.label}:
                </span>
                <span className="text-gorillaGray dark:text-white">
                  {displayInfo.value}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Fifth Row Info */}
      <div className="px-4 py-[15px]">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {FIFTH_ROW_INFO.map((field) => {
            const value = getNestedValue(accountData, field);
            if (value === undefined || value === null) return null;
            const displayInfo = getFieldDisplay(field, value);
            if (!displayInfo) return null;

            return (
              <div key={`top-${field}`} className="flex flex-col items-start justify-start">
                <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">
                  {displayInfo.label}:
                </span>
                <span className="text-gorillaGray dark:text-white">
                  {displayInfo.value}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Middle Row */}
      <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-center justify-between bg-whisper-white px-4 py-[15px]">
        {/* Member Since */}
        <div className="flex flex-col items-start justify-start text-gray-600 dark:text-gray-400">
          <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">Member Since:</span> <span className="text-gorillaGray dark:text-white">{memberSince instanceof Date ? memberSince.toLocaleDateString() : "Unknown"}</span>
        </div>

        {/* Member ID */}
        <div className="flex flex-col items-start justify-start text-gray-600 dark:text-gray-400">
          <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">Member ID:</span> <span className="text-gorillaGray dark:text-white">{userData?.modelId}</span>
        </div>

        {/* Backups */}
        {userData?.backup_accounts?.length > 0 && (
          <div className="flex flex-col items-start justify-start">
            <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">Backup Accounts:</span>

            <div className="flex flex-wrap items-center gap-2">
              {userData.backup_accounts.map((social: any, idx: number) => {
                const platform = social?.platform?.toLowerCase();
                const username = social?.username;
                return (
                  <Tippy
                    key={idx}
                    content={social.platform.replace('_', ' ')}
                    animation="shift-toward-subtle"
                    placement="top"
                    arrow={true}
                    theme="sugar"
                  >
                    <button
                      type="button"
                      className="backup-account-container group"
                      onClick={() => handleBackupAccountClick(platform, username)}
                    >
                      <div className="backup-account-icons-wrapper">
                        {/* Default icon */}
                        <div className={`backup-account-icon ${platform}`}>
                          {getBackupAccountIcon(social.platform)}
                        </div>
                        {/* Hover icon - duplicate with different styling */}
                        <div className={`backup-account-icon ${platform} hover-state`}>
                          {getBackupAccountIcon(social.platform)}
                        </div>
                      </div>
                    </button>
                  </Tippy>
                );
              })}
            </div>
          </div>
        )}

        {/* Social Media Icons */}
        {userData?.connected_socials?.length > 0 && (
          <div className="flex flex-col items-start justify-start">
            <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">Social Networks:</span>

            <div className="flex flex-wrap items-center gap-2">
              {userData.connected_socials.map((social: any, idx: number) => {
                const platform = social?.platform?.toLowerCase();
                const username = social?.username;
                return (
                  <Tippy
                    key={idx}
                    content={social.platform}
                    animation="shift-toward-subtle"
                    placement="top"
                    arrow={true}
                    theme="sugar"
                  >
                    <button
                      type="button"
                      className="social-container group"
                      onClick={() => handleSocialClick(platform + username)}
                    >
                      <div className="social-icons-wrapper">
                        {/* Default icon */}
                        <div className={`social-icon ${platform}`}>
                          {getSocialIcon(social.platform)}
                        </div>
                        {/* Hover icon - duplicate with different styling */}
                        <div className={`social-icon ${platform} hover-state`}>
                          {getSocialIcon(social.platform)}
                        </div>
                      </div>
                    </button>
                  </Tippy>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Collabs */}
      <div className="w-full flex flex-col items-start justify-start px-4 py-[15px]">
        <div className="flex items-center gap-2 mb-2">
          <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">Collabs:</span>
        </div>
        <CollabsCarousel />
      </div>

      {/* User Online Schedule */}
      <div className="bg-whisper-white w-full pb-2 px-4 py-[15px]">
        <div className="flex items-center justify-between mb-2">
          <span className="font-semibold text-[14px] text-gray-600 dark:text-gray-400">Online Activity:</span>
          <div className="flex items-center gap-2">
            <button onClick={prevDay} className="text-gray-600 dark:text-white hover:text-turquoise text-2xl px-2 pr-0 focus:outline-none">
              &#60;
            </button>
            <span className="text-gray-600 dark:text-white text-base font-medium w-26 text-center">
              {days[selectedDay]}
            </span>
            <button onClick={nextDay} className="text-gray-600 dark:text-white hover:text-turquoise text-2xl px-2 pl-0 focus:outline-none">
              &#62;
            </button>
          </div>
        </div>

        {/* frame for bars + baseline */}
        <div className="relative w-full">
          {/* 1) Bars container: fixed height, 24fr grid, bottom aligned */}
          <div
            className="grid w-full items-end"
            style={{
              gridTemplateColumns: "repeat(24, minmax(0, 1fr))",
              columnGap: "2px",
              height: "8rem",              // same as h-32
            }}
          >
            {mockSchedule[selectedDay].map((val, hour) => {
              const unit = 16;
              const raw = val > 0 ? val * unit : 2;
              return (
                <div key={hour} className="w-full">
                  <div
                    className={`w-full transition-[height] duration-200 ${hour === currentHour && selectedDay === now.getDay()
                      ? "bg-yellow-400"
                      : "bg-gray-400/80"
                      }`}
                    style={{
                      height: `${raw}px`,
                      maxHeight: "125px",
                    }}
                  />
                </div>
              );
            })}
          </div>

          {/* 2) Baseline line (always at bottom of the 8rem container) */}
          <div
            className="absolute left-0 right-0"
            style={{ bottom: 0, borderTop: "1px solid rgba(255,255,255,0.6)" }}
          />
        </div>

        {/* 3) Labels row: same 24‑column grid but no height constraint */}
        <div
          className="grid w-full mt-2"
          style={{
            gridTemplateColumns: "repeat(24, minmax(0, 1fr))",
            columnGap: "2px",
          }}
        >
          {hourLabels.map((label, hour) => (
            <div key={hour} className="text-xs text-gorillaGray dark:text-white text-center">
              {label}
            </div>
          ))}
        </div>
      </div>

    </div>
  );
});

AboutTab.displayName = 'AboutTab';

export default AboutTab;