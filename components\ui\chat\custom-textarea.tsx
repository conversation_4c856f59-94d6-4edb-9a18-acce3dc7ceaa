"use client";

import React, { forwardRef } from "react";
import { cn } from "@/lib/utils";
import { MessageSquare } from "lucide-react";

interface CustomTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  icon?: React.ReactNode;
  containerClassName?: string;
}

const CustomTextarea = forwardRef<HTMLTextAreaElement, CustomTextareaProps>(
  ({ className, icon, containerClassName, placeholder, ...props }, ref) => {
    return (
      <div className={cn("relative w-full", containerClassName)}>
        <textarea
          className={cn(
            "flex min-h-[80px] w-full rounded-lg border border-input bg-transparent px-3 py-3 pl-10 text-sm shadow-sm placeholder:text-transparent focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
            className
          )}
          ref={ref}
          placeholder={placeholder}
          {...props}
        />
        <div className="absolute left-3 top-3 flex items-center text-gorilla-gray dark:text-white pointer-events-none">
          {icon || <MessageSquare size={18} />}
        </div>
        {!props.value && (
          <div className="absolute left-10 top-3 text-gorilla-gray dark:text-white pointer-events-none">
            {placeholder}
          </div>
        )}
      </div>
    );
  }
);

CustomTextarea.displayName = "CustomTextarea";

export { CustomTextarea };