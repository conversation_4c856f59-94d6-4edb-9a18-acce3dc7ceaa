services:
  liquid-auth:
    # TODO: Pin the version of the image to a specific tag that is compatible with the current client
    image: ghcr.io/algorandfoundation/liquid-auth:develop
    restart: no
    env_file:
      - .env.docker
    ports:
      - "3000:3000"
    depends_on:
      - redis
      - mongo
  redis:
    image: redis
    restart: always
    ports:
      - "6379:6379"
  mongo:
    image: mongo:7.0
    restart: no
    environment:
      - MONGO_INITDB_DATABASE=${DB_NAME:-fido}
      - MONGO_INITDB_ROOT_USERNAME=${DB_USERNAME:-algorand}
      - MONGO_INITDB_ROOT_PASSWORD=${DB_PASSWORD:-algorand}
    ports:
      - "27017:27017"
    volumes:
      - mongo:/data/db
  turn:
    image: coturn/coturn
    restart: no
    deploy:
      replicas: 0
    depends_on:
      - mongo
    ports:
      - "3478:3478"
      - "3478:3478/udp"
      - "5349:5349"
      - "5349:5349/udp"
    command:
      - "--no-auth"
      - "--mongo-userdb"
      - "mongodb://${DB_USERNAME:-algorand}:${DB_PASSWORD:-algorand}@mongo:27017/${DB_NAME:-coturn}?authSource=admin&retryWrites=true&w=majority"
      - "--redis-userdb"
      - "ip=redis dbname=0"
volumes:
  mongo:
