import { jsx as C } from "react/jsx-runtime";
import { memo as t } from "react";
const e = (o) => /* @__PURE__ */ C(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 12 9",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M11.7256 1.68814C12.1056 1.28742 12.0889 0.654476 11.6881 0.274425C11.2874 -0.105626 10.6545 -0.0888669 10.2744 0.311857L11.7256 1.68814ZM3.63567 7.31189C3.25564 7.71263 3.27242 8.34557 3.67317 8.72561C4.07391 9.10564 4.70685 9.08885 5.08688 8.68811L3.63567 7.31189ZM1.72626 3.76138C1.34661 3.36027 0.713687 3.34288 0.312583 3.72253C-0.0885209 4.10218 -0.105913 4.73511 0.273737 5.13621L1.72626 3.76138ZM3.62752 8.67951C4.00717 9.08062 4.64009 9.09801 5.0412 8.71836C5.4423 8.33871 5.45969 7.70578 5.08004 7.30468L3.62752 8.67951ZM11 1C10.2744 0.311857 10.2744 0.311877 10.2744 0.311918C10.2743 0.311958 10.2743 0.312018 10.2742 0.312099C10.274 0.312259 10.2738 0.3125 10.2735 0.31282C10.2729 0.313461 10.272 0.31442 10.2708 0.315693C10.2684 0.318239 10.2648 0.322041 10.26 0.327065C10.2505 0.337114 10.2363 0.352051 10.2178 0.371608C10.1807 0.410722 10.126 0.468313 10.0559 0.542219C9.91576 0.690032 9.71368 0.90311 9.46609 1.16417C8.97091 1.68629 8.29371 2.40033 7.56563 3.16802C6.10947 4.7034 4.44978 6.45342 3.63567 7.31189L5.08688 8.68811C5.90096 7.82967 7.56063 6.07969 9.01679 4.5443C9.74487 3.77661 10.4221 3.06257 10.9172 2.54045C11.1648 2.27939 11.3669 2.06632 11.5071 1.9185C11.5772 1.8446 11.6318 1.78701 11.6689 1.74789C11.6875 1.72834 11.7016 1.7134 11.7112 1.70335C11.7159 1.69833 11.7195 1.69452 11.7219 1.69198C11.7231 1.69071 11.7241 1.68975 11.7247 1.68911C11.725 1.68879 11.7252 1.68854 11.7253 1.68838C11.7254 1.6883 11.7255 1.68824 11.7255 1.6882C11.7256 1.68816 11.7256 1.68814 11 1ZM0.273737 5.13621L3.62752 8.67951L5.08004 7.30468L1.72626 3.76138L0.273737 5.13621Z",
        fill: "currentColor"
      }
    )
  }
), i = t(e);
export {
  i as default
};
