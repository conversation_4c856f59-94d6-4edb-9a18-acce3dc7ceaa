import React, { useState, useRef, useEffect } from 'react';
import { LabelInputContainer } from '@/components/ui/label-input-container';
import { Label } from '@/components/ui/label';
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import { AccountFormData } from './AccountTab';

interface AccountInfoSectionProps {
  formData: AccountFormData;
  setFormData: React.Dispatch<React.SetStateAction<AccountFormData>>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handlePhoneChange: (value: string) => void;
  usernameStatus: 'checking' | 'available' | 'unavailable' | 'unchanged';
  usernameError: string | null;
  emailError: string | null;
}

const AccountInfoSection: React.FC<AccountInfoSectionProps> = ({
  formData,
  handleInputChange,
  handlePhoneChange,
  usernameStatus,
  usernameError,
  emailError,
}) => {
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === ' ') e.preventDefault();
  };

  return (
    <div className="space-y-6">
      {/* Account Info Section */}
      <LabelInputContainer className="mb-6">
        <h3 className="text-xl font-semibold text-[#121212] dark:text-white mb-1">Account Info</h3>
        <div className="mb-6 border border-solid border-b border-l-0 border-r-0 border-t-0 border-black/60 dark:border-white/60"></div>
      </LabelInputContainer>

      {/* Username */}
      <LabelInputContainer>
        <Label htmlFor="username">Username</Label>
        <input
          id="username"
          name="username"
          placeholder="Enter your username"
          value={formData.username}
          onChange={handleInputChange}
          pattern="[a-zA-Z0-9_]+"
          title="Username can only contain letters, numbers, and underscores"
          onKeyPress={handleKeyPress}
          className={`flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400`}
        />
        {usernameStatus === 'checking' && (
          <p className="text-gray-500 text-sm mt-1">Checking availability...</p>
        )}
        {usernameStatus === 'available' && (
          <p className="text-green-500 text-sm mt-1">Username is available</p>
        )}
        {usernameStatus === 'unavailable' && (
          <p className="text-red-500 text-sm mt-1">Username is already taken</p>
        )}
        {usernameError && <p className="text-red-500 text-sm mt-1">{usernameError}</p>}
      </LabelInputContainer>

      {/* Email */}
      <LabelInputContainer>
        <Label htmlFor="email">Email</Label>
        <input
          id="email"
          name="email"
          placeholder="Enter your email address"
          value={formData.email}
          onChange={handleInputChange}
          type="email"
          className={`flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400 ${emailError ? 'border-red-500' : ''}`}
        />
        {emailError && <p className="text-red-500 text-sm mt-1">{emailError}</p>}
      </LabelInputContainer>

      {/* Phone Number */}
      <LabelInputContainer>
        <Label htmlFor="phoneNumber">Phone Number</Label>
        <PhoneInput
          defaultCountry="us"
          value={formData.phoneNumber}
          onChange={handlePhoneChange}
          inputClassName="w-full !p-2 !flex !h-10 !border-none !bg-gray-300 dark:!bg-zinc-800 !text-[#121212] dark:!text-white !shadow-input !rounded-md !px-3 !py-2 !text-sm file:!border-0 file:!bg-transparent file:!text-sm file:!font-medium placeholder:!text-neutral-400 dark:!placeholder-text-neutral-600 focus-visible:!outline-none focus-visible:!ring-[2px] focus-visible:!ring-neutral-400 dark:focus-visible:!ring-neutral-600 disabled:!cursor-not-allowed disabled:!opacity-50 dark:!shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:!shadow-none !transition duration-400 !rounded-tl-none !rounded-bl-none"
          className="w-full"
          countrySelectorStyleProps={{
            buttonClassName: "!p-2 !flex !h-10 !border-none !bg-gray-300 dark:!bg-zinc-800 !text-[#121212] dark:!text-white !shadow-input !rounded-md !px-3 !py-2 !text-sm file:!border-0 file:!bg-transparent file:!text-sm file:!font-medium placeholder:!text-neutral-400 dark:!placeholder-text-neutral-600 focus-visible:!outline-none focus-visible:!ring-[2px] focus-visible:!ring-neutral-400 dark:focus-visible:!ring-neutral-600 disabled:!cursor-not-allowed disabled:!opacity-50 dark:!shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:!shadow-none !transition duration-400 !rounded-tr-none !rounded-br-none",
            dropdownStyleProps: {
              listItemClassName: "hover:!bg-zinc-200 dark:hover:!bg-[#121212] !z-[4]",
              className: "!bg-white dark:!bg-zinc-800 !border !border-[#121212]/40 dark:!border-white/40 !z-[4] !text-[#121212] dark:!text-white",
            },
          }}
        />
      </LabelInputContainer>
    </div>
  );
};
export default AccountInfoSection;