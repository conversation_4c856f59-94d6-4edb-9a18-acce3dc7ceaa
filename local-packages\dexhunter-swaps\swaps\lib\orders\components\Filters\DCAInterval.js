import { jsxs as f, jsx as o } from "react/jsx-runtime";
import { memo as x, useState as i, useEffect as g, useCallback as y } from "react";
import { cn as n } from "../../../lib/utils.js";
import { DropdownMenu as w, DropdownMenuTrigger as T, DropdownMenuContent as D, DropdownMenuItem as M } from "../../../components/ui/dropdown-menu.js";
import k from "../../../store/useStore.js";
import "../../../extend-tailwind-merge-e63b2b56.js";
import "../../../index-1c873780.js";
import "../../../index-c7156e07.js";
import "../../../index-563d1ed8.js";
import "../../../index-c8f2666b.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../index-bf605d8a.js";
import "../../../index-1fe761a6.js";
import "../../../index-67500cd3.js";
import "../../../index-27cadef5.js";
import "../../../index-4914f99c.js";
import "../../../index-0ce202b9.js";
import "../../../index-bcfeaad9.js";
import "../../../index-5116e957.js";
import "../../../lib.js";
import "../../../assets/svg/IconCheck.js";
import "../../../createLucideIcon-7a477fa6.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../../../swap/components/tokens.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
const r = [
  {
    title: "Minute",
    value: "minutely",
    pluralTitle: "Minutes",
    seconds: 60
  },
  { title: "Hour", value: "hourly", pluralTitle: "Hours", seconds: 3600 },
  { title: "Day", value: "daily", pluralTitle: "Days", seconds: 86400 },
  { title: "Week", value: "weekly", pluralTitle: "Weeks", seconds: 604800 },
  { title: "Month", value: "monthly", pluralTitle: "Months", seconds: 2592e3 }
], C = ({
  children: p,
  onSetSelected: m,
  buyAmount: s,
  className: a
}) => {
  const [d, u] = i(!1), [l, h] = i("daily"), { setIntervalLength: c } = k((t) => t.swapSlice);
  g(() => {
    const t = r.find(
      (e) => e.value === l
    );
    c((t == null ? void 0 : t.value) === "minutely" ? 5 : 1), m(t == null ? void 0 : t.value);
  }, [l]);
  const v = y(
    (t) => s > 1 ? t.pluralTitle : t.title,
    [s]
  );
  return /* @__PURE__ */ f(w, { open: d, onOpenChange: u, children: [
    /* @__PURE__ */ o(T, { asChild: !0, className: n("dhs-", a), children: p }),
    /* @__PURE__ */ o(D, { className: "dhs-overflow-auto dhs-font-proximaSemiBold dhs-max-h-[200px] dhs-text-mainText dhs-w-[200px]  @md/appRoot:dhs-w-[150px]", children: r.map((t, e) => /* @__PURE__ */ o(
      M,
      {
        className: n(
          "dhs-gap-2 dhs-cursor-pointer dhs-rounded-none dhs-hover:bg-gray-106",
          e === 0 && "dhs-rounded-t-md",
          e === Object.keys(r).length - 1 && "dhs-rounded-b-md"
        ),
        onSelect: () => h(t.value),
        children: /* @__PURE__ */ o("div", { className: "dhs-flex dhs-text-xs dhs-tracking-normal dhs-items-center", children: v(t) })
      },
      t.title
    )) })
  ] });
}, it = x(C);
export {
  it as default,
  r as intervalOptions
};
