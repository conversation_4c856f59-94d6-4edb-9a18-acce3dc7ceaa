import { useQuery } from '@tanstack/react-query';
import { useWallet } from '@txnlab/use-wallet-react';
import { useEffect, useState } from 'react';
import algosdk from 'algosdk';
import { formatAlgoPrice } from '@/public/main';

export default function useWalletBalance() {
  const [walletBalance, setWalletBalance] = useState(null);
  const [walletAvailableBalance, setWalletAvailableBalance] = useState(null);

  const { activeAccount } = useWallet();

  const algodClient = new algosdk.Algodv2('', 'https://mainnet-api.algonode.cloud');

  const getAccountInfo = async () => {
    if (!activeAccount) throw new Error('No selected account.');
    const accountInfo = await algodClient.accountInformation(activeAccount.address).do();

    return accountInfo;
  };

  const { data: accountInfo } = useQuery({
    queryKey: ['balance', activeAccount?.address],
    queryFn: getAccountInfo,
    enabled: !!activeAccount?.address,
    refetchInterval: 30000,
  });

  useEffect(() => {
    if (
      accountInfo &&
      accountInfo.amount !== undefined &&
      accountInfo['min-balance'] !== undefined
    ) {
      const balance = formatAlgoPrice(accountInfo.amount, false, { minimumFractionDigits: 6 });
      const availableBalance = formatAlgoPrice(accountInfo.amount - accountInfo['min-balance'], false, {
        minimumFractionDigits: 6,
      });

      if (balance !== walletBalance) {
        setWalletBalance(balance);
        return;
      }

      if (parseFloat(availableBalance) < 0) {
        setWalletAvailableBalance('0.000000');
        return;
      }

      if (availableBalance !== walletAvailableBalance) {
        setWalletAvailableBalance(availableBalance);
        return;
      }
    } else {
      setWalletBalance('0.000000');
      setWalletAvailableBalance('0.000000');
    }
  }, [accountInfo, walletBalance, walletAvailableBalance]);

  return {
    walletBalance,
    walletAvailableBalance,
  };
}