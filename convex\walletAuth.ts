import { mutation, query, internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Types
export interface NonceDoc {
  _id: Id<"Nonces">;
  _creationTime: number;
  address: string;
  blockchain: string;
  nonce: string;
  expiresAt: number;
}

export interface WalletUser {
  _id: Id<"Accounts">;
  _creationTime: number;
  user_id: string;
  email: string | null;
  password: string | null;
  wallet_address: string | null;
  blockchain: string | null;
  account_type: string | null;
  registration_date: string | null;
  user_info: {
    account: {
      username: string;
      displayName: string;
      profilePhoto: string;
    };
    email: string | null;
    emailVerified: string | null;
  };
  created_at: number;
  updated_at: number;
}

// Internal query to get nonce
export const getNonce = internalQuery({
  args: { address: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("Nonces")
      .withIndex("by_address", (q) => q.eq("address", args.address))
      .first();
  },
});

// Internal mutation to delete nonce
export const deleteNonce = internalMutation({
  args: { id: v.id("Nonces") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.id);
  },
});

// Generate nonce for wallet authentication
export const generateNonce = mutation({
  args: {
    address: v.string(),
    blockchain: v.string(),
  },
  handler: async (ctx, args) => {
    // Generate new nonce
    const nonce = Math.random().toString(36).substring(2, 15);
    const expiresAt = Date.now() + 15 * 60 * 1000; // 15 minutes expiration

    // Delete any existing nonce for this address
    const existing = await ctx.db
      .query("Nonces")
      .withIndex("by_address", (q) => q.eq("address", args.address))
      .first();

    if (existing) {
      await ctx.db.delete(existing._id);
    }

    // Store the new nonce
    await ctx.db.insert("Nonces", {
      address: args.address,
      blockchain: args.blockchain,
      nonce,
      expiresAt,
    });
    return { nonce, expiresAt };
  },
});

// Internal query to find user by wallet
export const findUserByWallet = internalQuery({
  args: {
    address: v.string(),
    blockchain: v.string()
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("Accounts")
      .withIndex("by_wallet", (q) =>
        q.and(
          q.eq("wallet_address", args.address),
          q.eq("blockchain", args.blockchain)
        )
      )
      .first();
  },
});

// Internal mutation to create user
export const createUser = internalMutation({
  args: {
    wallet_address: v.string(),
    blockchain: v.string(),
    account_type: v.string(),
    user_info: v.any(),
    created_at: v.number(),
    updated_at: v.number(),
  },
  handler: async (ctx, args) => {
    const newUser = {
      user_id: crypto.randomUUID(),
      email: null,
      password: null,
      wallet_address: args.wallet_address,
      blockchain: args.blockchain,
      account_type: args.account_type,
      registration_date: new Date().toISOString(),
      user_info: args.user_info,
      created_at: args.created_at,
      updated_at: args.updated_at,
    };
    return await ctx.db.insert("Accounts", newUser);
  },
});