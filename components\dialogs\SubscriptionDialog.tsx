'use client';

import { useState } from 'react';
import { X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Skeleton } from '@/components/ui/skeleton';
import Tippy from '@tippyjs/react';

interface SubscriptionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    username: string;
    displayName?: string;
    profilePhoto?: string;
    isVerified?: boolean;
    subscriptionPrice?: number;
    renewalPrice?: number;
    renewalDate?: string;
    coverBanner?: string;
    coverBannerType?: 'image' | 'video' | null;
  };
}

const SubscriptionDialog = ({ isOpen, onClose, userData }: SubscriptionDialogProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingBanner, setLoadingBanner] = useState(true);
  const [isVerifiedBadgeLoading, setIsVerifiedBadgeLoading] = useState(true);

  const handleSubscribe = async () => {
    setIsLoading(true);
    try {
      setTimeout(() => {
        setIsLoading(false);
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Subscription failed:', error);
      setIsLoading(false);
    }
  };

  const subscriptionPrice = userData?.subscriptionPrice || 8.88;
  const renewalPrice = userData?.renewalPrice || 8.88;
  const renewalDate = userData?.renewalDate || '10/06/25';

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="2xl"
      closeButton={false}
      header={null}
      footer={null}
    >
      <div className="bg-white dark:bg-zinc-900 text-black dark:text-white rounded-lg overflow-hidden">
        {/* Banner Image */}
        <div className="relative h-40 overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            {userData.coverBannerType === 'image' ? (
              <Skeleton loading={loadingBanner} width="100%" height="100%">
                <img
                  src={userData.coverBanner}
                  alt="Subscription Banner"
                  className="w-full h-full object-cover"
                  onLoadedData={() => setLoadingBanner(false)}
                  onError={() => setLoadingBanner(false)}
                />
              </Skeleton>
            ) : (
              <Skeleton loading={loadingBanner} width="100%" height="100%">
                <video autoPlay loop muted className="w-full h-full object-cover" onLoadedData={() => setLoadingBanner(false)} onError={() => setLoadingBanner(false)}>
                  <source src={userData.coverBanner} type="video/mp4" />
                </video>
              </Skeleton>
            )}
          </div>
          <button
            onClick={onClose}
            className="absolute top-2 right-2 p-1 rounded-full bg-black/40 text-white hover:bg-black/60"
          >
            <X size={20} />
          </button>
        </div>

        {/* User Info */}
        <div className="px-6 py-4 flex items-center -mt-14 relative z-[1]">
          <Avatar className="h-[101px] w-[101px] border-solid border-2 border-white">
            <AvatarImage src={userData?.profilePhoto} alt={userData?.displayName || userData?.username} />
            <AvatarFallback>{userData?.username?.substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div className="ml-3 mt-10">
            <div className="flex items-center gap-1">
              <h2 className="text-xl font-bold text-black dark:text-white">{userData?.displayName || userData?.username}</h2>
              {userData?.isVerified && (
                <Tippy content="Verified User" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                  <Skeleton loading={isVerifiedBadgeLoading} width="25px" height="25px">
                    <img className="w-[25px] h-[25px] user-profile-verified-badge" alt="Verified User" src="/images/user/verified.png" onError={() => setIsVerifiedBadgeLoading(false)} onLoad={() => setIsVerifiedBadgeLoading(false)} />
                  </Skeleton>
                </Tippy>
              )}
            </div>
            <p className="text-gray-500 dark:text-gray-400">@{userData?.username}</p>
          </div>
        </div>

        {/* Benefits */}
        <div className="px-6 py-4 bg-gray-100 dark:bg-zinc-800 rounded-lg mx-4 mb-4">
          <ul className="space-y-4">
            <li className="flex items-start">
              <Check className="mr-2 h-6 w-6 text-white bg-turquoise rounded-full p-1 flex-shrink-0 mt-0.5" />
              <span className="text-lg text-black dark:text-white">Get full access to my exclusive content</span>
            </li>
            <li className="flex items-start">
              <Check className="mr-2 h-6 w-6 text-white bg-turquoise rounded-full p-1 flex-shrink-0 mt-0.5" />
              <span className="text-lg text-black dark:text-white">Message me directly anytime you'd like</span>
            </li>
            <li className="flex items-start">
              <Check className="mr-2 h-6 w-6 text-white bg-turquoise rounded-full p-1 flex-shrink-0 mt-0.5" />
              <span className="text-lg text-black dark:text-white">Cancel any time, no strings attached!</span>
            </li>
          </ul>
        </div>

        {/* Pricing */}
        <div className="px-6 py-4 bg-white dark:bg-zinc-800 text-black dark:text-white rounded-lg mx-4 mb-4 border border-solid border-gray-300 dark:border-white/50">
          <div className="text-2xl font-bold">
            €{subscriptionPrice.toFixed(2)}/MONTH
          </div>
          <div className="text-gray-500 dark:text-gray-400">
            Renews for €{renewalPrice.toFixed(2)}/month on {renewalDate}. Risk free, cancel anytime.
          </div>
        </div>

        {/* Subscribe Button */}
        <div className="px-4 pb-4">
          <Button
            onClick={handleSubscribe}
            disabled={isLoading}
            className="w-full py-6 text-xl font-bold bg-turquoise hover:bg-cyan-600 text-white rounded-full"
          >
            <div className="flex items-center justify-between w-full px-4">
              <span>Join now</span>
              <div className="flex items-center">
                {renewalPrice > subscriptionPrice && <span className="line-through mr-2">€{renewalPrice.toFixed(2)}</span>}
                <span>€{subscriptionPrice.toFixed(2)}/month</span>
              </div>
            </div>
          </Button>
        </div>
      </div>
    </AnimatedModal>
  );
};
export default SubscriptionDialog;