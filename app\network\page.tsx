import { api } from '@/convex/_generated/api';
import { fetchQuery } from 'convex/nextjs'; // or your Convex SSR fetch helper
import NetworkClient from '@/components/network/NetworkClient';
import { NetworkProfile } from '@/types/network';

export default async function NetworkPage() {
  // Fetch creators from Convex
  const { creators, pagination } = await fetchQuery(
    api.users.fetchNetworkCreators,
    { page: 1, limit: 52, sortBy: 'recently-joined' }
  );

  // Format the creators data
  const initialProfiles: NetworkProfile[] = creators.map((creator: any) => ({
    id: creator.id,
    username: creator.username,
    displayName: creator.displayName,
    profilePhoto: creator.profilePhoto,
    niche: creator.niche,
    location: creator.location,
    latitude: creator.latitude,
    longitude: creator.longitude,
    followers: creator.followers,
    posts: creator.posts,
    subscribed: false,
    physicalAttributes: creator.physicalAttributes,
    ethnicity: creator.ethnicity,
    dateOfBirth: creator.dateOfBirth,
    coverBanner: creator.coverBanner,
    coverBannerType: creator.coverBannerType,
    isVerified: creator.isVerified,
    accountType: creator.accountType,
  }));

  return (
    <NetworkClient
      initialProfiles={initialProfiles}
      totalProfiles={pagination?.total || initialProfiles.length}
    />
  );
};