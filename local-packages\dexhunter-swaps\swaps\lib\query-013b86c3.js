var x = (t, e, s) => {
  if (!e.has(t))
    throw TypeError("Cannot " + s);
};
var n = (t, e, s) => (x(t, e, "read from private field"), s ? s.call(t) : e.get(t)), b = (t, e, s) => {
  if (e.has(t))
    throw TypeError("Cannot add the same private member more than once");
  e instanceof WeakSet ? e.add(t) : e.set(t, s);
}, c = (t, e, s, r) => (x(t, e, "write to private field"), r ? r.call(t, s) : e.set(t, s), s);
var E = (t, e, s) => (x(t, e, "access private method"), s);
var Z = class {
  constructor() {
    this.listeners = /* @__PURE__ */ new Set(), this.subscribe = this.subscribe.bind(this);
  }
  subscribe(t) {
    return this.listeners.add(t), this.onSubscribe(), () => {
      this.listeners.delete(t), this.onUnsubscribe();
    };
  }
  hasListeners() {
    return this.listeners.size > 0;
  }
  onSubscribe() {
  }
  onUnsubscribe() {
  }
}, I = typeof window > "u" || "Deno" in globalThis;
function V() {
}
function Re(t, e) {
  return typeof t == "function" ? t(e) : t;
}
function ue(t) {
  return typeof t == "number" && t >= 0 && t !== 1 / 0;
}
function oe(t, e) {
  return Math.max(t + (e || 0) - Date.now(), 0);
}
function qe(t, e) {
  return typeof t == "function" ? t(e) : t;
}
function he(t, e) {
  return typeof t == "function" ? t(e) : t;
}
function Ue(t, e) {
  const {
    type: s = "all",
    exact: r,
    fetchStatus: i,
    predicate: o,
    queryKey: l,
    stale: a
  } = t;
  if (l) {
    if (r) {
      if (e.queryHash !== ce(l, e.options))
        return !1;
    } else if (!z(e.queryKey, l))
      return !1;
  }
  if (s !== "all") {
    const f = e.isActive();
    if (s === "active" && !f || s === "inactive" && f)
      return !1;
  }
  return !(typeof a == "boolean" && e.isStale() !== a || i && i !== e.state.fetchStatus || o && !o(e));
}
function De(t, e) {
  const { exact: s, status: r, predicate: i, mutationKey: o } = t;
  if (o) {
    if (!e.options.mutationKey)
      return !1;
    if (s) {
      if (H(e.options.mutationKey) !== H(o))
        return !1;
    } else if (!z(e.options.mutationKey, o))
      return !1;
  }
  return !(r && e.state.status !== r || i && !i(e));
}
function ce(t, e) {
  return ((e == null ? void 0 : e.queryKeyHashFn) || H)(t);
}
function H(t) {
  return JSON.stringify(
    t,
    (e, s) => N(s) ? Object.keys(s).sort().reduce((r, i) => (r[i] = s[i], r), {}) : s
  );
}
function z(t, e) {
  return t === e ? !0 : typeof t != typeof e ? !1 : t && e && typeof t == "object" && typeof e == "object" ? !Object.keys(e).some((s) => !z(t[s], e[s])) : !1;
}
function ee(t, e) {
  if (t === e)
    return t;
  const s = $(t) && $(e);
  if (s || N(t) && N(e)) {
    const r = s ? t : Object.keys(t), i = r.length, o = s ? e : Object.keys(e), l = o.length, a = s ? [] : {};
    let f = 0;
    for (let w = 0; w < l; w++) {
      const y = s ? w : o[w];
      (!s && r.includes(y) || s) && t[y] === void 0 && e[y] === void 0 ? (a[y] = void 0, f++) : (a[y] = ee(t[y], e[y]), a[y] === t[y] && t[y] !== void 0 && f++);
    }
    return i === l && f === i ? t : a;
  }
  return e;
}
function Pe(t, e) {
  if (!e || Object.keys(t).length !== Object.keys(e).length)
    return !1;
  for (const s in t)
    if (t[s] !== e[s])
      return !1;
  return !0;
}
function $(t) {
  return Array.isArray(t) && t.length === Object.keys(t).length;
}
function N(t) {
  if (!J(t))
    return !1;
  const e = t.constructor;
  if (e === void 0)
    return !0;
  const s = e.prototype;
  return !(!J(s) || !s.hasOwnProperty("isPrototypeOf") || Object.getPrototypeOf(t) !== Object.prototype);
}
function J(t) {
  return Object.prototype.toString.call(t) === "[object Object]";
}
function le(t) {
  return new Promise((e) => {
    setTimeout(e, t);
  });
}
function fe(t, e, s) {
  return typeof s.structuralSharing == "function" ? s.structuralSharing(t, e) : s.structuralSharing !== !1 ? ee(t, e) : e;
}
function Te(t, e, s = 0) {
  const r = [...t, e];
  return s && r.length > s ? r.slice(1) : r;
}
function ge(t, e, s = 0) {
  const r = [e, ...t];
  return s && r.length > s ? r.slice(0, -1) : r;
}
var te = Symbol();
function de(t, e) {
  return !t.queryFn && (e != null && e.initialPromise) ? () => e.initialPromise : !t.queryFn || t.queryFn === te ? () => Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)) : t.queryFn;
}
var U, R, T, W, ye = (W = class extends Z {
  constructor() {
    super();
    b(this, U, void 0);
    b(this, R, void 0);
    b(this, T, void 0);
    c(this, T, (e) => {
      if (!I && window.addEventListener) {
        const s = () => e();
        return window.addEventListener("visibilitychange", s, !1), () => {
          window.removeEventListener("visibilitychange", s);
        };
      }
    });
  }
  onSubscribe() {
    n(this, R) || this.setEventListener(n(this, T));
  }
  onUnsubscribe() {
    var e;
    this.hasListeners() || ((e = n(this, R)) == null || e.call(this), c(this, R, void 0));
  }
  setEventListener(e) {
    var s;
    c(this, T, e), (s = n(this, R)) == null || s.call(this), c(this, R, e((r) => {
      typeof r == "boolean" ? this.setFocused(r) : this.onFocus();
    }));
  }
  setFocused(e) {
    n(this, U) !== e && (c(this, U, e), this.onFocus());
  }
  onFocus() {
    const e = this.isFocused();
    this.listeners.forEach((s) => {
      s(e);
    });
  }
  isFocused() {
    var e;
    return typeof n(this, U) == "boolean" ? n(this, U) : ((e = globalThis.document) == null ? void 0 : e.visibilityState) !== "hidden";
  }
}, U = new WeakMap(), R = new WeakMap(), T = new WeakMap(), W), pe = new ye(), g, q, A, _, ve = (_ = class extends Z {
  constructor() {
    super();
    b(this, g, !0);
    b(this, q, void 0);
    b(this, A, void 0);
    c(this, A, (e) => {
      if (!I && window.addEventListener) {
        const s = () => e(!0), r = () => e(!1);
        return window.addEventListener("online", s, !1), window.addEventListener("offline", r, !1), () => {
          window.removeEventListener("online", s), window.removeEventListener("offline", r);
        };
      }
    });
  }
  onSubscribe() {
    n(this, q) || this.setEventListener(n(this, A));
  }
  onUnsubscribe() {
    var e;
    this.hasListeners() || ((e = n(this, q)) == null || e.call(this), c(this, q, void 0));
  }
  setEventListener(e) {
    var s;
    c(this, A, e), (s = n(this, q)) == null || s.call(this), c(this, q, e(this.setOnline.bind(this)));
  }
  setOnline(e) {
    n(this, g) !== e && (c(this, g, e), this.listeners.forEach((r) => {
      r(e);
    }));
  }
  isOnline() {
    return n(this, g);
  }
}, g = new WeakMap(), q = new WeakMap(), A = new WeakMap(), _), se = new ve();
function be() {
  let t, e;
  const s = new Promise((i, o) => {
    t = i, e = o;
  });
  s.status = "pending", s.catch(() => {
  });
  function r(i) {
    Object.assign(s, i), delete s.resolve, delete s.reject;
  }
  return s.resolve = (i) => {
    r({
      status: "fulfilled",
      value: i
    }), t(i);
  }, s.reject = (i) => {
    r({
      status: "rejected",
      reason: i
    }), e(i);
  }, s;
}
function me(t) {
  return Math.min(1e3 * 2 ** t, 3e4);
}
function re(t) {
  return (t ?? "online") === "online" ? se.isOnline() : !0;
}
var ie = class extends Error {
  constructor(t) {
    super("CancelledError"), this.revert = t == null ? void 0 : t.revert, this.silent = t == null ? void 0 : t.silent;
  }
};
function Q(t) {
  return t instanceof ie;
}
function Se(t) {
  let e = !1, s = 0, r = !1, i;
  const o = be(), l = (h) => {
    var v;
    r || (d(new ie(h)), (v = t.abort) == null || v.call(t));
  }, a = () => {
    e = !0;
  }, f = () => {
    e = !1;
  }, w = () => pe.isFocused() && (t.networkMode === "always" || se.isOnline()) && t.canRun(), y = () => re(t.networkMode) && t.canRun(), u = (h) => {
    var v;
    r || (r = !0, (v = t.onSuccess) == null || v.call(t, h), i == null || i(), o.resolve(h));
  }, d = (h) => {
    var v;
    r || (r = !0, (v = t.onError) == null || v.call(t, h), i == null || i(), o.reject(h));
  }, O = () => new Promise((h) => {
    var v;
    i = (C) => {
      (r || w()) && h(C);
    }, (v = t.onPause) == null || v.call(t);
  }).then(() => {
    var h;
    i = void 0, r || (h = t.onContinue) == null || h.call(t);
  }), F = () => {
    if (r)
      return;
    let h;
    const v = s === 0 ? t.initialPromise : void 0;
    try {
      h = v ?? t.fn();
    } catch (C) {
      h = Promise.reject(C);
    }
    Promise.resolve(h).then(u).catch((C) => {
      var B;
      if (r)
        return;
      const K = t.retry ?? (I ? 0 : 3), G = t.retryDelay ?? me, ne = typeof G == "function" ? G(s, C) : G, ae = K === !0 || typeof K == "number" && s < K || typeof K == "function" && K(s, C);
      if (e || !ae) {
        d(C);
        return;
      }
      s++, (B = t.onFail) == null || B.call(t, s, C), le(ne).then(() => w() ? void 0 : O()).then(() => {
        e ? d(C) : F();
      });
    });
  };
  return {
    promise: o,
    cancel: l,
    continue: () => (i == null || i(), o),
    cancelRetry: a,
    continueRetry: f,
    canStart: y,
    start: () => (y() ? F() : O().then(F), o)
  };
}
function we() {
  let t = [], e = 0, s = (a) => {
    a();
  }, r = (a) => {
    a();
  }, i = (a) => setTimeout(a, 0);
  const o = (a) => {
    e ? t.push(a) : i(() => {
      s(a);
    });
  }, l = () => {
    const a = t;
    t = [], a.length && i(() => {
      r(() => {
        a.forEach((f) => {
          s(f);
        });
      });
    });
  };
  return {
    batch: (a) => {
      let f;
      e++;
      try {
        f = a();
      } finally {
        e--, e || l();
      }
      return f;
    },
    /**
     * All calls to the wrapped function will be batched.
     */
    batchCalls: (a) => (...f) => {
      o(() => {
        a(...f);
      });
    },
    schedule: o,
    /**
     * Use this method to set a custom notify function.
     * This can be used to for example wrap notifications with `React.act` while running tests.
     */
    setNotifyFunction: (a) => {
      s = a;
    },
    /**
     * Use this method to set a custom function to batch notifications together into a single tick.
     * By default React Query will use the batch function provided by ReactDOM or React Native.
     */
    setBatchNotifyFunction: (a) => {
      r = a;
    },
    setScheduler: (a) => {
      i = a;
    }
  };
}
var Fe = we(), D, X, Ce = (X = class {
  constructor() {
    b(this, D, void 0);
  }
  destroy() {
    this.clearGcTimeout();
  }
  scheduleGc() {
    this.clearGcTimeout(), ue(this.gcTime) && c(this, D, setTimeout(() => {
      this.optionalRemove();
    }, this.gcTime));
  }
  updateGcTime(t) {
    this.gcTime = Math.max(
      this.gcTime || 0,
      t ?? (I ? 1 / 0 : 5 * 60 * 1e3)
    );
  }
  clearGcTimeout() {
    n(this, D) && (clearTimeout(n(this, D)), c(this, D, void 0));
  }
}, D = new WeakMap(), X), M, L, m, p, k, P, S, j, Y, Ae = (Y = class extends Ce {
  constructor(e) {
    super();
    b(this, S);
    b(this, M, void 0);
    b(this, L, void 0);
    b(this, m, void 0);
    b(this, p, void 0);
    b(this, k, void 0);
    b(this, P, void 0);
    c(this, P, !1), c(this, k, e.defaultOptions), this.setOptions(e.options), this.observers = [], c(this, m, e.cache), this.queryKey = e.queryKey, this.queryHash = e.queryHash, c(this, M, je(this.options)), this.state = e.state ?? n(this, M), this.scheduleGc();
  }
  get meta() {
    return this.options.meta;
  }
  get promise() {
    var e;
    return (e = n(this, p)) == null ? void 0 : e.promise;
  }
  setOptions(e) {
    this.options = { ...n(this, k), ...e }, this.updateGcTime(this.options.gcTime);
  }
  optionalRemove() {
    !this.observers.length && this.state.fetchStatus === "idle" && n(this, m).remove(this);
  }
  setData(e, s) {
    const r = fe(this.state.data, e, this.options);
    return E(this, S, j).call(this, {
      data: r,
      type: "success",
      dataUpdatedAt: s == null ? void 0 : s.updatedAt,
      manual: s == null ? void 0 : s.manual
    }), r;
  }
  setState(e, s) {
    E(this, S, j).call(this, { type: "setState", state: e, setStateOptions: s });
  }
  cancel(e) {
    var r, i;
    const s = (r = n(this, p)) == null ? void 0 : r.promise;
    return (i = n(this, p)) == null || i.cancel(e), s ? s.then(V).catch(V) : Promise.resolve();
  }
  destroy() {
    super.destroy(), this.cancel({ silent: !0 });
  }
  reset() {
    this.destroy(), this.setState(n(this, M));
  }
  isActive() {
    return this.observers.some(
      (e) => he(e.options.enabled, this) !== !1
    );
  }
  isDisabled() {
    return this.getObserversCount() > 0 ? !this.isActive() : this.options.queryFn === te || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;
  }
  isStale() {
    return this.state.isInvalidated ? !0 : this.getObserversCount() > 0 ? this.observers.some(
      (e) => e.getCurrentResult().isStale
    ) : this.state.data === void 0;
  }
  isStaleByTime(e = 0) {
    return this.state.isInvalidated || this.state.data === void 0 || !oe(this.state.dataUpdatedAt, e);
  }
  onFocus() {
    var s;
    const e = this.observers.find((r) => r.shouldFetchOnWindowFocus());
    e == null || e.refetch({ cancelRefetch: !1 }), (s = n(this, p)) == null || s.continue();
  }
  onOnline() {
    var s;
    const e = this.observers.find((r) => r.shouldFetchOnReconnect());
    e == null || e.refetch({ cancelRefetch: !1 }), (s = n(this, p)) == null || s.continue();
  }
  addObserver(e) {
    this.observers.includes(e) || (this.observers.push(e), this.clearGcTimeout(), n(this, m).notify({ type: "observerAdded", query: this, observer: e }));
  }
  removeObserver(e) {
    this.observers.includes(e) && (this.observers = this.observers.filter((s) => s !== e), this.observers.length || (n(this, p) && (n(this, P) ? n(this, p).cancel({ revert: !0 }) : n(this, p).cancelRetry()), this.scheduleGc()), n(this, m).notify({ type: "observerRemoved", query: this, observer: e }));
  }
  getObserversCount() {
    return this.observers.length;
  }
  invalidate() {
    this.state.isInvalidated || E(this, S, j).call(this, { type: "invalidate" });
  }
  fetch(e, s) {
    var f, w, y;
    if (this.state.fetchStatus !== "idle") {
      if (this.state.data !== void 0 && (s != null && s.cancelRefetch))
        this.cancel({ silent: !0 });
      else if (n(this, p))
        return n(this, p).continueRetry(), n(this, p).promise;
    }
    if (e && this.setOptions(e), !this.options.queryFn) {
      const u = this.observers.find((d) => d.options.queryFn);
      u && this.setOptions(u.options);
    }
    const r = new AbortController(), i = (u) => {
      Object.defineProperty(u, "signal", {
        enumerable: !0,
        get: () => (c(this, P, !0), r.signal)
      });
    }, o = () => {
      const u = de(this.options, s), d = {
        queryKey: this.queryKey,
        meta: this.meta
      };
      return i(d), c(this, P, !1), this.options.persister ? this.options.persister(
        u,
        d,
        this
      ) : u(d);
    }, l = {
      fetchOptions: s,
      options: this.options,
      queryKey: this.queryKey,
      state: this.state,
      fetchFn: o
    };
    i(l), (f = this.options.behavior) == null || f.onFetch(
      l,
      this
    ), c(this, L, this.state), (this.state.fetchStatus === "idle" || this.state.fetchMeta !== ((w = l.fetchOptions) == null ? void 0 : w.meta)) && E(this, S, j).call(this, { type: "fetch", meta: (y = l.fetchOptions) == null ? void 0 : y.meta });
    const a = (u) => {
      var d, O, F, h;
      Q(u) && u.silent || E(this, S, j).call(this, {
        type: "error",
        error: u
      }), Q(u) || ((O = (d = n(this, m).config).onError) == null || O.call(
        d,
        u,
        this
      ), (h = (F = n(this, m).config).onSettled) == null || h.call(
        F,
        this.state.data,
        u,
        this
      )), this.scheduleGc();
    };
    return c(this, p, Se({
      initialPromise: s == null ? void 0 : s.initialPromise,
      fn: l.fetchFn,
      abort: r.abort.bind(r),
      onSuccess: (u) => {
        var d, O, F, h;
        if (u === void 0) {
          a(new Error(`${this.queryHash} data is undefined`));
          return;
        }
        try {
          this.setData(u);
        } catch (v) {
          a(v);
          return;
        }
        (O = (d = n(this, m).config).onSuccess) == null || O.call(d, u, this), (h = (F = n(this, m).config).onSettled) == null || h.call(
          F,
          u,
          this.state.error,
          this
        ), this.scheduleGc();
      },
      onError: a,
      onFail: (u, d) => {
        E(this, S, j).call(this, { type: "failed", failureCount: u, error: d });
      },
      onPause: () => {
        E(this, S, j).call(this, { type: "pause" });
      },
      onContinue: () => {
        E(this, S, j).call(this, { type: "continue" });
      },
      retry: l.options.retry,
      retryDelay: l.options.retryDelay,
      networkMode: l.options.networkMode,
      canRun: () => !0
    })), n(this, p).start();
  }
}, M = new WeakMap(), L = new WeakMap(), m = new WeakMap(), p = new WeakMap(), k = new WeakMap(), P = new WeakMap(), S = new WeakSet(), j = function(e) {
  const s = (r) => {
    switch (e.type) {
      case "failed":
        return {
          ...r,
          fetchFailureCount: e.failureCount,
          fetchFailureReason: e.error
        };
      case "pause":
        return {
          ...r,
          fetchStatus: "paused"
        };
      case "continue":
        return {
          ...r,
          fetchStatus: "fetching"
        };
      case "fetch":
        return {
          ...r,
          ...Ee(r.data, this.options),
          fetchMeta: e.meta ?? null
        };
      case "success":
        return {
          ...r,
          data: e.data,
          dataUpdateCount: r.dataUpdateCount + 1,
          dataUpdatedAt: e.dataUpdatedAt ?? Date.now(),
          error: null,
          isInvalidated: !1,
          status: "success",
          ...!e.manual && {
            fetchStatus: "idle",
            fetchFailureCount: 0,
            fetchFailureReason: null
          }
        };
      case "error":
        const i = e.error;
        return Q(i) && i.revert && n(this, L) ? { ...n(this, L), fetchStatus: "idle" } : {
          ...r,
          error: i,
          errorUpdateCount: r.errorUpdateCount + 1,
          errorUpdatedAt: Date.now(),
          fetchFailureCount: r.fetchFailureCount + 1,
          fetchFailureReason: i,
          fetchStatus: "idle",
          status: "error"
        };
      case "invalidate":
        return {
          ...r,
          isInvalidated: !0
        };
      case "setState":
        return {
          ...r,
          ...e.state
        };
    }
  };
  this.state = s(this.state), Fe.batch(() => {
    this.observers.forEach((r) => {
      r.onQueryUpdate();
    }), n(this, m).notify({ query: this, type: "updated", action: e });
  });
}, Y);
function Ee(t, e) {
  return {
    fetchFailureCount: 0,
    fetchFailureReason: null,
    fetchStatus: re(e.networkMode) ? "fetching" : "paused",
    ...t === void 0 && {
      error: null,
      status: "pending"
    }
  };
}
function je(t) {
  const e = typeof t.initialData == "function" ? t.initialData() : t.initialData, s = e !== void 0, r = s ? typeof t.initialDataUpdatedAt == "function" ? t.initialDataUpdatedAt() : t.initialDataUpdatedAt : 0;
  return {
    data: e,
    dataUpdateCount: 0,
    dataUpdatedAt: s ? r ?? Date.now() : 0,
    error: null,
    errorUpdateCount: 0,
    errorUpdatedAt: 0,
    fetchFailureCount: 0,
    fetchFailureReason: null,
    fetchMeta: null,
    isInvalidated: !1,
    status: s ? "success" : "pending",
    fetchStatus: "idle"
  };
}
export {
  Ae as Q,
  Ce as R,
  Z as S,
  De as a,
  V as b,
  Se as c,
  ge as d,
  de as e,
  Te as f,
  pe as g,
  ce as h,
  Re as i,
  H as j,
  be as k,
  he as l,
  Ue as m,
  Fe as n,
  se as o,
  z as p,
  Pe as q,
  qe as r,
  te as s,
  I as t,
  ue as u,
  oe as v,
  Ee as w,
  fe as x
};
