import { CardanoApi, SelectedWallet } from '../typescript/cardano-api';
export interface WalletSlice {
    open: boolean;
    api: CardanoApi | undefined;
    selectedWallet: SelectedWallet | null;
    balance: number | null;
    userAddress: string | null;
    stakeKey: string | null;
    desiredWallet: SelectedWallet | null;
    isLoadingWallet: boolean;
    userOrders: any[];
    userTokens: any[];
    availableWallets: any[];
    addressList: any[];
    isOpenWallet: boolean;
    onWalletConnect?: (data: any) => void;
    onClickWalletConnect?: () => void;
    setIsLoadingWallet: (_isLoadingWallet: boolean) => void;
    resetWallet: () => void;
    updateSelectedWallet: (_newWalletData: any) => void;
    openModal: () => void;
    closeModal: (callback?: () => void) => void;
    toggleModal: () => void;
    setUserOrders: (orders: any[]) => void;
    setUserTokens: (tokens: any[]) => void;
    setAvailableWallets: (wallets: any[]) => void;
    updateBalance: (balance: number) => void;
    setAddressList: (addressList: any[]) => void;
    setIsOpenWallet: (isOpenWallet: boolean) => void;
    setOnWalletConnect: (callback?: (data: any) => void) => void;
    setOnClickWalletConnect: (callback?: () => void) => void;
}
declare const createTokenSearchSlice: (set: any) => {
    open: boolean;
    api: undefined;
    selectedWallet: null;
    balance: null;
    userAddress: null;
    stakeKey: null;
    desiredWallet: null;
    isLoadingWallet: boolean;
    userOrders: never[];
    userTokens: never[];
    availableWallets: never[];
    addressList: never[];
    isOpenWallet: boolean;
    onClickWalletConnect: undefined;
    setIsLoadingWallet: (_isLoadingWallet: boolean) => void;
    onWalletConnect: undefined;
    resetWallet: () => void;
    updateSelectedWallet: (_newWalletData: any) => void;
    openModal: () => void;
    closeModal: (callback?: () => void) => void;
    toggleModal: () => void;
    setUserOrders: (orders: any[]) => void;
    setUserTokens: (tokens: any[]) => void;
    setAvailableWallets: (wallets: any[]) => void;
    updateBalance: (balance: number) => void;
    setAddressList: (addressList: any[]) => void;
    setIsOpenWallet: (isOpenWallet: boolean) => void;
    setOnWalletConnect: (callback?: ((data: any) => void) | undefined) => void;
    setOnClickWalletConnect: (callback?: () => void) => void;
};
export default createTokenSearchSlice;
