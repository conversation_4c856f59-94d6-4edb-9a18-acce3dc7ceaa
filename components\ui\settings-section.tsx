import Switch from '@/components/ui/switch';
import { SelectDropdown } from '@/components/ui/select-dropdown';
import { LabelInputContainer } from '@/components/ui/label-input-container';
import { type FC } from 'react';

export interface SettingOption {
  id: string;
  label: string;
  description?: string;
  type: 'switch' | 'select' | 'custom';
  value?: any;
  onChange?: (value: any) => void;
  options?: { value: string; label: string }[];
  disabled?: boolean;
  className?: string;
  loading?: boolean;
  isMulti?: boolean;
  component?: React.ReactNode; // Add this for custom components
}

interface SettingsSectionProps {
  title?: string;
  options: SettingOption[];
}

export const SettingsSection: FC<SettingsSectionProps> = ({ title, options }) => {
  const renderControl = (option: SettingOption) => {
    const commonClasses = `relative flex items-center justify-center transition-opacity duration-200 ${option.className || ''}`;

    return (
      <div className={commonClasses}>
        {option.type === 'switch' ? (
          <Switch
            checked={option.value}
            onChange={option.onChange as any}
            disabled={option.disabled}
          />
        ) : option.type === 'select' ? (
          <SelectDropdown
            value={option.value}
            onChange={option.onChange as any}
            options={option.options || []}
            placeholder="Select option"
            className="w-[180px] min-w-40 rounded-md"
            disabled={option.disabled}
            isMulti={option.isMulti}
          />
        ) : option.type === 'custom' ? (
          option.component
        ) : null}

        {option.loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="absolute inset-0 bg-black/40 dark:bg-white/10 backdrop-blur-[1px] rounded-lg" />
            <span className="option-loader z-10"></span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {title &&
      <LabelInputContainer className="mb-6">
        <h3 className="text-xl font-semibold text-[#121212] dark:text-white mb-1">{title}</h3>
        <div className="mb-6 border border-solid border-b border-l-0 border-r-0 border-t-0 border-black/60 dark:border-white/60"></div>
      </LabelInputContainer>}

      <div className="space-y-4">
        {options.map((option) => (
          <div
            key={option.id}
            className="flex items-center justify-between gap-x-4"
          >
            <div className="space-y-1">
              <div className="font-medium">{option.label}</div>
              <div className="text-sm text-gray-500">{option.description}</div>
            </div>
            {renderControl(option)}
          </div>
        ))}
      </div>
    </div>
  );
};
