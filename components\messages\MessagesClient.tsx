"use client";

import {
  Cha<PERSON><PERSON><PERSON><PERSON>,
  ChatB<PERSON>bleActionWrapper,
  ChatBubbleAction,
  ChatBubbleAvatar,
  ChatBubbleMessage,
} from "@/components/ui/chat/chat-bubble";
import { But<PERSON>, buttonVariants } from "@/components/ui/chat/ui/button";
import { EmojiPicker } from "@/components/ui/chat/emoji-picker";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UserHoverCard } from "../ui/hover-card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/chat/ui/avatar";
import { Textarea } from "@/components/ui/chat/ui/textarea";
import { toast } from "react-toastify";
import { cn } from "@/lib/utils";
import { ChatMessageList } from "@/components/ui/chat/chat-message-list";
import {
  Edit2,
  PlusCircle,
  Trash2,
  CornerDownLeft,
  Mic,
  Paperclip,
  FileImage,
  ThumbsUp,
  Send,
  MoreVertical,
  SendHorizontal,
  PhoneCall,
  Video,
  HeartPulse,
  Tag,
  VolumeX,
  Languages,
} from "lucide-react";
import { LuPointer } from "react-icons/lu";
import { AiOutlineDollarCircle } from "react-icons/ai";
import { MdBlock, MdOutlineReportGmailerrorred } from "react-icons/md";
import React, { useEffect, useRef, useState, createRef } from "react";
import { useUser } from "@/hooks/useUser";
import { ChatListSkeleton, ChatSkeleton } from "@/components/ui/chat/chat-skeleton";
import AnimatedTabs from '@/components/ui/animated-tabs';
import Tippy from '@tippyjs/react';
import Swal from 'sweetalert2';
import { formatDistance } from 'date-fns';
import { formatMessageDate, isValidDate } from '@/public/main';
import { ChatBubbleAttachment } from '@/components/ui/chat/chat-bubble-attachment';
import { FileUpload, FileWithPreview } from '@/components/ui/chat/file-upload';
import { VideoCallDialog } from "@/components/dialogs/VideoCallDialog";
import PokeDialog from '@/components/dialogs/PokeDialog';
import VideoCallRequestDialog from '@/components/dialogs/VideoCallRequestDialog';
import Link from 'next/link';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

interface Message {
  id: string;
  content: string;
  sender_id: string;
  created_at: string;
  is_deleted?: boolean;
  is_edited?: boolean;
  edited_at: string;
  sender?: {
    profilePhoto?: string;
    username?: string;
  };
  recipient?: {
    profilePhoto?: string;
    username?: string;
  };
  attachment?: {
    type: 'image' | 'video' | 'file';
    url: string;
    name: string;
  };
}

interface Recipient {
  profilePhoto?: string;
  username?: string;
  user_id?: string;
  status?: string;
  last_active?: string | null;
  displayName?: string;
  bio?: string;
  coverBanner?: string;
  coverBannerType?: 'image' | 'video';
  isVerified?: boolean;
  photoCount?: number;
  videoCount?: number;
  clipCount?: number;
  gifCount?: number;
  accountType?: string;
  followerCount?: number;
  followingCount?: number;
}

interface MessagesProps {
  initialMessages: Message[] | undefined;
  initialChatId?: string;
  hasMore: boolean | undefined;
  totalMessages?: number | null | undefined;
  recipient?: Recipient | null | undefined;
}

const MessageActions = [
  {
    icon: Edit2,
    label: "Edit",
    forSender: true,
    forReceiver: false,
  },
  {
    icon: Trash2,
    label: "Delete",
    forSender: true,
    forReceiver: false,
  },
  {
    icon: ThumbsUp,
    label: "Like",
    forSender: false,
    forReceiver: true,
  },
  {
    icon: CornerDownLeft,
    label: "Reply",
    forSender: true,
    forReceiver: true,
  },
  {
    icon: MdOutlineReportGmailerrorred,
    label: "Report",
    forSender: false,
    forReceiver: true,
  },
];

export const BottombarIcons = [
  { icon: FileImage, label: "Upload Image" },
  { icon: Paperclip, label: "Upload Video" },
];

const messageUserActions = [
  {
    icon: VolumeX,
    label: "Mute User",
    action: (recipientId: string) => {
      toast.info(`${recipientId} has been muted`);
    }
  },
  {
    icon: MdBlock,
    label: "Block User",
    action: (recipientId: string) => {
      Swal.fire({
        title: 'Block User',
        text: 'Are you sure you want to block this user?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: 'var(--turquoise)',
        cancelButtonColor: '#6e6e6e',
        confirmButtonText: 'Yes, block them'
      }).then((result) => {
        if (result.isConfirmed) {
          toast.success(`User has been blocked`);
        }
      });
    }
  },
  {
    icon: MdOutlineReportGmailerrorred,
    label: "Report User",
    action: (recipientId: string) => {
      Swal.fire({
        title: 'Report User',
        text: 'Please select a reason for reporting this user',
        input: 'select',
        inputOptions: {
          'spam': 'Spam',
          'harassment': 'Harassment',
          'inappropriate': 'Inappropriate content',
          'other': 'Other'
        },
        showCancelButton: true,
        confirmButtonColor: 'var(--turquoise)',
        cancelButtonColor: '#6e6e6e',
        confirmButtonText: 'Report'
      }).then((result) => {
        if (result.isConfirmed) {
          toast.success(`User has been reported`);
        }
      });
    }
  },
  {
    icon: Languages,
    label: "Translate",
    action: () => {
      toast.info("Translation feature coming soon");
    }
  },
  {
    icon: Edit2,
    label: "Clear Chat History",
    action: (recipientId: string, chatId: string | null) => {
      Swal.fire({
        title: 'Clear Chat History',
        text: 'Are you sure you want to clear all messages? This cannot be undone.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: 'var(--turquoise)',
        cancelButtonColor: '#6e6e6e',
        confirmButtonText: 'Yes, clear it'
      }).then((result) => {
        if (result.isConfirmed) {
          toast.success("Chat history cleared");
        }
      });
    }
  },
];

export default function MessagesClient({
  initialMessages = [],
  initialChatId,
  hasMore: initialHasMore,
  totalMessages: initialTotalMessages,
  recipient,
}: MessagesProps) {
  const { user, isLoading: userLoading } = useUser();
  const userId = user?.user_id;
  const messagesRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const tooltipButtonRef = useRef<HTMLButtonElement>(null);

  const [editedContent, setEditedContent] = useState("");
  const [editingInlineId, setEditingInlineId] = useState<string | null>(null);
  const [originalContent, setOriginalContent] = useState("");
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [message, setMessage] = useState("");
  const [isMessagesLoading, setIsMessagesLoading] = useState(false);
  const [selectedChat, setSelectedChat] = useState<string | null>(initialChatId || null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(initialHasMore);
  const [totalMessages, setTotalMessages] = useState(initialTotalMessages);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [showIconTooltip, setShowIconTooltip] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showVideoDialog, setShowVideoDialog] = useState(false);
  const [shareCamera, setShareCamera] = useState(true);
  const [shareAudio, setShareAudio] = useState(true);
  const [isEditingLoading, setIsEditingLoading] = useState(false);
  const [isDeletingLoading, setIsDeletingLoading] = useState(false);
  const [loadingMessageId, setLoadingMessageId] = useState<string | null>(null);
  const [isFirstMessage, setIsFirstMessage] = useState(false);
  const [recipientStatus, setRecipientStatus] = useState(recipient?.status || 'offline');
  const [recipientLastActive, setRecipientLastActive] = useState(recipient?.last_active || 'Unknown');
  const [isRecipientTyping, setIsRecipientTyping] = useState<boolean>(false);

  const [showPokeDialog, setShowPokeDialog] = useState(false);
  const [showVideoCallRequestDialog, setShowVideoCallRequestDialog] = useState(false);
  const typingTimeoutRef = useRef(null) as any;
  const [newLabel, setNewLabel] = useState("");
  const [showAddLabel, setShowAddLabel] = useState(false);
  const [replyingTo, setReplyingTo] = useState<{
    id: string;
    content: string;
    sender?: {
      username?: string;
      profilePhoto?: string;
    };
  } | null>(null);

  // Add a function to clear the reply
  const clearReply = () => {
    setReplyingTo(null);
  };

  // State for tab navigation
  const [activeTab, setActiveTab] = useState<string>("messages");

  // Add this function to handle input changes and emit typing status directly
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMessage(value);

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      if (recipient?.user_id) {
        const roomId = [userId, recipient.user_id].sort().join('-');
        console.log('Emitting stopped-typing event to room:', roomId);

        // This part of the logic is no longer needed as sockets are removed
        // socket.emit('stopped-typing', {
        //   roomId: roomId,
        //   user: { id: userId, username: user?.user_info.username || 'User' }
        // });
      }
    }, 4000);
  };

  const insertEmojiAtCursor = (text: string, emoji: any, cursorPosition: number) => {
    const start = text.slice(0, cursorPosition);
    const end = text.slice(cursorPosition);
    return {
      newText: start + emoji + end,
      newPosition: cursorPosition + emoji.length
    };
  };

  const handleActionClick = async (action: string, messageId: string) => {
    if (isEditingLoading || isDeletingLoading) return;

    const message = messages.find(m => m.id === messageId);
    if (!message) return;

    if (action === "Delete") {
      Swal.fire({
        title: 'Delete Message',
        text: 'Are you sure you want to delete this message?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: 'var(--turquoise)',
        cancelButtonColor: '#6e6e6e',
        confirmButtonText: 'Yes, delete it!'
      }).then(async (result) => {
        if (result.isConfirmed) {
          await deleteMessage(messageId);
        }
      });
    } else if (action === "Edit") {
      if (editingInlineId && editingInlineId !== messageId && originalContent !== editedContent) {
        Swal.fire({
          title: 'Unsaved Changes',
          text: 'You have unsaved changes. Do you want to discard them and edit this message?',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: 'var(--turquoise)',
          cancelButtonColor: '#6e6e6e',
          confirmButtonText: 'Yes, edit this message'
        }).then((result) => {
          if (result.isConfirmed) {
            setEditingInlineId(messageId);
            setOriginalContent(message.content);
            setEditedContent(message.content);
          }
        });
      } else {
        setEditingInlineId(messageId);
        setOriginalContent(message.content);
        setEditedContent(message.content);
      }
    } else if (action === "Reply") {
      // Set reply to this message
      setReplyingTo({
        id: messageId,
        content: message.content,
        sender: message.sender
      });

      // Focus the input field
      if (inputRef.current) {
        inputRef.current.focus();
      }
    } else if (action === "Report") {
      Swal.fire({
        title: 'Report Message',
        text: 'Please select a reason for reporting this message',
        input: 'select',
        inputOptions: {
          'spam': 'Spam',
          'harassment': 'Harassment',
          'inappropriate': 'Inappropriate content',
          'other': 'Other'
        },
        showCancelButton: true,
        confirmButtonColor: 'var(--turquoise)',
        cancelButtonColor: '#6e6e6e',
        confirmButtonText: 'Report'
      }).then((result) => {
        if (result.isConfirmed) {
          toast.success(`Message has been reported`);
        }
      });
    }
  };

  // Convex mutations
  const sendMessageMutation = useMutation(api.messages.sendMessage);
  const editMessageMutation = useMutation(api.messages.editMessage);
  const deleteMessageMutation = useMutation(api.messages.deleteMessage);

  const deleteMessage = async (messageId: string): Promise<void> => {
    try {
      setIsDeletingLoading(true);
      setLoadingMessageId(messageId);

      // Use Convex mutation
      const response = await deleteMessageMutation({ messageId });
      if (!response?.success) throw new Error(response?.message || 'Failed to delete message');

      // No need to update local state if using Convex queries for messages
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, content: "", is_deleted: true }
          : msg
      ));

    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error("Failed to delete message");
    } finally {
      setIsDeletingLoading(false);
      setLoadingMessageId(null);
    }
  };

  const handleEditMessage = async (messageId: string, newContent: string) => {
    if (!newContent.trim()) return;

    setIsEditingLoading(true);
    setLoadingMessageId(messageId);

    try {
      // Use Convex mutation
      const response = await editMessageMutation({ messageId, content: newContent });
      if (!response?.success) throw new Error(response?.message || 'Failed to edit message');

      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === messageId
            ? { ...msg, content: newContent, is_edited: true, edited_at: new Date().toISOString() }
            : msg
        )
      );

      setEditingInlineId(null);
      setOriginalContent("");
    } catch (error) {
      console.error('Error editing message:', error);
      toast.error('Failed to edit message');
    } finally {
      setIsEditingLoading(false);
      setLoadingMessageId(null);
    }
  };

  const cancelInlineEdit = () => {
    setEditingInlineId(null);
    setEditedContent("");
    setOriginalContent("");
  };

  const handleEmojiInInlineEdit = (emoji: any) => {
    const textareaElement = document.querySelector('textarea[value="' + editedContent + '"]') as any;
    if (!textareaElement) {
      setEditedContent(prev => prev + emoji);
      return;
    }

    const cursorPosition = textareaElement.selectionStart;
    const { newText, newPosition } = insertEmojiAtCursor(editedContent, emoji, cursorPosition);

    setEditedContent(newText);

    setTimeout(() => {
      if (textareaElement) {
        textareaElement.focus();
        textareaElement.selectionStart = textareaElement.selectionEnd = newPosition;
      }
    }, 0);
  };

  const loadMoreMessages = async () => {
    if (!hasMore || isLoadingMore || !selectedChat) return;

    try {
      setIsLoadingMore(true);
      const nextPage = page + 1;

      const response = await fetch(`/api/messages/${selectedChat}?page=${nextPage}`);
      if (!response.ok) throw new Error('Failed to fetch messages');

      const data = await response.json();

      setMessages(prevMessages => [...data.messages, ...prevMessages]);
      setHasMore(data.hasMore);
      setTotalMessages(data.totalMessages);
      setPage(nextPage);
    } catch (error) {
      console.error('Error loading more messages:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handleThumbsUp = async () => {
    if (!selectedChat) return;

    try {
      setIsMessagesLoading(true);
      // Use Convex mutation
      const response = await sendMessageMutation({ content: "👍", receiverId: selectedChat });
      if (!response?.success) throw new Error(response?.message || 'Failed to send message');

      setMessages(prev => [...prev, response.message]);

    } catch (error) {
      console.error('Error sending thumbs up:', error);
    } finally {
      setIsMessagesLoading(false);
    }
  };

  const handleSend = () => {
    if (message.trim() && !isMessagesLoading) {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      handleSendMessage(message);
    }
  };

  const handleSendMessage = async (content: string) => {
    if (!selectedChat) return;

    try {
      setIsMessagesLoading(true);
      // Use Convex mutation
      const response = await sendMessageMutation({ content, receiverId: selectedChat });
      if (!response?.success) throw new Error(response?.message || 'Failed to send message');

      // Optionally update local state if not using Convex query for messages
      setMessages(prev => [...prev, response.message]);

      setMessage('');
      clearReply(); // Clear the reply after sending

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setIsMessagesLoading(false);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }

    if (event.key === "Enter" && event.shiftKey) {
      event.preventDefault();
      setMessage((prev) => prev + "\n");
    }
  };

  // Filter messages with attachments for the Media tab
  const mediaMessages = messages.filter(
    (msg) => msg.attachment && !msg.is_deleted
  );

  const handleUserAction = (action: string, recipientId: string) => {
    const actionItem = messageUserActions.find(item => item.label === action);
    if (actionItem && actionItem.action) {
      actionItem.action(recipientId, selectedChat);
    }
  };

  const observerTarget = useRef<HTMLDivElement>(null) as any;

  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // This part of the logic is no longer needed as sockets are removed
      // if (socket && isConnected && recipient?.user_id) {
      //   const roomId = [userId, recipient.user_id].sort().join('-');
      //   socket.emit('stopped-typing', {
      //     roomId: roomId,
      //     user: { id: userId, username: userId || 'User' },
      //     typing: false
      //   });
      // }
    };
  }, [typingTimeoutRef.current, userId, recipient?.user_id]);

  useEffect(() => {
    if (initialMessages && initialMessages.length === 0 && selectedChat) {
      setIsFirstMessage(true);
    } else {
      setIsFirstMessage(false);
    }
  }, [initialMessages, selectedChat]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showIconTooltip &&
        tooltipRef.current &&
        tooltipButtonRef.current &&
        !tooltipRef.current.contains(event.target as Node) &&
        !tooltipButtonRef.current.contains(event.target as Node)
      ) {
        setShowIconTooltip(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showIconTooltip]);

  /* useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []); */

  // UseEffect for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          loadMoreMessages();
        }
      },
      { threshold: 0.1 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [hasMore, isLoadingMore, selectedChat]);

  useEffect(() => {
    // For online/offline status, use Convex query:
    // Example:
    const { data: recipientStatusData } = useQuery(
      api.users.getUserStatus,
      recipient?.user_id ? { userId: recipient.user_id } : "skip"
    );
    const recipientStatus = recipientStatusData?.status || 'offline';
    const recipientLastActive = recipientStatusData?.lastActive || null;

    // This useEffect block is no longer needed as socket-based status is removed
    // and recipientStatus is now managed by the user's status query.
  }, [recipient?.user_id]);

  useEffect(() => {
    if (messagesRef.current) {
      messagesRef.current.scrollTop = messagesRef.current.scrollHeight;
    }
  }, [messages, activeTab]);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.style.height = 'auto';
      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 150)}px`;
    }
  }, [message]);

  useEffect(() => {
    console.log({ isRecipientTyping });
  }, [isRecipientTyping]);

  //if (!user) return null;

  return (
    <>
      <main className="flex h-full w-full flex-col items-center">
        {/* Header */}
        <div className="w-full border-b border-t-0 border-r-0 border-l-0 border-solid border-[#18181b]/30 dark:border-white/30 p-3 flex items-center justify-between">
          {/* Avatar and username */}
          <div className="flex items-center gap-3">
            {/* Avatar */}
            <div className="relative">
              <Avatar className="h-10 w-10 relative overflow-visible">
                <AvatarImage className="rounded-full" src={recipient?.profilePhoto || '/images/user/default-avatar.webp'} />
                <AvatarFallback>
                  {(recipient?.username || recipient?.user_id?.slice(0, 2) || '?').charAt(0)}
                </AvatarFallback>

                <div className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-solid border-white ${recipientStatus === 'online' ? 'bg-green-500' : 'bg-gray-400'}`} />
              </Avatar>
            </div>

            {/* Username and status */}
            <div>
              <UserHoverCard
                username={recipient?.username || ''}
                userData={{
                  username: recipient?.username || '',
                  displayName: recipient?.displayName || '',
                  profilePhoto: recipient?.profilePhoto || '',
                  coverBanner: recipient?.coverBanner,
                  coverBannerType: recipient?.coverBannerType,
                  bio: recipient?.bio,
                  isVerified: recipient?.isVerified,
                  photoCount: recipient?.photoCount,
                  videoCount: recipient?.videoCount,
                  clipCount: recipient?.clipCount,
                  gifCount: recipient?.gifCount,
                  accountType: recipient?.accountType,
                }}
              >
                <Link href={`/user/${recipient?.username}`} className="font-medium cursor-pointer hover:text-turquoise transition-colors">
                  <h2 className="font-medium cursor-pointer hover:text-turquoise transition-colors">
                    {recipient?.username || recipient?.user_id?.slice(0, 8) || 'Unknown User'}
                  </h2>
                </Link>
              </UserHoverCard>
              <div className="flex items-center gap-1.5">
                <p className="text-sm text-gray-500">
                  {recipientStatus === 'online' ? 'Active Now' : 'Last Active: ' +
                    (recipientLastActive && isValidDate(recipientLastActive) ?
                      formatDistance(new Date(recipientLastActive), new Date(), { addSuffix: true }) :
                      'Unknown')}
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            {/* Tabs */}
            <div className="flex items-center justify-center gap-2 rounded-lg cursor-pointer p-2 relative z-0">
              <AnimatedTabs
                defaultValue="messages"
                onValueChange={(value: string | null) => {
                  if (value) setActiveTab(value);
                }}
                className="bg-gray-300 dark:bg-zinc-700 rounded-lg cursor-pointer"
                transition={{ duration: 0.2, ease: "easeOut" }}
              >
                <div
                  data-id="messages"
                  className={cn(
                    "px-4 py-2 text-sm font-medium rounded-full",
                    activeTab === "messages" ? "text-white dark:text-white" : "text-gray-500"
                  )}
                >
                  Messages
                </div>

                <div
                  data-id="media"
                  className={cn(
                    "px-4 py-2 text-sm font-medium rounded-full",
                    activeTab === "media" ? "text-white dark:text-white" : "text-gray-500"
                  )}
                >
                  Media
                </div>
              </AnimatedTabs>
            </div>

            {/* More menu button - converted to dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="h-9 w-9 flex items-center justify-center gap-2 rounded-full hover:bg-gray-200 dark:hover:bg-[#2a2a2a]">
                  <MoreVertical size={22} className="text-gray-500 dark:text-white" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48 z-10 bg-white/60 dark:!bg-[#18181b]/60 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-solid border-gray-200/30">
                {messageUserActions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <React.Fragment key={action.label}>
                      <DropdownMenuItem
                        onClick={() => handleUserAction(action.label, recipient?.user_id || '')}
                        className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                      >
                        <Icon className="mr-2 h-4 w-4" />
                        <span>{action.label}</span>
                      </DropdownMenuItem>
                      {index < messageUserActions.length - 1 && (
                        <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white/20" />
                      )}
                    </React.Fragment>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

        </div>

        {activeTab === "messages" ? (
          <>
            {userLoading ? (
              <div className="flex-1 w-full overflow-y-auto py-6">
                <ChatMessageList>
                  {Array.from({ length: Math.min(initialMessages?.length || 0, 5) }).map((_, index) => (
                    <ChatSkeleton
                      key={`skeleton-${index}`}
                      variant={index % 2 === 0 ? "received" : "sent"}
                    />
                  ))}
                </ChatMessageList>
              </div>
            ) : (
              <div className="flex-1 w-full overflow-y-auto py-6">
                <ChatMessageList ref={messagesRef}>
                  <div ref={observerTarget} className="h-px m-0" />

                  {isFirstMessage && (
                    <div className="w-full h-full flex justify-center my-4">
                      <div className="w-full p-3 text-center">
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          This is the beginning of your conversation.
                          Send a message to start chatting.
                        </p>
                      </div>
                    </div>
                  )}

                  {isLoadingMore && <ChatSkeleton variant="received" />}

                  {messages.map((message) => (
                    <ChatBubble
                      key={message.id}
                      variant={message.sender_id === userId ? "sent" : "received"}
                    >
                      <ChatBubbleAvatar
                        src={message.sender?.profilePhoto}
                        fallback={message.sender?.username?.[0] || 'U'}
                      />
                      {editingInlineId === message.id ? (
                        <div className="w-full">
                          <div className="relative">
                            <Textarea
                              value={editedContent}
                              onChange={(e) => setEditedContent(e.target.value)}
                              className="min-h-[120px] min-w-[350px] pr-10 w-full resize-none"
                              placeholder="Edit your message..."
                              autoFocus
                              disabled={isEditingLoading && loadingMessageId === message.id}
                            />
                            <div className="absolute right-5 bottom-3">
                              <EmojiPicker
                                onChange={handleEmojiInInlineEdit}
                              />
                            </div>
                          </div>
                          <div className="flex justify-end gap-2 mt-2">
                            <Button
                              onClick={() => handleEditMessage(message.id, editedContent)}
                              size="sm"
                              className="bg-turquoise hover:bg-turquoise/90 text-white dark:bg-turquoise/90 dark:hover:bg-turquoise/90"
                              disabled={(editedContent === originalContent) || (isEditingLoading && loadingMessageId === message.id)}
                            >
                              {isEditingLoading && loadingMessageId === message.id ? (
                                <div className="flex items-center gap-1">
                                  <span>Saving...</span>
                                </div>
                              ) : "Save"}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={cancelInlineEdit}
                              disabled={isEditingLoading && loadingMessageId === message.id}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col gap-1 justify-end">
                          <ChatBubbleMessage
                            variant={message.sender_id === userId ? "sent" : "received"}
                          >
                            {message.is_deleted ? (
                              <span className="text-xs italic">This message has been deleted</span>
                            ) : (
                              <>
                                {message.attachment && (
                                  <ChatBubbleAttachment attachment={message.attachment} />
                                )}
                                {message.content && (
                                  <div className={message.attachment ? "mt-2" : ""}>
                                    {message.content}
                                  </div>
                                )}
                                <span className="text-xs opacity-70 block mt-1 w-full text-right">
                                  {message.created_at && isValidDate(message.created_at) ?
                                    new Date(message.created_at).toLocaleDateString('en-US', {
                                      day: 'numeric',
                                      month: 'long',
                                      year: 'numeric',
                                      hour: 'numeric',
                                      minute: '2-digit',
                                      hour12: true
                                    }) : 'Just now'}
                                  {message.is_edited && !message.is_deleted && (
                                    <>
                                      {" • "}
                                      <span>(Edited)</span>
                                    </>
                                  )}
                                </span>
                              </>
                            )}
                          </ChatBubbleMessage>
                        </div>
                      )}
                      {message.sender_id === userId &&
                        !message.is_deleted &&
                        editingInlineId !== message.id &&
                        !(isDeletingLoading && loadingMessageId === message.id) && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <button className="absolute top-2 -left-9 h-7 w-7 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-zinc-700 transition-colors">
                                <MoreVertical size={16} className="text-gray-500 dark:text-gray-400" />
                              </button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="start" className="w-40 z-10 bg-white/60 dark:!bg-[#18181b]/60 backdrop-blur border border-solid border-gray-200/30">
                              {MessageActions.filter(action => action.forSender).map((action, index) => {
                                const Icon = action.icon;
                                return (
                                  <React.Fragment key={action.label}>
                                    <DropdownMenuItem
                                      onClick={() => handleActionClick(action.label, message.id)}
                                      className="cursor-pointer text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600"
                                      disabled={isEditingLoading || isDeletingLoading}
                                    >
                                      <Icon className="mr-2 h-4 w-4" />
                                      <span>{action.label}</span>
                                    </DropdownMenuItem>
                                    {index < MessageActions.filter(action => action.forSender).length - 1 && (
                                      <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white/20" />
                                    )}
                                  </React.Fragment>
                                );
                              })}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      {message.sender_id !== userId &&
                        !message.is_deleted && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <button className="absolute top-2 -right-9 h-7 w-7 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-zinc-700 transition-colors">
                                <MoreVertical size={16} className="text-gray-500 dark:text-gray-400" />
                              </button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-40 z-10 bg-white/60 dark:!bg-[#18181b]/60 backdrop-blur border border-solid border-gray-200/30">
                              {MessageActions.filter(action => action.forReceiver).map((action, index) => {
                                const Icon = action.icon;
                                return (
                                  <React.Fragment key={action.label}>
                                    <DropdownMenuItem
                                      onClick={() => handleActionClick(action.label, message.id)}
                                      className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                                    >
                                      <Icon className="mr-2 h-4 w-4" />
                                      <span>{action.label}</span>
                                    </DropdownMenuItem>
                                    {index < MessageActions.filter(action => action.forReceiver).length - 1 && (
                                      <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white/20" />
                                    )}
                                  </React.Fragment>
                                );
                              })}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      {isDeletingLoading && loadingMessageId === message.id && (
                        <div className="flex items-center justify-center p-2">
                          <span className="message-search-loader"></span>
                        </div>
                      )}
                    </ChatBubble>
                  ))}

                  {isMessagesLoading && (
                    <ChatBubble variant="sent">
                      <ChatBubbleAvatar
                        src={user?.user_info.profilePhoto}
                        fallback={user?.user_info.username?.[0] || 'U'}
                      />
                      <ChatBubbleMessage isLoading />
                    </ChatBubble>
                  )}
                </ChatMessageList>
              </div>
            )}

            {/* This section is no longer needed as typing indicator is removed */}
            {isRecipientTyping && (
              <ChatBubble variant="received">
                <ChatBubbleAvatar
                  src={recipient?.profilePhoto}
                  fallback={recipient?.username?.[0] || 'U'}
                />
                <ChatBubbleMessage isLoading />
              </ChatBubble>
            )}
          </>
        ) : (
          <div className="flex-1 w-full overflow-y-auto py-6">
            <div className="px-4">
              <h3 className="text-lg font-medium mb-4">Shared Media</h3>
              {mediaMessages.length === 0 ? (
                <div className="flex justify-center items-center h-full">
                  <p className="text-sm text-gray-500">No media shared in this conversation.</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {mediaMessages.map((message) => (
                    message.attachment && (
                      <div key={message.id} className="relative">
                        <ChatBubbleAttachment attachment={message.attachment} />
                        <span className="text-xs text-gray-500 mt-1 block">
                          {message.created_at && isValidDate(message.created_at)
                            ? new Date(message.created_at).toLocaleDateString('en-US', {
                              day: 'numeric',
                              month: 'short',
                              year: 'numeric'
                            })
                            : 'Just now'}
                        </span>
                      </div>
                    )
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Message Actions & Input */}
        {activeTab === "messages" && (
          <div className="px-2 py-4 flex w-full items-center gap-2 bg-transparent border border-solid border-gray-300 dark:border-white/30 border-l-0 border-r-0 border-b-0">
            <div className="flex">
              <Tippy content="Upload Image" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={isMessagesLoading}
                  className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
                >
                  <FileImage className="w-6 h-6" />
                </Button>
              </Tippy>
            </div>

            {/* Chat Input */}
            <div className="flex items-center gap-2 w-full bg-transparent rounded-full px-4 py-2 border border-solid border-gray-300 dark:border-white">
              <input
                value={message}
                onChange={handleInputChange}
                placeholder="Write Message"
                className="flex-grow bg-transparent border-none focus:outline-none text-gorilla-gray dark:text-white placeholder-gray-400 dark:placeholder-gray-400 placeholder:text-sm"
              />
              <button
                type="submit"
                disabled={!message.trim() || isMessagesLoading}
                className="text-blue-gray hover:text-turquoise dark:text-white dark:hover:text-turquoise disabled:opacity-50"
              >
                <SendHorizontal className="h-6 w-6" />
              </button>
            </div>

            <Tippy content="Insert Emoji" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                <EmojiPicker
                  onChange={(value) => {
                    setMessage(message + value);
                    if (inputRef.current) {
                      inputRef.current.focus();
                    }
                  }}
                className="h-6 w-6 text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
                />
            </Tippy>

            <Tippy content="Poke" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                disabled={isMessagesLoading}
                onClick={() => setShowPokeDialog(true)}
                className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
              >
                <LuPointer size={22} className="w-6 h-6" />
              </Button>
            </Tippy>

            <Tippy content="Send Tip" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                disabled={isMessagesLoading}
                className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
              >
                <AiOutlineDollarCircle size={22} className="w-6 h-6" />
              </Button>
            </Tippy>

            <Tippy content="Voice Note" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                disabled={isMessagesLoading}
                className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
              >
                <Mic size={22} className="w-6 h-6" />
              </Button>
            </Tippy>

            <Tippy content="Voice Call" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                disabled={isMessagesLoading}
                className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
                onClick={() => setShowVideoCallRequestDialog(true)}
              >
                <PhoneCall className="w-6 h-6" />
              </Button>
            </Tippy>

            <Tippy content="Video Call" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                disabled={isMessagesLoading}
                className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
                onClick={() => setShowVideoDialog(true)}
              >
                <Video className="w-6 h-6" />
              </Button>
            </Tippy>

          </div>
        )}
      </main>

      <VideoCallDialog
        open={showVideoDialog}
        onClose={() => setShowVideoDialog(false)}
        onAddFunds={() => {/* handle add funds */ }}
        onSelectSource={() => {/* handle select source */ }}
        balance={0}
        pricePerMinute={3.99}
        minCallTime={20}
        username={recipient?.username || "User"}
        shareCamera={shareCamera}
        setShareCamera={setShareCamera}
        shareAudio={shareAudio}
        setShareAudio={setShareAudio}
      />

      <PokeDialog
        isOpen={showPokeDialog}
        onClose={() => setShowPokeDialog(false)}
        recipientName={recipient?.username || "User"}
        recipientId={recipient?.user_id || ""}
      />

      <VideoCallRequestDialog
        isOpen={showVideoCallRequestDialog}
        onClose={() => setShowVideoCallRequestDialog(false)}
        recipientName={recipient?.username || "User"}
        recipientId={recipient?.user_id || ""}
      />
    </>
  );
};