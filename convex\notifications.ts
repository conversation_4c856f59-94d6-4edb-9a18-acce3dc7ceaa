import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { sendPushNotificationAction } from "./functions/sendPushNotificationAction";

// Add a notification
export const addNotification = mutation({
  args: {
    user_id: v.string(),
    title: v.optional(v.string()),
    message: v.string(),
    type: v.string(),
    link: v.optional(v.string()),
    icon: v.optional(v.string()),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    const id = await ctx.db
      .insert("Notifications", {
        user_id: args.user_id,
        title: args.title,
        message: args.message,
        type: args.type,
        link: args.link,
        icon: args.icon,
        metadata: args.metadata,
        created_at: now,
        status: "unread",
      });
    // Optionally: emit a socket event or send push notification
    return { success: true, id };
  },
});

// Get notifications for a user
export const getNotifications = query({
  args: { user_id: v.string() },
  handler: async (ctx, args) => {
    try {
      const notifications = await ctx.db
        .query("Notifications")
        .filter(q => q.eq(q.field("user_id"), args.user_id))
        .order("desc")
        .collect();
      return { success: true, notifications };
    } catch (error: any) {
      console.error('getNotifications Convex error:', error.message, error.stack);
      return { success: false, message: 'Failed to fetch notifications' };
    }
  },
});

// Mark a notification as read
export const markNotificationAsRead = mutation({
  args: { notification_id: v.id("Notifications") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.notification_id, { status: "read" });
    return { success: true };
  },
});

// Mark all notifications as read for a user
export const markAllNotificationsAsRead = mutation({
  args: { user_id: v.string() },
  handler: async (ctx, args) => {
    const notifications = await ctx.db
      .query("Notifications")
      .filter(q =>
        q.and(
          q.eq(q.field("user_id"), args.user_id),
          q.eq(q.field("status"), "unread")
        )
      )
      .collect();

    for (const notification of notifications) {
      await ctx.db.patch(notification._id, { status: "read" });
    }

    return { success: true, count: notifications.length };
  },
});

// Mark all message-related notifications as read for a user
export const markMessagesAsRead = mutation({
  args: { user_id: v.string() },
  handler: async (ctx, args) => {
    const notifications = await ctx.db
      .query("Notifications")
      .filter(q =>
        q.and(
          q.eq(q.field("user_id"), args.user_id),
          q.eq(q.field("status"), "unread"),
          q.or(
            q.eq(q.field("type"), "new-message"),
            q.eq(q.field("type"), "message-reply")
          )
        )
      )
      .collect();

    for (const notification of notifications) {
      await ctx.db.patch(notification._id, { status: "read" });
    }

    return { success: true, count: notifications.length };
  },
});

// Clear all notifications for a user
export const clearNotifications = mutation({
  args: { user_id: v.string() },
  handler: async (ctx, args) => {
    const notifications = await ctx.db
      .query("Notifications")
      .filter(q => q.eq(q.field("user_id"), args.user_id))
      .collect();

    for (const notification of notifications) {
      await ctx.db.delete(notification._id);
    }

    return { success: true, count: notifications.length };
  },
});

// Send notification to a specific user with push notification
export const sendNotificationToUser = mutation({
  args: {
    user_id: v.string(),
    title: v.string(),
    message: v.string(),
    type: v.string(),
    link: v.optional(v.string()),
    icon: v.optional(v.string()),
    metadata: v.optional(v.any()),
    sendPush: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();

    // Save notification to database
    const notificationId = await ctx.db.insert("Notifications", {
      user_id: args.user_id,
      title: args.title,
      message: args.message,
      type: args.type,
      link: args.link,
      icon: args.icon,
      metadata: args.metadata,
      created_at: now,
      status: "unread",
    });

    // Send push notification if requested
    if (args.sendPush) {
      try {
        // Get user's device IDs
        const devices = await ctx.db
          .query("UserDevices")
          .filter(q => q.eq(q.field("user_id"), args.user_id))
          .collect();

        const deviceIds = devices.map(device => device.device_id);
        if (deviceIds.length > 0) {
          await ctx.scheduler.runAfter(0, sendPushNotificationAction, {
            deviceIds,
            title: args.title,
            message: args.message,
            data: args.metadata || {},
            url: args.link || "",
          });
        }
      } catch (error) {
        console.error('Error scheduling push notification:', error);
        // Don't fail the entire operation if push notification fails
      }
    }

    return { success: true, id: notificationId };
  },
});

// Send notification to multiple users
export const sendNotificationToUsers = mutation({
  args: {
    user_ids: v.array(v.string()),
    title: v.string(),
    message: v.string(),
    type: v.string(),
    link: v.optional(v.string()),
    icon: v.optional(v.string()),
    metadata: v.optional(v.any()),
    sendPush: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    const results = [];

    for (const user_id of args.user_ids) {
      // Save notification to database
      const notificationId = await ctx.db.insert("Notifications", {
        user_id,
        title: args.title,
        message: args.message,
        type: args.type,
        link: args.link,
        icon: args.icon,
        metadata: args.metadata,
        created_at: now,
        status: "unread",
      });

      results.push({ user_id, notification_id: notificationId });
    }

    // Send push notifications if requested
    if (args.sendPush) {
      try {
        // Get devices for the first user only to avoid duplicate device IDs
        const allDevices = await ctx.db
          .query("UserDevices")
          .filter(q => q.eq(q.field("user_id"), args.user_ids[0]))
          .collect();

        // Get devices for all users except the first one by querying each user separately
        for (let i = 1; i < args.user_ids.length; i++) {
          const userDevices = await ctx.db
            .query("UserDevices")
            .filter(q => q.eq(q.field("user_id"), args.user_ids[i]))
            .collect();
          allDevices.push(...userDevices);
        }

        const deviceIds = allDevices.map(device => device.device_id);
        if (deviceIds.length > 0) {
          await ctx.scheduler.runAfter(0, sendPushNotificationAction, {
            deviceIds,
            title: args.title,
            message: args.message,
            data: args.metadata || {},
            url: args.link || "",
          });
        }
      } catch (error) {
        console.error('Error scheduling push notifications:', error);
        // Don't fail the entire operation if push notification fails
      }
    }

    return { success: true, results };
  },
});

// Get unread message-type notifications (new-message, message-reply)
export const getUnreadMessageNotifications = query({
  args: { user_id: v.string() },
  handler: async (ctx, args) => {
    const notifications = await ctx.db
      .query("Notifications")
      .filter(q =>
        q.and(
          q.eq(q.field("user_id"), args.user_id),
          q.eq(q.field("status"), "unread"),
          q.or(
            q.eq(q.field("type"), "new-message"),
            q.eq(q.field("type"), "message-reply")
          )
        )
      )
      .order("desc")
      .collect();
    return { success: true, notifications };
  },
});

// Get unread non-message-type notifications (not new-message, not message-reply)
export const getUnreadOtherNotifications = query({
  args: { user_id: v.string() },
  handler: async (ctx, args) => {
    const notifications = await ctx.db
      .query("Notifications")
      .filter(q =>
        q.and(
          q.eq(q.field("user_id"), args.user_id),
          q.eq(q.field("status"), "unread"),
          q.not(q.or(
            q.eq(q.field("type"), "new-message"),
            q.eq(q.field("type"), "message-reply")
          ))
        )
      )
      .order("desc")
      .collect();
    return { success: true, notifications };
  },
});