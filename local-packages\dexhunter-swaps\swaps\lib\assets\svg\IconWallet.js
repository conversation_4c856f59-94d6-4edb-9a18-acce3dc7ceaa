import { jsx as t, jsxs as o } from "react/jsx-runtime";
import { memo as r } from "react";
const i = (e) => /* @__PURE__ */ t(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    width: "1em",
    height: "1em",
    viewBox: "0 0 216 176",
    ...e,
    children: /* @__PURE__ */ t("g", { id: "Page-1", stroke: "none", strokeWidth: 1, fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ o(
      "g",
      {
        id: "wallet-svgrepo-com-(2)",
        transform: "translate(8, 8)",
        stroke: "currentColor",
        children: [
          /* @__PURE__ */ t(
            "line",
            {
              x1: 40,
              y1: 40,
              x2: 80,
              y2: 40,
              id: "Path",
              strokeWidth: 15,
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          ),
          /* @__PURE__ */ t(
            "path",
            {
              d: "M188.333,50 L162.308,50 C144.465,50 130,63.431 130,80 C130,96.569 144.465,110 162.308,110 L188.333,110 C189.167,110 189.583,110 189.935,109.979 C195.328,109.65 199.623,105.662 199.977,100.654 C200,100.327 200,99.94 200,99.167 L200,60.833 C200,60.06 200,59.6726 199.977,59.346 C199.623,54.3384 195.328,50.3496 189.935,50.0214 C189.583,50 189.167,50 188.333,50 Z",
              id: "Path",
              strokeWidth: 15
            }
          ),
          /* @__PURE__ */ t(
            "path",
            {
              d: "M189.65,50 C188.873,31.277 186.366,19.7975 178.284,11.7157 C166.569,0 147.712,0 110,0 L80,0 C42.2876,0 23.4315,0 11.7157,11.7157 C0,23.4315 0,42.2876 0,80 C0,117.712 0,136.569 11.7157,148.284 C23.4315,160 42.2876,160 80,160 L110,160 C147.712,160 166.569,160 178.284,148.284 C186.366,140.203 188.873,128.723 189.65,110",
              id: "Path",
              strokeWidth: 15
            }
          ),
          /* @__PURE__ */ t(
            "line",
            {
              x1: 159.912,
              y1: 80,
              x2: 160.002,
              y2: 80,
              id: "Path",
              strokeWidth: 20,
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          )
        ]
      }
    ) })
  }
), s = r(i);
export {
  s as default
};
