import { jsxs as t, jsx as o } from "react/jsx-runtime";
import { memo as e } from "react";
const n = (r) => /* @__PURE__ */ t(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 10 10",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...r,
    children: [
      /* @__PURE__ */ o(
        "path",
        {
          d: "M1 1H9V9",
          stroke: "currentColor",
          strokeWidth: 1.6,
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M1 9L9 1",
          stroke: "currentColor",
          strokeWidth: 1.6,
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }
      )
    ]
  }
), d = e(n);
export {
  d as default
};
