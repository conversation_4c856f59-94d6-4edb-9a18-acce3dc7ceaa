import { jsxs as o, jsx as t } from "react/jsx-runtime";
import w from "../../store/useStore.js";
import { memo as A, useRef as U, useEffect as b, useMemo as v } from "react";
import { roundNumber as V, formatBalance as z, formatUsd as K, formatInput as P } from "../../utils/formatNumber.js";
import q from "../../assets/svg/IconChevronDown.js";
import { s as O } from "../../shallow-27fd7e97.js";
import { TokenImage as y } from "../../components/common/TokenImage.js";
import { useInputShortcuts as Q } from "../../hooks/useInputShortcuts.js";
import { useUsdPrices as $ } from "../hooks/useUsdPrices.js";
import { C as Y } from "../../index.esm-fb2f5862.js";
import { u as G } from "../../QueryClientProvider-6bcd4331.js";
import { cn as d } from "../../lib/utils.js";
import H from "../../hooks/useScreen.js";
import { I as J } from "../../IconTilde-bf643edd.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "./tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../components/ui/skeleton.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../utils/cardanoUtils.js";
import "../../index-ca8eb9e1.js";
import "../../config/axios.js";
import "../../axios-ddd885c5.js";
import "../../components/ui/tooltipDialog.js";
import "../../components/ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../components/ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../useQuery-febd7967.js";
import "../../query-013b86c3.js";
import "../hooks/useSwapAction.js";
import "../../hooks/useNotify.js";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../createReactComponent-ec43b511.js";
const W = () => {
  const a = U(null), { handleKeyDown: T } = Q(), k = G(), { isMobile: l } = H(), { tokenSellUsdPrice: N, userTokenSellBalance: m } = $(), {
    openTokenSearchModal: M,
    setSwapType: R,
    tokenBuy: I,
    tokenSell: s,
    sellAmount: i,
    setSellAmount: n,
    orderType: c,
    swapDetails: r,
    setInputMode: u,
    supportedTokens: f,
    autoFocus: x
  } = w(
    (e) => ({
      openTokenSearchModal: e.tokenSearchSlice.openModal,
      setSwapType: e.tokenSearchSlice.setSwapType,
      tokenBuy: e.swapSlice.tokenBuy,
      tokenSell: e.swapSlice.tokenSell,
      sellAmount: e.swapSlice.sellAmount,
      setSellAmount: e.swapSlice.setSellAmount,
      orderType: e.swapSlice.orderType,
      swapDetails: e.swapSlice.swapDetails,
      setInputMode: e.swapSlice.setInputMode,
      supportedTokens: e.tokenSearchSlice.supportedTokens,
      autoFocus: e.swapSlice.autoFocus
    }),
    O
  ), { defaultBuySize: g } = w((e) => e.globalSettingsSlice);
  b(() => {
    x && (l || C());
  }, [I, l, x]), b(() => {
    g && (s == null ? void 0 : s.token_id) === "" && n(g);
  }, []);
  const C = () => {
    var e;
    (e = a.current) == null || e.focus();
  }, _ = () => {
    m && (u("SELL"), n(parseFloat(m)));
  }, B = (e) => {
    n(P(e));
  }, L = () => {
    M(), R("SELL");
  }, p = v(() => r != null && r.total_input_without_slippage && c !== "STOP_LOSS" ? V(r == null ? void 0 : r.total_input_without_slippage) : i, [i, r, c]), D = (e) => {
    const h = e.target;
    h.value == 0 && setTimeout(() => {
      h.setSelectionRange(h.value.length, h.value.length);
    }, 0), k.cancelQueries({
      predicate: (E) => E.queryKey[0] === "swapDetails"
    }), u("SELL");
  }, F = (e) => {
    if (e.key === ",") {
      if (e.target.value.includes("."))
        return;
      e.preventDefault(), e.target.value = (i == null ? void 0 : i.toString()) + ".", e.target.selectionEnd = e.target.value.length, n(e.target.value);
      return;
    }
    T(e);
  }, j = (p == null ? void 0 : p.toString().length) > 12, S = v(() => f.length === 1, [f]);
  return /* @__PURE__ */ o(
    "div",
    {
      className: d(
        "dhs-flex dhs-flex-col dhs-px-3.5 dhs-py-3 @sm/appRoot:dhs-px-8 sm/appRoot:dhs-py-5 dhs-bg-containers dhs-rounded-3xl dhs-w-full dhs-border-containers dhs-border-2 dhs-min-h-[110px]"
      ),
      children: [
        /* @__PURE__ */ o("div", { className: "dhs-text-sm dhs-font-semibold dhs-flex dhs-justify-between dhs-w-full dhs-items-center sm:dhs-pb-[7px]", children: [
          /* @__PURE__ */ t("span", { className: "dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-lg sm:dhs-text-sm/none dhs-text-subText dhs-tracking-tight", children: "You sell" }),
          /* @__PURE__ */ o(
            "div",
            {
              className: "dhs-flex dhs-gap-2 dhs-items-center dhs-text-subText sm:dhs-text-sm/[17px]",
              onClick: _,
              children: [
                /* @__PURE__ */ o("span", { className: "sm:dhs-font-proximaSemiBold", children: [
                  "Balance: ",
                  z(m)
                ] }),
                /* @__PURE__ */ t("span", { className: "sm:dhs-font-proximaSemiBold dhs-text-accent hover:dhs-text-accent hover:dhs-opacity-90 dhs-cursor-pointer dhs-uppercase", children: "Max" })
              ]
            }
          )
        ] }),
        /* @__PURE__ */ o("div", { className: "dhs-flex dhs-items-center dhs-space-x-2 dhs-justify-between", children: [
          /* @__PURE__ */ o(
            "div",
            {
              className: d(
                "dhs-flex dhs-items-center dhs-p-2 -dhs-ml-2 sm:dhs-ml-0 sm:dhs-p-0 dhs-gap-1 dhs-rounded-lg dhs-cursor-pointer dhs-duration-200 dhs-ease-in-out hover:dhs-bg-background",
                S ? "dhs-pointer-events-none" : ""
              ),
              onClick: L,
              children: [
                /* @__PURE__ */ t(
                  y,
                  {
                    token: s,
                    isVerified: s == null ? void 0 : s.is_verified,
                    className: "dhs-w-[18px] dhs-h-[18px] @md/appRoot:dhs-hidden dhs-mr-1 dhs-rounded-full dhs-pb-0.5",
                    size: 18,
                    verifiedClass: "dhs-w-2.5 dhs-h-2.5"
                  }
                ),
                /* @__PURE__ */ t(
                  y,
                  {
                    token: s,
                    isVerified: s == null ? void 0 : s.is_verified,
                    className: "dhs-hidden dhs-w-[30px] dhs-h-[30px] @md/appRoot:dhs-block @md/appRoot:dhs-h-[30px] dhs-mr-1 dhs-rounded-full dhs-pb-0.5",
                    size: 30
                  }
                ),
                /* @__PURE__ */ t(
                  "span",
                  {
                    className: d(
                      "@md/appRoot:dhs-text-3xl @sm/appRoot:dhs-text-lg !dhs-leading-none dhs-text-mainText dhs-font-proximaMedium dhs-tracking-tighter",
                      (s == null ? void 0 : s.ticker.length) > 4 && "@md/appRoot:dhs-text-[18px]"
                    ),
                    children: s == null ? void 0 : s.ticker
                  }
                ),
                /* @__PURE__ */ t(
                  q,
                  {
                    width: 15,
                    height: 15,
                    className: d(
                      "dhs-text-mainText dhs-w-2.5 dhs-h-2.5 @md/appRoot:dhs-w-3 @md/appRoot:dhs-h-3",
                      S ? "dhs-hidden" : ""
                    )
                  }
                )
              ]
            }
          ),
          /* @__PURE__ */ t("div", { children: /* @__PURE__ */ t(
            Y,
            {
              type: "text",
              placeholder: "0",
              className: d(
                "dhs-px-0 dhs-border-none dhs-border-transparent focus-visible:dhs-border-transparent focus-visible:dhs-ring-0 focus-visible:dhs-ring-offset-0 dhs-text-right dhs-bg-transparent dhs-w-full sm:dhs-h-7.5 @md/appRoot:dhs-text-3xl dhs-text-lg dhs-text-mainText dhs-font-proximaMedium dhs-tracking-tighter",
                j && "dhs-text-md @md/appRoot:dhs-text-[22px]"
              ),
              onValueChange: B,
              ref: a,
              value: p,
              onKeyDown: F,
              onFocus: D,
              maxLength: 12,
              allowNegativeValue: !1,
              decimalsLimit: 8,
              intlConfig: { locale: "en-US", currency: "" }
            }
          ) })
        ] }),
        /* @__PURE__ */ o("div", { className: "dhs-text-sm dhs-flex dhs-justify-between dhs-w-full sm:dhs-pt-[7px]", children: [
          /* @__PURE__ */ t("span", { className: "dhs-text-base sm:dhs-text-sm/none dhs-text-subText dhs-font-proximaMedium", children: s == null ? void 0 : s.token_ascii }),
          /* @__PURE__ */ o("span", { className: "dhs-text-base sm:dhs-text-sm/none dhs-text-subText dhs-font-proximaMedium dhs-flex dhs-items-center", children: [
            /* @__PURE__ */ t(J, { width: 12, className: "sm:dhs-h-[14px]" }),
            K(N || 0)
          ] })
        ] })
      ]
    }
  );
}, is = A(W);
export {
  is as default
};
