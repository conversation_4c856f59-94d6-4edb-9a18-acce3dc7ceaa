import { jsxs as o, jsx as C } from "react/jsx-runtime";
import { memo as t } from "react";
const r = (e) => /* @__PURE__ */ o(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 26 27",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...e,
    children: [
      /* @__PURE__ */ C(
        "ellipse",
        {
          cx: 13,
          cy: 13.5,
          rx: 3.81833,
          ry: 3.75,
          stroke: "currentColor",
          strokeWidth: 2
        }
      ),
      /* @__PURE__ */ C(
        "path",
        {
          fillRule: "evenodd",
          clipRule: "evenodd",
          d: "M15.247 1.1903C14.7791 1 14.1861 1 13 1C11.8139 1 11.2209 1 10.7531 1.1903C10.1294 1.44404 9.6338 1.93073 9.37544 2.54329C9.2575 2.82293 9.21134 3.14813 9.19328 3.62249C9.16674 4.3196 8.80273 4.96486 8.18757 5.31366C7.57244 5.66245 6.82143 5.64943 6.19345 5.32345C5.76613 5.10163 5.45628 4.97828 5.15073 4.93878C4.48137 4.85223 3.80443 5.03036 3.26882 5.434C2.86711 5.73673 2.57059 6.24112 1.97756 7.24991C1.38452 8.25871 1.088 8.7631 1.0219 9.25614C0.93379 9.9135 1.11517 10.5783 1.52617 11.1044C1.71375 11.3445 1.97739 11.5462 2.38657 11.7987C2.9881 12.17 3.37514 12.8024 3.3751 13.5C3.37506 14.1976 2.98804 14.8299 2.38656 15.201C1.97733 15.4536 1.71365 15.6555 1.52604 15.8956C1.11505 16.4216 0.933663 17.0864 1.02179 17.7437C1.08787 18.2367 1.38439 18.7413 1.97743 19.75C2.57048 20.7587 2.867 21.2632 3.2687 21.5659C3.80431 21.9695 4.48125 22.1476 5.1506 22.0611C5.45613 22.0216 5.76596 21.8983 6.19326 21.6765C6.82128 21.3505 7.57233 21.3375 8.18752 21.6863C8.8027 22.0351 9.16674 22.6804 9.19328 23.3776C9.21135 23.8519 9.2575 24.1771 9.37544 24.4568C9.6338 25.0693 10.1294 25.556 10.7531 25.8097C11.2209 26 11.8139 26 13 26C14.1861 26 14.7791 26 15.247 25.8097C15.8706 25.556 16.3662 25.0693 16.6245 24.4568C16.7425 24.1771 16.7887 23.8519 16.8068 23.3775C16.8332 22.6804 17.1972 22.0351 17.8124 21.6863C18.4275 21.3374 19.1786 21.3505 19.8067 21.6765C20.234 21.8983 20.5438 22.0215 20.8492 22.061C21.5186 22.1476 22.1956 21.9695 22.7311 21.5659C23.1328 21.2631 23.4294 20.7587 24.0224 19.7499C24.6155 18.7411 24.912 18.2367 24.9781 17.7437C25.0662 17.0864 24.8848 16.4215 24.4738 15.8955C24.2862 15.6554 24.0225 15.4535 23.6133 15.201C23.0119 14.8299 22.6249 14.1975 22.6249 13.4999C22.6249 12.8023 23.0119 12.1701 23.6133 11.799C24.0226 11.5464 24.2863 11.3446 24.474 11.1044C24.8849 10.5784 25.0663 9.91359 24.9782 9.25621C24.9122 8.76319 24.6156 8.25879 24.0225 7.25C23.4295 6.24121 23.133 5.73681 22.7313 5.43409C22.1957 5.03045 21.5187 4.85231 20.8493 4.93886C20.5439 4.97836 20.2341 5.1017 19.8067 5.3235C19.1787 5.64949 18.4276 5.66253 17.8125 5.3137C17.1972 4.96489 16.8332 4.31958 16.8068 3.62243C16.7887 3.1481 16.7425 2.82291 16.6245 2.54329C16.3662 1.93073 15.8706 1.44404 15.247 1.1903Z",
          stroke: "currentColor",
          strokeWidth: 2
        }
      )
    ]
  }
), n = t(r);
export {
  n as default
};
