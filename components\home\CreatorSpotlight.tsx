'use client';

import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import { Star } from 'lucide-react';
import { motion, useAnimation } from 'framer-motion';
import { useExpandable } from '@/hooks/useExpandable';
import Link from 'next/link';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';
import { Profile } from '@/types/user';

// Constants
const SCROLL_INTERVAL = 10000; // 10 seconds
const DRAG_THRESHOLD = 30; // Minimum drag distance to trigger slide change

interface CreatorSpotlightProps {
  creators: Profile[];
}

export default memo(function CreatorSpotlight({ creators: initialCreators }: CreatorSpotlightProps) {
  const { isExpanded, toggleExpand, animatedHeight } = useExpandable(true);
  const contentRef = useRef<HTMLDivElement>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const slideRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [creators, setCreators] = useState<Profile[]>(initialCreators || []);
  const [currentPage, setCurrentPage] = useState(0);
  const [loadingProfile, setLoadingProfile] = useState(true);
  const [isVerifiedBadgeLoading, setIsVerifiedBadgeLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isDragging, setIsDragging] = useState(false);
  const [slideWidth, setSlideWidth] = useState(0);
  const [autoScrollPaused, setAutoScrollPaused] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const controls = useAnimation();

  // Display 3 per page
  const perPage = 6;

  const slides: Profile[][] = [];
  for (let i = 0; i < creators.length; i += perPage) {
    slides.push(creators.slice(i, i + perPage));
  }
  const totalPages = slides.length;

  const loadMoreCreators = useCallback(() => {
    if (loading || !hasMore) return;
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      if (creators.length >= 20) setHasMore(false);
    }, 1000);
  }, [creators.length, hasMore, loading]);

  const nextPage = useCallback(() => {
    const next = (currentPage + 1) % totalPages;
    setCurrentPage(next);
    controls.start({ x: -next * slideWidth });
  }, [currentPage, totalPages, slideWidth, controls]);

  const prevPage = useCallback(() => {
    const prev = (currentPage - 1 + totalPages) % totalPages;
    setCurrentPage(prev);
    controls.start({ x: -prev * slideWidth });
  }, [currentPage, totalPages, slideWidth, controls]);

  useEffect(() => {
    if (totalPages <= 1 || autoScrollPaused || isHovering) return;

    const interval = setInterval(() => {
      nextPage();
    }, SCROLL_INTERVAL);

    return () => clearInterval(interval);
  }, [nextPage, totalPages, autoScrollPaused, isHovering]);

  useEffect(() => {
    if (contentRef.current && isExpanded) {
      //const h = slideRefs.current[currentPage]?.scrollHeight || 0;
      const slideEl = slideRefs.current[currentPage];
      const h = slideEl
        ? Math.round(slideEl.offsetHeight)
        : 0;
      animatedHeight.set(h - 45);
    } else {
      animatedHeight.set(0);
    }
  }, [isExpanded, currentPage, creators, animatedHeight]);

  useEffect(() => {
    if (!contentRef.current) return;
    const ro = new ResizeObserver(() => {
      if (contentRef.current && isExpanded) {
        const h = slideRefs.current[currentPage]?.scrollHeight || 0;
        animatedHeight.set(h - 45);
      }
    });
    ro.observe(contentRef.current);
    return () => ro.disconnect();
  }, [isExpanded, currentPage, animatedHeight]);

  useEffect(() => {
    if (slideRefs.current[0]) {
      setSlideWidth(slideRefs.current[0]!.offsetWidth);
    }
  }, []);

  return (
    <div className="mb-3 w-full max-w-full overflow-hidden relative">
      <button
        onClick={toggleExpand}
        className="flex items-center justify-between w-full h-10 px-3 rounded-lg bg-transparent transition-colors text-gorilla-gray dark:text-white border border-solid border-gray-300 dark:border-white"
      >
        <div className="flex items-center text-sm">
          <Star className="h-4 w-4 mr-2" />
          <span>Spotlight</span>
        </div>
        <svg
          width={16}
          height={16}
          viewBox="0 0 20 20"
          className={`fill-current transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
        >
          <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
        </svg>
      </button>

      <motion.div className="overflow-hidden w-full" style={{ height: animatedHeight }}>
        <div ref={contentRef} className="mt-4 w-full relative">
          <div className="overflow-hidden w-full">
            <motion.div
              className="flex cursor-grab active:cursor-grabbing"
              ref={carouselRef}
              animate={controls}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              drag="x"
              dragConstraints={{
                left: -(totalPages - 1) * slideWidth,
                right: 0
              }}
              dragElastic={0.1}
              onDragStart={() => {
                setIsDragging(true);
                setAutoScrollPaused(true);
              }}
              onDragEnd={(_, info) => {
                setIsDragging(false);
                const offsetX = info.offset.x;
                if (offsetX > DRAG_THRESHOLD) prevPage();
                else if (offsetX < -DRAG_THRESHOLD) nextPage();
                else controls.start({ x: -currentPage * slideWidth });
                setTimeout(() => setAutoScrollPaused(false), 1000);
              }}
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
              onTouchStart={() => setIsHovering(true)}
              onTouchEnd={() => setTimeout(() => setIsHovering(false), 1000)}
            >
              {slides.map((slide, idx) => (
                <div
                  key={idx}
                  ref={el => (slideRefs.current[idx] = el)}
                  className="flex-shrink-0 w-full flex flex-col space-y-2"
                >
                  {slide.map(creator => (
                    <div key={creator.id} className="relative border border-solid border-gray-300 dark:border-white rounded-lg">
                      <div className="relative w-full h-[64px] overflow-hidden rounded-t-lg">
                        <img src={creator.coverBanner || '/images/user/default-banner.webp'} alt="cover" className="w-full h-full object-cover" />
                        <div className="absolute inset-0 bg-black/30" />
                      </div>
                      <div className="absolute bottom-6 left-4">
                        <Link href={`/user/${creator.username}`}>
                          <div className="relative w-20 h-20 rounded-full border-4 border-black/90 overflow-hidden">
                            <Skeleton loading={loadingProfile} width="100%" height="100%" borderRadius="50%">
                              <Image
                                src={creator.avatar || '/images/user/default-avatar.webp'}
                                alt={creator.name}
                                fill
                                className="object-cover"
                                onLoad={() => setLoadingProfile(false)}
                              />
                            </Skeleton>
                          </div>
                        </Link>
                      </div>
                      <div className="flex justify-end pb-2.5 px-4 w-full">
                        <div className="flex flex-col items-start justify-center w-[69%]">
                          <Link href={`/user/${creator.username}`}>
                            <h3 className="text-gorilla-gray dark:text-white font-bold text-lg truncate w-full">
                              {creator.name} {creator.isVerified && (
                                <Skeleton loading={isVerifiedBadgeLoading} width="16px" height="16px" className="inline-block">
                                  <img
                                    src="/images/user/verified.png"
                                    alt="Verified"
                                    onLoad={() => setIsVerifiedBadgeLoading(false)}
                                  />
                                </Skeleton>
                              )}
                            </h3>
                          </Link>
                          <Link href={`/user/${creator.username}`}>
                            <p className="text-gray-300 text-sm truncate w-full">@{creator.username}</p>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}

                  {slide.length < perPage &&
                    Array.from({ length: perPage - slide.length }).map((_, i) => (
                      <div key={i} className="h-40 bg-transparent"></div>
                    ))
                  }
                </div>
              ))}

              {loading && (
                <div className="flex-shrink-0 w-full flex flex-col space-y-2">
                  {Array.from({ length: perPage }).map((_, i) => (
                    <div key={i} className="h-40 bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse"></div>
                  ))}
                </div>
              )}
            </motion.div>
          </div>

          {/* Carousel indicators */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-2 gap-1 mb-2">
              {Array.from({ length: totalPages }).map((_, i) => (
                <button
                  key={i}
                  className={`h-1.5 rounded-full transition-all ${currentPage === i ? 'w-4 bg-gray-800 dark:bg-white' : 'w-1.5 bg-gray-300 dark:bg-gray-600'}`}
                  onClick={() => setCurrentPage(i)}
                />
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}, (a, b) => JSON.stringify(a.creators) === JSON.stringify(b.creators));