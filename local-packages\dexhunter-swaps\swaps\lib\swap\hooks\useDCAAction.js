import n from "../../store/useStore.js";
import { server as m } from "../../config/axios.js";
import { useNotify as M } from "../../hooks/useNotify.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
import "react/jsx-runtime";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../hooks/useScreen.js";
const wt = () => {
  const { notify: u } = M(), {
    tokenBuy: p,
    tokenSell: a,
    sellAmount: d,
    setIsTransactionLoading: c,
    estimationError: S,
    setBuyAmount: C,
    setSellAmount: h,
    setSwapDetails: x,
    swapDetails: D,
    setDexBlacklist: k,
    setIsSwapSubmitted: F,
    inputMode: T,
    intervalLength: v,
    buyInterval: N,
    timesAmount: I
  } = n((t) => t.swapSlice), {
    api: i,
    userAddress: L
  } = n((t) => t.walletSlice), { slippage: B, isAutomaticSlippage: E } = n((t) => t.swapSettingsSlice), { balance: g, selectedWallet: P } = n((t) => t.walletSlice), { partnerName: Y, partnerCode: j } = n((t) => t.globalSettingsSlice), J = () => {
    const r = parseFloat(d);
    return T === "BUY" ? parseFloat(D.total_input_without_slippage) : r + 15 > g && (a == null ? void 0 : a.token_id) === "" ? parseFloat(r - 15) : parseFloat(r);
  };
  return { dcaToken: async () => {
    var r, f, w, b;
    if (c(!0), d === 0 || S) {
      c(!1);
      return;
    }
    let t = {
      sign: null,
      tx: "",
      payload: null,
      walletName: P,
      ada_balance: g,
      err: null,
      step: "pre-swap",
      signatures: null
    };
    try {
      console.time("swap");
      let e = E ? 5 : B;
      const y = {
        user_address: L,
        token_in: a == null ? void 0 : a.token_id,
        token_out: (p == null ? void 0 : p.token_id) || "",
        amount_in: J(),
        interval: N,
        interval_length: v,
        slippage: e,
        cycles: I,
        referrer: Y
      };
      t.payload = y;
      const { data: s } = await m.post("/dca/create", y, {
        headers: {
          "X-Partner-Id": j
        }
      });
      t.swap = s, t.step = "pre-sign";
      const _ = await (i == null ? void 0 : i.signTx(s == null ? void 0 : s.cbor, !0));
      t.signatures = _;
      const { data: l } = await m.post("/swap/sign", {
        txCbor: s == null ? void 0 : s.cbor,
        signatures: _
      });
      t.sign = l, t.step = "pre-submit";
      const A = await (i == null ? void 0 : i.submitTx(l == null ? void 0 : l.cbor));
      t.tx = A, t.step = "after-submit";
      try {
        await m.post("/marking/submit", {
          tx_hash: A,
          order_type: "DCA"
        });
      } catch (o) {
        console.log(o), t.marking_err = ((r = o == null ? void 0 : o.response) == null ? void 0 : r.data) || (o == null ? void 0 : o.message) || (o == null ? void 0 : o.info);
      }
      F(!0), u({
        type: "success",
        title: "DCA Create Success",
        desc: "Your DCA has been placed successfully",
        actionName: "View DCA"
      }), h(null), C(0), x(null), k([]);
    } catch (e) {
      if (console.log(e), console.log(e.message), t.err = ((f = e == null ? void 0 : e.response) == null ? void 0 : f.data) || (e == null ? void 0 : e.message) || (e == null ? void 0 : e.info), (w = e == null ? void 0 : e.message) != null && w.toLowerCase().includes("declined") || (b = e == null ? void 0 : e.info) != null && b.toLowerCase().includes("declined"))
        return;
      u({
        type: "error",
        title: "Error placing order",
        desc: "There was an error placing your order",
        actionCallback: () => {
          navigator.clipboard.writeText(JSON.stringify(t));
        }
      });
    } finally {
      c(!1);
    }
  } };
};
export {
  wt as useDCAAction
};
