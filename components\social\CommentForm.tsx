import React, { useState, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { createComment } from '@/redux/slices/commentsSlice';
import { Button } from '@/components/ui/button';
import { EmojiPicker } from '@/components/ui/chat/emoji-picker';
import { Image as ImageIcon, Smile, Mic, SendHorizontal } from 'lucide-react';
import Tippy from '@tippyjs/react';

interface CommentFormProps {
  postId: string;
  onCommentAdded: (comment: any) => void;
}

const CommentForm: React.FC<CommentFormProps> = ({ postId, onCommentAdded }) => {
  const dispatch = useDispatch<any>();
  const [commentText, setCommentText] = useState('');
  const [mediaFile, setMediaFile] = useState<File | null>(null);
  const [mediaPreview, setMediaPreview] = useState<string | null>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (commentText.trim() || mediaFile) {
      const formData = new FormData();
      formData.append('content', commentText);
      if (mediaFile) {
        formData.append('media', mediaFile);
      }

      try {
        const result = await dispatch(createComment({
          postId,
          commentData: formData
        })).unwrap();

        onCommentAdded(result);
        setCommentText('');
        setMediaFile(null);
        setMediaPreview(null);
      } catch (error: any) {
        console.error('Failed to create comment:', error);
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setMediaFile(file);
      const url = URL.createObjectURL(file);
      setMediaPreview(url);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setCommentText(prev => prev + emoji);
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="flex items-center justify-center gap-2 w-full px-3 pb-2 bg-transparent rounded-lg">
        <div className="flex items-center gap-2 w-full bg-transparent rounded-full px-4 py-2 border border-solid border-gray-300 dark:border-white">
          <input
            value={commentText}
            onChange={(e) => setCommentText(e.target.value)}
            placeholder="Write Message"
            className="flex-grow bg-transparent border-none focus:outline-none text-gorilla-gray dark:text-white placeholder-gray-400 dark:placeholder-gray-400 placeholder:text-sm"
          />
          <button
            type="submit"
            disabled={!commentText.trim() && !mediaFile}
            className="text-blue-gray hover:text-turquoise dark:text-white dark:hover:text-turquoise disabled:opacity-50"
          >
            <SendHorizontal className="h-6 w-6" />
          </button>
        </div>

        <div className="flex items-center gap-2">
          <Tippy content="Insert Emoji" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
            <EmojiPicker onChange={handleEmojiSelect} className="h-6 w-6 text-blue-gray hover:text-turquoise dark:text-white dark:hover:text-turquoise" />
          </Tippy>
        </div>
      </div>

      {mediaPreview && (
        <div className="relative mt-2">
          <img src={mediaPreview} alt="Preview" className="max-h-40 rounded" />
          <Button
            type="button"
            variant="destructive"
            size="sm"
            className="absolute top-2 right-2"
            onClick={() => {
              setMediaFile(null);
              setMediaPreview(null);
            }}
          >
            Remove
          </Button>
        </div>
      )}

      <input
        type="file"
        ref={imageInputRef}
        className="hidden"
        accept="image/*"
        onChange={handleFileSelect}
      />
    </form>
  );
};
export default CommentForm;
