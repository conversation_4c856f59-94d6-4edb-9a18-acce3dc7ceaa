import { internalMutation } from "../_generated/server";

// Internal mutation to backfill auth_user_id for all Accounts
export const backfillAuthUserIds = internalMutation(async (ctx) => {
  // Get all accounts
  const accounts = await ctx.db.query("Accounts").collect();

  for (const account of accounts) {
    // Skip if already set
    if (account.auth_user_id) continue;

    // Find the Convex Auth user for this account
    const authUser = await ctx.db
      .query("users")
      .withIndex("by_account_id", (q) => q.eq("accountId", account._id))
      .first();

    if (authUser) {
      await ctx.db.patch(account._id, { auth_user_id: authUser._id });
      // Optionally log or track progress
    }
  }
});

// To run this, call backfillAuthUserIds from the Convex dashboard or an internal action.