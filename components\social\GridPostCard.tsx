'use client';

import React, { memo, useEffect, useState } from 'react';
import { Lock, Play } from 'lucide-react';
import { Post } from '@/types/post';
import PhotoGridItem from '@/components/media/PhotoGridItem';
import VideoGridItem from '@/components/media/VideoGridItem';
import { useTrackPostView } from '@/hooks/convexHooks';
import { useUser } from '@/hooks/useUser';

interface GridPostCardProps {
  post: Post;
  panel?: string;
  onOpenLightbox: (post: Post, mediaArray: any[], initialIndex: number) => void;
}

// Helper function to parse duration from various formats
const parseDuration = (duration: any): number => {
  if (typeof duration === 'number') {
    return duration;
  }

  if (typeof duration === 'string') {
    // Handle "mm:ss" format (e.g., "00:59")
    const match = duration.match(/^(\d+):(\d{2})$/);
    if (match) {
      const minutes = parseInt(match[1], 10);
      const seconds = parseInt(match[2], 10);
      return minutes * 60 + seconds;
    }

    // Handle other string formats by trying to parse as number
    const parsed = parseFloat(duration);
    if (!isNaN(parsed)) {
      return parsed;
    }
  }

  return 0; // Default fallback
};

const GridPostCard = memo(({
  post,
  panel,
  onOpenLightbox
}: GridPostCardProps) => {
  // Get media from either direct properties or media array
  const mediaItems = post.media || [];
  const mediaCount = mediaItems.length;
  const firstMedia = mediaItems[0] || {};
  const mediaUrl = firstMedia.mediaUrl || '/images/user/default-avatar.webp';
  const thumbnailUrl = firstMedia.thumbnailUrl || mediaUrl;
  const mediaType = firstMedia.mediaType;

  // Determine if the post is a video based on mediaType or panel
  const isVideo = mediaType?.startsWith('video/') || panel === 'videos';

  // Get duration from post metadata or first media item
  const duration = post.duration || firstMedia.duration;
  const durationInSeconds = parseDuration(duration);
  const isClip = mediaType?.startsWith('video/') && durationInSeconds < 60;

  // Convert post to photo/video format for the respective components
  let photoData = {
    id: post.id,
    thumbnail: thumbnailUrl,
    title: post.content || 'Untitled',
    isPaid: post.isPaid,
    price: post.price,
    views: post.views
  };
  // For audio posts, use a default audio thumbnail
  if (mediaType?.startsWith('audio/')) {
    photoData = {
      ...photoData,
      thumbnail: '/images/default-audio.webp',
    };
  }

  const videoData = {
    ...photoData,
    duration: duration,
    isPaid: post.isPaid,
    price: post.price,
  };

  const trackPostView = useTrackPostView();
  const { token: sessionData } = useUser();
  const isLoggedIn = !!sessionData;

  // Helper to fetch IP address
  async function fetchIpAddress(): Promise<string> {
    try {
      const res = await fetch('https://api.ipify.org?format=json');
      const data = await res.json();
      return data.ip;
    } catch {
      return '0.0.0.0';
    }
  }

  // Handlers that maintain the same functionality
  const handleView = async () => {
    // Track view
    const postId = post.id;
    const contentType = post.contentType || 'post';
    const userAgent = typeof window !== 'undefined' ? window.navigator.userAgent : '';
    const ipAddress = await fetchIpAddress();
    let sessionId: string | undefined = undefined;
    if (!isLoggedIn) {
      sessionId = btoa(`${ipAddress}-${userAgent}`);
    }
    try {
      await trackPostView({
        contentId: postId,
        contentType,
        ipAddress,
        userAgent,
        sessionId,
      });
    } catch (error) {
      // Optionally handle error (e.g., log or toast)
    }
    onOpenLightbox(post, mediaItems, 0);
  };

  return (
    <div className="h-full relative">
      {post.isPaid && (
        <div className="absolute inset-0 flex flex-col gap-3 items-center justify-center bg-black/50 z-[1]">
          <Lock className="text-white h-8 w-8" />
          <span className="bg-black/70 text-white text-xs px-2 py-1 rounded">
            {post.price || 'Premium'}
          </span>
        </div>
      )}

      {isVideo ? (
        <VideoGridItem
          video={videoData}
          onView={handleView}
          isClip={isClip}
        />
      ) : mediaType === 'image/gif' ? (
        // Show GIF as a static image (paused)
        <PhotoGridItem
          photo={{
            ...photoData,
            // Use thumbnail if available, otherwise fallback to GIF itself
            thumbnail: firstMedia.thumbnailUrl || firstMedia.mediaUrl || '/images/user/default-avatar.webp',
          }}
          onView={handleView}
          mediaCount={mediaCount}
          fileType={mediaType}
        />
      ) : mediaType?.startsWith('audio/') ? (
        <div className="relative w-full h-full">
          <PhotoGridItem
            photo={photoData}
            onView={handleView}
            mediaCount={mediaCount}
            fileType={mediaType}
          />
          {/* Play button overlay (same as VideoGridItem) */}
          <div className="absolute inset-0 bg-black/10 flex items-center justify-center z-[1] rounded-lg">
            <div className="relative">
              <div className="absolute inset-0 bg-turquoise rounded-full opacity-30 animate-ping-slow"></div>
              <button
                className="relative w-12 h-12 bg-turquoise rounded-full flex items-center justify-center hover:bg-turquoise-hover transition-colors"
              >
                <Play className="h-6 w-6 text-white fill-white ml-1" />
              </button>
            </div>
          </div>
        </div>
      ) : (
        <PhotoGridItem
          photo={photoData}
          onView={handleView}
          mediaCount={mediaCount}
          fileType={mediaType}
        />
      )}
    </div>
  );
}, (prevProps, nextProps) =>
  prevProps.post.id === nextProps.post.id
);
export default GridPostCard;