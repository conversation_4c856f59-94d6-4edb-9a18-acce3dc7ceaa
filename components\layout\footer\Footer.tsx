'use client';

import Link from "next/link";
import { useState } from "react";
import dynamic from 'next/dynamic';
import { IoShieldCheckmarkOutline } from "react-icons/io5";

// Dynamically import the modal to avoid SSR issues
const CookiePreferencesModal = dynamic(() => import('@/components/CookiePreferencesModal'), {
  ssr: false
});

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const [isPreferencesModalOpen, setIsPreferencesModalOpen] = useState(false);

  return (
    <footer className="bg-white dark:bg-[#18181b] border-solid border-b-0 border-l-0 border-r-0 border-t border-gray-300 dark:border-white/50 pt-12 pb-8">
      <div className="container mx-auto px-4">
        {/* Main footer content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-10">
          {/* About section */}
          <div>
            <h3 className="text-turquoise font-semibold text-lg mb-4">INTRODUCTION</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4 max-w-[350px]">
              Sugar Club is a social network platform for adult content creators. Our goal is to build a safe, fun & vibrant community for content creators and their fans.
              <Link href="/partners" className="text-turquoise underline ml-1">
                View our partners
              </Link>
            </p>
          </div>

          {/* Contact section */}
          <div>
            <h3 className="text-turquoise font-semibold text-lg mb-4 uppercase">Contact</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4 max-w-[250px]">
              For press, ad spaces, CVs, partnerships, magazine features, sponsorship deals and more: <Link href="/" className="text-turquoise underline capitalize">contact us</Link>
            </p>
          </div>

          {/* Community section */}
          <div>
            <h3 className="text-turquoise font-semibold text-lg mb-4">COMMUNITY</h3>
            <div className="flex flex-col space-y-[0.93px]">
              <a href="https://feetpicsmerch.com/products/feetfinder-welcome-kit" target="_blank" rel="noopener noreferrer nofollow" className="flex items-center justify-between text-gray-500 dark:text-gray-400 hover:text-turquoise w-full min-h-[22px]">
                Store
              </a>

              <Link href="https://www.sextpanther.com/top-models" className="flex items-center justify-between text-gray-500 dark:text-gray-400 hover:text-turquoise w-full min-h-[22px]">
                Awards
              </Link>

              <Link href="/contests" className="flex items-center justify-between text-gray-500 dark:text-gray-400 hover:text-turquoise w-full min-h-[22px]">
                Contests
              </Link>

              <Link href="/magazine" className="flex items-center justify-between text-gray-500 dark:text-gray-400 hover:text-turquoise w-full min-h-[22px]">
                Magazine
              </Link>

            </div>
          </div>

          {/* Help & support section */}
          <div>
            <h3 className="text-turquoise font-semibold text-lg mb-4 uppercase">Support</h3>
            <div className="flex flex-col space-y-[0.93px]">
              <Link href="/help-support/help-center" target="_blank" rel="noopener noreferrer" className="flex items-center justify-between text-gray-500 dark:text-gray-400 hover:text-turquoise w-full min-h-[22px]">
                <span>Help Center</span>
                <span className="flex items-center justify-center p-1 rounded-md bg-[var(--turquoise)] text-white transition-colors relative -top-1">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className="w-3 h-3 fill-white" width="24" height="24">
                    <path d="M228.3 469.1L47.6 300.4c-4.2-3.9-8.2-8.1-11.9-12.4l87 0c22.6 0 43-13.6 51.7-34.5l10.5-25.2 49.3 109.5c3.8 8.5 12.1 14 21.4 14.1s17.8-5 22-13.3L320 253.7l1.7 3.4c9.5 19 28.9 31 50.1 31l104.5 0c-3.7 4.3-7.7 8.5-11.9 12.4L283.7 469.1c-7.5 7-17.4 10.9-27.7 10.9s-20.2-3.9-27.7-10.9zM503.7 240l-132 0c-3 0-5.8-1.7-7.2-4.4l-23.2-46.3c-4.1-8.1-12.4-13.3-21.5-13.3s-17.4 5.1-21.5 13.3l-41.4 82.8L205.9 158.2c-3.9-8.7-12.7-14.3-22.2-14.1s-18.1 5.9-21.8 14.8l-31.8 76.3c-1.2 3-4.2 4.9-7.4 4.9L16 240c-2.6 0-5 .4-7.3 1.1C3 225.2 0 208.2 0 190.9l0-5.8c0-69.9 50.5-129.5 119.4-141C165 36.5 211.4 51.4 244 84l12 12 12-12c32.6-32.6 79-47.5 124.6-39.9C461.5 55.6 512 115.2 512 185.1l0 5.8c0 16.9-2.8 33.5-8.3 49.1z" />
                  </svg>
                </span>
              </Link>

              <Link href="/terms" target="_blank" rel="noopener noreferrer" className="flex items-center justify-between text-gray-500 dark:text-gray-400 hover:text-turquoise w-full min-h-[22px]">
                <span>Terms Of Use</span>
                <span className="flex items-center justify-center p-1 rounded-md bg-[var(--turquoise)] text-white transition-colors relative -top-1">
                  <IoShieldCheckmarkOutline className="w-3 h-3 text-white" />
                </span>
              </Link>

              <Link href="/help-support/trust-safety" target="_blank" rel="noopener noreferrer" className="flex items-center justify-between text-gray-500 dark:text-gray-400 hover:text-turquoise w-full min-h-[22px]">
                <span>Trust & Safety</span>
                <span className="flex items-center justify-center p-1 rounded-md bg-[var(--turquoise)] text-white transition-colors relative -top-1">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className="w-3 h-3 fill-white" width="24" height="24">
                    <path d="M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0z" />
                  </svg>
                </span>
              </Link>

              <Link href="/help-support/dmca-request" target="_blank" rel="noopener noreferrer" className="flex items-center justify-between text-gray-500 dark:text-gray-400 hover:text-turquoise w-full min-h-[22px]">
                <span>DMCA Request</span>
                <span className="flex items-center justify-center p-1 rounded-md bg-[var(--turquoise)] text-white transition-colors relative -top-1">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className="w-3 h-3 fill-white" width="24" height="24">
                    <path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM199.4 312.6c-31.2-31.2-31.2-81.9 0-113.1s81.9-31.2 113.1 0c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9c-50-50-131-50-181 0s-50 131 0 181s131 50 181 0c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0c-31.2 31.2-81.9 31.2-113.1 0z" />
                  </svg>
                </span>
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom footer */}
        <div className="border-t border-gray-200 dark:border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          {/* Copyright */}
          <div className="flex items-center mb-4 md:mb-0">
            <p className="text-gray-500 dark:text-gray-400">© {currentYear} Sugar Club. All Rights Reserved.</p>
          </div>

          {/* Logo */}
          <div className="flex space-x-4 text-gray-500 dark:text-gray-400">
            <img className="w-[120px] mr-4" src="/images/logo.webp" alt="Sugar Club Logo" />
          </div>

          {/* Social links */}
          <div className="flex space-x-2 text-gray-500 dark:text-gray-400">
            <Link href="/privacy" className="hover:text-turquoise">
              Privacy Policy
            </Link>
            <span>|</span>
            <Link href="/cookies" className="hover:text-turquoise">
              Cookies Policy
            </Link>
            <span>|</span>
            <button
              onClick={() => setIsPreferencesModalOpen(true)}
              className="hover:text-turquoise cursor-pointer"
            >
              Cookie Settings
            </button>
          </div>
        </div>
      </div>

      {isPreferencesModalOpen && (
        <CookiePreferencesModal
          isOpen={isPreferencesModalOpen}
          onClose={() => setIsPreferencesModalOpen(false)}
        />
      )}
    </footer>
  );
};
export default Footer;