import { jsx as x } from "react/jsx-runtime";
import * as R from "react";
import { forwardRef as v, useState as q, useRef as g, createElement as i, useEffect as B } from "react";
import { a as I, _ as h } from "../../index-1c873780.js";
import { a as M, $ as T } from "../../index-c7156e07.js";
import { $ as H } from "../../index-563d1ed8.js";
import { $ as j } from "../../index-6460524a.js";
import { $ as A } from "../../index-bcfeaad9.js";
import { $ as C } from "../../index-c8f2666b.js";
import { cn as w } from "../../lib.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const S = "Switch", [O, ne] = H(S), [z, D] = O(S), F = /* @__PURE__ */ v((e, c) => {
  const { __scopeSwitch: t, name: o, checked: r, defaultChecked: b, required: s, disabled: n, value: d = "on", onCheckedChange: l, ...m } = e, [a, u] = q(null), P = I(
    c,
    (p) => u(p)
  ), $ = g(!1), k = a ? !!a.closest("form") : !0, [f = !1, E] = M({
    prop: r,
    defaultProp: b,
    onChange: l
  });
  return /* @__PURE__ */ i(z, {
    scope: t,
    checked: f,
    disabled: n
  }, /* @__PURE__ */ i(C.button, h({
    type: "button",
    role: "switch",
    "aria-checked": f,
    "aria-required": s,
    "data-state": y(f),
    "data-disabled": n ? "" : void 0,
    disabled: n,
    value: d
  }, m, {
    ref: P,
    onClick: T(e.onClick, (p) => {
      E(
        (N) => !N
      ), k && ($.current = p.isPropagationStopped(), $.current || p.stopPropagation());
    })
  })), k && /* @__PURE__ */ i(W, {
    control: a,
    bubbles: !$.current,
    name: o,
    value: d,
    checked: f,
    required: s,
    disabled: n,
    style: {
      transform: "translateX(-100%)"
    }
  }));
}), L = "SwitchThumb", U = /* @__PURE__ */ v((e, c) => {
  const { __scopeSwitch: t, ...o } = e, r = D(L, t);
  return /* @__PURE__ */ i(C.span, h({
    "data-state": y(r.checked),
    "data-disabled": r.disabled ? "" : void 0
  }, o, {
    ref: c
  }));
}), W = (e) => {
  const { control: c, checked: t, bubbles: o = !0, ...r } = e, b = g(null), s = j(t), n = A(c);
  return B(() => {
    const d = b.current, l = window.HTMLInputElement.prototype, a = Object.getOwnPropertyDescriptor(l, "checked").set;
    if (s !== t && a) {
      const u = new Event("click", {
        bubbles: o
      });
      a.call(d, t), d.dispatchEvent(u);
    }
  }, [
    s,
    t,
    o
  ]), /* @__PURE__ */ i("input", h({
    type: "checkbox",
    "aria-hidden": !0,
    defaultChecked: t
  }, r, {
    tabIndex: -1,
    ref: b,
    style: {
      ...e.style,
      ...n,
      position: "absolute",
      pointerEvents: "none",
      opacity: 0,
      margin: 0
    }
  }));
};
function y(e) {
  return e ? "checked" : "unchecked";
}
const _ = F, X = U, G = R.forwardRef(({ className: e, ...c }, t) => /* @__PURE__ */ x(
  _,
  {
    className: w(
      "peer inline-flex h-[40px] w-[84px] shrink-0 cursor-pointer items-center rounded-[10px] border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",
      e
    ),
    ...c,
    ref: t,
    children: /* @__PURE__ */ x(
      X,
      {
        className: w(
          "pointer-events-none block h-9 w-10 rounded-[10px] bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-10 data-[state=unchecked]:translate-x-0"
        )
      }
    )
  }
));
G.displayName = _.displayName;
export {
  G as Switch
};
