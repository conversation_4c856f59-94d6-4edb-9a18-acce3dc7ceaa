import { jsxs as t, jsx as o } from "react/jsx-runtime";
import { memo as e } from "react";
const i = (r) => /* @__PURE__ */ t(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 18 17",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...r,
    children: [
      /* @__PURE__ */ o(
        "path",
        {
          d: "M10.5652 2.17394C9.92033 0.884099 8.07967 0.884104 7.43475 2.17394L1.85053 13.3424C1.26874 14.506 2.11486 15.875 3.41578 15.875H14.5842C15.8851 15.875 16.7313 14.5059 16.1495 13.3424L10.5652 2.17394Z",
          stroke: "currentColor",
          strokeWidth: 1.75
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M9 7.125V9.75",
          stroke: "currentColor",
          strokeWidth: 1.75,
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }
      ),
      /* @__PURE__ */ o("circle", { cx: 9, cy: 12.375, r: 0.875, fill: "currentColor" })
    ]
  }
), l = e(i);
export {
  l as default
};
