import { jsx as k } from "react/jsx-runtime";
import * as M from "react";
import { forwardRef as w, useState as q, useRef as C, useEffect as P, createElement as l } from "react";
import { a as B, _ as v } from "../../index-1c873780.js";
import { $ as L } from "../../index-563d1ed8.js";
import { a as O, $ as E } from "../../index-c7156e07.js";
import { $ as T } from "../../index-6460524a.js";
import { $ as A } from "../../index-bcfeaad9.js";
import { $ as K } from "../../index-5116e957.js";
import { $ as I } from "../../index-c8f2666b.js";
import { cn as _ } from "../../lib.js";
import { c as H } from "../../createLucideIcon-7a477fa6.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const X = H("Check", [
  ["polyline", { points: "20 6 9 17 4 12", key: "10jjfj" }]
]), N = "Checkbox", [z, be] = L(N), [F, G] = z(N), J = /* @__PURE__ */ w((e, s) => {
  const { __scopeCheckbox: t, name: a, checked: b, defaultChecked: c, required: p, disabled: d, value: i = "on", onCheckedChange: $, ...y } = e, [r, h] = q(null), S = B(
    s,
    (o) => h(o)
  ), m = C(!1), g = r ? !!r.closest("form") : !0, [f = !1, x] = O({
    prop: b,
    defaultProp: c,
    onChange: $
  }), D = C(f);
  return P(() => {
    const o = r == null ? void 0 : r.form;
    if (o) {
      const u = () => x(D.current);
      return o.addEventListener("reset", u), () => o.removeEventListener("reset", u);
    }
  }, [
    r,
    x
  ]), /* @__PURE__ */ l(F, {
    scope: t,
    state: f,
    disabled: d
  }, /* @__PURE__ */ l(I.button, v({
    type: "button",
    role: "checkbox",
    "aria-checked": n(f) ? "mixed" : f,
    "aria-required": p,
    "data-state": R(f),
    "data-disabled": d ? "" : void 0,
    disabled: d,
    value: i
  }, y, {
    ref: S,
    onKeyDown: E(e.onKeyDown, (o) => {
      o.key === "Enter" && o.preventDefault();
    }),
    onClick: E(e.onClick, (o) => {
      x(
        (u) => n(u) ? !0 : !u
      ), g && (m.current = o.isPropagationStopped(), m.current || o.stopPropagation());
    })
  })), g && /* @__PURE__ */ l(V, {
    control: r,
    bubbles: !m.current,
    name: a,
    value: i,
    checked: f,
    required: p,
    disabled: d,
    style: {
      transform: "translateX(-100%)"
    }
  }));
}), Q = "CheckboxIndicator", U = /* @__PURE__ */ w((e, s) => {
  const { __scopeCheckbox: t, forceMount: a, ...b } = e, c = G(Q, t);
  return /* @__PURE__ */ l(K, {
    present: a || n(c.state) || c.state === !0
  }, /* @__PURE__ */ l(I.span, v({
    "data-state": R(c.state),
    "data-disabled": c.disabled ? "" : void 0
  }, b, {
    ref: s,
    style: {
      pointerEvents: "none",
      ...e.style
    }
  })));
}), V = (e) => {
  const { control: s, checked: t, bubbles: a = !0, ...b } = e, c = C(null), p = T(t), d = A(s);
  return P(() => {
    const i = c.current, $ = window.HTMLInputElement.prototype, r = Object.getOwnPropertyDescriptor($, "checked").set;
    if (p !== t && r) {
      const h = new Event("click", {
        bubbles: a
      });
      i.indeterminate = n(t), r.call(i, n(t) ? !1 : t), i.dispatchEvent(h);
    }
  }, [
    p,
    t,
    a
  ]), /* @__PURE__ */ l("input", v({
    type: "checkbox",
    "aria-hidden": !0,
    defaultChecked: n(t) ? !1 : t
  }, b, {
    tabIndex: -1,
    ref: c,
    style: {
      ...e.style,
      ...d,
      position: "absolute",
      pointerEvents: "none",
      opacity: 0,
      margin: 0
    }
  }));
};
function n(e) {
  return e === "indeterminate";
}
function R(e) {
  return n(e) ? "indeterminate" : e ? "checked" : "unchecked";
}
const j = J, W = U, Y = M.forwardRef(({ className: e, ...s }, t) => /* @__PURE__ */ k(
  j,
  {
    ref: t,
    className: _(
      "dhs-peer dhs-h-4 dhs-w-4 dhs-shrink-0 dhs-rounded-[6px] dhs-border dhs-border-primary dhs-ring-offset-background focus-visible:dhs-outline-none focus-visible:dhs-ring-2 focus-visible:dhs-ring-ring focus-visible:dhs-ring-offset-2 disabled:dhs-cursor-not-allowed disabled:dhs-opacity-50 data-[state=checked]:dhs-bg-accent data-[state=checked]:dhs-text-mainText",
      e
    ),
    style: {
      border: "2px solid #414853"
    },
    ...s,
    children: /* @__PURE__ */ k(
      W,
      {
        className: _(
          "dhs-flex dhs-items-center dhs-justify-center dhs-text-current"
        ),
        children: /* @__PURE__ */ k(X, { className: "dhs-h-4 dhs-w-4 dhs-text-mainText" })
      }
    )
  }
));
Y.displayName = j.displayName;
export {
  Y as Checkbox
};
