"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface ScrollAreaProps extends React.HTMLAttributes<HTMLDivElement> {
  viewportRef?: React.RefObject<HTMLDivElement>;
  orientation?: "vertical" | "horizontal" | "both";
}

const ScrollArea = React.forwardRef<HTMLDivElement, ScrollAreaProps>(
  ({ className, children, viewportRef, orientation = "vertical", ...props }, ref) => {
    const internalViewportRef = React.useRef<HTMLDivElement>(null);
    const actualViewportRef = viewportRef || internalViewportRef;

    return (
      <div
        ref={ref}
        className={cn(
          "relative overflow-hidden",
          className
        )}
        {...props}
      >
        <div
          ref={actualViewportRef}
          className={cn(
            "h-full w-full rounded-[inherit]",
            orientation === "vertical" || orientation === "both"
              ? "overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent"
              : "overflow-y-hidden",
            orientation === "horizontal" || orientation === "both"
              ? "overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent"
              : "overflow-x-hidden"
          )}
        >
          {children}
        </div>
      </div>
    );
  }
);
ScrollArea.displayName = "ScrollArea";

export { ScrollArea };
