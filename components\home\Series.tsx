'use client';

import React, { useState, useRef, useEffect } from 'react';
import { MoreVertical, Gift, Eye, Tv } from 'lucide-react';
import { motion } from 'framer-motion';
import { useExpandable } from '@/hooks/useExpandable';

interface VideoItem {
  id: string;
  thumbnailUrl: string;
  title: string;
  channel: string;
  views: string;
  timeAgo: string;
  duration: string;
  isVerified?: boolean;
  isNew?: boolean;
}

interface IntroVideoProps {
  videos?: VideoItem[];
  onVideoClick?: (videoId: string) => void;
}

const Series: React.FC<IntroVideoProps> = ({
  videos = [],
  onVideoClick
}) => {
  const { isExpanded, toggleExpand, animatedHeight } = useExpandable(true);
  const contentRef = useRef<HTMLDivElement>(null);

  const listVideos: VideoItem[] = [
    {
      id: '1',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_69865500_1739511644_20250214054044_320_320_sq.jpg',
      title: 'Pharrell Admits He Couldn\'t Produce Stevie Wonder',
      channel: 'Classic Hip Hop',
      views: '1.9M',
      timeAgo: '6 weeks ago',
      duration: '1:23',
      isVerified: true,
      isNew: true
    },
    {
      id: '2',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_92034100_1739479281_20250213204121_320_320_sq.jpg',
      title: 'Lucas and Tyler the Creator',
      channel: 'Music',
      views: '2.4M',
      timeAgo: '1 month ago',
      duration: '2:15'
    },
    {
      id: '3',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_29362500_1739093699_20250209093459_320_320_sq.jpg',
      title: 'How Chrome Hearts took over',
      channel: 'Fashion',
      views: '998K',
      timeAgo: '1 month ago',
      duration: '3:45'
    },
    {
      id: '4',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_71618400_1737082604_20250117025644_320_320_sq.jpg',
      title: 'Tom Cruise On Brad Pitt, Paul Newman and "Mission"',
      channel: 'Letterman',
      views: '165K',
      timeAgo: '2 days ago',
      duration: '15:57',
      isVerified: true,
      isNew: true
    },
  ];

  const handleVideoClick = (videoId: string) => {
    if (onVideoClick) {
      onVideoClick(videoId);
    }
  };

  const displayListVideos = videos.length > 0 ? videos.slice(3) : listVideos;

  useEffect(() => {
    if (contentRef.current && isExpanded) {
      animatedHeight.set(contentRef.current.offsetHeight);
    } else {
      animatedHeight.set(0);
    }
  }, [isExpanded, animatedHeight]);

  return (
    <div className="mb-3 w-full max-w-full overflow-hidden">
      <button
        onClick={toggleExpand}
        className="flex items-center justify-between w-full h-10 px-3 rounded-lg bg-transparent transition-colors text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white"
      >
        <div className="flex items-center text-sm">
          <Tv strokeWidth={2.75} className="h-4 w-4 mr-2" />
          <span>Sugar Club Original Series</span>
        </div>
        <div>
          <svg
            width={16}
            height={16}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`fill-current transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
          >
            <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
          </svg>
        </div>
      </button>

      <motion.div
        className="overflow-hidden w-full"
        style={{ height: animatedHeight }}
      >
        <div
          ref={contentRef}
          className="mt-3 pb-4 w-full"
        >
          {/* Bottom row videos (vertical layout) */}
          <div className="space-y-1.5 w-full">
            {displayListVideos.map((video) => (
              <div
                key={video.id}
                className="flex cursor-pointer w-full"
                onClick={() => handleVideoClick(video.id)}
              >
                <div className="relative w-32 h-20 md:w-40 md:h-24 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={video.thumbnailUrl}
                    alt={video.title}
                    className="object-cover w-full h-full hover:scale-105 transition-transform duration-300 ease-in-out"
                  />
                  <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 py-0.5 rounded">
                    {video.duration}
                  </div>
                </div>
                <div className="ml-2 flex-1 min-w-0">
                  <h3 className="font-semibold text-sm line-clamp-2 text-gray-600 dark:text-white leading-none">
                    {video.title}
                  </h3>
                  <div className="flex items-center mt-1">
                    <span className="text-xs text-gorilla-gray truncate">
                      {video.channel}
                    </span>
                    {video.isVerified && (
                      <svg className="w-3 h-3 ml-1 flex-shrink-0 text-gray-600 dark:text-gray-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm-1.9 14.7l-4.5-4.5 1.4-1.4 3.1 3.1 6.5-6.5 1.4 1.4-7.9 7.9z" />
                      </svg>
                    )}
                  </div>
                  <div className="flex flex-col justify-center items-start gap-1">
                    <span className="text-xs text-gorilla-gray flex items-center gap-1 mt-1">
                      <Eye className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                      {video.views}
                    </span>

                    <span className="text-xs text-gorilla-gray">
                      {video.timeAgo}
                      {video.isNew && (
                        <span className="ml-1 text-xs bg-gray-200 dark:bg-gray-700 px-1 rounded text-gray-800 dark:text-gray-300 flex-shrink-0">
                          New
                        </span>
                      )}
                    </span>

                  </div>
                </div>
                <button className="p-1 self-start flex-shrink-0">
                  <MoreVertical className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
};
export default Series;