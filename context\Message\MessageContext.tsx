'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { Message } from '@/types/message';

interface MessagesContextType {
    messages: Message[];
}

const MessagesContext = createContext<MessagesContextType | undefined>(undefined);

export function MessagesProvider({
    messages,
    children,
}: {
    messages: Message[];
    children: ReactNode;
}) {
    return (
        <MessagesContext.Provider value={{ messages }}>
            {children}
        </MessagesContext.Provider>
    );
}

export function useMessages() {
    const context = useContext(MessagesContext);
    if (!context) {
        throw new Error('useMessages must be used within a MessagesProvider');
    }
    return context;
}