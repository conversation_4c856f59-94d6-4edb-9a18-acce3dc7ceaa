"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Search } from "lucide-react";
import { AnimatedModal } from "@/components/ui/animated-modal";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/chat/ui/avatar";
import { useDebounce } from "@/hooks/useDebounce";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

interface UserInfo {
  id: string;
  username?: string;
  profilePhoto?: string;
  display_name?: string;
}

interface NewMessageDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const NewMessageDialog: React.FC<NewMessageDialogProps> = ({ isOpen, onClose }) => {
  const router = useRouter();
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [userSearchResults, setUserSearchResults] = useState<UserInfo[]>([]);
  const [isUserSearching, setIsUserSearching] = useState(false);
  const debouncedUserSearchQuery = useDebounce(userSearchQuery, 500);

  // Convex user search
  const userSearchConvex = useQuery(
    api.users.searchUsers,
    debouncedUserSearchQuery && debouncedUserSearchQuery.length >= 3
      ? { query: debouncedUserSearchQuery, reason: "message" }
      : "skip"
  );

  useEffect(() => {
    if (debouncedUserSearchQuery.length < 3) {
        setUserSearchResults([]);
        setIsUserSearching(false);
        return;
      }
    setIsUserSearching(userSearchConvex === undefined);
    if (userSearchConvex && userSearchConvex.success) {
      setUserSearchResults(userSearchConvex.data || []);
      }
  }, [debouncedUserSearchQuery, userSearchConvex]);

  // Start a new conversation with selected user
  const startConversation = async (recipientId: string) => {
    try {
      onClose();
      setUserSearchQuery('');
      router.push(`/messages/new-message?userId=${recipientId}`);
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  };

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="md"
      title="New Message"
      closeButton={true}
    >
      <div className="space-y-4 py-2 rounded-lg px-2">
        <div className="relative">
          <input
            type="text"
            placeholder="Search for a user..."
            className="w-full bg-gray-200 dark:bg-zinc-900 text-sm rounded-md py-2 px-3 focus:outline-none border border-solid border-[#18181b]/30 dark:border-white/30"
            value={userSearchQuery}
            onChange={(e) => setUserSearchQuery(e.target.value)}
          />
          {isUserSearching ? (
            <div className="absolute right-3 top-2.5 h-4 w-4">
              <span className="message-search-loader"></span>
            </div>
          ) : (
            <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
          )}
        </div>

        <div className="max-h-[300px] overflow-y-auto">
          {isUserSearching ? (
            <div className="flex items-center justify-center p-4">
              <div className="top-search-bar-loader" />
            </div>
          ) : userSearchQuery.length >= 3 ? (
            userSearchResults.length > 0 ? (
              userSearchResults.map((user) => (
                <button
                  key={user.id}
                  onClick={() => startConversation(user.id)}
                  className="w-full flex items-center p-3 hover:bg-gray-200 dark:hover:bg-[#2a2a2a] rounded-md"
                >
                  <Avatar className="h-10 w-10 mr-3">
                    <AvatarImage src={user.profilePhoto} />
                    <AvatarFallback>
                      {(user.username || user.id?.slice(0, 2) || '?').charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <p className="font-medium">{user.username || user.id?.slice(0, 8)}</p>
                    {user.display_name && (
                      <p className="text-sm text-gray-400">{user.display_name}</p>
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="flex items-center justify-center h-20 text-gray-400">
                No users found
              </div>
            )
          ) : userSearchQuery.length > 0 ? (
            <div className="flex items-center justify-center h-20 text-gray-400">
              Type at least 3 characters to search
            </div>
          ) : (
            <div className="flex items-center justify-center h-20 text-gray-400">
              Search for users to message
            </div>
          )}
        </div>
      </div>
    </AnimatedModal>
  );
};
export default NewMessageDialog;