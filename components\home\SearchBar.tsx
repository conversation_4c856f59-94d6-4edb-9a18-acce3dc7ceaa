'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Search, X, Clock, Trash2 } from 'lucide-react';
import debounce from 'lodash/debounce';
import { cn } from '@/lib/utils';
import { useConvex } from 'convex/react';
import { api } from '@/convex/_generated/api';

// Constants
const RECENT_SEARCHES_KEY = 'recent-tag-searches';
const MAX_RECENT_SEARCHES = 8;

interface SearchResult {
  id: string;
  type: string;
  name: string;
  postCount: number;
}

interface RecentSearch {
  query: string;
  timestamp: number;
  tag?: SearchResult;
}

interface SearchBarProps {
  onTagSelect?: (tagName: string) => void;
  isExpanded: boolean;
  onExpand: (expanded: boolean) => void;
}

export default function SearchBar({ onTagSelect, isExpanded, onExpand }: SearchBarProps) {
  const router = useRouter();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);
  const [showRecentSearches, setShowRecentSearches] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchBarRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const convex = useConvex();

  // Memoize recentSearches to prevent re-renders
  const memoizedRecentSearches = useMemo(() => recentSearches, [recentSearches]);

  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.length < 2) {
        setResults([]);
        setIsLoading(false);
        return;
      }

      try {
        const data = await convex.query(api.hashtags.searchHashtags, {
          query: searchQuery,
          limit: 15,
        });
        // Validate and set results
        const tags = data.results.tags || [];
        const validTags = tags.filter((tag: any) =>
          typeof tag.id === 'string' &&
          typeof tag.type === 'string' &&
          typeof tag.name === 'string' &&
          typeof tag.postCount === 'number'
        );
        setResults(validTags);
      } catch (error) {
        console.error('[SearchBar] Search error:', error);
      } finally {
        setIsLoading(false);
      }
    }, 800),
    [convex]
  );

  const addToRecentSearches = useCallback((searchQuery: string, tag?: SearchResult) => {
    if (!searchQuery.trim()) return;

    setRecentSearches(prev => {
      const filtered = prev.filter(item => item.query.toLowerCase() !== searchQuery.toLowerCase());
      const newSearches = [
        {
          query: searchQuery,
          timestamp: Date.now(),
          tag: tag ? {
            id: tag.id,
            type: tag.type,
            name: tag.name,
            postCount: tag.postCount
          } : undefined
        },
        ...filtered
      ].slice(0, MAX_RECENT_SEARCHES);

      return newSearches;
    });
  }, []);

  const removeRecentSearch = useCallback((searchQuery: string, e?: React.MouseEvent) => {
    console.log('[SearchBar] removeRecentSearch:', searchQuery);
    if (e) {
      e.stopPropagation();
    }

    setRecentSearches(prev => {
      const newSearches = prev.filter(item => item.query.toLowerCase() !== searchQuery.toLowerCase());
      console.log('[SearchBar] removeRecentSearch: newSearches length:', newSearches.length);
      return [...newSearches];
    });
  }, []);

  const clearAllRecentSearches = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setRecentSearches([]);
    try {
      localStorage.removeItem(RECENT_SEARCHES_KEY);
    } catch (error) {
      console.error('[SearchBar] Failed to clear recent searches:', error);
    }
  }, []);

  useEffect(() => {
    try {
      const storedSearches = localStorage.getItem(RECENT_SEARCHES_KEY);
      if (storedSearches) {
        const parsedSearches = JSON.parse(storedSearches) as RecentSearch[];
        const validSearches = parsedSearches.filter(
          item => item.query && item.timestamp && (!item.tag || (item.tag.name && typeof item.tag.name === 'string'))
        );
        setRecentSearches(validSearches);
      }
    } catch (error) {
      console.error('[SearchBar] Failed to load recent searches:', error);
    }
  }, []);

  useEffect(() => {
    try {
      localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(memoizedRecentSearches));
    } catch (error) {
      console.error('[SearchBar] Failed to save recent searches:', error);
    }
  }, [memoizedRecentSearches]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
        setShowDropdown(false);
        onExpand(false);
        setShowRecentSearches(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onExpand]);

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleResultClick = (result: SearchResult) => {
    console.log('[SearchBar] handleResultClick:', result.name, { onTagSelect: !!onTagSelect });
    if (onTagSelect && result.name) {
      onTagSelect(result.name);
    } else {
      console.warn('[SearchBar] onTagSelect is undefined or invalid tag name, falling back to default navigation');
      router.push(`/explore?tag=${encodeURIComponent(result.name)}`);
    }
    addToRecentSearches(result.name, result);
    setShowDropdown(false);
    setQuery('');
    onExpand(false);
  };

  const handleRecentSearchClick = (recentSearch: RecentSearch, e?: React.MouseEvent) => {
    console.log('[SearchBar] handleRecentSearchClick: Start', { query: recentSearch.query, tagName: recentSearch.tag?.name });
    if (e) {
      e.stopPropagation();
    }

    if (recentSearch.tag && recentSearch.tag.name) {
      console.log('[SearchBar] handleRecentSearchClick: Valid tag', recentSearch.tag.name, { onTagSelect: !!onTagSelect });
      if (onTagSelect) {
        onTagSelect(recentSearch.tag.name);
      } else {
        console.warn('[SearchBar] onTagSelect is undefined, falling back to default navigation');
        router.push(`/explore?tag=${encodeURIComponent(recentSearch.tag.name)}`);
      }
      addToRecentSearches(recentSearch.query, recentSearch.tag);
      setShowDropdown(false);
      setQuery('');
      onExpand(false);
    } else {
      console.log('[SearchBar] handleRecentSearchClick: No valid tag info, searching query:', recentSearch.query);
      setQuery(recentSearch.query);
      setIsLoading(true);
      setShowDropdown(true);
      setShowRecentSearches(false);
      debouncedSearch(recentSearch.query);
      addToRecentSearches(recentSearch.query);
    }
  };

  const handleFocus = () => {
    setShowDropdown(true);
    onExpand(true);
    if (memoizedRecentSearches.length > 0 && !query) {
      setShowRecentSearches(true);
    }
  };

  const handleBlur = () => {
    // Rely on click-outside handler
  };

  const handleClear = () => {
    setQuery('');
    setResults([]);
    if (memoizedRecentSearches.length > 0) {
      setShowRecentSearches(true);
    }
    onExpand(false);
    if (inputRef.current) {
      inputRef.current.blur();
    }
  };

  return (
    <div className="relative w-full z-10" ref={dropdownRef}>
      <div className="w-full flex items-center space-x-2 gap-0">
        <div
          className={cn(
            "relative transition-[width] duration-300 linear",
            isExpanded ? "w-full" : "w-full"
          )}
          ref={searchBarRef}
        >
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => {
              const newQuery = e.target.value;
              setQuery(newQuery);
              setShowDropdown(true);
              setShowRecentSearches(false);

              if (newQuery) {
                onExpand(true);
              }

              if (newQuery.length >= 2) {
                setIsLoading(true);
              } else {
                setIsLoading(false);
                setResults([]);
                if (newQuery === '' && memoizedRecentSearches.length > 0) {
                  setShowRecentSearches(true);
                }
              }

              debouncedSearch(newQuery);
            }}
            onFocus={handleFocus}
            placeholder="Search Tags..."
            className="h-10 w-full bg-white dark:bg-[#18181b] placeholder:text-sm text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white rounded-lg px-3 py-2 pr-11 focus:outline-none transition-[width] duration-300 ease-in-out"
          />

          {query ? (
            <button
              onClick={handleClear}
              className="absolute right-3 top-0 h-10 text-gorilla-gray hover:text-gray-600 transition-colors border border-solid border-gray-400 dark:border-white border-r-0 border-t-0 border-b-0 pl-2"
            >
              <X className="h-5 w-5 text-gry-400 dark:text-white" />
            </button>
          ) : (
            <button className="absolute right-3 top-0 h-10 text-gorilla-gray hover:text-gray-600 transition-colors border border-solid border-gray-400 dark:border-white border-r-0 border-t-0 border-b-0 pl-2">
              <Search className="h-5 w-5 text-gry-400 dark:text-white" />
            </button>
          )}
        </div>
      </div>

      {showDropdown && (
        <div className="absolute z-10 w-full mt-1 bg-white/80 dark:bg-[#18181b]/80 backdrop-blur shadow-lg rounded-md border border-[#18181b]/20 dark:border-white/20 max-h-60 overflow-auto">
          <div className="max-h-[300px] overflow-y-auto select-dropdown-content">
            {showRecentSearches && memoizedRecentSearches.length > 0 && !query && (
              <div className="p-1">
                <div className="px-2 py-1.5 flex items-center justify-between">
                  <div className="text-xs text-gorilla-gray flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Recent Searches
                  </div>
                  <button
                    onClick={clearAllRecentSearches}
                    className="text-xs text-gorilla-gray hover:text-red-500 flex items-center gap-1"
                  >
                    <Trash2 className="h-3 w-3" />
                    Clear All
                  </button>
                </div>

                {memoizedRecentSearches.map((item) => (
                  <div
                    key={`${item.query}-${item.timestamp}`}
                    role="button"
                    tabIndex={0}
                    data-tag={item.query}
                    onClick={() => {
                      console.log('[SearchBar] Recent search clicked:', item.query);
                      handleRecentSearchClick(item);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        console.log('[SearchBar] Recent search keydown:', item.query);
                        handleRecentSearchClick(item);
                      }
                    }}
                    className="w-full px-2 py-2 hover:bg-accent hover:text-accent-foreground rounded-sm flex items-center justify-between transition-colors cursor-pointer"
                  >
                    <div className="flex items-center gap-2">
                      <div className="text-sm">#{item.query}</div>
                      {item.tag && (
                        <div className="text-xs text-muted-foreground">{item.tag.postCount} posts</div>
                      )}
                    </div>
                    <button
                      onClick={(e) => removeRecentSearch(item.query, e)}
                      className="text-muted-foreground hover:text-red-500"
                    >
                      <X className="h-3.5 w-3.5" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Search Results Section */}
            {query.length > 1 && (
              <>
                {isLoading ? (
                  <div className="flex items-center justify-center p-4">
                    <div className="top-search-bar-loader" />
                  </div>
                ) : results.length > 0 ? (
                  <div className="p-1">
                    {results.map((tag) => (
                      <div
                        key={tag.id}
                        role="button"
                        tabIndex={0}
                        data-tag={tag.name}
                        onClick={() => handleResultClick(tag)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            handleResultClick(tag);
                          }
                        }}
                        className="w-full px-2 py-2 hover:bg-accent hover:text-accent-foreground rounded-sm flex items-center justify-between transition-colors cursor-pointer"
                      >
                        <div className="text-sm">#{tag.name}</div>
                        <div className="text-xs text-muted-foreground">{tag.postCount} posts</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 text-center text-muted-foreground">
                    No tags found
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};