import { p as n } from "../immer-548168ec.js";
const r = (o) => ({
  open: !1,
  searchInput: "",
  swapType: "BUY",
  adaUsdPrice: 0,
  tokenList: [],
  unverifiedTokenList: [],
  supportedTokens: [],
  callbackSelectedToken: () => {
  },
  openModal: () => {
    o(
      n((e) => {
        e.tokenSearchSlice.searchInput = "", e.tokenSearchSlice.open = !0;
      })
    );
  },
  closeModal: (e) => {
    o(
      n((c) => {
        c.tokenSearchSlice.open = !1;
      })
    ), e && e();
  },
  toggleModal: () => {
    o(
      n((e) => {
        e.tokenSearchSlice.searchInput = "", e.tokenSearchSlice.open = !e.tokenSearchSlice.open;
      })
    );
  },
  setSearchInput: (e) => {
    o(
      n((c) => {
        c.tokenSearchSlice.searchInput = e;
      })
    );
  },
  setSwapType: (e) => {
    o(
      n((c) => {
        c.tokenSearchSlice.swapType = e;
      })
    );
  },
  setAdaUsdPrice: (e) => {
    o(
      n((c) => {
        c.tokenSearchSlice.adaUsdPrice = e;
      })
    );
  },
  setTokenList: (e) => {
    o(
      n((c) => {
        c.tokenSearchSlice.tokenList = e;
      })
    );
  },
  setUnverifiedTokenList: (e) => {
    o(
      n((c) => {
        c.tokenSearchSlice.unverifiedTokenList = e;
      })
    );
  },
  setCallbackSelectedToken: (e) => {
    o(
      n((c) => {
        c.tokenSearchSlice.callbackSelectedToken = e;
      })
    );
  },
  setSupportedTokens: (e) => {
    o(
      n((c) => {
        c.tokenSearchSlice.supportedTokens = e;
      })
    );
  }
});
export {
  r as default
};
