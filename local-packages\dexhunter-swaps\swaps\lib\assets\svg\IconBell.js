import { jsxs as e, jsx as o } from "react/jsx-runtime";
import { memo as r } from "react";
const s = (t) => /* @__PURE__ */ e(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 20 22",
    fill: "none",
    ...t,
    children: [
      /* @__PURE__ */ o(
        "path",
        {
          d: "M16.7491 8.70957V8.00497C16.7491 4.13623 13.7274 1 10 1C6.27256 1 3.25087 4.13623 3.25087 8.00497V8.70957C3.25087 9.5552 3.00972 10.3818 2.5578 11.0854L1.45036 12.8095C0.438821 14.3843 1.21105 16.5249 2.97036 17.0229C7.57274 18.3257 12.4273 18.3257 17.0296 17.0229C18.789 16.5249 19.5612 14.3843 18.5496 12.8095L17.4422 11.0854C16.9903 10.3818 16.7491 9.5552 16.7491 8.70957Z",
          stroke: "currentColor",
          strokeWidth: 2
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M5.5 18C6.15503 19.7478 7.92246 21 10 21C12.0775 21 13.845 19.7478 14.5 18",
          stroke: "currentColor",
          strokeWidth: 2,
          strokeLinecap: "round"
        }
      )
    ]
  }
), i = r(s);
export {
  i as default
};
