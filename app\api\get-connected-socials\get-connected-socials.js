import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_API_KEY);
const mmSupabase = createClient(process.env.MM_SUPABASE_URL, process.env.MM_SUPABASE_API_KEY);

async function getUserId(userAddress) {
    try {
        const { data: userIdQuery, error: userIdError } = await mmSupabase
            .from('UserWallets')
            .select('local_user_id')
            .eq('address', userAddress);

        if (userIdError) {
            throw new Error(`Error fetching user ID:, ${userIdError}`);
        }

        return userIdQuery ? userIdQuery[0]?.local_user_id : null;
    } catch (err) {
        console.error(err);
    }
};

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ success: false, message: 'Method Not Allowed' });
    }

    const { headers } = req;
    const authorizationHeader = headers['authorization'];
    const secretToken = process.env.NEXTAUTH_SECRET;

    if (authorizationHeader !== `Bearer ${secretToken}`) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
    }

    const { walletAddress } = req.body;

    if (!walletAddress) {
        return res.status(400).json({ success: false, message: 'Wallet address is required' });
    }

    try {
        const userId = await getUserId(walletAddress);

        if (!userId) {
            return res.status(404).json({ success: false, message: 'User not found' });
        }

        // Query the Supabase database for connected social accounts
        const { data, error } = await mmSupabase
            .from('Users')
            .select('user_info')
            .eq('local_user_id', userId);

        if (error) {
            console.error('Error fetching connected socials:', error);
            return res.status(500).json({ success: false, message: 'Internal Server Error' });
        }

        if (!data || data.length === 0) {
            return res.status(404).json({ success: false, message: 'User information not found' });
        }

        const userInfo = data[0]?.user_info || {};
        let connectedSocials = [];

        if (userInfo.connected_socials) {
            if (typeof userInfo.connected_socials === 'string') {
                try {
                    connectedSocials = JSON.parse(userInfo.connected_socials);
                } catch (parseError) {
                    console.error('Error parsing connected_socials:', parseError);
                    connectedSocials = [];
                }
            } else if (Array.isArray(userInfo.connected_socials)) {
                connectedSocials = userInfo.connected_socials;
            }
        }

        return res.status(200).json({ success: true, connectedSocials });
    } catch (error) {
        console.error('Error fetching connected socials:', error);
        return res.status(500).json({ success: false, message: 'Internal Server Error' });
    }
}