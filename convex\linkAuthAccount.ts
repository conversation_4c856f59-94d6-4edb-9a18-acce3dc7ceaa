import { action, mutation } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";

export const linkAuthAccount = action({
    args: {
        authAccountId: v.id("authAccounts"),
        linkedAccountId: v.id("Accounts"),
    },
    handler: async (ctx: any, { authAccountId, linkedAccountId }: any) => {
        try {
            await ctx.patch(authAccountId, {
                profileData: { linkedAccountId }
            });
        } catch (error) {
            console.error("Failed to link auth account:", error);
            throw new Error("Failed to link authentication account.");
        }
    },
});

export const findAuthAccount = action({
    args: {
        provider: v.string(),
        providerAccountId: v.string(),
    },
    handler: async (ctx: any, { provider, providerAccountId }: any) => {
        return await ctx.db
            .query("authAccounts")
            .withIndex("providerAndAccountId", (q: any) =>
                q
                    .eq("provider", provider)
                    .eq("providerAccountId", providerAccountId)
            )
            .first();
    },
});

export const upsertAuthAccount = mutation({
    args: {
        provider: v.string(),
        providerAccountId: v.string(),
        email: v.optional(v.string()),
        password: v.optional(v.string()),
        user_info: v.optional(v.any()),
    },
    handler: async (ctx: any, args: any): Promise<any> => {
        return await ctx.runMutation(api.accounts.upsertAuthAccount, args);
    }
});