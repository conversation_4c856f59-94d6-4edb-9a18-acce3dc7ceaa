import { jsxs as n, jsx as s, Fragment as y } from "react/jsx-runtime";
import { Button as t } from "../../components/ui/button.js";
import b from "../../store/useStore.js";
import { useSwapAction as L } from "../hooks/useSwapAction.js";
import { u as O } from "../../react-hotkeys-hook.esm-60f1c3b8.js";
import { useEffect as R } from "react";
import { useLimitAction as _ } from "../hooks/useLimitAction.js";
import { useStopLossAction as I } from "../hooks/useStopLossAction.js";
import g from "../../assets/svg/IconSquaredSpinner.js";
import { useDCAAction as P } from "../hooks/useDCAAction.js";
import "../../index-1c873780.js";
import "../../index-1d6812f7.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../lib.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "./tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../config/axios.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
import "../../hooks/useNotify.js";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../hooks/useScreen.js";
import "../../utils/formatNumber.js";
import "../../QueryClientProvider-6bcd4331.js";
import "../../utils/formatToken.js";
function As() {
  var f;
  const {
    tokenSell: o,
    sellAmount: e,
    isTransactionLoading: w,
    estimationError: l,
    swapDetails: m,
    orderType: h,
    limitPrice: r,
    limitMultiples: i,
    isSwapSubmitted: c,
    setIsSwapSubmitted: v
  } = b((d) => d.swapSlice), {
    userAddress: S,
    isLoadingWallet: a,
    balance: u,
    userTokens: N,
    setIsOpenWallet: T,
    onClickWalletConnect: p
  } = b((d) => d.walletSlice), { buyToken: x } = L(), { limitToken: A } = _(), { stopLossToken: M } = I(), { dcaToken: k } = P();
  if (O("enter", x), R(() => {
    if (c) {
      const d = setTimeout(() => {
        v(!1);
      }, 2e3);
      return () => clearTimeout(d);
    }
  }, [c]), w)
    return /* @__PURE__ */ n("div", { className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-flex dhs-justify-center dhs-items-center dhs-gap-4 dhs-bg-background dhs-animate-pulse", children: [
      /* @__PURE__ */ s("div", { className: "dhs-text-lg dhs-text-accent dhs-font-proximaMedium dhs-text-center dhs-flex dhs-gap-2", children: "Swapping" }),
      /* @__PURE__ */ s(
        g,
        {
          width: 25,
          height: 25,
          className: "dhs-text-accent"
        }
      )
    ] });
  if (a)
    return /* @__PURE__ */ n("div", { className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-flex dhs-justify-center dhs-items-center dhs-gap-4 dhs-bg-background dhs-animate-pulse", children: [
      /* @__PURE__ */ s("div", { className: "dhs-text-lg dhs-text-accent dhs-font-proximaMedium dhs-text-center dhs-flex dhs-gap-2", children: "Connecting" }),
      /* @__PURE__ */ s(
        g,
        {
          width: 25,
          height: 25,
          className: "dhs-text-accent"
        }
      )
    ] });
  if (!S)
    return /* @__PURE__ */ s(
      t,
      {
        className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
        onClick: () => p !== void 0 ? p() : T(!0),
        variant: "default",
        children: "Connect Wallet"
      }
    );
  const B = (o == null ? void 0 : o.token_ascii) === "Cardano" ? u : (f = N.find((d) => d.token_id === (o == null ? void 0 : o.token_id))) == null ? void 0 : f.amount, C = (m.total_fee + (m.partner_fee || m.dexhunter_fee || 0)) / 1e6;
  return !(Number(e) <= B && C < u) && Number(e) > 0 ? /* @__PURE__ */ s(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      variant: "default",
      disabled: !0,
      children: "Insufficient Balance"
    }
  ) : l ? /* @__PURE__ */ s(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      variant: "default",
      disabled: !0,
      children: l || "Estimation Error"
    }
  ) : Number(r) === 0 && (h === "LIMIT" || h === "STOP_LOSS") ? /* @__PURE__ */ s(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      variant: "default",
      disabled: !0,
      children: "Choose Price"
    }
  ) : h === "DCA" ? Number(e) >= 1 ? /* @__PURE__ */ s(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-blue-101 hover:dhs-bg-blue-600 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-white dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      onClick: k,
      variant: "default",
      disabled: a || !e,
      children: "Create"
    }
  ) : /* @__PURE__ */ s(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-blue-101 hover:dhs-bg-blue-600 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-white dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      variant: "default",
      disabled: !0,
      children: "Minimum 1 ADA"
    }
  ) : h === "STOP_LOSS" ? Number(e) * r >= 1 ? /* @__PURE__ */ n(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      onClick: M,
      variant: "default",
      disabled: a || !e || !r,
      children: [
        "Place ",
        i > 1 && i,
        " Order",
        i > 1 && "s"
      ]
    }
  ) : /* @__PURE__ */ s(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      variant: "default",
      disabled: !0,
      children: "Mininum 1 ADA"
    }
  ) : h === "LIMIT" ? /* @__PURE__ */ n(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      onClick: A,
      variant: "default",
      disabled: a || !e || !r,
      children: [
        "Place ",
        i > 1 && i,
        " Order",
        i > 1 && "s"
      ]
    }
  ) : /* @__PURE__ */ s(y, { children: /* @__PURE__ */ s(
    t,
    {
      className: "dhs-w-full dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
      onClick: x,
      variant: "default",
      disabled: a || Number(e) === 0 && h === "SWAP",
      children: "Swap"
    }
  ) });
}
export {
  As as default
};
