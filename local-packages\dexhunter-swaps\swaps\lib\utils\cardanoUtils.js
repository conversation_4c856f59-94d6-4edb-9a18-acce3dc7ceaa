import { d as A } from "../index-ca8eb9e1.js";
import { server as N } from "../config/axios.js";
import "../axios-ddd885c5.js";
var m = {};
Object.defineProperty(m, "__esModule", { value: !0 });
m.bech32m = b = m.bech32 = void 0;
const w = "qpzry9x8gf2tvdw0s3jn54khce6mua7l", S = {};
for (let t = 0; t < w.length; t++) {
  const e = w.charAt(t);
  S[e] = t;
}
function u(t) {
  const e = t >> 25;
  return (t & 33554431) << 5 ^ -(e >> 0 & 1) & 996825010 ^ -(e >> 1 & 1) & 642813549 ^ -(e >> 2 & 1) & 513874426 ^ -(e >> 3 & 1) & 1027748829 ^ -(e >> 4 & 1) & 705979059;
}
function C(t) {
  let e = 1;
  for (let r = 0; r < t.length; ++r) {
    const n = t.charCodeAt(r);
    if (n < 33 || n > 126)
      return "Invalid prefix (" + t + ")";
    e = u(e) ^ n >> 5;
  }
  e = u(e);
  for (let r = 0; r < t.length; ++r) {
    const n = t.charCodeAt(r);
    e = u(e) ^ n & 31;
  }
  return e;
}
function y(t, e, r, n) {
  let h = 0, i = 0;
  const o = (1 << r) - 1, s = [];
  for (let c = 0; c < t.length; ++c)
    for (h = h << e | t[c], i += e; i >= r; )
      i -= r, s.push(h >> i & o);
  if (n)
    i > 0 && s.push(h << r - i & o);
  else {
    if (i >= e)
      return "Excess padding";
    if (h << r - i & o)
      return "Non-zero padding";
  }
  return s;
}
function P(t) {
  return y(t, 8, 5, !0);
}
function W(t) {
  const e = y(t, 5, 8, !1);
  if (Array.isArray(e))
    return e;
}
function B(t) {
  const e = y(t, 5, 8, !1);
  if (Array.isArray(e))
    return e;
  throw new Error(e);
}
function k(t) {
  let e;
  t === "bech32" ? e = 1 : e = 734539939;
  function r(o, s, c) {
    if (c = c || 90, o.length + 7 + s.length > c)
      throw new TypeError("Exceeds length limit");
    o = o.toLowerCase();
    let f = C(o);
    if (typeof f == "string")
      throw new Error(f);
    let d = o + "1";
    for (let a = 0; a < s.length; ++a) {
      const l = s[a];
      if (l >> 5)
        throw new Error("Non 5-bit word");
      f = u(f) ^ l, d += w.charAt(l);
    }
    for (let a = 0; a < 6; ++a)
      f = u(f);
    f ^= e;
    for (let a = 0; a < 6; ++a) {
      const l = f >> (5 - a) * 5 & 31;
      d += w.charAt(l);
    }
    return d;
  }
  function n(o, s) {
    if (s = s || 90, o.length < 8)
      return o + " too short";
    if (o.length > s)
      return "Exceeds length limit";
    const c = o.toLowerCase(), f = o.toUpperCase();
    if (o !== c && o !== f)
      return "Mixed-case string " + o;
    o = c;
    const d = o.lastIndexOf("1");
    if (d === -1)
      return "No separator character for " + o;
    if (d === 0)
      return "Missing prefix for " + o;
    const a = o.slice(0, d), l = o.slice(d + 1);
    if (l.length < 6)
      return "Data too short";
    let g = C(a);
    if (typeof g == "string")
      return g;
    const E = [];
    for (let p = 0; p < l.length; ++p) {
      const v = l.charAt(p), x = S[v];
      if (x === void 0)
        return "Unknown character " + v;
      g = u(g) ^ x, !(p + 6 >= l.length) && E.push(x);
    }
    return g !== e ? "Invalid checksum for " + o : { prefix: a, words: E };
  }
  function h(o, s) {
    const c = n(o, s);
    if (typeof c == "object")
      return c;
  }
  function i(o, s) {
    const c = n(o, s);
    if (typeof c == "object")
      return c;
    throw new Error(c);
  }
  return {
    decodeUnsafe: h,
    decode: i,
    encode: r,
    toWords: P,
    fromWordsUnsafe: W,
    fromWords: B
  };
}
var b = m.bech32 = k("bech32");
m.bech32m = k("bech32m");
const _ = (t) => A.Buffer.from(t, "hex"), F = (t) => A.Buffer.from(t).toString("hex"), O = (t) => {
  const e = _(t), r = b.toWords(e);
  return b.encode(
    e[0] == 1 ? "addr" : "addr_test",
    r,
    130
  );
}, H = (t) => t.map((e) => O(e)).filter((e) => !e.startsWith("addr_test")), R = (t) => {
  const e = b.decode(t, 140);
  return A.Buffer.from(b.fromWords(e.words)).toString("hex");
}, z = (t) => {
  let e = "";
  for (let r = 0; r < t.length; r += 2) {
    const n = parseInt(t.substr(r, 2), 16);
    e += String.fromCharCode(n);
  }
  return e;
}, G = async (t) => {
  try {
    const { data: e } = await N.post("/swap/wallet", { addresses: t }), r = e.cardano;
    return {
      balance: Math.max(
        parseFloat(
          ((r == null ? void 0 : r.total_ada) - (r == null ? void 0 : r.locked_ada)).toFixed(2)
        ),
        0
      ),
      tokens: e.tokens
    };
  } catch (e) {
    return console.log("ERROR GETTING BALANCE", e), {
      balance: 0,
      tokens: []
    };
  }
}, L = async (t) => {
  const e = await t.getBalance(), { decode: r } = await import("../decode-dc84e057.js"), n = r(_(e));
  return typeof n == "number" ? n.toString() : (n[0] / 1e6).toString();
}, D = (t, e = 6) => t ? (t.length > e * 2 ? t.substr(0, e - 1) + ".." + t.substr(t.length - (e - 1), t.length) : t).toString().replace(" ..", "..") : "", V = (t, e) => t ? (t.length > e * 2 ? t.substr(0, e - 1) + ".." + t.substr(t.length - (e - 1), t.length) : t).toString().replace(" ..", "..") : "", q = (t, e) => {
  const r = new URLSearchParams(window.location.search);
  return e === "" ? r.delete(t) : r.set(t, e), r.toString();
};
export {
  R as addressFromBech32,
  O as addressToBech32,
  _ as fromHex,
  G as getBalance,
  L as getWalletBalance,
  z as hexToAscii,
  D as middlen,
  V as middlenBetween,
  H as parseAddressesToBech32,
  F as toHex,
  q as updateSearchParams
};
