import { jsxs as n, jsx as i } from "react/jsx-runtime";
import F from "../../store/useStore.js";
import { formatTokenName as N } from "../../utils/formatToken.js";
import { useMemo as m } from "react";
import { cn as c } from "../../lib/utils.js";
import b from "../../trends/components/PriceFormatter.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../swap/components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../ui/tooltipDialog.js";
import "../../hooks/useScreen.js";
import "../ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../lib.js";
import "../ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../utils/formatNumber.js";
const mr = ({
  tokenPrice: t,
  className: d,
  tokenSellTicker: e,
  tokenBuyTicker: s,
  classNameText: f,
  isLoading: u = !1,
  isReverse: o = !1,
  onClick: a
}) => {
  const { isPricesFlipped: p } = F((r) => r.globalSettingsSlice), x = () => {
    a && a();
  }, l = m(() => o ? !p : p, [p, o]), g = m(() => {
    if (!t)
      return "0";
    let r = t;
    return o && (r = 1 / r), r;
  }, [t, o]), h = m(() => l ? e : s, [l, s, e]);
  return u && !t ? /* @__PURE__ */ n("div", { className: "flex gap-1 w-[13px] h-[13px] items-center sm:hidden", children: [
    /* @__PURE__ */ i(
      "img",
      {
        src: "https://storage.googleapis.com/dexhunter-images/public/yellow_spinner.svg",
        width: 13,
        height: 13,
        alt: "spinner",
        className: "pb-0.5"
      }
    ),
    /* @__PURE__ */ i("span", { className: "font-proximaMedium text-xs uppercase", children: "ADA" })
  ] }) : /* @__PURE__ */ i(
    "div",
    {
      className: c(
        "flex items-center gap-2 hover:opacity-90 transition-all duration-100 cursor-pointer",
        d
      ),
      onClick: x,
      children: /* @__PURE__ */ n(
        "div",
        {
          className: c(
            "sm:text-gray-101 sm:font-proximaMedium sm:text-sm flex gap-1",
            f
          ),
          children: [
            /* @__PURE__ */ i(b, { price: g }),
            /* @__PURE__ */ i("span", { children: N(h) })
          ]
        }
      )
    }
  );
};
export {
  mr as default
};
