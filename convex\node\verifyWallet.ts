"use node";
// convex/node/verifyWallet.ts
import { action } from "../_generated/server";
import { v } from "convex/values";
import { Verifier } from "bip322-js";
import { Id } from "../_generated/dataModel";
import { internal } from "../_generated/api";

// This file contains Node.js specific code and must be in the node/ directory
// to be properly handled by Convex's Node.js runtime

export const verifyWalletSignIn = action({
  args: {
    address: v.string(),
    blockchain: v.string(),
    signature: v.string(),
    accountType: v.string(),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    message?: string;
    userId?: Id<"Accounts">;
    accountType?: string;
  }> => {
    try {
      // Get the nonce for this address
      const nonceDoc = await ctx.runQuery(internal.walletAuth.getNonce, {
        address: args.address
      });

      if (!nonceDoc || nonceDoc.expiresAt < Date.now()) {
        return { success: false, message: "Invalid or expired nonce" };
      }

      // Verify the signature
      const message = `Sign this message to verify your wallet. Nonce: ${nonceDoc.nonce}`;
      let isValid = false;

      // Handle different blockchains
      switch (args.blockchain.toLowerCase()) {
        case 'bitcoin':
          isValid = await Verifier.verifySignature(
            args.address,
            message,
            args.signature
          );
          break;
        // Add other blockchains as needed
        default:
          return { success: false, message: `Unsupported blockchain: ${args.blockchain}` };
      }

      if (!isValid) {
        return { success: false, message: "Invalid signature" };
      }

      // Find or create user
      let user = await ctx.runQuery(internal.walletAuth.findUserByWallet, {
        address: args.address,
        blockchain: args.blockchain,
      });

      if (!user && args.accountType === "signup") {
        const timestamp = Date.now();
        const newUser = {
          user_id: crypto.randomUUID(),
          email: null,
          password: null,
          wallet_address: args.address,
          blockchain: args.blockchain,
          account_type: "standard",
          registration_date: new Date().toISOString(),
          user_info: {
            account: {
              username: `user_${args.address.slice(0, 8)}`,
              displayName: `User ${args.address.slice(0, 8)}`,
              profilePhoto: "",
            },
            email: null,
            emailVerified: null,
          },
          created_at: timestamp,
          updated_at: timestamp,
        };

        const userId = await ctx.runMutation(internal.walletAuth.createUser, newUser);
        user = { _id: userId, _creationTime: timestamp, ...newUser };
      }

      if (!user) {
        return { success: false, message: "User not found. Please sign up first." };
      }

      // Delete used nonce
      await ctx.runMutation(internal.walletAuth.deleteNonce, { id: nonceDoc._id });

      return {
        success: true,
        userId: user._id,
        accountType: user.account_type || "standard",
      };
    } catch (error) {
      console.error("Error in verifyWalletSignIn:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "An unknown error occurred"
      };
    }
  },
});