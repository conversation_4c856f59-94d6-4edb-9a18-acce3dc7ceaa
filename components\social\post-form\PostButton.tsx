import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { ChevronDown, CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';

interface PostButtonProps {
  isSubmitting: boolean;
  hasContent: boolean;
  contentLength: number;
  maxChars: number;
  scheduledDate?: Date;
  scheduledTime: string;
  onScheduledDateChange: (date: Date | undefined) => void;
  onScheduledTimeChange: (time: string) => void;
  onSubmit: (isScheduled: boolean) => void;
  setIsPostDropdownOpen: (value: boolean) => void;
  setIsChildPopoverOpen: (value: boolean) => void;
  setIsFocused: (value: boolean) => void;
}

export const PostButton: React.FC<PostButtonProps> = ({
  isSubmitting,
  hasContent,
  contentLength,
  maxChars,
  scheduledDate,
  scheduledTime,
  onScheduledDateChange,
  onScheduledTimeChange,
  onSubmit,
  setIsPostDropdownOpen,
  setIsChildPopoverOpen,
  setIsFocused,
}) => {
  return (
    <Popover
      onOpenChange={(open) => {
        setIsPostDropdownOpen(open);
        setIsChildPopoverOpen(open);
        if (open) setIsFocused(true);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="default"
          className="!bg-turquoise text-white rounded-lg px-4 py-0 !text-sm font-medium shadow-md transition-all flex items-center gap-2"
          disabled={isSubmitting || !hasContent || contentLength > maxChars}
        >
          Create Post
          <ChevronDown className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      {/* Rest of the popover content... */}
    </Popover>
  );
};