import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { useUpdatePost } from '@/hooks/convexHooks';
import { DialogRoot as Dialog, DialogContent, DialogHeader, DialogTitle, DialogBody, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';
import { EmojiPicker } from '@/components/ui/chat/emoji-picker';
import { Label } from "@/components/ui/label";
import Tippy from '@tippyjs/react';
import { cn } from '@/lib/utils';
import MixedTagsInput, { EditorRefType } from '../social/MixedTagsInputEdit';
import { useUser } from '@/hooks/useUser';
import Swal from 'sweetalert2';
import { X } from 'lucide-react';
import AnimatedTabs from '@/components/ui/animated-tabs';
import { TaggedUser } from '@/types/post';
import { EditorState, Modifier, ContentState, convertFromRaw, convertToRaw } from 'draft-js';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useDebounce } from '@/hooks/useDebounce';

const MAX_CHARS = 400;

interface EditPostDialogProps {
  isOpen: boolean;
  onClose: () => void;
  post: {
    id: string;
    content: string;
    rawState?: string; // New field for serialized editor state
    visibility?: 'public' | 'followers' | 'onlyme';
    mediaUrl?: string | null;
    mediaType?: string | null;
    mediaDescription?: string | null;
    mediaId?: string;
    taggedUsers?: TaggedUser[];
    mentions?: Array<{
      userId: string;
      username: string;
      profilePhoto?: string;
    }>;
    hashtags?: string[];
    isPaid?: boolean;
  };
  onPostUpdated?: (updatedPost: any) => void;
};

const EditPostDialog: React.FC<EditPostDialogProps> = ({
  isOpen,
  onClose,
  post,
  onPostUpdated
}) => {
  const updatePost = useUpdatePost();
  const { user } = useUser();
  const [content, setContent] = useState<string>(post?.content || '');
  const [rawState, setRawState] = useState<string | null>(post?.rawState || null); // Store serialized state
  const [visibility, setVisibility] = useState<'public' | 'followers' | 'onlyme'>(
    post?.visibility || 'public'
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [mediaFile, setMediaFile] = useState<File | null>(null);
  const [mediaPreview, setMediaPreview] = useState<string | null>(post.mediaUrl || null);
  const [mediaType, setMediaType] = useState<string | null>(post.mediaType || null);
  const [mediaDescription, setMediaDescription] = useState(post.mediaDescription || '');
  const [taggedUsers, setTaggedUsers] = useState<TaggedUser[]>(post.taggedUsers || []);
  const [shouldRemoveMedia, setShouldRemoveMedia] = useState(false);
  const [tagData, setTagData] = useState<{
    hashtags: string[];
    mentions: Array<{ userId: string; username: string; profilePhoto?: string }>
  }>({
    hashtags: post.hashtags || [],
    mentions: post.mentions || []
  });

  // State for user search query
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const debouncedUserQuery = useDebounce(userSearchQuery, 300);

  // Use Convex query for user search
  const userSearchResults = useQuery(
    api.users.searchUsers,
    debouncedUserQuery && debouncedUserQuery.length >= 3
      ? {
          query: debouncedUserQuery,
          limit: 10,
          userId: user?.id
        }
      : 'skip'
  );

  // State for hashtag search query
  const [hashtagSearchQuery, setHashtagSearchQuery] = useState('');
  const debouncedHashtagQuery = useDebounce(hashtagSearchQuery, 300);

  // Use Convex query for hashtag search
  const hashtagSearchResults = useQuery(
    api.hashtags.searchHashtags,
    debouncedHashtagQuery && debouncedHashtagQuery.length >= 2
      ? { query: debouncedHashtagQuery, limit: 10 }
      : 'skip'
  );

  const editorRef = useRef<EditorRefType>(null);

  // Track if any changes have been made
  const hasChanges = useMemo(() => {
    return content?.trim() !== post?.content?.trim() ||
      visibility !== (post.visibility || 'public') ||
      mediaFile !== null ||
      (post.mediaUrl && !mediaPreview) ||
      (!post.mediaUrl && mediaPreview) ||
      mediaDescription !== (post.mediaDescription || '') ||
      JSON.stringify(taggedUsers) !== JSON.stringify(post.taggedUsers || []) ||
      JSON.stringify(tagData) !== JSON.stringify({
        hashtags: post.hashtags || [],
        mentions: post.mentions || []
      });
  }, [
    content,
    visibility,
    mediaFile,
    mediaPreview,
    mediaDescription,
    taggedUsers,
    tagData,
    post
  ]);

  // Create a function that matches the expected signature for MixedTagsInput
  const fetchUserSuggestions = useCallback(async (query: string) => {
    // Update the search query state to trigger the Convex query
    setUserSearchQuery(query);

    // Return the results from the Convex query, formatted for the MixedTagsInput
    const results = userSearchResults?.results?.creators || [];

    return results.map((user: any) => ({
      id: user.id,
      label: user.displayName || user.username,
      value: user.username,
      display: user.displayName || user.username,
      username: '@' + user.username,
      avatar: user.profilePhoto,
      type: 'mention' as const
    }));
  }, [userSearchResults]);

  const fetchHashtagSuggestions = useCallback(async (query: string) => {
    setHashtagSearchQuery(query);
    const tags = hashtagSearchResults?.results?.tags || [];
    return tags.map((tag: any) => ({
        id: tag.id,
        label: tag.name,
        value: tag.name,
      display: `#${tag.name} (${tag.postCount} posts)`,
        type: 'hashtag' as const
      }));
  }, [hashtagSearchResults]);

  const handleRemoveMedia = async () => {
    const result = await Swal.fire({
      title: 'Remove Media?',
      text: "This action cannot be undone. The media will be permanently removed from the post.",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, remove it!'
    });

    if (result.isConfirmed) {
      setMediaPreview(null);
      setMediaType(null);
      setMediaDescription('');
      setShouldRemoveMedia(true);
    }
  };

  const handleUpdate = async () => {
    if (!content.trim() && !mediaPreview) {
      toast.error('Post content cannot be empty');
      return;
    }

    setIsUpdating(true);

    try {
      const { hashtags, mentions } = tagData;
      const uniqueHashtags = [...new Set(hashtags)];

      let shouldRemoveMedia = mediaPreview === null && post.mediaUrl;

      if (visibility !== post.visibility) {
        const visibilityResponse = await fetch(`/api/posts/${post.id}/visibility`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ visibility }),
        });

        if (!visibilityResponse.ok) {
          throw new Error('Failed to update visibility');
        }
      }

      // Get the current editor state from the editor
      let editorState: EditorState | null = null;
      if (editorRef.current) {
        editorState = editorRef.current.getEditorState();
      }

      // Serialize the editor state
      let serializedRawState: string | null = null;
      if (editorState) {
        serializedRawState = JSON.stringify(convertToRaw(editorState.getCurrentContent()));
      }

      const updateData = {
        postId: post.id,
        description: content.trim(),
        visibility,
        metadata: {
          raw_state: serializedRawState,
          media_url: shouldRemoveMedia ? undefined : post.mediaUrl,
          media_type: shouldRemoveMedia ? undefined : post.mediaType,
          media_description: shouldRemoveMedia ? undefined : mediaDescription.trim(),
          removeMedia: shouldRemoveMedia,
          media_id: post.mediaId || undefined,
          taggedUsers,
          mentions: mentions.map(m => ({
            userId: m.userId,
            username: m.username,
            profilePhoto: m.profilePhoto
          })),
          hashtags: uniqueHashtags,
        },
      };

      const result = await updatePost(updateData);

      if (result && result.success) {
        onPostUpdated?.(result.post || {});
        toast.success('Post updated successfully');
        onClose();
      } else {
        throw new Error(result?.error || 'Failed to update post');
      }

    } catch (error) {
      console.error('Error updating post:', error);
      toast.error('Failed to update post');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    const insertEmoji = () => {
      if (!editorRef.current) {
        console.error('Editor ref not found');
        setTimeout(insertEmoji, 100);
        return;
      }

      editorRef.current.focus();
      const editorState = editorRef.current.getEditorState();
      if (!editorState) {
        console.error('Editor state not found');
        return;
      }

      const selectionState = editorState.getSelection();
      const contentState = editorState.getCurrentContent();
      const newContentState = Modifier.insertText(
        contentState,
        selectionState,
        emoji
      );

      let newEditorState = EditorState.push(
        editorState,
        newContentState,
        'insert-characters'
      );

      const newSelection = selectionState.merge({
        anchorOffset: selectionState.getAnchorOffset() + emoji.length,
        focusOffset: selectionState.getAnchorOffset() + emoji.length,
      });
      newEditorState = EditorState.forceSelection(newEditorState, newSelection);

      editorRef.current.onChange(newEditorState);
    };

    insertEmoji();
  };

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
  };

  const formatContentForEdit = (
    content: string,
    mentions?: Array<{ userId: string; username: string; profilePhoto?: string }>,
    hashtags?: string[]
  ): string => {
    if (!content) return '';

    let formattedContent = content;

    if (mentions?.length) {
      mentions.forEach(mention => {
        const mentionRegex = new RegExp(`@${mention.username}\\b`, 'g');
        const mentionTag = `@${mention.username}`;
        formattedContent = formattedContent.replace(mentionRegex, mentionTag);
      });
    }

    if (hashtags?.length) {
      hashtags.forEach(hashtag => {
        const hashtagRegex = new RegExp(`#${hashtag}\\b`, 'g');
        const hashtagTag = `#${hashtag}`;
        formattedContent = formattedContent.replace(hashtagRegex, hashtagTag);
      });
    }

    return formattedContent;
  };

  useEffect(() => {
    console.log({ post })
    if (isOpen && post) {
      setContent(post.content || '');
      setRawState(post.rawState || null);
      setTagData({
        hashtags: post.hashtags || [],
        mentions: post.mentions || []
      });
      setVisibility(post.visibility || 'public');
      setMediaPreview(post.mediaUrl || null);
      setMediaType(post.mediaType || null);
      setMediaDescription(post.mediaDescription || '');
      setTaggedUsers(post.taggedUsers || []);
      setShouldRemoveMedia(false);
    } else if (!isOpen) {
      setMediaPreview(post.mediaUrl || null);
      setMediaType(post.mediaType || null);
      setMediaDescription(post.mediaDescription || '');
      setShouldRemoveMedia(false);
      setContent(post.content || '');
      setRawState(post.rawState || null);
      setVisibility(post.visibility || 'public');
      setTaggedUsers(post.taggedUsers || []);
      setTagData({
        hashtags: post.hashtags || [],
        mentions: post.mentions || []
      });
    }
  }, [isOpen, post]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose} placement="center" motionPreset="slide-in-bottom">
      <DialogContent className="max-w-[630px] w-full bg-white dark:bg-zinc-900 border-none backdrop-blur p-2">
        <DialogHeader className="!px-1 justify-center">
          <DialogTitle>
            <span className="!text-3xl uppercase w-full text-center !px-0 font-semibold text-[#121212] dark:text-white">Edit Post</span>
          </DialogTitle>
        </DialogHeader>

        <DialogBody className="flex flex-col items-center gap-6 p-2">
          <div className="w-full space-y-4 py-4 px-0">
            <div className="w-full">
              <div className="mb-2 relative">
                <MixedTagsInput
                  ref={editorRef}
                  value={content}
                  rawState={rawState} // Pass rawState
                  initialTagData={{
                    mentions: post.mentions || [],
                    hashtags: post.hashtags || []
                  }}
                  onChange={handleContentChange}
                  onTagData={setTagData}
                  fetchUserSuggestions={fetchUserSuggestions}
                  fetchHashtagSuggestions={fetchHashtagSuggestions}
                />

                <div className="absolute bottom-2 right-2">
                  <Tippy
                    content="Insert Emoji"
                    animation="shift-toward-subtle"
                    placement="top"
                    arrow={true}
                    theme="sugar"
                  >
                    <div className="inline-flex">
                      <EmojiPicker
                        onChange={handleEmojiSelect}
                        className="text-turquoise hover:text-accent-foreground hover:bg-blue-100 dark:hover:bg-blue-900/30 !z-[99999] relative"
                      />
                    </div>
                  </Tippy>
                </div>
              </div>

              <div className={cn(
                "text-sm w-full text-right px-3",
                content.length > MAX_CHARS ? "text-red-500" :
                  content.length > MAX_CHARS * 0.8 ? "text-yellow-500" :
                    "text-gray-500"
              )}>
                {MAX_CHARS - content.length}
              </div>
            </div>

            <div className="space-y-4">
              <Label className="font-semibold text-xl">Post Visibility</Label>
              <div className="flex gap-4 bg-gray-300 dark:bg-zinc-800 rounded-lg p-2">
                <AnimatedTabs
                  defaultValue={visibility}
                  className="rounded-lg bg-black/20 dark:bg-white/20 flex"
                  transition={{
                    type: "spring",
                    bounce: 0.2,
                    duration: 0.3,
                  }}
                  onValueChange={(value) => {
                    if (value) {
                      setVisibility(value as 'public' | 'followers' | 'onlyme');
                    }
                  }}
                >
                  <button
                    data-id="public"
                    type="button"
                    disabled={isUpdating}
                    className="inline-flex h-10 w-full items-center justify-center transition-colors duration-100 focus-visible:outline-2 data-[checked=true]:text-white dark:data-[checked=true]:text-[#fff]"
                  >
                    Public
                  </button>
                  <button
                    data-id="followers"
                    type="button"
                    disabled={isUpdating}
                    className="inline-flex h-10 w-full items-center justify-center transition-colors duration-100 focus-visible:outline-2 data-[checked=true]:text-white dark:data-[checked=true]:text-[#fff]"
                  >
                    Followers Only
                  </button>
                  <button
                    data-id="onlyme"
                    type="button"
                    disabled={isUpdating}
                    className="inline-flex h-10 w-full items-center justify-center transition-colors duration-100 focus-visible:outline-2 data-[checked=true]:text-white dark:data-[checked=true]:text-[#fff]"
                  >
                    Only Me
                  </button>
                </AnimatedTabs>
              </div>
            </div>
          </div>

          {mediaPreview && (
            <div className="w-full space-y-4">
              <div className="relative rounded-2xl overflow-hidden border border-gray-200 dark:border-gray-800">
                <div className="absolute z-50 top-0 left-0 right-0 flex justify-end items-center p-2 bg-gradient-to-b from-black/50 to-transparent">
                  <button
                    type="button"
                    disabled={isUpdating}
                    className="h-8 w-8 rounded-full !bg-black/80 hover:bg-black/80 flex items-center justify-center transition-colors"
                    onClick={handleRemoveMedia}
                  >
                    <X className="h-5 w-5 text-white" />
                  </button>
                </div>

                {mediaType?.startsWith('image/') ? (
                  <img
                    src={mediaPreview}
                    alt="Preview"
                    className="w-full h-auto max-h-[512px] object-contain"
                  />
                ) : mediaType?.startsWith('video/') ? (
                  <video
                    src={mediaPreview}
                    controls
                    className="w-full h-auto max-h-[512px] object-contain"
                  />
                ) : null}
              </div>
            </div>
          )}
        </DialogBody>

        <DialogFooter className="flex !justify-between gap-4 mt-2 !mb-3 w-full !px-0">
          <div className="flex items-center justify-between py-4 border-t border-gray-200 dark:border-gray-800 w-full">
            <div className="flex items-center space-x-2 w-full justify-between px-3">
              <div className="flex items-center space-x-2 gap-3 w-full">
                <Button
                  variant="ghost"
                  onClick={handleUpdate}
                  disabled={
                    isUpdating ||
                    (!content.trim() && !mediaPreview) ||
                    content.length > MAX_CHARS ||
                    !hasChanges
                  }
                  className="bg-turquoise hover:bg-turquoise/80 rounded-lg text-white hover:text-white w-full h-12"
                >
                  {isUpdating ? 'Updating...' : 'Update Post'}
                </Button>

                <Button
                  variant="ghost"
                  onClick={onClose}
                  disabled={isUpdating}
                  className="bg-red-600 hover:bg-red-800 rounded-lg text-white hover:text-white w-full h-12"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default EditPostDialog;