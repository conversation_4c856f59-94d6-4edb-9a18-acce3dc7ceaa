'use client';

import { useEffect, useState } from 'react';
import { CookieCategory, isCookieCategoryAllowed } from '@/utils/cookieConsent';

export function useCookieConsent(category: CookieCategory = 'essential') {
  const [isAllowed, setIsAllowed] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Only run on client-side
    if (typeof window !== 'undefined') {
      setIsAllowed(isCookieCategoryAllowed(category));
      setIsLoaded(true);
    }
  }, [category]);

  return { isAllowed, isLoaded };
}
