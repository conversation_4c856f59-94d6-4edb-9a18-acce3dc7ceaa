'use client';

import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import MessagesClient from '@/components/messages/MessagesClient';
import { useParams } from 'next/navigation';

export default function MessagesUserPage() {
    const params = useParams();
    const partnerId = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';

    // Fetch messages for this conversation
    const result = useQuery(api.messages.fetchMessages, {
        partnerId,
        limit: 50,
    });

    const messages = result?.messages || [];
    const isLoading = result === undefined;
    const error = result && 'error' in result ? result.error : null;

    // Optionally, fetch partner info if needed (from the first message)
    const partner =
        messages.length > 0
            ? messages[0].sender_id === partnerId
                ? messages[0].sender
                : messages[0].receiver
            : null;

    if (isLoading) {
        return <div className="text-center w-full h-full flex items-center justify-center">Loading...</div>;
    }
    if (error) {
        return <div className="text-xl text-red-500 w-full h-full flex items-center justify-center">Error: {error}</div>;
    }

    return (
        <MessagesClient
            initialMessages={messages}
            initialChatId={partnerId}
            hasMore={false}
            totalMessages={messages.length}
            recipient={partner}
        />
    );
}