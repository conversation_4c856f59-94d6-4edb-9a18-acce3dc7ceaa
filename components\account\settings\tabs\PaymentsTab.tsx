'use client';

import { motion } from 'framer-motion';

const PaymentsTab = () => {
  return (
    <motion.div
      key="payments"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
    >
      <h2 className="text-xl font-semibold text-white mb-4">Payments</h2>
      <input type="text" placeholder="Card Number" className="w-full bg-gray-700 text-white p-2 rounded mb-2" />
      <input type="text" placeholder="Expiry Date" className="w-full bg-gray-700 text-white p-2 rounded" />
    </motion.div>
  );
};

export default PaymentsTab;