"use client";

import React, { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { RiLoader3Fill } from "react-icons/ri";
import {
  IconBrandGithub,
  IconBrandGoogle,
  IconBrandFacebook,
  IconBrandTwitter,
  IconBrandDiscord,
  IconBrandLinkedin,
  IconBrandReddit,
  IconBrandTwitch,
} from "@tabler/icons-react";
import { useAuthActions } from "@convex-dev/auth/react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useDebounce } from "@/hooks/useDebounce";
import { useDispatch } from 'react-redux';
import { setUserInfo } from '@/redux/slices/userInfoSlice';
import { useUser } from '@/hooks/useUser';

interface SignupFormProps {
  onSuccess: (data: any) => void;
  userType: string;
  isCreator: boolean;
}

export function SignupForm({ onSuccess, userType, isCreator }: SignupFormProps) {
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
  });

  const [errors, setErrors] = useState({
    username: "",
    email: "",
    password: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [usernameStatus, setUsernameStatus] = useState<'unchanged' | 'checking' | 'available' | 'unavailable'>('unchanged');

  // Convex mutations
  const creatorSignup = useMutation(api.accounts.creatorSignup);

  // Debounce the username for checking
  const debouncedUsername = useDebounce(formData.username, 500);

  // Check username availability using Convex
  const isUsernameTaken = useQuery(
    api.accounts.checkUsername,
    debouncedUsername ? { username: debouncedUsername } : 'skip'
  );

  // Update username status based on the query result
  useEffect(() => {
    if (!debouncedUsername) {
      setUsernameStatus('unchanged');
      return;
    }
    if (isUsernameTaken === undefined) {
      setUsernameStatus('checking');
    } else if (isUsernameTaken?.available === false) {
      setUsernameStatus('unavailable');
    } else {
      setUsernameStatus('available');
    }
  }, [isUsernameTaken, debouncedUsername]);

  // Update errors based on username status
  useEffect(() => {
    if (usernameStatus === 'unavailable') {
      setErrors(prev => ({ ...prev, username: "Username is already taken" }));
    } else if (usernameStatus === 'available') {
      setErrors(prev => ({ ...prev, username: "" }));
    }
  }, [usernameStatus]);

  const dispatch = useDispatch();
  const { signIn } = useAuthActions();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({ username: "", email: "", password: "" });

    // Basic validation
    let hasErrors = false;
    const newErrors = { username: "", email: "", password: "" };

    if (!formData.username.trim()) {
      newErrors.username = "Username is required";
      hasErrors = true;
    } else if (usernameStatus === 'checking') {
      newErrors.username = "Please wait while we check username availability";
      hasErrors = true;
    } else if (usernameStatus === 'unavailable') {
      newErrors.username = "Username is already taken";
      hasErrors = true;
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Invalid email address";
      hasErrors = true;
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
      hasErrors = true;
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Use Convex mutation instead of API route
      const signupResult = await creatorSignup({
        email: formData.email,
        password: formData.password,
        username: formData.username,
        userType: userType,
        user_info: {
          account: {
            username: formData.username,
            email: formData.email,
            accountType: userType,
            displayName: formData.username, // Use username as default display name
          },
        },
        ip_address: "unknown", // You might want to get this from the client
        user_agent: "unknown", // You might want to get this from the client
      });

      if (!signupResult.success) {
        setErrors({
          ...newErrors,
          email: signupResult.message || "Failed to sign up. Please try again.",
        });
        setIsSubmitting(false);
        return;
      }

      // Extract user ID from the Convex response
      const userId = signupResult.user?.user_id;

      if (!userId) {
        setErrors({
          ...newErrors,
          email: "User ID not found in response",
        });
        setIsSubmitting(false);
        return;
      }

      // Step 2: If Convex signup succeeds, sign in with Convex Auth custom-provider
      const session = await signIn("custom-provider", {
        email: formData.email,
        password: formData.password,
        userType: userType,
        flow: "signIn",
      });

      onSuccess(session);
    } catch (error) {
      console.error("Signup failed:", error);
      setErrors({
        ...newErrors,
        email: "An error occurred. Please try again.",
      });
      setIsSubmitting(false);
    }
  };

  const handleSocialLogin = (provider: string) => {
    signIn(provider.toLowerCase(), { callbackUrl: "/" });
  };

  return (
    <div className="w-full p-6 rounded-lg border border-[#18181b] dark:border-white bg-white/5 dark:bg-[#18181b]/5">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-6">
          {/* Username */}
          <div>
            <Label htmlFor="username" className="text-[#18181b] dark:text-white">
              Username
            </Label>
            <input
              id="username"
              placeholder="Enter a username"
              type="text"
              value={formData.username}
              onChange={(e) =>
                setFormData({ ...formData, username: e.target.value })
              }
              className="flex h-10 w-full mt-2 border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
            />
            {usernameStatus === 'checking' && (
              <p className="text-gray-500 text-xs mt-1">Checking availability...</p>
            )}
            {usernameStatus === 'available' && (
              <p className="text-green-500 text-xs mt-1">Username is available</p>
            )}
            {usernameStatus === 'unavailable' && (
              <p className="text-red-500 text-xs mt-1">Username is already taken</p>
            )}
            {errors.username && (
              <p className="text-red-500 text-xs mt-1">{errors.username}</p>
            )}
          </div>

          {/* Email */}
          <div>
            <Label htmlFor="email" className="text-[#18181b] dark:text-white">
              Email Address
            </Label>
            <input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              className="flex h-10 w-full mt-2 border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email}</p>
            )}
          </div>

          {/* Password */}
          <div>
            <Label htmlFor="password" className="text-[#18181b] dark:text-white">
              Password
            </Label>
            <input
              id="password"
              placeholder="••••••••"
              type="password"
              value={formData.password}
              onChange={(e) =>
                setFormData({ ...formData, password: e.target.value })
              }
              className="flex h-10 w-full mt-2 border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
            />
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password}</p>
            )}
          </div>
        </div>

        <Button
          type="submit"
          className="w-full !bg-turquoise hover:bg-turquoise text-white"
          disabled={isSubmitting || usernameStatus === 'checking'}
        >
          {isSubmitting ? (
            <span className="flex items-center gap-1">
              <RiLoader3Fill className="h-4 w-4 animate-spin" />
              Signing Up...
            </span>
          ) : (
            "Sign Up"
          )}
        </Button>
      </form>

      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-300 dark:border-gray-700" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="px-2 text-gray-300 dark:text-gray-600">
            Or sign up with
          </span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {[
          { icon: IconBrandGoogle, label: "Google", bg: "bg-[#fff]", hover: "hover:bg-[#e0e0e0]", text: "text-[#18181b]" },
          { icon: IconBrandFacebook, label: "Facebook", bg: "bg-[#1877F2]", hover: "hover:bg-[#0C63D4]", text: "text-white" },
          { icon: IconBrandTwitter, label: "Twitter", bg: "bg-[#1DA1F2]", hover: "hover:bg-[#0C85D0]", text: "text-white" },
          { icon: IconBrandDiscord, label: "Discord", bg: "bg-[#5865F2]", hover: "hover:bg-[#4752C4]", text: "text-white" },
          { icon: IconBrandLinkedin, label: "LinkedIn", bg: "bg-[#0A66C2]", hover: "hover:bg-[#084E95]", text: "text-white" },
          { icon: IconBrandReddit, label: "Reddit", bg: "bg-[#FF4500]", hover: "hover:bg-[#CC3700]", text: "text-white" },
          { icon: IconBrandTwitch, label: "Twitch", bg: "bg-[#6441A4]", hover: "hover:bg-[#4B3178]", text: "text-white" },
          { icon: IconBrandGithub, label: "GitHub", bg: "bg-[#333]", hover: "hover:bg-[#24292E]", text: "text-white" },
        ].map((provider) => (
          <Button
            key={provider.label}
            onClick={() => handleSocialLogin(provider.label)}
            className={cn(
              "flex items-center gap-2",
              provider.bg,
              provider.hover,
              provider.text,
              "border border-[#18181b] dark:border-white"
            )}
          >
            <provider.icon className="h-4 w-4" />
            {provider.label}
          </Button>
        ))}
      </div>
    </div>
  );
};