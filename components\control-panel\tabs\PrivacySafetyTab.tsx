'use client';

import { useState, useEffect, useRef } from 'react';
import { updateUserSettings } from '@/lib/api/updateUserSettings';
import { toast } from 'react-toastify';
import { SettingsSection, type SettingOption } from '@/components/ui/settings-section';
import { motion } from 'framer-motion';

interface PrivacySafetyTabProps {
  userData: any;
  onFormChange: (hasChanges: boolean, updatedData?: any, isLoading?: boolean) => void;
};

const PrivacySafetyTab = ({ userData, onFormChange }: PrivacySafetyTabProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingSetting, setLoadingSetting] = useState<string | null>(null);
  const initialSettings = useRef(userData?.privacySettings || {});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Handle beforeunload event
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isLoading || hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isLoading, hasUnsavedChanges]);

  const handleSettingChange = async (
    setting: string,
    value: boolean | string
  ) => {
    setLoadingSetting(setting);
    setIsLoading(true);
    onFormChange(true, undefined, true); // Notify about loading state

    try {
      const result = await updateUserSettings(
        userData.userId,
        userData.accountType,
        'privacy',
        { [setting]: value }
      );

      if (result.success) {
        const updatedUserData = {
          ...userData,
          privacySettings: {
            ...userData.privacySettings,
            [setting]: value
          }
        };

        initialSettings.current = {
          ...initialSettings.current,
          [setting]: value
        };

        onFormChange(false, updatedUserData, false);
        setHasUnsavedChanges(false);
        toast.success('Setting updated successfully');
      } else {
        throw new Error('Failed to update setting');
      }
    } catch (error) {
      toast.error('Failed to update setting');
      onFormChange(true, userData, false);
      throw error;
    } finally {
      setLoadingSetting(null);
      setIsLoading(false);
      onFormChange(hasUnsavedChanges, undefined, false);
    }
  };

  // Check for changes whenever settings are modified
  useEffect(() => {
    const currentSettings = userData?.privacySettings || {};
    const hasChanges = JSON.stringify(initialSettings.current) !== JSON.stringify(currentSettings);
    setHasUnsavedChanges(hasChanges);

    // Notify parent component about changes and loading state
    if (onFormChange) {
      onFormChange(hasChanges, undefined, isLoading);
    }
  }, [userData?.privacySettings, onFormChange, isLoading]);

  const getOptionWithLoadingState = (option: SettingOption): SettingOption => {
    const isLoading = loadingSetting === option.id;
    const isDisabled = isLoading || loadingSetting !== null; // Disable all options while any is loading

    return {
      ...option,
      disabled: isDisabled,
      className: isDisabled ? 'opacity-50 cursor-not-allowed' : '',
      loading: isLoading
    };
  };

  const privacyOptions: SettingOption[] = [
    {
      id: 'accountVisibility',
      label: 'Account Visibility',
      description: 'Make your account public or private. Toggle on means private, toggle off means public',
      type: 'switch' as const,
      value: userData?.privacySettings?.accountVisibility === 'private',
      onChange: async (checked: any) => handleSettingChange('accountVisibility', checked ? 'private' : 'public')
    },
    {
      id: 'profilePrivacy',
      label: 'Profile Privacy',
      description: 'Control who can see your profile',
      type: 'select' as const,
      value: userData?.privacySettings?.profilePrivacy || 'everyone',
      options: [
        { value: 'everyone', label: 'Everyone' },
        { value: 'subscribers', label: 'Subscribers Only' }
      ],
      onChange: async (value: any) => handleSettingChange('profilePrivacy', value)
    },
    {
      id: 'hideSubscribers',
      label: 'Hide Subscriber Count',
      description: 'Hide your subscriber count from other users',
      type: 'switch' as const,
      value: userData?.privacySettings?.hideSubscribers || false,
      onChange: async (checked: any) => handleSettingChange('hideSubscribers', checked)
    },
    {
      id: 'locationPrivacy',
      label: 'Location Privacy',
      description: 'Hide your location information from other creators, location is automatically hidden for regular users',
      type: 'switch' as const,
      value: userData?.privacySettings?.locationPrivacy || false,
      onChange: async (checked: any) => handleSettingChange('locationPrivacy', checked)
    },
    {
      id: 'searchVisibility',
      label: 'Search Visibility',
      description: 'Allow others to find you in search',
      type: 'switch' as const,
      value: userData?.privacySettings?.searchVisibility || false,
      onChange: async (checked: any) => handleSettingChange('searchVisibility', checked)
    }
  ].map(getOptionWithLoadingState);

  const messagingOptions: SettingOption[] = [
    {
      id: 'directMessages',
      label: 'Direct Messages',
      description: 'Control who can send you messages',
      type: 'select' as const,
      value: userData?.privacySettings?.directMessages || 'everyone',
      options: [
        { value: 'everyone', label: 'Everyone' },
        { value: 'subscribers', label: 'Subscribers Only' },
        { value: 'none', label: 'No One' }
      ],
      onChange: async (value: any) => handleSettingChange('directMessages', value)
    },
    {
      id: 'commentSettings',
      label: 'Comment Settings',
      description: 'Control who can comment on your posts',
      type: 'select' as const,
      value: userData?.privacySettings?.commentSettings || 'everyone',
      options: [
        { value: 'everyone', label: 'Everyone' },
        { value: 'subscribers', label: 'Subscribers Only' },
        { value: 'none', label: 'No One' }
      ],
      onChange: async (value: any) => handleSettingChange('commentSettings', value)
    },
    {
      id: 'taggingMentions',
      label: 'Tagging & Mentions',
      description: 'Control who can tag or mention you',
      type: 'select' as const,
      value: userData?.privacySettings?.taggingMentions || 'everyone',
      options: [
        { value: 'everyone', label: 'Everyone' },
        { value: 'subscribers', label: 'Subscribers Only' },
        { value: 'none', label: 'No One' }
      ],
      onChange: async (value: any) => handleSettingChange('taggingMentions', value)
    },
    {
      id: 'storyVisibility',
      label: 'Story Visibility',
      description: 'Control who can see your stories',
      type: 'select' as const,
      value: userData?.privacySettings?.storyVisibility || 'everyone',
      options: [
        { value: 'everyone', label: 'Everyone' },
        { value: 'subscribers', label: 'Subscribers Only' },
        { value: 'none', label: 'No One' }
      ],
      onChange: async (value: any) => handleSettingChange('storyVisibility', value)
    }
  ].map(getOptionWithLoadingState);

  const premiumMessageOptions: SettingOption[] = [
    {
      id: 'blockChatBlasts',
      label: (
        <div className="flex items-center">
          <span>BLOCK CHAT BLASTS</span>
          <span className="ml-2 px-2 py-0.5 text-xs font-semibold bg-pink-500 text-white rounded">
            PRIME
          </span>
        </div>
      ),
      description: 'Opt-out of receiving mass direct messages from Slushy creators',
      type: 'switch' as const,
      value: userData?.privacySettings?.blockChatBlasts || false,
      onChange: async (checked: any) => handleSettingChange('blockChatBlasts', checked)
    },
    {
      id: 'blockColdDMs',
      label: (
        <div className="flex items-center">
          <span>BLOCK COLD DMS</span>
          <span className="ml-2 px-2 py-0.5 text-xs font-semibold bg-pink-500 text-white rounded">
            PRIME
          </span>
        </div>
      ),
      description: 'Opt-out of receiving direct messages from creators you haven\'t added or purchased from',
      type: 'switch' as const,
      value: userData?.privacySettings?.blockColdDMs || false,
      onChange: async (checked: any) => handleSettingChange('blockColdDMs', checked)
    }
  ].map(getOptionWithLoadingState);

  const safetyOptions: SettingOption[] = [
    {
      id: 'sensitiveContent',
      label: 'Sensitive Content Filter',
      description: 'Filter potentially sensitive content',
      type: 'switch' as const,
      value: userData?.privacySettings?.sensitiveContent || false,
      onChange: async (checked: any) => handleSettingChange('sensitiveContent', checked)
    }
  ].map(getOptionWithLoadingState);

  const customizationOptions: SettingOption[] = [
    {
      id: 'customizationFrequency',
      label: 'Customization Frequency',
      description: 'Control how often you receive customization requests',
      type: 'select' as const,
      value: userData?.privacySettings?.customizationFrequency || 'everyone',
      options: [
        { value: 'everyone', label: 'Everyone' },
        { value: 'subscribers', label: 'Subscribers Only' },
        { value: 'none', label: 'No One' }
      ],
      onChange: async (value: any) => handleSettingChange('customizationFrequency', value)
    }
  ].map(getOptionWithLoadingState);

  return (
    <motion.div
      key="privacy"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`space-y-6 ${isLoading ? 'pointer-events-none' : ''}`}
    >
      <SettingsSection
        title="Privacy Settings"
        options={privacyOptions}
      />

      <SettingsSection
        title="Premium Message Settings"
        options={premiumMessageOptions}
      />

      <SettingsSection
        title="Messaging & Interaction Controls"
        options={messagingOptions}
      />

      <SettingsSection
        title="Content & Safety"
        options={safetyOptions}
      />

      <SettingsSection
        title="Customization & Frequency Controls"
        options={customizationOptions}
      />
    </motion.div>
  );
};
export default PrivacySafetyTab;
