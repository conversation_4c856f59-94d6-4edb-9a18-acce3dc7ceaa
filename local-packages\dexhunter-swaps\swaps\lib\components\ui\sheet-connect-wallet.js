import { jsx as t, jsxs as x } from "react/jsx-runtime";
import * as o from "react";
import { $ as r, a as h, b as i, c as m, d as c, e as b, f as $, g as y } from "../../index-840f2930.js";
import { c as g } from "../../index-1d6812f7.js";
import { cn as a } from "../../lib.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const q = b, A = $, G = y, l = ({ ...e }) => /* @__PURE__ */ t(
  r,
  {
    ...e,
    container: document.getElementById("swap-main")
  }
);
l.displayName = r.displayName;
const p = o.forwardRef(({ className: e, ...s }, d) => /* @__PURE__ */ t(
  h,
  {
    className: a(
      "dhs-fixed dhs-dhs-inset-0 dhs-z-50 dhs-bg-opacity-80 dhs-backdrop-blur-sm",
      e
    ),
    ...s,
    ref: d
  }
));
p.displayName = h.displayName;
const N = g(
  "dhs-absolute dhs-z-50 dhs-gap-4 dhs-bg-background dhs-pt-3 dhs-pb-5 dhs-shadow-lg",
  {
    variants: {
      side: {
        top: "dhs-inset-x-0 dhs-top-0 dhs-translate-y-[2rem] dhs-mx-4 dhs-rounded-[21px] data-[state=closed]:dhs-slide-out-to-top data-[state=open]:dhs-slide-in-from-top",
        bottom: "dhs-inset-x-0 dhs-bottom-0 dhs-translate-y-[-4rem] dhs-mx-4 dhs-rounded-[21px] data-[state=closed]:dhs-slide-out-to-bottom data-[state=open]:dhs-slide-in-from-bottom",
        left: "dhs-inset-y-0 dhs-left-0 dhs-h-full dhs-w-3/4 dhs-border-r data-[state=closed]:dhs-slide-out-to-left data-[state=open]:dhs-slide-in-from-left sm:dhs-max-w-sm",
        right: "dhs-inset-y-0 dhs-right-0 dhs-h-full dhs-w-3/4 dhs-border-l data-[state=closed]:dhs-slide-out-to-right data-[state=open]:dhs-slide-in-from-right sm:dhs-max-w-sm"
      }
    },
    defaultVariants: {
      side: "right"
    }
  }
), u = o.forwardRef(({ side: e = "right", className: s, children: d, ...f }, n) => /* @__PURE__ */ x(l, { children: [
  /* @__PURE__ */ t(p, {}),
  /* @__PURE__ */ t(
    i,
    {
      ref: n,
      className: a(N({ side: e }), s),
      ...f,
      children: d
    }
  )
] }));
u.displayName = i.displayName;
const S = ({
  className: e,
  ...s
}) => /* @__PURE__ */ t(
  "div",
  {
    className: a(
      "dhs-flex dhs-flex-col dhs-space-y-2 dhs-text-center sm:dhs-text-left",
      e
    ),
    ...s
  }
);
S.displayName = "SheetHeader";
const w = ({
  className: e,
  ...s
}) => /* @__PURE__ */ t(
  "div",
  {
    className: a(
      "dhs-flex dhs-flex-col-reverse sm:dhs-flex-row sm:dhs-justify-end sm:dhs-space-x-2",
      e
    ),
    ...s
  }
);
w.displayName = "SheetFooter";
const v = o.forwardRef(({ className: e, ...s }, d) => /* @__PURE__ */ t(
  m,
  {
    ref: d,
    className: a(
      "dhs-text-lg dhs-font-semibold dhs-text-foreground",
      e
    ),
    ...s
  }
));
v.displayName = m.displayName;
const R = o.forwardRef(({ className: e, ...s }, d) => /* @__PURE__ */ t(
  c,
  {
    ref: d,
    className: a("dhs-text-sm dhs-text-muted-foreground", e),
    ...s
  }
));
R.displayName = c.displayName;
export {
  q as Sheet,
  G as SheetClose,
  u as SheetContent,
  R as SheetDescription,
  w as SheetFooter,
  S as SheetHeader,
  v as SheetTitle,
  A as SheetTrigger
};
