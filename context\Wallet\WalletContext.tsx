'use client';

import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { useWallet } from "@meshsdk/react";
import { useWallet as useAlgoWallet } from '@txnlab/use-wallet-react';
import { useRouter, usePathname } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { clearNotifications } from '@/redux/slices/notificationSlice';
import { clearUserInfo, setUserInfo } from '@/redux/slices/userInfoSlice';
import { setWallet, clearWalletItem, clearWallet } from '@/redux/slices/walletSlice';
import { useUserStatus } from '@/context/UserStatusContext';
import { useConnect, useConnectors } from 'wagmi';
import { request, RpcErrorCode, } from 'sats-connect';
import { Verifier } from "bip322-js";
import { getAddress, signMessage as signGemMessage } from '@gemwallet/api';
import { InjectedWalletProvider } from './injectedWallets';
import { extensionConfiguration } from './types';
import { getBalance } from '@wagmi/core';
import { useSwitchChain, useSignMessage } from 'wagmi';
import { recoverMessageAddress } from 'viem';
import useWalletBalance from '@/hooks/useWalletBalance';
import Torus from "@toruslabs/torus-embed";
import { config } from '../../config';
import { toast } from 'react-toastify';
import sdk from '@crossmarkio/sdk';
//import useVechainWalletIntegration from './VechainWallet';
//import { WalletSource as VechainWalletSource } from '@vechain/dapp-kit'; // Import the WalletSource from the library
import Swal from 'sweetalert2';
import { chainIdMap, elFormatter, searchForSubstring, convertSatoshisToBitcoin, bytesToBase64, stringToHex } from '@/public/main';
import Cookies from 'js-cookie';
import { BrowserWallet } from '@meshsdk/core';
import { useAuthActions } from '@convex-dev/auth/react';
import { useUser } from '@/hooks/useUser';
import io from 'socket.io-client';

// Define types
interface Wallet {
    connect: () => Promise<void>;
    getAccounts: () => Promise<{ address: string }[]>;
    signer?: any;
}

interface PhantomProvider {
    isPhantom: boolean;
    requestAccounts: () => Promise<{ address: string }[]>;
    signMessage: (address: string, message: Uint8Array) => Promise<{ signature: Uint8Array }>;
}

interface WalletProviderProps {
    children: React.ReactNode;
    initialSession?: any;
}

interface WalletState {
    userId: string | null;
    walletAddress: string | null;
    solWalletAddress: string | null;
    blockchain: string | null;
    walletType: string | null;
    evmConnector: any;
    isConnecting: boolean;
    isConnected: boolean;
}

const WalletContext = createContext<{
    cardanoLogin: (walletType: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    cardanoSignup: (walletType: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    bitcoinLogin: (walletName: string, walletProvider: any, accountType: string) => Promise<{ success: boolean; message: string }>;
    bitcoinSignup: (walletName: string, walletProvider: any, accountType: string) => Promise<{ success: boolean; message: string }>;
    evmLogin: (walletName: string, blockchain: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    evmSignup: (walletName: string, blockchain: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    algorandLogin: (walletName: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    algorandSignup: (walletName: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    xrpLogin: (walletName: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    xrpSignup: (walletName: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    seiLogin: (walletName: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    seiSignup: (walletName: string, accountType: string) => Promise<{ success: boolean; message: string }>;
    polkadotLogin: (walletName: string, accountType: string) => Promise<{ success: boolean; message?: string; error?: string }>;
    polkadotSignup: (walletName: string, accountType: string) => Promise<{ success: boolean; message?: string; error?: string }>;
    logOut: (loginType?: string) => Promise<void>;
    getPhantomBitcoinProvider: () => PhantomProvider | undefined;
    connectors: any[];
    activeAccount: any;
    torus: Torus;
    retrieveBalance: (unitToFilter?: string, blockchain?: string) => Promise<number>;
    verifyWalletOwnership: (accountType: string, walletType: string, reason?: string) => Promise<{ success: boolean; reason?: string; nonce?: string }>;
    backendGetNonce: (userAddress: string, accountType: string, reason?: string) => Promise<string>;
    backendVerifySignature: (userAddress: string, signature: string, reason?: string) => Promise<any>;
    signTransactions: any;
    request: any;
    algoWallets: any[];
    polkadotWallets: Wallet[];
    polkadotWalletProvider: any;
    session: any;
    sessionStatus: string;
    syncSession: () => Promise<any>;
    handleDisconnect: () => Promise<void>;
}>({
    cardanoLogin: async () => ({ success: false, message: 'Not implemented' }),
    cardanoSignup: async () => ({ success: false, message: 'Not implemented' }),
    bitcoinLogin: async () => ({ success: false, message: 'Not implemented' }),
    bitcoinSignup: async () => ({ success: false, message: 'Not implemented' }),
    evmLogin: async () => ({ success: false, message: 'Not implemented' }),
    evmSignup: async () => ({ success: false, message: 'Not implemented' }),
    algorandLogin: async () => ({ success: false, message: 'Not implemented' }),
    algorandSignup: async () => ({ success: false, message: 'Not implemented' }),
    xrpLogin: async () => ({ success: false, message: 'Not implemented' }),
    xrpSignup: async () => ({ success: false, message: 'Not implemented' }),
    seiLogin: async () => ({ success: false, message: 'Not implemented' }),
    seiSignup: async () => ({ success: false, message: 'Not implemented' }),
    polkadotLogin: async () => ({ success: false, message: 'Not implemented' }),
    polkadotSignup: async () => ({ success: false, message: 'Not implemented' }),
    logOut: async () => { },
    getPhantomBitcoinProvider: () => undefined,
    connectors: [],
    activeAccount: null,
    torus: new Torus({}),
    retrieveBalance: async () => 0,
    verifyWalletOwnership: async () => ({ success: false, reason: 'Not implemented' }),
    backendGetNonce: async () => '',
    backendVerifySignature: async () => ({}),
    signTransactions: null,
    request: null,
    algoWallets: [],
    polkadotWallets: [],
    polkadotWalletProvider: null,
    session: null,
    sessionStatus: 'unauthenticated',
    syncSession: async () => null,
    handleDisconnect: async () => { },
});

export const useWalletContext = () => useContext(WalletContext);

export const WalletProvider = ({ children, initialSession }: WalletProviderProps) => {
    const { disconnect } = useWallet();
    const connectors = useConnectors();
    const { signIn, signOut } = useAuthActions();
    const { isAuthenticated, token: userToken } = useUser();
    const [session, setSession] = useState(initialSession);
    const [sessionStatus, setSessionStatus] = useState(initialSession ? 'authenticated' : 'unauthenticated');

    const APP_NAME = 'Sugar Club';

    const torus = new Torus({
        buttonPosition: 'top-right',
        modalZIndex: *********,
    });

    const [polkadotWalletProvider, setPolkadotWalletProvider] = useState<any>(null);
    const [polkadotWallets, setPolkadotWallets] = useState<Wallet[]>([]);

    const { userId, walletAddress, solWalletAddress, blockchain, walletType, evmConnector, isConnecting, isConnected } = useSelector((state: { wallet: WalletState }) => state.wallet);

    const { wallets: algoWallets, activeWallet, activeAccount, signTransactions } = useAlgoWallet();

    const { walletBalance } = useWalletBalance();

    const { switchChainAsync } = useSwitchChain({ config });

    const {updateUserStatus} = useUserStatus();

    const walletLoginSignMessage = 'Sign and verify to log in with this wallet: ';
    const walletSignupSignMessage = 'Sign and verify to sign up with this wallet: ';

    const router = useRouter();
    const activeComponent = usePathname().substring(1) || 'home';

    const isMessagesActive = activeComponent.includes('messages');
    const isProfileActive = activeComponent.includes('account');

    const dispatch = useDispatch();

    const getPhantomBitcoinProvider = (): PhantomProvider | undefined => {
        if ('phantom' in window) {
            const provider = (window as any).phantom?.bitcoin;
            if (provider && provider.isPhantom) {
                return provider as PhantomProvider;
            }
        }
        return undefined;
    };

    const syncSession = async () => {
        try {
            const res = await fetch('/api/auth/session');
            if (res.ok) {
                const sessionData = await res.json();
                setSession(sessionData);
                setSessionStatus(sessionData?.user ? 'authenticated' : 'unauthenticated');
                return sessionData;
            }
            return null;
        } catch (error) {
            console.error('Error syncing session:', error);
            return null;
        }
    };

    const handleDisconnect = async () => {
        try {
            dispatch(clearWallet());
            dispatch(clearUserInfo());
            await signOut();
            await syncSession();
            if (disconnect) {
                await disconnect();
            }

            if(isProfileActive) router.push('/');
        } catch (error) {
            console.error('Error disconnecting wallet:', error);
            toast.error('Failed to disconnect wallet');
        }
    };

    const bitcoinLogin = async (walletName: string, walletProvider: any, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: 'Already connecting' };
        }

        dispatch(setWallet({ isConnecting: true, isSolConnecting: true }));

        try {
            await syncSession();
            if (sessionStatus === 'authenticated') {
                return { success: false, message: 'Already logged in' };
            }

            const url = accountType === 'user' ? '/api/user-login' : '/api/creator-login';
            let btcAddress: string | undefined;
            let ordinalsAddress: string | undefined;

            if (walletName.includes('Leather')) {
                const response = await request(
                    'getAddresses',
                    { purposes: ['payment', 'ordinals'], message: '' },
                    walletProvider,
                );

                if (response.status === 'success') {
                    btcAddress = response.result.addresses[0].address;
                    ordinalsAddress = response.result.addresses[1].address;

                    if (btcAddress && ordinalsAddress) {
                        const message = await request(
                            'signMessage',
                            { message: walletLoginSignMessage, address: btcAddress },
                            walletProvider,
                        );

                        if (message.status === 'success') {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                credentials: 'include',
                                body: JSON.stringify({ address: btcAddress, blockchain: 'Bitcoin' }),
                            });

                            const userLogin = await res.json();

                            if (userLogin.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: btcAddress,
                                    walletAddress: btcAddress,
                                    userId: userLogin.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(
                                        setWallet({
                                            userId: userLogin.user?.user_id,
                                            walletType: walletName,
                                            walletAddress: btcAddress,
                                            blockchain: 'Bitcoin',
                                            btcWalletProvider: walletProvider,
                                            isConnected: true,
                                            stakeKey: null,
                                            wallet: null,
                                            solWalletAddress: null,
                                            solWalletProvider: null,
                                            evmConnector: null,
                                            solWalletType: null,
                                            isSolConnected: false,
                                            isSolConnecting: false,
                                            accountType: userLogin.user?.user_info.accountType,
                                        }),
                                    );

                                    dispatch(
                                        setUserInfo({
                                            username: userLogin.user.user_info.account.username,
                                            displayName: userLogin.user.user_info.account.displayName,
                                            profilePhoto: userLogin.user.user_info.profilePhoto,
                                        }),
                                    );

                                    return { success: true, message: 'Login successful' };
                                }
                            } else {
                                if (userLogin.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (
                                    userLogin.message.includes('Invalid credentials') ||
                                    userLogin.message.includes('Authentication failed')
                                ) {
                                    toast.error(`${userLogin.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                        icon: 'error',
                                        confirmButtonText: 'Close',
                                    });
                                }
                                return { success: false, message: 'Something went wrong. Please try again later.' };
                            }
                        } else {
                            if (message.error.code === RpcErrorCode.USER_REJECTION) {
                                console.error('User rejected the request.');
                                return { success: false, message: 'User rejected the request.' };
                            } else {
                                console.error('Error signing message:', message.error);
                                toast.error('Error signing message, please try again!');
                                return { success: false, message: 'Something went wrong. Please try again later.' };
                            }
                        }
                    } else {
                        console.error('No available addresses found in the wallet.');
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: 'No available addresses found in the wallet.' };
                    }
                } else {
                    console.error(response.error.message);
                    toast.error(response.error.message);
                    return { success: false, message: 'Something went wrong. Please try again later.' };
                }
            } else if (walletName.includes('Phantom')) {
                const phantom = getPhantomBitcoinProvider();
                if (!phantom) {
                    toast.error('Phantom wallet not found.');
                    return { success: false, message: 'Phantom wallet not found.' };
                }

                const accounts = await phantom.requestAccounts();
                btcAddress = Object.values(accounts as Record<string, { purpose: string; address: string }>).find(
                    (x) => x.purpose === 'payment',
                )?.address;
                ordinalsAddress = Object.values(accounts as Record<string, { purpose: string; address: string }>).find(
                    (x) => x.purpose === 'ordinals',
                )?.address;

                if (btcAddress && ordinalsAddress) {
                    const message = new TextEncoder().encode(walletLoginSignMessage);
                    const { signature } = await phantom.signMessage(btcAddress, message);

                    const isValidSignature = Verifier.verifySignature(
                        btcAddress,
                        new TextDecoder().decode(message),
                        bytesToBase64(signature),
                    );

                    if (isValidSignature) {
                        const headers = new Headers();
                        headers.append('Content-Type', 'application/json');

                        const res = await fetch(url, {
                            method: 'POST',
                            headers,
                            body: JSON.stringify({ address: btcAddress, blockchain: 'Bitcoin' }),
                        });

                        const userLogin = await res.json();

                        if (userLogin.success) {
                            const currentSession = await signIn('credentials', {
                                redirect: false,
                                address: btcAddress,
                                walletAddress: btcAddress,
                                userId: userLogin.user?.user_id,
                            }) as any;

                            if (currentSession.ok) {
                                dispatch(
                                    setWallet({
                                        userId: userLogin.user?.user_id,
                                        walletType: walletName,
                                        walletAddress: btcAddress,
                                        blockchain: 'Bitcoin',
                                        btcWalletProvider: walletProvider,
                                        isConnected: true,
                                        stakeKey: null,
                                        wallet: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType,
                                    }),
                                );

                                dispatch(
                                    setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }),
                                );

                                return { success: true, message: 'User logged in successfully' };
                            }
                        } else {
                            if (userLogin.message.includes('User not found')) {
                                toast.error('No account found for this wallet. Sign up to get started.');
                            } else if (
                                userLogin.message.includes('Invalid credentials') ||
                                userLogin.message.includes('Authentication failed')
                            ) {
                                toast.error(`${userLogin.message} Please try again.`);
                            } else {
                                Swal.fire({
                                    html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                    icon: 'error',
                                    confirmButtonText: 'Close',
                                });
                            }
                            return { success: false, message: 'No account found for this wallet. Sign up to get started.' };
                        }
                    } else {
                        console.error('Error signing message, please try again!');
                        toast.error('Error signing message, please try again!');
                        return { success: false, message: 'Error signing message, please try again!' };
                    }
                } else {
                    console.error('No available addresses found in the wallet.');
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: 'No available addresses found in the wallet.' };
                }
            } else if (walletName.includes('Unisat')) {
                const unisat = (window as any).unisat;
                if (!unisat) {
                    toast.error('Unisat wallet not found.');
                    return { success: false, message: 'Unisat wallet not found.' };
                }

                const [address] = await unisat.requestAccounts();
                if (address) {
                    const message = await unisat.signMessage(walletLoginSignMessage, address);
                    if (message) {
                        const headers = new Headers();
                        headers.append('Content-Type', 'application/json');

                        const res = await fetch(url, {
                            method: 'POST',
                            headers,
                            body: JSON.stringify({ address, blockchain: 'Bitcoin' }),
                        });

                        const userLogin = await res.json();

                        if (userLogin.success) {
                            const currentSession = await signIn('credentials', {
                                redirect: false,
                                address,
                                walletAddress: address,
                                userId: userLogin.user?.user_id,
                            }) as any;

                            if (currentSession.ok) {
                                dispatch(
                                    setWallet({
                                        userId: userLogin.user?.user_id,
                                        walletType: walletName,
                                        walletAddress: address,
                                        blockchain: 'Bitcoin',
                                        btcWalletProvider: walletProvider,
                                        isConnected: true,
                                        stakeKey: null,
                                        wallet: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType,
                                    }),
                                );

                                dispatch(
                                    setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }),
                                );

                                return { success: true, message: 'Login successful' };
                            }
                        } else {
                            if (userLogin.message.includes('User not found')) {
                                toast.error('No account found for this wallet. Sign up to get started.');
                            } else if (
                                userLogin.message.includes('Invalid credentials') ||
                                userLogin.message.includes('Authentication failed')
                            ) {
                                toast.error(`${userLogin.message} Please try again.`);
                            } else {
                                Swal.fire({
                                    html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                    icon: 'error',
                                    confirmButtonText: 'Close',
                                });
                            }
                            return { success: false, message: 'No account found for this wallet. Sign up to get started.' };
                        }
                    } else {
                        console.error('Error signing message, please try again!');
                        toast.error('Error signing message, please try again!');
                        return { success: false, message: 'Error signing message, please try again!' };
                    }
                } else {
                    console.error('No available addresses found in the wallet.');
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: 'No available addresses found in the wallet.' };
                }
            } else if (walletName.includes('OKX')) {
                const okx = (window as any).okxwallet;
                if (!okx) {
                    toast.error('OKX wallet not found.');
                    return { success: false, message: 'OKX wallet not found.' };
                }

                const [address] = await okx.bitcoin.requestAccounts();
                if (address) {
                    const message = await okx.bitcoin.signMessage(walletLoginSignMessage);
                    if (message) {
                        const headers = new Headers();
                        headers.append('Content-Type', 'application/json');

                        const res = await fetch(url, {
                            method: 'POST',
                            headers,
                            body: JSON.stringify({ address, blockchain: 'Bitcoin' }),
                        });

                        const userLogin = await res.json();

                        if (userLogin.success) {
                            const currentSession = await signIn('credentials', {
                                redirect: false,
                                address,
                                walletAddress: address,
                                userId: userLogin.user?.user_id,
                            }) as any;

                            if (currentSession.ok) {
                                dispatch(
                                    setWallet({
                                        userId: userLogin.user?.user_id,
                                        walletType: walletName,
                                        walletAddress: address,
                                        blockchain: 'Bitcoin',
                                        btcWalletProvider: walletProvider,
                                        isConnected: true,
                                        stakeKey: null,
                                        wallet: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType,
                                    }),
                                );

                                dispatch(
                                    setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }),
                                );

                                return { success: true, message: 'User logged in successfully' };
                            }
                        } else {
                            if (userLogin.message.includes('User not found')) {
                                toast.error('No account found for this wallet. Sign up to get started.');
                            } else if (
                                userLogin.message.includes('Invalid credentials') ||
                                userLogin.message.includes('Authentication failed')
                            ) {
                                toast.error(`${userLogin.message} Please try again.`);
                            } else {
                                Swal.fire({
                                    html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                    icon: 'error',
                                    confirmButtonText: 'Close',
                                });
                            }
                            return { success: false, message: 'No account found for this wallet. Sign up to get started.' };
                        }
                    } else {
                        console.error('Error signing message, please try again!');
                        toast.error('Error signing message, please try again!');
                        return { success: false, message: 'Error signing message, please try again!' };
                    }
                } else {
                    console.error('No available addresses found in the wallet.');
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: 'No available addresses found in the wallet.' };
                }
            } else {
                const response = await request(
                    'getAccounts',
                    { purposes: ['payment', 'ordinals'], message: '' },
                    walletProvider,
                );

                if (response.status === 'success') {
                    btcAddress = (response.result as Array<{ purpose: string; address: string }>).find(
                        (x) => x.purpose === 'payment',
                    )?.address;
                    ordinalsAddress = (response.result as Array<{ purpose: string; address: string }>).find(
                        (x) => x.purpose === 'ordinals',
                    )?.address;

                    if (btcAddress && ordinalsAddress) {
                        const message = await request(
                            'signMessage',
                            { message: walletLoginSignMessage, address: btcAddress },
                            walletProvider,
                        );

                        if (message.status === 'success') {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({ address: btcAddress, blockchain: 'Bitcoin' }),
                            });

                            const userLogin = await res.json();

                            if (userLogin.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: btcAddress,
                                    walletAddress: btcAddress,
                                    userId: userLogin.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(
                                        setWallet({
                                            userId: userLogin.user?.user_id,
                                            walletType: walletName,
                                            walletAddress: btcAddress,
                                            blockchain: 'Bitcoin',
                                            btcWalletProvider: walletProvider,
                                            isConnected: true,
                                            stakeKey: null,
                                            wallet: null,
                                            solWalletAddress: null,
                                            solWalletProvider: null,
                                            evmConnector: null,
                                            solWalletType: null,
                                            isSolConnected: false,
                                            isSolConnecting: false,
                                            accountType: userLogin.user?.user_info.accountType,
                                        }),
                                    );

                                    dispatch(
                                        setUserInfo({
                                            username: userLogin.user.user_info.account.username,
                                            displayName: userLogin.user.user_info.account.displayName,
                                            profilePhoto: userLogin.user.user_info.profilePhoto,
                                        }),
                                    );

                                    return { success: true, message: 'User logged in successfully' };
                                }
                            } else {
                                if (userLogin.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (
                                    userLogin.message.includes('Invalid credentials') ||
                                    userLogin.message.includes('Authentication failed')
                                ) {
                                    toast.error(`${userLogin.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                        icon: 'error',
                                        confirmButtonText: 'Close',
                                    });
                                }
                                return { success: false, message: 'No account found for this wallet. Sign up to get started.' };
                            }
                        } else {
                            if (message.error.code === RpcErrorCode.USER_REJECTION) {
                                console.error('User rejected the request.');
                                return { success: false, message: 'User rejected the request.' };
                            } else {
                                console.error('Error signing message:', message.error);
                                toast.error('Error signing message, please try again!');
                                return { success: false, message: 'Error signing message, please try again!' };
                            }
                        }
                    } else {
                        console.error('No available addresses found in the wallet.');
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: 'No available addresses found in the wallet.' };
                    }
                } else {
                    console.error(response.error);
                    toast.error('Something went wrong. Please try again later.');
                    return { success: false, message: 'Something went wrong. Please try again later.' };
                }
            }
        } catch (err) {
            console.error(err);
            toast.error('Something went wrong. Please try again later.');
            return { success: false, message: 'Something went wrong. Please try again later.' };
        } finally {
            dispatch(setWallet({ isConnecting: false, isSolConnecting: false }));
        }
    };

    const bitcoinSignup = async (walletName: string, walletProvider: any, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: 'Already connecting' };
        }

        dispatch(setWallet({ isConnecting: true, isSolConnecting: true }));

        try {
            const url = accountType === 'user' ? '/api/user-signup' : '/api/creator-signup';
            let btcAddress: string | undefined;
            let ordinalsAddress: string | undefined;

            if (walletName.includes('Leather')) {
                const response = await request(
                    'getAddresses',
                    { purposes: ['payment', 'ordinals'], message: '' },
                    walletProvider,
                );

                if (response.status === 'success') {
                    btcAddress = response.result.addresses[0].address;
                    ordinalsAddress = response.result.addresses[1].address;

                    if (btcAddress && ordinalsAddress) {
                        const message = await request(
                            'signMessage',
                            {
                                message: walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2),
                                address: btcAddress,
                            },
                            walletProvider,
                        );

                        if (message.status === 'success') {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                credentials: 'include',
                                body: JSON.stringify({ address: btcAddress, blockchain: 'Bitcoin', accountType }),
                            });

                            const userSignup = await res.json();

                            if (userSignup.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: btcAddress,
                                    walletAddress: btcAddress,
                                    userId: userSignup.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(
                                        setWallet({
                                            userId: userSignup.user?.user_id,
                                            walletType: walletName,
                                            walletAddress: btcAddress,
                                            blockchain: 'Bitcoin',
                                            btcWalletProvider: walletProvider,
                                            isConnected: true,
                                            stakeKey: null,
                                            wallet: null,
                                            solWalletAddress: null,
                                            solWalletProvider: null,
                                            evmConnector: null,
                                            solWalletType: null,
                                            isSolConnected: false,
                                            isSolConnecting: false,
                                            accountType: userSignup.user?.user_info.accountType,
                                        }),
                                    );

                                    dispatch(
                                        setUserInfo({
                                            username: userSignup.user.user_info.account.username,
                                            displayName: userSignup.user.user_info.account.displayName,
                                            profilePhoto: userSignup.user.user_info.profilePhoto,
                                        }),
                                    );

                                    toast.success('Your account has been created, sign up successful!');

                                    const campaignRedirect = Cookies.get('campaignRedirect');
                                    if (campaignRedirect && campaignRedirect === 'active') {
                                        router.push('/decentralized-advertisement-agency');
                                    } else {
                                        router.push('/account/settings');
                                    }

                                    return { success: true, message: 'User account created successfully' };
                                }
                            } else {
                                if (userSignup.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userSignup.message.includes('User already exists')) {
                                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                                } else if (
                                    userSignup.message.includes('Invalid credentials') ||
                                    userSignup.message.includes('Authentication failed')
                                ) {
                                    toast.error(`${userSignup.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                        icon: 'error',
                                        confirmButtonText: 'Close',
                                    });
                                }
                                return { success: false, message: 'Something went wrong. Please try again later.' };
                            }
                        } else {
                            if (message.error.code === RpcErrorCode.USER_REJECTION) {
                                console.error('User rejected the request.');
                                return { success: false, message: 'User rejected the request.' };
                            } else {
                                console.error('Error signing message:', message.error);
                                toast.error('Error signing message, please try again!');
                                return { success: false, message: 'Something went wrong. Please try again later.' };
                            }
                        }
                    } else {
                        console.error('No available addresses found in the wallet.');
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: 'No available addresses found in the wallet.' };
                    }
                } else {
                    console.error(response.error.message);
                    toast.error(response.error.message);
                    return { success: false, message: 'Something went wrong. Please try again later.' };
                }
            } else if (walletName.includes('Phantom')) {
                const phantom = getPhantomBitcoinProvider();
                if (!phantom) {
                    toast.error('Phantom wallet not found.');
                    return { success: false, message: 'Phantom wallet not found.' };
                }

                const accounts = await phantom.requestAccounts();
                btcAddress = Object.values(accounts as Record<string, { purpose: string; address: string }>).find(
                    (x) => x.purpose === 'payment',
                )?.address;
                ordinalsAddress = Object.values(accounts as Record<string, { purpose: string; address: string }>).find(
                    (x) => x.purpose === 'ordinals',
                )?.address;

                if (btcAddress && ordinalsAddress) {
                    const message = new TextEncoder().encode(walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2));
                    const { signature } = await phantom.signMessage(btcAddress, message);

                    const isValidSignature = Verifier.verifySignature(
                        btcAddress,
                        new TextDecoder().decode(message),
                        bytesToBase64(signature),
                    );

                    if (isValidSignature) {
                        const headers = new Headers();
                        headers.append('Content-Type', 'application/json');

                        const res = await fetch(url, {
                            method: 'POST',
                            headers,
                            body: JSON.stringify({ address: btcAddress, blockchain: 'Bitcoin', accountType }),
                        });

                        const userSignup = await res.json();

                        if (userSignup.success) {
                            const currentSession = await signIn('credentials', {
                                redirect: false,
                                address: btcAddress,
                                walletAddress: btcAddress,
                                userId: userSignup.user?.user_id,
                            }) as any;

                            if (currentSession.ok) {
                                dispatch(
                                    setWallet({
                                        userId: userSignup.user?.user_id,
                                        walletType: walletName,
                                        walletAddress: btcAddress,
                                        blockchain: 'Bitcoin',
                                        btcWalletProvider: walletProvider,
                                        isConnected: true,
                                        stakeKey: null,
                                        wallet: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType,
                                    }),
                                );

                                dispatch(
                                    setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }),
                                );

                                toast.success('Your account has been created, sign up successful!');

                                const campaignRedirect = Cookies.get('campaignRedirect');
                                if (campaignRedirect && campaignRedirect === 'active') {
                                    router.push('/decentralized-advertisement-agency');
                                } else {
                                    router.push('/account/settings');
                                }

                                return { success: true, message: 'User account created successfully' };
                            }
                        } else {
                            if (userSignup.message.includes('User not found')) {
                                toast.error('No account found for this wallet. Sign up to get started.');
                            } else if (userSignup.message.includes('User already exists')) {
                                toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                            } else if (
                                userSignup.message.includes('Invalid credentials') ||
                                userSignup.message.includes('Authentication failed')
                            ) {
                                toast.error(`${userSignup.message} Please try again.`);
                            } else {
                                Swal.fire({
                                    html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                    icon: 'error',
                                    confirmButtonText: 'Close',
                                });
                            }
                            return { success: false, message: 'Something went wrong. Please try again later.' };
                        }
                    } else {
                        console.error('Error signing message, please try again!');
                        toast.error('Error signing message, please try again!');
                        return { success: false, message: 'Error signing message, please try again!' };
                    }
                } else {
                    console.error('No available addresses found in the wallet.');
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: 'No available addresses found in the wallet.' };
                }
            } else if (walletName.includes('Unisat')) {
                const unisat = (window as any).unisat;
                if (!unisat) {
                    toast.error('Unisat wallet not found.');
                    return { success: false, message: 'Unisat wallet not found.' };
                }

                const [address] = await unisat.requestAccounts();
                if (address) {
                    const message = await unisat.signMessage(walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2), address);
                    if (message) {
                        const headers = new Headers();
                        headers.append('Content-Type', 'application/json');

                        const res = await fetch(url, {
                            method: 'POST',
                            headers,
                            body: JSON.stringify({ address, blockchain: 'Bitcoin', accountType }),
                        });

                        const userSignup = await res.json();

                        if (userSignup.success) {
                            const currentSession = await signIn('credentials', {
                                redirect: false,
                                address,
                                walletAddress: address,
                                userId: userSignup.user?.user_id,
                            }) as any;

                            if (currentSession.ok) {
                                dispatch(
                                    setWallet({
                                        userId: userSignup.user?.user_id,
                                        walletType: walletName,
                                        walletAddress: address,
                                        blockchain: 'Bitcoin',
                                        btcWalletProvider: walletProvider,
                                        isConnected: true,
                                        stakeKey: null,
                                        wallet: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType,
                                    }),
                                );

                                dispatch(
                                    setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }),
                                );

                                toast.success('Your account has been created, sign up successful!');

                                const campaignRedirect = Cookies.get('campaignRedirect');

                                if (campaignRedirect && campaignRedirect == 'active') {
                                    router.push('/decentralized-advertisement-agency');
                                } else if (!campaignRedirect || campaignRedirect != 'active') {
                                    router.push('/account/settings');
                                }

                                return { success: true, message: 'User account created successfully' };
                            }
                        } else {
                            if (userSignup.message.includes('User not found')) {
                                toast.error('No account found for this wallet. Sign up to get started.');
                            } else if (userSignup.message.includes('User already exists')) {
                                toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                            } else if (
                                userSignup.message.includes('Invalid credentials') ||
                                userSignup.message.includes('Authentication failed')
                            ) {
                                toast.error(`${userSignup.message} Please try again.`);
                            } else {
                                Swal.fire({
                                    html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                    icon: 'error',
                                    confirmButtonText: 'Close',
                                });
                            }
                            return { success: false, message: 'Something went wrong. Please try again later.' };
                        }
                    } else {
                        console.error('Error signing message, please try again!');
                        toast.error('Error signing message, please try again!');
                        return { success: false, message: 'Error signing message, please try again!' };
                    }
                } else {
                    console.error('No available addresses found in the wallet.');
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: 'No available addresses found in the wallet.' };
                }
            } else if (walletName.includes('OKX')) {
                const okx = (window as any).okxwallet;
                if (!okx) {
                    toast.error('OKX wallet not found.');
                    return { success: false, message: 'OKX wallet not found.' };
                }

                const [address] = await okx.bitcoin.requestAccounts();
                if (address) {
                    const message = await okx.bitcoin.signMessage(walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2));
                    if (message) {
                        const headers = new Headers();
                        headers.append('Content-Type', 'application/json');

                        const res = await fetch(url, {
                            method: 'POST',
                            headers,
                            body: JSON.stringify({ address, blockchain: 'Bitcoin', accountType }),
                        });

                        const userSignup = await res.json();

                        if (userSignup.success) {
                            const currentSession = await signIn('credentials', {
                                redirect: false,
                                address,
                                walletAddress: address,
                                userId: userSignup.user?.user_id,
                            }) as any;

                            if (currentSession.ok) {
                                dispatch(
                                    setWallet({
                                        userId: userSignup.user?.user_id,
                                        walletType: walletName,
                                        walletAddress: address,
                                        blockchain: 'Bitcoin',
                                        btcWalletProvider: walletProvider,
                                        isConnected: true,
                                        stakeKey: null,
                                        wallet: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType,
                                    }),
                                );

                                dispatch(
                                    setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }),
                                );

                                toast.success('Your account has been created, sign up successful!');

                                const campaignRedirect = Cookies.get('campaignRedirect');

                                if (campaignRedirect && campaignRedirect == 'active') {
                                    router.push('/decentralized-advertisement-agency');
                                } else if (!campaignRedirect || campaignRedirect != 'active') {
                                    router.push('/account/settings');
                                }

                                return { success: true, message: 'User account created successfully' };
                            }
                        } else {
                            if (userSignup.message.includes('User not found')) {
                                toast.error('No account found for this wallet. Sign up to get started.');
                            } else if (userSignup.message.includes('User already exists')) {
                                toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                            } else if (
                                userSignup.message.includes('Invalid credentials') ||
                                userSignup.message.includes('Authentication failed')
                            ) {
                                toast.error(`${userSignup.message} Please try again.`);
                            } else {
                                Swal.fire({
                                    html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                    icon: 'error',
                                    confirmButtonText: 'Close',
                                });
                            }
                            return { success: false, message: 'Something went wrong. Please try again later.' };
                        }
                    } else {
                        console.error('Error signing message, please try again!');
                        toast.error('Error signing message, please try again!');
                        return { success: false, message: 'Error signing message, please try again!' };
                    }
                } else {
                    console.error('No available addresses found in the wallet.');
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: 'No available addresses found in the wallet.' };
                }
            } else {
                const response = await request(
                    'getAccounts',
                    { purposes: ['payment', 'ordinals'], message: '' },
                    walletProvider,
                );

                if (response.status === 'success') {
                    btcAddress = (response.result as Array<{ purpose: string; address: string }>).find(
                        (x) => x.purpose === 'payment',
                    )?.address;
                    ordinalsAddress = (response.result as Array<{ purpose: string; address: string }>).find(
                        (x) => x.purpose === 'ordinals',
                    )?.address;

                    if (btcAddress && ordinalsAddress) {
                        const message = await request(
                            'signMessage',
                            {
                                message: walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2),
                                address: btcAddress,
                            },
                            walletProvider,
                        );

                        if (message.status === 'success') {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({ address: btcAddress, blockchain: 'Bitcoin', accountType }),
                            });

                            const userSignup = await res.json();

                            if (userSignup.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: btcAddress,
                                    walletAddress: btcAddress,
                                    userId: userSignup.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(
                                        setWallet({
                                            userId: userSignup.user?.user_id,
                                            walletType: walletName,
                                            walletAddress: btcAddress,
                                            blockchain: 'Bitcoin',
                                            btcWalletProvider: walletProvider,
                                            isConnected: true,
                                            stakeKey: null,
                                            wallet: null,
                                            solWalletAddress: null,
                                            solWalletProvider: null,
                                            evmConnector: null,
                                            solWalletType: null,
                                            isSolConnected: false,
                                            isSolConnecting: false,
                                            accountType: userSignup.user?.user_info.accountType,
                                        }),
                                    );

                                    dispatch(
                                        setUserInfo({
                                            username: userSignup.user.user_info.account.username,
                                            displayName: userSignup.user.user_info.account.displayName,
                                            profilePhoto: userSignup.user.user_info.profilePhoto,
                                        }),
                                    );

                                    toast.success('Your account has been created, sign up successful!');

                                    const campaignRedirect = Cookies.get('campaignRedirect');

                                    if (campaignRedirect && campaignRedirect == 'active') {
                                        router.push('/decentralized-advertisement-agency');
                                    } else if (!campaignRedirect || campaignRedirect != 'active') {
                                        router.push('/account/settings');
                                    }

                                    return { success: true, message: 'User account created successfully' };
                                }
                            } else {
                                if (userSignup.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userSignup.message.includes('User already exists')) {
                                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                                } else if (
                                    userSignup.message.includes('Invalid credentials') ||
                                    userSignup.message.includes('Authentication failed')
                                ) {
                                    toast.error(`${userSignup.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: 'Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.',
                                        icon: 'error',
                                        confirmButtonText: 'Close',
                                    });
                                }
                                return { success: false, message: 'Something went wrong. Please try again later.' };
                            }
                        } else {
                            if (message.error.code === RpcErrorCode.USER_REJECTION) {
                                console.error('User rejected the request.');
                                return { success: false, message: 'User rejected the request.' };
                            } else {
                                console.error('Error signing message:', message.error);
                                toast.error('Error signing message, please try again!');
                                return { success: false, message: 'Error signing message, please try again!' };
                            }
                        }
                    } else {
                        console.error('No available addresses found in the wallet.');
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: 'No available addresses found in the wallet.' };
                    }
                } else {
                    console.error(response.error);
                    toast.error('Something went wrong. Please try again later.');
                    return { success: false, message: 'Something went wrong. Please try again later.' };
                }
            }
        } catch (err) {
            console.error(err);
            toast.error('Something went wrong. Please try again later.');
            return { success: false, message: 'Something went wrong. Please try again later.' };
        } finally {
            dispatch(setWallet({ isConnecting: false, isSolConnecting: false }));
        }
    };

    const seiLogin = async (walletName: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        try {
            const chainId = "pacific-1";
            const url = accountType == 'user' ? '/api/user-login' : '/api/creator-login';

            if (walletName.includes('Keplr')) {

                await window.keplr.enable(chainId);

                const offlineSigner = window.keplr.getOfflineSigner(chainId);

                const accounts = await offlineSigner.getAccounts();

                if (accounts) {
                    const address = accounts[0]?.address;

                    if (address) {
                        const message = await window.keplr.signArbitrary(chainId, address, walletLoginSignMessage) as unknown as { signature: any };
                        if (message?.signature) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'Sei'
                                })
                            });

                            const userLogin = await res.json();

                            if (userLogin.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userLogin.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userLogin.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'Sei',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }));

                                    return { success: true, message: "Login successful" };
                                }

                            } else {
                                if (userLogin.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                                    toast.error(`${userLogin.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        } else {
                            throw new Error('Failed to sign message');
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Selected connector is undefined');
                    toast.error('Selected connector is undefined');
                    return { success: false, message: "Selected connector is undefined" };
                }
            } else if (walletName.includes('Fin')) {

                await window.fin.enable(chainId);

                const offlineSigner = window.fin.getOfflineSigner(chainId);

                const accounts = await offlineSigner.getAccounts();

                if (accounts) {
                    const address = accounts[0]?.address;

                    if (address) {
                        const message = await window.fin.signArbitrary(chainId, address, walletLoginSignMessage) as unknown as { signature: any };
                        if (message?.signature) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'Sei'
                                })
                            });

                            const userLogin = await res.json();

                            if (userLogin.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userLogin.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userLogin.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'Sei',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }));

                                    return { success: true, message: "Login successful" };
                                }

                            } else {
                                if (userLogin.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                                    toast.error(`${userLogin.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        } else {
                            throw new Error('Failed to sign message');
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Selected connector is undefined');
                    toast.error('Selected connector is undefined');
                    return { success: false, message: "Selected connector is undefined" };
                }
            } else if (walletName.includes('Compass')) {

                await window.compass.enable(chainId);

                const offlineSigner = window.compass.getOfflineSigner(chainId);

                const accounts = await offlineSigner.getAccounts();

                if (accounts) {
                    const address = accounts[0]?.address;

                    if (address) {
                        const message = await window.compass.signArbitrary(chainId, address, walletLoginSignMessage) as unknown as { signature: any };
                        if (message?.signature) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'Sei'
                                })
                            });

                            const userLogin = await res.json();

                            if (userLogin.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userLogin.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userLogin.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'Sei',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }));

                                    return { success: true, message: "Login successful" };
                                }

                            } else {
                                if (userLogin.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                                    toast.error(`${userLogin.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        } else {
                            throw new Error('Failed to sign message');
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Selected connector is undefined');
                    toast.error('Selected connector is undefined');
                    return { success: false, message: "Selected connector is undefined" };
                }
            } else if (walletName.includes('Leap')) {

                await window.leap.enable(chainId);

                const offlineSigner = window.leap.getOfflineSigner(chainId);

                const accounts = await offlineSigner.getAccounts();

                if (accounts) {
                    const address = accounts[0]?.address;

                    if (address) {
                        const message = await window.leap.signArbitrary(chainId, address, walletLoginSignMessage) as unknown as { signature: any };
                        if (message?.signature) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'Sei'
                                })
                            });

                            const userLogin = await res.json();

                            if (userLogin.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userLogin.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userLogin.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'Sei',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }));

                                    return { success: true, message: "Login successful" };
                                }

                            } else {
                                if (userLogin.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                                    toast.error(`${userLogin.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        } else {
                            throw new Error('Failed to sign message');
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Selected connector is undefined');
                    toast.error('Selected connector is undefined');
                    return { success: false, message: "Selected connector is undefined" };
                }
            }
        } catch (err: any) {
            console.error(err);
            const errorString = err.toString();
            if (errorString.includes('Error: Request rejected') || errorString.includes('User rejected the request.')) { } else {
                toast.error('Error connecting wallet, please try again!');
                return { success: false, message: "Error connecting wallet, please try again!" };
            }
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const seiSignup = async (walletName: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        try {
            const chainId = "pacific-1";
            const url = accountType == 'user' ? '/api/user-signup' : '/api/creator-signup';

            if (walletName.includes('Keplr')) {

                await window.keplr.enable(chainId);

                const offlineSigner = window.keplr.getOfflineSigner(chainId);

                const accounts = await offlineSigner.getAccounts();

                if (accounts) {
                    const address = accounts[0]?.address;

                    if (address) {
                        const message = await window.keplr.signArbitrary(chainId, address, walletSignupSignMessage) as unknown as { signature: any };

                        if (message?.signature) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'Sei'
                                })
                            });

                            const userSignup = await res.json();

                            if (userSignup.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userSignup.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userSignup.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'Sei',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }));

                                    toast.success('Your account has been created, sign up successful!');

                                    const campaignRedirect = Cookies.get('campaignRedirect');

                                    if (campaignRedirect && campaignRedirect == 'active') {
                                        router.push('/decentralized-advertisement-agency');
                                    } else if (!campaignRedirect || campaignRedirect != 'active') {
                                        router.push('/account/settings');
                                    }

                                    return { success: true, message: "User account created successfully." };
                                }

                            } else {
                                if (userSignup.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                                    toast.error(`${userSignup.message} Please try again.`);
                                } else if (userSignup.message.includes('User already exists')) {
                                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        } else {
                            throw new Error('Failed to sign message');
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Selected connector is undefined');
                    toast.error('Selected connector is undefined');
                    return { success: false, message: "Selected connector is undefined" };
                }
            } else if (walletName.includes('Fin')) {

                await window.fin.enable(chainId);

                const offlineSigner = window.fin.getOfflineSigner(chainId);

                const accounts = await offlineSigner.getAccounts();

                if (accounts) {
                    const address = accounts[0]?.address;

                    if (address) {
                        const message = await window.fin.signArbitrary(chainId, address, walletSignupSignMessage) as unknown as { signature: any };
                        if (message?.signature) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'Sei'
                                })
                            });

                            const userSignup = await res.json();

                            if (userSignup.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userSignup.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userSignup.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'Sei',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }));

                                    toast.success('Your account has been created, sign up successful!');

                                    const campaignRedirect = Cookies.get('campaignRedirect');

                                    if (campaignRedirect && campaignRedirect == 'active') {
                                        router.push('/decentralized-advertisement-agency');
                                    } else if (!campaignRedirect || campaignRedirect != 'active') {
                                        router.push('/account/settings');
                                    }

                                    return { success: true, message: "User account created successfully." };
                                }

                            } else {
                                if (userSignup.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                                    toast.error(`${userSignup.message} Please try again.`);
                                } else if (userSignup.message.includes('User already exists')) {
                                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        } else {
                            throw new Error('Failed to sign message');
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Selected connector is undefined');
                    toast.error('Selected connector is undefined');
                    return { success: false, message: "Selected connector is undefined" };
                }
            } else if (walletName.includes('Compass')) {

                await window.compass.enable(chainId);

                const offlineSigner = window.compass.getOfflineSigner(chainId);

                const accounts = await offlineSigner.getAccounts();

                if (accounts) {
                    const address = accounts[0]?.address;

                    if (address) {
                        const message = await window.compass.signArbitrary(chainId, address, walletSignupSignMessage) as unknown as { signature: any };
                        if (message?.signature) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'Sei'
                                })
                            });

                            const userSignup = await res.json();

                            if (userSignup.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userSignup.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userSignup.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'Sei',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }));

                                    toast.success('Your account has been created, sign up successful!');

                                    const campaignRedirect = Cookies.get('campaignRedirect');

                                    if (campaignRedirect && campaignRedirect == 'active') {
                                        router.push('/decentralized-advertisement-agency');
                                    } else if (!campaignRedirect || campaignRedirect != 'active') {
                                        router.push('/account/settings');
                                    }

                                    return { success: true, message: "User account created successfully." };
                                }

                            } else {
                                if (userSignup.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                                    toast.error(`${userSignup.message} Please try again.`);
                                } else if (userSignup.message.includes('User already exists')) {
                                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        } else {
                            throw new Error('Failed to sign message');
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Selected connector is undefined');
                    toast.error('Selected connector is undefined');
                    return { success: false, message: "Selected connector is undefined" };
                }
            } else if (walletName.includes('Leap')) {

                await window.leap.enable(chainId);

                const offlineSigner = window.leap.getOfflineSigner(chainId);

                const accounts = await offlineSigner.getAccounts();

                if (accounts) {
                    const address = accounts[0]?.address;

                    if (address) {
                        const message = await window.leap.signArbitrary(chainId, address, walletSignupSignMessage) as unknown as { signature: any };
                        if (message?.signature) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'Sei'
                                })
                            });

                            const userSignup = await res.json();

                            if (userSignup.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userSignup.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userSignup.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'Sei',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }));

                                    toast.success('Your account has been created, sign up successful!');

                                    const campaignRedirect = Cookies.get('campaignRedirect');

                                    if (campaignRedirect && campaignRedirect == 'active') {
                                        router.push('/decentralized-advertisement-agency');
                                    } else if (!campaignRedirect || campaignRedirect != 'active') {
                                        router.push('/account/settings');
                                    }
                                }

                                return { success: true, message: "Login successful" };
                            } else {
                                if (userSignup.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                                    toast.error(`${userSignup.message} Please try again.`);
                                } else if (userSignup.message.includes('User already exists')) {
                                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        } else {
                            throw new Error('Failed to sign message');
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Selected connector is undefined');
                    toast.error('Selected connector is undefined');
                    return { success: false, message: "Selected connector is undefined" };
                }
            }
        } catch (err: any) {
            console.error(err);
            const errorString = err.toString();
            if (errorString.includes('Error: Request rejected') || errorString.includes('User rejected the request.')) { } else {
                toast.error('Error connecting wallet, please try again!');
                return { success: false, message: "Error connecting wallet, please try again!" };
            }
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const evmLogin = async (walletName: string, blockchain: any, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        try {
            let selectedConnector: any;

            connectors.map(async (connector) => {
                if (connector.name == walletName) {
                    selectedConnector = connector;
                }
            });

            const chainId = chainIdMap[blockchain as keyof typeof chainIdMap];

            await switchChainAsync({
                chainId,
                connector: selectedConnector, // Ensure connector is set correctly
            });

            const isConnected = await selectedConnector?.connect() as any;

            if (isConnected) {
                const [address] = isConnected?.accounts;

                if (address) {
                    // Await the signature promise instead of just calling signMessage
                    const signature = await signMessageAsync({
                        account: address,
                        message: walletLoginSignMessage.substring(0, walletLoginSignMessage.length - 2),
                        connector: selectedConnector
                    });

                    if (!signature) {
                        toast.error('Message signing failed');
                        return { success: false, message: "Message signing failed" };
                    }

                    // Now call evmLoginMain with the recovered address
                    const result = await evmLoginMain(blockchain, accountType, address);

                    return result;
                } else {
                    console.error("No available addresses found in the wallet.");
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: "No available addresses found in the wallet." };
                }
            } else {
                console.error('Error connecting wallet. Please try again!');
                toast.error('Error connecting wallet. Please try again!');
                return { success: false, message: "Error connecting wallet. Please try again!" };
            }
        } catch (err: any) {
            console.error(err);
            toast.error('Unknown error, please refresh and try again!');
            return { success: false, message: "Unknown error, please refresh and try again!" };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const evmSignup = async (walletName: string, blockchain: any, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        try {
            let selectedConnector;
            connectors.forEach(connector => {
                if (connector.name === walletName) {
                    selectedConnector = connector;
                }
            });

            const chainId = chainIdMap[blockchain as keyof typeof chainIdMap];
            await switchChainAsync({
                chainId,
                connector: selectedConnector,
            });

            const isConnected = selectedConnector && await selectedConnector.connect() as { accounts: string[] };

            if (isConnected) {
                const address = Array.isArray(isConnected.accounts) ? isConnected.accounts[0] : undefined;

                if (address) {
                    // Await the signature promise instead of just calling signMessage
                    const signature = await signMessageAsync({
                        account: address,
                        message: walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2),
                        connector: selectedConnector
                    });

                    if (!signature) {
                        toast.error('Message signing failed');
                        return { success: false, message: "Message signing failed" };
                    }

                    // Now call evmSignupMain with the recovered address
                    const result = await evmSignupMain(blockchain, accountType, address);

                    return result;
                } else {
                    console.error("No available addresses found in the wallet.");
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: "No available addresses found in the wallet." };
                }
            } else {
                console.error('Error connecting wallet. Please try again!');
                toast.error('Error connecting wallet. Please try again!');
                return { success: false, message: "Error connecting wallet. Please try again!" };
            }
        } catch (err: any) {
            console.error(err);
            toast.error('Unknown error, please refresh and try again!');
            return { success: false, message: "Unknown error, please refresh and try again!" };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false,
            }));
        }
    };

    const evmLoginMain = async (blockchain: any, accountType: string, address: string) => {
        try {
            const headers = new Headers();
            headers.append('Content-Type', 'application/json');

            const url = accountType == 'user' ? '/api/user-login' : '/api/creator-login';

            const res = await fetch(url, {
                method: 'POST',
                headers,
                body: JSON.stringify({
                    address,
                    blockchain
                })
            });

            const userLogin = await res.json();

            if (userLogin.success) {
                const currentSession = await signIn('credentials', {
                    redirect: false,
                    address,
                    walletAddress: address,
                    userId: userLogin.user?.user_id,
                }) as any;

                if (currentSession.ok) {
                    dispatch(setWallet({
                        walletType,
                        userId: userLogin.user?.user_id,
                        walletAddress: address,
                        blockchain,
                        isConnected: true,
                        btcWalletProvider: null,
                        solWalletAddress: null,
                        solWalletProvider: null,
                        evmConnector: null,
                        solWalletType: null,
                        isSolConnected: false,
                        isSolConnecting: false,
                        accountType: userLogin.user?.user_info.accountType
                    }));

                    dispatch(setUserInfo({
                        username: userLogin.user.user_info.account.username,
                        displayName: userLogin.user.user_info.account.displayName,
                        profilePhoto: userLogin.user.user_info.profilePhoto,
                    }));

                    return { success: true, message: "Login successful" };
                }

            } else {
                if (userLogin.message.includes('User not found')) {
                    toast.error('No account found for this wallet. Sign up to get started.');
                } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                    toast.error(`${userLogin.message} Please try again.`);
                } else {
                    Swal.fire({
                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                        icon: "error",
                        confirmButtonText: "Close"
                    });
                }
                return { success: false, message: "Something went wrong. Please try again later." };
            }
        } catch (err: any) {
            console.error(err);
            toast.error('Error connecting wallet');
            return { success: false, message: "Error connecting wallet" };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const evmSignupMain = async (blockchain: any, accountType: string, address: string) => {
        try {
            const headers = new Headers();
            headers.append('Content-Type', 'application/json');

            const url = accountType == 'user' ? '/api/user-signup' : '/api/creator-signup';

            const res = await fetch(url, {
                method: 'POST',
                headers,
                body: JSON.stringify({
                    address,
                    blockchain
                })
            });

            const userSignup = await res.json();

            if (userSignup.success) {
                const currentSession = await signIn('credentials', {
                    redirect: false,
                    address: address,
                    walletAddress: address,
                    userId: userSignup.user?.user_id,
                }) as any;

                if (currentSession.ok) {
                    dispatch(setWallet({
                        walletType,
                        userId: userSignup.user?.user_id,
                        walletAddress: address,
                        blockchain,
                        isConnected: true,
                        btcWalletProvider: null,
                        solWalletAddress: null,
                        solWalletProvider: null,
                        evmConnector: null,
                        solWalletType: null,
                        isSolConnected: false,
                        isSolConnecting: false,
                        accountType: userSignup.user?.user_info.accountType
                    }));

                    dispatch(setUserInfo({
                        username: userSignup.user.user_info.account.username,
                        displayName: userSignup.user.user_info.account.displayName,
                        profilePhoto: userSignup.user.user_info.profilePhoto,
                    }));

                    toast.success('Your account has been created, sign up successful!');

                    const campaignRedirect = Cookies.get('campaignRedirect');

                    if (campaignRedirect && campaignRedirect == 'active') {
                        router.push('/decentralized-advertisement-agency');
                    } else if (!campaignRedirect || campaignRedirect != 'active') {
                        router.push('/account/settings');
                    }

                    return { success: true, message: "User account created successfully." };
                }

            } else {
                if (userSignup.message.includes('User not found')) {
                    toast.error('No account found for this wallet. Sign up to get started.');
                } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                    toast.error(`${userSignup.message} Please try again.`);
                } else if (userSignup.message.includes('User already exists')) {
                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                } else {
                    Swal.fire({
                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                        icon: "error",
                        confirmButtonText: "Close"
                    });
                }
                return { success: false, message: "Something went wrong. Please try again later." };
            }
        } catch (err: any) {
            console.error(err);
            toast.error('Error connecting wallet');
            return { success: false, message: "Error connecting wallet" };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const signMessageAsync = ({ account, message, connector }: { account: any, message: string, connector: any }): Promise<string> => {
        return new Promise((resolve, reject) => {
            try {
                signMessage({ account, message, connector });

                const interval = setInterval(async () => {
                    if (variablesRef.current?.message && signMessageDataRef.current) {
                        clearInterval(interval);
                        console.log({ signedMessage: signMessageDataRef.current, message });
                        const recoveredAddress = await recoverMessageAddress({
                            message,
                            signature: signMessageDataRef.current,
                        });

                        console.log({ recoveredAddress });

                        if (recoveredAddress) {
                            resolve(signMessageDataRef.current);
                        } else {
                            reject(new Error('Failed to verify signature'));
                        }
                    }
                }, 100);

                setTimeout(() => {
                    clearInterval(interval);
                    reject(new Error('Signing timed out'));
                }, 3 * 60 * 1000);
            } catch (error) {
                reject(error);
            }
        });
    };

    const xrpLogin = async (walletName: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        try {
            const url = accountType == 'user' ? '/api/user-login' : '/api/creator-login';

            if (walletName == 'Crossmark') {
                const { response } = await sdk.async.signInAndWait() as { response: any };

                if (response.data.meta.isSuccess) {
                    const address = response?.data?.address;

                    if (address) {
                        const message = sdk.sync.verify(stringToHex(walletLoginSignMessage.substring(0, walletLoginSignMessage.length - 2)));
                        if (message) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'XRP'
                                })
                            });

                            const userLogin = await res.json();

                            if (userLogin.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userLogin.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userLogin.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'XRP',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }));

                                    return { success: true, message: "Login successful" };
                                }

                            } else {
                                if (userLogin.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                                    toast.error(`${userLogin.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Error connecting wallet:', response.data.meta.message || 'Unknown error');
                    toast.error('Error connecting wallet. Please try again!');
                    return { success: false, message: "Error connecting wallet. Please try again!" };
                }
            } else if (walletName == 'Gem') {
                const access = await getAddress();

                if (access) {
                    const address = access?.result?.address;

                    if (address) {
                        const message = await signGemMessage(walletLoginSignMessage.substring(0, walletLoginSignMessage.length - 2));

                        if (message?.result?.signedMessage) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'XRP'
                                })
                            });

                            const userLogin = await res.json();

                            if (userLogin.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userLogin.user?.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userLogin.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'XRP',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userLogin.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userLogin.user.user_info.account.username,
                                        displayName: userLogin.user.user_info.account.displayName,
                                        profilePhoto: userLogin.user.user_info.profilePhoto,
                                    }));

                                    return { success: true, message: "Login successful" };
                                }

                            } else {
                                if (userLogin.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                                    toast.error(`${userLogin.message} Please try again.`);
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Error connecting wallet:', access);
                    toast.error('Error connecting wallet. Please try again!');
                    return { success: false, message: "Error connecting wallet. Please try again!" };
                }
            }
        } catch (err: any) {
            console.error(err);
            toast.error('Error connecting wallet. Please try again!');
            return { success: false, message: "Error connecting wallet. Please try again!" };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const xrpSignup = async (walletName: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        try {
            const url = accountType == 'user' ? '/api/user-signup' : '/api/creator-signup';

            if (walletName == 'Crossmark') {
                const { response } = await sdk.async.signInAndWait() as { response: any };

                if (response.data.meta.isSuccess) {
                    const address = response?.data?.address;

                    if (address) {
                        const message = sdk.sync.verify(stringToHex(walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2)));
                        if (message) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'XRP'
                                })
                            });

                            const userSignup = await res.json();

                            if (userSignup.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userSignup.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userSignup.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'XRP',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }));

                                    toast.success('Your account has been created, sign up successful!');

                                    const campaignRedirect = Cookies.get('campaignRedirect');

                                    if (campaignRedirect && campaignRedirect == 'active') {
                                        router.push('/decentralized-advertisement-agency');
                                    } else if (!campaignRedirect || campaignRedirect != 'active') {
                                        router.push('/account/settings');
                                    }

                                    return { success: true, message: "User account created successfully." };
                                }

                            } else {
                                if (userSignup.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                                    toast.error(`${userSignup.message} Please try again.`);
                                } else if (userSignup.message.includes('User already exists')) {
                                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }
                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Error connecting wallet:', response.data.meta.errorMessage || 'Unknown error');
                    toast.error('Error connecting wallet. Please try again!');
                    return { success: false, message: "Error connecting wallet. Please try again!" };
                }
            } else if (walletName == 'Gem') {
                const access = await getAddress();

                if (access) {
                    const address = access?.result?.address;

                    if (address) {
                        const message = await signGemMessage(walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2));

                        if (message?.result?.signedMessage) {
                            const headers = new Headers();
                            headers.append('Content-Type', 'application/json');

                            const res = await fetch(url, {
                                method: 'POST',
                                headers,
                                body: JSON.stringify({
                                    address,
                                    blockchain: 'XRP'
                                })
                            });

                            const userSignup = await res.json();

                            if (userSignup.success) {
                                const currentSession = await signIn('credentials', {
                                    redirect: false,
                                    address: address,
                                    walletAddress: address,
                                    userId: userSignup.user_id,
                                }) as any;

                                if (currentSession.ok) {
                                    dispatch(setWallet({
                                        walletType,
                                        userId: userSignup.user?.user_id,
                                        walletAddress: address,
                                        blockchain: 'XRP',
                                        isConnected: true,
                                        btcWalletProvider: null,
                                        solWalletAddress: null,
                                        solWalletProvider: null,
                                        evmConnector: null,
                                        solWalletType: null,
                                        isSolConnected: false,
                                        isSolConnecting: false,
                                        accountType: userSignup.user?.user_info.accountType
                                    }));

                                    dispatch(setUserInfo({
                                        username: userSignup.user.user_info.account.username,
                                        displayName: userSignup.user.user_info.account.displayName,
                                        profilePhoto: userSignup.user.user_info.profilePhoto,
                                    }));

                                    toast.success('Your account has been created, sign up successful!');

                                    const campaignRedirect = Cookies.get('campaignRedirect');

                                    if (campaignRedirect && campaignRedirect == 'active') {
                                        router.push('/decentralized-advertisement-agency');
                                    } else if (!campaignRedirect || campaignRedirect != 'active') {
                                        router.push('/account/settings');
                                    }

                                    return { success: true, message: "User account created successfully." };
                                }

                            } else {
                                if (userSignup.message.includes('User not found')) {
                                    toast.error('No account found for this wallet. Sign up to get started.');
                                } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                                    toast.error(`${userSignup.message} Please try again.`);
                                } else if (userSignup.message.includes('User already exists')) {
                                    toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                                } else {
                                    Swal.fire({
                                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                        icon: "error",
                                        confirmButtonText: "Close"
                                    });
                                }
                                return { success: false, message: "Something went wrong. Please try again later." };
                            }

                        }
                    } else {
                        console.error("No available addresses found in the wallet.");
                        toast.error('No available addresses found in the wallet.');
                        return { success: false, message: "No available addresses found in the wallet." };
                    }
                } else {
                    console.error('Error connecting wallet:', access);
                    toast.error('Error connecting wallet. Please try again!');
                    return { success: false, message: "Error connecting wallet. Please try again!" };
                }
            }
        } catch (err: any) {
            console.error(err);
            toast.error('Error connecting wallet. Please try again!');
            return { success: false, message: "Error connecting wallet. Please try again!" };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const polkadotLogin = async (walletName: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        if (!polkadotWalletProvider) return { success: false, error: 'Polkadot wallet provider not found' };

        const wallet: Wallet = polkadotWallets.find((w: any) => w.metadata.title === walletName) as any;

        if (!wallet) {
            console.error(`Wallet ${walletName} not found`);
            toast.error(`Wallet ${walletName} not found`);
            return { success: false, error: 'Wallet not found' };
        }

        try {
            await wallet.connect();
            const accounts = await wallet.getAccounts();

            if (accounts.length > 0) {

                const signRaw = wallet.signer?.signRaw;

                if (!!signRaw && accounts[0]?.address) {
                    const { signature } = await signRaw({
                        address: accounts[0].address,
                        data: walletLoginSignMessage.substring(0, walletLoginSignMessage.length - 2),
                        type: 'bytes',
                    });

                    if (signature) {
                        const address = accounts[0].address;

                        const headers = new Headers();
                        headers.append('Content-Type', 'application/json');

                        const url = accountType == 'user' ? '/api/user-login' : '/api/creator-login';

                        const res = await fetch(url, {
                            method: 'POST',
                            headers,
                            body: JSON.stringify({
                                address,
                                blockchain: 'Polkadot'
                            })
                        });

                        const userLogin = await res.json();

                        if (userLogin.success) {
                            const currentSession = await signIn('credentials', {
                                redirect: false,
                                address: address,
                                walletAddress: address,
                                userId: userLogin.user?.user_id,
                            }) as any;

                            if (currentSession.ok) {
                                dispatch(setWallet({
                                    walletType,
                                    userId: userLogin.user?.user_id,
                                    walletAddress: address,
                                    blockchain: 'Polkadot',
                                    isConnected: true,
                                    btcWalletProvider: null,
                                    solWalletAddress: null,
                                    solWalletProvider: null,
                                    evmConnector: null,
                                    solWalletType: null,
                                    isSolConnected: false,
                                    isSolConnecting: false,
                                    accountType: userLogin.user?.user_info.accountType
                                }));

                                dispatch(setUserInfo({
                                    username: userLogin.user.user_info.account.username,
                                    displayName: userLogin.user.user_info.account.displayName,
                                    profilePhoto: userLogin.user.user_info.profilePhoto,
                                }));

                                return { success: true, message: "Login successful" };
                            }

                        } else {
                            if (userLogin.message.includes('User not found')) {
                                toast.error('No account found for this wallet. Sign up to get started.');
                            } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                                toast.error(`${userLogin.message} Please try again.`);
                            } else {
                                Swal.fire({
                                    html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                    icon: "error",
                                    confirmButtonText: "Close"
                                });
                            }
                            return { success: false, message: "Something went wrong. Please try again later." };
                        }
                    }
                } else {
                    console.error("No available addresses found in the wallet.");
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, reason: 'no available addresses found in the wallet' };
                }

            } else {
                console.error('Selected connector is undefined');
                toast.error('Selected connector is undefined');
                return { success: false, reason: 'selected connector is undefined' };
            }
        } catch (error: any) {
            console.error(error);
            toast.error('Error connecting wallet. Please try again!');
            return { success: false, reason: 'error connecting wallet' };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const polkadotSignup = async (walletName: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        if (!polkadotWalletProvider) return { success: false, error: 'Polkadot wallet provider not found' };

        const wallet: Wallet = polkadotWallets.find((w: any) => w.metadata.title === walletName) as any;

        if (!wallet) {
            console.error(`Wallet ${walletName} not found`);
            toast.error(`Wallet ${walletName} not found`);
            return { success: false, error: 'Wallet not found' };
        }

        try {
            await wallet.connect();
            const accounts = await wallet.getAccounts();

            if (accounts.length > 0) {

                const signRaw = wallet.signer?.signRaw;

                if (!!signRaw && accounts[0]?.address) {
                    const { signature } = await signRaw({
                        address: accounts[0].address,
                        data: walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2),
                        type: 'bytes',
                    });

                    if (signature) {
                        const address = accounts[0].address;

                        const headers = new Headers();
                        headers.append('Content-Type', 'application/json');

                        const url = accountType == 'user' ? '/api/user-signup' : '/api/creator-signup';

                        const res = await fetch(url, {
                            method: 'POST',
                            headers,
                            body: JSON.stringify({
                                address,
                                blockchain: 'Polkadot'
                            })
                        });

                        const userSignup = await res.json();

                        if (userSignup.success) {
                            const currentSession = await signIn('credentials', {
                                redirect: false,
                                address: address,
                                walletAddress: address,
                                userId: userSignup.user?.user_id,
                            }) as any;

                            if (currentSession.ok) {
                                dispatch(setWallet({
                                    walletType,
                                    userId: userSignup.user?.user_id,
                                    walletAddress: address,
                                    blockchain: 'Polkadot',
                                    isConnected: true,
                                    btcWalletProvider: null,
                                    solWalletAddress: null,
                                    solWalletProvider: null,
                                    evmConnector: null,
                                    solWalletType: null,
                                    isSolConnected: false,
                                    isSolConnecting: false,
                                    accountType: userSignup.user?.user_info.accountType
                                }));

                                dispatch(setUserInfo({
                                    username: userSignup.user.user_info.account.username,
                                    displayName: userSignup.user.user_info.account.displayName,
                                    profilePhoto: userSignup.user.user_info.profilePhoto,
                                }));

                                toast.success('Your account has been created, sign up successful!');

                                const campaignRedirect = Cookies.get('campaignRedirect');

                                if (campaignRedirect && campaignRedirect == 'active') {
                                    router.push('/decentralized-advertisement-agency');
                                } else if (!campaignRedirect || campaignRedirect != 'active') {
                                    router.push('/account/settings');
                                }

                                return { success: true, message: "User account created successfully." };
                            }

                        } else {
                            if (userSignup.message.includes('User not found')) {
                                toast.error('No account found for this wallet. Sign up to get started.');
                            } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                                toast.error(`${userSignup.message} Please try again.`);
                            } else if (userSignup.message.includes('User already exists')) {
                                toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                            } else {
                                Swal.fire({
                                    html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                    icon: "error",
                                    confirmButtonText: "Close"
                                });
                            }
                            return { success: false, message: "Something went wrong. Please try again later." };
                        }
                    }
                } else {
                    console.error("No available addresses found in the wallet.");
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, reason: 'no available addresses found in the wallet' };
                }

            } else {
                console.error('Selected connector is undefined');
                toast.error('Selected connector is undefined');
                return { success: false, reason: 'selected connector is undefined' };
            }
        } catch (error: any) {
            console.error(error);
            toast.error('Error connecting wallet. Please try again!');
            return { success: false, reason: 'error connecting wallet' };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const algorandLogin = async (walletName: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true,
        }));

        try {

            const wallet = algoWallets.find(w => w.metadata.name === walletName);

            if (!wallet) {
                throw new Error(`Wallet with name ${walletName} not found.`);
            }

            // Connect the wallet
            const connected = await wallet.connect();

            if (connected) {
                const address = connected[0]?.address;

                if (address) {
                    const headers = new Headers();
                    headers.append('Content-Type', 'application/json');

                    const url = accountType == 'user' ? '/api/user-login' : '/api/creator-login';

                    const res = await fetch(url, {
                        method: 'POST',
                        headers,
                        body: JSON.stringify({
                            address,
                            blockchain: 'Algorand'
                        })
                    });

                    const userLogin = await res.json();

                    if (userLogin.success) {
                        const currentSession = await signIn('credentials', {
                            redirect: false,
                            address: address,
                            walletAddress: address,
                            userId: userLogin.user?.user_id,
                        }) as any;

                        if (currentSession.ok) {
                            dispatch(setWallet({
                                walletType,
                                userId: userLogin.user?.user_id,
                                walletAddress: address,
                                blockchain: 'Algorand',
                                isConnected: true,
                                btcWalletProvider: null,
                                solWalletAddress: null,
                                solWalletProvider: null,
                                evmConnector: null,
                                solWalletType: null,
                                isSolConnected: false,
                                isSolConnecting: false,
                                accountType: userLogin.user?.user_info.accountType
                            }));

                            dispatch(setUserInfo({
                                username: userLogin.user.user_info.account.username,
                                displayName: userLogin.user.user_info.account.displayName,
                                profilePhoto: userLogin.user.user_info.profilePhoto,
                            }));

                            return { success: true, message: "Login successful" };
                        }

                    } else {
                        if (userLogin.message.includes('User not found')) {
                            toast.error('No account found for this wallet. Sign up to get started.');
                        } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                            toast.error(`${userLogin.message} Please try again.`);
                        } else {
                            Swal.fire({
                                html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                icon: "error",
                                confirmButtonText: "Close"
                            });
                        }
                        return { success: false, message: "Something went wrong. Please try again later." };
                    }
                } else {
                    console.error("No available addresses found in the wallet.");
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: "No available addresses found in the wallet." };
                }
            } else {
                throw new Error(`Failed to connect to ${walletName} wallet`);
            }
        } catch (err: any) {
            console.error(err);
            const errorString = err.toString();
            if (errorString.includes('The user rejected the request through the wallet.')) { } else {
                toast.error('Error connecting wallet. Please try again!');
                return { success: false, message: "Error connecting wallet. Please try again!" };
            }
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false,
            }));
        }
    };

    const algorandSignup = async (walletName: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true,
        }));

        try {

            const wallet = algoWallets.find(w => w.metadata.name === walletName);

            if (!wallet) {
                throw new Error(`Wallet with name ${walletName} not found.`);
            }

            // Connect the wallet
            const connected = await wallet.connect();

            if (connected) {
                const address = connected[0]?.address;

                if (address) {
                    const headers = new Headers();
                    headers.append('Content-Type', 'application/json');

                    const url = accountType == 'user' ? '/api/user-signup' : '/api/creator-signup';

                    const res = await fetch(url, {
                        method: 'POST',
                        headers,
                        body: JSON.stringify({
                            address,
                            blockchain: 'Algorand'
                        })
                    });

                    const userSignup = await res.json();

                    if (userSignup.success) {
                        const currentSession = await signIn('credentials', {
                            redirect: false,
                            address: address,
                            walletAddress: address,
                            userId: userSignup.user?.user_id,
                        }) as any;

                        if (currentSession.ok) {
                            dispatch(setWallet({
                                walletType,
                                userId: userSignup.user?.user_id,
                                walletAddress: address,
                                blockchain: 'Algorand',
                                isConnected: true,
                                btcWalletProvider: null,
                                solWalletAddress: null,
                                solWalletProvider: null,
                                evmConnector: null,
                                solWalletType: null,
                                isSolConnected: false,
                                isSolConnecting: false,
                                accountType: userSignup.user?.user_info.accountType
                            }));

                            dispatch(setUserInfo({
                                username: userSignup.user.user_info.account.username,
                                displayName: userSignup.user.user_info.account.displayName,
                                profilePhoto: userSignup.user.user_info.profilePhoto,
                            }));

                            toast.success('Your account has been created, sign up successful!');

                            const campaignRedirect = Cookies.get('campaignRedirect');

                            if (campaignRedirect && campaignRedirect == 'active') {
                                router.push('/decentralized-advertisement-agency');
                            } else if (!campaignRedirect || campaignRedirect != 'active') {
                                router.push('/account/settings');
                            }

                            return { success: true, message: "User account created successfully." };
                        }

                    } else {
                        if (userSignup.message.includes('User not found')) {
                            toast.error('No account found for this wallet. Sign up to get started.');
                        } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                            toast.error(`${userSignup.message} Please try again.`);
                        } else if (userSignup.message.includes('User already exists')) {
                            toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                        } else {
                            Swal.fire({
                                html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                icon: "error",
                                confirmButtonText: "Close"
                            });
                        }
                        return { success: false, message: "Something went wrong. Please try again later." };
                    }
                } else {
                    console.error("No available addresses found in the wallet.");
                    toast.error('No available addresses found in the wallet.');
                    return { success: false, message: "No available addresses found in the wallet." };
                }
            } else {
                throw new Error(`Failed to connect to ${walletName} wallet`);
            }
        } catch (err: any) {
            console.error(err);
            const errorString = err.toString();
            if (errorString.includes('The user rejected the request through the wallet.')) { } else {
                toast.error('Error connecting wallet. Please try again!');
                return { success: false, message: "Error connecting wallet. Please try again!" };
            }
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false,
            }));
        }
    };

    const cardanoLogin = async (data: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        const walletType = data;

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        try {
            const userWallet = await BrowserWallet.enable(walletType);
            const changeAddress = await userWallet.getChangeAddress();
            const rewardAddresses = await userWallet.getRewardAddresses();
            const stakeKey = Array.isArray(rewardAddresses) ? rewardAddresses.find((addr) => addr !== "") : null;

            const usedAddress = changeAddress;

            const networkID = await userWallet.getNetworkId();

            if (networkID !== 1) {
                toast.error('Wrong network detected, make sure you are on Mainnet!');
                return;
            }

            if (usedAddress) {
                const nonce = await backendGetNonce(usedAddress, accountType, walletLoginSignMessage);
                const signature = await userWallet.signData(nonce, usedAddress) as any;

                if (signature?.key && signature?.signature) {
                    const headers = new Headers();
                    headers.append('Content-Type', 'application/json');

                    const url = accountType == 'user' ? '/api/user-login' : '/api/creator-login';

                    const res = await fetch(url, {
                        method: 'POST',
                        headers,
                        credentials: 'include',
                        body: JSON.stringify({
                            address: usedAddress,
                            blockchain: 'Cardano'
                        })
                    });

                    const userLogin = await res.json();

                    if (userLogin.success) {
                        const currentSession = await signIn('credentials', {
                            redirect: false,
                            address: stakeKey,
                            walletAddress: changeAddress,
                            userId: userLogin.user.user_id,
                        }) as any;

                        if (currentSession.ok) {
                            dispatch(setWallet({
                                walletType,
                                userId: userLogin.user.user_id,
                                walletAddress: usedAddress,
                                blockchain: 'Cardano',
                                stakeKey,
                                isConnected: true,
                                btcWalletProvider: null,
                                solWalletAddress: null,
                                solWalletProvider: null,
                                evmConnector: null,
                                solWalletType: null,
                                isSolConnected: false,
                                isSolConnecting: false,
                                accountType: userLogin.user.user_info.accountType
                            }));

                            dispatch(setUserInfo({
                                username: userLogin.user.user_info.account.username,
                                displayName: userLogin.user.user_info.account.displayName,
                                profilePhoto: userLogin.user.user_info.profilePhoto,
                            }));

                            return { success: true, message: "Login successful" };
                        }

                    } else {
                        if (userLogin.message.includes('User not found')) {
                            toast.error('No account found for this wallet. Sign up to get started.');
                        } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                            toast.error(`${userLogin.message} Please try again.`);
                        } else {
                            Swal.fire({
                                html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                icon: "error",
                                confirmButtonText: "Close"
                            });
                        }
                        return { success: false, message: "No account found for this wallet. Sign up to get started." };
                    }
                } else {
                    console.error("Wallet ownership verification failed.");
                    toast.error('Wallet ownership verification failed.');
                    return { success: false, message: "Wallet ownership verification failed." };
                }
            } else {
                console.error("No available addresses found in the wallet.");
                toast.error('No available addresses found in the wallet.');
                return { success: false, message: "No available addresses found in the wallet." };
            }
        } catch (error: any) {
            console.error(error);
            toast.error('Error connecting wallet: ' + error.message);
            return { success: false, message: "Error connecting wallet: " + error.message };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const cardanoSignup = async (data: string, accountType: string) => {
        if (isConnecting) {
            return { success: false, message: "Already connecting" };
        }

        const walletType = data;

        dispatch(setWallet({
            isConnecting: true,
            isSolConnecting: true
        }));

        try {
            const userWallet = await BrowserWallet.enable(walletType);
            const changeAddress = await userWallet.getChangeAddress();
            const rewardAddresses = await userWallet.getRewardAddresses();
            const stakeKey = Array.isArray(rewardAddresses) ? rewardAddresses.find((addr) => addr !== "") : null;

            const usedAddress = changeAddress;

            const networkID = await userWallet.getNetworkId();

            if (networkID !== 1) {
                toast.error('Wrong network detected, make sure you are on Mainnet!');
                return;
            }

            if (usedAddress) {
                const nonce = await backendGetNonce(usedAddress, accountType, walletSignupSignMessage);
                const signature = await userWallet.signData(nonce, usedAddress) as any;

                if (signature?.key && signature?.signature) {
                    const headers = new Headers();
                    headers.append('Content-Type', 'application/json');

                    const url = accountType == 'user' ? '/api/user-signup' : '/api/creator-signup';

                    const res = await fetch(url, {
                        method: 'POST',
                        headers,
                        credentials: 'include',
                        body: JSON.stringify({
                            address: usedAddress,
                            blockchain: 'Cardano',
                            accountType
                        })
                    });

                    const userSignup = await res.json();

                    if (userSignup.success) {
                        const currentSession = await signIn('credentials', {
                            redirect: false,
                            address: stakeKey,
                            walletAddress: changeAddress,
                            userId: userSignup.user_id,
                        }) as any;

                        if (currentSession.ok) {
                            dispatch(setWallet({
                                walletType,
                                userId: userSignup.user.user_id,
                                walletAddress: usedAddress,
                                blockchain: 'Cardano',
                                stakeKey,
                                isConnected: true,
                                btcWalletProvider: null,
                                solWalletAddress: null,
                                solWalletProvider: null,
                                evmConnector: null,
                                solWalletType: null,
                                isSolConnected: false,
                                isSolConnecting: false,
                                accountType: userSignup.user?.user_info.accountType
                            }));

                            dispatch(setUserInfo({
                                username: userSignup.user.user_info.account.username,
                                displayName: userSignup.user.user_info.account.displayName,
                                profilePhoto: userSignup.user.user_info.profilePhoto,
                            }));

                            toast.success('Your account has been created, sign up successful!');

                            const campaignRedirect = Cookies.get('campaignRedirect');

                            if (campaignRedirect && campaignRedirect == 'active') {
                                router.push('/decentralized-advertisement-agency');
                            } else if (!campaignRedirect || campaignRedirect != 'active') {
                                router.push('/account/settings');
                            }

                            return { success: true, message: "User account created successfully" };
                        }

                    } else {
                        if (userSignup.message.includes('User not found')) {
                            toast.error('No account found for this wallet. Sign up to get started.');
                        } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                            toast.error(`${userSignup.message} Please try again.`);
                        } else if (userSignup.message.includes('User already exists')) {
                            toast.error('This wallet is linked to another account. Please log in or use a different wallet.');
                        } else {
                            Swal.fire({
                                html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                                icon: "error",
                                confirmButtonText: "Close"
                            });
                        }
                        return { success: false, message: "Something went wrong. Please try again later." };
                    }
                } else {
                    console.error("Wallet ownership verification failed.");
                    toast.error('Wallet ownership verification failed.');
                    return { success: false, message: "Wallet ownership verification failed." };
                }
            } else {
                console.error("No available addresses found in the wallet.");
                toast.error('No available addresses found in the wallet.');
                return { success: false, message: "No available addresses found in the wallet." };
            }
        } catch (error: any) {
            console.error(error);
            toast.error('Error connecting wallet: ' + error.message);
            return { success: false, message: "Error connecting wallet: " + error.message };
        } finally {
            dispatch(setWallet({
                isConnecting: false,
                isSolConnecting: false
            }));
        }
    };

    const logOut = async (loginType: string = 'wallet') => {
        try {
            if (loginType == 'wallet') {
                switch (blockchain) {
                    case 'Cardano':
                        disconnect();
                        break;
                    case 'Vechain':
                        veDisconnect();
                        break;
                    case 'Polkadot':
                        const currentWallet = polkadotWallets.find(w => w.isConnected()) as any;
                        if (currentWallet) {
                            await currentWallet.disconnect();
                        }
                        break;
                    case 'Algorand':
                        activeWallet?.disconnect() as any;
                        break;
                    default:
                };

                signOut();
                if (userId) await updateUserStatus('offline', userId);
                dispatch(clearWallet());
                //Cookies.remove('wallet_verified');
                //Cookies.remove('campaignRedirect');
                dispatch(clearNotifications());
                dispatch(clearUserInfo());

                if (isMessagesActive || isProfileActive) {
                    router.push('/');
                }
            } else if (loginType == 'email') {
                signOut();
                if (userId) await updateUserStatus('offline', userId);
                dispatch(clearNotifications());
                dispatch(clearUserInfo());
            }

        } catch (error: any) {
            console.error('Error during disconnect:', error.message);
        }
    };

    const retrieveBalance = async (unitToFilter: string = 'lovelace', blockchain: string = 'Cardano') => {
        switch (blockchain) {
            case 'Cardano':
                if (walletType) {
                    const userWallet = await BrowserWallet.enable(walletType) as any;
                    const balance = await userWallet.getBalance() as any;

                    const filteredBalance = balance.filter((item: any) => item.unit === unitToFilter);

                    if (filteredBalance.length > 0) {
                        return Number(filteredBalance[0].quantity);
                    } else {
                        return 0;
                    }
                }
                console.error('Failed to retrieve balance, wallet not connected!');
                toast.error('Failed to retrieve balance, Cardano wallet not connected!');
                return 0;
            case 'Ethereum':
            case 'Polygon':
            case 'BNB Smart Chain':
            case 'Base':
            case 'Linea':
            case 'Blast':
            case 'Optimism':
            case 'Immutable':
            case 'Arbitrum':
            case 'Avalanche':
            case 'EOS':
                const balance = await getBalance(config, {
                    address: walletAddress,
                    blockTag: 'latest',
                    chainId: chainIdMap[blockchain]
                }) as any;
                return balance.formatted;
            case 'Vechain':
                if (account) {
                    connex.thor.account(account).get()
                        .then(({ balance }: any) => {
                            return balance === '0x0' ? 0 : Number(elFormatter(balance, 2))
                        });
                } else {
                    return 0;
                }
            case 'Bitcoin':
                const res1 = await request("getBalance", undefined);

                if (res1.status === "success") {
                    return convertSatoshisToBitcoin(res1.result.total) as number;
                }

                return 0;
            case 'XRP':

                return 0;
            case 'Sei':

                return 0;
            case 'Polkadot':

                return 0;
            case 'Algorand':
                if (!activeAccount) {
                    return 0;
                }
                return walletBalance;
            default:
                return 0;
        }
    };

    const verifyWalletOwnership = async (accountType: string, walletType: string, reason?: string) => {
        try {
            const wallet = await BrowserWallet.enable(walletType);
            const userAddress = (await wallet.getRewardAddresses())[0];
            const nonce = await backendGetNonce(userAddress, accountType, reason);
            const signature = await wallet.signData(nonce, userAddress) as any;
            const verificationResult = await backendVerifySignature(userAddress, signature, reason);

            if (verificationResult.success) {
                Cookies.set('wallet_verified', 'true', { expires: 1 / 24 }); // 1 hour
                return { success: true, reason: '', nonce: verificationResult.nonce };
            } else {
                toast.error('Wallet verification failed. Please try again.');
                return { success: false, reason: 'wallet verification failed' };
            }
        } catch (error: any) {
            const errorString = error.toString();
            const policyId = ['policy id', 'asset name'];

            console.error(errorString);

            if (errorString.includes('Wallet could not send the tx')) {
                toast.error('Error: ' + error.info);
            } else if (errorString.includes('Insufficient input in transaction') && !searchForSubstring(errorString, policyId, 'and')) {
                toast.error('Error: Insufficient funds!');
            } else if (searchForSubstring(errorString, ['policy id', 'asset name', 'Insufficient input in transaction.'], 'and')) {
                toast.error('Error: This asset is not in your wallet!');
            } else if (
                errorString.includes('Plutus inputs are present, but no collateral inputs are added.') ||
                errorString.includes("TypeError: Cannot read properties of undefined (reading 'getCollateral').")
            ) {
                toast.error('Error: You do not have any collateral set!');
            } else if (errorString.includes('User declined to sign the transaction')) {
                return { success: false, reason: 'failed to sign data' };
            } else if (errorString.includes('An error occurred during signTx: {"code":2,"info":"user declined sign tx"}.')) {
                return { success: false, reason: 'failed to sign data' };
            } else if (errorString.includes('wallet disconnects')) {
                return { success: false, reason: 'failed to sign data' };
            } else if (errorString.includes('user declined tx')) {
                return { success: false, reason: 'failed to sign data' };
            } else if (errorString.includes('Request rejected')) {
                return { success: false, reason: 'failed to sign data' };
            } else {
                toast.error('Unknown Error! Please refresh and try again.');
                return { success: false, reason: 'unknown error' };
            }
        }
    };

    const backendGetNonce = async (userAddress: string, accountType: string, reason: string = '') => {
        const response = await fetch('/api/get-nonce', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userAddress, reason, accountType }),
        });
        const data = await response.json();
        return data.nonce;
    };

    const backendVerifySignature = async (userAddress: string, signature: string, reason: string = '') => {
        const response = await fetch('/api/verify-signature', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userAddress, signature, reason }),
        });
        return response.json();
    };

    useEffect(() => {
        const provider = new InjectedWalletProvider(extensionConfiguration, APP_NAME) as any;
        setPolkadotWalletProvider(provider);
        provider.getWallets().then(setPolkadotWallets);
    }, []);

    useEffect(() => {
        if (isConnected && blockchain === 'Cardano' && window.cardano) {
            if (window.cardano.onAccountChange) {
                window.cardano.onAccountChange(() => handleDisconnect());
            }
            if (window.cardano.onNetworkChange) {
                window.cardano.onNetworkChange(() => handleDisconnect());
            }
        }
    }, [isConnected, blockchain, handleDisconnect]);

    useEffect(() => {
        if (!isAuthenticated) {
            handleDisconnect();
        }
    }, [isAuthenticated]);

    useEffect(() => {
        const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || '';
        console.log('Initializing socket connection to:', socketUrl || 'same origin');

        const socket = io(socketUrl, {
            path: '/socket.io',
            transports: ['websocket'],
            reconnection: true,
            reconnectionAttempts: Infinity,
            withCredentials: true,
        });

        const onAddWallet = async (data: { address: string }) => {
            if (data.address === walletAddress) {
                await handleDisconnect();
            }
        };

        const onRemoveWallet = async (data: { address: string }) => {
            if (data.address === walletAddress) {
                await handleDisconnect();
            }
        };

        socket.on('addWallet', onAddWallet);
        socket.on('removeWallet', onRemoveWallet);

        return () => {
            socket.off('addWallet', onAddWallet);
            socket.off('removeWallet', onRemoveWallet);
            socket.disconnect();
        };
    }, [walletAddress]);

    return (
        <WalletContext.Provider
            value={{
                cardanoLogin: async (data: string, accountType: string) => {
                    const result = await cardanoLogin(data, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                cardanoSignup: async (data: string, accountType: string) => {
                    const result = await cardanoSignup(data, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                bitcoinLogin: async (walletName: string, walletProvider: any, accountType: string) => {
                    const result = await bitcoinLogin(walletName, walletProvider, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                bitcoinSignup: async (walletName: string, walletProvider: any, accountType: string) => {
                    const result = await bitcoinSignup(walletName, walletProvider, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                algorandLogin: async (walletName: string, accountType: string) => {
                    const result = await algorandLogin(walletName, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                algorandSignup: async (walletName: string, accountType: string) => {
                    const result = await algorandSignup(walletName, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                xrpLogin: async (walletName: string, accountType: string) => {
                    const result = await xrpLogin(walletName, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                xrpSignup: async (walletName: string, accountType: string) => {
                    const result = await xrpSignup(walletName, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                seiLogin: async (walletName: string, accountType: string) => {
                    const result = await seiLogin(walletName, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                seiSignup: async (walletName: string, accountType: string) => {
                    const result = await seiSignup(walletName, accountType)
                    if (result) return result
                    return { success: false, message: 'Unknown error' }
                },
                polkadotLogin: async (walletName: string, accountType: string) => {
                    const result = await polkadotLogin(walletName, accountType);
                    if (result) return result;
                    return { success: false, message: 'Unknown error' };
                },
                polkadotSignup: async (walletName: string, accountType: string) => {
                    const result = await polkadotSignup(walletName, accountType);
                    if (result) return result;
                    return { success: false, message: 'Unknown error' };
                },
                evmLogin: async (walletName: string, blockchain: string, accountType: string) => {
                    const result = await evmLogin(walletName, blockchain, accountType);
                    if (result) return result;
                    return { success: false, message: 'Unknown error' };
                },
                evmSignup: async (walletName: string, blockchain: string, accountType: string) => {
                    const result = await evmSignup(walletName, blockchain, accountType);
                    if (result) return result;
                    return { success: false, message: 'Unknown error' };
                },
                logOut: handleDisconnect,
                getPhantomBitcoinProvider,
                connectors: Array.isArray(connectors) ? [...connectors] : [],
                activeAccount,
                torus,
                retrieveBalance,
                verifyWalletOwnership: async (accountType: string, walletType: string, reason?: string) => {
                    const result = await verifyWalletOwnership(accountType, walletType, reason);
                    if (result) return result;
                    return { success: false, reason: 'Unknown error' };
                },
                backendGetNonce,
                backendVerifySignature,
                signTransactions,
                request,
                algoWallets,
                polkadotWallets,
                polkadotWalletProvider,
                session,
                sessionStatus,
                syncSession,
                handleDisconnect,
            }}
        >
            {children}
        </WalletContext.Provider>
    );
};