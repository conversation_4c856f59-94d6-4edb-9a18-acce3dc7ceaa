import React, { createContext, useContext, useState } from 'react';

const ChatVisibilityContext = createContext();

export const useChatVisibility = () => {
  return useContext(ChatVisibilityContext);
};

export const ChatVisibilityProvider = ({ children }) => {
  const [visibleConversations, setVisibleConversations] = useState(new Set());

  const addVisibleConversation = (conversationId) => {
    setVisibleConversations(prev => new Set(prev).add(conversationId));
  };

  const removeVisibleConversation = (conversationId) => {
    setVisibleConversations(prev => {
      const newSet = new Set(prev);
      newSet.delete(conversationId);
      return newSet;
    });
  };

  return (
    <ChatVisibilityContext.Provider value={{ visibleConversations, addVisibleConversation, removeVisibleConversation }}>
      {children}
    </ChatVisibilityContext.Provider>
  );
};