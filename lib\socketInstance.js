const { initSocket } = require('./socket');

let io;
let isInitialized = false;

function initializeSocket(server) {
    console.log('Initializing Socket.IO instance...', isInitialized);
    if (!io || !isInitialized) {
        io = initSocket(server);
        isInitialized = true;
        console.log('Socket.IO initialized successfully:', isInitialized);
    }
    return io;
}

function getIO() {
    if (!io || !isInitialized) {
        console.error('Socket.IO instance not found. Current state:', isInitialized);
        throw new Error('Socket.io not initialized');
    }
    return io;
}

function isSocketInitialized() {
    return isInitialized && io !== null;
}

// Add a way to explicitly set the initialization state
function setSocketInitialized(state) {
    isInitialized = state;
}

module.exports = {
    initializeSocket,
    getIO,
    isSocketInitialized,
    setSocketInitialized
};