import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Define media types
const MediaType = v.union(v.literal("image"), v.literal("video"), v.literal("other"));

type MediaItem = {
    id: string;
    url: string;
    thumbnail_url: string | null;
    type: string;
    user_id: string;
    created_at: string;
    is_used: boolean;
    expires_at: string;
    thumbnail_key: string | null;
    metadata: any;
};

// TODO: Implement this function to delete from S3
async function deleteFromS3(key: string): Promise<void> {
    try {
        // Implementation will depend on your S3 setup
        // This is a placeholder for now
        console.log(`Deleting from S3: ${key}`);
    } catch (error: any) {
        console.error(`Failed to delete from S3: ${error}`);
        throw new Error(`Failed to delete file: ${error.message}`);
    }
}

export const cleanupUnusedMedia = mutation({
    args: {},
    handler: async (ctx) => {
        try {
            // 1. Find unused media that has expired
            const unusedMedia = await ctx.db
                .query("TemporaryMedia")
                .filter((q) => q.and(
                    q.lte(q.field("expires_at"), new Date().toISOString()),
                    q.eq(q.field("is_used"), false)
                ))
                .collect();

            let deletedCount = 0;

            for (const media of unusedMedia) {
                try {
                    // Validate media object
                    if (!media || typeof media !== 'object' || !media.id) {
                        throw new Error('Invalid media object format');
                    }

                    // 2. Delete from S3/Uploadthing
                    if (media.url) {
                        // Validate URL using regex
                        const urlRegex = /^(http|https):\/\/.+/;
                        if (!urlRegex.test(media.url)) {
                            throw new Error(`Invalid URL format: ${media.url}`);
                        }

                        // For images, delete using the URL (which is the file key)
                        if (media.type === 'image') {
                            await deleteFromS3(media.url);
                        } else if (media.type === 'video') {
                            // For videos, delete both main file and thumbnail
                            await deleteFromS3(media.url);
                            if (media.thumbnail_url) {
                                await deleteFromS3(media.thumbnail_url);
                            }
                        } else {
                            console.warn(`Unknown media type: ${media.type}`);
                        }
                    }

                    // 3. Delete from TemporaryMedia table
                    await ctx.db.delete(media._id);
                    console.log(`Successfully deleted unused media: ${media.id}`);
                    deletedCount++;
                } catch (err) {
                    console.error(`Failed to process media ${media.id}:`, err);
                    // Continue with next item even if one fails
                }
            }

            return {
                success: true,
                message: `Cleaned up ${deletedCount} out of ${unusedMedia.length} unused media items`,
                details: {
                    totalFound: unusedMedia.length,
                    successfullyDeleted: deletedCount,
                    failedToDelete: unusedMedia.length - deletedCount
                }
            };
        } catch (err) {
            console.error('Cleanup error:', err);
            throw new Error('Failed to clean up media');
        }
    }
});
