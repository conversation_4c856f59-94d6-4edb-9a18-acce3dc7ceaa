type GlobalSettings = {
    isHideSmallBalances: boolean;
    defaultBuySize: number;
    isExactTime: boolean;
    isAdvancedMode: boolean;
    isPriceChangeInverted: boolean;
    isAnimationsDisabled: boolean;
    isMyOrdersOnTrends: boolean;
    isPricesFlipped: boolean;
};
declare const useGlobalSettings: () => {
    settings: any;
    saveSettings: (newSettings: Partial<GlobalSettings>) => void;
};
export default useGlobalSettings;
