// api/uploadthing/core.ts
import { UploadThingError } from "uploadthing/server";
import { createUploadthing, type FileRouter } from "uploadthing/next";
import { fetchMutation } from "convex/nextjs";
import { api } from "@/convex/_generated/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";

const f = createUploadthing();

// Reusable function to get authenticated Convex token
async function getConvexToken(req: Request) {
  const token = await convexAuthNextjsToken(req);
  if (!token) {
    throw new UploadThingError("Unauthorized: No Convex auth token");
  }
  return token;
}

export const uploadRouter = {
  profileImage: f({ image: { maxFileSize: "2MB", maxFileCount: 1 } })
    .middleware(async ({ req }) => {
      const token = await getConvexToken(req);
      const accountType = req.headers.get("x-account-type") || "user";

      return { token, accountType };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      if (!metadata.token) {
        throw new UploadThingError("Missing auth token");
      }

      try {
        await fetchMutation(
          api.accounts.updateUserImage,
          {
            userId: metadata.userId,
            imageUrl: file.ufsUrl,
            imageType: "profile",
          },
          { token: metadata.token }
        );

        return {
          success: true,
          url: file.ufsUrl,
          message: "Profile image updated",
        };
      } catch (err) {
        console.error("Failed to update profile image:", err);
        throw new UploadThingError("Update failed");
      }
    }),

  coverImage: f({ image: { maxFileSize: "4MB", maxFileCount: 1 } })
    .middleware(async ({ req }) => {
      const token = await getConvexToken(req);
      const accountType = req.headers.get("x-account-type") || "user";

      return { token, accountType };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      if (!metadata.token) {
        throw new UploadThingError("Missing auth token");
      }

      try {
        await fetchMutation(
          api.accounts.updateUserImage,
          {
            userId: metadata.userId,
            imageUrl: file.ufsUrl,
            imageType: "cover",
          },
          { token: metadata.token }
        );

        return {
          success: true,
          url: file.ufsUrl,
          message: "Cover image updated",
        };
      } catch (err) {
        console.error("Failed to update cover image:", err);
        throw new UploadThingError("Update failed");
      }
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof uploadRouter;