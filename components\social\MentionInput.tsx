"use client";

import React, { useState, useRef, useEffect, FC } from 'react';
import { cn } from "@/lib/utils";
import type { Suggestion } from "./MixedTagsInput";
import MixedTagInput from './MixedTagsInput';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';
import { useUser } from '@/hooks/useUser';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

interface MentionInputProps {
    value: string;
    className?: string;
    onChange: (value: string) => void;
    onTagData?: (data: { hashtags: string[], mentions: any[] }) => void;
}

const MentionInput: FC<MentionInputProps> = ({
    value,
    className,
    onChange,
    onTagData,
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const { token: session } = useUser();

    // Function to fetch user mentions
    const fetchUserSuggestions: (query: string) => Promise<Suggestion[]> = async (query: string) => {
        setIsLoading(true);
        try {
            const { data, success } = await convex.query(api.users.searchUsers, {
                query,
                userId: session?.user?.userId || '',
                limit: 10
            });
            if (!success) throw new Error('Failed to fetch users');

            return data.map((user: any): Suggestion => ({
                id: user.id,
                label: user.display_name,
                value: user.username,
                display: user.display_name,
                username: '@' + user.username,
                avatar: user.profilePhoto,
                type: 'mention' as const
            }));
        } catch (error) {
            console.error('Error fetching users:', error);
            return [];
        } finally {
            setIsLoading(false);
        }
    };

    // Function to fetch hashtag suggestions using Convex
    const fetchHashtagSuggestions: (query: string) => Promise<Suggestion[]> = async (query: string) => {
        setIsLoading(true);
        try {
            const { data } = await convex.query(api.hashtags.searchHashtags, {
                query,
                userId: session?.user?.userId || '',
                limit: 10
            });

            return data.map((tag: any): Suggestion => ({
                id: tag.id,
                label: tag.name,
                value: tag.name,
                display: `#${tag.name} (${tag.post_count} posts)`,
                type: 'hashtag' as const
            }));
        } catch (error) {
            console.error('Error fetching hashtags:', error);
            return [];
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className={cn("relative w-full", className)}>
            <MixedTagInput
                value={value}
                onChange={onChange}
                onTagData={onTagData}
                fetchUserSuggestions={fetchUserSuggestions}
                fetchHashtagSuggestions={fetchHashtagSuggestions}
            />
            {isLoading && (
                <div className="absolute right-3 top-3">
                    <span className="loading loading-spinner loading-sm" />
                </div>
            )}
        </div>
    );
};
export default MentionInput;
