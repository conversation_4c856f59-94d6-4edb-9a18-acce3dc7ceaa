import { jsx as j } from "react/jsx-runtime";
import { useSwapSetup as l } from "./Main/useSwapSetup.js";
import b from "./Swap.js";
import "react";
import "../../store/useStore.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "./tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../hooks/useWalletConnect.js";
import "../../utils/cardanoUtils.js";
import "../../index-ca8eb9e1.js";
import "../../config/axios.js";
import "../../axios-ddd885c5.js";
import "../../constants/wallets.js";
import "../../useQuery-febd7967.js";
import "../../query-013b86c3.js";
import "../../QueryClientProvider-6bcd4331.js";
import "./Details/SwapDetails.js";
import "../../components/ui/skeleton.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../shallow-27fd7e97.js";
import "../../constants/dexes.js";
import "../../lib/utils.js";
import "../../react-hotkeys-hook.esm-60f1c3b8.js";
import "./Details/SwapDetailSplit.js";
import "./Details/OrderPreview.js";
import "../../components/ui/progress.js";
import "../../index-1c873780.js";
import "../../index-563d1ed8.js";
import "../../index-c8f2666b.js";
import "../../utils/formatNumber.js";
import "../../components/ui/tooltipDialog.js";
import "../../hooks/useScreen.js";
import "../../components/ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-c7156e07.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../components/ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../assets/svg/IconSquareInfo.js";
import "../../x-9e07c78a.js";
import "../../createLucideIcon-7a477fa6.js";
import "./Details/SwapDetailOutput.js";
import "../../components/common/TokenPrice.js";
import "../../utils/formatToken.js";
import "../../trends/components/PriceFormatter.js";
import "../../assets/svg/IconTwoWay.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconDown.js";
import "../../components/common/RealtimePulse.js";
import "../../components/ui/separator.js";
import "./SwapConfim.js";
import "../../components/ui/button.js";
import "../../index-1d6812f7.js";
import "../hooks/useSwapAction.js";
import "../../hooks/useNotify.js";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../hooks/useLimitAction.js";
import "../hooks/useStopLossAction.js";
import "../../assets/svg/IconSquaredSpinner.js";
import "../hooks/useDCAAction.js";
import "../hooks/useSwapEstimation.js";
import "./Settings/SwapSettings.js";
import "../../assets/svg/IconSetting.js";
import "../../components/ui/input.js";
import "../../components/ui/switchWithText.js";
import "../../assets/svg/IconRemove.js";
import "./Main/SwapHeader.js";
import "./Settings/Slippage.js";
import "../../assets/svg/IconRefresh.js";
import "./Main/SwapSelect.js";
import "./TokenSell.js";
import "../../assets/svg/IconChevronDown.js";
import "../../components/common/TokenImage.js";
import "../../hooks/useInputShortcuts.js";
import "../hooks/useUsdPrices.js";
import "../../index.esm-fb2f5862.js";
import "../../IconTilde-bf643edd.js";
import "../../createReactComponent-ec43b511.js";
import "../../assets/svg/IconArrowDown.js";
import "./TokenBuy.js";
import "./TokenLimit.js";
import "../../components/ui/slider.js";
import "../../index-1fe761a6.js";
import "../../index-6460524a.js";
import "../../index-bf605d8a.js";
import "./TokenDCA.js";
import "../../orders/components/Filters/DCAInterval.js";
import "../../components/ui/dropdown-menu.js";
import "../../assets/svg/IconCheck.js";
import "./Main/useSwapShortcuts.js";
import "../hooks/useLimitEstimation.js";
import "../hooks/useStopLossEstimation.js";
const yt = ({
  searchParams: r,
  defaultToken: t,
  orderTypesProps: o,
  orderTypes: p,
  supportedTokens: m,
  partnerName: i,
  partnerCode: e,
  onSwapSuccess: u,
  onSwapError: a,
  selectedWallet: f,
  inputs: s,
  onWalletConnect: S,
  onClickWalletConnect: n,
  onViewOrder: w,
  orderTypeOnButtonClick: x,
  defaultSettings: c,
  autoFocus: d
}) => (l({
  searchParams: r,
  defaultToken: t,
  orderTypesProps: o,
  orderTypes: p,
  supportedTokens: m,
  partnerName: i,
  partnerCode: e,
  onSwapSuccess: u,
  onSwapError: a,
  selectedWallet: f,
  inputs: s,
  onWalletConnect: S,
  onClickWalletConnect: n,
  onViewOrder: w,
  orderTypeOnButtonClick: x,
  defaultSettings: c,
  autoFocus: d
}), /* @__PURE__ */ j(b, {}));
export {
  yt as default
};
