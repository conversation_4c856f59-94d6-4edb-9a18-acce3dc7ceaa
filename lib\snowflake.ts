let lastTimestamp = -1;
let sequence = 0;

const EPOCH = 1609459200000; // Custom epoch (e.g., 2021-01-01)
const WORKER_ID_BITS = 5;
const DATACENTER_ID_BITS = 5;
const SEQUENCE_BITS = 12;

const WORKER_ID = 1; // Configure these based on your needs
const DATACENTER_ID = 1;

export function generateSnowflakeId(): string {
  let timestamp = Date.now();

  if (timestamp < lastTimestamp) {
    throw new Error('Clock moved backwards!');
  }

  if (lastTimestamp === timestamp) {
    sequence = (sequence + 1) & ((1 << SEQUENCE_BITS) - 1);
    if (sequence === 0) {
      // Sequence overflow, wait for next millisecond
      timestamp = waitNextMillis(lastTimestamp);
    }
  } else {
    sequence = 0;
  }

  lastTimestamp = timestamp;

  // Convert to BigInt to handle large numbers
  const timestampBigInt = BigInt(timestamp - EPOCH);
  const datacenterBigInt = BigInt(DATACENTER_ID);
  const workerBigInt = BigInt(WORKER_ID);
  const sequenceBigInt = BigInt(sequence);

  const shiftBits = BigInt(WORKER_ID_BITS + DATACENTER_ID_BITS + SEQUENCE_BITS);
  const workerShiftBits = BigInt(SEQUENCE_BITS);
  const datacenterShiftBits = BigInt(WORKER_ID_BITS + SEQUENCE_BITS);

  const id = (timestampBigInt << shiftBits) |
    (datacenterBigInt << datacenterShiftBits) |
    (workerBigInt << workerShiftBits) |
    sequenceBigInt;

  return id.toString();
}

function waitNextMillis(lastTimestamp: number): number {
  let timestamp = Date.now();
  while (timestamp <= lastTimestamp) {
    timestamp = Date.now();
  }
  return timestamp;
}
