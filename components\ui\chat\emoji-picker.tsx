"use client";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/chat/ui/popover";
import { HiOutlineFaceSmile } from "react-icons/hi2";
import Picker from "@emoji-mart/react";
import data from "@emoji-mart/data";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";

interface EmojiPickerProps {
  onChange: (value: string, event?: any) => void;
  className?: string;
}

export const EmojiPicker = ({ onChange, className }: EmojiPickerProps) => {
  const { theme } = useTheme();

  return (
    <Popover className="z-[999991]">
      <PopoverTrigger>
        <HiOutlineFaceSmile className={cn("h-5 w-5", className)} />
      </PopoverTrigger>
      <PopoverContent
        className="w-full !z-[9999]"
        side="top"
        sideOffset={10}
      >
        <Picker
          emojiSize={18}
          theme={theme || "light"}
          data={data}
          maxFrequentRows={1}
          onEmojiSelect={(emoji: any) => onChange(emoji.native, emoji)}
        />
      </PopoverContent>
    </Popover>
  );
};