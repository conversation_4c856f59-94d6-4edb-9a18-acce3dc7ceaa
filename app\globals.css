@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {

  :root,
  :root[data-theme='light'] {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --text-color: #18181b;
    --background-color: #fff;

    --hot-pink: #e4568a;
    --turquoise: #5ec1ce;
    --turquoise-hover: #4ba9b6;
    --blue-gray: #5d6b71;
    --whisper-white: #fcfcfc;

    --results-gray: rgb(200 205 213);
    --gorilla-gray: rgb(129, 129, 129);

    --sb-track-color: #151515;
    --sb-thumb-color: #ffffff;
    --sb-size: 10px;

    --hot-pink-rgb: 222, 74, 138;
    --hot-pink-rgba: 222, 74, 138, 1;
    --turquoise-rgb: 94, 193, 206;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  :root.dark,
  :root[data-theme='dark'] {
    --background: 240 5.9% 10%;
    --foreground: 210 40% 98%;
    --text-color: #fff;
    --background-color: #18181b;

    --hot-pink: #e4568a;
    --turquoise: #5ec1ce;
    --turquoise-hover: #4ba9b6;
    --blue-gray: #5d6b71;
    --whisper-white: #1f1f23;

    --results-gray: rgb(129 129 129);
    --gorilla-gray: rgb(200 205 213);

    --sb-track-color: #fff;
    --sb-thumb-color: #151515;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  body {
    font-family: Poppins, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  }

  /* width */
  ::-webkit-scrollbar {
    width: 10px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #888;
    border-radius: 5px;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #000;
    border-radius: 5px;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Track */
  .dark ::-webkit-scrollbar-track {
    background: #404040;
    /* dark:bg-neutral-700 */
    border-radius: 9999px;
    /* rounded-full */
  }

  /* Handle */
  .dark ::-webkit-scrollbar-thumb {
    background: #fafafa;
    /* dark:bg-neutral-50 */
    border-radius: 9999px;
    /* rounded-full */
  }

  /* Handle on hover */
  .dark ::-webkit-scrollbar-thumb:hover {
    background: #e5e5e5;
    /* slightly lighter than neutral-50 for hover effect */
  }

  /* Light mode scrollbar styles */
  ::-webkit-scrollbar {
    width: 0.5rem;
    /* w-2 */
  }

  ::-webkit-scrollbar-track {
    background: #f3f4f6;
    /* bg-gray-100 */
    border-radius: 9999px;
    /* rounded-full */
  }

  ::-webkit-scrollbar-thumb {
    background: #d1d5db;
    /* bg-gray-300 */
    border-radius: 9999px;
    /* rounded-full */
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
    /* slightly darker than gray-300 for hover effect */
  }

  .border-border {
    border-bottom: 1px solid var(--border);
  }

  .border-hot-pink {
    border-color: var(--hot-pink);
  }

  .dark-shadow {
    text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.5);
  }

  .light-shadow {
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
  }

  .text-turquoise {
    color: var(--turquoise);
  }

  .text-blue-gray {
    color: var(--blue-gray);
  }

  .text-gorilla-gray {
    color: var(--gorilla-gray);
  }

  .text-hot-pink {
    color: var(--hot-pink);
  }

  .text-results-gray {
    color: var(--results-gray);
  }

  .text-whisper-white {
    color: var(--whisper-white);
  }

  .bg-whisper-white {
    background-color: var(--whisper-white);
  }

  .bg-hot-pink {
    background-color: var(--hot-pink);
  }

  .bg-turquoise {
    background-color: var(--turquoise);
  }

  .bg-gorilla-gray {
    background-color: var(--gorilla-gray);
  }

  .fill-gorilla-gray {
    fill: var(--gorilla-gray);
  }

  .border-gorilla-gray {
    border-color: var(--gorilla-gray);
  }

  .bg-results-gray {
    background-color: var(--results-gray);
  }

  .bg-muted {
    background-color: hsl(var(--muted));
  }

  .text-muted-foreground {
    color: hsl(var(--muted-foreground));
  }

  html .bg-popover {
    background-color: #fff;
  }

  html.dark .bg-popover {
    background-color: #18181b;
  }

  [type="submit"] {
    background-color: inherit;
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: 0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: -0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: 0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: -0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .data-\[state\=checked\]\:translate-x-5[data-state="checked"] {
    --tw-translate-x: 1.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: hsl(var(--background));
  }

  .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: hsl(var(--primary));
  }

  .data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
    background-color: hsl(var(--input));
  }

  .data-\[state\=active\]\:text-foreground[data-state="active"] {
    color: hsl(var(--foreground));
  }

  .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: hsl(var(--primary-foreground));
  }

  .dark .data-\[selected\=\'true\'\]\:bg-accent[data-selected="true"] {
    background-color: transparent;
    color: #fff;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: 0.5;
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:bg-\[radial-gradient\(\#ffffff22_1px\2c transparent_1px\)\]:is(.dark *) {
    background-image: radial-gradient(#ffffff22 1px, transparent 1px);
  }

  .tagify__dropdown {
    @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg;
  }

  .tagify__dropdown__item {
    @apply flex items-center gap-3 px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700;
  }

  .tagify__dropdown__item .avatar {
    @apply w-6 h-6 rounded-full;
  }

  .mention-tag {
    @apply bg-turquoise dark:bg-turquoise text-white rounded px-1;
  }

  .hashtag-tag {
    @apply bg-turquoise dark:bg-turquoise text-white rounded px-1;
  }

}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
}

/* Add this to your globals.css file */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
