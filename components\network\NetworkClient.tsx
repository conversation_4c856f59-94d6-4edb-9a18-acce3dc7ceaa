'use client';

import { useState, useEffect, useCallback } from 'react';
import { LayoutGrid, LayoutList, MapPin, Users as UsersIcon } from 'lucide-react';
import { Virtuoso } from 'react-virtuoso';
import NetworkProfileCard from '@/components/network/NetworkProfileCard';
import NetworkProfileSkeleton from '@/components/network/NetworkProfileSkeleton';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import 'tippy.js/dist/svg-arrow.css';
import { toast } from 'react-toastify';
import { NetworkProfile } from '@/types/network';
import { FaRotate } from 'react-icons/fa6';
import { IoMdMale, IoMdFemale, IoMdTransgender } from 'react-icons/io';
import MapContainer from './Map';
import {
  countryOptions,
  nicheOptions,
  ageOptions,
  ethnicityOptions,
  hairColourOptions,
  eyeColourOptions,
  bodyTypeOptions,
  breastTypeOptions,
  breastSizeOptions,
  languageOptions,
  heightOptions,
  piercingOptions,
  tattooOptions,
  waistOptions,
  hipsOptions,
  buttOptions,
  shoeSizeOptions,
} from './filter-options';
import { Option } from '@/components/ui/multiple-select';
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SelectDropdown } from '@/components/ui/select-dropdown';
import SearchableDropdown from '@/components/network/SearchableDropdown';
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

interface NetworkClientProps {
  initialProfiles: NetworkProfile[];
  totalProfiles: number;
}

interface FilterOption {
  id: string;
  label: string;
  fixedPlaceholder?: string;
  value: string;
  options?: { value: string; label: string }[] | { [key: string]: { value: string; label: string }[] };
  onChange: (value: string) => void;
  component?: any;
  condition?: (state: any) => boolean;
}

const genderOptions = [
  {
    value: 'all',
    label: 'All',
  },
  {
    value: 'female',
    label: 'Female',
    icon: <IoMdFemale className="w-4 h-4" />
  },
  {
    value: 'male',
    label: 'Male',
    icon: <IoMdMale className="w-4 h-4" />
  },
  {
    value: 'trans',
    label: 'Trans',
    icon: <IoMdTransgender className="w-5 h-5" />
  },
  {
    value: 'couples',
    label: 'Couples',
    icon: <UsersIcon className="w-4 h-4" />
  },
];

const NetworkClient = ({ initialProfiles, totalProfiles }: NetworkClientProps) => {
  const [viewMode, setViewMode] = useState<'grid' | 'map' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('most-followed');
  const [isLoading, setIsLoading] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [profiles, setProfiles] = useState<NetworkProfile[]>(initialProfiles);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(totalProfiles > initialProfiles.length);
  const [totalUnpaginatedResults, setTotalUnpaginatedResults] = useState(totalProfiles);

  const [selectedCountry, setSelectedCountry] = useState('all');
  const [selectedGender, setSelectedGender] = useState('all');
  const [selectedNiche, setSelectedNiche] = useState('all');
  const [selectedEthnicity, setSelectedEthnicity] = useState('all');
  const [selectedAge, setSelectedAge] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('all');
  const [selectedHeight, setSelectedHeight] = useState('all');
  const [selectedHairColor, setSelectedHairColor] = useState('all');
  const [selectedEyeColor, setSelectedEyeColor] = useState('all');
  const [selectedWaist, setSelectedWaist] = useState('all');
  const [selectedHips, setSelectedHips] = useState('all');
  const [selectedButt, setSelectedButt] = useState('all');
  const [selectedShoeSize, setSelectedShoeSize] = useState('all');
  const [selectedBodyType, setSelectedBodyType] = useState('all');
  const [selectedPiercings, setSelectedPiercings] = useState<Option[]>([]);
  const [selectedTattoos, setSelectedTattoos] = useState<Option[]>([]);
  const [selectedBreastType, setSelectedBreastType] = useState('all');
  const [selectedBreastSize, setSelectedBreastSize] = useState('all');
  const [selectedChestSize, setSelectedChestSize] = useState('all');
  const [resetTriggered, setResetTriggered] = useState(false);

  const filterOptions = [
    { value: 'most-liked', label: 'Most Liked' },
    { value: 'most-viewed', label: 'Most Viewed' },
    { value: 'most-followed', label: 'Most Followed' },
    { value: 'highest-rated', label: 'Highest Rated' },
    { value: 'currently-active', label: 'Currently Active' },
    { value: 'recently-joined', label: 'Recently Joined' },
  ];

  const filters: FilterOption[] = [
    {
      id: 'country',
      label: 'Country',
      value: selectedCountry,
      options: countryOptions,
      onChange: (value: string | Option[]) => {
        if (typeof value === 'string') {
          setSelectedCountry(value);
        } else if (Array.isArray(value)) {
          setSelectedCountry(value.length > 0 ? value[0].value : 'all');
        }
      },
      component: (
        <div className="mb-4">
          <SearchableDropdown
            value={selectedCountry}
            onChange={(value: string) => setSelectedCountry(value)}
            options={countryOptions}
            label="Country"
            fixedPlaceholder="Country: "
          />
        </div>
      ),
    },
    {
      id: 'gender',
      label: 'Gender',
      value: selectedGender,
      fixedPlaceholder: 'Gender: ',
      options: genderOptions,
      onChange: (value: string | Option[]) => {
        if (typeof value === 'string') {
          setSelectedGender(value);
        } else if (Array.isArray(value)) {
          setSelectedGender(value.length > 0 ? value[0].value : 'all');
        }
      },
      component: (
        <div className="mb-4">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild className="w-full">
              <button
                className="flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white focus-visible:!border focus-visible:!border-solid focus-visible:!border-gray-400 dark:focus-visible:!border-white"
              >
                <span>
                  {`Gender: ${genderOptions.find(opt => opt.value === selectedGender)?.label || 'Gender'}`}
                </span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[211px] left-0 border-gray-400 dark:border-white max-h-52 overflow-auto">
              {genderOptions.map((option, index) => (
                <>
                  <DropdownMenuItem
                    key={option.value}
                    onSelect={() => setSelectedGender(option.value)}
                    className={cn(
                      selectedGender === option.value && 'bg-transparent hover:bg-transparent text-gray-600 dark:text-white',
                      'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                    )}
                  >
                    <div className="flex items-center gap-1">
                      {option.label}
                      {option.icon}
                    </div>
                  </DropdownMenuItem>
                  {index < genderOptions.length - 1 && <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />}
                </>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
    {
      id: 'ethnicity',
      label: 'Ethnicity',
      value: selectedEthnicity,
      options: ethnicityOptions,
      onChange: (value: string | Option[]) => {
        if (typeof value === 'string') {
          setSelectedEthnicity(value);
        } else if (Array.isArray(value)) {
          setSelectedEthnicity(value.length > 0 ? value[0].value : 'all');
        }
      },
      component: (
        <div className="mb-4">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild className="w-full">
              <button
                className="flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white focus-visible:!border focus-visible:!border-solid focus-visible:!border-gray-400 dark:focus-visible:!border-white"
              >
                <span>
                  {`Ethnicity: ${ethnicityOptions.find(opt => opt.value === selectedEthnicity)?.label || 'Ethnicity'}`}
                </span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[211px] left-0 border-gray-400 dark:border-white max-h-52 overflow-auto">
              {ethnicityOptions.map((option, index) => (
                <>
                  <DropdownMenuItem
                    key={option.value}
                    onSelect={() => setSelectedEthnicity(option.value)}
                    className={cn(
                      selectedEthnicity === option.value && 'bg-transparent hover:bg-transparent text-gray-600 dark:text-white',
                      'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                    )}
                  >
                    {option.label}
                  </DropdownMenuItem>
                  {index < ethnicityOptions.length - 1 && <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />}
                </>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
    {
      id: 'hairColor',
      label: 'Hair Color',
      value: selectedHairColor,
      options: hairColourOptions,
      onChange: (value: string | Option[]) => {
        if (typeof value === 'string') {
          setSelectedHairColor(value);
        } else if (Array.isArray(value)) {
          setSelectedHairColor(value.length > 0 ? value[0].value : 'all');
        }
      },
      component: (
        <div className="mb-4">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild className="w-full">
              <button
                className="flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white focus-visible:!border focus-visible:!border-solid focus-visible:!border-gray-400 dark:focus-visible:!border-white"
              >
                <span>
                  {`Hair Color: ${hairColourOptions.find(opt => opt.value === selectedHairColor)?.label || 'Hair Color'}`}
                </span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[211px] left-0 border-gray-400 dark:border-white max-h-52 overflow-auto">
              {hairColourOptions.map((option, index) => (
                <>
                  <DropdownMenuItem
                    key={option.value}
                    onSelect={() => setSelectedHairColor(option.value)}
                    className={cn(
                      selectedHairColor === option.value && 'bg-transparent hover:bg-transparent text-gray-600 dark:text-white',
                      'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                    )}
                  >
                    {option.label}
                  </DropdownMenuItem>
                  {index < hairColourOptions.length - 1 && <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />}
                </>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
    {
      id: 'eyeColor',
      label: 'Eye Color',
      value: selectedEyeColor,
      options: eyeColourOptions,
      onChange: (value: string | Option[]) => {
        if (typeof value === 'string') {
          setSelectedEyeColor(value);
        } else if (Array.isArray(value)) {
          setSelectedEyeColor(value.length > 0 ? value[0].value : 'all');
        }
      },
      component: (
        <div className="mb-4">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild className="w-full">
              <button
                className="flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white focus-visible:!border focus-visible:!border-solid focus-visible:!border-gray-400 dark:focus-visible:!border-white"
              >
                <span>
                  {`Eye Color: ${eyeColourOptions.find(opt => opt.value === selectedEyeColor)?.label || 'Eye Color'}`}
                </span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[211px] left-0 border-gray-400 dark:border-white max-h-52 overflow-auto">
              {eyeColourOptions.map((option, index) => (
                <>
                  <DropdownMenuItem
                    key={option.value}
                    onSelect={() => setSelectedEyeColor(option.value)}
                    className={cn(
                      selectedEyeColor === option.value && 'bg-transparent hover:bg-transparent text-gray-600 dark:text-white',
                      'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                    )}
                  >
                    {option.label}
                  </DropdownMenuItem>
                  {index < eyeColourOptions.length - 1 && <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />}
                </>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
    {
      id: 'bodyType',
      label: 'Body Type',
      value: selectedBodyType,
      options: bodyTypeOptions,
      onChange: (value: string | Option[]) => {
        if (typeof value === 'string') {
          setSelectedBodyType(value);
        } else if (Array.isArray(value)) {
          setSelectedBodyType(value.length > 0 ? value[0].value : 'all');
        }
      },
      component: (
        <div className="mb-4">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild className="w-full">
              <button
                className="flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white focus-visible:!border focus-visible:!border-solid focus-visible:!border-gray-400 dark:focus-visible:!border-white"
              >
                <span>
                  {`Body Type: ${bodyTypeOptions.find(opt => opt.value === selectedBodyType)?.label || 'Body Type'}`}
                </span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[211px] left-0 border-gray-400 dark:border-white max-h-52 overflow-auto">
              {bodyTypeOptions.map((option, index) => (
                <>
                  <DropdownMenuItem
                    key={option.value}
                    onSelect={() => setSelectedBodyType(option.value)}
                    className={cn(
                      selectedBodyType === option.value && 'bg-transparent hover:bg-transparent text-gray-600 dark:text-white',
                      'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                    )}
                  >
                    {option.label}
                  </DropdownMenuItem>
                  {index < bodyTypeOptions.length - 1 && <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />}
                </>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
      condition: (state) => state.selectedGender.toLowerCase() === 'male' || state.selectedGender.toLowerCase() === 'female' || state.selectedGender.toLowerCase() === 'trans',
    },
    {
      id: 'breastSize',
      label: 'Breast Size',
      value: selectedBreastSize,
      options: breastSizeOptions,
      onChange: (value: string | Option[]) => {
        if (typeof value === 'string') {
          setSelectedBreastSize(value);
        } else if (Array.isArray(value)) {
          setSelectedBreastSize(value.length > 0 ? value[0].value : 'all');
        }
      },
      condition: (state) => state.selectedGender.toLowerCase() === 'female',
      component: (
        <div className="mb-4">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild className="w-full">
              <button
                className="flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white focus-visible:!border focus-visible:!border-solid focus-visible:!border-gray-400 dark:focus-visible:!border-white"
              >
                <span>
                  {`Breast Size: ${breastSizeOptions.find(opt => opt.value === selectedBreastSize)?.label || 'Breast Size'}`}
                </span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[211px] left-0 border-gray-400 dark:border-white max-h-52 overflow-auto">
              {breastSizeOptions.map((option, index) => (
                <>
                  <DropdownMenuItem
                    key={option.value}
                    onSelect={() => setSelectedBreastSize(option.value)}
                    className={cn(
                      selectedBreastSize === option.value && 'bg-transparent hover:bg-transparent text-gray-600 dark:text-white',
                      'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                    )}
                  >
                    {option.label}
                  </DropdownMenuItem>
                  {index < breastSizeOptions.length - 1 && <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />}
                </>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
    {
      id: 'breastType',
      label: 'Breast Type',
      value: selectedBreastType,
      options: breastTypeOptions,
      onChange: (value: string | Option[]) => {
        if (typeof value === 'string') {
          setSelectedBreastType(value);
        } else if (Array.isArray(value)) {
          setSelectedBreastType(value.length > 0 ? value[0].value : 'all');
        }
      },
      condition: (state) => state.selectedGender.toLowerCase() === 'female',
      component: (
        <div className="mb-4">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild className="w-full">
              <button
                className="flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white focus-visible:!border focus-visible:!border-solid focus-visible:!border-gray-400 dark:focus-visible:!border-white"
              >
                <span>
                  {`Breast Type: ${breastTypeOptions.find(opt => opt.value === selectedBreastType)?.label || 'Breast Type'}`}
                </span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[211px] left-0 border-gray-400 dark:border-white max-h-52 overflow-auto">
              {breastTypeOptions.map((option, index) => (
                <>
                  <DropdownMenuItem
                    key={option.value}
                    onSelect={() => setSelectedBreastType(option.value)}
                    className={cn(
                      selectedBreastType === option.value && 'bg-transparent hover:bg-transparent text-gray-600 dark:text-white',
                      'w-full text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent focus:text-gray-600'
                    )}
                  >
                    {option.label}
                  </DropdownMenuItem>
                  {index < breastTypeOptions.length - 1 && <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />}
                </>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  // Check if any filter is non-default
  const hasActiveFilters = () => {
    return (
      selectedCountry !== 'all' ||
      selectedGender !== 'all' ||
      selectedNiche !== 'all' ||
      selectedEthnicity !== 'all' ||
      selectedAge !== 'all' ||
      selectedLanguage !== 'all' ||
      selectedHeight !== 'all' ||
      selectedHairColor !== 'all' ||
      selectedEyeColor !== 'all' ||
      selectedWaist !== 'all' ||
      selectedHips !== 'all' ||
      selectedButt !== 'all' ||
      selectedShoeSize !== 'all' ||
      selectedBodyType !== 'all' ||
      selectedPiercings.length > 0 ||
      selectedTattoos.length > 0 ||
      selectedBreastType !== 'all' ||
      selectedBreastSize !== 'all' ||
      selectedChestSize !== 'all'
    );
  };

  const convexProfiles = useQuery(api.users.fetchNetworkCreators, {
    page,
    limit: 52,
          sortBy,
    country: selectedCountry !== "all" ? selectedCountry : undefined,
    gender: selectedGender !== "all" ? selectedGender : undefined,
    niche: selectedNiche !== "all" ? selectedNiche : undefined,
    ethnicity: selectedEthnicity !== "all" ? selectedEthnicity : undefined,
    age: selectedAge !== "all" ? selectedAge : undefined,
    language: selectedLanguage !== "all" ? selectedLanguage : undefined,
    height: selectedHeight !== "all" ? selectedHeight : undefined,
    hairColor: selectedHairColor !== "all" ? selectedHairColor : undefined,
    eyeColor: selectedEyeColor !== "all" ? selectedEyeColor : undefined,
    waist: selectedWaist !== "all" ? selectedWaist : undefined,
    hips: selectedHips !== "all" ? selectedHips : undefined,
    butt: selectedButt !== "all" ? selectedButt : undefined,
    shoeSize: selectedShoeSize !== "all" ? selectedShoeSize : undefined,
    bodyType: selectedBodyType !== "all" ? selectedBodyType : undefined,
    breastType: selectedBreastType !== "all" ? selectedBreastType : undefined,
    breastSize: selectedBreastSize !== "all" ? selectedBreastSize : undefined,
    piercings: selectedPiercings.length > 0 ? selectedPiercings.map(p => p.value) : undefined,
    tattoos: selectedTattoos.length > 0 ? selectedTattoos.map(t => t.value) : undefined,
  });

  useEffect(() => {
    if (convexProfiles) {
      setProfiles(convexProfiles.creators);
      setTotalUnpaginatedResults(convexProfiles.totalUnpaginated);
      setHasMore(convexProfiles.creators.length === 52 && convexProfiles.creators.length + profiles.length < convexProfiles.totalUnpaginated);
      }
  }, [convexProfiles]);

  const handleSortChange = (value: string) => {
    setSortBy(value);
    setPage(1);
    setProfiles([]);
    setHasMore(true);
  };

  const handleFilterChange = () => {
    setIsLoading(true);
    setPage(1);
    setProfiles([]);
    setHasMore(true);
  };

  const resetFilters = () => {
    setIsResetting(true);
    setResetTriggered(true);

    setSelectedCountry('all');
    setSelectedGender('all');
    setSelectedNiche('all');
    setSelectedEthnicity('all');
    setSelectedAge('all');
    setSelectedLanguage('all');
    setSelectedHeight('all');
    setSelectedHairColor('all');
    setSelectedEyeColor('all');
    setSelectedWaist('all');
    setSelectedHips('all');
    setSelectedButt('all');
    setSelectedShoeSize('all');
    setSelectedBodyType('all');
    setSelectedBreastType('all');
    setSelectedBreastSize('all');
    setSelectedChestSize('all');
    setSelectedPiercings([]);
    setSelectedTattoos([]);
  };

  // Reset selectedShoeSize when selectedGender changes
  useEffect(() => {
    if (selectedGender === 'all') {
      setSelectedShoeSize('all');
    } else {
      const genderKey = selectedGender as keyof typeof shoeSizeOptions;
      const validOptions = shoeSizeOptions[genderKey] || [];
      const isCurrentShoeSizeValid = validOptions.some((option) => option.value === selectedShoeSize);
      if (!isCurrentShoeSizeValid) {
        setSelectedShoeSize('all');
      }
    }
  }, [selectedGender]);

  // Effect to handle filter fetch after reset
  useEffect(() => {
    if (resetTriggered) {
      handleFilterChange();
        setIsResetting(false);
        setResetTriggered(false);
    }
  }, [resetTriggered]);

  // Load more profiles when reaching the end
  const loadMore = useCallback(() => {
    if (hasMore && !isLoading) {
      setPage((prev) => prev + 1);
    }
  }, [hasMore, isLoading]);

  // Group profiles into rows for grid display
  const profilesPerRow = 4;
  const groupedProfiles = [];
  for (let i = 0; i < profiles.length; i += profilesPerRow) {
    groupedProfiles.push(profiles.slice(i, i + profilesPerRow));
  }

  return (
    <main className="min-h-screen bg-white dark:bg-[#18181b]">
      {/* Banner Ad */}
      <div className="container max-w-[90.7%] mx-auto mt-4 w-full bg-turquoise text-white flex items-center justify-center h-24 sm:h-32 md:h-[136px] text-center">
        <h1 className="text-base sm:text-lg md:text-xl font-medium">Network</h1>
      </div>

      <div className="container max-w-[90.7%] mx-auto px-0 py-4 flex flex-col md:flex-row gap-6">
        {/* Sidebar with filters */}
        <div className="w-full md:w-64 flex-shrink-0 sticky top-[120px] z-[2] max-h-[calc(100vh-120px)] min-h-0">
          <div className="flex flex-col bg-transparent border border-solid border-gray-300 dark:border-white rounded-lg p-4 shadow-sm min-h-0">
            {/* Fixed Top Section */}
            <div className="flex justify-between items-center mb-4 flex-shrink-0">
              <h2 className="text-lg font-semibold text-gray-600 dark:text-white">Filters</h2>
              <button
                onClick={resetFilters}
                className="text-xs text-gray-600 dark:text-white flex gap-2 items-center disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!hasActiveFilters()}
              >
                Reset All <FaRotate className={`h-4 w-4 reset-icon transition-transform duration-500 ${isResetting ? 'rotating' : ''}`} />
              </button>
            </div>

            {/* Filter Section */}
            <div className="flex-1 min-h-0 h-full">
              <div className="h-full w-full max-h-[calc(100vh-120px-64px-48px-32px)] overflow-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50">
                {filters.map((filter) =>
                  !filter.condition || filter.condition({ selectedGender, selectedNiche, selectedBodyType, selectedAge }) ? (
                    <div key={filter.id}>{filter.component}</div>
                  ) : null
                )}
              </div>
            </div>

            {/* Search Button */}
            <div className="flex-shrink-0">
              <button
                onClick={handleFilterChange}
                className="w-full bg-turquoise text-white py-2 rounded-md hover:bg-turquoise/80"
              >
                Search
              </button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="w-full z-[1] relative">
          {/* Sorting and View Mode */}
          <div className="relative flex flex-col md:flex-row items-center justify-between gap-4 mb-4">
            <div className="flex-shrink-0 w-full md:w-48 z-[1]">
              <SelectDropdown
                options={filterOptions}
                value={sortBy}
                onChange={handleSortChange}
                placeholder="Sort by"
              />
            </div>

            <div className="absolute w-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center gap-2 text-gorilla-gray dark:text-white text-sm z-0">
              Results Found: {totalUnpaginatedResults}
            </div>

            <div className="flex-shrink-0 flex space-x-2 mb-4 md:mb-0 z-[1]">
              <Tippy content="Grid View">
                <button onClick={() => setViewMode('grid')}>
                  <LayoutGrid className={`w-8 h-8 ${viewMode === 'grid' ? 'text-turquoise dark:text-turquoise' : 'text-gray-400 dark:text-white'}`} />
                </button>
              </Tippy>

              <Tippy content="List View">
                <button onClick={() => setViewMode('list')}>
                  <LayoutList className={`w-8 h-8 ${viewMode === 'list' ? 'text-turquoise dark:text-turquoise' : 'text-gray-400 dark:text-white'}`} />
                </button>
              </Tippy>

              <Tippy content="Map View">
                <button onClick={() => setViewMode('map')}>
                  <MapPin className={`w-[29.5px] h-[29.5px] relative -top-[1px] ${viewMode === 'map' ? 'text-turquoise dark:text-turquoise' : 'text-gray-400 dark:text-white'}`} />
                </button>
              </Tippy>
            </div>
          </div>

          {/* Loading Indicator */}
          {isLoading && profiles.length === 0 && (
            viewMode === 'map' ? (
              <div className="bg-gray-200 dark:bg-zinc-800 rounded-lg h-[600px] overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-700 to-transparent animate-pulse-slow"></div>
                <div className="absolute top-3 right-3 w-40 h-10 bg-gray-300 dark:bg-gray-700 rounded-md"></div>
                <div className="absolute bottom-6 right-6 w-12 h-12 bg-gray-300 dark:bg-gray-700 rounded-md"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 rounded-full border-4 border-gray-300 dark:border-gray-700 border-t-turquoise animate-spin"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({ length: 8 }).map((_, index) => (
                  <NetworkProfileSkeleton key={index} />
                ))}
              </div>
            )
          )}

          {/* No results found messages */}
          {!isLoading && profiles.length === 0 && (
            viewMode === 'grid' ? (
              <div className="w-full text-center py-12">
                <p className="text-xl text-gray-300 dark:text-white capitalize">No creators found matching your criteria.</p>
              </div>
            ) : (
              <div className="relative w-full h-[600px]">
                <MapContainer users={[]} setIsLoading={setIsLoading} />
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <p className="text-xl text-white capitalize">No creators found matching your criteria.</p>
                </div>
              </div>
            )
          )}

          {viewMode === 'grid' && profiles.length > 0 && (
            <Virtuoso
              useWindowScroll={true}
              data={groupedProfiles}
              endReached={loadMore}
              itemContent={(index, rowProfiles) => (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-0 pb-4">
                  {rowProfiles.map((profile: NetworkProfile) => (
                    <NetworkProfileCard key={profile.id} profile={profile} />
                  ))}
                  {/* Fill empty grid slots if row is not full */}
                  {rowProfiles.length < profilesPerRow &&
                    Array.from({ length: profilesPerRow - rowProfiles.length }).map((_, i) => (
                      <div key={`empty-${index}-${i}`} className="hidden sm:block lg:block xl:block" />
                    ))}
                </div>
              )}
              components={{
                Footer: () =>
                  hasMore ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-2 py-2">
                      {Array.from({ length: 4 }).map((_, index) => (
                        <NetworkProfileSkeleton key={index} />
                      ))}
                    </div>
                  ) : null,
              }}
            />
          )}

          {viewMode === 'map' && profiles.length > 0 && (
            <MapContainer users={profiles} setIsLoading={setIsLoading} />
          )}
        </div>
      </div>
    </main>
  );
};
export default NetworkClient;