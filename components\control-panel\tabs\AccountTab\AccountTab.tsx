'use client';

import React, { useState, useEffect, useRef } from 'react';
import { updateUserSettings } from '@/lib/api/updateUserSettings';
import { toast } from 'react-toastify';
import { useDispatch } from 'react-redux';
import { setUserInfo } from '@/redux/slices/userInfoSlice';
import { motion } from 'framer-motion';
import zxcvbn from 'zxcvbn';
import ConnectSocialModal from '@/components/account/settings/modals/ConnectSocialModal';
import ProfileInfoSection from '@/components/account/settings/tabs/AccountTab/ProfileInfoSection';
import AccountInfoSection from '@/components/account/settings/tabs/AccountTab/AccountInfoSection';
import BasicDetailsSection from '@/components/account/settings/tabs/AccountTab/BasicDetailsSection';
import PhysicalAttributesSection from '@/components/account/settings/tabs/AccountTab/PhysicalAttributesSection';
import SecuritySection from '@/components/account/settings/tabs/AccountTab/SecuritySection';
import SaveButton from '@/components/account/settings/tabs/AccountTab/SaveButton';
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { debounce } from 'lodash';
import { useDebounce } from '@/hooks/useDebounce';

interface AccountTabProps {
  userData: any;
  connectedAccounts: any;
  onFormChange: (hasChanges: boolean, updatedData?: any) => void;
}

interface LocationSuggestion {
  display_name: string;
  place_id: number;
  lat: string;
  lon: string;
  type: string;
}

export interface AccountFormData {
  username: string;
  email: string;
  phoneNumber: string;
  twoFactorAuth: boolean;
  displayName: string;
  bio: string;
  countryOfOrigin: string;
  latitude: string;
  longitude: string;
  amazonWishlist: string;
  officialWebsite: string;
  accountType: string;
  is_verified: boolean;
  profilePhoto: string;
  coverBanner: string;
  coverBannerType: string;
  dateOfBirth: Date | null;
  birthplace: string;
  languages: string[];
  astrology: string;
  ethnicity: string;
  bodyType: string;
  breastSize: string;
  breastType: string;
  eyeColor: string;
  mixedEyeColors: { left: string; right: string };
  gender: string;
  relationshipStatus: string;
  interestedIn: string[];
  niche: string[];
  collabs: any;
  interests: string[];
  fetishes: string[];
  piercings: string[];
  tattoos: string[];
  physicalAttributes: {
    height: string;
    heightCentimeters?: number;
    weight: {
      pounds: number | string;
      kilograms: number | string;
    };
    cupSize: string;
    bustSize: string;
    braSize: string;
    waist: string;
    hips: string;
    butt: string;
    shoeSize: string;
    eyeColor: string;
    mixedEyeColors: { left: string; right: string };
    bodyType: string;
    breastSize: string;
    breastType: string;
    hairColor: string;
  };
  location: string;
}

const AccountTab = ({ userData, onFormChange, connectedAccounts }: AccountTabProps) => {
  const dispatch = useDispatch();

  const [isLoading, setIsLoading] = useState(false);
  const [isConnectedSocialModalOpen, setIsConnectedSocialModalOpen] = useState(false);
  const [formData, setFormData] = useState<AccountFormData>({
    username: userData?.account?.username || '',
    email: userData?.account?.email || '',
    phoneNumber: userData?.account?.phoneNumber || '',
    twoFactorAuth: userData?.account?.twoFactorAuth || false,
    displayName: userData?.account?.displayName || '',
    bio: userData?.account?.bio || '',
    countryOfOrigin: userData?.account?.countryOfOrigin || '',
    latitude: userData?.account?.latitude || '',
    longitude: userData?.account?.longitude || '',
    amazonWishlist: userData?.account?.amazonWishlist || '',
    officialWebsite: userData?.account?.officialWebsite || '',
    accountType: userData?.account?.accountType || 'user',
    is_verified: userData?.account?.is_verified || false,
    profilePhoto: userData?.account?.profilePhoto || '',
    coverBanner: userData?.account?.coverBanner || '',
    coverBannerType: userData?.account?.coverBannerType || 'image',
    dateOfBirth: userData?.account?.dateOfBirth ? new Date(userData?.account?.dateOfBirth) : null,
    birthplace: userData?.account?.birthplace || '',
    languages: userData?.account?.languages || [],
    astrology: userData?.account?.astrology || '',
    ethnicity: userData?.account?.ethnicity || '',
    gender: userData?.account?.gender || 'male',
    relationshipStatus: userData?.account?.relationshipStatus || 'single',
    interestedIn: userData?.account?.interestedIn || [],
    niche: userData?.account?.niche || [],
    collabs: userData?.account?.collabs || {},
    interests: userData?.account?.interests || [],
    fetishes: userData?.account?.fetishes || [],
    piercings: userData?.account?.piercings || [],
    tattoos: userData?.account?.tattoos || [],
    bodyType: userData?.account?.physicalAttributes?.bodyType || '',
    breastSize: userData?.account?.physicalAttributes?.breastSize || '',
    breastType: userData?.account?.physicalAttributes?.breastType || '',
    eyeColor: userData?.account?.physicalAttributes?.eyeColor || '',
    mixedEyeColors: {
      left: userData?.account?.physicalAttributes?.mixedEyeColors?.left || '',
      right: userData?.account?.physicalAttributes?.mixedEyeColors?.right || '',
    },
    physicalAttributes: {
      height: userData?.account?.physicalAttributes?.height || '',
      heightCentimeters: userData?.account?.physicalAttributes?.heightCentimeters,
      weight: {
        pounds: userData?.account?.physicalAttributes?.weight?.pounds || 0,
        kilograms: userData?.account?.physicalAttributes?.weight?.kilograms || 0,
      },
      cupSize: userData?.account?.physicalAttributes?.cupSize || '',
      bustSize: userData?.account?.physicalAttributes?.bustSize || '',
      braSize: userData?.account?.physicalAttributes?.braSize || '',
      waist: userData?.account?.physicalAttributes?.waist || '',
      hips: userData?.account?.physicalAttributes?.hips || '',
      butt: userData?.account?.physicalAttributes?.butt || '',
      shoeSize: userData?.account?.physicalAttributes?.shoeSize || '',
      eyeColor: userData?.account?.physicalAttributes?.eyeColor || '',
      mixedEyeColors: {
        left: userData?.account?.physicalAttributes?.mixedEyeColors?.left || '',
        right: userData?.account?.physicalAttributes?.mixedEyeColors?.right || '',
      },
      bodyType: userData?.account?.physicalAttributes?.bodyType || '',
      breastSize: userData?.account?.physicalAttributes?.breastSize || '',
      breastType: userData?.account?.physicalAttributes?.breastType || '',
      hairColor: userData?.account?.physicalAttributes?.hairColor || '',
    },
    location: userData?.account?.location || '',
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [nationalitySuggestions, setNationalitySuggestions] = useState<LocationSuggestion[]>([]);
  const [birthplaceSuggestions, setBirthplaceSuggestions] = useState<LocationSuggestion[]>([]);
  const [locationSuggestions, setLocationSuggestions] = useState<LocationSuggestion[]>([]);
  const [showNationalitySuggestions, setShowNationalitySuggestions] = useState(false);
  const [showBirthplaceSuggestions, setShowBirthplaceSuggestions] = useState(false);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [isLoadingNationalitySuggestions, setIsLoadingNationalitySuggestions] = useState(false);
  const [isLoadingBirthplaceSuggestions, setIsLoadingBirthplaceSuggestions] = useState(false);
  const [isLoadingLocationSuggestions, setIsLoadingLocationSuggestions] = useState(false);
  const [amazonUrlError, setAmazonUrlError] = useState('');
  const [websiteError, setWebsiteError] = useState<string | null>(null);
  const [usernameError, setUsernameError] = useState<string | null>(null);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [usernameStatus, setUsernameStatus] = useState<'checking' | 'available' | 'unavailable' | 'unchanged'>('unchanged');
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const nationalitySuggestionsRef = useRef<HTMLDivElement>(null) as any;
  const birthplaceSuggestionsRef = useRef<HTMLDivElement>(null) as any;
  const locationSuggestionsRef = useRef<HTMLDivElement>(null) as any;
  const initialFormData = useRef({ ...formData });

  const initialUsername = useRef(formData.username);

  const immediateUsername = formData.username;
  const debouncedUsername = useDebounce(immediateUsername, 500);

  const shouldCheckUsername =
    debouncedUsername &&
    debouncedUsername !== initialUsername.current;

  const isUsernameTaken = useQuery(
    api.accounts.checkUsername,
    shouldCheckUsername
      ? { username: debouncedUsername }
      : 'skip'
  );

  useEffect(() => {
    if (!shouldCheckUsername) {
      setUsernameStatus('unchanged');
    } else if (isUsernameTaken === undefined) {
      setUsernameStatus('checking');
    } else if (isUsernameTaken) {
      setUsernameStatus('unavailable');
    } else {
      setUsernameStatus('available');
    }
  }, [shouldCheckUsername, isUsernameTaken]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    } else {
      setEmailError(null);
      return true;
    }
  };

  const validateUsername = (username: string): boolean => {
    if (!username) {
      setUsernameError('Username is required');
      return false;
    }
    if (username.length < 3) {
      setUsernameError('Username must be at least 3 characters');
      return false;
    }
    if (username.length > 30) {
      setUsernameError('Username must be less than 30 characters');
      return false;
    }
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(username)) {
      setUsernameError('Username can only contain letters, numbers, and underscores');
      return false;
    }
    setUsernameError(null);
    return true;
  };

  const validateWebsiteUrl = (url: string): boolean => {
    if (!url) return true;
    try {
      const urlObject = new URL(url);
      return urlObject.protocol === 'http:' || urlObject.protocol === 'https:';
    } catch {
      return false;
    }
  };

  const validateAmazonUrl = (url: string) => {
    if (!url) return true;
    const amazonRegex = /^https?:\/\/(www\.)?(amazon\.com|amzn\.to)/i;
    return amazonRegex.test(url);
  };

  const calculatePasswordStrength = (password: string) => {
    if (!password) {
      setPasswordStrength(0);
      return;
    }
    const result = zxcvbn(password);
    setPasswordStrength(result.score);
  };

  const debouncedFetchLocations = useRef(
    debounce((query: string, type: 'nationality' | 'birthplace' | 'location') => {
      fetchLocationSuggestions(query, type);
    }, 300)
  ).current;

  const debouncedValidateAmazonUrl = useRef(
    debounce((url: string) => {
      if (url && !validateAmazonUrl(url)) {
        setAmazonUrlError('Please enter a valid Amazon Wishlist URL');
      } else {
        setAmazonUrlError('');
      }
    }, 500)
  ).current;

  const fetchLocationSuggestions = async (query: string, type: 'nationality' | 'birthplace' | 'location') => {
    const setSuggestions =
      type === 'nationality' ? setNationalitySuggestions :
        type === 'birthplace' ? setBirthplaceSuggestions :
          setLocationSuggestions;
    const setIsLoading =
      type === 'nationality' ? setIsLoadingNationalitySuggestions :
        type === 'birthplace' ? setIsLoadingBirthplaceSuggestions :
          setIsLoadingLocationSuggestions;

    setIsLoading(true);
    try {
      const response = await fetch(
        `https://api.locationiq.com/v1/autocomplete?key=${process.env.NEXT_PUBLIC_LOCATION_IQ_API_KEY}&q=${encodeURIComponent(query)}&limit=10`,
        { headers: { 'Accept-Language': 'en' } }
      );
      if (!response.ok) throw new Error('Failed to fetch location suggestions');
      const data = await response.json();
      const suggestions = data
        .filter((item: any) => type === 'nationality' ? item.type === 'country' : ['country', 'city'].includes(item.type))
        .map((item: any) => ({
          place_id: item.place_id,
          display_name: item.display_name.split(',')[0].trim(),
          lat: item.lat,
          lon: item.lon,
          type: item.type,
        }));
      setSuggestions(suggestions);
    } catch (error: any) {
      console.error('Error fetching location suggestions:', error);
      toast.error('Error fetching location suggestions. Please try again.');
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (name === 'officialWebsite') {
      if (value && !validateWebsiteUrl(value)) {
        setWebsiteError('Please enter a valid website URL (e.g., https://example.com)');
      } else {
        setWebsiteError(null);
      }
    }
    setFormData(prev => ({ ...prev, [name]: value }));
    if (name === 'username') {
      setFormData(prev => ({ ...prev, username: value }));
    } else if (name === 'email') {
      if (value && !validateEmail(value)) {
        setEmailError('Please enter a valid email address');
      } else {
        setEmailError('');
      }
    } else if (name === 'countryOfOrigin' && value.length > 1) {
      debouncedFetchLocations(value, 'nationality');
      setShowNationalitySuggestions(true);
    } else if (name === 'countryOfOrigin') {
      setNationalitySuggestions([]);
      setShowNationalitySuggestions(false);
    } else if (name === 'birthplace' && value.length > 1) {
      debouncedFetchLocations(value, 'birthplace');
      setShowBirthplaceSuggestions(true);
    } else if (name === 'birthplace') {
      setBirthplaceSuggestions([]);
      setShowBirthplaceSuggestions(false);
    } else if (name === 'location' && value.length > 1) {
      debouncedFetchLocations(value, 'location');
      setShowLocationSuggestions(true);
    } else if (name === 'location') {
      setLocationSuggestions([]);
      setShowLocationSuggestions(false);
    } else if (name === 'amazonWishlist') {
      debouncedValidateAmazonUrl(value);
    }
  };

  const handlePhoneChange = (value: string) => {
    setFormData(prev => ({ ...prev, phoneNumber: value }));
  };

  const handleLocationSelect = (suggestion: LocationSuggestion, field: 'countryOfOrigin' | 'birthplace' | 'location') => {
    setFormData(prev => ({
      ...prev,
      [field]: suggestion.display_name,
      latitude: field === 'countryOfOrigin' ? suggestion.lat : prev.latitude,
      longitude: field === 'countryOfOrigin' ? suggestion.lon : prev.longitude,
    }));
    if (field === 'countryOfOrigin') {
      setShowNationalitySuggestions(false);
    } else if (field === 'birthplace') {
      setShowBirthplaceSuggestions(false);
    } else {
      setShowLocationSuggestions(false);
    }
  };

  const handlePhysicalAttributeChange = (
    field: keyof AccountFormData['physicalAttributes'],
    value: string | number | { left: string; right: string }
  ) => {
    setFormData(prev => {
      if (field === 'weight' || field === 'mixedEyeColors') {
        return {
          ...prev,
          physicalAttributes: {
            ...prev.physicalAttributes,
            [field]: value,
          },
        };
      }
      return {
        ...prev,
        physicalAttributes: {
          ...prev.physicalAttributes,
          [field]: value,
        },
      };
    });
  };

  const handleSave = async () => {
    const hasEmailChanged = formData.email !== initialFormData.current.email;
    const hasUsernameChanged = formData.username !== initialFormData.current.username;

    if (!userData.accountType) {
      toast.error('Account type is missing!');
      return;
    }

    if (hasEmailChanged || formData.email) {
      if (!validateEmail(formData.email)) {
        toast.error('Please enter a valid email address');
        return;
      }
    }

    if (hasUsernameChanged) {
      if (usernameStatus === 'unavailable') {
        toast.error('This username is unavailable, please try another.');
        return;
      }
      if (!validateUsername(formData.username)) {
        toast.error('Please enter a valid username');
        return;
      }
    }

    if (!validateAmazonUrl(formData.amazonWishlist)) {
      toast.error('Please enter a valid Amazon Wishlist URL');
      return;
    }

    if (formData.officialWebsite && !validateWebsiteUrl(formData.officialWebsite)) {
      toast.error('Please enter a valid website URL');
      return;
    }

    setIsLoading(true);
    try {
      const accountData = {
        username: formData.username,
        email: formData.email,
        phoneNumber: formData.phoneNumber,
        twoFactorAuth: formData.twoFactorAuth,
        displayName: formData.displayName,
        bio: formData.bio,
        countryOfOrigin: formData.countryOfOrigin,
        latitude: formData.latitude,
        longitude: formData.longitude,
        amazonWishlist: formData.amazonWishlist,
        officialWebsite: formData.officialWebsite,
        dateOfBirth: formData.dateOfBirth,
        birthplace: formData.birthplace,
        languages: formData.languages,
        ethnicity: formData.ethnicity,
        gender: formData.gender,
        relationshipStatus: formData.relationshipStatus,
        interestedIn: formData.interestedIn,
        fetishes: formData.fetishes,
        piercings: formData.piercings,
        tattoos: formData.tattoos,
        physicalAttributes: formData.physicalAttributes,
        location: formData.location,
        is_verified: formData.is_verified,
        niche: formData.niche,
        collabs: formData.collabs,
        profilePhoto: formData.profilePhoto,
        coverBanner: formData.coverBanner,
        coverBannerType: formData.coverBannerType,
      };

      const accountResult = await updateUserSettings(userData.userId, userData.accountType, 'account', accountData);

      if (accountResult.success) {
        dispatch(setUserInfo({
          ...userData,
          username: formData.username,
          displayName: formData.displayName,
        }));
        toast.success('Changes to account settings saved successfully!');
        initialFormData.current = { ...formData };
        const updatedUserData = {
          ...userData,
          account: { ...userData.account, ...accountData },
        };
        if (onFormChange) onFormChange(false, updatedUserData);
      } else {
        throw new Error('Failed to update settings');
      }
    } catch (error: any) {
      console.error(error);
      toast.error(error?.message || 'An error occurred while saving settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({ ...prev, [name]: value }));
    if (name === 'newPassword') calculatePasswordStrength(value);
  };

  const handleUpdatePassword = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords don't match");
      return;
    }
    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }
    setIsUpdatingPassword(true);
    try {
      const result = await updateUserSettings(
        userData.userId,
        userData.accountType,
        'account',
        { password: { new: passwordData.newPassword } }
      );
      if (result.success) {
        toast.success('Password updated successfully');
        setIsChangingPassword(false);
        setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
        setIsUpdatingPassword(false);
      } else {
        throw new Error(result.message || 'Failed to update password');
      }
    } catch (error: any) {
      console.error('Error:', error);
      toast.error(error.message || 'Failed to update password');
      setIsUpdatingPassword(false);
    }
  };

  useEffect(() => {
    const changed = JSON.stringify(initialFormData.current) !== JSON.stringify(formData);
    if (onFormChange) onFormChange(changed);
  }, [formData, onFormChange]);

  useEffect(() => {
    return () => {
      debouncedFetchLocations.cancel();
      debouncedValidateAmazonUrl.cancel();
    };
  }, [debouncedFetchLocations, debouncedValidateAmazonUrl]);

  return (
    <motion.div
      key="account"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="space-y-6"
    >
      <ProfileInfoSection
        formData={formData}
        setFormData={setFormData}
        handleInputChange={handleInputChange}
        locationSuggestions={locationSuggestions}
        showLocationSuggestions={showLocationSuggestions}
        isLoadingLocationSuggestions={isLoadingLocationSuggestions}
        handleLocationSelect={handleLocationSelect}
        amazonUrlError={amazonUrlError}
        websiteError={websiteError}
        locationSuggestionsRef={locationSuggestionsRef}
      />

      <AccountInfoSection
        formData={formData}
        setFormData={setFormData}
        handleInputChange={handleInputChange}
        handlePhoneChange={handlePhoneChange}
        usernameStatus={usernameStatus}
        usernameError={usernameError}
        emailError={emailError}
      />

      <BasicDetailsSection
        formData={formData}
        setFormData={setFormData}
        handleInputChange={handleInputChange}
        nationalitySuggestions={nationalitySuggestions}
        birthplaceSuggestions={birthplaceSuggestions}
        showNationalitySuggestions={showNationalitySuggestions}
        showBirthplaceSuggestions={showBirthplaceSuggestions}
        isLoadingNationalitySuggestions={isLoadingNationalitySuggestions}
        isLoadingBirthplaceSuggestions={isLoadingBirthplaceSuggestions}
        handleLocationSelect={handleLocationSelect}
        nationalitySuggestionsRef={nationalitySuggestionsRef}
        birthplaceSuggestionsRef={birthplaceSuggestionsRef}
      />

      <PhysicalAttributesSection
        formData={formData}
        setFormData={setFormData}
        handlePhysicalAttributeChange={handlePhysicalAttributeChange}
      />

      <SecuritySection
        userData={userData}
        passwordData={passwordData}
        handlePasswordChange={handlePasswordChange}
        handleUpdatePassword={handleUpdatePassword}
        isChangingPassword={isChangingPassword}
        setIsChangingPassword={setIsChangingPassword}
        isUpdatingPassword={isUpdatingPassword}
        showPassword={showPassword}
        setShowPassword={setShowPassword}
        passwordStrength={passwordStrength}
      />

      <SaveButton
        isLoading={isLoading}
        handleSave={handleSave}
        disabled={isLoading || !!emailError || !!amazonUrlError || usernameStatus === 'unavailable' || usernameStatus === 'checking'}
      />

      <ConnectSocialModal
        isOpen={isConnectedSocialModalOpen}
        onClose={() => setIsConnectedSocialModalOpen(false)}
        connectedAccounts={connectedAccounts}
      />
    </motion.div>
  );
};
export default AccountTab;