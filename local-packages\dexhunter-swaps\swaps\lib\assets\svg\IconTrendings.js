import { jsx as C } from "react/jsx-runtime";
import { memo as e } from "react";
const t = (o) => /* @__PURE__ */ C(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 16 20",
    fill: "none",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M2.23403 18.4125C3.06785 19.1492 4.05534 19.667 5.11555 19.9235C5.217 19.9585 5.28527 19.8185 5.217 19.7485C2.94611 17.4635 4.19392 14.9905 5.11555 13.8756C5.77263 13.0796 6.70849 11.7686 6.64022 10.0106C6.64022 9.83459 6.81089 9.69359 6.94553 9.79959C8.23411 10.5026 9.11497 12.0496 9.35201 13.3146C9.72559 12.9286 9.86118 12.3306 9.86118 11.8036C9.86118 11.6276 10.0641 11.4866 10.2338 11.6276C11.4541 12.7876 13.5553 16.7245 10.1656 19.8175C10.0982 19.8885 10.1655 20.0295 10.2338 19.9945C11.2617 19.7103 12.2283 19.2208 13.0812 18.5525C18.6062 14.0526 15.0126 6.07262 12.6735 3.43664C12.3691 3.11964 11.8268 3.33064 11.8268 3.78764C11.7926 4.73763 11.5214 5.79162 10.8103 6.49462C10.268 4.10464 8.46072 1.38966 5.88452 0.0536695C5.54602 -0.122329 5.13925 0.158669 5.17338 0.545666C5.23976 3.81564 3.21729 5.89762 1.45462 8.6046C-0.105131 11.0296 -1.12158 15.4945 2.23403 18.4125Z",
        fill: "currentColor"
      }
    )
  }
), n = e(t);
export {
  n as default
};
