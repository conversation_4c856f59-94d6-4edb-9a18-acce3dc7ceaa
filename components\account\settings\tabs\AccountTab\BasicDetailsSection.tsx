'use client';

import React, { useState, useEffect, useRef } from 'react';
import { LabelInputContainer } from '@/components/ui/label-input-container';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { SettingsSection } from '@/components/ui/settings-section';
import { MultipleSelector } from '@/components/ui/multiple-select';
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChevronDown } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { CaptionLabelProps, MonthGridProps } from "react-day-picker";
import { eachMonthOfInterval, eachYearOfInterval, endOfYear, format, isAfter, isBefore, startOfYear } from "date-fns";
import { ethnicityOptions, genderOptions, interestedInOptions, languageOptions, tattooOptions, piercingOptions, relationshipStatusOptions, nicheOptions } from '@/app/account/settings/constants';
import { AccountFormData } from '@/components/account/settings/tabs/AccountTab/AccountTab';

interface LocationSuggestion {
  display_name: string;
  place_id: number;
  lat: string;
  lon: string;
  type: string;
}

interface PersonalInfoSectionProps {
  formData: AccountFormData;
  setFormData: React.Dispatch<React.SetStateAction<AccountFormData>>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleLocationSelect: (suggestion: LocationSuggestion, field: 'countryOfOrigin' | 'birthplace' | 'location') => void;
  nationalitySuggestions: LocationSuggestion[];
  birthplaceSuggestions: LocationSuggestion[];
  showNationalitySuggestions: boolean;
  showBirthplaceSuggestions: boolean;
  isLoadingNationalitySuggestions: boolean;
  isLoadingBirthplaceSuggestions: boolean;
  nationalitySuggestionsRef: React.RefObject<HTMLDivElement>;
  birthplaceSuggestionsRef: React.RefObject<HTMLDivElement>;
}

function MonthGrid({
  className,
  children,
  isYearView,
  startDate,
  endDate,
  years,
  currentYear,
  currentMonth,
  onMonthSelect,
}: {
  className?: string;
  children: React.ReactNode;
  isYearView: boolean;
  setIsYearView: React.Dispatch<React.SetStateAction<boolean>>;
  startDate: Date;
  endDate: Date;
  years: Date[];
  currentYear: number;
  currentMonth: number;
  onMonthSelect: (date: Date) => void;
}) {
  const currentYearRef = useRef<HTMLDivElement>(null);
  const currentMonthButtonRef = useRef<HTMLButtonElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isYearView && currentYearRef.current && scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]",
      ) as HTMLElement;
      if (viewport) {
        const yearTop = currentYearRef.current.offsetTop;
        viewport.scrollTop = yearTop;
      }
      setTimeout(() => {
        currentMonthButtonRef.current?.focus();
      }, 100);
    }
  }, [isYearView]);

  return (
    <div className="relative">
      <table className={className}>{children}</table>
      {isYearView && (
        <div className="absolute inset-0 z-20 -mx-2 -mb-2 bg-background">
          <ScrollArea ref={scrollAreaRef} className="h-full">
            {years.map((year) => {
              const months = eachMonthOfInterval({
                start: startOfYear(year),
                end: endOfYear(year),
              });
              const isCurrentYear = year.getFullYear() === currentYear;

              return (
                <div key={year.getFullYear()} ref={isCurrentYear ? currentYearRef : undefined}>
                  <CollapsibleYear title={year.getFullYear().toString()} open={isCurrentYear}>
                    <div className="grid grid-cols-3 gap-2">
                      {months.map((month) => {
                        const isDisabled = isBefore(month, startDate) || isAfter(month, endDate);
                        const isCurrentMonth =
                          month.getMonth() === currentMonth && year.getFullYear() === currentYear;

                        return (
                          <Button
                            key={month.getTime()}
                            ref={isCurrentMonth ? currentMonthButtonRef : undefined}
                            variant={isCurrentMonth ? "default" : "outline"}
                            size="sm"
                            className="h-7"
                            disabled={isDisabled}
                            onClick={() => onMonthSelect(month)}
                          >
                            {format(month, "MMM")}
                          </Button>
                        );
                      })}
                    </div>
                  </CollapsibleYear>
                </div>
              );
            })}
          </ScrollArea>
        </div>
      )}
    </div>
  );
}

function CaptionLabel({
  children,
  isYearView,
  setIsYearView,
}: {
  isYearView: boolean;
  setIsYearView: React.Dispatch<React.SetStateAction<boolean>>;
} & React.HTMLAttributes<HTMLSpanElement>) {
  return (
    <Button
      className="-ms-2 flex items-center gap-2 text-sm font-medium hover:bg-transparent data-[state=open]:text-muted-foreground/80 [&[data-state=open]>svg]:rotate-180"
      variant="ghost"
      size="sm"
      onClick={() => setIsYearView((prev) => !prev)}
      data-state={isYearView ? "open" : "closed"}
    >
      {children}
      <ChevronDown
        size={16}
        strokeWidth={2}
        className="shrink-0 text-muted-foreground/80 transition-transform duration-200"
        aria-hidden="true"
      />
    </Button>
  );
}

function CollapsibleYear({
  title,
  children,
  open,
}: {
  title: string;
  children: React.ReactNode;
  open?: boolean;
}) {
  return (
    <Collapsible className="border-t border-border px-2 py-1.5" defaultOpen={open}>
      <CollapsibleTrigger asChild>
        <Button
          className="flex w-full justify-start gap-2 text-sm font-medium hover:bg-transparent [&[data-state=open]>svg]:rotate-180"
          variant="ghost"
          size="sm"
        >
          <ChevronDown
            size={16}
            strokeWidth={2}
            className="shrink-0 text-muted-foreground/80 transition-transform duration-200"
            aria-hidden="true"
          />
          {title}
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="overflow-hidden px-3 py-1 text-sm transition-all data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
}

const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  formData,
  setFormData,
  handleInputChange,
  handleLocationSelect,
  nationalitySuggestions,
  birthplaceSuggestions,
  showNationalitySuggestions,
  showBirthplaceSuggestions,
  isLoadingNationalitySuggestions,
  isLoadingBirthplaceSuggestions,
  nationalitySuggestionsRef,
  birthplaceSuggestionsRef,
}) => {
  const today = new Date();
  const [month, setMonth] = useState(today);
  const [date, setDate] = useState<Date | undefined>(formData.dateOfBirth || today);
  const [isYearView, setIsYearView] = useState(false);
  const startDate = new Date(1980, 6);
  const endDate = new Date(2030, 6);

  const years = eachYearOfInterval({
    start: startOfYear(startDate),
    end: endOfYear(endDate),
  });

  useEffect(() => {
    if (date) {
      setFormData((prev: any) => ({ ...prev, dateOfBirth: date }));
    }
  }, [date, setFormData]);

  const handleEthnicityChange = (value: string) => {
    setFormData((prev: any) => ({ ...prev, ethnicity: value }));
  };

  const handleGenderChange = (value: string) => {
    setFormData((prev: any) => ({ ...prev, gender: value }));
  };

  const handleInterestedInChange = (value: string) => {
    setFormData((prev: any) => ({ ...prev, interestedIn: value }));
  };

  const personalInfoSettings = [
    {
      id: 'ethnicity',
      label: 'Ethnicity',
      description: 'Select your ethnicity',
      type: 'select' as const,
      value: formData.ethnicity,
      options: ethnicityOptions,
      onChange: handleEthnicityChange,
    },
    {
      id: 'gender',
      label: 'Gender',
      description: 'Select your gender',
      type: 'select' as const,
      value: formData.gender,
      options: genderOptions,
      onChange: handleGenderChange,
    },
  ];

  const relationshipSettings = [
    {
      id: 'interestedIn',
      label: 'Interested In',
      description: 'Select your interests',
      type: 'select' as const,
      value: formData.interestedIn,
      options: interestedInOptions,
      onChange: handleInterestedInChange,
      isMulti: true,
    },
    {
      id: 'relationshipStatus',
      label: 'Relationship Status',
      description: 'Select your relationship status',
      type: 'select' as const,
      value: formData.relationshipStatus,
      options: relationshipStatusOptions,
      onChange: (value: string) =>
        setFormData((prev: any) => ({
          ...prev,
          relationshipStatus: value,
        })),
    },
  ];

  return (
    <div className="space-y-6">

      {/* Basic Details Header */}
      <LabelInputContainer className="mb-6 mt-10">
        <h3 className="text-xl font-semibold text-[#121212] dark:text-white mb-1">Basic Details</h3>
        <div className="mb-6 border border-solid border-b border-l-0 border-r-0 border-t-0 border-black/60 dark:border-white/60"></div>
      </LabelInputContainer>

      {/* Date of Birth */}
      <LabelInputContainer>
        <Label htmlFor="dateOfBirth">Date of Birth</Label>
        <div className="space-y-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="dateOfBirth"
                variant="default"
                className={cn(
                  "flex h-10 w-full items-center justify-between border-none !bg-gray-300 dark:!bg-zinc-800 dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
                )}
              >
                <span className={cn("truncate font-normal text-neutral-400", !formData.dateOfBirth && "text-neutral-400")}>
                  {formData.dateOfBirth ? format(formData.dateOfBirth, "PPP") : "Select your date of birth"}
                </span>
                <CalendarIcon
                  size={16}
                  strokeWidth={2}
                  className="shrink-0 text-neutral-400/80 transition-colors group-hover:text-neutral-400"
                  aria-hidden="true"
                />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-2 border-none" align="start">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                month={month}
                onMonthChange={setMonth}
                defaultMonth={new Date()}
                startMonth={startDate}
                endMonth={endDate}
                className="overflow-hidden rounded-lg border border-border p-2 bg-gray-200 dark:bg-zinc-800"
                classNames={{
                  month_caption: "ms-2.5 me-20 justify-start",
                  nav: "justify-end",
                  day_button: "hover:bg-zinc-600 hover:text-white dark:hover:bg-zinc-400",
                }}
                components={{
                  CaptionLabel: (props: CaptionLabelProps) => (
                    <CaptionLabel isYearView={isYearView} setIsYearView={setIsYearView} {...props} />
                  ),
                  MonthGrid: (props: MonthGridProps) => {
                    return (
                      <MonthGrid
                        className={props.className}
                        isYearView={isYearView}
                        setIsYearView={setIsYearView}
                        startDate={startDate}
                        endDate={endDate}
                        years={years}
                        currentYear={month.getFullYear()}
                        currentMonth={month.getMonth()}
                        onMonthSelect={(selectedMonth: Date) => {
                          setMonth(selectedMonth);
                          setIsYearView(false);
                        }}
                      >
                        {props.children}
                      </MonthGrid>
                    );
                  },
                }}
              />
            </PopoverContent>
          </Popover>
        </div>
      </LabelInputContainer>

      {/* Nationality */}
      <LabelInputContainer>
        <Label htmlFor="countryOfOrigin">Nationality</Label>
        <div className="relative">
          <input
            id="countryOfOrigin"
            name="countryOfOrigin"
            placeholder="Select the country you were born in"
            value={formData.countryOfOrigin}
            onChange={handleInputChange}
            autoComplete="off"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
          {showNationalitySuggestions && (
            <div
              ref={nationalitySuggestionsRef}
              className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto"
            >
              {isLoadingNationalitySuggestions ? (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  Loading suggestions...
                </div>
              ) : nationalitySuggestions.length > 0 ? (
                nationalitySuggestions.map((suggestion) => (
                  <div
                    key={suggestion.place_id}
                    className="px-4 py-2 hover:bg-gray-300 dark:hover:bg-gray-700 cursor-pointer text-sm"
                    onClick={() => handleLocationSelect(suggestion, 'countryOfOrigin')}
                  >
                    {suggestion.display_name}
                  </div>
                ))
              ) : (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  No countries found. Try typing something different.
                </div>
              )}
            </div>
          )}
        </div>
      </LabelInputContainer>

      {/* Birthplace */}
      <LabelInputContainer>
        <Label htmlFor="birthplace">Birthplace</Label>
        <div className="relative">
          <input
            type="text"
            id="birthplace"
            name="birthplace"
            value={formData.birthplace}
            onChange={handleInputChange}
            placeholder="Enter city or country"
            autoComplete="off"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
          {showBirthplaceSuggestions && (
            <div
              ref={birthplaceSuggestionsRef}
              className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto"
            >
              {isLoadingBirthplaceSuggestions ? (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  Loading suggestions...
                </div>
              ) : birthplaceSuggestions.length > 0 ? (
                birthplaceSuggestions.map((suggestion) => (
                  <div
                    key={suggestion.place_id}
                    className="px-4 py-2 hover:bg-gray-300 dark:hover:bg-gray-700 cursor-pointer text-sm"
                    onClick={() => handleLocationSelect(suggestion, 'birthplace')}
                  >
                    {suggestion.display_name}
                  </div>
                ))
              ) : (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  No locations found. Try typing something different.
                </div>
              )}
            </div>
          )}
        </div>
      </LabelInputContainer>

      {/* Languages */}
      <LabelInputContainer>
        <Label htmlFor="languages">Languages</Label>
        <MultipleSelector
          options={languageOptions}
          value={formData.languages.map(lang => ({
            value: lang,
            label: languageOptions.find(opt => opt.value === lang)?.label || lang
          }))}
          onChange={(selected) => {
            setFormData((prev: any) => ({
              ...prev,
              languages: selected.map(option => option.value)
            }));
          }}
          placeholder="Select languages..."
          className="flex w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          badgeClassName="bg-white hover:bg-zinc-800 dark:bg-zinc-700 text-[#121212] hover:text-white dark:text-white"
        />
      </LabelInputContainer>

      <SettingsSection options={personalInfoSettings} />

      {/* Lifestyle Details Header */}
      <LabelInputContainer className="mb-6 mt-10">
        <h3 className="text-xl font-semibold text-[#121212] dark:text-white mb-1">Lifestyle Details</h3>
        <div className="mb-6 border border-solid border-b border-l-0 border-r-0 border-t-0 border-black/60 dark:border-white/60"></div>
      </LabelInputContainer>

      {/* Relationship Details */}
      <SettingsSection options={relationshipSettings} />

      {/* Piercings */}
      <LabelInputContainer>
        <Label htmlFor="piercings">Piercings</Label>
        <MultipleSelector
          options={piercingOptions}
          value={(formData.piercings || []).map(piercing => ({
            value: piercing,
            label: piercingOptions.find(opt => opt.value === piercing)?.label || piercing
          }))}
          onChange={(selected) => {
            setFormData((prev: any) => ({
              ...prev,
              piercings: selected.map(option => option.value)
            }));
          }}
          placeholder="Select piercings..."
          className="flex w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          badgeClassName="bg-white hover:bg-zinc-800 dark:bg-zinc-700 text-[#121212] hover:text-white dark:text-white"
        />
      </LabelInputContainer>

      {/* Tattoos */}
      <LabelInputContainer>
        <Label htmlFor="tattoos">Tattoos</Label>
        <MultipleSelector
          options={tattooOptions}
          value={(Array.isArray(formData.tattoos) ? formData.tattoos : []).map((tattoo: any) => ({
            value: tattoo,
            label: tattooOptions.find(opt => opt.value === tattoo)?.label || tattoo
          }))}
          onChange={(selected) => {
            setFormData((prev: any) => ({
              ...prev,
              tattoos: selected.map(option => option.value)
            }));
          }}
          placeholder="Select tattoos..."
          className="flex w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          badgeClassName="bg-white hover:bg-zinc-800 dark:bg-zinc-700 text-[#121212] hover:text-white dark:text-white"
        />
      </LabelInputContainer>

      {/* Niche */}
      <LabelInputContainer>
        <Label htmlFor="niche">Niche</Label>
        <MultipleSelector
          options={nicheOptions}
          value={(Array.isArray(formData.niche) ? formData.niche : []).map((niche: any) => ({
            value: niche,
            label: nicheOptions.find(opt => opt.value === niche)?.label || niche
          }))}
          onChange={(selected) => {
            setFormData((prev: any) => ({
              ...prev,
              niche: selected.map(option => option.value)
            }));
          }}
          placeholder="Select 3 main niches that help people discover you..."
          className="flex w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          badgeClassName="bg-white hover:bg-zinc-800 dark:bg-zinc-700 text-[#121212] hover:text-white dark:text-white"
        />
      </LabelInputContainer>
    </div>
  );
};
export default PersonalInfoSection;