import { jsx as c } from "react/jsx-runtime";
import { cn as g } from "../../../lib/utils.js";
import v from "./OrderPreview.js";
import { memo as w } from "react";
import "../../../extend-tailwind-merge-e63b2b56.js";
import "../../../components/ui/progress.js";
import "../../../index-1c873780.js";
import "../../../index-563d1ed8.js";
import "../../../index-c8f2666b.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../lib.js";
import "../../../store/useStore.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../tokens.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
import "../../../utils/formatNumber.js";
import "../../../components/ui/tooltipDialog.js";
import "../../../hooks/useScreen.js";
import "../../../components/ui/dialog.js";
import "../../../index-840f2930.js";
import "../../../index-c7156e07.js";
import "../../../index-4914f99c.js";
import "../../../index-67500cd3.js";
import "../../../index-27cadef5.js";
import "../../../index-5116e957.js";
import "../../../components/ui/tooltip.js";
import "../../../index-0ce202b9.js";
import "../../../index-bcfeaad9.js";
import "../../../index-f7426637.js";
import "../../../assets/svg/IconSquareInfo.js";
import "../../../x-9e07c78a.js";
import "../../../createLucideIcon-7a477fa6.js";
const _ = ({
  dexes: s,
  dexSplits: o,
  possibleSplits: h
}) => {
  const u = o.reduce((r, t) => r + t.amount_in, 0);
  return /* @__PURE__ */ c(
    "div",
    {
      className: g(
        "dhs-grid dhs-grid-cols-2 dhs-gap-6 dhs-gap-y-2 dhs-leading-normal dhs-font-proximaMedium dhs-h-[185px] dhs-overflow-y-auto"
      ),
      children: Object.entries(s).sort((r, t) => {
        var p, d;
        const i = (p = r[1]) != null && p.image ? 1 : 0, n = (d = t[1]) != null && d.image ? 1 : 0;
        if (i === n) {
          if (!i) {
            const a = o == null ? void 0 : o.find(
              (m) => (m == null ? void 0 : m.dex) === r[0]
            ), e = o == null ? void 0 : o.find(
              (m) => (m == null ? void 0 : m.dex) === t[0]
            ), f = a && (a == null ? void 0 : a.amount_in) > 0 ? 1 : 0;
            return (e && (e == null ? void 0 : e.amount_in) > 0 ? 1 : 0) - f;
          }
          return 0;
        }
        return n - i;
      }).slice(0, 12).map(([r, t], i) => {
        const n = o.find(
          (p) => p.dex === r
        );
        return /* @__PURE__ */ c(
          "div",
          {
            className: "dhs-col-span-1 dhs-flex dhs-flex-col dhs-gap-3.5 dhs-text-sm",
            children: /* @__PURE__ */ c(
              v,
              {
                split: n,
                dex: t,
                totalAmount: u,
                possibleSplit: h[r],
                side: i % 2 === 0 ? "left" : "right"
              }
            )
          },
          r
        );
      })
    }
  );
}, ao = w(_);
export {
  ao as default
};
