"use client";

import "./datefield-rac.css";
import { cn } from "@/lib/utils";
import {
  DateFieldProps,
  DateField as DateFieldRac,
  DateInputProps as DateInputPropsRac,
  DateInput as DateInputRac,
  DateSegmentProps,
  DateSegment as DateSegmentRac,
  DateValue as <PERSON><PERSON><PERSON>ueR<PERSON>,
  TimeFieldProps,
  TimeField as TimeFieldRac,
  TimeValue as TimeValueRac,
  composeRenderProps,
} from "react-aria-components";

// Wrapper component to scope styles
const DateFieldWrapper = ({ children, className }: { children: React.ReactNode; className?: string }) => (
  <div className={cn("react-aria-datefield", className)}>
    {children}
  </div>
);

const DateField = <T extends DateValueRac>({
  className,
  children,
  ...props
}: DateFieldProps<T>) => {
  return (
    <DateFieldWrapper className={typeof className === "string" ? className : undefined}>
      <DateFieldRac
        className={composeRenderProps(className, (className) =>
          cn("space-y-2 bg-background text-foreground", className)
        )}
        {...props}
      >
        {children}
      </DateFieldRac>
    </DateFieldWrapper>
  );
};

const TimeField = <T extends TimeValueRac>({
  className,
  children,
  ...props
}: TimeFieldProps<T>) => {
  return (
    <DateFieldWrapper className={typeof className === "string" ? className : undefined}>
      <TimeFieldRac
        className={composeRenderProps(className, (className) =>
          cn("space-y-2 bg-background text-foreground", className)
        )}
        {...props}
      >
        {children}
      </TimeFieldRac>
    </DateFieldWrapper>
  );
};

const DateSegment = ({ className, ...props }: DateSegmentProps) => {
  return (
    <DateSegmentRac
      className={composeRenderProps(className, (className) =>
        cn(
          "inline rounded p-0.5 text-foreground caret-transparent outline-none",
          "data-[disabled]:cursor-not-allowed data-[disabled]:opacity-50",
          "data-[focused]:bg-accent data-[focused]:text-accent-foreground",
          "data-[invalid]:data-[focused]:bg-destructive data-[invalid]:data-[focused]:text-destructive-foreground",
          "data-[type=literal]:px-0 data-[type=literal]:text-muted-foreground/70",
          "data-[placeholder]:text-muted-foreground/70",
          "data-[invalid]:text-destructive data-[invalid]:data-[placeholder]:text-destructive",
          className
        )
      )}
      {...props}
    />
  );
};

const dateInputStyle =
  "relative inline-flex h-9 w-full items-center overflow-hidden whitespace-nowrap rounded-lg border border-input bg-background px-3 py-2 text-sm shadow-sm shadow-black/5 transition-shadow data-[focus-within]:border-ring data-[disabled]:opacity-50 data-[focus-within]:outline-none data-[focus-within]:ring-[3px] data-[focus-within]:ring-ring/20";

interface DateInputProps extends DateInputPropsRac {
  className?: string;
  unstyled?: boolean;
}

const DateInput = ({ className, unstyled = false, ...props }: Omit<DateInputProps, "children">) => {
  return (
    <DateInputRac
      className={composeRenderProps(className, (className) =>
        cn(!unstyled && dateInputStyle, "bg-background text-foreground", className)
      )}
      {...props}
    >
      {(segment) => <DateSegment segment={segment} />}
    </DateInputRac>
  );
};

export { DateField, DateInput, DateSegment, TimeField, dateInputStyle };
export type { DateInputProps };