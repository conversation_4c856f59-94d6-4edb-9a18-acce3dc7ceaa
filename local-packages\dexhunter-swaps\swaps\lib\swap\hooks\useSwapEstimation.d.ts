declare const useEstimateSwap: () => {
    estimateSwap: ({ signal, newTokenSell, newTokenBuy }: {
        signal?: AbortSignal | undefined;
        newTokenSell?: any;
        newTokenBuy?: any;
    }) => Promise<boolean>;
    estimatePrice: ({ signal, newTokenSell, newTokenBuy }: {
        signal?: AbortSignal | undefined;
        newTokenSell?: any;
        newTokenBuy?: any;
    }) => Promise<boolean>;
};
export { useEstimateSwap };
