import { jsx as t, jsxs as d } from "react/jsx-runtime";
import { cn as r } from "../../lib/utils.js";
import { useMemo as a } from "react";
import { middlen as o, hexTo<PERSON>cii as m } from "../../utils/cardanoUtils.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../index-ca8eb9e1.js";
import "../../config/axios.js";
import "../../axios-ddd885c5.js";
const v = ({
  token: s,
  isVerified: i,
  verifiedSize: e = 15
}) => {
  const h = s == null ? void 0 : s.is_snek_fun;
  return a(() => {
    if (s != null && s.isBoosted)
      return /* @__PURE__ */ t(
        "img",
        {
          src: "https://storage.googleapis.com/dexhunter-images/public/boost.svg",
          className: "dhs-absolute dhs-bottom-[-2.5px] dhs-right-[-2.5px] dhs-z-2 dhs-sm:bottom-[-1px] dhs-sm:right-[-1px] dhs-bg-gray-106 dhs-rounded-full",
          alt: "",
          width: e,
          height: e
        }
      );
    if (h)
      return /* @__PURE__ */ t(
        "img",
        {
          src: "https://storage.googleapis.com/dexhunter-images/public/snekfunverified.svg",
          className: "dhs-absolute dhs-bottom-[-2.5px] dhs-right-[-2.5px] dhs-z-2 dhs-sm:bottom-[-1px] dhs-sm:right-[-1px]",
          width: e,
          height: e,
          alt: ""
        }
      );
    if (i)
      return /* @__PURE__ */ t(
        "img",
        {
          src: "https://storage.googleapis.com/dexhunter-images/public/verified.svg",
          className: "dhs-absolute dhs-bottom-[-2.5px] dhs-right-[-2.5px] dhs-z-2 dhs-sm:bottom-[-1px] dhs-sm:right-[-1px]",
          width: e,
          height: e,
          alt: ""
        }
      );
  }, [e, s, h, i]);
}, w = ({ tokenId: s, token: i, isTokenVerified: e }) => /* @__PURE__ */ d("div", { className: "dhs-flex dhs-flex-col dhs-gap-1.5 dhs-text-gray-103 dhs-text-sm dhs-p-2", children: [
  /* @__PURE__ */ d("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
    /* @__PURE__ */ t("div", { children: "Token Policy" }),
    /* @__PURE__ */ t(
      "div",
      {
        className: "dhs-break-all dhs-text-accent dhs-hover:opacity-90 dhs-cursor-pointer",
        onClick: () => window.open(`https://cardanoscan.io/token/${s}`),
        children: o(s == null ? void 0 : s.slice(0, 56), 10)
      }
    )
  ] }),
  /* @__PURE__ */ d("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
    /* @__PURE__ */ t("div", { children: "Token Name" }),
    /* @__PURE__ */ t("div", { className: "dhs-text-white", children: (i == null ? void 0 : i.token_ascii) || m(s == null ? void 0 : s.slice(56)) })
  ] }),
  /* @__PURE__ */ t("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: /* @__PURE__ */ t("div", { className: "dhs-text-white", children: e ? "Verified tokens are searchable by name, it's not an endorsement of the project. Fire icon means recently verified." : /* @__PURE__ */ t(
    "span",
    {
      className: "dhs-text-accent dhs-cursor-pointer dhs-hover:opacity-90 dhs-font-proximaSemiBold",
      onClick: () => window.open("https://tally.so/r/wvLPoA"),
      children: "Apply for Verification"
    }
  ) }) })
] }), y = ({ verifiedElement: s, tokenId: i, className: e, size: h }) => /* @__PURE__ */ d(
  "div",
  {
    className: r(
      e,
      "dhs-relative dhs-flex dhs-justify-center dhs-items-center dhs-rounded-full"
    ),
    style: {
      minHeight: h,
      minWidth: h,
      maxHeight: h,
      maxWidth: h
    },
    children: [
      /* @__PURE__ */ t(
        "img",
        {
          src: "https://storage.googleapis.com/dexhunter-images/tokens/cardano.png",
          alt: i,
          width: h,
          height: h,
          className: "dhs-rounded-full"
        }
      ),
      s
    ]
  }
);
export {
  y as CardanoImage,
  v as TokenImageBadge,
  w as TokenImageTooltip
};
