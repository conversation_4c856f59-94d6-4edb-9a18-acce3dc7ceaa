import { p as a } from "../immer-548168ec.js";
const d = (t) => ({
  poolInfo: null,
  symbolInfo: null,
  isLoadingData: !0,
  isLoadingWidget: !0,
  selectedLiquidityDexName: "",
  setPoolInfo: (e) => {
    t(
      a((i) => {
        i.chartSlice.poolInfo = e;
      })
    );
  },
  setIsLoadingData: (e) => {
    t(
      a((i) => {
        i.chartSlice.isLoadingData = e;
      })
    );
  },
  setSelectedLiquidityDexName: (e) => {
    t(
      a((i) => {
        i.chartSlice.selectedLiquidityDexName = e;
      })
    );
  },
  setIsLoadingWidget: (e) => {
    t(
      a((i) => {
        i.chartSlice.isLoadingWidget = e;
      })
    );
  }
});
export {
  d as default
};
