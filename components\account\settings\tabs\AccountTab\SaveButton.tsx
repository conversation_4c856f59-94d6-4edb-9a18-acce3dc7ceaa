import React from 'react';
import StyledButton from '@/components/ui/styled-button';

interface SaveButtonProps {
  isLoading: boolean;
  handleSave: () => void;
  disabled: boolean;
}

const SaveButton: React.FC<SaveButtonProps> = ({ isLoading, handleSave, disabled }) => {
  return (
    <div className="flex justify-start mt-6">
      <StyledButton
        onClick={handleSave}
        disabled={isLoading || disabled}
        className={`!text-sm w-40 h-10 flex items-center justify-center !p-0 ${isLoading ? 'hover' : ''}`}
        buttonText={isLoading ? 'Saving...' : 'Save Changes'}
      />
    </div>
  );
};

export default SaveButton;