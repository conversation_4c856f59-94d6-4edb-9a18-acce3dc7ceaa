import React from 'react';
import { LabelInputContainer } from '@/components/ui/label-input-container';
import { Label } from '@/components/ui/label';

interface SecuritySectionProps {
  userData: any;
  passwordData: { currentPassword: string; newPassword: string; confirmPassword: string };
  handlePasswordChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleUpdatePassword: () => void;
  isChangingPassword: boolean;
  setIsChangingPassword: React.Dispatch<React.SetStateAction<boolean>>;
  isUpdatingPassword: boolean;
  showPassword: boolean;
  setShowPassword: React.Dispatch<React.SetStateAction<boolean>>;
  passwordStrength: number;
}

const SecuritySection: React.FC<SecuritySectionProps> = ({
  userData,
  passwordData,
  handlePasswordChange,
  handleUpdatePassword,
  isChangingPassword,
  setIsChangingPassword,
  isUpdatingPassword,
  showPassword,
  setShowPassword,
  passwordStrength,
}) => {
  const hasExistingPassword = Boolean(userData?.password?.length);

  const getPasswordStrengthInfo = () => {
    const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const colors = ['#ff4d4f', '#faad14', '#faad14', '#52c41a', '#52c41a'];
    return { label: labels[passwordStrength], color: colors[passwordStrength] };
  };

  return (
    <div className="space-y-6">
      <LabelInputContainer className="mb-6 !mt-10">
        <h3 className="text-xl font-semibold text-[#121212] dark:text-white mb-1">Security</h3>
        <div className="mb-6 border border-solid border-b border-l-0 border-r-0 border-t-0 border-black/60 dark:border-white/60"></div>
      </LabelInputContainer>

      <LabelInputContainer>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Label htmlFor="password">Password</Label>
            <button
              type="button"
              onClick={() => setIsChangingPassword(!isChangingPassword)}
              className={`h-10 px-5 rounded-lg flex items-center justify-center ${isChangingPassword ? '!bg-red-500 hover:!bg-red-600' : '!bg-[var(--turquoise)] hover:!bg-[var(--turquoise)]'} text-white`}
            >
              {isChangingPassword ? 'Cancel' : hasExistingPassword ? 'Change Password' : 'Set Password'}
            </button>
          </div>

          {isChangingPassword && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="newPassword"
                    placeholder="New Password"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" className="w-5 h-5 fill-[#121212]/60 dark:fill-white/60">
                        <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" className="w-5 h-5 fill-[#121212]/60 dark:fill-white/60">
                        <path d="M288 32c-80.8 0-145.5 36.8-192.6 80.6C48.6 156 17.3 208 2.5 243.7c-3.3 7.9-3.3 16.7 0 24.6C17.3 304 48.6 356 95.4 399.4C142.5 443.2 207.2 480 288 480s145.5-36.8 192.6-80.6c46.8-43.5 78.1-95.4 93-131.1c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C433.5 68.8 368.8 32 288 32zM144 256a144 144 0 1 1 288 0 144 144 0 1 1 -288 0zm144-64c0 35.3-28.7 64-64 64c-7.1 0-13.9-1.2-20.3-3.3c-5.5-1.8-11.9 1.6-11.7 7.4c.3 6.9 1.3 13.8 3.2 20.7c13.7 51.2 66.4 81.6 117.6 67.9s81.6-66.4 67.9-117.6c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3z" />
                      </svg>
                    )}
                  </button>
                </div>
                {passwordData.newPassword && (
                  <div className="space-y-1">
                    <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full transition-all duration-300"
                        style={{
                          width: `${(passwordStrength + 1) * 20}%`,
                          backgroundColor: getPasswordStrengthInfo().color,
                        }}
                      />
                    </div>
                    <p className="text-sm" style={{ color: getPasswordStrengthInfo().color }}>
                      Password Strength: {getPasswordStrengthInfo().label}
                    </p>
                  </div>
                )}
              </div>
              <input
                type={showPassword ? 'text' : 'password'}
                name="confirmPassword"
                placeholder="Confirm New Password"
                value={passwordData.confirmPassword}
                onChange={handlePasswordChange}
                className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
              />
              <div className="flex justify-start">
                <button
                  type="button"
                  onClick={handleUpdatePassword}
                  disabled={
                    !passwordData.newPassword ||
                    !passwordData.confirmPassword ||
                    passwordStrength < 2 ||
                    isUpdatingPassword
                  }
                  className={`h-10 px-5 rounded-lg flex items-center justify-center text-white ${
                    passwordStrength < 2 ? '!bg-gray-400 cursor-not-allowed' : '!bg-[var(--turquoise)] hover:bg-[var(--turquoise)]'
                  }`}
                >
                  {isUpdatingPassword ? (hasExistingPassword ? 'Updating...' : 'Setting...') : hasExistingPassword ? 'Update Password' : 'Set Password'}
                </button>
              </div>
              {passwordData.newPassword && passwordStrength < 2 && (
                <p className="text-sm text-red-500">
                  Please choose a stronger password. Use a mix of letters, numbers, and symbols.
                </p>
              )}
            </div>
          )}
        </div>
      </LabelInputContainer>

      <LabelInputContainer>
        <div className="w-full flex items-center justify-between border-none bg-gray-300 dark:bg-zinc-800 shadow-input rounded-md px-3 py-4 text-md dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] transition">
          <div className="flex items-center justify-center font-bold text-[#121212] dark:text-white">Login Sessions</div>
          <button className="w-28 h-10 rounded-lg flex items-center justify-center bg-[var(--turquoise)] hover:bg-[var(--hot-pink)] text-white">View</button>
        </div>
      </LabelInputContainer>

      <LabelInputContainer>
        <div className="w-full flex items-center justify-between border-none bg-gray-300 dark:bg-zinc-800 shadow-input rounded-md px-3 py-4 text-md dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] transition">
          <div className="flex items-center justify-center font-bold text-[#121212] dark:text-white">2 Factor Authentication</div>
          <button className="w-28 h-10 rounded-lg flex items-center justify-center bg-[var(--turquoise)] hover:bg-[var(--hot-pink)] text-white">Edit</button>
        </div>
      </LabelInputContainer>

      <LabelInputContainer>
        <div className="w-full flex items-center justify-between border-none bg-gray-300 dark:bg-zinc-800 shadow-input rounded-md px-3 py-4 text-md dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] transition">
          <div className="flex items-center justify-center font-bold text-[#121212] dark:text-white">Passwordless Sign In</div>
          <button className="w-28 h-10 rounded-lg flex items-center justify-center bg-[var(--turquoise)] hover:bg-[var(--hot-pink)] text-white">Edit</button>
        </div>
      </LabelInputContainer>
    </div>
  );
};
export default SecuritySection;