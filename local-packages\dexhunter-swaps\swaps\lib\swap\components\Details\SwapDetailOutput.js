import { jsxs as o, jsx as e } from "react/jsx-runtime";
import { formatBalance as u } from "../../../utils/formatNumber.js";
import { cn as n } from "../../../lib.js";
import f from "../../../components/common/TokenPrice.js";
import { s as R } from "../../../shallow-27fd7e97.js";
import d from "../../../store/useStore.js";
import r from "../../../components/ui/tooltipDialog.js";
import T from "../../../assets/svg/IconTwoWay.js";
import "../../../extend-tailwind-merge-e63b2b56.js";
import "../../../utils/formatToken.js";
import "react";
import "../../../lib/utils.js";
import "../../../trends/components/PriceFormatter.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../tokens.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
import "../../../hooks/useScreen.js";
import "../../../components/ui/dialog.js";
import "../../../index-840f2930.js";
import "../../../index-1c873780.js";
import "../../../index-c7156e07.js";
import "../../../index-563d1ed8.js";
import "../../../index-4914f99c.js";
import "../../../index-67500cd3.js";
import "../../../index-c8f2666b.js";
import "../../../index-27cadef5.js";
import "../../../index-5116e957.js";
import "../../../components/ui/tooltip.js";
import "../../../index-0ce202b9.js";
import "../../../index-bcfeaad9.js";
import "../../../index-f7426637.js";
const rt = () => {
  const { swapDetails: t, tokenBuy: i, tokenSell: m, inputMode: a } = d(
    (s) => ({
      swapDetails: s.swapSlice.swapDetails,
      tokenBuy: s.swapSlice.tokenBuy,
      tokenSell: s.swapSlice.tokenSell,
      bonusOutput: s.swapSlice.bonusOutput,
      inputMode: s.swapSlice.inputMode
    }),
    R
  ), { swapType: h } = d((s) => s.tokenSearchSlice), { slippage: l } = d((s) => s.swapSettingsSlice), { isPricesFlipped: c, setIsPricesFlipped: x } = d(
    (s) => s.globalSettingsSlice
  ), p = t.splits.length > 1, g = a === "SELL" ? t.net_price : (
    // [NOTE]: temporary fix, need backend fix
    ((t == null ? void 0 : t.total_input) - (t == null ? void 0 : t.total_input) * (l / 100) - (t == null ? void 0 : t.batcher_fee) - (t == null ? void 0 : t.partner_fee)) / (t == null ? void 0 : t.total_output)
  );
  return /* @__PURE__ */ o(
    "div",
    {
      className: n(
        "dhs-grid @md/appRoot:dhs-grid-cols-3 dhs-grid-cols-[1fr_110px] @md/appRoot:dhs-gap-2.5 dhs-gap-x-2.5 dhs-leading-normal dhs-font-proximaMedium"
      ),
      children: [
        /* @__PURE__ */ o("div", { className: "dhs-col-span-1 dhs-flex dhs-flex-col dhs-items-start sm:dhs-gap-[2px]", children: [
          /* @__PURE__ */ e(
            r,
            {
              trigger: /* @__PURE__ */ e("span", { className: "dhs-text-xs @sm/appRoot:dhs-text-sm dhs-leading-none dhs-text-subText dhs-font-semibold sm:dhs-font-proximaRegular", children: "Swap Route" }),
              content: `The aggregator bonus is the extra amount of tokens you get when using an aggregator
        instead of a single dex.`,
              contentClass: "dhs-text-mainText"
            }
          ),
          /* @__PURE__ */ o(
            "div",
            {
              className: n(
                "dhs-flex dhs-items-center dhs-gap-[5px]",
                p ? "dhs-text-success" : "dhs-text-mainText"
              ),
              children: [
                /* @__PURE__ */ e(T, { width: 12, height: 12 }),
                p ? /* @__PURE__ */ o("span", { className: "dhs-text-xs @sm/appRoot:dhs-text-sm @md/appRoot:dhs-text-md dhs-text-success dhs-font-bold", children: [
                  t.splits.length,
                  " ROUTES"
                ] }) : /* @__PURE__ */ e("span", { className: "dhs-text-xs @sm/appRoot:dhs-text-sm @md/appRoot:dhs-text-md dhs-text-mainText sm:dhs-text-sm dhs-leading-none", children: "DIRECT" })
              ]
            }
          )
        ] }),
        /* @__PURE__ */ o("div", { className: "dhs-col-span-1 sm:dhs-order-3 dhs-flex dhs-flex-col dhs-items-start sm:dhs-gap-[2px]", children: [
          /* @__PURE__ */ e(
            r,
            {
              trigger: /* @__PURE__ */ e("span", { className: "dhs-text-xs @sm/appRoot:dhs-text-sm dhs-leading-none dhs-text-subText dhs-font-semibold sm:dhs-font-proximaRegular", children: "Net price" }),
              content: "The net price is the average price you get for your tokens after the fees.",
              contentClass: "dhs-text-mainText"
            }
          ),
          /* @__PURE__ */ e("span", { className: "dhs-text-xs @sm/appRoot:dhs-text-sm @md/appRoot:dhs-text-md dhs-text-mainText sm:dhs-text-sm dhs-leading-none", children: /* @__PURE__ */ e(
            f,
            {
              tokenPrice: g,
              tokenBuyTicker: i == null ? void 0 : i.ticker,
              tokenSellTicker: m == null ? void 0 : m.ticker,
              isReverse: h === "BUY",
              onClick: () => x(!c)
            }
          ) })
        ] }),
        /* @__PURE__ */ o("div", { className: "dhs-col-span-1 sm:dhs-order-5 dhs-flex dhs-flex-col dhs-items-start sm:dhs-gap-[2px]", children: [
          /* @__PURE__ */ e(
            r,
            {
              trigger: /* @__PURE__ */ e("span", { className: "dhs-text-xs @sm/appRoot:dhs-text-sm dhs-leading-none dhs-text-subText dhs-font-semibold sm:dhs-font-proximaRegular", children: "Min. receive" }),
              content: "The minimum amount of tokens you will receive after the fees.",
              contentClass: "dhs-text-mainTex dhs-w-[250px]",
              side: "bottom"
            }
          ),
          /* @__PURE__ */ o("span", { className: "dhs-text-xs @sm/appRoot:dhs-text-sm @md/appRoot:dhs-text-md dhs-text-mainText sm:dhs-text-sm dhs-leading-none", children: [
            u(t.total_output),
            " ",
            i == null ? void 0 : i.ticker
          ] })
        ] })
      ]
    }
  );
};
export {
  rt as default
};
