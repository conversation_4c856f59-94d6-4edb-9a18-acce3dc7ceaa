'use client';

import { Profile } from '@/types/user';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/chat/ui/avatar';
import { Button } from '@/components/ui/button';
import { CheckCircle2 } from 'lucide-react';
import Link from 'next/link';

interface CreatorCardProps {
  profile: Profile;
}

export default function CreatorCard({ profile }: CreatorCardProps) {
  return (
    <Link href={`/profile/${profile.id}`}>
      <div className="bg-transparent overflow-hidden group cursor-pointer p-1 border border-solid border-gray-300 dark:border-white rounded-lg hover:shadow-lg transition-shadow">
        {/* Cover Image */}
        <div className="relative aspect-[3/2] rounded-lg overflow-hidden">
          <img
            src={profile.coverBanner || '/default-cover.jpg'}
            alt={`${profile.name}'s cover`}
            className="w-full h-full object-cover"
          />
          {/* Online Status */}
          {profile.status == 'online' && (
            <div className="absolute top-2 right-2 bg-green-500 h-3 w-3 rounded-full border-2 border-white"></div>
          )}
        </div>

        {/* Creator Info */}
        <div className="px-0 py-1.5 flex items-center justify-start gap-1.5">
          <Avatar className="h-9 w-9">
            <AvatarImage src={profile.avatar || '/images/user/default-avatar.webp'} />
            <AvatarFallback>{profile.name?.charAt(0) || 'U'}</AvatarFallback>
          </Avatar>
          <div className="flex items-center justify-start flex-col w-full">
            <div className="flex items-center w-full">
              <h3 className="font-semibold text-sm line-clamp-1 text-gray-600 dark:text-white text-ellipsis overflow-hidden">
                {profile.name || 'Anonymous'}
              </h3>
              {profile.isVerified && (
                <CheckCircle2 className="ml-1 h-4 w-4 text-blue-500" />
              )}
            </div>
            <p className="text-xs text-gorilla-gray w-full">@{profile.username}</p>
          </div>
        </div>

        {/* Stats */}
        <div className="px-0 pb-3 pt-1 flex items-center justify-between gap-2 border border-solid border-l-0 border-r-0 border-t-0 border-gray-200 dark:border-white mb-2">
          <div className="text-xs text-gray-600 dark:text-gray-300">
            <span>{profile.stats.followers || 0} Followers</span>
            <span className="mx-2">•</span>
            <span>{profile.stats.posts || 0} Posts</span>
          </div>
        </div>

        {/* Action Button */}
        <div className="px-0 pb-3 pt-1 flex items-center justify-center">
          <Button
            className="w-28 h-8 bg-turquoise rounded-lg text-sm hover:bg-cyan-600 transition-colors text-white"
          >
            View Profile
          </Button>
        </div>
      </div>
    </Link>
  );
};