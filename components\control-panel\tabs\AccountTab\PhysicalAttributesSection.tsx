import React from 'react';
import { LabelInputContainer } from '@/components/ui/label-input-container';
import { Label } from '@/components/ui/label';
import { SettingsSection } from '@/components/ui/settings-section';
import { bodyTypeOptions, eyeColorOptions, hairColorOptions, breastSizeOptions, breastTypeOptions, heightOptions } from '@/app/account/settings/constants';
import { AccountFormData } from '@/components/account/settings/tabs/AccountTab/AccountTab';
import { heightToCentimeters } from '@/lib/utils';

// Generate weight options with minimum and maximum bounds
const generateWeightOptions = () => {
  const options = [];

  // Add "< 50 lbs" option
  const minKg = (50 * 0.453592).toFixed(1); // 22.7 kg
  options.push({
    value: '<50',
    label: `< 50 lbs (< ${minKg} kg)`,
  });

  // Generate range from 50 lbs to 300 lbs in 5-lb increments
  for (let pounds = 50; pounds <= 300; pounds += 5) {
    const kilograms = (pounds * 0.453592).toFixed(1); // Convert to kg and round to 1 decimal
    options.push({
      value: pounds.toString(),
      label: `${pounds} lbs (${kilograms} kg)`,
    });
  }

  // Add "> 300 lbs" option
  const maxKg = (300 * 0.453592).toFixed(1); // 136.1 kg
  options.push({
    value: '>300',
    label: `> 300 lbs (> ${maxKg} kg)`,
  });

  return options;
};

const weightOptions = generateWeightOptions();

interface PhysicalAttributesSectionProps {
  formData: AccountFormData;
  setFormData: React.Dispatch<React.SetStateAction<AccountFormData>>;
  handlePhysicalAttributeChange: (field: keyof AccountFormData['physicalAttributes'], subField: string, value: string | number) => void;
}

const PhysicalAttributesSection: React.FC<PhysicalAttributesSectionProps> = ({
  formData,
  setFormData,
}) => {
  const handleEyeColorChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      physicalAttributes: { ...prev.physicalAttributes, eyeColor: value },
    }));
  };

  const handleHairColorChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      physicalAttributes: { ...prev.physicalAttributes, hairColor: value },
    }));
  };

  const handleBodyTypeChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      physicalAttributes: { ...prev.physicalAttributes, bodyType: value },
    }));
  };

  const handleMixedEyeColorChange = (eye: 'left' | 'right', value: string) => {
    setFormData(prev => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        mixedEyeColors: { ...prev.physicalAttributes.mixedEyeColors, [eye]: value },
      },
    }));
  };

  const handleWeightChange = (value: string) => {
    if (value === '<50') {
      setFormData(prev => ({
        ...prev,
        physicalAttributes: {
          ...prev.physicalAttributes,
          weight: {
            pounds: '<50',
            kilograms: '<22.7',
          },
        },
      }));
    } else if (value === '>300') {
      setFormData(prev => ({
        ...prev,
        physicalAttributes: {
          ...prev.physicalAttributes,
          weight: {
            pounds: '>300',
            kilograms: '>136.1',
          },
        },
      }));
    } else {
      const pounds = parseInt(value, 10);
      const kilograms = (pounds * 0.453592).toFixed(1);
      setFormData(prev => ({
        ...prev,
        physicalAttributes: {
          ...prev.physicalAttributes,
          weight: {
            pounds: pounds.toString(),
            kilograms: kilograms.toString(),
          },
        },
      }));
    }
  };

  const handleHeightChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        height: value,
        heightCentimeters: heightToCentimeters(value),
      },
    }));
  };

  // Local handlers for input fields
  const handleCupSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        cupSize: { value: e.target.value },
      },
    }));
  };

  const handleBustSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        bustSize: { value: e.target.value },
      },
    }));
  };

  const handleBraSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        braSize: { value: e.target.value },
      },
    }));
  };

  const handleWaistChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        waist: { value: e.target.value },
      },
    }));
  };

  const handleHipsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        hips: { value: e.target.value },
      },
    }));
  };

  const handleButtChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        butt: { value: e.target.value },
      },
    }));
  };

  const handleShoeSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      physicalAttributes: {
        ...prev.physicalAttributes,
        shoeSize: { value: e.target.value },
      },
    }));
  };

  const physicalAttributesSettings = [
    {
      id: 'height',
      label: 'Height',
      description: 'Select your height',
      type: 'select' as const,
      value: formData.physicalAttributes.height,
      options: heightOptions,
      onChange: handleHeightChange,
    },
    {
      id: 'weight',
      label: 'Weight',
      description: 'Select your weight',
      type: 'select' as const,
      value: formData.physicalAttributes.weight.pounds,
      options: weightOptions,
      onChange: handleWeightChange,
    },
    {
      id: 'eyeColor',
      label: 'Eye Color',
      description: 'Select your eye color',
      type: 'select' as const,
      value: formData.physicalAttributes.eyeColor,
      options: eyeColorOptions,
      onChange: handleEyeColorChange,
    },
    {
      id: 'hairColor',
      label: 'Hair Color',
      description: 'Select your hair color',
      type: 'select' as const,
      value: formData.physicalAttributes.hairColor,
      options: hairColorOptions,
      onChange: handleHairColorChange,
    },
    {
      id: 'bodyType',
      label: 'Body Type',
      description: 'Select your body type',
      type: 'select' as const,
      value: formData.physicalAttributes.bodyType,
      options: bodyTypeOptions,
      onChange: handleBodyTypeChange,
    },
    {
      id: 'breastSize',
      label: 'Breast Size',
      description: 'Select your breast size',
      type: 'select' as const,
      value: formData.physicalAttributes.breastSize,
      options: breastSizeOptions,
      onChange: (value: string) =>
        setFormData((prev: any) => ({
          ...prev,
          physicalAttributes: { ...prev.physicalAttributes, breastSize: value },
        })),
    },
    {
      id: 'breastType',
      label: 'Breast Type',
      description: 'Select your breast type',
      type: 'select' as const,
      value: formData.physicalAttributes.breastType,
      options: breastTypeOptions,
      onChange: (value: string) =>
        setFormData((prev: any) => ({
          ...prev,
          physicalAttributes: { ...prev.physicalAttributes, breastType: value },
        })),
    },
  ];

  const mixedEyeColorOptions = [
    {
      id: 'leftEye',
      label: 'Left Eye',
      description: 'Select your left eye color',
      type: 'select' as const,
      value: formData.physicalAttributes.mixedEyeColors?.left,
      options: eyeColorOptions.filter(opt => opt.value !== 'mixed'),
      onChange: (value: string) => handleMixedEyeColorChange('left', value),
    },
    {
      id: 'rightEye',
      label: 'Right Eye',
      description: 'Select your right eye color',
      type: 'select' as const,
      value: formData.physicalAttributes.mixedEyeColors?.right,
      options: eyeColorOptions.filter(opt => opt.value !== 'mixed'),
      onChange: (value: string) => handleMixedEyeColorChange('right', value),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Physical Attributes Section */}
      <LabelInputContainer className="mb-6 mt-10">
        <h3 className="text-xl font-semibold text-[#121212] dark:text-white mb-1">Physical Attributes</h3>
        <div className="mb-6 border border-solid border-b border-l-0 border-r-0 border-t-0 border-black/60 dark:border-white/60"></div>
      </LabelInputContainer>

      <div className="space-y-4">
        {/* Cup Size */}
        <LabelInputContainer>
          <Label htmlFor="cupSize">Cup Size</Label>
          <input
            type="text"
            id="cupSize"
            name="cupSize"
            value={formData.physicalAttributes.cupSize || ''}
            onChange={handleCupSizeChange} // Use local handler
            placeholder="e.g., D"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
        </LabelInputContainer>

        {/* Bust Size */}
        <LabelInputContainer>
          <Label htmlFor="bustSize">Bust Size</Label>
          <input
            type="text"
            id="bustSize"
            name="bustSize"
            value={formData.physicalAttributes.bustSize || ''}
            onChange={handleBustSizeChange} // Use local handler
            placeholder="e.g., 34 inches"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
        </LabelInputContainer>

        {/* Bra Size */}
        <LabelInputContainer>
          <Label htmlFor="braSize">Bra Size</Label>
          <input
            type="text"
            id="braSize"
            name="braSize"
            value={formData.physicalAttributes.braSize || ''}
            onChange={handleBraSizeChange} // Use local handler
            placeholder="e.g., 34D"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
        </LabelInputContainer>

        {/* Waist */}
        <LabelInputContainer>
          <Label htmlFor="waist">Waist</Label>
          <input
            type="text"
            id="waist"
            name="waist"
            value={formData.physicalAttributes.waist || ''}
            onChange={handleWaistChange} // Use local handler
            placeholder="e.g., 26 inches"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
        </LabelInputContainer>

        {/* Hips */}
        <LabelInputContainer>
          <Label htmlFor="hips">Hips</Label>
          <input
            type="text"
            id="hips"
            name="hips"
            value={formData.physicalAttributes.hips || ''}
            onChange={handleHipsChange} // Use local handler
            placeholder="e.g., 36 inches"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
        </LabelInputContainer>

        {/* Butt */}
        <LabelInputContainer>
          <Label htmlFor="butt">Butt</Label>
          <input
            type="text"
            id="butt"
            name="butt"
            value={formData.physicalAttributes.butt || ''}
            onChange={handleButtChange} // Use local handler
            placeholder="e.g., 40 inches"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
        </LabelInputContainer>

        {/* Shoe Size */}
        <LabelInputContainer>
          <Label htmlFor="shoeSize">Shoe Size</Label>
          <input
            type="text"
            id="shoeSize"
            name="shoeSize"
            value={formData.physicalAttributes.shoeSize || ''}
            onChange={handleShoeSizeChange} // Use local handler
            placeholder="e.g., US 7"
            className="flex h-10 w-full border-none bg-gray-300 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
        </LabelInputContainer>
      </div>

      <SettingsSection options={physicalAttributesSettings} />

      {formData.physicalAttributes.eyeColor === 'mixed' && (
        <SettingsSection options={mixedEyeColorOptions} />
      )}
    </div>
  );
};
export default PhysicalAttributesSection;