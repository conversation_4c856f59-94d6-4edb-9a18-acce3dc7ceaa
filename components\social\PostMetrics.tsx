import React from 'react';

interface PostMetricsProps {
  likes: number;
  shares: number;
  saves: number;
  comments: number;
}

const PostMetrics: React.FC<PostMetricsProps> = ({ likes, shares, saves, comments }: any) => {
  return (
    <div className="flex space-x-4">
      <div className="flex items-center">
        <span className="font-bold">{likes}</span> Likes
      </div>
      <div className="flex items-center">
        <span className="font-bold">{shares}</span> Shares
      </div>
      <div className="flex items-center">
        <span className="font-bold">{saves}</span> Saves
      </div>
      <div className="flex items-center">
        <span className="font-bold">{comments}</span> Comments
      </div>
    </div>
  );
};
export default PostMetrics;