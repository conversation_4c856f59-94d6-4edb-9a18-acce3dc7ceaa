import UserProfileClient from '@/components/user/UserProfileClient';
import { fetchQuery } from 'convex/nextjs';
import { api } from '@/convex/_generated/api';

export async function generateMetadata({ params }: { params: { username?: string } }) {
  const username = params.username || '';
  // Fetch just enough for meta tags
  const userData = await fetchQuery(api.accounts.getUserDetails, { user: username });
  if (!userData?.success) {
    return { title: 'User Not Found', description: 'This profile does not exist.' };
  }
  return {
    title: userData.data?.user_info?.account?.displayName || username,
    description: userData.data?.user_info?.account?.bio || `View ${username}'s profile`,
  };
}

export default function UserProfile({ params }: { params: { username?: string } }) {
  const username = params.username || '';
  // Pass only username to client
  return <UserProfileClient username={username} />;
}