# Dex Hunter - Swap Component

### How to use library (with React)

1. Install package:
    ```console
    npm i @dexhunterio/swaps
    ```
2. Import to your code
    2.1. For ReactJS:
    ```js
      import '@dexhunterio/swaps/lib/assets/style.css'
      import Swap from '@dexhunterio/swaps'
      ...
      ```
    2.2. Ffor NextJS:
      ```js
      "use client";
      import '@dexhunterio/swaps/lib/assets/style.css'
      const Swap = dynamic(() => import("@dexhunterio/swaps"), {
          ssr: false,
      });
      ...
      ```
3. Use Swap Component:
      ```js
      function App() {
        return (
          <div>
            <Swap {...settings} />
          </div>
        );
      }
      ```
### How to use library (with VanilaJS)
  - In head tag
      ```html
      <head>
          ...
          <script
              src="https://unpkg.com/react@18.2/umd/react.production.min.js"
              crossorigin
            ></script>
            <script
              src="https://unpkg.com/react-dom@18.2/umd/react-dom.production.min.js"
              crossorigin
            ></script>
            <script
              type="module"
              src="https://unpkg.com/@dexhunterio/swaps@0.0.95/lib/umd/swaps.umd.js"
            ></script>
            ...
      </head>
    ```
  - In body tag:
    ```html
      ...
      <div id="dexhunter"></div>
        <script type="module">
          ReactDOM.render(
            React.createElement(dexhunterSwap, {
              orderTypes: ["SWAP", "LIMIT", "DCA"],
              theme: "light",
            }),
            document.getElementById("dexhunter")
          );
        </script>
    ...
    ```
### Available props:

|      Prop       | Required? |        Type         |      Default      |                                                                                                 Description                                                                                                 |
| :-------------: | :-------: | :-----------------: | :---------------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|  defaultToken   |   false   |       string        |         -         |                                                                                      The default token to be selected.                                                                                      |
|      width      |   false   |        px\|%        |         -         |                                                                     The width of the component. Accepts values in pixels or percentage.                                                                     |
|     height      |   false   |        px\|%        |         -         |                                                                    The height of the component. Accepts values in pixels or percentage.                                                                     |
|      theme      |   false   |    dark \| light    |       dark        |                                                                        The theme of the component. Can be either 'dark' or 'light'.                                                                         |
|   orderTypes    |   false   |        array        | ['SWAP', 'LIMIT'] |                                                             An array of order types supported by the component. Defaults to 'SWAP' and 'LIMIT'.                                                             |
| supportedTokens |   false   |      tokenId[]      |         -         |                                                                           An array of token IDs that are supported for searching.                                                                           |
|   partnerName   |   true    |       string        |         -         |                                                                             The name of the partner. This is a required field.                                                                              |
|   partnerCode   |   true    |       string        |         -         |                                                                             The code of the partner. This is a required field.                                                                              |
|     colors      |   false   |       object        |         -         |                                                        Supported colors: `mainText`, `subText`, `background`, `containers`, `buttonText`, `accent`.                                                         |
|    className    |   false   |       string        |         -         |                                                                                     Custom CSS class for the component.                                                                                     |
|      style      |   false   |       object        |         -         |                                                                                      Inline styles for the component.                                                                                       |
|  onSwapSuccess  |   false   | (data: any) => void |         -         |                                                                                    Callback function on successful swap.                                                                                    |
|   onSwapError   |   false   | (err: any) => void  |         -         |                                                                                      Callback function on swap error.                                                                                       |
| selectedWallet  |   false   |   SelectedWallet    |         -         | The wallet selected by the user. Accepts one of the specified wallet identifiers. Supported wallets: `nami`, `eternl`, `flint`, `gerowallet`, `typhoncip30`, `nufi`, `lace`, `vespr`, `begin`, and `yoroi`. |
|     inputs      |   false   |      string[]       |         -         |                                                                                       Input fields for the component.                                                                                       |
| onWalletConnect |   false   | (data: any) => void |         -         |                                                                                Callback function when a wallet is connected.                                                                                |
| onClickWalletConnect |   false   | () => void |         -         |                                                                                Trigger when clicking the wallet connect button                                                                                |
| onViewOrder |   false   | (data: any) => void |         -         |                                                                                Trigger when clicking the view order button                                                                                |
| displayType |   false   | 'BUTTON' `|` 'DEFAULT' `|` 'WIDGET' |         'DEFAULT'         |                                                                                display type                                                                               |
| buttonText |   false   | string |         Swap         |                                                                                Button text when displayType = 'BUTTON'                                                                               |
| orderTypeOnButtonClick |   false   | 'SWAP' `|` 'LIMIT' `|` 'DCA' |         'SWAP'         |                                                                                Tab default when displayed                                                                              |
| defaultSettings |   false   | { isCustomSlippage?: boolean; isAutomaticSlippage?: boolean; slippage?: number; } |         -         |                                                                                Settings default                                                                              |
| autoFocus |   false   | boolean |         false         |                                                                                auto focus on sell input when page loads                                                                             |
