import { jsx as h } from "react/jsx-runtime";
import * as n from "react";
import { $ as i } from "../../index-1c873780.js";
import { c as a } from "../../index-1d6812f7.js";
import { cn as c } from "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const u = a(
  "dhs-inline-flex dhs-items-center dhs-justify-center dhs-rounded-md dhs-text-sm dhs-font-medium dhs-ring-offset-background dhs-transition-colors focus-visible:dhs-outline-none focus-visible:dhs-ring-2 focus-visible:dhs-ring-ring focus-visible:dhs-ring-offset-2 disabled:dhs-pointer-events-none disabled:dhs-opacity-50",
  {
    variants: {
      variant: {
        default: "dhs-bg-primary dhs-text-primary-foreground hover:dhs-bg-primary hover:dhs-bg-opacity-90",
        destructive: "dhs-bg-destructive dhs-text-destructive-foreground hover:dhs-bg-destructive hover:dhs-bg-opacity-90",
        outline: "dhs-border dhs-border-input dhs-bg-background hover:dhs-bg-accent hover:dhs-text-accent-foreground",
        secondary: "dhs-bg-secondary dhs-text-secondary-foreground hover:dhs-bg-secondary hover:dhs-bg-opacity-80",
        ghost: "hover:dhs-bg-accent hover:dhs-text-accent-foreground",
        link: "dhs-text-primary dhs-underline-offset-4 hover:dhs-underline",
        warning: "dhs-bg-warning dhs-text-warning-foreground hover:dhs-bg-warning hover:dhs-bg-opacity-90"
      },
      size: {
        default: "dhs-h-10 dhs-px-4 dhs-py-2",
        sm: "dhs-h-9 dhs-rounded-md dhs-px-3",
        lg: "dhs-h-11 dhs-rounded-md dhs-px-8",
        icon: "dhs-h-10 dhs-w-10"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
), g = n.forwardRef(
  ({ className: s, variant: e, size: d, asChild: r = !1, ...o }, t) => /* @__PURE__ */ h(
    r ? i : "button",
    {
      className: c(u({ variant: e, size: d, className: s })),
      ref: t,
      ...o
    }
  )
);
g.displayName = "Button";
export {
  g as Button,
  u as buttonVariants
};
