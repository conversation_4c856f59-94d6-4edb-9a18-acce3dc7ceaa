import { _ as l, $ as M, a as m } from "./index-1c873780.js";
import { createElement as s, Children as F, forwardRef as $, useRef as u, useEffect as T, Fragment as y, useCallback as N } from "react";
import { $ as p, a as k } from "./index-c7156e07.js";
import { $ as L } from "./index-563d1ed8.js";
import { $ as x } from "./index-4914f99c.js";
import { $ as w, a as S } from "./index-67500cd3.js";
import { $ as G, h as K, a as U, b as V } from "./index-27cadef5.js";
import { $ as C } from "./index-5116e957.js";
import { $ as g } from "./index-c8f2666b.js";
const R = "Dialog", [P, be] = L(R), [Y, i] = P(R), Z = (e) => {
  const { __scopeDialog: r, children: c, open: n, defaultOpen: t, onOpenChange: o, modal: a = !0 } = e, d = u(null), f = u(null), [_ = !1, D] = k({
    prop: n,
    defaultProp: t,
    onChange: o
  });
  return /* @__PURE__ */ s(Y, {
    scope: r,
    triggerRef: d,
    contentRef: f,
    contentId: x(),
    titleId: x(),
    descriptionId: x(),
    open: _,
    onOpenChange: D,
    onOpenToggle: N(
      () => D(
        (I) => !I
      ),
      [
        D
      ]
    ),
    modal: a
  }, c);
}, j = "DialogTrigger", q = /* @__PURE__ */ $((e, r) => {
  const { __scopeDialog: c, ...n } = e, t = i(j, c), o = m(r, t.triggerRef);
  return /* @__PURE__ */ s(g.button, l({
    type: "button",
    "aria-haspopup": "dialog",
    "aria-expanded": t.open,
    "aria-controls": t.contentId,
    "data-state": O(t.open)
  }, n, {
    ref: o,
    onClick: p(e.onClick, t.onOpenToggle)
  }));
}), h = "DialogPortal", [z, E] = P(h, {
  forceMount: void 0
}), B = (e) => {
  const { __scopeDialog: r, forceMount: c, children: n, container: t } = e, o = i(h, r);
  return /* @__PURE__ */ s(z, {
    scope: r,
    forceMount: c
  }, F.map(
    n,
    (a) => /* @__PURE__ */ s(C, {
      present: c || o.open
    }, /* @__PURE__ */ s(w, {
      asChild: !0,
      container: t
    }, a))
  ));
}, v = "DialogOverlay", H = /* @__PURE__ */ $((e, r) => {
  const c = E(v, e.__scopeDialog), { forceMount: n = c.forceMount, ...t } = e, o = i(v, e.__scopeDialog);
  return o.modal ? /* @__PURE__ */ s(C, {
    present: n || o.open
  }, /* @__PURE__ */ s(J, l({}, t, {
    ref: r
  }))) : null;
}), J = /* @__PURE__ */ $((e, r) => {
  const { __scopeDialog: c, ...n } = e, t = i(v, c);
  return (
    // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`
    // ie. when `Overlay` and `Content` are siblings
    /* @__PURE__ */ s(G, {
      as: M,
      allowPinchZoom: !0,
      shards: [
        t.contentRef
      ]
    }, /* @__PURE__ */ s(g.div, l({
      "data-state": O(t.open)
    }, n, {
      ref: r,
      style: {
        pointerEvents: "auto",
        ...n.style
      }
    })))
  );
}), b = "DialogContent", Q = /* @__PURE__ */ $((e, r) => {
  const c = E(b, e.__scopeDialog), { forceMount: n = c.forceMount, ...t } = e, o = i(b, e.__scopeDialog);
  return /* @__PURE__ */ s(C, {
    present: n || o.open
  }, o.modal ? /* @__PURE__ */ s(W, l({}, t, {
    ref: r
  })) : /* @__PURE__ */ s(X, l({}, t, {
    ref: r
  })));
}), W = /* @__PURE__ */ $((e, r) => {
  const c = i(b, e.__scopeDialog), n = u(null), t = m(r, c.contentRef, n);
  return T(() => {
    const o = n.current;
    if (o)
      return K(o);
  }, []), /* @__PURE__ */ s(A, l({}, e, {
    ref: t,
    trapFocus: c.open,
    disableOutsidePointerEvents: !0,
    onCloseAutoFocus: p(e.onCloseAutoFocus, (o) => {
      var a;
      o.preventDefault(), (a = c.triggerRef.current) === null || a === void 0 || a.focus();
    }),
    onPointerDownOutside: p(e.onPointerDownOutside, (o) => {
      const a = o.detail.originalEvent, d = a.button === 0 && a.ctrlKey === !0;
      (a.button === 2 || d) && o.preventDefault();
    }),
    onFocusOutside: p(
      e.onFocusOutside,
      (o) => o.preventDefault()
    )
  }));
}), X = /* @__PURE__ */ $((e, r) => {
  const c = i(b, e.__scopeDialog), n = u(!1), t = u(!1);
  return /* @__PURE__ */ s(A, l({}, e, {
    ref: r,
    trapFocus: !1,
    disableOutsidePointerEvents: !1,
    onCloseAutoFocus: (o) => {
      var a;
      if ((a = e.onCloseAutoFocus) === null || a === void 0 || a.call(e, o), !o.defaultPrevented) {
        var d;
        n.current || (d = c.triggerRef.current) === null || d === void 0 || d.focus(), o.preventDefault();
      }
      n.current = !1, t.current = !1;
    },
    onInteractOutside: (o) => {
      var a, d;
      (a = e.onInteractOutside) === null || a === void 0 || a.call(e, o), o.defaultPrevented || (n.current = !0, o.detail.originalEvent.type === "pointerdown" && (t.current = !0));
      const f = o.target;
      ((d = c.triggerRef.current) === null || d === void 0 ? void 0 : d.contains(f)) && o.preventDefault(), o.detail.originalEvent.type === "focusin" && t.current && o.preventDefault();
    }
  }));
}), A = /* @__PURE__ */ $((e, r) => {
  const { __scopeDialog: c, trapFocus: n, onOpenAutoFocus: t, onCloseAutoFocus: o, ...a } = e, d = i(b, c), f = u(null), _ = m(r, f);
  return U(), /* @__PURE__ */ s(y, null, /* @__PURE__ */ s(V, {
    asChild: !0,
    loop: !0,
    trapped: n,
    onMountAutoFocus: t,
    onUnmountAutoFocus: o
  }, /* @__PURE__ */ s(S, l({
    role: "dialog",
    id: d.contentId,
    "aria-describedby": d.descriptionId,
    "aria-labelledby": d.titleId,
    "data-state": O(d.open)
  }, a, {
    ref: _,
    onDismiss: () => d.onOpenChange(!1)
  }))), !1);
}), ee = "DialogTitle", oe = /* @__PURE__ */ $((e, r) => {
  const { __scopeDialog: c, ...n } = e, t = i(ee, c);
  return /* @__PURE__ */ s(g.h2, l({
    id: t.titleId
  }, n, {
    ref: r
  }));
}), te = "DialogDescription", ce = /* @__PURE__ */ $((e, r) => {
  const { __scopeDialog: c, ...n } = e, t = i(te, c);
  return /* @__PURE__ */ s(g.p, l({
    id: t.descriptionId
  }, n, {
    ref: r
  }));
}), ne = "DialogClose", re = /* @__PURE__ */ $((e, r) => {
  const { __scopeDialog: c, ...n } = e, t = i(ne, c);
  return /* @__PURE__ */ s(g.button, l({
    type: "button"
  }, n, {
    ref: r,
    onClick: p(
      e.onClick,
      () => t.onOpenChange(!1)
    )
  }));
});
function O(e) {
  return e ? "open" : "closed";
}
const ge = Z, _e = q, De = B, xe = H, ve = Q, me = oe, Ce = ce, Oe = re;
export {
  De as $,
  xe as a,
  ve as b,
  me as c,
  Ce as d,
  ge as e,
  _e as f,
  Oe as g
};
