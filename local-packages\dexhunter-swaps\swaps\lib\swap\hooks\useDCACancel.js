import w from "../../store/useStore.js";
import { server as l } from "../../config/axios.js";
import { useNotify as y } from "../../hooks/useNotify.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
import "react/jsx-runtime";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../hooks/useScreen.js";
const Z = () => {
  const { notify: i } = y(), {
    api: t,
    userAddress: c,
    selectedWallet: f
  } = w((s) => s.walletSlice), { setSelectedOrdersIds: d, setIsOrderCancelLoading: a, setCancellingOrders: m, setPendingOrdersCount: u, pendingOrdersCount: g, cancellingOrders: b } = w((s) => s.userOrdersSlice);
  return { cancelDCA: async (s) => {
    const e = {
      sign: null,
      tx: "",
      address: c,
      dca_id: s,
      err: null,
      step: "pre-cancel",
      err_message: "",
      walletName: f
    };
    a(!0);
    try {
      const { data: r } = await l.post("/dca/cancel", {
        dca_id: s,
        user_address: c
      });
      e.step = "pre-sign";
      const p = await (t == null ? void 0 : t.signTx(r.cbor, !0));
      e.step = "pre-sign-post";
      const { data: n } = await l.post("/swap/sign", {
        txCbor: r.cbor,
        signatures: p
      });
      e.sign = n, e.step = "pre-submit";
      const o = await (t == null ? void 0 : t.submitTx(n.cbor));
      return e.tx = o, i({
        type: "success",
        title: "DCA cancelled",
        desc: "DCA cancelled successfully"
      }), m([s, ...b]), u(g + 1), o;
    } catch (r) {
      if (console.log(r), e.err = r, e.err_message = r.message, r.info === "User declined to sign the transaction.")
        return;
      i({
        type: "error",
        title: "Error cancelling DCA",
        desc: "There was an error cancelling your DCA",
        actionCallback: () => {
          navigator.clipboard.writeText(JSON.stringify(e));
        }
      });
    } finally {
      a(!1), d([]);
    }
  }, bundleCancelDCA: async (s) => {
    const e = {
      sign: null,
      tx: "",
      address: c,
      order_ids: s,
      err: null,
      step: "pre-cancel"
    };
    a(!0);
    try {
      const { data: r } = await l.post("/swap/bulkcancel", {
        order_ids: s,
        address: c
      });
      e.step = "pre-sign";
      const p = await (t == null ? void 0 : t.signTx(r.cbor, !0));
      e.step = "pre-sign-post";
      const { data: n } = await l.post("/swap/sign", {
        txCbor: r.cbor,
        signatures: p
      });
      e.sign = n, e.step = "pre-submit";
      const o = await (t == null ? void 0 : t.submitTx(n.cbor));
      return e.tx = o, i({
        type: "success",
        title: "Order cancelled",
        desc: "Order cancelled successfully",
        actionName: "View order",
        actionCallback: () => {
          var C;
          (C = window.open("https://app.dexhunter.io/orders", "_blank")) == null || C.focus();
        }
      }), m([...b, ...s]), u(g + s.length), o;
    } catch (r) {
      if (console.log(r), e.err = r.message, r.info === "User declined to sign the transaction.")
        return;
      i({
        type: "error",
        title: "Error cancelling orders",
        desc: "There was an error cancelling your orders",
        actionCallback: () => {
          navigator.clipboard.writeText(JSON.stringify(e));
        }
      });
    } finally {
      a(!1), d([]);
    }
  } };
};
export {
  Z as useDCACancel
};
