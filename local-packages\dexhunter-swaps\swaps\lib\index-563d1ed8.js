import { createContext as v, use<PERSON>emo as l, createElement as b, useContext as h } from "react";
function P(e, i = []) {
  let o = [];
  function f(u, t) {
    const n = /* @__PURE__ */ v(t), c = o.length;
    o = [
      ...o,
      t
    ];
    function a(p) {
      const { scope: r, children: $, ...d } = p, m = (r == null ? void 0 : r[e][c]) || n, S = l(
        () => d,
        Object.values(d)
      );
      return /* @__PURE__ */ b(m.Provider, {
        value: S
      }, $);
    }
    function x(p, r) {
      const $ = (r == null ? void 0 : r[e][c]) || n, d = h($);
      if (d)
        return d;
      if (t !== void 0)
        return t;
      throw new Error(`\`${p}\` must be used within \`${u}\``);
    }
    return a.displayName = u + "Provider", [
      a,
      x
    ];
  }
  const s = () => {
    const u = o.map((t) => /* @__PURE__ */ v(t));
    return function(n) {
      const c = (n == null ? void 0 : n[e]) || u;
      return l(
        () => ({
          [`__scope${e}`]: {
            ...n,
            [e]: c
          }
        }),
        [
          n,
          c
        ]
      );
    };
  };
  return s.scopeName = e, [
    f,
    C(s, ...i)
  ];
}
function C(...e) {
  const i = e[0];
  if (e.length === 1)
    return i;
  const o = () => {
    const f = e.map(
      (s) => ({
        useScope: s(),
        scopeName: s.scopeName
      })
    );
    return function(u) {
      const t = f.reduce((n, { useScope: c, scopeName: a }) => {
        const p = c(u)[`__scope${a}`];
        return {
          ...n,
          ...p
        };
      }, {});
      return l(
        () => ({
          [`__scope${i.scopeName}`]: t
        }),
        [
          t
        ]
      );
    };
  };
  return o.scopeName = i.scopeName, o;
}
export {
  P as $
};
