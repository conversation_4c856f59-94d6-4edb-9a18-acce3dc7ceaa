import { a as Nt, _ as Bt } from "./index-1c873780.js";
import * as D from "react";
import { useLayoutEffect as we, useEffect as Vt, forwardRef as zt, useState as at, createElement as tt, useRef as xe } from "react";
import { r as ye, $ as It } from "./index-c8f2666b.js";
import { $ as ve } from "./index-563d1ed8.js";
import { c as be, b as St } from "./index-c7156e07.js";
import { $ as $e } from "./index-bcfeaad9.js";
const Ae = ["top", "right", "bottom", "left"], j = Math.min, M = Math.max, dt = Math.round, lt = Math.floor, Y = (t) => ({
  x: t,
  y: t
}), Re = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
}, Ce = {
  start: "end",
  end: "start"
};
function wt(t, e, n) {
  return M(t, j(e, n));
}
function B(t, e) {
  return typeof t == "function" ? t(e) : t;
}
function V(t) {
  return t.split("-")[0];
}
function G(t) {
  return t.split("-")[1];
}
function yt(t) {
  return t === "x" ? "y" : "x";
}
function vt(t) {
  return t === "y" ? "height" : "width";
}
function J(t) {
  return ["top", "bottom"].includes(V(t)) ? "y" : "x";
}
function bt(t) {
  return yt(J(t));
}
function Pe(t, e, n) {
  n === void 0 && (n = !1);
  const o = G(t), i = bt(t), r = vt(i);
  let s = i === "x" ? o === (n ? "end" : "start") ? "right" : "left" : o === "start" ? "bottom" : "top";
  return e.reference[r] > e.floating[r] && (s = ut(s)), [s, ut(s)];
}
function Oe(t) {
  const e = ut(t);
  return [xt(t), e, xt(e)];
}
function xt(t) {
  return t.replace(/start|end/g, (e) => Ce[e]);
}
function Ee(t, e, n) {
  const o = ["left", "right"], i = ["right", "left"], r = ["top", "bottom"], s = ["bottom", "top"];
  switch (t) {
    case "top":
    case "bottom":
      return n ? e ? i : o : e ? o : i;
    case "left":
    case "right":
      return e ? r : s;
    default:
      return [];
  }
}
function Se(t, e, n, o) {
  const i = G(t);
  let r = Ee(V(t), n === "start", o);
  return i && (r = r.map((s) => s + "-" + i), e && (r = r.concat(r.map(xt)))), r;
}
function ut(t) {
  return t.replace(/left|right|bottom|top/g, (e) => Re[e]);
}
function De(t) {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    ...t
  };
}
function jt(t) {
  return typeof t != "number" ? De(t) : {
    top: t,
    right: t,
    bottom: t,
    left: t
  };
}
function mt(t) {
  return {
    ...t,
    top: t.y,
    left: t.x,
    right: t.x + t.width,
    bottom: t.y + t.height
  };
}
function Dt(t, e, n) {
  let {
    reference: o,
    floating: i
  } = t;
  const r = J(e), s = bt(e), l = vt(s), c = V(e), a = r === "y", m = o.x + o.width / 2 - i.width / 2, d = o.y + o.height / 2 - i.height / 2, p = o[l] / 2 - i[l] / 2;
  let f;
  switch (c) {
    case "top":
      f = {
        x: m,
        y: o.y - i.height
      };
      break;
    case "bottom":
      f = {
        x: m,
        y: o.y + o.height
      };
      break;
    case "right":
      f = {
        x: o.x + o.width,
        y: d
      };
      break;
    case "left":
      f = {
        x: o.x - i.width,
        y: d
      };
      break;
    default:
      f = {
        x: o.x,
        y: o.y
      };
  }
  switch (G(e)) {
    case "start":
      f[s] -= p * (n && a ? -1 : 1);
      break;
    case "end":
      f[s] += p * (n && a ? -1 : 1);
      break;
  }
  return f;
}
const Te = async (t, e, n) => {
  const {
    placement: o = "bottom",
    strategy: i = "absolute",
    middleware: r = [],
    platform: s
  } = n, l = r.filter(Boolean), c = await (s.isRTL == null ? void 0 : s.isRTL(e));
  let a = await s.getElementRects({
    reference: t,
    floating: e,
    strategy: i
  }), {
    x: m,
    y: d
  } = Dt(a, o, c), p = o, f = {}, u = 0;
  for (let h = 0; h < l.length; h++) {
    const {
      name: w,
      fn: g
    } = l[h], {
      x: v,
      y: x,
      data: y,
      reset: b
    } = await g({
      x: m,
      y: d,
      initialPlacement: o,
      placement: p,
      strategy: i,
      middlewareData: f,
      rects: a,
      platform: s,
      elements: {
        reference: t,
        floating: e
      }
    });
    if (m = v ?? m, d = x ?? d, f = {
      ...f,
      [w]: {
        ...f[w],
        ...y
      }
    }, b && u <= 50) {
      u++, typeof b == "object" && (b.placement && (p = b.placement), b.rects && (a = b.rects === !0 ? await s.getElementRects({
        reference: t,
        floating: e,
        strategy: i
      }) : b.rects), {
        x: m,
        y: d
      } = Dt(a, p, c)), h = -1;
      continue;
    }
  }
  return {
    x: m,
    y: d,
    placement: p,
    strategy: i,
    middlewareData: f
  };
};
async function et(t, e) {
  var n;
  e === void 0 && (e = {});
  const {
    x: o,
    y: i,
    platform: r,
    rects: s,
    elements: l,
    strategy: c
  } = t, {
    boundary: a = "clippingAncestors",
    rootBoundary: m = "viewport",
    elementContext: d = "floating",
    altBoundary: p = !1,
    padding: f = 0
  } = B(e, t), u = jt(f), w = l[p ? d === "floating" ? "reference" : "floating" : d], g = mt(await r.getClippingRect({
    element: (n = await (r.isElement == null ? void 0 : r.isElement(w))) == null || n ? w : w.contextElement || await (r.getDocumentElement == null ? void 0 : r.getDocumentElement(l.floating)),
    boundary: a,
    rootBoundary: m,
    strategy: c
  })), v = d === "floating" ? {
    ...s.floating,
    x: o,
    y: i
  } : s.reference, x = await (r.getOffsetParent == null ? void 0 : r.getOffsetParent(l.floating)), y = await (r.isElement == null ? void 0 : r.isElement(x)) ? await (r.getScale == null ? void 0 : r.getScale(x)) || {
    x: 1,
    y: 1
  } : {
    x: 1,
    y: 1
  }, b = mt(r.convertOffsetParentRelativeRectToViewportRelativeRect ? await r.convertOffsetParentRelativeRectToViewportRelativeRect({
    rect: v,
    offsetParent: x,
    strategy: c
  }) : v);
  return {
    top: (g.top - b.top + u.top) / y.y,
    bottom: (b.bottom - g.bottom + u.bottom) / y.y,
    left: (g.left - b.left + u.left) / y.x,
    right: (b.right - g.right + u.right) / y.x
  };
}
const Tt = (t) => ({
  name: "arrow",
  options: t,
  async fn(e) {
    const {
      x: n,
      y: o,
      placement: i,
      rects: r,
      platform: s,
      elements: l,
      middlewareData: c
    } = e, {
      element: a,
      padding: m = 0
    } = B(t, e) || {};
    if (a == null)
      return {};
    const d = jt(m), p = {
      x: n,
      y: o
    }, f = bt(i), u = vt(f), h = await s.getDimensions(a), w = f === "y", g = w ? "top" : "left", v = w ? "bottom" : "right", x = w ? "clientHeight" : "clientWidth", y = r.reference[u] + r.reference[f] - p[f] - r.floating[u], b = p[f] - r.reference[f], $ = await (s.getOffsetParent == null ? void 0 : s.getOffsetParent(a));
    let C = $ ? $[x] : 0;
    (!C || !await (s.isElement == null ? void 0 : s.isElement($))) && (C = l.floating[x] || r.floating[u]);
    const O = y / 2 - b / 2, E = C / 2 - h[u] / 2 - 1, H = j(d[g], E), L = j(d[v], E), P = H, F = C - h[u] - L, R = C / 2 - h[u] / 2 + O, T = wt(P, R, F), A = !c.arrow && G(i) != null && R != T && r.reference[u] / 2 - (R < P ? H : L) - h[u] / 2 < 0, S = A ? R < P ? R - P : R - F : 0;
    return {
      [f]: p[f] + S,
      data: {
        [f]: T,
        centerOffset: R - T - S,
        ...A && {
          alignmentOffset: S
        }
      },
      reset: A
    };
  }
}), Le = function(t) {
  return t === void 0 && (t = {}), {
    name: "flip",
    options: t,
    async fn(e) {
      var n, o;
      const {
        placement: i,
        middlewareData: r,
        rects: s,
        initialPlacement: l,
        platform: c,
        elements: a
      } = e, {
        mainAxis: m = !0,
        crossAxis: d = !0,
        fallbackPlacements: p,
        fallbackStrategy: f = "bestFit",
        fallbackAxisSideDirection: u = "none",
        flipAlignment: h = !0,
        ...w
      } = B(t, e);
      if ((n = r.arrow) != null && n.alignmentOffset)
        return {};
      const g = V(i), v = V(l) === l, x = await (c.isRTL == null ? void 0 : c.isRTL(a.floating)), y = p || (v || !h ? [ut(l)] : Oe(l));
      !p && u !== "none" && y.push(...Se(l, h, u, x));
      const b = [l, ...y], $ = await et(e, w), C = [];
      let O = ((o = r.flip) == null ? void 0 : o.overflows) || [];
      if (m && C.push($[g]), d) {
        const P = Pe(i, s, x);
        C.push($[P[0]], $[P[1]]);
      }
      if (O = [...O, {
        placement: i,
        overflows: C
      }], !C.every((P) => P <= 0)) {
        var E, H;
        const P = (((E = r.flip) == null ? void 0 : E.index) || 0) + 1, F = b[P];
        if (F)
          return {
            data: {
              index: P,
              overflows: O
            },
            reset: {
              placement: F
            }
          };
        let R = (H = O.filter((T) => T.overflows[0] <= 0).sort((T, A) => T.overflows[1] - A.overflows[1])[0]) == null ? void 0 : H.placement;
        if (!R)
          switch (f) {
            case "bestFit": {
              var L;
              const T = (L = O.map((A) => [A.placement, A.overflows.filter((S) => S > 0).reduce((S, W) => S + W, 0)]).sort((A, S) => A[1] - S[1])[0]) == null ? void 0 : L[0];
              T && (R = T);
              break;
            }
            case "initialPlacement":
              R = l;
              break;
          }
        if (i !== R)
          return {
            reset: {
              placement: R
            }
          };
      }
      return {};
    }
  };
};
function Lt(t, e) {
  return {
    top: t.top - e.height,
    right: t.right - e.width,
    bottom: t.bottom - e.height,
    left: t.left - e.width
  };
}
function Mt(t) {
  return Ae.some((e) => t[e] >= 0);
}
const Me = function(t) {
  return t === void 0 && (t = {}), {
    name: "hide",
    options: t,
    async fn(e) {
      const {
        rects: n
      } = e, {
        strategy: o = "referenceHidden",
        ...i
      } = B(t, e);
      switch (o) {
        case "referenceHidden": {
          const r = await et(e, {
            ...i,
            elementContext: "reference"
          }), s = Lt(r, n.reference);
          return {
            data: {
              referenceHiddenOffsets: s,
              referenceHidden: Mt(s)
            }
          };
        }
        case "escaped": {
          const r = await et(e, {
            ...i,
            altBoundary: !0
          }), s = Lt(r, n.floating);
          return {
            data: {
              escapedOffsets: s,
              escaped: Mt(s)
            }
          };
        }
        default:
          return {};
      }
    }
  };
};
async function _e(t, e) {
  const {
    placement: n,
    platform: o,
    elements: i
  } = t, r = await (o.isRTL == null ? void 0 : o.isRTL(i.floating)), s = V(n), l = G(n), c = J(n) === "y", a = ["left", "top"].includes(s) ? -1 : 1, m = r && c ? -1 : 1, d = B(e, t);
  let {
    mainAxis: p,
    crossAxis: f,
    alignmentAxis: u
  } = typeof d == "number" ? {
    mainAxis: d,
    crossAxis: 0,
    alignmentAxis: null
  } : {
    mainAxis: 0,
    crossAxis: 0,
    alignmentAxis: null,
    ...d
  };
  return l && typeof u == "number" && (f = l === "end" ? u * -1 : u), c ? {
    x: f * m,
    y: p * a
  } : {
    x: p * a,
    y: f * m
  };
}
const ke = function(t) {
  return t === void 0 && (t = 0), {
    name: "offset",
    options: t,
    async fn(e) {
      const {
        x: n,
        y: o
      } = e, i = await _e(e, t);
      return {
        x: n + i.x,
        y: o + i.y,
        data: i
      };
    }
  };
}, He = function(t) {
  return t === void 0 && (t = {}), {
    name: "shift",
    options: t,
    async fn(e) {
      const {
        x: n,
        y: o,
        placement: i
      } = e, {
        mainAxis: r = !0,
        crossAxis: s = !1,
        limiter: l = {
          fn: (w) => {
            let {
              x: g,
              y: v
            } = w;
            return {
              x: g,
              y: v
            };
          }
        },
        ...c
      } = B(t, e), a = {
        x: n,
        y: o
      }, m = await et(e, c), d = J(V(i)), p = yt(d);
      let f = a[p], u = a[d];
      if (r) {
        const w = p === "y" ? "top" : "left", g = p === "y" ? "bottom" : "right", v = f + m[w], x = f - m[g];
        f = wt(v, f, x);
      }
      if (s) {
        const w = d === "y" ? "top" : "left", g = d === "y" ? "bottom" : "right", v = u + m[w], x = u - m[g];
        u = wt(v, u, x);
      }
      const h = l.fn({
        ...e,
        [p]: f,
        [d]: u
      });
      return {
        ...h,
        data: {
          x: h.x - n,
          y: h.y - o
        }
      };
    }
  };
}, Fe = function(t) {
  return t === void 0 && (t = {}), {
    options: t,
    fn(e) {
      const {
        x: n,
        y: o,
        placement: i,
        rects: r,
        middlewareData: s
      } = e, {
        offset: l = 0,
        mainAxis: c = !0,
        crossAxis: a = !0
      } = B(t, e), m = {
        x: n,
        y: o
      }, d = J(i), p = yt(d);
      let f = m[p], u = m[d];
      const h = B(l, e), w = typeof h == "number" ? {
        mainAxis: h,
        crossAxis: 0
      } : {
        mainAxis: 0,
        crossAxis: 0,
        ...h
      };
      if (c) {
        const x = p === "y" ? "height" : "width", y = r.reference[p] - r.floating[x] + w.mainAxis, b = r.reference[p] + r.reference[x] - w.mainAxis;
        f < y ? f = y : f > b && (f = b);
      }
      if (a) {
        var g, v;
        const x = p === "y" ? "width" : "height", y = ["top", "left"].includes(V(i)), b = r.reference[d] - r.floating[x] + (y && ((g = s.offset) == null ? void 0 : g[d]) || 0) + (y ? 0 : w.crossAxis), $ = r.reference[d] + r.reference[x] + (y ? 0 : ((v = s.offset) == null ? void 0 : v[d]) || 0) - (y ? w.crossAxis : 0);
        u < b ? u = b : u > $ && (u = $);
      }
      return {
        [p]: f,
        [d]: u
      };
    }
  };
}, We = function(t) {
  return t === void 0 && (t = {}), {
    name: "size",
    options: t,
    async fn(e) {
      const {
        placement: n,
        rects: o,
        platform: i,
        elements: r
      } = e, {
        apply: s = () => {
        },
        ...l
      } = B(t, e), c = await et(e, l), a = V(n), m = G(n), d = J(n) === "y", {
        width: p,
        height: f
      } = o.floating;
      let u, h;
      a === "top" || a === "bottom" ? (u = a, h = m === (await (i.isRTL == null ? void 0 : i.isRTL(r.floating)) ? "start" : "end") ? "left" : "right") : (h = a, u = m === "end" ? "top" : "bottom");
      const w = f - c[u], g = p - c[h], v = !e.middlewareData.shift;
      let x = w, y = g;
      if (d) {
        const $ = p - c.left - c.right;
        y = m || v ? j(g, $) : $;
      } else {
        const $ = f - c.top - c.bottom;
        x = m || v ? j(w, $) : $;
      }
      if (v && !m) {
        const $ = M(c.left, 0), C = M(c.right, 0), O = M(c.top, 0), E = M(c.bottom, 0);
        d ? y = p - 2 * ($ !== 0 || C !== 0 ? $ + C : M(c.left, c.right)) : x = f - 2 * (O !== 0 || E !== 0 ? O + E : M(c.top, c.bottom));
      }
      await s({
        ...e,
        availableWidth: y,
        availableHeight: x
      });
      const b = await i.getDimensions(r.floating);
      return p !== b.width || f !== b.height ? {
        reset: {
          rects: !0
        }
      } : {};
    }
  };
};
function X(t) {
  return Yt(t) ? (t.nodeName || "").toLowerCase() : "#document";
}
function _(t) {
  var e;
  return (t == null || (e = t.ownerDocument) == null ? void 0 : e.defaultView) || window;
}
function I(t) {
  var e;
  return (e = (Yt(t) ? t.ownerDocument : t.document) || window.document) == null ? void 0 : e.documentElement;
}
function Yt(t) {
  return t instanceof Node || t instanceof _(t).Node;
}
function z(t) {
  return t instanceof Element || t instanceof _(t).Element;
}
function N(t) {
  return t instanceof HTMLElement || t instanceof _(t).HTMLElement;
}
function _t(t) {
  return typeof ShadowRoot > "u" ? !1 : t instanceof ShadowRoot || t instanceof _(t).ShadowRoot;
}
function ot(t) {
  const {
    overflow: e,
    overflowX: n,
    overflowY: o,
    display: i
  } = k(t);
  return /auto|scroll|overlay|hidden|clip/.test(e + o + n) && !["inline", "contents"].includes(i);
}
function Ne(t) {
  return ["table", "td", "th"].includes(X(t));
}
function $t(t) {
  const e = At(), n = k(t);
  return n.transform !== "none" || n.perspective !== "none" || (n.containerType ? n.containerType !== "normal" : !1) || !e && (n.backdropFilter ? n.backdropFilter !== "none" : !1) || !e && (n.filter ? n.filter !== "none" : !1) || ["transform", "perspective", "filter"].some((o) => (n.willChange || "").includes(o)) || ["paint", "layout", "strict", "content"].some((o) => (n.contain || "").includes(o));
}
function Be(t) {
  let e = K(t);
  for (; N(e) && !gt(e); ) {
    if ($t(e))
      return e;
    e = K(e);
  }
  return null;
}
function At() {
  return typeof CSS > "u" || !CSS.supports ? !1 : CSS.supports("-webkit-backdrop-filter", "none");
}
function gt(t) {
  return ["html", "body", "#document"].includes(X(t));
}
function k(t) {
  return _(t).getComputedStyle(t);
}
function ht(t) {
  return z(t) ? {
    scrollLeft: t.scrollLeft,
    scrollTop: t.scrollTop
  } : {
    scrollLeft: t.pageXOffset,
    scrollTop: t.pageYOffset
  };
}
function K(t) {
  if (X(t) === "html")
    return t;
  const e = (
    // Step into the shadow DOM of the parent of a slotted node.
    t.assignedSlot || // DOM Element detected.
    t.parentNode || // ShadowRoot detected.
    _t(t) && t.host || // Fallback.
    I(t)
  );
  return _t(e) ? e.host : e;
}
function Xt(t) {
  const e = K(t);
  return gt(e) ? t.ownerDocument ? t.ownerDocument.body : t.body : N(e) && ot(e) ? e : Xt(e);
}
function nt(t, e, n) {
  var o;
  e === void 0 && (e = []), n === void 0 && (n = !0);
  const i = Xt(t), r = i === ((o = t.ownerDocument) == null ? void 0 : o.body), s = _(i);
  return r ? e.concat(s, s.visualViewport || [], ot(i) ? i : [], s.frameElement && n ? nt(s.frameElement) : []) : e.concat(i, nt(i, [], n));
}
function qt(t) {
  const e = k(t);
  let n = parseFloat(e.width) || 0, o = parseFloat(e.height) || 0;
  const i = N(t), r = i ? t.offsetWidth : n, s = i ? t.offsetHeight : o, l = dt(n) !== r || dt(o) !== s;
  return l && (n = r, o = s), {
    width: n,
    height: o,
    $: l
  };
}
function Rt(t) {
  return z(t) ? t : t.contextElement;
}
function Z(t) {
  const e = Rt(t);
  if (!N(e))
    return Y(1);
  const n = e.getBoundingClientRect(), {
    width: o,
    height: i,
    $: r
  } = qt(e);
  let s = (r ? dt(n.width) : n.width) / o, l = (r ? dt(n.height) : n.height) / i;
  return (!s || !Number.isFinite(s)) && (s = 1), (!l || !Number.isFinite(l)) && (l = 1), {
    x: s,
    y: l
  };
}
const Ve = /* @__PURE__ */ Y(0);
function Ut(t) {
  const e = _(t);
  return !At() || !e.visualViewport ? Ve : {
    x: e.visualViewport.offsetLeft,
    y: e.visualViewport.offsetTop
  };
}
function ze(t, e, n) {
  return e === void 0 && (e = !1), !n || e && n !== _(t) ? !1 : e;
}
function q(t, e, n, o) {
  e === void 0 && (e = !1), n === void 0 && (n = !1);
  const i = t.getBoundingClientRect(), r = Rt(t);
  let s = Y(1);
  e && (o ? z(o) && (s = Z(o)) : s = Z(t));
  const l = ze(r, n, o) ? Ut(r) : Y(0);
  let c = (i.left + l.x) / s.x, a = (i.top + l.y) / s.y, m = i.width / s.x, d = i.height / s.y;
  if (r) {
    const p = _(r), f = o && z(o) ? _(o) : o;
    let u = p.frameElement;
    for (; u && o && f !== p; ) {
      const h = Z(u), w = u.getBoundingClientRect(), g = k(u), v = w.left + (u.clientLeft + parseFloat(g.paddingLeft)) * h.x, x = w.top + (u.clientTop + parseFloat(g.paddingTop)) * h.y;
      c *= h.x, a *= h.y, m *= h.x, d *= h.y, c += v, a += x, u = _(u).frameElement;
    }
  }
  return mt({
    width: m,
    height: d,
    x: c,
    y: a
  });
}
function Ie(t) {
  let {
    rect: e,
    offsetParent: n,
    strategy: o
  } = t;
  const i = N(n), r = I(n);
  if (n === r)
    return e;
  let s = {
    scrollLeft: 0,
    scrollTop: 0
  }, l = Y(1);
  const c = Y(0);
  if ((i || !i && o !== "fixed") && ((X(n) !== "body" || ot(r)) && (s = ht(n)), N(n))) {
    const a = q(n);
    l = Z(n), c.x = a.x + n.clientLeft, c.y = a.y + n.clientTop;
  }
  return {
    width: e.width * l.x,
    height: e.height * l.y,
    x: e.x * l.x - s.scrollLeft * l.x + c.x,
    y: e.y * l.y - s.scrollTop * l.y + c.y
  };
}
function je(t) {
  return Array.from(t.getClientRects());
}
function Zt(t) {
  return q(I(t)).left + ht(t).scrollLeft;
}
function Ye(t) {
  const e = I(t), n = ht(t), o = t.ownerDocument.body, i = M(e.scrollWidth, e.clientWidth, o.scrollWidth, o.clientWidth), r = M(e.scrollHeight, e.clientHeight, o.scrollHeight, o.clientHeight);
  let s = -n.scrollLeft + Zt(t);
  const l = -n.scrollTop;
  return k(o).direction === "rtl" && (s += M(e.clientWidth, o.clientWidth) - i), {
    width: i,
    height: r,
    x: s,
    y: l
  };
}
function Xe(t, e) {
  const n = _(t), o = I(t), i = n.visualViewport;
  let r = o.clientWidth, s = o.clientHeight, l = 0, c = 0;
  if (i) {
    r = i.width, s = i.height;
    const a = At();
    (!a || a && e === "fixed") && (l = i.offsetLeft, c = i.offsetTop);
  }
  return {
    width: r,
    height: s,
    x: l,
    y: c
  };
}
function qe(t, e) {
  const n = q(t, !0, e === "fixed"), o = n.top + t.clientTop, i = n.left + t.clientLeft, r = N(t) ? Z(t) : Y(1), s = t.clientWidth * r.x, l = t.clientHeight * r.y, c = i * r.x, a = o * r.y;
  return {
    width: s,
    height: l,
    x: c,
    y: a
  };
}
function kt(t, e, n) {
  let o;
  if (e === "viewport")
    o = Xe(t, n);
  else if (e === "document")
    o = Ye(I(t));
  else if (z(e))
    o = qe(e, n);
  else {
    const i = Ut(t);
    o = {
      ...e,
      x: e.x - i.x,
      y: e.y - i.y
    };
  }
  return mt(o);
}
function Kt(t, e) {
  const n = K(t);
  return n === e || !z(n) || gt(n) ? !1 : k(n).position === "fixed" || Kt(n, e);
}
function Ue(t, e) {
  const n = e.get(t);
  if (n)
    return n;
  let o = nt(t, [], !1).filter((l) => z(l) && X(l) !== "body"), i = null;
  const r = k(t).position === "fixed";
  let s = r ? K(t) : t;
  for (; z(s) && !gt(s); ) {
    const l = k(s), c = $t(s);
    !c && l.position === "fixed" && (i = null), (r ? !c && !i : !c && l.position === "static" && !!i && ["absolute", "fixed"].includes(i.position) || ot(s) && !c && Kt(t, s)) ? o = o.filter((m) => m !== s) : i = l, s = K(s);
  }
  return e.set(t, o), o;
}
function Ze(t) {
  let {
    element: e,
    boundary: n,
    rootBoundary: o,
    strategy: i
  } = t;
  const s = [...n === "clippingAncestors" ? Ue(e, this._c) : [].concat(n), o], l = s[0], c = s.reduce((a, m) => {
    const d = kt(e, m, i);
    return a.top = M(d.top, a.top), a.right = j(d.right, a.right), a.bottom = j(d.bottom, a.bottom), a.left = M(d.left, a.left), a;
  }, kt(e, l, i));
  return {
    width: c.right - c.left,
    height: c.bottom - c.top,
    x: c.left,
    y: c.top
  };
}
function Ke(t) {
  return qt(t);
}
function Ge(t, e, n) {
  const o = N(e), i = I(e), r = n === "fixed", s = q(t, !0, r, e);
  let l = {
    scrollLeft: 0,
    scrollTop: 0
  };
  const c = Y(0);
  if (o || !o && !r)
    if ((X(e) !== "body" || ot(i)) && (l = ht(e)), o) {
      const a = q(e, !0, r, e);
      c.x = a.x + e.clientLeft, c.y = a.y + e.clientTop;
    } else
      i && (c.x = Zt(i));
  return {
    x: s.left + l.scrollLeft - c.x,
    y: s.top + l.scrollTop - c.y,
    width: s.width,
    height: s.height
  };
}
function Ht(t, e) {
  return !N(t) || k(t).position === "fixed" ? null : e ? e(t) : t.offsetParent;
}
function Gt(t, e) {
  const n = _(t);
  if (!N(t))
    return n;
  let o = Ht(t, e);
  for (; o && Ne(o) && k(o).position === "static"; )
    o = Ht(o, e);
  return o && (X(o) === "html" || X(o) === "body" && k(o).position === "static" && !$t(o)) ? n : o || Be(t) || n;
}
const Je = async function(t) {
  let {
    reference: e,
    floating: n,
    strategy: o
  } = t;
  const i = this.getOffsetParent || Gt, r = this.getDimensions;
  return {
    reference: Ge(e, await i(n), o),
    floating: {
      x: 0,
      y: 0,
      ...await r(n)
    }
  };
};
function Qe(t) {
  return k(t).direction === "rtl";
}
const tn = {
  convertOffsetParentRelativeRectToViewportRelativeRect: Ie,
  getDocumentElement: I,
  getClippingRect: Ze,
  getOffsetParent: Gt,
  getElementRects: Je,
  getClientRects: je,
  getDimensions: Ke,
  getScale: Z,
  isElement: z,
  isRTL: Qe
};
function en(t, e) {
  let n = null, o;
  const i = I(t);
  function r() {
    clearTimeout(o), n && n.disconnect(), n = null;
  }
  function s(l, c) {
    l === void 0 && (l = !1), c === void 0 && (c = 1), r();
    const {
      left: a,
      top: m,
      width: d,
      height: p
    } = t.getBoundingClientRect();
    if (l || e(), !d || !p)
      return;
    const f = lt(m), u = lt(i.clientWidth - (a + d)), h = lt(i.clientHeight - (m + p)), w = lt(a), v = {
      rootMargin: -f + "px " + -u + "px " + -h + "px " + -w + "px",
      threshold: M(0, j(1, c)) || 1
    };
    let x = !0;
    function y(b) {
      const $ = b[0].intersectionRatio;
      if ($ !== c) {
        if (!x)
          return s();
        $ ? s(!1, $) : o = setTimeout(() => {
          s(!1, 1e-7);
        }, 100);
      }
      x = !1;
    }
    try {
      n = new IntersectionObserver(y, {
        ...v,
        // Handle <iframe>s
        root: i.ownerDocument
      });
    } catch {
      n = new IntersectionObserver(y, v);
    }
    n.observe(t);
  }
  return s(!0), r;
}
function nn(t, e, n, o) {
  o === void 0 && (o = {});
  const {
    ancestorScroll: i = !0,
    ancestorResize: r = !0,
    elementResize: s = typeof ResizeObserver == "function",
    layoutShift: l = typeof IntersectionObserver == "function",
    animationFrame: c = !1
  } = o, a = Rt(t), m = i || r ? [...a ? nt(a) : [], ...nt(e)] : [];
  m.forEach((g) => {
    i && g.addEventListener("scroll", n, {
      passive: !0
    }), r && g.addEventListener("resize", n);
  });
  const d = a && l ? en(a, n) : null;
  let p = -1, f = null;
  s && (f = new ResizeObserver((g) => {
    let [v] = g;
    v && v.target === a && f && (f.unobserve(e), cancelAnimationFrame(p), p = requestAnimationFrame(() => {
      f && f.observe(e);
    })), n();
  }), a && !c && f.observe(a), f.observe(e));
  let u, h = c ? q(t) : null;
  c && w();
  function w() {
    const g = q(t);
    h && (g.x !== h.x || g.y !== h.y || g.width !== h.width || g.height !== h.height) && n(), h = g, u = requestAnimationFrame(w);
  }
  return n(), () => {
    m.forEach((g) => {
      i && g.removeEventListener("scroll", n), r && g.removeEventListener("resize", n);
    }), d && d(), f && f.disconnect(), f = null, c && cancelAnimationFrame(u);
  };
}
const on = (t, e, n) => {
  const o = /* @__PURE__ */ new Map(), i = {
    platform: tn,
    ...n
  }, r = {
    ...i.platform,
    _c: o
  };
  return Te(t, e, {
    ...i,
    platform: r
  });
}, rn = (t) => {
  function e(n) {
    return {}.hasOwnProperty.call(n, "current");
  }
  return {
    name: "arrow",
    options: t,
    fn(n) {
      const {
        element: o,
        padding: i
      } = typeof t == "function" ? t(n) : t;
      return o && e(o) ? o.current != null ? Tt({
        element: o.current,
        padding: i
      }).fn(n) : {} : o ? Tt({
        element: o,
        padding: i
      }).fn(n) : {};
    }
  };
};
var ft = typeof document < "u" ? we : Vt;
function pt(t, e) {
  if (t === e)
    return !0;
  if (typeof t != typeof e)
    return !1;
  if (typeof t == "function" && t.toString() === e.toString())
    return !0;
  let n, o, i;
  if (t && e && typeof t == "object") {
    if (Array.isArray(t)) {
      if (n = t.length, n != e.length)
        return !1;
      for (o = n; o-- !== 0; )
        if (!pt(t[o], e[o]))
          return !1;
      return !0;
    }
    if (i = Object.keys(t), n = i.length, n !== Object.keys(e).length)
      return !1;
    for (o = n; o-- !== 0; )
      if (!{}.hasOwnProperty.call(e, i[o]))
        return !1;
    for (o = n; o-- !== 0; ) {
      const r = i[o];
      if (!(r === "_owner" && t.$$typeof) && !pt(t[r], e[r]))
        return !1;
    }
    return !0;
  }
  return t !== t && e !== e;
}
function Jt(t) {
  return typeof window > "u" ? 1 : (t.ownerDocument.defaultView || window).devicePixelRatio || 1;
}
function Ft(t, e) {
  const n = Jt(t);
  return Math.round(e * n) / n;
}
function Wt(t) {
  const e = D.useRef(t);
  return ft(() => {
    e.current = t;
  }), e;
}
function sn(t) {
  t === void 0 && (t = {});
  const {
    placement: e = "bottom",
    strategy: n = "absolute",
    middleware: o = [],
    platform: i,
    elements: {
      reference: r,
      floating: s
    } = {},
    transform: l = !0,
    whileElementsMounted: c,
    open: a
  } = t, [m, d] = D.useState({
    x: 0,
    y: 0,
    strategy: n,
    placement: e,
    middlewareData: {},
    isPositioned: !1
  }), [p, f] = D.useState(o);
  pt(p, o) || f(o);
  const [u, h] = D.useState(null), [w, g] = D.useState(null), v = D.useCallback((A) => {
    A != $.current && ($.current = A, h(A));
  }, [h]), x = D.useCallback((A) => {
    A !== C.current && (C.current = A, g(A));
  }, [g]), y = r || u, b = s || w, $ = D.useRef(null), C = D.useRef(null), O = D.useRef(m), E = Wt(c), H = Wt(i), L = D.useCallback(() => {
    if (!$.current || !C.current)
      return;
    const A = {
      placement: e,
      strategy: n,
      middleware: p
    };
    H.current && (A.platform = H.current), on($.current, C.current, A).then((S) => {
      const W = {
        ...S,
        isPositioned: !0
      };
      P.current && !pt(O.current, W) && (O.current = W, ye.flushSync(() => {
        d(W);
      }));
    });
  }, [p, e, n, H]);
  ft(() => {
    a === !1 && O.current.isPositioned && (O.current.isPositioned = !1, d((A) => ({
      ...A,
      isPositioned: !1
    })));
  }, [a]);
  const P = D.useRef(!1);
  ft(() => (P.current = !0, () => {
    P.current = !1;
  }), []), ft(() => {
    if (y && ($.current = y), b && (C.current = b), y && b) {
      if (E.current)
        return E.current(y, b, L);
      L();
    }
  }, [y, b, L, E]);
  const F = D.useMemo(() => ({
    reference: $,
    floating: C,
    setReference: v,
    setFloating: x
  }), [v, x]), R = D.useMemo(() => ({
    reference: y,
    floating: b
  }), [y, b]), T = D.useMemo(() => {
    const A = {
      position: n,
      left: 0,
      top: 0
    };
    if (!R.floating)
      return A;
    const S = Ft(R.floating, m.x), W = Ft(R.floating, m.y);
    return l ? {
      ...A,
      transform: "translate(" + S + "px, " + W + "px)",
      ...Jt(R.floating) >= 1.5 && {
        willChange: "transform"
      }
    } : {
      position: n,
      left: S,
      top: W
    };
  }, [n, l, R.floating, m.x, m.y]);
  return D.useMemo(() => ({
    ...m,
    update: L,
    refs: F,
    elements: R,
    floatingStyles: T
  }), [m, L, F, R, T]);
}
const Qt = "Popper", [te, bn] = ve(Qt), [cn, ee] = te(Qt), ln = (t) => {
  const { __scopePopper: e, children: n } = t, [o, i] = at(null);
  return /* @__PURE__ */ tt(cn, {
    scope: e,
    anchor: o,
    onAnchorChange: i
  }, n);
}, an = "PopperAnchor", fn = /* @__PURE__ */ zt((t, e) => {
  const { __scopePopper: n, virtualRef: o, ...i } = t, r = ee(an, n), s = xe(null), l = Nt(e, s);
  return Vt(() => {
    r.onAnchorChange((o == null ? void 0 : o.current) || s.current);
  }), o ? null : /* @__PURE__ */ tt(It.div, Bt({}, i, {
    ref: l
  }));
}), ne = "PopperContent", [dn, $n] = te(ne), un = /* @__PURE__ */ zt((t, e) => {
  var n, o, i, r, s, l, c, a;
  const { __scopePopper: m, side: d = "bottom", sideOffset: p = 0, align: f = "center", alignOffset: u = 0, arrowPadding: h = 0, avoidCollisions: w = !0, collisionBoundary: g = [], collisionPadding: v = 0, sticky: x = "partial", hideWhenDetached: y = !1, updatePositionStrategy: b = "optimized", onPlaced: $, ...C } = t, O = ee(ne, m), [E, H] = at(null), L = Nt(
    e,
    (Q) => H(Q)
  ), [P, F] = at(null), R = $e(P), T = (n = R == null ? void 0 : R.width) !== null && n !== void 0 ? n : 0, A = (o = R == null ? void 0 : R.height) !== null && o !== void 0 ? o : 0, S = d + (f !== "center" ? "-" + f : ""), W = typeof v == "number" ? v : {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    ...v
  }, Ct = Array.isArray(g) ? g : [
    g
  ], ie = Ct.length > 0, it = {
    padding: W,
    boundary: Ct.filter(mn),
    // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries
    altBoundary: ie
  }, { refs: re, floatingStyles: Pt, placement: se, isPositioned: rt, middlewareData: U } = sn({
    // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues
    strategy: "fixed",
    placement: S,
    whileElementsMounted: (...Q) => nn(...Q, {
      animationFrame: b === "always"
    }),
    elements: {
      reference: O.anchor
    },
    middleware: [
      ke({
        mainAxis: p + A,
        alignmentAxis: u
      }),
      w && He({
        mainAxis: !0,
        crossAxis: !1,
        limiter: x === "partial" ? Fe() : void 0,
        ...it
      }),
      w && Le({
        ...it
      }),
      We({
        ...it,
        apply: ({ elements: Q, rects: Et, availableWidth: me, availableHeight: pe }) => {
          const { width: ge, height: he } = Et.reference, ct = Q.floating.style;
          ct.setProperty("--radix-popper-available-width", `${me}px`), ct.setProperty("--radix-popper-available-height", `${pe}px`), ct.setProperty("--radix-popper-anchor-width", `${ge}px`), ct.setProperty("--radix-popper-anchor-height", `${he}px`);
        }
      }),
      P && rn({
        element: P,
        padding: h
      }),
      pn({
        arrowWidth: T,
        arrowHeight: A
      }),
      y && Me({
        strategy: "referenceHidden",
        ...it
      })
    ]
  }), [Ot, ce] = oe(se), st = be($);
  St(() => {
    rt && (st == null || st());
  }, [
    rt,
    st
  ]);
  const le = (i = U.arrow) === null || i === void 0 ? void 0 : i.x, ae = (r = U.arrow) === null || r === void 0 ? void 0 : r.y, fe = ((s = U.arrow) === null || s === void 0 ? void 0 : s.centerOffset) !== 0, [de, ue] = at();
  return St(() => {
    E && ue(window.getComputedStyle(E).zIndex);
  }, [
    E
  ]), /* @__PURE__ */ tt("div", {
    ref: re.setFloating,
    "data-radix-popper-content-wrapper": "",
    style: {
      ...Pt,
      transform: rt ? Pt.transform : "translate(0, -200%)",
      // keep off the page when measuring
      minWidth: "max-content",
      zIndex: de,
      "--radix-popper-transform-origin": [
        (l = U.transformOrigin) === null || l === void 0 ? void 0 : l.x,
        (c = U.transformOrigin) === null || c === void 0 ? void 0 : c.y
      ].join(" ")
    },
    dir: t.dir
  }, /* @__PURE__ */ tt(dn, {
    scope: m,
    placedSide: Ot,
    onArrowChange: F,
    arrowX: le,
    arrowY: ae,
    shouldHideArrow: fe
  }, /* @__PURE__ */ tt(It.div, Bt({
    "data-side": Ot,
    "data-align": ce
  }, C, {
    ref: L,
    style: {
      ...C.style,
      // if the PopperContent hasn't been placed yet (not all measurements done)
      // we prevent animations so that users's animation don't kick in too early referring wrong sides
      animation: rt ? void 0 : "none",
      // hide the content if using the hide middleware and should be hidden
      opacity: (a = U.hide) !== null && a !== void 0 && a.referenceHidden ? 0 : void 0
    }
  }))));
});
function mn(t) {
  return t !== null;
}
const pn = (t) => ({
  name: "transformOrigin",
  options: t,
  fn(e) {
    var n, o, i, r, s;
    const { placement: l, rects: c, middlewareData: a } = e, d = ((n = a.arrow) === null || n === void 0 ? void 0 : n.centerOffset) !== 0, p = d ? 0 : t.arrowWidth, f = d ? 0 : t.arrowHeight, [u, h] = oe(l), w = {
      start: "0%",
      center: "50%",
      end: "100%"
    }[h], g = ((o = (i = a.arrow) === null || i === void 0 ? void 0 : i.x) !== null && o !== void 0 ? o : 0) + p / 2, v = ((r = (s = a.arrow) === null || s === void 0 ? void 0 : s.y) !== null && r !== void 0 ? r : 0) + f / 2;
    let x = "", y = "";
    return u === "bottom" ? (x = d ? w : `${g}px`, y = `${-f}px`) : u === "top" ? (x = d ? w : `${g}px`, y = `${c.floating.height + f}px`) : u === "right" ? (x = `${-f}px`, y = d ? w : `${v}px`) : u === "left" && (x = `${c.floating.width + f}px`, y = d ? w : `${v}px`), {
      data: {
        x,
        y
      }
    };
  }
});
function oe(t) {
  const [e, n = "center"] = t.split("-");
  return [
    e,
    n
  ];
}
const An = ln, Rn = fn, Cn = un;
export {
  bn as $,
  Cn as a,
  An as b,
  Rn as c
};
