'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  DialogRoot,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogCloseTrigger,
} from "@/components/ui/dialog";
import { toast } from 'react-toastify';
import { setUserInfo } from "@/redux/slices/userInfoSlice";
import ReactCrop, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import StyledButton from '@/components/ui/styled-button';
import { useDispatch } from "react-redux";

interface ProfileImageUploadModalProps {
  isOpen: boolean;
  userData: any;
  onClose: () => void;
  onSave: (imageData: string) => Promise<{ success: boolean; error: string } | undefined>;
  currentImage?: string;
}

const ProfileImageUploadModal = ({ isOpen, userData, onClose, onSave }: ProfileImageUploadModalProps) => {
  const dispatch = useDispatch();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 100,
    height: 100,
    x: 0,
    y: 0
  });
  const [completedCrop, setCompletedCrop] = useState<any>(null);
  const imgRef = useRef<HTMLImageElement | null>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const Toast = () => toast('');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const reader = new FileReader();
      reader.addEventListener('load', () => {
        setSelectedImage(reader.result as string);
      });
      reader.readAsDataURL(e.target.files[0]);
    }
  };
  // Fix the onImageLoad function - there's a variable name error
  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    imgRef.current = e.currentTarget;

    // Set initial crop to be a square in the center
    const { width, height } = e.currentTarget;
    const cropSize = Math.min(width, height);
    const x = (width - cropSize) / 2;
    const y = (height - cropSize) / 2;

    const initialCrop = {
      unit: 'px',
      width: cropSize,
      height: cropSize,
      x: x,
      y: y
    } as any;

    setCrop(initialCrop);
    setCompletedCrop(initialCrop); // Set initial completed crop
  };
  // Update getCroppedImg to preserve high quality
  const getCroppedImg = () => {
    if (!completedCrop || !imgRef.current || !previewCanvasRef.current) return;

    const image = imgRef.current;
    const canvas = previewCanvasRef.current;
    const crop = completedCrop;

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    // Use the original dimensions from the cropped area
    const pixelCrop = {
      x: crop.x * scaleX,
      y: crop.y * scaleY,
      width: crop.width * scaleX,
      height: crop.height * scaleY
    };

    // Set canvas to the actual dimensions of the cropped area
    // For profile images, we want a reasonably large size but not too large
    const size = Math.max(pixelCrop.width, pixelCrop.height);
    canvas.width = size;
    canvas.height = size;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // For circular crop, we need to create a circular clipping path
    ctx.beginPath();
    ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
    ctx.clip();

    // Draw the cropped image at full resolution
    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      size,
      size
    );

    // Return high quality JPEG
    return canvas.toDataURL('image/webp', 0.95);
  };

  const handleSave = async () => {
    setIsLoading(true);

    try {
      const croppedImageData = getCroppedImg();
      if (croppedImageData) {
        const savedData = await onSave(croppedImageData) as any;
        if (!savedData?.success) throw new Error('Failed to save image: ' + savedData.error);
        dispatch(setUserInfo({ ...userData, profilePhoto: croppedImageData }));

        setIsLoading(false);
        onClose();
      }
    } catch (error: any) {
      console.error('Error saving cropped image:', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setSelectedImage(null);
      setIsLoading(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <DialogRoot open={isOpen} onOpenChange={onClose} placement="center" motionPreset="slide-in-bottom">
      <DialogContent className="!max-w-2xl w-full bg-white/60 dark:bg-[#121212]/60 backdrop-blur border-2 border-solid border-white/20 p-2">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-[#121212] dark:text-white">
            Update Profile Picture
          </DialogTitle>
          <DialogCloseTrigger />
        </DialogHeader>

        <DialogBody className="flex flex-col items-center gap-6 py-4">
          {selectedImage ? (
            <div className="flex flex-col items-center justify-center gap-4 w-full">
              <ReactCrop
                crop={crop}
                onChange={(c) => setCrop(c)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={1}
                circularCrop
              >
                <img
                  ref={imgRef}
                  src={selectedImage}
                  alt="Upload"
                  className="!max-h-[400px] max-w-full w-full h-auto"
                  onLoad={onImageLoad}
                />
              </ReactCrop>

              <canvas
                ref={previewCanvasRef}
                className="hidden"
              />

              <button
                onClick={() => setSelectedImage(null)}
                className="relative bg-red-500 hover:bg-red-600 text-white w-16 h-10 flex items-center justify-center rounded-lg transition-colors"
                aria-label="Reset image"
                disabled={isLoading}
              >
                Reset
              </button>
            </div>
          ) : (
            <div className="flex flex-col items-center gap-4 w-full h-full">
              <div
                className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:bg-[#121212]/50 hover:bg-gray-100 dark:hover:bg-[#121212]/60"
                onDragOver={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const file = e.dataTransfer.files[0];
                  if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.addEventListener('load', () => {
                      setSelectedImage(reader.result as string);
                    });
                    reader.readAsDataURL(file);
                  }
                }}
              >
                <label className="flex flex-col items-center justify-center w-full h-full cursor-pointer">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                      <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                    </svg>
                    <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">Drag and drop or click to upload</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG or GIF</p>
                  </div>
                  <input type="file" className="hidden" onChange={handleFileChange} accept="image/*" />
                </label>
              </div>
            </div>
          )}
        </DialogBody>

        <DialogFooter className="flex !justify-start gap-4 mt-2 !mb-3 w-full !px-1">
          <StyledButton
            onClick={handleSave}
            disabled={!selectedImage || !completedCrop || isLoading}
            buttonText={isLoading ? "Saving..." : "Save Changes"}
            className={`!text-sm w-40 h-10 flex items-center justify-center !p-0 ${isLoading ? 'hover' : ''}`}
          />

          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
          >
            Cancel
          </button>
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  );
};
export default ProfileImageUploadModal;