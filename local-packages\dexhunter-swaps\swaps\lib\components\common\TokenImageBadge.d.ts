declare const TokenImageBadge: ({ token, isVerified, verifiedSize, }: {
    token: any;
    isVerified?: boolean | undefined;
    verifiedSize?: number | undefined;
    badgeClassName?: string | undefined;
}) => import("react/jsx-runtime").JSX.Element | undefined;
declare const TokenImageTooltip: ({ tokenId, token, isTokenVerified }: any) => import("react/jsx-runtime").JSX.Element;
declare const CardanoImage: ({ verifiedElement, tokenId, className, size }: any) => import("react/jsx-runtime").JSX.Element;
export { TokenImageBadge, TokenImageTooltip, CardanoImage };
