'use client';

import React, { useState, useRef, useEffect, memo } from 'react';
import { Play } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Video, Smartphone } from "lucide-react";
import NextImage from 'next/image';

interface VideoPlayerWithBlurProps {
  thumbnailUrl: string;
  onClick?: () => void;
  duration?: string;
  resolution?: string;
  isClip?: boolean; // <-- add this prop
}

const VideoPlayerWithBlur: React.FC<VideoPlayerWithBlurProps> = ({
  thumbnailUrl,
  onClick,
  duration,
  resolution,
  isClip // <-- use this prop
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isPortrait, setIsPortrait] = useState<boolean | null>(null);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick?.();
  };

  const handleImgLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    setIsLoading(false);
    const { naturalWidth: w, naturalHeight: h } = e.currentTarget;
    setIsPortrait(h > w);
  };

  return (
    <div
      className={`
        relative rounded-md
        overflow-hidden bg-black
        ${isPortrait === null
          ? 'h-[415px]'
          : isPortrait
            ? 'h-[clamp(200px,88vh,100vh)] w-full'
            : 'w-full h-auto aspect-video'
        }
      `}
      onClick={handleClick}
    >
      {/* Blurred background only for portrait */}
      {isPortrait && (
        <div
          className="absolute inset-0 bg-center bg-cover filter blur-2xl scale-105 opacity-80"
          style={{ backgroundImage: `url(${thumbnailUrl})` }}
        />
      )}

      {/* Centered thumbnail */}
      <div className="absolute inset-0 w-full h-full flex items-center justify-center">
        <Skeleton loading={isLoading} width="100%" height="100%">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Icon overlay in top-left */}
            <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1 z-[1]">
              {isClip ? (
                <Smartphone className="h-4 w-4 dark:text-white" />
              ) : (
                <Video className="h-auto w-[22px] relative -top-[1px] dark:text-white" />
              )} 1
            </div>
            <NextImage
              src={thumbnailUrl || '/images/user/default-avatar.webp'}
              alt="Video thumbnail"
              width={600}
              height={600}
              className={isPortrait ? 'h-full w-auto object-contain' : 'w-full h-full object-cover'}
              loading="lazy"
              placeholder="empty"
              onLoad={handleImgLoad}
              onError={() => setIsLoading(false)}
            />
          </div>
        </Skeleton>

        {/* Play button overlay */}
        <div
          className="absolute inset-0 flex items-center justify-center cursor-pointer"
          onClick={handleClick}
        >
          <div className="relative">
            <div className="absolute inset-0 bg-turquoise rounded-full opacity-30 animate-ping-slow" />
            <button className="relative w-16 h-16 bg-turquoise rounded-full flex items-center justify-center hover:bg-turquoise-hover transition-colors">
              <Play className="h-8 w-8 text-white fill-white ml-1" />
            </button>
          </div>
        </div>
      </div>

      {/* Video info overlay (duration, resolution) */}
      {(duration || resolution) && (
        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
          {resolution && <span className="mr-1">{resolution}</span>}
          {duration && <span>{duration}</span>}
        </div>
      )}
    </div>
  );
};
export default VideoPlayerWithBlur;