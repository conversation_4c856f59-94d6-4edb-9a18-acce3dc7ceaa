import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { MediaItem } from "@/types/post";

interface ExtendedMediaItem extends MediaItem {
  metadata?: {
    dimensions?: { width: number; height: number };
    duration?: number;
    resolution?: string;
    format?: string;
  };
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function heightToCentimeters(height: string): number {
  const match = height.match(/(\d+)'(\d+)"?/);
  if (match) {
    const feet = parseInt(match[1], 10);
    const inches = parseInt(match[2], 10);
    return Math.round((feet * 12 + inches) * 2.54);
  }
  return 0;
}

export function centimetersToHeight(cm: number): string {
  if (!cm) return '';

  const totalInches = Math.round(cm / 2.54);
  const feet = Math.floor(totalInches / 12);
  const inches = totalInches % 12;

  return `${feet}'${inches}"`;
}

export const extractMetadata = (file: File): Promise<ExtendedMediaItem['metadata']> => {
  return new Promise((resolve, reject) => {
    if (file.type.startsWith('image/')) {
      const img = new Image();
      const url = URL.createObjectURL(file);
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({
          dimensions: { width: img.width, height: img.height },
          format: file.type.split('/')[1], // e.g., 'jpeg', 'png'
        });
      };
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };
      img.src = url;
    } else if (file.type.startsWith('video/')) {
      const video = document.createElement('video');
      const url = URL.createObjectURL(file);
      video.preload = 'metadata';
      video.onloadedmetadata = () => {
        URL.revokeObjectURL(url);
        resolve({
          dimensions: { width: video.videoWidth, height: video.videoHeight },
          resolution: `${video.videoWidth}x${video.videoHeight}`,
          duration: video.duration,
          format: file.type.split('/')[1], // e.g., 'mp4', 'webm'
        });
      };
      video.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load video metadata'));
      };
      video.src = url;
    } else if (file.type.startsWith('audio/')) {
      const audio = new Audio();
      const url = URL.createObjectURL(file);
      audio.preload = 'metadata';
      audio.onloadedmetadata = () => {
        URL.revokeObjectURL(url);
        resolve({
          duration: audio.duration,
          format: file.type.split('/')[1], // e.g., 'mp3', 'wav'
        });
      };
      audio.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load audio metadata'));
      };
      audio.src = url;
    } else {
      resolve({});
    }
  });
};