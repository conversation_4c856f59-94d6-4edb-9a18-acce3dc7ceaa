import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

// Types
interface FolderInput {
  folderName: string;
  parentFolderId?: string | null;
}

// Create folder (POST)
export const createFolder = mutation({
  args: {
    folderName: v.string(),
    parentFolderId: v.optional(v.union(v.string(), v.null())),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');
    if (!args.folderName) throw new Error('Folder name is required');

    const now = new Date().toISOString();
    const folderId = await ctx.db.insert('Folders', {
      creator_id: user.subject,
      folder_name: args.folderName,
      parent_folder_id: args.parentFolderId ?? null,
      created_at: now,
      updated_at: now,
    });

    const folder = await ctx.db.get(folderId);
    return { success: true, folder };
  },
});

// Fetch folders (GET)
export const fetchFolders = query({
  args: {
    parentFolderId: v.optional(v.union(v.string(), v.null())),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    let foldersQuery = ctx.db
      .query('Folders')
      .filter(q => q.eq(q.field('creator_id'), user.subject));

    if (args.parentFolderId !== undefined) {
      if (args.parentFolderId === null) {
        foldersQuery = foldersQuery.filter(q => q.eq(q.field('parent_folder_id'), null));
      } else {
        foldersQuery = foldersQuery.filter(q => q.eq(q.field('parent_folder_id'), args.parentFolderId));
      }
    }

    // Convex doesn't support order by string, so you may need to sort client-side
    const folders = await foldersQuery.collect();
    return { success: true, folders };
  },
});