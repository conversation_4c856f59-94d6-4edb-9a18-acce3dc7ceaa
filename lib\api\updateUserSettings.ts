import { api } from '@/convex/_generated/api';
import { ConvexHttpClient } from 'convex/browser';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function updateUserSettings(
    userId: string,
    accountType: string,
    section: 'notifications' | 'privacy' | 'account' | 'profile',
    data: Record<string, any>
) {
    try {
        const response = await convex.mutation(api.users.updateUserSettings, {
            section,
            data,
        });
        if (!response.success) throw new Error(response.message || 'Failed to update settings');
        return response;
    } catch (error: any) {
        console.error('Error updating settings:', error);
        throw error;
    }
}

export async function updateUserBanner(userId: string, accountType: string, data: any) {
    try {
        const result = await convex.mutation(api.accounts.updateUserBanner, {
            userId,
            data,
        });
        if (!result.success) throw new Error('Failed to update banner');
        return { success: true, data: result.data };
    } catch (error: any) {
        console.error('Error updating banner:', error);
        return { success: false, error: 'Failed to update banner: ' + error.message };
    }
};