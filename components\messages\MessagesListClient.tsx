'use client';

import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import Link from 'next/link';
import { useUser } from '@/hooks/useUser';

export default function MessagesListClient() {
  const { user } = useUser();
  const userId = user?.user_id;

  const result = useQuery(api.messageSearch.searchMessages, {
    query: '',
    type: 'direct',
    limit: 10,
  });

  const conversations = result?.conversations || [];
  const isLoading = result === undefined;
  const error = result && 'error' in result ? result.error : null;

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading messages</div>;

  // Use userId for correct partner logic
  const getPartnerId = (conv: any) =>
    conv.sender_id === userId ? conv.receiver_id : conv.sender_id;
  const getPartner = (conv: any) =>
    conv.sender_id === userId ? conv.receiver : conv.sender;

  return (
    <div className="flex-1 flex flex-col gap-2 items-center justify-center text-[#18181b] dark:text-white">
      <h2 className="text-xl font-medium mb-2">Recent Conversations</h2>
      <ul className="w-full max-w-md space-y-2">
        {conversations.length === 0 && (
          <li className="text-gray-500 w-full text-center">No conversations found.</li>
        )}
        {conversations.map((conv: any) => {
          const partner = getPartner(conv);
          const partnerId = getPartnerId(conv);
          return (
            <li key={conv._id}>
              <Link
                href={`/messages/${partnerId}`}
                className="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <img
                  src={partner?.profilePhoto || '/images/user/default-avatar.webp'}
                  alt={partner?.username || 'User'}
                  className="h-10 w-10 rounded-full mr-3 object-cover"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-1">
                    <p className="font-medium truncate">
                      {partner?.username || partnerId || 'Unknown User'}
                    </p>
                  </div>
                  <p className="text-sm text-gray-500 truncate">
                    {conv.content?.length > 30
                      ? conv.content.substring(0, 30) + '...'
                      : conv.content}
                  </p>
                </div>
                <span className="text-xs text-gray-400">
                  {conv.created_at
                    ? new Date(conv.created_at).toLocaleDateString()
                    : ''}
                </span>
              </Link>
            </li>
          );
        })}
      </ul>
    </div>
  );
};