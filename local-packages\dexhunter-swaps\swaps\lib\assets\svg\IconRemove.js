import { jsx as C } from "react/jsx-runtime";
import { memo as e } from "react";
const t = (o) => /* @__PURE__ */ C(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 14 14",
    fill: "none",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M12.8839 2.88388C13.372 2.39573 13.372 1.60427 12.8839 1.11612C12.3957 0.627962 11.6043 0.627961 11.1161 1.11612L12.8839 2.88388ZM1.11612 11.1161C0.627962 11.6043 0.627961 12.3957 1.11612 12.8839C1.60427 13.372 2.39573 13.372 2.88388 12.8839L1.11612 11.1161ZM2.88388 1.11612C2.39573 0.627961 1.60427 0.627961 1.11612 1.11612C0.627961 1.60427 0.627961 2.39573 1.11612 2.88388L2.88388 1.11612ZM11.1161 12.8839C11.6043 13.372 12.3957 13.372 12.8839 12.8839C13.372 12.3957 13.372 11.6043 12.8839 11.1161L11.1161 12.8839ZM12 2C11.1161 1.11612 11.1161 1.11613 11.1161 1.11615C11.1161 1.11618 11.116 1.11622 11.116 1.11627C11.1159 1.11637 11.1157 1.11652 11.1155 1.11672C11.1151 1.11712 11.1145 1.11773 11.1137 1.11853C11.1121 1.12013 11.1097 1.12253 11.1065 1.1257C11.1002 1.13203 11.0908 1.14146 11.0784 1.15383C11.0537 1.17857 11.0172 1.21507 10.9701 1.26212C10.876 1.35622 10.7397 1.49252 10.5709 1.66133C10.2333 1.99895 9.76564 2.46659 9.24554 2.98669C8.20535 4.02688 6.95535 5.27688 6.11611 6.11612L7.88389 7.88388C8.72312 7.04465 9.97312 5.79465 11.0133 4.75446C11.5334 4.23436 12.001 3.76672 12.3387 3.4291C12.5075 3.26029 12.6438 3.12399 12.7379 3.02988C12.7849 2.98283 12.8214 2.94633 12.8462 2.9216C12.8585 2.90923 12.868 2.8998 12.8743 2.89346C12.8775 2.8903 12.8799 2.8879 12.8815 2.8863C12.8823 2.8855 12.8829 2.88489 12.8833 2.88449C12.8835 2.88429 12.8836 2.88414 12.8837 2.88404C12.8838 2.88399 12.8838 2.88395 12.8838 2.88392C12.8839 2.8839 12.8839 2.88388 12 2ZM6.11611 6.11612C5.27688 6.95535 4.02688 8.20535 2.98669 9.24554C2.4666 9.76564 1.99895 10.2333 1.66133 10.5709C1.49252 10.7397 1.35622 10.876 1.26212 10.9701C1.21507 11.0172 1.17857 11.0537 1.15383 11.0784C1.14146 11.0908 1.13203 11.1002 1.1257 11.1065C1.12253 11.1097 1.12013 11.1121 1.11853 11.1137C1.11773 11.1145 1.11713 11.1151 1.11672 11.1155C1.11652 11.1157 1.11637 11.1159 1.11627 11.116C1.11622 11.116 1.11618 11.1161 1.11616 11.1161C1.11613 11.1161 1.11612 11.1161 2 12C2.88388 12.8839 2.8839 12.8839 2.88392 12.8838C2.88395 12.8838 2.88398 12.8838 2.88403 12.8837C2.88413 12.8836 2.88429 12.8835 2.88449 12.8833C2.88489 12.8829 2.88549 12.8823 2.8863 12.8815C2.8879 12.8799 2.89029 12.8775 2.89346 12.8743C2.8998 12.868 2.90923 12.8585 2.92159 12.8462C2.94633 12.8214 2.98283 12.7849 3.02988 12.7379C3.12398 12.6438 3.26029 12.5075 3.4291 12.3387C3.76672 12.0011 4.23436 11.5334 4.75446 11.0133C5.79465 9.97312 7.04465 8.72312 7.88389 7.88388L6.11611 6.11612ZM1.11612 2.88388L11.1161 12.8839L12.8839 11.1161L2.88388 1.11612L1.11612 2.88388Z",
        fill: "currentColor"
      }
    )
  }
), L = e(t);
export {
  L as default
};
