import { Metadata } from "next";
import Link from "next/link";
import '@/styles/404.css';

export async function generateMetadata({ params }: any): Promise<Metadata> {
  return {
      title: '404',
      description: 'Page not found',
  }
};

export default function NotFound() {
  return (
    <div id="notfound">
      <div className="notfound">
        <div className="notfound-404">
          <h1>404</h1>
          <h2>Page not found</h2>
        </div>
        <Link href="/" prefetch={false}>Back to homepage</Link>
      </div>
    </div>
  );
}