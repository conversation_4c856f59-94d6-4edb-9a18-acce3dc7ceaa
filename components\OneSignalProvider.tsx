'use client';

import React, { useEffect } from 'react';
import OneSignal from 'react-onesignal';
import { useSelector } from 'react-redux';
import { useUser } from '@/hooks/useUser';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';

interface OneSignalProviderProps {
  children: React.ReactNode;
}

const OneSignalProvider = ({ children }: OneSignalProviderProps) => {
  const { isAuthenticated } = useUser();
  const userData = useSelector((state: any) => state.userInfo);
  const userId = userData?.userId;
  const accountType = userData?.accountType || 'user';

  // Convex mutation for saving device
  const saveUserDeviceMutation = useMutation(api.userDevices.saveUserDevice);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      initializeOneSignal();
    }
  }, []);

  useEffect(() => {
    if (isAuthenticated && userId) {
      // Set external user ID with fallback for different API versions
      try {
        if (OneSignal.User && OneSignal.User.setExternalId) {
          OneSignal.User.setExternalId(userId);
        } else if (OneSignal.setExternalUserId) {
          // Fallback for older API
          OneSignal.setExternalUserId(userId);
        }
      } catch (error) {
        console.error('Error setting external user ID:', error);
      }

      // Get device ID with fallback
      const getDeviceId = async () => {
        try {
          let deviceId: string | null = null;

          if (OneSignal.User && OneSignal.User.getOneSignalId) {
            deviceId = await OneSignal.User.getOneSignalId();
          } else if (OneSignal.User && OneSignal.User.onesignalId) {
            deviceId = await OneSignal.User.onesignalId;
          } else if (OneSignal.getUserId) {
            // Fallback for older API
            deviceId = await OneSignal.getUserId();
          }

          if (deviceId) {
            saveUserDevice(deviceId);
          }
        } catch (error) {
          console.error('Error getting OneSignal device ID:', error);
        }
      };

      getDeviceId();
    }
  }, [isAuthenticated, userId]);

  const initializeOneSignal = async () => {
    try {
      await OneSignal.init({
        appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID!,
        safari_web_id: process.env.NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID,
        notifyButton: {
          enable: false,
          prenotify: true,
          showCredit: false,
          text: {
            "tip.state.unsubscribed": "Subscribe to notifications",
            "tip.state.subscribed": "You are subscribed to notifications",
            "tip.state.blocked": "You have blocked notifications",
            "message.prenotify": "Click to subscribe to notifications",
            "message.action.subscribed": "Thanks for subscribing!",
            "message.action.subscribing": "Subscribing...",
            "message.action.resubscribed": "You are now subscribed to notifications",
            "message.action.unsubscribed": "You will no longer receive notifications",
            "dialog.main.title": "Manage your notification settings",
            "dialog.main.button.subscribe": "SUBSCRIBE",
            "dialog.main.button.unsubscribe": "UNSUBSCRIBE",
            "dialog.blocked.title": "Unblock Notifications",
            "dialog.blocked.message": "Follow these instructions to allow notifications:"
          }
        },
        allowLocalhostAsSecureOrigin: process.env.NODE_ENV !== 'production',
      });

      // Request notification permission
      if (OneSignal.Notifications && OneSignal.Notifications.requestPermission) {
        OneSignal.Notifications.requestPermission();
      } else if (OneSignal.registerForPushNotifications) {
        // Fallback for older API
        OneSignal.registerForPushNotifications();
      }
    } catch (error) {
      console.error('Error initializing OneSignal:', error);
    }
  };

  const saveUserDevice = async (deviceId: string) => {
    try {
      await saveUserDeviceMutation({
        device_id: deviceId,
        account_type: accountType,
      });
    } catch (error) {
      console.error('Error saving device ID:', error);
    }
  };

  return <>{children}</>;
};
export default OneSignalProvider;