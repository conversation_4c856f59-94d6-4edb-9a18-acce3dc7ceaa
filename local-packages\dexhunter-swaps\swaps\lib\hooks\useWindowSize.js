import { useState as w, useEffect as d } from "react";
function r() {
  const e = typeof window > "u", [n, t] = w({
    width: e ? 1200 : window.innerWidth,
    height: e ? 800 : window.innerHeight
  });
  function i() {
    t({ width: window.innerWidth, height: window.innerHeight });
  }
  return d(() => (window.addEventListener("resize", i), () => {
    window.removeEventListener("resize", i);
  }), []), n;
}
export {
  r as default
};
