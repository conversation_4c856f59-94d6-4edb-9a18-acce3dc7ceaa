import n from "../../store/useStore.js";
import { server as T } from "../../config/axios.js";
import { useNotify as U } from "../../hooks/useNotify.js";
import { CARDANO_TOKEN_IDENTIFIER as L } from "../components/tokens.js";
import { convertFromPercentage as B } from "../../utils/formatNumber.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
import "react/jsx-runtime";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../hooks/useScreen.js";
const _t = () => {
  const { notify: u } = U(), {
    tokenBuy: a,
    tokenSell: e,
    sellAmount: d,
    setIsTransactionLoading: p,
    estimationError: S,
    setBuyAmount: A,
    setSellAmount: O,
    setSwapDetails: h,
    swapDetails: r,
    limitPrice: g,
    limitMultiples: c,
    setLimitMultiples: C,
    setIsSwapSubmitted: F
  } = n((t) => t.swapSlice), {
    api: s,
    userAddress: _,
    balance: E
  } = n((t) => t.walletSlice), {
    setUpcomingOrders: I,
    setPendingOrdersCount: k,
    pendingOrdersCount: N
  } = n((t) => t.userOrdersSlice), { slippage: P } = n((t) => t.swapSettingsSlice), m = () => {
    const t = r.total_fee + r.partner_fee, i = parseFloat(d);
    return i + t > E && (e == null ? void 0 : e.token_id) === "" ? parseFloat(i - t) : parseFloat(i);
  };
  return { stopLossToken: async () => {
    var i, f, b, w;
    if (p(!0), d === 0 || S) {
      p(!1);
      return;
    }
    let t = {
      sign: null,
      tx: "",
      err: null,
      step: "pre-create",
      payload: null
    };
    try {
      const o = {
        user_address: _,
        token_in: e == null ? void 0 : e.token_id,
        amount: m(),
        price: parseFloat(g),
        chunks: c || 1,
        max_price_change: B(P)
      };
      t.payload = o;
      const { data: l } = await T.post("/stoploss", o);
      t.swap = l, t.step = "pre-sign";
      const v = await (s == null ? void 0 : s.signTx(l.cbor, !0)), { data: y } = await T.post("/swap/sign", {
        txCbor: l.cbor,
        signatures: v
      });
      t.sign = y, t.step = "pre-submit";
      const x = await (s == null ? void 0 : s.submitTx(y.cbor));
      t.tx = x, t.step = "after-submit", F(!0), u({
        type: "success",
        title: "Order placed",
        desc: "Your order has been placed successfully",
        actionName: "View order",
        actionCallback: () => {
          console.log("callback");
        }
      }), I(r == null ? void 0 : r.splits.map((M) => ({
        ...M,
        tx_hash: x,
        status: "SUBMITTED",
        amount_in: m(),
        token_id_in: (e == null ? void 0 : e.token_id) || L,
        token_id_out: (a == null ? void 0 : a.token_id) || L,
        expected_out_amount: m() * c * parseFloat(g),
        submission_time: (/* @__PURE__ */ new Date()).toISOString(),
        user_address: _,
        upcoming: !0,
        type: (e == null ? void 0 : e.token_id) === "" ? "BUY" : "SELL"
      }))), k(N + (c || ((i = r == null ? void 0 : r.splits) == null ? void 0 : i.length))), O(null), A(0), C(0), h(null);
    } catch (o) {
      if (console.log(o), console.log(o.message), t.err = ((f = o.response) == null ? void 0 : f.data) || o.message || o.info, (b = o.message) != null && b.toLowerCase().includes("declined") || (w = o.info) != null && w.toLowerCase().includes("declined"))
        return;
      u({
        type: "error",
        title: "Error placing order",
        desc: "There was an error placing your order",
        actionCallback: () => {
          navigator.clipboard.writeText(JSON.stringify(t));
        }
      });
    } finally {
      p(!1);
    }
  } };
};
export {
  _t as useStopLossAction
};
