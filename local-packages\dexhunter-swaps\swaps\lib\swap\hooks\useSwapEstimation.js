import A from "../../store/useStore.js";
import { server as S } from "../../config/axios.js";
import { roundNumber as L, getBonusOutput as T } from "../../utils/formatNumber.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
const it = () => {
  const {
    tokenSell: t,
    tokenBuy: r,
    sellAmount: o,
    setIsTokenPriceLoading: g,
    setSwapDetails: c,
    setTokenPrice: w,
    setIsSwapDetailsLoading: y,
    setEstimationError: s,
    isTransactionLoading: h,
    inputMode: d,
    buyAmount: m,
    dexBlacklist: R,
    setBuyAmount: E,
    setSellAmount: P,
    setBonusOutput: I
  } = A((a) => a.swapSlice), { slippage: U } = A((a) => a.swapSettingsSlice), { defaultBuySize: $, partnerCode: B, partnerName: v } = A((a) => a.globalSettingsSlice);
  return { estimateSwap: async ({ signal: a, newTokenSell: f, newTokenBuy: b }) => {
    var p, l, _, D, F, k, N;
    if (h)
      return !0;
    g(!0), y(!0);
    let n = R;
    ((r == null ? void 0 : r.token_id) === "8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa4d494c4b" || (t == null ? void 0 : t.token_id) === "8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa4d494c4b") && ((t == null ? void 0 : t.token_id) === "" && o && o <= 500 && (n = ["WINGRIDER", "SUNDAESWAP", "SPECTRUM", "VYFI"]), (t == null ? void 0 : t.token_id) === "8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa4d494c4b" && o && o <= 50 && (n = ["WINGRIDER", "SUNDAESWAP", "SPECTRUM", "VYFI"]));
    const u = {
      token_in: (p = f || t) == null ? void 0 : p.token_id,
      token_out: (l = b || r) == null ? void 0 : l.token_id,
      slippage: U,
      blacklisted_dexes: n,
      referrer: v
    };
    try {
      if (d === "SELL" && o && parseFloat(o) > 0) {
        const e = {
          amount_in: parseFloat(o || $),
          ...u
        }, { data: i } = await S.post("/swap/estimate", e, {
          signal: a,
          headers: {
            "X-Partner-Id": B
          }
        });
        return delete i.total_input, c(i), s(""), E(L(i.total_output_without_slippage)), I(`+${T(i.possible_routes)} ${r == null ? void 0 : r.ticker}`), !0;
      }
      if (d === "BUY" && m && parseFloat(m) > 0) {
        const e = {
          amount_out: parseFloat(m),
          ...u,
          slippage: 0
        }, { data: i } = await S.post("/swap/reverseEstimate", e, {
          signal: a
        });
        return delete i.total_output_without_slippage, c(i), s(""), P(L(i.total_input)), I(`+${T(i.possible_routes)} ${t == null ? void 0 : t.ticker}`), !0;
      }
      (o === null || parseFloat(o) === 0) && d === "SELL" && (c(null), E(0)), (o === null || parseFloat(m) === 0) && d === "BUY" && (c(null), P(null)), s("");
    } catch (e) {
      if (console.log(e), e.name === "AbortError")
        return !0;
      ((_ = e.response) == null ? void 0 : _.data) === "pool_out_of_sync" && s("Pools Out Of Sync"), ((D = e.response) == null ? void 0 : D.data) === "not_enough_liquidity" && s("Not Enough Liquidity"), ((F = e.response) == null ? void 0 : F.data) === "pool_not_found" && s("Pool Not Found"), ((k = e.response) == null ? void 0 : k.data) === "no_valid_pools_for_trade" && s("Pool Not Found"), ((N = e.response) == null ? void 0 : N.data) === "input_too_small" && s("Input Too Small");
    } finally {
      y(!1);
    }
    return !0;
  }, estimatePrice: async ({ signal: a, newTokenSell: f, newTokenBuy: b }) => {
    var n, u;
    try {
      const p = ((n = f || t) == null ? void 0 : n.token_id) || "ADA", l = ((u = b || r) == null ? void 0 : u.token_id) || "ADA";
      if (p === "ADA" && l === "ADA")
        return !0;
      const { data: _ } = await S.get(
        `/swap/averagePrice/${p}/${l}`,
        { signal: a }
      );
      w(_);
    } catch {
      w(0);
    } finally {
      g(!1);
    }
    return !0;
  } };
};
export {
  it as useEstimateSwap
};
