export declare const CARDANO_TOKEN: {
    token_id: string;
    token_policy: string;
    token_ascii: string;
    is_verified: boolean;
    ticker: string;
};
export declare const DEFAULT_TOKEN: {
    token_id: string;
    token_policy: string;
    token_ascii: string;
    is_verified: boolean;
    ticker: string;
};
export type TokenId = keyof typeof DEFAULT_TOKEN_LIST;
export declare const CARDANO_TOKEN_IDENTIFIER = "000000000000000000000000000000000000000000000000000000006c6f76656c616365";
export declare const DEFAULT_TOKEN_LIST: {
    "": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    undefined: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "000000000000000000000000000000000000000000000000000000006c6f76656c616365": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "95a427e384527065f2f8946f5e86320d0117839a5e98ea2c0b55fb0048554e54": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
        decimals: number;
    };
    a3931691f5c4e65d01c429e473d0dd24c51afdb6daf88e632a6c1e516f7263666178746f6b656e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    '279f842c33eed9054b9e3c70cd6a3b32298259c24b78b895cb41d91a54554e41': {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    '501dd5d2fbab6af0a26b1421076ff3afc4d5a34d6b3f9694571116ea4b4f4e4441': {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "12274673b36fa1507084eae4b9f78d28bd67266396331f46528c397441445548": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "2d92af60ee429bce238d3fd9f2531b45457301d74dad1bcf3f9d1dca564e4d": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ffb1abe9fe93ee9f13874403a3d4f8addaa65fbf22d5d7f41c087d8e4d5554414e54: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f0451f9d13eb8e7c9be861a675d00cc4e0e174f527757b772c4dff18434845: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b34b3ea80060ace9427bda98690a73d33840e27aaa8d6edb7f0c757a634e455441: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "0c92aabef5a8f91a36470d0762806c165c0d04aa992541e25d55486a424347": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6a078f51d4a343c5f9022ebe2fa88975a18cbd58442cb1021a1b900c54524f4c4c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "52489ea87bbceaf6375cc22f74c19382a3d5da3f8b9b15d2537044b95052535052": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    bf524874448cbf52be3a26133b0a0edf5eb65c09ffed383b881ad3274353574150: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4a2e215bced3fd8ccc3edec108347d9f8984ef82bac8c8903f02d1b04144444158": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "3f2880d10da4c8567ffc1f705b4b526c331ed2e875c63c4b68aff5804252454144": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "43b07d4037f0d75ee10f9863097463fc02ff3c0b8b705ae61d9c75bf4d796e746820546f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "94cbb4fcbcaa2975779f273b263eb3b5f24a9951e446d6dc4c135864": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "961f2cac0bb1967d74691af179350c1e1062c7298d1f7be1e4696e312444455250": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c4c00fbd8fa227442a5e7cdecde33b24588494d05a2c50fda8938c6d4b49445a: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e3334680040528178a13026dee65a620be396d4a34b25a524c653af14c495a4f: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6787a47e9f73efe4002d763337140da27afa8eb9a39413d2c39d4286524144546f6b656e73": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    d3a034e403b98cbdb0adbc8a3144d7779330916e190d387815bb85c650555252: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "533bb94a8850ee3ccbe483106489399112b74c905342cb1792a797a0494e4459": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "401f734390bb03578000c28b7c69d818fce14de3caed59c8aaec08ff47494e4b4f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "7bf18ff8bbc7cd4adbf4a31828fc3ada26fe40f52de27394ec36b03e4f544b2052756d": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "91fbce324827fcb04240e95772277225997fde8af6bfebd16b3f381c457370657273": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "43a078846ae28140f5a5c2714ffcc689555d7e0efcfcaf79ce2910fa487964726147616d6573": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "376b46d7abe9a2a8d9d6dfa8f1979c41bc4251f02bbd7bc58acf3e704a6675656c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9d093d73d273ff6456e85bcd9c524fa18fedb0f0274e41d7344a57b4444f4e4f54425559": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ad1c1ed3a0bb6f83630ef052a31b71ba3287bf4bafa8370758f14d315349434b: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a26022096c6a8052987dabbfa94849ab7886cf0bb7840044e017d5be4b756265436f696e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c9281d64953f9531304e57e4ff15111f0c5445167c0b413f70fcd50c52554e45: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "92292852e3820cfbe99874b284fdf2befbddb38e070cf3512009a60a436f6c6f72506561726c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "41a806e6cc38741eb81cc68f6300ee37df0c10b6b99dcb251aa82c3b5a5230": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "279c909f348e533da5808898f87f9a14bb2c3dfbbacccd631d927a3f534e454b": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    eb2fb0975dbd8d4b439eef8f252dbbf7dea998e58186b6a74b5431b353414c5a59: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "65bdf33f8f7fd4debeb2ad659473749eb4eac177e06650bb75a8fe504d69746872546f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "32bc130691066340caf48ef53d52c684a0e497ffc935d2fd6741efe0574f5a": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e1ed70ad8423bc63ac1287f2329c9380528284894dd4cbe70a5c722d5341454c454d: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9f452e23804df3040b352b478039357b506ad3b50d2ce0d7cbd5f806435456": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "79906b9c8d2fbddeba9658387a2a1187f3edd8f546e5dc49225710a146524f47474945": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    af2e27f580f7f08e93190a81f72462f153026d06450924726645891b44524950: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "473e6a5a73108783abdd8b540eb0bca7265741d1f1e8fa55b0e8ef32646567656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "02a4e5d69c39dc69ca5673836545743f425563965af06fe002bdd79043455446": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f66d78b4a3cb3d37afa0ec36461e51ecbde00f26c8f0a68f94b6988069555344: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6767df9b642eb1252ceb2d469a85b93889eae2790acd788ea31fa0834b495441": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "3682715f97086e72ff37a7e6759a5ff0b44c98296afc2b407debe52b524f554e44": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "50009ca6291a2d1069bfb8d01dd2776401fe5d767f603b6cee55ab015241535441": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "354a6c0acd846b195768ead31c92693ad26d82ba013e7df5d9777081514149": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "20cd68533b47565f3c61efb39c30fdace9963bfa4c0060b613448e3c50524f584945": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a00fdf4fb9ab6c8c2bd1533a2f14855edf12aed5ecbf96d4b5f5b9394334: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a8a1dccea2e378081f2d500d98d022dd3c0bd77afd9dbc7b55a9d21b63544f5349: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ce5b9e0f8a88255b65f2e4d065c6e716e9fa9a8a86dfb86423dd1ac044494e47: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "59515c449f1ec1d21a4b2d93013337d13b29d74c3a665a11d5b7fc6644534c5652": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "25c5de5f5b286073c593edfd77b48abc7a48e5a4f3d4cd9d428ff935455448": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    d01794c4604f3c0e544c537bb1f4268c0e81f45880c00c09ebe4b4a74d595354: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9b199c2d7d2f8fe66c8da8b843380c2228d3e92aee64a3ee81d9d50453757065725065736f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a56b1bfd69d317076c5c864d647851e2961594b21ec056f3c84914856473636e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "7536b72f0d1f84c7935fc3b4471032ad4ca33b92974d0a3fe7e03e514d494e474f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    cc8d1b026353022abbfcc2e1e71159f9e308d9c6e905ac1db24c7fb650617269627573: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "91fbce324827fcb04240e95772277225997fde8af6bfebd16b3f381c46726f67436f696e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "057e283baef26fe8879f0a5b56d55893507cee6933c85babcb8db216464441": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a0028f350aaabe0545fdcb56b039bfb08e4bb4d8c4d7c3c7d481c235484f534b59: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "25c5de5f5b286073c593edfd77b48abc7a48e5a4f3d4cd9d428ff935425443": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4a50a3c79cd9365b8eb10e257865f93f0e05d79004b409956fdcb3a85a414c474f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e9c28a71273f825b13f38244ccf1fea97c4025813610d01a7c5d681f436861726c7a20546f6b656e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a1ce0414d79b040f986f3bcd187a7563fd26662390dece6b12262b52464c45534820544f4b454e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e6f464202e7c89befd79fdd3905ca96c896772721485dff66fd6b2d24144414c4f54: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6a659a86a0f35825bb9b0724ee70bc6a090e5910e1423a86a48cc955434150": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "2adf188218a66847024664f4f63939577627a56c090f679fe366c5ee535441424c45": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f2fe9aafd2e5b3b00e2949a96d44a84d9f4d818d63945010a466a4ae4155444954: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e8e6b35d977a56ae5a221786eb0b2b348b8555e6e6afc6a54c25d2c54379636c72: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "86abe45be4d8fb2e8f28e8047d17d0ba5592f2a6c8c452fc88c2c14358524159": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "5d16cc1a177b5d9ba9cfa9793b07e60f1fb70fea1f8aef064415d114494147": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a1fb1a8f55298d54133e96c8edba68244ef14d9e27272bf9b9643e0a465844: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a8a1dccea2e378081f2d500d98d022dd3c0bd77afd9dbc7b55a9d21b63544f534920: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6954264b15bc92d6d592febeac84f14645e1ed46ca5ebb9acdb5c15f5354524950": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b3bd74dd43f83815519e387bdffd1cb9be411df8f2774f48e0fd3669534e455045: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f43a62fdc3965df486de8a0d32fe800963589c41b38946602a0dc53541474958: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "2274b1699f5398170e0497598de7877ebb370ba7b5d25a1d0b2fea075249534b": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e14fe3ab348f9a6198359481472601f4557b9f86984f40a186a3b1e8434845525259: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "389224fd2601bc0d9fcf546430956d8d61b0dc911d6f41d57ccaf7e742414259": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9e975c76508686eb2d57985dbaea7e3843767a31b4dcf4e99e5646834d41595a": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
        decimals: number;
    };
    "8d7cc34c1a44ef419cf1560cbb84e7720ca6c03ab99f8745ab61d19d50414e4441": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9c2a02b3a38380568749ff510a27d8884bd689dd6acbe2acfee4a509576f726c64": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    faf9889f47c0b39177a7e07fe71ee5f5037beb01dcec3d4f2daae586456c657661746f72204c616273: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e71a541df41dd14269bc63d40fb02bc91a3c3d344605948d173d88145748414c45: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "98986c9f88fbe01cb16af3fb85dcca5c876b5eff68aaa1d5e785b2756d6f6e6b6579": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9b426921a21f54600711da0be1a12b026703a9bd8eb9848d08c9d921434154534b59": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e043fd7b2076ea9e1b279d200b59e153bf6b299a72ce6e2c14aeb790424c554553: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "1e54cf3b5832978582ac3baade392fa09a52dbd588dae1a403c4d72c52554d41484b495441": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "2d420236ffaada336c21e3f4520b799f6e246d8618f2fc89a4907da64564756c6164646572546f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8f9c32977d2bacb87836b64f7811e99734c6368373958da20172afba4d5949454c44": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b201928d6bdb21c2e39205a92e226653d6002b949eaaacde3d986c2f524f4e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "86b116d8cfd880445825208b4a355b7715b63763d6d8f3d37338d211657465726e616c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c4c497a70541172b39be5d30c89e07a047b0a75b17adbf41927a5c9748545048: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "78985eeb3babbfd4029864598a9a6064283543ba7551c3cc199ddf775344554d50": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "85d87212f6096732ac1cdbf604a12aa8cfd1fd0b66ab28fc77d3764c44494e4f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    fbae99b8679369079a7f6f0da14a2cf1c2d6bfd3afdf3a96a64ab67a0014df1047454e5358: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c50e06eb7d36ce67af2b15dd38228b59e39a5dae644166c84e3d2d1344554d42: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a2936e00439913f1ac105c29883c013322360247c409343028b831be4d564f5543484552: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    dbc31b04d90b37332813cb4cee3e8f79994643d899a5366797e745ee465544: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "804f5544c1962a40546827cab750a88404dc7108c0f588b72964754f56594649": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "1111170eb2f1fc2eaf1ea6503c0160eda20de2a84418f988720a7c8e4b525950544f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    bf9354cba4ee83c5de05c72830c6430967a26a1656b06293541d23e154414e47: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    da247e71f71b0195c8188bf43827f34c9357cc388fceed596d100d95424f54: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ea2d23f1fa631b414252824c153f2d6ba833506477a929770a4dd9c24d414442554c: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "0171c997b8853fde686763d93b36ab8e04ce947bb6aa09a9ee5c4401544f4b454e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f01ec1cb021922a491ea300fb4791dbaca720372b2a3142579c52e7d4b616e69: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "7192cee89b2cd731152e60de5fd849f3930c24ac97f6daca09b54cf35065616365": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b38d94e35b3e638dce7e5e0b0aeebcbe06987a118b46d3d4c042c8c26861726d6c657373636f696e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ddbca1e3ee9849c439e13c2cc3daebea6ad17a04df11bc0aad3b76ae534848: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "94cbb4fcbcaa2975779f273b263eb3b5f24a9951e446d6dc4c13586452455655": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "43a078846ae28140f5a5c2714ffcc689555d7e0efcfcaf79ce2910fa4879647261": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "92292852e3820cfbe99874b284fdf2befbddb38e070cf3512009a60a426c61636b506561726c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4632693c8a8ab3e325391bf319d69c24c54b823d70a85d870197fd3853414d55": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "462d81b809c4cbc9039a536a6139393d640e3f54c563c0404ef92a5f57415249": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "50861cf745ea40fb79024380fa3b642152a47cb3acae2778a517f16357494c44": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "5e607fcf4933ae7a713a4d190ace86832c67e610eab8c27f71ae746a456e64": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "25c5de5f5b286073c593edfd77b48abc7a48e5a4f3d4cd9d428ff93557414e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c30190b33eff1af6566201bed3400676d8a8e61ebb3180c14e90734e4d494c41445943: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    dc280dbb5381ef28afbab4ca31751f13c29562d376998e907ff1af32474e5547: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c5f87616092bc2595960b3f87c8760703cc5afb87b3a025d9ae6f70470414441: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b1a80ea5d4b5c9f8d550fb9fa9fe53433903f420a449c8977b3470fa4245414d: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6f46e1304b16d884c85c62fb0eef35028facdc41aaa0fd319a152ed64d434f53": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "25f0fc240e91bd95dcdaebd2ba7713fc5168ac77234a3d79449fc20c534f4349455459": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b4273f1d8d0021d188784c3d72dc514f602b7ab8cfb87e3134550c42446f6765414441: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8cfd6893f5f6c1cc954cec1a0a1460841b74da6e7803820dde62bb78524a56": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a2944573e99d2ed3055b808eaa264f0bf119e01fc6b18863067c63e44d454c44: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "682fe60c9918842b3323c43b5144bc3d52a23bd2fb81345560d73f634e45574d": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "0fd9819a9d7fb414880883f43a42d33458f12bc5f9841cec6457dc155669527553": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "09f2d4e4a5c3662f4c1e6a7d9600e9605279dbdcedb22d4507cb6e75535046": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "29d222ce763455e3d7a09a665ce554f00ac89d2e99a1a83d267170c64d494e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c0ee29a85b13209423b10447d3c2e6a50641a15c57770e27cb9d507357696e67526964657273: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "5ec2e9813fa385d9333d18186d8257d1b3ebea97bdec2dad74026d8d50554743484950": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9f144f7c7c9e656f905c09442e08376dbb6ee0ef80dd8afe0a4fc07347726f6f7065657a546f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a5039068cb2b3ebcf874c0a4b012f19409befcf99e38c34c080af9d552686f6469756d436f696e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    efa0e694d5915f52b00a0d01135efdb85a0bdde7009bbcf68ea12a3d5456: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "24a87872afa67f8860b7f6e910a63ddaffe39ed3a8affb04981eeaa9434f544f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f66d78b4a3cb3d37afa0ec36461e51ecbde00f26c8f0a68f94b6988069425443: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "160a880d9fc45380737cb7e57ff859763230aab28b3ef6a84007bfcc4d495241": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "133fac9e153194428eb0919be39837b42b9e977fc7298f3ff1b76ef95055444759": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4ffaa4ef3217df37c4995bb96066af4cb68dfcc66b9f2a10e0c333b95779726d73746f6e65": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4fde92c2f6dbcfa2879b44f7453872b31394cfb2f70f1d4c411169ac427562626c65": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "5b01968867e13432afaa2f814e1d15e332d6cd0aa77e350972b0967d4144414f476f7665726e616e6365546f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "1d7f33bd23d85e1a25d87d86fac4f199c3197a2f7afeb662a0f34e1e776f726c646d6f62696c65746f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "95b1962fbb6c42c1a16a3ac60459806e7ee7dba4f845e4498082524f4e474d49": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "884892bcdc360bcef87d6b3f806e7f9cd5ac30d999d49970e7a903ae5041564941": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "7192cee89b2cd731152e60de5fd849f3930c24ac97f6daca09b54cf34c6f7665": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "815418a1b078a259e678ecccc9d7eac7648d10b88f6f75ce2db8a25a4672616374696f6e2045737461746520546f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9abf0afd2f236a19f2842d502d0450cbcd9c79f123a9708f96fd9b96454e4353": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    cfbc6b8519f252b1f1694cdbb9df9f339b343bdd0f2079d84e4afdd941504543415348: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "23ac1afe924ebbc002cbc7afdf22f09a9ea8fb8bddd3b20398be71c2534841524b": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "25c5de5f5b286073c593edfd77b48abc7a48e5a4f3d4cd9d428ff93555534454": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "0c3b14ddb319c5a50bfd93e69d0706d1527e2b2169744665a1c534d0635049474759": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "88691a70bb0fe49cf9124b4f78553c36c09fa6264844e2b2941191734575736b6f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    cdaaee586376139ee8c3cc4061623968810d177ca5c300afb890b48a43415354: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8654e8b350e298c80d2451beb5ed80fc9eee9f38ce6b039fb8706bc34c4f4253544552": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "521ec1ae6f87ec944a4b32890d62cd4a243d612f80fe26db999628f05757": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "2c85a478d53f0e484b852c357e56057dfd8e80a6b72ecb4daffe42e544444f53": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    df1d850c46d6c9d12cbf6181c35db9225a91b77c8a646b7f636f8ae40014df104e494e4a415a: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "25c5de5f5b286073c593edfd77b48abc7a48e5a4f3d4cd9d428ff93555534443": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ad1c1ed3a0bb6f83630ef052a31b71ba3287bf4bafa8370758f14d315349434b5349434b: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6ded36273214629f9ff35e9f9c7b8945d937a80a3796ddc4a646a50e485943": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "772791eb3f4b92874a49d487375a90db631988291c1a643b817668ca4d616420446f6720546f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8db269c3ec630e06ae29f74bc39edd1f87c819f1056206e879a1cd61446a65644d6963726f555344": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ee0633e757fdd1423220f43688c74678abde1cead7ce265ba8a24fcd43424c50: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    fed1c459a47cbff56bd7d29c2dde0de3e9bd15cee02b98622fce82f743617264616e6f476f6c64: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "2edc63d29e94747f7ef65df559260c994d2ecfe208ac6afa564517384b494e47": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4d8469c80677574a5dd23b0fdd0eb0057fb6ce4e20cf21c7e8655f954348524f4d41": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "29d222ce763455e3d7a09a665ce554f00ac89d2e99a1a83d267170c64d494e74": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "322490202fbfba723c837c54695d80b61c79ed39dee31dd492391f4b4d6f6e6579": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "10a49b996e2402269af553a8a96fb8eb90d79e9eca79e2b4223057b64745524f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4f771973824f79fff176fe5cc0f29f5f5331a4692e5aa9a0bd0cdde6415058": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "09f5f55fcad17503e6b7acc81de7c80f84b76e76d17085f0e32f1ce241574f4f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b6a7467ea1deb012808ef4e87b5ff371e85f7142d7b356a40d9b42a0436f726e75636f70696173: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "078eafce5cd7edafdf63900edef2c1ea759e77f30ca81d6bbdeec92479756d6d69": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c79a25118b06dddc9094a547dd534046ddf9efde264ae1ab9cdf777642756c6c79: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c490a79c6afd84c5e5dfe487a5fe8b6009c239befff69745c43711ef524f434b4554: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    d1407478fb3151f60fc533cd42d806dd7115cecfa25a018baf53c4dc415246: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8fef2d34078659493ce161a6c7fba4b56afefa8535296a5743f6958741414441": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6c8642400e8437f737eb86df0fc8a8437c760f48592b1ba8f5767e81456d706f7761": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "2441ab3351c3b80213a98f4e09ddcf7dabe4879c3c94cc4e7205cb6346495245": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4623ab311b7d982d8d26fcbe1a9439ca56661aafcdcd8d8a0ef31fd6475245454e53": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8db269c3ec630e06ae29f74bc39edd1f87c819f1056206e879a1cd615368656e4d6963726f555344": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "92292852e3820cfbe99874b284fdf2befbddb38e070cf3512009a60a47726179506561726c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    edfd7a1d77bcb8b884c474bdc92a16002d1fb720e454fa6e993444794e5458: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "755a497e592fb6a20337d19d86e1da82d27282db5c9083e0ddaa338c47414c415859": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    fccb53cf9d12b07a148c40b349c857bfe2b413cef5cbcf5e6afe7351434f4f4e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "74be1f6940dadc4ab85c643d7447b45cf4e1b5e9cfd5fed062e903f863617264616e6f2e626c7565": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "23690795536ccf407ed6eeac57db98db9668b3a026991fb516ecd7ef0014df10534d4f4b4553": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "482fb00dc32186a4c587dca2df3c7cf2bc455332ab581d51967306e14d4f4149": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c448753e6dac6d4d3508e47f636122c715e3c8575955ae77307b305f696e76616461: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "89267e9a35153a419e1b8ffa23e511ac39ea4e3b00452e9d500f2982436176616c6965724b696e67436861726c6573": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4342a3d3c15545a592bf38294dc75c7a1dd3550388303e3a06f4416d4345525241": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8499f3958a114d03b6289185138a60378951a2b5491cb2a080ff7b6e504d58": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9a9693a9a37912a5097918f97918d15240c92ab729a0b7c4aa144d7753554e444145": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "64c3ebd40ed377989aa3069a2936e07c6ce82df46688c473d921520664676166": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ca942cb8bb5d1ef750766ded355f320880539111f10efa2b1a478ff9524147: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "0fc1a2c01b3b96e6fc18681852c26d904b4ca180150671e1b250369f464f524745": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4756b1e7626e84c6de4fe4256b874412c5529a8e75b707ba1e677f4642455043": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "454669a208d486fe98c6eca9070c62df7fc8449d1bf6237a5fe1aa11465849": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "07ccfad78099fef727bfc64de1cf2e684c0872aab3c3bb3bed5e1081706565706565": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "87a59599f8f47db431de642329a275cd4b297f21f1517af9db442cee506f705570576f726c64": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    bbd0ec94cf9ccc1407b3dbc66bfbbff82ea49718ae4e3dceb817125f24574f524b: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ace2ea0fe142a3687acf86f55bcded860a920864163ee0d3dda8b60252414b4552: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "2b28c81dbba6d67e4b5a997c6be1212cba9d60d33f82444ab8b1f21842414e4b": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "681b5d0383ac3b457e1bcc453223c90ccef26b234328f45fa10fd2764a5047": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
        decimals: number;
    };
    b6a7467ea1deb012808ef4e87b5ff371e85f7142d7b356a40d9b42a0436f726e75636f70696173205b76696120436861696e506f72742e696f5d: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    db30c7905f598ed0154de14f970de0f61f0cb3943ed82c891968480a434c4150: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8be741cb809416f0785837d00a0fba01de42e3580d96e378f82b24ab427574746572": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "7e4f764cd4283d99b2bbf532d80e8105c3ae58a21b089bce8068170b4745434b": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "0d90046ad35546156aaf790525133f7fc713ca2790e397784b85f5c85554494c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4ed5b703c146c25c09516f4b83aab31f44f7c55cdc159866e49731534f504f43": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "38ad9dc3aec6a2f38e220142b9aa6ade63ebe71f65e7cc2b7d8a8535434c4159": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "885742cd7e0dad321622b5d3ad186797bd50c44cbde8b48be1583fbd534b554c4c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa4d494c4b": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a1b284d7218dd63772c67ca26ab73721a196b404929cddef595f9967574f4a414b424c5545: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "52162581184a457fad70470161179c5766f00237d4b67e0f1df1b4e65452544c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "5acc52d5696e52345aec108468050d9d743eb21d6e41305bbc23a27b4154484f4d": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "1b99120f61cd9c30938661b5144a352857431a14b2334faef8a180c7424644526f636b6574436f696e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c69b36d2939ca42bf3d8c1dae2bfed9e8a4c34dc3f002ff3782227c044414e5a4f: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "96abe9a17b65c2133ed7e11864338e965efc418c0961ceecfac1617259455449": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "5dac8536653edc12f6f5e1045d8164b9f59998d3bdc300fc928434894e4d4b52": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "92292852e3820cfbe99874b284fdf2befbddb38e070cf3512009a60a5768697465506561726c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    e88e1e4d7e5c807a4c325d7d7d6bba98eaa4d53c2b8c0ff5ae5187326444: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6ac8ef33b510ec004fe11585f7c5a9f0c07f0c23428ab4f29c1d7d104d454c44": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "71ccb467ef856b242753ca53ade36cd1f8d9abb33fdfa7d1ff89cda3434643": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    d244fcb1d038951eb4fcdba0cc77299453ec96d18ac786ccd284cc784c657879: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    fed1c459a47cbff56bd7d29c2dde0de3e9bd15cee02b98622fce82f743617264616e6f53696c766572: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f66d78b4a3cb3d37afa0ec36461e51ecbde00f26c8f0a68f94b6988069455448: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "94a21344f388a259dc8b1f3bcf91d9439379dd748b18a7168ed0b35976414441": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    dda5fdb1002f7389b33e036b6afee82a8189becb6cba852e8b79b4fb0014df1047454e53: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    fffb818d86ad49314e29379db26680c9669002ce72f2f1f6c2f761ce466f787879: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4190b2941d9be04acc69c39739bd5acc66d60ccab480d8e20bc87e3763425443": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "7ced2090df97de4a4c25a6be62a916cfdd4ad794a828fbb743b2d56e444544": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    eef4f770d16d2d4c09f0eae47eec8a6c13244d772f45ee41fc822c944352444f: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "75fcc276057db5fc48eae0e11453c773c8a54604c3086bf9d95ac1b743485259": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b9168f05e657b6946fede254e383586cf7e7a2573d5a0fa12b3ef6ac494450: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "88c7fe7cf68625fd24dc3405432684ae85da094dceff25f81d6664fd5a4f4d424945": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "007394e3117755fbb0558b93c54ce3bc6c85770920044ade143dc742505443": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "16fdd33c86af604e837ae57d79d5f0f1156406086db5f16afb3fcf5144474f4c44": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9d86482511c8a49f21d5c83ee58a49e2926f70ac57dd73ed204042697175657374": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "7914fae20eb2903ed6fd5021a415c1bd2626b64a2d86a304cb40ff5e4c494649": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "07f019ce45fb638353258bda4316ce5eb3d0f76a3fb739c45174084953414c5459": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "438514ae1beb020d35e5389993447cea29637d6272c918017988ef364164615969656c64": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "3ee8e0824a7a81bdc5bcacd004c25d3264dd8ac3eb4ab877b5d7d0ce6b656b77": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "37a2b93b4aa2b286b92b7099faff0bb87e0c629a5079c0584da2d5774f534d": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    afc910d7a306d20c12903979d4935ae4307241d03245743548e767834153484942: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "91fbce324827fcb04240e95772277225997fde8af6bfebd16b3f381c50757061436f696e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "15637826ecbdb4e5747fd41f830d9735a8c4e6d927e44d29ceef9e885a4f4f": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    b0af30edf2c7f11465853821137e0a6ebc395cab71ee39c24127ffb44e465443: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f4d97191f857096b441a410c036f63d6697dde0c71d2755dd664e3024d4b41: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "91fbce324827fcb04240e95772277225997fde8af6bfebd16b3f381c44696d696e7574697665436f696e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "21abdf54f427b378fe9ba07419eff6e8e8fe0c5932e1fee2d3853b9350455045424c5545": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f6ac48c64aa7af16434d9f84e014d11fba38525b436acc338ff20b0d4d7463: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8f52f6a88acf6127bc4758a16b6047afc4da7887feae121ec217b75a534e4f57": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "12d5f4fefe222d52a4fdcee56f4b272911d7c2202b068a08ebf5327049414d58": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8daefa391220bd0d8d007f3748d870f7f3c106040314c8515ccc35a5464c4143": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "585cfcbdd0786e961187999e5d5d36b38d1ebc1c4112a0a95a8bd477424c43": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    f4364875e75320d405ceadebdf0db63fadaff55c72d4ff6b82f0676a434152474f: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ddeec4b6d0c03b6ba4c6135e8e81031748554a5626c23363878f53dd44554d50: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c8764cd53485b29f6ad94bf69f399ac72903b4df1e70d6a119d6596e4859445241: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "09f5f55fcad17503e6b7acc81de7c80f84b76e76d17085f0e32f1ce22d6d2041574f4f0a": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    bb4cfbe0f6be60b80f90f815e8353b93431de4df785d75350b9d214a48455242: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "1a71dc14baa0b4fcfb34464adc6656d0e562571e2ac1bc990c9ce5f6574f4c46": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "493136618a39dcefb89ff573061f715ee32e88092d0299c54e3a377356595241": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    fce30a7f7d16e48c69a7847ec908fe098a0efa0dbc99bad8e2aec15a69646f6c: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6106ab6d8912a8f7e08b5d3300d5f8bdd6371793caceee7e0859868547617279546865436c6f776e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    fed1c459a47cbff56bd7d29c2dde0de3e9bd15cee02b98622fce82f743617264616e6f436f70706572: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ab3e31c490d248c592d5bb495823a45fd10f9c8e4f561f13551803fb43617264616e6f20436f6d6d756e697479204368617269747920436f696e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    ee7ffc3c9402a4b75de30574d631546492bb4a448cc8966fd994ba30435241434b: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6d03347cbf9d02ace480933170b190844f61ead0477c0bd0e53bb4dd5354494e4b59": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "47959e79846b8bdcacb91f586408d97e2dff44f31a04f03902cba8185357454554": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    c7dcfa416c127f630b263c7e0fe0564430cfa9c56bba43e1a37c6915474f4b4559: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    fb0e9a083ac66c814548002cbdfc54557e064e4cdf5c6675e72d22b44445414e: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "12274673b36fa1507084eae4b9f78d28bd67266396331f46528c397461647568": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    a4da8764a57e66a0085b5bfcde96c89b798d92ee83a75f59237e375b46495245: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "5612bee388219c1b76fd527ed0fa5aa1d28652838bcab4ee4ee63197446973636f696e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "4f9f1653cd6fc1bbcd2f5512d5c92bfacd5573455c56723cf029beb6434152524f5453": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "0a0a43129b17470da7b60dcbbeb5a7c2007fe58bd64440dbbe5f7731756e697665727365": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "8e51398904a5d3fc129fbf4f1589701de23c7824d5c90fdb9490e15a434841524c4933": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "905da53004cfee0b8549285949aebedf82b5c6bb9a412cbd9658c6de434343": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "6f0d19b97761a48b53438e926c63a2a75ec1aaa3f763a4f7070c258942524557": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    d5dec6074942b36b50975294fd801f7f28c907476b1ecc1b57c916ed524154: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "782c158a98aed3aa676d9c85117525dcf3acc5506a30a8d87369fbcb4d6f6e6574": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "1ddcb9c9de95361565392c5bdff64767492d61a96166cb16094e54be4f5054": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "3fc6548aed99326821131fc480ec65a03d01efe92f58978e24ab8e795445434b454c": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "5b01968867e13432afaa2f814e1d15e332d6cd0aa77e350972b0967d476f76546f6b656e": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    da8c30857834c6ae7203935b89278c532b3995245295456f993e1d244c51: {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "358e87af871fa88479b5e07f1744e2f866831f43a779e696ef945ee74d4548": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9d9ffb66370242f89e75cbed564d2d7b9b08eab35d9cc2ab5e25a844424c4f434b": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "91fbce324827fcb04240e95772277225997fde8af6bfebd16b3f381c4469676974616c4b617368": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "586c476d22fdff736e0b399485eb9fb9803a941b0b676f1b4c8d7c4345444f54": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
    "9ff14600a3fdaee6da3b1e3d227e2235d1fad199c3691ba2c7d8665a4d415253": {
        token_id: string;
        token_policy: string;
        token_ascii: string;
        is_verified: boolean;
        ticker: string;
    };
};
export declare const DEFAULT_TOKEN_NAME_TO_ID_MAPPING: {
    ada: string;
    fact: string;
    tuna: string;
    konda: string;
    $aduh: string;
    edot: string;
    vnm: string;
    mutant: string;
    che: string;
    cneta: string;
    bcg: string;
    troll: string;
    prspr: string;
    addax: string;
    bread: string;
    mnt: string;
    revu: string;
    derp: string;
    kidz: string;
    lizo: string;
    rad: string;
    $purr: string;
    indy: string;
    ginko: string;
    rum: string;
    wesp: string;
    hydrgms: string;
    jfuel: string;
    dnb: string;
    bukele: string;
    sick: string;
    kube: string;
    rune: string;
    cp: string;
    zro: string;
    snek: string;
    salzy: string;
    mithr: string;
    woz: string;
    saelem: string;
    ctv: string;
    froggie: string;
    drip: string;
    deg: string;
    cetf: string;
    iusd: string;
    kita: string;
    round: string;
    rasta: string;
    qai: string;
    teckel: string;
    proxie: string;
    c4: string;
    ctosi: string;
    ding: string;
    dslvr: string;
    "": string;
    myst: string;
    superpeso: string;
    cmc: string;
    mingo: string;
    pbx: string;
    wfrog: string;
    fda: string;
    hosky: string;
    zalgo: string;
    chrlz: string;
    flesh: string;
    adalot: string;
    cap: string;
    stable: string;
    audit: string;
    cyclr: string;
    xray: string;
    iag: string;
    fxd: string;
    strip: string;
    snepe: string;
    agix: string;
    risk: string;
    cherry: string;
    baby: string;
    mayz: string;
    panda: string;
    world: string;
    elv: string;
    whale: string;
    amonkey: string;
    catsky: string;
    blues: string;
    rk: string;
    eladr: string;
    myield: string;
    ron: string;
    eternal: string;
    htph: string;
    dump: string;
    dino: string;
    gensx: string;
    dumb: string;
    mvoucher: string;
    fud: string;
    vyfi: string;
    kc: string;
    tang: string;
    bot: string;
    madbl: string;
    jpg: string;
    token: string;
    kani: string;
    peace: string;
    harm: string;
    shh: string;
    hydra: string;
    bp: string;
    hunt: string;
    samu: string;
    wari: string;
    wild: string;
    end: string;
    ladyc: string;
    gnug: string;
    pada: string;
    beam: string;
    mcos: string;
    society: string;
    soc: string;
    dgada: string;
    rjv: string;
    meld: string;
    newm: string;
    virus: string;
    min: string;
    wrt: string;
    pugchip: string;
    nfa: string;
    rhod: string;
    tv: string;
    coto: string;
    ibtc: string;
    cerra: string;
    mira: string;
    pudgy: string;
    wyrm: string;
    bubble: string;
    adao: string;
    hyc: string;
    wmt: string;
    ngmi: string;
    pavia: string;
    love: string;
    fet: string;
    encs: string;
    apecash: string;
    shark: string;
    cpiggy: string;
    pmx: string;
    eus: string;
    $cast: string;
    $lobster: string;
    ww: string;
    ddos: string;
    ninjaz: string;
    md: string;
    djed: string;
    lexy: string;
    cblp: string;
    cd: string;
    king: string;
    chroma: string;
    mint: string;
    money: string;
    gero: string;
    apx: string;
    awoo: string;
    yummi: string;
    bd: string;
    cswap: string;
    rocket: string;
    arf: string;
    lenfi: string;
    emp: string;
    fire: string;
    greens: string;
    shen: string;
    gp: string;
    ntx: string;
    galaxy: string;
    coon: string;
    cblue: string;
    smokes: string;
    moai: string;
    invada: string;
    charly: string;
    sundae: string;
    dgaf: string;
    rag: string;
    forge: string;
    bepc: string;
    fxi: string;
    peepee: string;
    puw: string;
    work: string;
    raker: string;
    bank: string;
    copi: string;
    clap: string;
    butter: string;
    geck: string;
    util: string;
    clay: string;
    skull: string;
    milk: string;
    wojakblue: string;
    trtl: string;
    ath: string;
    bfd: string;
    danzo: string;
    yeti: string;
    nmkr: string;
    wp: string;
    dd: string;
    cfc: string;
    ieth: string;
    vada: string;
    gens: string;
    cbtc: string;
    ded: string;
    crdo: string;
    chry: string;
    idp: string;
    zombie: string;
    ptc: string;
    dgold: string;
    qts: string;
    lifi: string;
    salty: string;
    ady: string;
    kekw: string;
    osm: string;
    spf: string;
    ashib: string;
    wppcn: string;
    zoo: string;
    nftc: string;
    mka: string;
    wdimi: string;
    mtc: string;
    snow: string;
    iamx: string;
    flac: string;
    blc: string;
    cargo: string;
    herb: string;
    wolf: string;
    vyra: string;
    idol: string;
    $garycbdc: string;
    cccc: string;
    crack: string;
    $stinky: string;
    sweet: string;
    gokey: string;
    dean: string;
    aduh: string;
    disco: string;
    carrots: string;
    one: string;
    c3: string;
    ccc: string;
    $brew: string;
    rat: string;
    monet: string;
    opt: string;
    tadao: string;
    lq: string;
    opoc: string;
    meh: string;
    block: string;
    adkash: string;
    mars: string;
};
export type TokenName = keyof typeof DEFAULT_TOKEN_NAME_TO_ID_MAPPING;
export declare const PRIORITY_TOKEN_IDS: {
    [key: string]: boolean;
};
export type supportedTokensType = [TokenId, ...TokenId[]];
