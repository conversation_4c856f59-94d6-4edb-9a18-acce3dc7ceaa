'use client';

import { AnimatedModal } from "@/components/ui/animated-modal";
import { Button } from "@/components/ui/button";
import { useState } from 'react';
import { toast } from 'react-toastify';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useRouter } from 'next/navigation';
interface BlockUserDialogProps {
    isOpen: boolean;
    onClose: () => void;
    userId: string;
    username: string;
    profilePhoto?: string;
}

const BlockUserDialog = ({ isOpen, onClose, userId, username, profilePhoto }: BlockUserDialogProps) => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [alreadyBlocked, setAlreadyBlocked] = useState(false);

    const blockUser = useMutation(api.users.blockUser);
    const router = useRouter();

    const handleBlock = async () => {
        if (!userId || !username) {
            toast.error('User information is missing. Cannot block user.');
            setIsSubmitting(false);
            return;
        }
        setIsSubmitting(true);
        try {
            const result = await blockUser({ blockedUserId: userId });
            if (result?.alreadyBlocked) {
                setAlreadyBlocked(true);
                toast.info(`${username} is already blocked.`);
            } else {
                toast.success(`Successfully blocked ${username}`);
                onClose();
            }
        } catch (error) {
            toast.error('Failed to block user');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <AnimatedModal
            open={isOpen}
            onOpenChange={onClose}
            size="md"
            closeButton={true}
            header={null}
            footer={null}
        >
            <div className="flex flex-col items-center justify-center pt-8 pb-2 px-6">
                {/* Profile Picture */}
                <div className="w-32 h-32 rounded-full border-solid border-4 border-white [box-shadow:0_0_2px_rgba(0,_0,_0,_.60)] mb-2 bg-white flex items-center justify-center overflow-hidden">
                    <img
                        src={profilePhoto || '/images/user/default-avatar.webp'}
                        alt="User profile"
                        className="w-full h-full object-cover rounded-full"
                    />
                </div>
                <div className="w-full text-center mb-2">
                    <div className="text-2xl font-bold text-gray-800 dark:text-white mt-2">Block User</div>
                </div>
                {alreadyBlocked ? (
                    <div className="w-full text-center text-gray-700 dark:text-gray-200 mb-4">
                        <div className="text-lg font-semibold mb-2">{username} is already blocked.</div>
                        <div className="text-sm mb-4">You have already blocked this user. They cannot see your posts, message you, or interact with you.</div>
                        <Button onClick={onClose} className="w-full mt-2">Close</Button>
                    </div>
                ) : (
                    <>
                        <div className="w-full text-left text-gray-700 dark:text-gray-200 mb-4">
                            Are you sure you want to block <span className="font-semibold">{username}</span>?<br />
                            They will no longer be able to:
                            <ul className="list-disc list-inside mt-2 text-left text-sm w-full">
                                <li>See your posts</li>
                                <li>Message you</li>
                                <li>Follow you</li>
                                <li>Comment on your posts</li>
                                <li>See your posts on your timeline</li>
                                <li>Tag you</li>
                                <li>Invite you to events or groups</li>
                                <li>Message you</li>
                                <li>Add you as a friend</li>
                            </ul>
                        </div>
                        <div className="w-full flex flex-row items-center justify-center gap-2 mt-6 mb-2">
                            <Button
                                onClick={handleBlock}
                                disabled={isSubmitting || !userId || !username}
                                className="w-1/2 bg-red-500 hover:bg-red-600 text-white"
                            >
                                {isSubmitting ? 'Blocking...' : 'Yes, Block'}
                            </Button>
                            <Button
                                variant="outline"
                                onClick={onClose}
                                className="w-1/2"
                            >
                                No, Cancel
                            </Button>
                        </div>
                    </>
                )}
            </div>
        </AnimatedModal>
    );
};
export default BlockUserDialog;