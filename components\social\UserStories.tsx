'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import Tippy from '@tippyjs/react';
import { motion, useMotionValue, useAnimation, PanInfo } from 'framer-motion';
import StoryViewer, { Story as StoryType } from '@/components/social/StoryViewer';
import { Story } from '@/types/story';
import { mockStories as mockData } from '@/data/mockStories'; // Adjust the import path as necessary

interface UserStoriesProps {
  profileUserId: string;
  isCurrentUser: boolean;
  stories?: Story[];
  onViewStory?: (storyId: string) => void;
  onCreateStory?: () => void;
}

const UserStories: React.FC<UserStoriesProps> = ({
  profileUserId,
  isCurrentUser,
  stories = [],
  onViewStory,
  onCreateStory,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Motion values for drag gesture
  const x = useMotionValue(0);
  const controls = useAnimation();

  // Track container dimensions
  const [containerWidth, setContainerWidth] = useState(0);
  const [scrollWidth, setScrollWidth] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  // StoryViewer state variables
  const [storyViewerOpen, setStoryViewerOpen] = useState(false);
  const [selectedStoryId, setSelectedStoryId] = useState<string>('');
  const [selectedUserId, setSelectedUserId] = useState<string>('');

  // Standardized mock stories
  const mockStories: Story[] = stories.length > 0 ? stories : mockData;

  // Create formatted stories for the StoryViewer
  const formattedStories: StoryType[] = mockStories
    .filter(story => story && (Array.isArray(story.media) || (story.mediaUrl && story.mediaType)))
    .map(story => {
      const media = Array.isArray(story.media)
        ? story.media.map((media, index) => ({
          id: media.id || `${story.id}-media-${index + 1}`,
          mediaUrl: media.mediaUrl,
          thumbnailUrl: media.thumbnailUrl,
          mediaType: media.mediaType,
          duration: media.duration || 5,
        }))
        : [{
          id: `${story.id}-media-1`,
          mediaUrl: story.mediaUrl!,
          thumbnailUrl: story.thumbnailUrl || story.mediaUrl!,
          mediaType: story.mediaType!,
          duration: story.mediaType === 'video' ? 10 : 5,
        }];

      return {
        id: story.id,
        userId: story.userId,
        username: story.username,
        profileImage: story.profileImage,
        media,
        user: {
          id: story.user.id,
          username: story.user.username,
          profileImage: story.user.profileImage,
          isVerified: story.user.isVerified,
          timeAgo: story.user.timeAgo,
        },
        createdAt: story.createdAt,
        expiresAt: story.expiresAt,
        isLive: story.isLive,
        timeAgo: story.timeAgo,
        isViewed: story.isViewed,
      };
    });

  // Simulate loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Update container dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current && scrollContainerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
        setScrollWidth(scrollContainerRef.current.scrollWidth);
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, [mockStories]);

  // Calculate drag constraints
  const dragConstraints = {
    right: 0,
    left: scrollWidth > containerWidth ? -(scrollWidth - containerWidth) : 0,
  };

  // Handle story click
  const handleStoryClick = (storyId: string) => {
    if (!isDragging) {
      const story = mockStories.find(s => s.id === storyId);
      if (story && (Array.isArray(story.media) || (story.mediaUrl && story.mediaType))) {
        const mediaId = Array.isArray(story.media)
          ? story.media[0].id
          : `${story.id}-media-1`;
        setSelectedStoryId(mediaId);
        setSelectedUserId(story.userId);
        setStoryViewerOpen(true);

        if (onViewStory) {
          onViewStory(storyId);
        }
      }
    }
  };

  const handleStoryViewed = (storyId: string) => {
    console.log(`Story media viewed: ${storyId}`);
    // Update state to mark specific media as viewed if needed
  };

  const handleStoryExpired = (storyId: string) => {
    console.log(`Story media expired: ${storyId}`);
    // Update state if needed
  };

  // Handle scroll buttons
  const handleScroll = (direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return;

    const scrollAmount = Math.min(containerWidth * 0.7, 300); // Limit scroll amount
    const currentX = x.get();
    let newX = direction === 'left' ? currentX + scrollAmount : currentX - scrollAmount;

    // Apply constraints
    newX = Math.max(dragConstraints.left, Math.min(dragConstraints.right, newX));

    // Only animate if the new position is different from current
    if (Math.abs(newX - currentX) > 1) {
      controls.start({
        x: newX,
        transition: { type: 'spring', stiffness: 400, damping: 35 },
      });
    }
  };

  // Handle drag start
  const handleDragStart = () => {
    setIsDragging(true);
  };

  // Handle drag end with inertia
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false);

    // Apply inertia based on velocity
    const velocity = info.velocity.x;
    const currentX = x.get();

    // If velocity is high enough, apply inertia
    if (Math.abs(velocity) > 500) {
      const projection = currentX + velocity * 0.2;
      const clampedProjection = Math.max(dragConstraints.left, Math.min(dragConstraints.right, projection));

      controls.start({
        x: clampedProjection,
        transition: {
          type: 'spring',
          velocity: velocity * 0.01,
          stiffness: 300,
          damping: 40,
        },
      });
    } else {
      // Just make sure we're within constraints
      const clampedX = Math.max(dragConstraints.left, Math.min(dragConstraints.right, currentX));

      if (clampedX !== currentX) {
        controls.start({
          x: clampedX,
          transition: { type: 'spring', stiffness: 500, damping: 30 },
        });
      }
    }
  };

  const [atStart, setAtStart] = useState(true);
  const [atEnd, setAtEnd] = useState(false);

  // Update atStart/atEnd when x changes
  useEffect(() => {
    const unsubscribe = x.onChange((latest) => {
      setAtStart(Math.abs(latest) < 5);
      setAtEnd(dragConstraints.left < 0 && Math.abs(latest - dragConstraints.left) < 5);
    });
    return () => unsubscribe();
  }, [x, dragConstraints.left]);

  return (
    <div className="relative w-full -mb-2.5" ref={containerRef}>
      {/* Left scroll button - only show if not at start */}
      {!atStart && (
        <button
          onClick={() => handleScroll('left')}
          className="absolute -left-2 top-[38%] -translate-y-1/2 z-10 bg-white/80 dark:bg-[#18181b]/80 rounded-full p-1 shadow-md hover:bg-white dark:hover:bg-[#18181b] transition-colors"
        >
          <ChevronLeft className="h-5 w-5 text-gray-600 dark:text-white" />
        </button>
      )}

      {/* Stories container */}
      <div className="overflow-hidden px-2">
        <motion.div
          ref={scrollContainerRef}
          className="flex space-x-4 py-2"
          style={{ x }}
          animate={controls}
          drag="x"
          dragConstraints={dragConstraints}
          dragElastic={0.1}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          whileTap={{ cursor: 'grabbing' }}
        >
          {/* Add story button (only for current user) */}
          {isCurrentUser && (
            <div className="flex flex-col items-center min-w-[72px]">
              <button
                onClick={onCreateStory}
                className="w-16 h-16 rounded-full bg-gray-100 dark:bg-zinc-800 flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-turquoise dark:hover:border-turquoise transition-colors"
                onPointerDown={(e) => {
                  e.stopPropagation();
                }}
              >
                <Plus className="h-6 w-6 text-gray-500 dark:text-gray-400" />
              </button>
              <span className="text-xs mt-1 text-gray-500 dark:text-gray-400">Add Story</span>
            </div>
          )}

          {/* Story items */}
          {mockStories.map((story) => (
            <div key={story.id} className="flex flex-col items-center min-w-[72px]">
              <Tippy
                content={story.isLive ? "Live Story" : story.isViewed ? "Viewed" : "New Story"}
                animation="shift-toward-subtle"
                placement="top"
                arrow={true}
                theme="sugar"
                disabled={isDragging}
              >
                <div className="pointer-events-none">
                  <button
                    onClick={() => handleStoryClick(story.id)}
                    className={`relative w-16 h-16 rounded-full p-[2px] pointer-events-auto ${story.isViewed
                      ? 'bg-gray-300 dark:bg-gray-600'
                      : 'bg-gradient-to-tr from-turquoise via-hot-pink to-purple-500'
                      }`}
                    onPointerDown={(e) => {
                      // Stop propagation to prevent drag when clicking the story button
                      e.stopPropagation();
                    }}
                  >
                    <div className="relative w-full h-full rounded-full overflow-hidden border-2 border-white dark:border-[#18181b]">
                      <Skeleton loading={isLoading} width="100%" height="100%" borderRadius="full">
                        <Image
                          src={story.profileImage}
                          alt={story.username}
                          width={64}
                          height={64}
                          className="object-cover w-full h-full"
                          onLoad={() => { }}
                          draggable={false}
                        />
                        {story.isLive && (
                          <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                            <div className="absolute w-full h-full bg-black bg-opacity-30"></div>
                            <div className="z-[2] px-1.5 py-0.5 bg-red-500 text-white text-[10px] font-medium rounded">LIVE</div>
                          </div>
                        )}
                      </Skeleton>
                    </div>
                  </button>
                </div>
              </Tippy>
              <span className="text-xs mt-1 truncate w-full text-center">{story.username}</span>
            </div>
          ))}
        </motion.div>
      </div>

      {/* Right scroll button - only show if not at end */}
      {!atEnd && (
        <button
          onClick={() => handleScroll('right')}
          className="absolute -right-2 top-[38%] -translate-y-1/2 z-10 bg-white/80 dark:bg-[#18181b]/80 rounded-full p-1 shadow-md hover:bg-white dark:hover:bg-[#18181b] transition-colors"
        >
          <ChevronRight className="h-5 w-5 text-gray-600 dark:text-white" />
        </button>
      )}

      {/* Story Viewer */}
      <StoryViewer
        stories={formattedStories}
        initialStoryId={selectedStoryId}
        initialUserId={selectedUserId}
        isOpen={storyViewerOpen}
        onClose={() => setStoryViewerOpen(false)}
        onStoryComplete={(storyId) => {
          console.log(`Story completed: ${storyId}`);
          // Mark story as viewed in your state management
          const updatedStories = mockStories.map(story =>
            story.id === storyId ? { ...story, isViewed: true } : story
          );
          // You would typically update your state here
        }}
        onStoryViewed={handleStoryViewed}
        onStoryExpired={handleStoryExpired}
      />
    </div>
  );
};
export default UserStories;