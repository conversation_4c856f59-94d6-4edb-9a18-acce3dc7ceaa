import { useState as ro, useEffect as s, useMemo as _ } from "react";
import p from "../../../store/useStore.js";
import { useWalletConnect as so } from "../../../hooks/useWalletConnect.js";
import { wallets as eo } from "../../../constants/wallets.js";
import { u as x } from "../../../useQuery-febd7967.js";
import { server as z } from "../../../config/axios.js";
import { CARDANO_TOKEN as E, DEFAULT_TOKEN as G } from "../tokens.js";
import { hexToAscii as I } from "../../../utils/cardanoUtils.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
import "../../../query-013b86c3.js";
import "../../../QueryClientProvider-6bcd4331.js";
import "react/jsx-runtime";
import "../../../axios-ddd885c5.js";
import "../../../index-ca8eb9e1.js";
const Ho = ({
  defaultToken: r,
  orderTypes: S,
  supportedTokens: t,
  partnerName: N,
  partnerCode: b,
  onSwapSuccess: g,
  onSwapError: B,
  selectedWallet: A,
  inputs: F,
  onWalletConnect: $,
  onClickWalletConnect: O,
  onViewOrder: R,
  orderTypeOnButtonClick: v,
  defaultSettings: i,
  autoFocus: C
}) => {
  const {
    setOrderTypes: K,
    setOnSwapSuccess: q,
    setOnSwapError: H,
    setOnViewOrder: L,
    setOrderType: U,
    setAutoFocus: P,
    setSellAmount: W
  } = p((o) => o.swapSlice), {
    setIsHideSmallBalances: Q,
    setDefaultBuySize: j,
    setIsAdvancedMode: V,
    setPartner: u,
    setInputs: D
  } = p((o) => o.globalSettingsSlice), {
    setAvailableWallets: X,
    setIsLoadingWallet: Z,
    resetWallet: f,
    setOnClickWalletConnect: k
  } = p((o) => o.walletSlice), {
    setSlippage: T,
    setIsAutomaticSlippage: d,
    setIsCustomSlippage: oo
  } = p((o) => o.swapSettingsSlice), { setTokenBuy: m, setTokenSell: a, tokenSell: w, tokenBuy: y } = p(
    (o) => o.swapSlice
  ), { setSwapType: J, setSupportedTokens: io } = p(
    (o) => o.tokenSearchSlice
  ), [l, M] = ro(!1), { reconnectToWallet: Y } = so($);
  s(() => {
    (i == null ? void 0 : i.slippage) !== void 0 && (i == null ? void 0 : i.slippage) !== null && T(i.slippage), (i == null ? void 0 : i.isAutomaticSlippage) !== void 0 && (i == null ? void 0 : i.isAutomaticSlippage) !== null && d(i.isAutomaticSlippage), (i == null ? void 0 : i.isCustomSlippage) !== void 0 && (i == null ? void 0 : i.isCustomSlippage) !== null && oo(i.isCustomSlippage);
  }, [JSON.stringify(i)]), s(() => {
    k(
      O || void 0
    );
  }, [O]), s(() => {
    P(C);
  }, [C]), s(() => {
    D(F);
  }, [F, D]), s(() => {
    K(S);
  }, [S, K]), s(() => {
    v && U(v);
  }, [v, U]), s(() => {
    u(N, b);
  }, [N, b, u]), s(() => {
    q(g), H(B), L(R);
  }, [
    g,
    B,
    q,
    H,
    R,
    L
  ]), s(() => {
    const o = localStorage.getItem("globalSettings");
    if (o) {
      const n = JSON.parse(o);
      Q(n.isHideSmallBalances), j(n.defaultBuySize), V(n.isAdvancedMode);
    }
  }, []), s(() => {
    A || (f(), Z(!1)), Y(A);
  }, [A]), s(() => {
    const o = eo.filter((n) => {
      if (n.alwaysShow)
        return !0;
      try {
        return window.cardano[n.windowName];
      } catch {
        return !1;
      }
    });
    X(o);
  }, []), s(() => {
    var o;
    if ((o = window.cardano) != null && o.onAccountChange) {
      const n = () => {
        Y();
      };
      window.cardano.onAccountChange(n);
    }
  }, []);
  const c = _(() => (w == null ? void 0 : w.token_id) || "", [w]), e = _(() => (y == null ? void 0 : y.token_id) || "", [y]), h = _(() => c === e, [c, e]);
  s(() => (r && (console.log("setting default token", r), W(null), J("BUY"), a(E), m({
    token_id: r,
    token_policy: r == null ? void 0 : r.slice(0, 56),
    token_ascii: I(r == null ? void 0 : r.slice(56)),
    is_verified: !1,
    ticker: I(r == null ? void 0 : r.slice(56))
  })), () => {
    m(null), a(null);
  }), [r, l]), s(() => {
    io(
      t && t.length > 0 ? t : []
    );
  }, [JSON.stringify(t)]), s(() => {
    M(c !== "" && e !== "" && !h);
  }, [c, e, h]), s(() => {
    !c && !e && !r && (W(null), J("BUY"), a(E), m(G));
  }, [c, e, r]), x({
    queryKey: ["tokenSellInfo", c],
    queryFn: async () => {
      if (l)
        return !0;
      try {
        const { data: o } = await z.get(
          `/swap/token/${c}`
        );
        return a(o), o;
      } catch (o) {
        return console.log("ERROR FETCHING SELL TOKEN", o), E;
      }
    },
    refetchOnWindowFocus: !1,
    refetchInterval: !1,
    enabled: !h && c !== "" && !l
  }), x({
    queryKey: ["tokenBuyInfo", e],
    queryFn: async () => {
      if (console.log("fetching token buy info", r), l)
        return !0;
      console.log("settings new token ref", e);
      try {
        const o = await z.get(`/swap/token/${e}`);
        return m(o == null ? void 0 : o.data), !0;
      } catch (o) {
        return console.log("ERROR FETCHING BUY TOKEN", o), e && m({
          token_id: e,
          token_policy: e.slice(0, 56),
          token_ascii: I(e.slice(56)),
          is_verified: !1,
          ticker: I(e.slice(56))
        }), G;
      }
    },
    refetchOnWindowFocus: !1,
    refetchInterval: !1,
    enabled: !h && e !== "" && !l
  });
};
export {
  Ho as useSwapSetup
};
