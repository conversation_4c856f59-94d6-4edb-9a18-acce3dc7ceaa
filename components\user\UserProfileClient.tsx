'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { formatDistance } from 'date-fns';
import Image from "next/image";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { MoreVertical, LinkIcon, Flag, Ban, Rss, UserRoundPlus, Play } from "lucide-react";
import { FaRegEnvelope } from "react-icons/fa6";
import { truncateText, elFormatter } from "@/public/main";
import MediaLightbox from '@/components/social/MediaLightbox';
import Tippy from '@tippyjs/react';
import AboutTab from './tabs/AboutTab';
import CalendarTab from './tabs/CalendarTab';
import VideosTab from './tabs/VideosTab';
import FundMeTab from './tabs/FundMeTab';
import PhotosTab from './tabs/PhotosTab';
import NewsFeedTab from './tabs/NewsFeedTab';
import StoreTab from './tabs/StoreTab';
import WishlistTab from './tabs/WishlistTab';
import { useSelector, useDispatch } from 'react-redux';
import { useSocket } from '@/context/SocketContext';
import Swal from 'sweetalert2';
import { useIsCurrentUser } from '@/hooks/useIsCurrentUser';
import { toast } from 'react-toastify';
import ReportUserDialog from '@/components/dialogs/ReportUserDialog';
import BlockUserDialog from '@/components/dialogs/BlockUserDialog';
import SubscriptionDialog from '@/components/dialogs/SubscriptionDialog';
import TipUserDialog from '@/components/dialogs/TipUserDialog';
import UserStories from '@/components/social/UserStories';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Post } from "@/types/post";
import { useUser } from "@/hooks/useUser";
import { setStatus, setLastActive } from '@/redux/slices/userActivitySlice';
import { motion } from 'framer-motion';
import { useExpandable } from "@/hooks/useExpandable";
import SpecialOffers from './SpecialOffers';
import IntroVideo from './IntroVideo';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import '@/styles/not-found.css';

interface UserProfileClientProps {
    username: string;
}

interface UserActivityState {
    userActivity: {
        status: { [key: string]: string };
        lastActive: { [key: string]: string };
    }
}

const UserProfileClient = ({ username }: UserProfileClientProps) => {
    const { user, isAuthenticated } = useUser();

    const router = useRouter();

    const [activeTab, setActiveTab] = useState("Media");
    const [profileLoading, setProfileLoading] = useState(true);
    const [bannerLoading, setBannerLoading] = useState(true);
    const [showMessagePopup, setShowMessagePopup] = useState(false);
    const aboutTabRef = useRef<HTMLDivElement>(null);
    const fanClubTabRef = useRef<HTMLDivElement>(null);
    const dispatch = useDispatch<any>();
    const { socket, isConnected } = useSocket();
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
    const [isBlockDialogOpen, setIsBlockDialogOpen] = useState(false);
    const [isVerifiedBadgeLoading, setIsVerifiedBadgeLoading] = useState(true);
    const copyTooltipTimer = useRef<NodeJS.Timeout | null>(null);
    const [isSubscriptionDialogOpen, setIsSubscriptionDialogOpen] = useState(false);
    const [isTipDialogOpen, setIsTipDialogOpen] = useState(false);
    const [lightboxOpen, setLightboxOpen] = useState(false);
    const [currentPost, setCurrentPost] = useState<any>(null);
    const [mediaArray, setMediaArray] = useState<any[]>([]);
    const [initialIndex, setInitialIndex] = useState<number>(0);
    const videoRef = useRef<HTMLVideoElement>(null);
    const bannerTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Real-time Convex queries for user data - with proper parameter validation
    const userDetails = useQuery(
        api.accounts.getUserDetails,
        username ? { user: username } : 'skip'
    );

    const userStatus = useQuery(
        api.users.getUserStatus,
        userDetails?.data?.user_id ? { userId: userDetails.data.user_id } : 'skip'
    );

    // Get follow counts in real-time
    const followCounts = useQuery(
        api.accounts.getFollowCount,
        userDetails?.data?.user_id ? { userId: userDetails.data.user_id } : 'skip'
    );

    // Get subscriber count for creators - fix the conditional logic
    const subscriberData = useQuery(
        api.accounts.getSubscriberCount,
        userDetails?.data?.user_id && userDetails?.data?.account_type === 'creator'
            ? { userId: userDetails?.data?.user_id }
            : 'skip'
    );

    const contentCounts = useQuery(
        api.content.getContentCounts,
        userDetails?.data?.user_id ? { profileUserId: userDetails.data.user_id } : 'skip'
    );

    const isCurrentUser = useIsCurrentUser(userDetails?.data?.user_id);

    // Enhanced error handling and connection monitoring
    const queryErrors: string[] = [
        userDetails && !userDetails.success ? userDetails.message : null,
        // Other queries don't have error properties - they return data or undefined
    ].filter(Boolean) as string[];

    if (queryErrors.length > 0) {
        console.error('🚨 Convex query errors:', queryErrors);
    }

    // Compute real-time statistics with enhanced reactivity
    const counts = useMemo(() => ({
        followers: followCounts?.followers,
        following: followCounts?.following,
        subscriberCount: subscriberData?.subscribers,
    }), [followCounts, subscriberData]);

    const liveUserData = useMemo(() => {
        if (!userDetails?.success || !userDetails?.data) {
            return null;
        }

        const data = userDetails.data;

        // Get status with fallback
        const currentStatus = userStatus?.status || data.user_info?.status || 'offline';
        const lastActiveTime = userStatus?.lastActive || data.user_info?.lastActive;

        return {
            // User ID and Account Type
            userId: data.user_id,
            accountType: data.account_type,
            isVerified: data.user_info?.account?.is_verified,
            modelId: data.user_info?.modelId,

            // Account Tab Fields
            account: {
                username: data.user_info?.account?.username || username,
                email: data.user_info?.email || '',
                phoneNumber: data.user_info?.account?.phoneNumber || '',
                displayName: data.user_info?.account?.displayName || '',
                bio: data.user_info?.account?.bio || '',
                countryOfOrigin: data.user_info?.account?.countryOfOrigin || '',
                latitude: data.user_info?.account?.latitude || 0,
                longitude: data.user_info?.account?.longitude || 0,
                amazonWishlist: data.user_info?.account?.amazonWishlist || '',
                officialWebsite: data.user_info?.account?.officialWebsite || '',
                dateOfBirth: data.user_info?.account?.dateOfBirth || '',
                birthplace: data.user_info?.account?.birthplace || '',
                languages: data.user_info?.account?.languages || [],
                ethnicity: data.user_info?.account?.ethnicity || '',
                gender: data.user_info?.account?.gender || '',
                relationshipStatus: data.user_info?.account?.relationshipStatus || '',
                interestedIn: data.user_info?.account?.interestedIn || [],
                fetishes: data.user_info?.account?.fetishes || [],
                piercings: data.user_info?.account?.piercings || '',
                tattoos: data.user_info?.account?.tattoos || '',
                location: data.user_info?.account?.location || '',
                physicalAttributes: data.user_info?.account?.physicalAttributes || {},
            },

            // Privacy Settings
            privacySettings: {
                accountVisibility: data.user_info?.privacy?.accountVisibility || 'public',
                profilePrivacy: data.user_info?.privacy?.profilePrivacy || 'everyone',
                hideSubscribers: data.user_info?.privacy?.hideSubscribers || false,
                locationPrivacy: data.user_info?.privacy?.locationPrivacy || false,
                searchVisibility: data.user_info?.privacy?.searchVisibility || true,
                directMessages: data.user_info?.privacy?.directMessages || 'everyone',
                commentSettings: data.user_info?.privacy?.commentSettings || 'everyone',
                taggingMentions: data.user_info?.privacy?.taggingMentions || 'everyone',
                storyVisibility: data.user_info?.privacy?.storyVisibility || 'everyone',
                sensitiveContent: data.user_info?.privacy?.sensitiveContent || false,
            },

            memberSince: data?.registration_date,

            // Media Fields
            profilePhoto: data.user_info?.profilePhoto || '',
            coverBanner: data.user_info?.coverBanner || '',
            coverBannerType: data.user_info?.coverBannerType || 'image',

            // Real-time counts
            followerCount: counts.followers,
            followingCount: counts.following,
            subscribers: counts.subscriberCount,

            // Social Connections
            connected_socials: data.user_info?.connected_socials?.map((social: any) => ({
                platform: social.platform,
                username: social.username,
            })) || [],

            // Backup Accounts
            backup_accounts: data.user_info?.backup_accounts?.map((social: any) => ({
                platform: social.platform,
                username: social.username,
            })) || [],

            // Online Status
            status: currentStatus,
            lastActive: lastActiveTime,
        };
    }, [
        userDetails?.success,
        userDetails?.data,
        userStatus?.status,
        userStatus?.lastActive,
        username,
        counts
    ]);

    // Only update currentUserData if liveUserData changes meaningfully
    const currentUserData = useMemo(() => liveUserData || null, [liveUserData]);

    // Handle tab switching with confirmation
    const handleTabChange = async (newTab: string) => {
        if (isCurrentUser && hasUnsavedChanges) {
            const result = await Swal.fire({
                title: 'Warning',
                text: 'You have unsaved changes. Are you sure you want to leave this tab?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, leave',
                cancelButtonText: 'Stay here'
            });

            if (!result.isConfirmed) {
                return;
            }
            setHasUnsavedChanges(false);
        }

        setActiveTab(newTab);
    };

    // Customize the tab labels to match your screenshot
    const menuItems = [
        'Media',
        'About',
        'Items',
        'Wishlist',
        'Fund Me',
        'Calendar',
        'Podcast',
        'TV Series'
    ];

    // Graceful fallback data if some fields are missing
    const displayName = (currentUserData as any)?.account?.displayName || "";
    const usernameText = (currentUserData as any)?.account?.username || "";
    const bio = (currentUserData as any)?.account?.bio || "";

    const handleSendMessage = () => {
        setShowMessagePopup(true);
    };

    const handleReport = () => {
        if (!user) {
            toast.error('You must be logged in to report a user!');
            return;
        }
        setIsReportDialogOpen(true);
    };

    const handleBlock = () => {
        if (!user) {
            toast.error('You must be logged in to block a user');
            return;
        }
        setIsBlockDialogOpen(true);
    };

    const handleShare = async () => {
        try {
            const profileUrl = `${window.location.origin}/user/${(currentUserData as any)?.account?.username}`;
            const shareData = {
                title: (currentUserData as any)?.account?.displayName || (currentUserData as any)?.account?.username,
                text: `Check out this profile on Sugar Club!`,
                url: profileUrl,
            };

            if (navigator.share) {
                await navigator.share(shareData);
            } else {
                await navigator.clipboard.writeText(profileUrl);
                toast.success('Link copied to clipboard!');
            }
        } catch (error) {
            toast.error('Failed to share profile link');
        }
    };

    const handleCopyProfileLink = useCallback(async () => {
        try {
            const profileUrl = `${window.location.origin}/user/${(currentUserData as any)?.account?.username}`;
            await navigator.clipboard.writeText(profileUrl);
            toast.success('Profile link copied to clipboard!');
        } catch (error) {
            toast.error('Failed to copy profile link');
        }
    }, [currentUserData]);

    const handleOpenLightbox = useCallback((post: Post, mediaArray: any[], initialIndex: number) => {
        setCurrentPost(post);
        setMediaArray(mediaArray);
        setInitialIndex(initialIndex);
        setLightboxOpen(true);
    }, []);

    // Get the specific user's status from Redux
    const reduxUserStatus = useSelector((state: UserActivityState) => {
        return {
            status: username && state.userActivity?.status
                ? state.userActivity.status[username] ?? 'offline'
                : 'offline',
            lastActive: username && state.userActivity?.lastActive
                ? state.userActivity.lastActive[username] ?? undefined
                : undefined
        };
    });

    // Render the status with proper null checks
    const renderUserStatus = () => {
        if (!username) return null;

        const status = userStatus?.status || reduxUserStatus.status || 'offline';
        const lastActive = userStatus?.lastActive || reduxUserStatus.lastActive;

        let statusText = status || 'Offline';
        let lastActiveText = 'Last Seen: Unknown';

        if (status === 'online') {
            statusText = 'Online';
            lastActiveText = 'Last Seen: Online now'; // <-- update tooltip for online
        } else if (lastActive) {
            try {
                const lastActiveDate = new Date(lastActive);
                // Check if the date is valid
                if (!isNaN(lastActiveDate.getTime())) {
                    lastActiveText = `Last Seen: ${formatDistance(lastActiveDate, new Date(), {
                        addSuffix: true
                    })}`;
                }
            } catch (error) {
                console.error('Error formatting last active date:', error);
                statusText = 'Offline';
                lastActiveText = 'Last Seen: Unknown';
            }
        }

        return (
            <Tippy content={lastActiveText} animation="scale-subtle" placement="top" arrow={true} inertia={true} theme="sugar">
                <div className="user-active-status text-gray-600 dark:text-white border border-solid border-gray-300 dark:border-white">
                    {statusText} <span className={`activity-status-icon ${statusText.toLowerCase()}`}></span>
                </div>
            </Tippy>
        );
    };

    // Clean up timer on component unmount
    useEffect(() => {
        return () => {
            if (copyTooltipTimer.current) {
                clearTimeout(copyTooltipTimer.current);
            }
        };
    }, []);

    // Listen for socket events
    useEffect(() => {
        if (!socket || !isConnected || !username) return;

        const handleStatusChange = (data: any) => {
            if (data.userId === userDetails?.data?.user_id) {
                dispatch(setStatus({
                    userId: data.userId,
                    status: data.status
                }));
                dispatch(setLastActive({
                    userId: data.userId,
                    lastActive: data.last_active
                }));
            }
        };

        socket.on('userStatusChanged', handleStatusChange);
        socket.on('userStatusUpdate', handleStatusChange);

        return () => {
            socket.off('userStatusChanged', handleStatusChange);
            socket.off('userStatusUpdate', handleStatusChange);
        };
    }, [socket, isConnected, userDetails?.data?.user_id, dispatch]);

    // Add beforeunload event listener
    useEffect(() => {
        // Only add the event listener if user is viewing their own profile
        if (isCurrentUser && isAuthenticated) {
            const handleBeforeUnload = (e: BeforeUnloadEvent) => {
                console.log({ hasUnsavedChanges, isCurrentUser, isAuthenticated });
                if (hasUnsavedChanges) {
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            };

            window.addEventListener('beforeunload', handleBeforeUnload);
            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        }
    }, [hasUnsavedChanges, isCurrentUser, isAuthenticated]);

    // Subscription bundles expandable section
    const { isExpanded: isBundlesExpanded, toggleExpand: toggleBundlesExpand, animatedHeight: bundlesAnimatedHeight } = useExpandable(true);
    const bundlesContentRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (bundlesContentRef.current && isBundlesExpanded) {
            bundlesAnimatedHeight.set(bundlesContentRef.current.offsetHeight);
        } else {
            bundlesAnimatedHeight.set(0);
        }
    }, [isBundlesExpanded, bundlesAnimatedHeight]);

    // Robust video loading handler
    const handleVideoLoad = useCallback(() => {
        setBannerLoading(false);
        if (bannerTimeoutRef.current) {
            clearTimeout(bannerTimeoutRef.current);
            bannerTimeoutRef.current = null;
        }
    }, []);

    // Check video ready state
    useEffect(() => {
        if (videoRef.current && (currentUserData as any)?.coverBannerType === "video") {
            const video = videoRef.current;

            // Check if video is already ready
            if (video.readyState >= 2) { // HAVE_CURRENT_DATA
                setBannerLoading(false);
                return;
            }

            // Add additional ready state check
            const checkReadyState = () => {
                if (video.readyState >= 2) {
                    setBannerLoading(false);
                }
            };

            // Check periodically
            const interval = setInterval(checkReadyState, 100);

            return () => clearInterval(interval);
        }
    }, [(currentUserData as any)?.coverBannerType, videoRef.current]);

    // Video loading timeout fallback
    useEffect(() => {
        if ((currentUserData as any)?.coverBannerType === "video" && bannerLoading) {
            // Set a timeout to remove skeleton after 5 seconds as fallback
            bannerTimeoutRef.current = setTimeout(() => {
                console.warn('Video loading timeout - removing skeleton');
                setBannerLoading(false);
            }, 5000);

            return () => {
                if (bannerTimeoutRef.current) {
                    clearTimeout(bannerTimeoutRef.current);
                }
            };
        }
    }, [(currentUserData as any)?.coverBannerType, bannerLoading]);

    // Clean up timeout on unmount
    useEffect(() => {
        return () => {
            if (bannerTimeoutRef.current) {
                clearTimeout(bannerTimeoutRef.current);
            }
        };
    }, []);

    // Reset banner loading when source changes
    useEffect(() => {
        setBannerLoading(true);
        if (bannerTimeoutRef.current) {
            clearTimeout(bannerTimeoutRef.current);
            bannerTimeoutRef.current = null;
        }
    }, [(currentUserData as any)?.coverBanner, (currentUserData as any)?.coverBannerType]);

    // After all hooks, but before main render:
    const blockStatus = useQuery(
        api.users.isUserBlocked,
        user && userDetails?.data?.user_id
            ? { viewerId: user.user_id, profileUserId: userDetails?.data?.user_id }
            : 'skip'
    );

    if (blockStatus?.blocked) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
                <div className="text-2xl font-bold text-gray-800 dark:text-white mb-4">User Blocked</div>
                <div className="text-gray-600 dark:text-gray-300 mb-2">
                    You have blocked this user or have been blocked. Their profile and content are not visible to you.
                </div>
                <Button onClick={() => router.push('/')} className="mt-4 bg-turquoise text-white">
                    Go to Home
                </Button>
            </div>
        );
    }

    const aboutTabUserData = useMemo(() => currentUserData, [currentUserData]);

    if (!username || !userDetails?.success || !userDetails?.data) {
        return (
            <div id="notfound">
                <div className="notfound">
                    <div className="notfound-404">
                        <h1>User Not Found</h1>
                        <h2>This profile does not exist or could not be found</h2>
                    </div>
                    <Link href="/" prefetch={false}>Back to homepage</Link>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-white dark:bg-[#18181b]">
            {/* Banner (image or video) - Full width */}
            <div className="w-full h-[400px] relative border border-solid border-t-0 border-l-0 border-r-0 border-gray-300 dark:border-white/50">
                <Skeleton loading={bannerLoading} width="100%" height="100%">
                    {(currentUserData as any)?.coverBannerType === "video" ? (
                        <video
                            ref={videoRef}
                            src={(currentUserData as any)?.coverBanner || "/images/user/default-banner.webp"}
                            autoPlay
                            muted
                            loop
                            playsInline
                            className="object-cover w-full h-full absolute top-0 left-0"
                            onLoadStart={handleVideoLoad}
                            onLoadedMetadata={handleVideoLoad}
                            onLoadedData={handleVideoLoad}
                            onCanPlay={handleVideoLoad}
                            onCanPlayThrough={handleVideoLoad}
                            onError={handleVideoLoad}
                            onAbort={handleVideoLoad}
                        />
                    ) : (
                        <Image
                            src={(currentUserData as any)?.coverBanner || "/images/user/default-banner.webp"}
                            alt="Profile Banner"
                            fill
                            className="object-cover w-full h-full absolute top-0 left-0"
                            onLoad={() => setBannerLoading(false)}
                            onError={() => setBannerLoading(false)}
                        />
                    )}
                </Skeleton>

                {/* Gradient overlay behind stats/nav */}
                <div
                    className={`
                        pointer-events-none
                        absolute left-0 right-0
                        bottom-0
                        h-[100px]
                        z-[1]
                        ${'dark' in document.documentElement.classList
                            ? 'bg-gradient-to-b from-transparent to-[#18181b]/60'
                            : 'bg-gradient-to-b from-transparent to-[#18181b]/60'}
                        dark:bg-gradient-to-b dark:from-transparent dark:to-[#18181b]/60
                        bg-gradient-to-b from-transparent to-[#18181b]/60
                        transition-colors
                    `}
                />

            </div>

            {/* Main Content + Sidebar - Contained within max-width */}
            <div className="container mx-auto px-4 relative -mt-[105px]">
                <div className="flex flex-col md:flex-row gap-6">
                    {/* Sidebar - 25% width on desktop */}
                    <div className="w-full md:w-[300px] flex-shrink-0 mb-8 -mt-[25px] z-[2]">
                        {/* Profile Picture + Status */}
                        <div className="flex flex-col items-center justify-center relative mb-2 gap-2 mt-[32.5px]">
                            <div className="flex flex-col items-center justify-center relative w-full max-w-[300px] aspect-square rounded-full overflow-visible border-solid border-4 border-white shadow-md mb-0">
                                <Skeleton className="w-full h-full relative" loading={profileLoading} borderRadius="50%">
                                    <Image
                                        src={(currentUserData as any)?.profilePhoto || "/images/user/default-avatar.webp"}
                                        alt={usernameText}
                                        fill
                                        className="object-cover rounded-full"
                                        onLoad={() => setProfileLoading(false)}
                                    />
                                </Skeleton>
                            </div>
                            {renderUserStatus()}
                        </div>

                        {/* Member Since (disabled for now) */}
                        {/* <div className="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400 mt-1 mb-3 text-[15px]">
                            <Calendar className="w-4 h-4" />
                            <span>Member Since: {memberSince instanceof Date ? memberSince.toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                            }) : "Unknown"}</span>
                        </div> */}

                        {/* Country */}
                        {/* <div className="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400 mt-1 mb-3 text-[15px]">
                            {flagName && (
                                <Skeleton loading={isFlagLoading} width="26px" height="26px" lineHeight={1}>
                                    <img
                                        src={flagName}
                                        alt={userData.account.countryOfOrigin}
                                        onError={() => setIsFlagLoading(false)}
                                        onLoad={() => setIsFlagLoading(false)}
                                        className="inline-block w-[26px] h-[26px] user-profile-flag"
                                    /></Skeleton>)}
                            <span>{userData?.account?.countryOfOrigin}</span>
                        </div> */}

                        {/* Introduction Video */}
                        <IntroVideo />

                        {/* Ad Space */}
                        <div className="sticky top-[120px] mt-7 space-y-4">
                            <div className="flex flex-col items-center justify-center w-full h-[40vh] bg-turquoise uppercase text-white">
                                Ad Space
                            </div>
                            <div className="flex flex-col items-center justify-center w-full h-[40vh] bg-turquoise uppercase text-white">
                                Ad Space
                            </div>
                        </div>
                    </div>

                    {/* Main Content Area - 75% width on desktop */}
                    <div className="flex-1 md:w-[72%] mt-5 z-[2]">
                        {/* Username, Display Name, and Stats Section */}
                        <div className="flex flex-col items-end justify-center mb-4">
                            <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4 md:gap-[108px] w-full">
                                {/* Username and Display Name */}
                                <div className="flex items-center gap-1 relative top-[7px]">
                                    <h2 className="flex gap-2 items-center justify-center text-[25px] font-semibold text-white text-shadow-sm text-shadow-black">
                                        <span className="[text-shadow:1px_1px_2px_#000] relative top-[-3px]">
                                            {displayName || truncateText(usernameText, 15)}
                                        </span>
                                    </h2>
                                    {(currentUserData as any)?.isVerified && (
                                        <Tippy content="Verified User" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                                            <Skeleton loading={isVerifiedBadgeLoading} width="25px" height="25px">
                                                <img className="w-[25px] h-[25px] user-profile-verified-badge" alt="Verified User" src="/images/user/verified.png" onError={() => setIsVerifiedBadgeLoading(false)} onLoad={() => setIsVerifiedBadgeLoading(false)} />
                                            </Skeleton>
                                        </Tippy>
                                    )}
                                </div>

                                {/* Stats Row - Responsive layout */}
                                <div className="flex flex-wrap items-center gap-4 md:gap-8 lg:gap-12 relative top-[17.5px] -mb-2">
                                    {/* Photos Stats */}
                                    <div className="flex flex-col items-center">
                                        <span className="text-xl font-bold text-white [text-shadow:1px_1px_2px_#000] leading-[25px] -mt-[5px] mb-0">
                                            {elFormatter(contentCounts?.photosCount ?? 0, 0)}
                                        </span>
                                        <span className="text-[17px] text-white [text-shadow:1px_1px_2px_#000]">
                                            Photos
                                        </span>
                                    </div>
                                    {/* Videos Stats */}
                                    <div className="flex flex-col items-center">
                                        <span className="text-xl font-bold text-white [text-shadow:1px_1px_2px_#000] leading-[25px] -mt-[5px] mb-0">
                                            {elFormatter(contentCounts?.videosCount ?? 0, 0)}
                                        </span>
                                        <span className="text-[17px] text-white [text-shadow:1px_1px_2px_#000]">
                                            Videos
                                        </span>
                                    </div>
                                    {/* Audio Stats */}
                                    <div className="flex flex-col items-center">
                                        <span className="text-xl font-bold text-white [text-shadow:1px_1px_2px_#000] leading-[25px] -mt-[5px] mb-0">
                                            {elFormatter(contentCounts?.audioCount ?? 0, 0)}
                                        </span>
                                        <span className="text-[17px] text-white [text-shadow:1px_1px_2px_#000]">
                                            Audio
                                        </span>
                                    </div>
                                    {/* Clips Stats */}
                                    <div className="flex flex-col items-center">
                                        <span className="text-xl font-bold text-white [text-shadow:1px_1px_2px_#000] leading-[25px] -mt-[5px] mb-0">
                                            {elFormatter(contentCounts?.clipsCount ?? 0, 0)}
                                        </span>
                                        <span className="text-[17px] text-white [text-shadow:1px_1px_2px_#000]">
                                            Clips
                                        </span>
                                    </div>
                                    {/* GIFs Stats */}
                                    <div className="flex flex-col items-center">
                                        <span className="text-xl font-bold text-white [text-shadow:1px_1px_2px_#000] leading-[25px] -mt-[5px] mb-0">
                                            {elFormatter(contentCounts?.gifsCount ?? 0, 0)}
                                        </span>
                                        <span className="text-[17px] text-white [text-shadow:1px_1px_2px_#000]">
                                            GIFs
                                        </span>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex flex-col relative bottom-[-20px]">
                                    <div className="flex items-center justify-center gap-2 overflow-visible">
                                        {!isCurrentUser && (
                                            <>
                                                <Tippy content="Send Message" theme="sugar" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true}>
                                                    <Button
                                                        variant="default"
                                                        size="icon"
                                                        onClick={handleSendMessage}
                                                        className="w-full bg-turquoise hover:bg-turquoise-hover text-white rounded-md h-9 px-3 flex items-center justify-center"
                                                    >
                                                        <FaRegEnvelope className="w-4 h-4 text-white" />
                                                    </Button>
                                                </Tippy>
                                                <Tippy content="Follow User" theme="sugar" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true}>
                                                    <Button
                                                        variant="default"
                                                        size="icon"
                                                        className="w-full bg-turquoise hover:bg-turquoise-hover text-white rounded-md h-9 px-3 flex items-center justify-center"
                                                    >
                                                        <UserRoundPlus className="h-4 w-4" />
                                                    </Button>
                                                </Tippy>
                                                <Tippy content="Share Profile" theme="sugar" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true}>
                                                    <Button
                                                        variant="default"
                                                        size="icon"
                                                        className="w-full bg-turquoise hover:bg-turquoise-hover text-white rounded-md h-9 flex items-center justify-center px-3"
                                                        onClick={handleShare}
                                                    >
                                                        <img className="w-4 h-4" src="/images/share-icon.webp" alt="Copy Profile Link" />
                                                    </Button>
                                                </Tippy>
                                            </>
                                        )}
                                        <div className="relative">
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button
                                                        variant="default"
                                                        size="icon"
                                                        className="!bg-turquoise hover:bg-turquoise-hover text-white rounded-lg h-9 px-3 flex items-center justify-center"
                                                    >
                                                        <Tippy content="More" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                                                            <span>
                                                                <MoreVertical className="h-4 w-4" />
                                                            </span>
                                                        </Tippy>
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end" className="w-48 z-10 bg-white/60 dark:!bg-[#18181b]/60 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-solid border-gray-200/30">
                                                    <DropdownMenuItem onClick={handleCopyProfileLink} className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800">
                                                        <LinkIcon className="mr-2 h-4 w-4" />
                                                        Copy Profile Link
                                                    </DropdownMenuItem>
                                                    {!isCurrentUser && <DropdownMenuSeparator />}
                                                    {!isCurrentUser && (
                                                        <>
                                                            <DropdownMenuItem onClick={handleReport} className="cursor-pointer">
                                                                <Flag className="mr-2 h-4 w-4" />
                                                                Report User
                                                            </DropdownMenuItem>
                                                            <DropdownMenuSeparator />
                                                            <DropdownMenuItem onClick={handleBlock} className="cursor-pointer text-destructive">
                                                                <Ban className="mr-2 h-4 w-4" />
                                                                Block User
                                                            </DropdownMenuItem>
                                                        </>
                                                    )}
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p className="w-full text-left text-white [text-shadow:1px_1px_2px_#000] text-[17px]">@{usernameText}</p>
                        </div>

                        {/* Bio */}
                        {bio && (
                            <div className="relative w-full mt-9 mb-2">
                                <p className="text-gray-600 dark:text-white line-clamp-3">{bio}</p>
                            </div>
                        )}

                        {/* User Stories */}
                        <UserStories
                            profileUserId={userDetails?.data?.user_id}
                            isCurrentUser={isCurrentUser}
                            onViewStory={(storyId) => {
                                // Handle story viewing logic here
                                console.info(`Viewing story: ${storyId}`);
                            }}
                            onCreateStory={() => {
                                // Handle story creation logic here
                                console.info('Creating new story');
                            }}
                        />

                        {/* Tabs Navigation */}
                        <nav className="flex gap-1 overflow-x-auto border border-solid border-gray-300 border-l-0 border-r-0 border-t-0 user-profile-tabs">
                            {menuItems.map((item) => (
                                <button
                                    key={item}
                                    onClick={() => handleTabChange(item)}
                                    className={`py-4 user-profile-tabs__link ${item.toLowerCase() === 'news feed' ? 'px-0 pr-5 pl-3' : 'px-5'} relative whitespace-nowrap ${activeTab === item
                                        ? "text-turquoise font-medium user-profile-tabs__link_active"
                                        : "text-gray-600 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-400"
                                        }`}
                                >
                                    {item}
                                </button>
                            ))}
                        </nav>

                        {/* Tab Content */}
                        <div className="bg-white dark:bg-[#18181b] rounded-b-lg py-4 px-0">
                            {activeTab === "Media" && (
                                <div className="flex flex-col lg:flex-row gap-4">
                                    <div className="w-full lg:w-[70%]">
                                        <NewsFeedTab
                                            profileUserId={userDetails?.data?.user_id}
                                            userData={currentUserData}
                                            setHasUnsavedChanges={isCurrentUser ? setHasUnsavedChanges : () => { }}
                                        />
                                    </div>

                                    <div className="w-full lg:w-[30%] h-fit sticky top-[120px]">
                                        {/* Subscription Menu Section */}
                                        <div className="mb-4">
                                            <button
                                                onClick={toggleBundlesExpand}
                                                className="flex items-center justify-between w-full h-10 px-3 rounded-lg bg-transparent transition-colors text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white"
                                            >
                                                <div className="flex items-center text-sm">
                                                    <Rss strokeWidth={2.75} className="h-4 w-4 mr-2" />
                                                    <span>Subscription Menu</span>
                                                </div>
                                                <div>
                                                    <svg
                                                        width={16}
                                                        height={16}
                                                        viewBox="0 0 20 20"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        className={`fill-current transition-transform duration-200 ${isBundlesExpanded ? 'rotate-180' : ''}`}
                                                    >
                                                        <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
                                                    </svg>
                                                </div>
                                            </button>

                                            <motion.div
                                                className="overflow-hidden"
                                                style={{ height: bundlesAnimatedHeight }}
                                            >
                                                <div
                                                    ref={bundlesContentRef}
                                                    className="mt-4 space-y-1.5 pb-4"
                                                >
                                                    <button onClick={() => setIsSubscriptionDialogOpen(true)} className="w-full h-[38px] px-5 rounded-full bg-turquoise hover:bg-turquoise-hover transition-colors flex items-center justify-between text-sm">
                                                        <span className="font-medium text-white">3 MONTHS (15% off)</span>
                                                        <span className="font-medium text-white">$28.02</span>
                                                    </button>
                                                    <button onClick={() => setIsSubscriptionDialogOpen(true)} className="w-full h-[38px] px-5 rounded-full bg-turquoise hover:bg-turquoise-hover transition-colors flex items-center justify-between text-sm">
                                                        <span className="font-medium text-white">6 MONTHS (25% off)</span>
                                                        <span className="font-medium text-white">$49.46</span>
                                                    </button>
                                                    <button onClick={() => setIsSubscriptionDialogOpen(true)} className="w-full h-[38px] px-5 rounded-full bg-turquoise hover:bg-turquoise-hover transition-colors flex items-center justify-between text-sm">
                                                        <span className="font-medium text-white">12 MONTHS (40% off)</span>
                                                        <span className="font-medium text-white">$79.13</span>
                                                    </button>
                                                </div>
                                            </motion.div>
                                        </div>

                                        {/* Special Offers Section */}
                                        <SpecialOffers
                                            videos={[]}
                                            onOpenLightbox={handleOpenLightbox}
                                        />

                                    </div>
                                </div>
                            )}

                            {activeTab === "About" && (
                                <AboutTab ref={aboutTabRef} userData={aboutTabUserData} />
                            )}

                            {activeTab === "Photos" && (
                                <PhotosTab />
                            )}

                            {activeTab === "Videos" && (
                                <VideosTab />
                            )}

                            {activeTab === "My Store" && (
                                <StoreTab />
                            )}

                            {activeTab === "Wishlist" && (
                                <WishlistTab />
                            )}

                            {activeTab === "Calendar" && (
                                <CalendarTab />
                            )}

                            {activeTab === "Fund Me" && (
                                <FundMeTab userData={currentUserData} ref={fanClubTabRef} />
                            )}

                        </div>
                    </div>
                </div>
            </div>

            <ReportUserDialog
                isOpen={isReportDialogOpen}
                onClose={() => setIsReportDialogOpen(false)}
                userId={userDetails?.data?.user_id}
                profilePhoto={currentUserData?.profilePhoto}
            />

            <BlockUserDialog
                isOpen={isBlockDialogOpen}
                onClose={() => setIsBlockDialogOpen(false)}
                userId={userDetails?.data?.user_id}
                username={username}
                profilePhoto={currentUserData?.profilePhoto}
            />

            <SubscriptionDialog
                isOpen={isSubscriptionDialogOpen}
                onClose={() => setIsSubscriptionDialogOpen(false)}
                userData={{
                    username: username,
                    displayName: displayName,
                    profilePhoto: currentUserData?.profilePhoto || '/images/user/default-avatar.webp',
                    coverBanner: currentUserData?.coverBanner || '/images/user/default-banner.webp',
                    coverBannerType: currentUserData?.coverBannerType as "video" | "image",
                    isVerified: currentUserData?.isVerified,
                    subscriptionPrice: currentUserData?.subscriptionPrice || 8.88,
                    renewalPrice: currentUserData?.renewalPrice || 8.88,
                    renewalDate: currentUserData?.renewalDate || '10/06/25',
                }}
            />

            <TipUserDialog
                isOpen={isTipDialogOpen}
                onClose={() => setIsTipDialogOpen(false)}
                userData={{
                    username: username,
                    displayName: displayName,
                    profileImage: currentUserData?.profilePhoto
                }}
                onTipSuccess={() => {
                    toast.success('Tip sent successfully!');
                    setIsTipDialogOpen(false);
                }}
            />

            {currentPost && (
                <MediaLightbox
                    open={lightboxOpen}
                    onOpenChange={setLightboxOpen}
                    post={currentPost}
                    mediaArray={mediaArray}
                    initialIndex={initialIndex}
                />
            )}
        </div>
    );
};
export default UserProfileClient;