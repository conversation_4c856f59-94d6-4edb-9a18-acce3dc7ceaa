import { TokenSearchSlice } from "./createTokenSearchSlice";
import { WalletSlice } from "./createWalletSlice";
import { SwapSettingsSlice } from "./createSwapSettingsSlice";
import { GlobalSettingsSlice } from "./createGlobalSettingsSlice";
import { UserOrdersSlice } from "./createUserOrdersSlice";
import { SwapSlice } from "./createSwapSlice";
import { ChartSlice } from "./createChartSlice";
import { BasketSlice } from "./createBasketSlice";
import { ModalWhatsNewSlice } from "./createModalWhatsNewSlice";
import { SwapParamsSlice } from "./createSwapParamsSlice";
export type AppState = {
    tokenSearchSlice: TokenSearchSlice;
    walletSlice: WalletSlice;
    swapSettingsSlice: SwapSettingsSlice;
    swapSlice: SwapSlice;
    globalSettingsSlice: GlobalSettingsSlice;
    userOrdersSlice: UserOrdersSlice;
    chartSlice: ChartSlice;
    modalWhatsNewSlice: ModalWhatsNewSlice;
    basketSlice: BasketSlice;
    swapParamsSlice: SwapParamsSlice;
};
declare const useStore: import("zustand").UseBoundStore<import("zustand").StoreApi<AppState>>;
export default useStore;
