import React, { FC, useState, useEffect, useRef } from 'react';
import Map, { <PERSON><PERSON>, <PERSON>up, NavigationControl } from 'react-map-gl/mapbox';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { useTheme } from 'next-themes';
import { NetworkProfile } from '@/types/network';
import { default as NextImage } from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';
//import { MoreVertical } from 'lucide-react';

interface MapContainerProps {
  users: NetworkProfile[];
  setIsLoading: (loading: boolean) => void;
}

const MapContainer: FC<MapContainerProps> = ({ users, setIsLoading }) => {
  const { theme } = useTheme();
  const mapRef = useRef(null) as any;

  const [viewState, setViewState] = useState<{
    longitude: number;
    latitude: number;
    zoom: number;
    pitch: number;
    bearing: number;
  }>({
    longitude: 0,
    latitude: 0,
    zoom: 1,
    pitch: 0,
    bearing: 0
  });

  const [activeMarker, setActiveMarker] = useState<NetworkProfile | null>(null);
  const [customMarkers, setCustomMarkers] = useState<Record<string, string>>({});
  const [adjustedPositions, setAdjustedPositions] = useState<Record<string, { lat: number; lng: number }>>({});
  const [bannerLoading, setBannerLoading] = useState(true);

  const adjustMarkerPosition = (lat: number, lng: number, existingPositions: Array<{ lat: number; lng: number }>) => {
    let newLat = lat;
    let newLng = lng;

    for (let pos of existingPositions) {
      if (Math.abs(pos.lat - lat) < 0.0001 && Math.abs(pos.lng - lng) < 0.0001) {
        newLat += (Math.random() - 0.5) / 1500;
        newLng += (Math.random() - 0.5) / 1500;
      }
    }

    return { lat: newLat, lng: newLng };
  };

  const calculateBoundsAndCenter = () => {
    if (users.length === 0) return;

    const bounds = new mapboxgl.LngLatBounds();
    const positions: { lat: number; lng: number }[] = [];

    users.forEach(user => {
      const lat = parseFloat(user.latitude as unknown as string);
      const lng = parseFloat(user.longitude as unknown as string);

      if (!isNaN(lat) && !isNaN(lng)) {
        const adjustedPos = adjustMarkerPosition(lat, lng, positions);
        positions.push(adjustedPos);
        bounds.extend([adjustedPos.lng, adjustedPos.lat]);
      } else {
        console.error(`Invalid coordinates for user: ${user.id}`);
      }
    });

    if (mapRef.current && bounds.isEmpty()) {
      mapRef.current.flyTo({ center: [0, 0], zoom: 1 });
    } else if (mapRef.current) {
      mapRef.current.fitBounds(bounds as any, { padding: 50, duration: 0 });
    }
    setIsLoading(false);
  };

  const onMarkerClick = (user: NetworkProfile) => {
    setActiveMarker(user);
  };

  const generateCustomMarkers = async () => {
    const map: Record<string, string> = {};
    const positions: Array<{ lat: number, lng: number }> = [];

    for (const user of users) {
      const lat = parseFloat(user.latitude as unknown as string);
      const lng = parseFloat(user.longitude as unknown as string);
      if (!isNaN(lat) && !isNaN(lng)) {
        const adjustedPos = adjustMarkerPosition(lat, lng, positions);
        positions.push(adjustedPos);
        setAdjustedPositions(prev => ({
          ...prev,
          [user.id]: adjustedPos,
        }));

        try {
          const url = await createCustomMarker(user.profilePhoto || '/images/user/default-avatar.webp');
          map[user.id] = url;
        } catch (err) {
          console.error(`✖️ createCustomMarker failed for ${user.id}:`, err);
        }
      } else {
        console.warn(`⚠️ skipping invalid coords for ${user.id}`);
      }
    }

    setCustomMarkers(map);

    return map;
  };

  const createCustomMarker = async (imageUrl: string) => {
    const defaultMarkerImg = new Image();
    defaultMarkerImg.src = "/images/map-marker.webp";

    const userImg = new Image();
    userImg.crossOrigin = "Anonymous";
    userImg.src = imageUrl;

    await new Promise((resolve) => {
      defaultMarkerImg.onload = resolve;
    });
    await new Promise((resolve) => {
      userImg.onload = resolve;
    });

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;

    canvas.width = defaultMarkerImg.width;
    canvas.height = defaultMarkerImg.height;

    ctx.drawImage(defaultMarkerImg, 0, 0);

    ctx.beginPath();
    ctx.arc(canvas.width / 2, canvas.height / 2.8, 65, 0, 2 * Math.PI, false);
    ctx.clip();

    ctx.drawImage(userImg, canvas.width / 2 - 65, canvas.height / 2.8 - 65, 130, 130);

    return canvas.toDataURL();
  };

  useEffect(() => {
    mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || '';

    calculateBoundsAndCenter();

    // wrap in async so we can await
    (async () => {
      try {
        await generateCustomMarkers();
      } catch (err) {
        console.error('generateCustomMarkers error:', err);
      }
    })();
  }, [users]);

  return (
    <div style={{ position: 'relative', width: '100%', height: '600px' }}>
      <Map
        ref={mapRef}
        {...viewState}
        onMove={evt => setViewState(evt.viewState)}
        mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || ''}
        mapStyle={theme === 'light' ? 'mapbox://styles/sugarclub/cma712ds700fg01sg4wv23djh' : 'mapbox://styles/sugarclub/cma71p2cw00fi01qy76iwe74w'}
        projection="globe"
        minZoom={1}
      >
        {Object.entries(customMarkers).map(([userId, customMarker]) => {
          const user = users.find(u => u.id === userId);
          const adjustedPos = adjustedPositions[userId];
          if (adjustedPos && user) {
            const { lat, lng } = adjustedPos;
            if (!isNaN(lat) && !isNaN(lng)) {
              return (
                <Marker
                  key={userId}
                  longitude={lng}
                  latitude={lat}
                >
                  <img
                    src={customMarker || "/images/user/default-avatar.webp"}
                    alt="marker"
                    style={{ width: '60px', height: '60px', cursor: 'pointer' }}
                    onClick={() => onMarkerClick(user)}
                    onError={e => console.error(`Image failed to load for ${userId}:`, e.currentTarget.src)}
                  />
                </Marker>
              );
            }
          }
          return null;
        })}

        {activeMarker && (
          <Popup
            longitude={adjustedPositions[activeMarker.id]?.lng || 0}
            latitude={adjustedPositions[activeMarker.id]?.lat || 0}
            onClose={() => setActiveMarker(null)}
            closeOnClick={false}
            className="map-user-popup min-w-[180px] md:min-w-[250px]"
          >
            <div className="relative w-full bg-white dark:bg-zinc-700 text-white rounded-lg overflow-hidden backdrop-blur-md p-4">
              {/* Cover Banner */}
              <div className="absolute top-0 left-0 w-full h-[140px] overflow-hidden z-0">
                {activeMarker.coverBannerType === 'video' ? (
                  <Skeleton loading={bannerLoading} width="100%" height="100%">
                    <video
                      src={activeMarker.coverBanner}
                      autoPlay
                      muted
                      loop
                      playsInline
                      className="object-cover w-full h-full absolute top-0 left-0"
                      onLoadedData={() => setBannerLoading(false)}
                      onError={() => setBannerLoading(false)}
                    />
                  </Skeleton>
                ) : (
                  <Skeleton loading={bannerLoading} width="100%" height="100%">
                    <NextImage
                      src={activeMarker.coverBanner || '/images/user/default-banner.webp'}
                      alt={activeMarker.username || 'User'}
                      width={1000}
                      height={320}
                      className="object-cover w-full h-full absolute top-0 left-0"
                      onLoad={() => setBannerLoading(false)}
                      onError={() => setBannerLoading(false)}
                    />
                  </Skeleton>
                )}
              </div>

              {/* Circular Profile Image */}
              <div className="flex flex-col items-center justify-center !pt-16 relative z-10">
                <div className="w-24 h-24 rounded-full border border-solid border-white overflow-hidden">
                  <img
                    src={activeMarker.profilePhoto || '/images/user/default-avatar.webp'}
                    alt={activeMarker.username || 'User'}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Username with Verification Badge */}
                <h3 className="text-2xl font-bold mt-0 flex items-center gap-1 text-gray-600 dark:text-white">
                  {activeMarker.displayName || 'User'}
                  {activeMarker.isVerified && (
                    <img className="w-4 h-4" alt="Verified User" src="/images/user/verified.png" />
                  )}
                </h3>

                {/* Handle */}
                <p className="text-gray-400 mb-3 w-full text-center">@{activeMarker.username || 'user'}</p>

                {/* Subscribe Button */}
                <div className="flex w-full justify-between items-center">
                  <button
                    className="bg-turquoise hover:bg-[#4EAFAF] text-white py-1 px-4 rounded-md flex items-center justify-center w-full mr-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      window.location.href = `/user/${activeMarker.username}`;
                    }}
                  >
                    View Profile
                  </button>
                </div>
              </div>
            </div>
          </Popup>
        )}

        <NavigationControl showCompass={false} />
      </Map>
    </div>
  );
};
export default MapContainer;