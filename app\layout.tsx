import { Metadata } from "next";
import Navigation from "@/components/layout/header/Navigation";
import BackToTop from '@/components/BackToTop';
import Loading from '@/components/Loader';
import { RouteChangeProvider } from '@/components/RouteChangeProvider';
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";
import Providers from '@/components/Providers';
//import { ReactScan } from '@/components/ReactScan';
import OneSignalProvider from '@/components/OneSignalProvider';
import { headers } from 'next/headers';
import { ConvexAuthNextjsServerProvider } from "@convex-dev/auth/nextjs/server";
import '@/styles/style.css';
import '@/utils/polyfills';
import "./globals.css";

export const metadata: Metadata = {
  title: {
    default: 'Sugar Club',
    template: '%s | Sugar Club'
  },
  description: "",
  icons: {
    icon: '/favicon.ico',
  },
};

export default async function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Get headers for user agent detection if needed
  const headersList = headers();
  const userAgent = (await headersList).get('user-agent') || '';
  const isMobile = /mobile/i.test(userAgent);

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="keywords" content="" />
        <meta name="author" content="" />
        <meta name="copyright" content="" />
        <meta name="robots" content="index,follow" />
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="theme-color" content="#000000" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="msapplication-TileImage" content="/ms-icon-144x144.png" />
        <meta property="og:locale" content="en_GB" />
        <meta property="og:site_name" content="" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="" />
        <meta property="og:description" content="" />
        <meta property="og:url" content="https://sugarclub.com/" />
        <meta property="og:image" content="" />
        <meta property="og:image:secure_url" content="" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@SugarClub" />
        <meta name="twitter:title" content="Sugar Club" />
        <meta name="twitter:description" content="" />
        <meta name="twitter:image" content="" />
      </head>
      <body className={`antialiased`}>
        <Providers>
          <OneSignalProvider>
            <RouteChangeProvider>
              <Loading />
              <Navigation isMobile={isMobile}>
                <main className="pt-20">
                  {children}
                </main>
              </Navigation>
            </RouteChangeProvider>
          </OneSignalProvider>
        </Providers>

        {/* <ReactScan /> */}
        <Analytics />
        <SpeedInsights />
        <BackToTop />
      </body>
    </html >
  );
};