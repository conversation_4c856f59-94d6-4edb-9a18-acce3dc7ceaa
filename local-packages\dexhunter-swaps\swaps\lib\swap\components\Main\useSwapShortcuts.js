import { u as D } from "../../../QueryClientProvider-6bcd4331.js";
import l from "../../../store/useStore.js";
import { s as x } from "../../../shallow-27fd7e97.js";
import { useEffect as A } from "react";
import { round as n } from "../../../utils/formatNumber.js";
import { u as s } from "../../../react-hotkeys-hook.esm-60f1c3b8.js";
import "react/jsx-runtime";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../tokens.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
const z = () => {
  const a = D(), { flipTokens: r } = l(
    (e) => ({
      flipTokens: e.swapSlice.flipTokens,
      sellAmount: e.swapSlice.sellAmount,
      buyAmount: e.swapSlice.buyAmount,
      tokenSell: e.swapSlice.tokenSell,
      tokenBuy: e.swapSlice.tokenBuy,
      dexBlacklist: e.swapSlice.dexBlacklist,
      inputMode: e.swapSlice.inputMode
    }),
    x
  ), {
    slippage: p,
    setisDexSplitting: S,
    setLocalRouting: m,
    setSlippage: i,
    setIsCustomSlippage: c,
    isOpenSwapSetting: g,
    setIsOpenSwapSetting: u,
    setIsAutomaticSlippage: w,
    isCustomSlippage: f
  } = l((e) => e.swapSettingsSlice), k = () => {
    a.invalidateQueries({
      predicate: (e) => e.queryKey[0] === "swapDetails"
    }), r();
  }, d = () => {
    u(!g);
  };
  A(() => {
    const e = localStorage.getItem("swapSettings");
    if (e) {
      const t = JSON.parse(e);
      S(t.isDexSplitting), m(t.localRouting), i(t.slippage), c(t.isCustomSlippage), w(t.isAutomaticSlippage);
    } else
      i(5);
  }, []);
  const o = (e) => {
    const t = {
      slippage: p,
      isCustomSlippage: f,
      ...e
    };
    localStorage.setItem("swapSettings", JSON.stringify(t));
  }, y = () => {
    const e = n(Math.min(p + 1, 500), 1);
    i(e), o({ slippage: e });
  }, h = () => {
    const e = n(Math.max(p - 1, 1), 1);
    i(e), o({ slippage: e });
  };
  s("s", d), s("k", (e) => {
    e.preventDefault(), k();
  }), s("e", (e) => {
    e.preventDefault(), y();
  }), s("q", (e) => {
    e.preventDefault(), h();
  });
};
export {
  z as useSwapShortcuts
};
