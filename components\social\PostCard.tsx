'use client';

import React, { useState, useEffect, useRef, useMemo, useCallback, memo } from 'react';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { UserHoverCard } from '@/components/ui/hover-card';
import { Button } from '@/components/ui/button';
import Tippy from '@tippyjs/react';
import {
  Link as LinkIcon,
  Bookmark,
  Pencil,
  Trash2,
  Flag,
  MoreVertical,
  Eye,
  Ban,
  Share2,
  Pin,
  Play,
  X,
  Image as ImageIcon,
  Music,
  Pause
} from 'lucide-react';
import { FaRegComment } from 'react-icons/fa';
import { AiOutlineDollarCircle } from 'react-icons/ai';
import VideoPlayerWithBlur from '@/components/media/VideoPlayerWithBlur';
import GifPlayerWithBlur from '@/components/media/GifPlayerWithBlur';
import { useUser } from '@/hooks/useUser';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import CommentForm from '@/components/social/CommentForm';
import CommentCard from '@/components/social/CommentCard';
import { AnimatePresence, motion } from 'framer-motion';
import { toast } from 'react-toastify';
import { elFormatter } from '@/public/main';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Swal from 'sweetalert2';
import ReportPostDialog from '@/components/dialogs/ReportPostDialog';
import TaggedUsersDialog from '@/components/dialogs/TaggedUsersDialog';
import EditPostDialog from '@/components/dialogs/EditPostDialog';
import TipUserDialog from '@/components/dialogs/TipUserDialog';
import { useIsCurrentUser } from '@/hooks/useIsCurrentUser';
import { Post, Comment } from '@/types/post';
import { useSocket } from '@/context/SocketContext';
import AudioPlayer from '@/components/media/AudioPlayer';
import { useViewTracker } from '@/hooks/useViewTracker';
import { usePostImagePreloader } from '@/hooks/useImagePreloader';
import BlockUserDialog from '@/components/dialogs/BlockUserDialog';

interface MediaItem {
  mediaUrl: string;
  mediaType: string;
  mediaDescription?: string;
  thumbnailUrl?: string;
  duration?: string | number;
  resolution?: string;
}

interface PostUser {
  id: string;
  username: string;
  name: string;
  profileImage?: string;
  verified?: boolean;
}

interface TaggedUser {
  id: string;
  username: string;
  name: string;
  profileImage: string;
}

interface PostCardProps {
  post: Post;
  isLiked: boolean | undefined;
  onOpenLightbox: (post: Post, mediaArray: any[], initialIndex: number) => void;
  onPostUpdated?: (postId: string, updatedPost: Post) => void;
  onPostDeleted?: (postId: string) => void;
  onLike: (postId: string) => void;
  onSave: (post: Post) => void;
  onDelete: (postId: string) => void;
  onReport: (postId: string, reason: string) => void;
  onComment: (postId: string, content: string) => void;
  onShare: (postId: string) => void;
  onTrackView: (contentId: string, contentType: string, ipAddress: string, userAgent: string, sessionId: string) => void;
}

const PostCard: React.FC<PostCardProps> = ({
  post: initialPost,
  isLiked,
  onOpenLightbox,
  onPostUpdated,
  onPostDeleted,
  onLike,
  onSave,
  onDelete,
  onReport,
  onComment,
  onShare,
  onTrackView
}) => {
  const router = useRouter();
  const { isAuthenticated, user } = useUser();
  const isLoggedIn = isAuthenticated;

  // Use user.id for current user check
  const isCurrentUser = useIsCurrentUser(initialPost?.user?.id);

  // Consolidated state for better performance (only for post content, not likes/saves)
  const [post, setPost] = useState<Post>({
    ...initialPost,
    media: initialPost.media || [],
    views: initialPost.views || 0,
    comments: initialPost.comments || 0,
    is_edited: initialPost.is_edited || false
  });
  const [uiState, setUiState] = useState({
    showComments: false,
    showTaggedUsers: false,
    isEditDialogOpen: false,
    isReportDialogOpen: false,
    isShareDialogOpen: false,
    isTipDialogOpen: false,
  });
  const [loadingState, setLoadingState] = useState({
    isLiking: false,
    isDeleting: false,
    isSaving: false,
    isVerifiedBadgeLoading: true,
    isLoadingMedia: true,
  });
  const [interactionState, setInteractionState] = useState({
    comments: [] as Comment[],
    tipCount: 0,
    viewTracked: false,
  });

  const likeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { socket, isConnected, joinPostRoom, leavePostRoom } = useSocket();

  const mediaLoadedRef = useRef(false);

  // Preload images for better performance
  const { preloadStatus, getImageStatus, isAllLoaded } = usePostImagePreloader(
    post.media || [],
    { priority: 'medium', maxConcurrent: 2 }
  );

  const Toast = () => toast('');

  // Optimized handleLike with consolidated state
  const handleLike = useCallback(async () => {
    if (loadingState.isLiking || !isLoggedIn) {
      if (!isLoggedIn) {
        toast.error('You must be logged in to perform this action!');
      }
      return;
    }

    // Clear any existing timeout
    if (likeTimeoutRef.current) {
      clearTimeout(likeTimeoutRef.current);
    }

    // Immediate state update to prevent double-clicks
    setLoadingState(prev => ({ ...prev, isLiking: true }));

    // Add a small delay to ensure state update is processed
    likeTimeoutRef.current = setTimeout(async () => {
      try {
        onLike(post.id);
      } catch (error) {
        console.error('Error liking post:', error);
        toast.error('Failed to like post');
      } finally {
        setLoadingState(prev => ({ ...prev, isLiking: false }));
      }
    }, 50);
  }, [loadingState.isLiking, isLoggedIn, onLike, post.id]);

  // Optimized handleSave with consolidated state
  const handleSave = useCallback(async () => {
    if (!isLoggedIn) {
      toast.error('You must be logged in to perform this action!');
      return;
    }

    setLoadingState(prev => ({ ...prev, isSaving: true }));
    try {
      const save = await onSave(post);
    } catch (err: any) {
      toast.error(err.message || 'Failed to save post');
    } finally {
      setLoadingState(prev => ({ ...prev, isSaving: false }));
    }
  }, [isLoggedIn, onSave, post]);

  const handleCommentClick = useCallback(() => {
    setUiState(prev => ({ ...prev, showComments: !prev.showComments }));
  }, []);

  const handleNewComment = useCallback(async (commentData: { postId: string; content: string }) => {
    try {
      onComment(post.id, commentData.content);
    } catch (err) {
      toast.error('Failed to add comment');
    }
  }, [onComment, post.id]);

  const handleShare = useCallback(async () => {
    onShare(post.id);
  }, []);

  // Optimized handleReport with consolidated state
  const handleReport = useCallback(() => {
    setUiState(prev => ({ ...prev, isReportDialogOpen: true }));
  }, []);

  const handleReportSubmit = useCallback(async (reason: string) => {
    try {
      toast.success('Post reported');
      setUiState(prev => ({ ...prev, isReportDialogOpen: false }));
    } catch (err: any) {
      toast.error('Failed to report post');
    }
  }, []);

  // Optimized handleDelete with consolidated state
  const handleDelete = useCallback(async () => {
    const confirmed = await Swal.fire({
      title: 'Delete Post',
      text: 'Are you sure you want to delete this post? This action cannot be undone.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: 'var(--turquoise)',
      cancelButtonColor: '#6e6e6e',
      confirmButtonText: 'Yes, delete it!'
    });

    if (confirmed.isConfirmed && isCurrentUser) {
      setLoadingState(prev => ({ ...prev, isDeleting: true }));
      try {
        onDelete(post.id);
        onPostDeleted?.(post.id);
        toast.success('Post deleted successfully');
      } catch (error) {
        toast.error('Failed to delete post');
        console.error('Error deleting post:', error);
      } finally {
        setLoadingState(prev => ({ ...prev, isDeleting: false }));
      }
    } else if (!isCurrentUser) {
      toast.error('You must be the creator of this post to delete it!');
    }
  }, [isCurrentUser, onDelete, post.id, onPostDeleted]);

  // Memoized timestamp formatting to prevent recalculation on every render
  const [currentTime, setCurrentTime] = useState(Date.now());

  const formatTimestamp = useCallback((timestamp: string) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '';

    const diff = Math.floor((currentTime - date.getTime()) / 1000);

    if (diff < 60) return 'just now';
    if (diff < 3600) {
      const minutes = Math.floor(diff / 60);
      return `${minutes}m`;
    }
    if (diff < 86400) {
      const hours = Math.floor(diff / 3600);
      return `${hours}h`;
    }
    if (diff < 604800) {
      const days = Math.floor(diff / 86400);
      return `${days}d`;
    }
    if (diff < 2628000) {
      const weeks = Math.floor(diff / 604800);
      return `${weeks}w`;
    }
    if (diff < 31536000) {
      const months = Math.floor(diff / 2628000);
      return `${months}mo`;
    }
    const years = Math.floor(diff / 31536000);
    return `${years}y`;
  }, [currentTime]);

  // Memoized formatted timestamps
  const formattedCreatedAt = useMemo(() => formatTimestamp(post.createdAt || ''), [formatTimestamp, post.createdAt]);
  const formattedEditedAt = useMemo(() => post.edited_at ? formatTimestamp(post.edited_at) : '', [formatTimestamp, post.edited_at]);

  // Memoized function to replace hashtags and mentions with links
  const renderContentWithLinks = useMemo(() => {
    if (!post.content) return null;

    // First, normalize line breaks and remove extra spaces
    let processedContent = post.content
      .replace(/\r?\n/g, '\n')
      .replace(/[ \t]+/g, ' ')
      .trim();

    // Escape HTML to prevent XSS
    const escapedContent = processedContent.replace(/[&<>"']/g, char => ({
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
    })[char] || char);

    // Split content into parts to handle hashtags and mentions
    const parts = escapedContent.split(/(\s+)/);
    const elements: React.ReactNode[] = [];

    parts.forEach((part, index) => {
      // Handle hashtags
      if (part.match(/^#[\w\u0590-\u05ff]+$/)) {
        const hashtag = part.slice(1);
        elements.push(
          <a
            key={`hashtag-${index}`}
            href={`/hashtag/${hashtag}`}
            className="text-turquoise hover:underline inline-flex"
          >
            {part}
          </a>
        );
      } else if (part.match(/^@[\w]+$/)) {
        const username = part.slice(1);
        elements.push(
          post?.mentions?.find((mention: any) => mention.username === username) ? (
            <UserHoverCard
              key={`mention-${index}`}
              username={username}
              userData={post.mentions.find((mention: any) => mention.username === username)}
            >
              <a
                href={`/user/${username}`}
                className="text-turquoise hover:underline inline-flex"
              >
                {part}
              </a>
            </UserHoverCard>
          ) : (
            <a
              key={`mention-${index}`}
              href={`/user/${username}`}
              className="text-turquoise hover:underline inline-flex"
            >
              {part}
            </a>
          )
        );
      } else {
        elements.push(part);
      }
    });

    return (
      <div className="mb-4 whitespace-pre-wrap break-words text-gray-600 dark:text-white">
        {elements}
      </div>
    );
  }, [post.content, post.mentions]);

  const handlePostUpdate = (updatedPost: Post) => {
    if (!post.id) return;
    setPost(updatedPost);
    onPostUpdated?.(post.id, updatedPost);
  };

  const handleTip = useCallback(() => {
    if (!post?.user) {
      toast.error('Cannot tip - user information not available');
      return;
    }
    setUiState(prev => ({ ...prev, isTipDialogOpen: true }));
  }, [post?.user]);

  const handlePin = useCallback(() => {
    // Pin functionality to be implemented
  }, []);

  // Optimized media handling with preloader integration
  const handleMediaLoad = useCallback(() => {
    if (!mediaLoadedRef.current) {
      mediaLoadedRef.current = true;
      setLoadingState(prev => ({ ...prev, isLoadingMedia: false }));
    }
  }, []);

  const handleMediaError = useCallback(() => {
    if (!mediaLoadedRef.current) {
      console.error(`[PostCard ${post.id}] Media failed to load`);
      mediaLoadedRef.current = true;
      setLoadingState(prev => ({ ...prev, isLoadingMedia: false }));
    }
  }, [post.id]);

  // Update loading state based on preloader status
  useEffect(() => {
    if (isAllLoaded && !mediaLoadedRef.current) {
      mediaLoadedRef.current = true;
      setLoadingState(prev => ({ ...prev, isLoadingMedia: false }));
    }
  }, [isAllLoaded]);

  // Render media gallery based on the number of images
  const renderMediaGallery = () => {
    const mediaItems = post.media || [];
    const mediaCount = mediaItems.length;

    if (mediaCount === 0) return null;

    return (
      <div className="relative rounded-md overflow-hidden cursor-pointer">
        {/* Picture counter */}
        {post.media?.[0]?.mediaType?.includes('image') && !post.media?.[0]?.mediaType?.includes('gif') && (
          <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1 z-[1]">
            <ImageIcon className="h-4 w-4 dark:text-white" /> {mediaCount}
          </div>
        )}

        {/* Skeleton while loading */}
        {post.media?.[0]?.mediaType?.includes('image') ? (
          post.media?.[0]?.mediaType?.includes('gif') ? (
            // GIF handling
            <GifPlayerWithBlur
              gifUrl={mediaItems[0].mediaUrl || ''}
              onClick={() => onOpenLightbox(post, mediaItems, 0)}
            />
          ) : (
            // Regular image handling
            <Skeleton loading={loadingState.isLoadingMedia} width="100%" height="100%" borderRadius="6px">
              <div className="grid grid-cols-2 gap-1">
                {mediaCount === 1 && (
                  <img
                    src={mediaItems[0].mediaUrl}
                    alt={mediaItems[0].mediaDescription || 'Post media'}
                    className="w-full h-auto object-cover aspect-[1] col-span-2"
                    onLoad={handleMediaLoad}
                    onError={handleMediaError}
                    onClick={() => onOpenLightbox(post, mediaItems, 0)}
                  />
                )}
                {mediaCount === 2 && (
                  <>
                    {mediaItems.slice(0, 2).map((media: any, index: number) => (
                      <img
                        key={index}
                        src={media.mediaUrl}
                        alt={media.mediaDescription || `Post media ${index + 1}`}
                        className="w-full h-auto object-cover aspect-square"
                        onLoad={handleMediaLoad}
                        onError={handleMediaError}
                        onClick={() => onOpenLightbox(post, mediaItems, index)}
                      />
                    ))}
                  </>
                )}
                {mediaCount === 3 && (
                  <>
                    <img
                      src={mediaItems[0].mediaUrl}
                      alt={mediaItems[0].mediaDescription || "Post media 1"}
                      className="w-full h-auto object-cover aspect-square col-span-2"
                      onLoad={handleMediaLoad}
                      onError={handleMediaError}
                      onClick={() => onOpenLightbox(post, mediaItems, 0)}
                    />
                    <div className="grid grid-cols-2 gap-1 col-span-2">
                      {mediaItems.slice(1, 3).map((media: any, index: number) => (
                        <img
                          key={index + 1}
                          src={media.mediaUrl}
                          alt={media.mediaDescription || `Post media ${index + 2}`}
                          className="w-full h-auto object-cover aspect-square"
                          onLoad={handleMediaLoad}
                          onError={handleMediaError}
                          onClick={() => onOpenLightbox(post, mediaItems, index + 1)}
                        />
                      ))}
                    </div>
                  </>
                )}
                {(mediaCount === 4 || mediaCount > 4) && (
                  <>
                    {mediaItems.slice(0, 4).map((media: any, index: number) => (
                      <div key={index} className="relative">
                        <img
                          src={media.mediaUrl}
                          alt={media.mediaDescription || `Post media ${index + 1}`}
                          className="w-full h-auto object-cover aspect-square"
                          onLoad={handleMediaLoad}
                          onError={handleMediaError}
                          onClick={() => onOpenLightbox(post, mediaItems, index)}
                        />
                        {index === 3 && mediaCount > 4 && (
                          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                            <span className="text-white text-2xl font-bold">
                              +{mediaCount - 4}
                            </span>
                          </div>
                        )}
                      </div>
                    ))}
                  </>
                )}
              </div>
            </Skeleton>
          )
        ) : post.media?.[0]?.mediaType?.includes('video') ? (
          <VideoPlayerWithBlur
            thumbnailUrl={post.media?.[0]?.thumbnailUrl || ''}
            onClick={() => onOpenLightbox(post, mediaItems, 0)}
            duration={post.media?.[0]?.duration?.toString()}
            resolution={post.media?.[0]?.resolution}
          />
        ) : post.media?.[0]?.mediaType?.includes('audio') ? (
          <Skeleton loading={loadingState.isLoadingMedia} width="100%" height="100%" borderRadius="6px">
            <AudioPlayer
              audioUrl={post.media[0].mediaUrl || ''}
              duration={typeof post.media[0].duration === 'string' ? parseInt(post.media[0].duration) : post.media[0].duration}
              onLoad={handleMediaLoad}
              onError={handleMediaError}
            />
          </Skeleton>
        ) : null}

        {post.taggedUsers && post.taggedUsers.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            className="text-turquoise hover:underline mt-2"
            onClick={() => setUiState(prev => ({ ...prev, showTaggedUsers: true }))}
          >
            View Tagged Users ({post.taggedUsers.length})
          </Button>
        )}
      </div>
    );
  };

  useEffect(() => {
    if (isConnected) {
      joinPostRoom(initialPost.id);
      return () => {
        leavePostRoom(initialPost.id);
      };
    }
  }, [isConnected, initialPost.id]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (likeTimeoutRef.current) {
        clearTimeout(likeTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(Date.now());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Optimized view tracking with centralized intersection observer
  const handleViewTracking = useCallback(async () => {
    if (interactionState.viewTracked) return;

    const contentId = post.id;
    const contentType = post.contentType || 'post';
    const userAgent = typeof window !== 'undefined' ? window.navigator.userAgent : '';

    try {
      // Fetch IP address for anonymous users
      let ipAddress = '0.0.0.0';
      let sessionId: string | undefined = undefined;

      if (!isLoggedIn) {
        try {
          const res = await fetch('https://api.ipify.org?format=json');
          const data = await res.json();
          ipAddress = data.ip;
          sessionId = btoa(`${ipAddress}-${userAgent}`);
        } catch {
          ipAddress = '0.0.0.0';
        }
      }

      // Call the onTrackView callback if provided
      onTrackView?.({ contentId, contentType, ipAddress, userAgent, sessionId: sessionId || '' });

      // Update local state
      setInteractionState(prev => ({ ...prev, viewTracked: true }));

      console.log(`[PostCard ${contentId}] View tracked`);
    } catch (error) {
      console.error('Error tracking view:', error);
    }
  }, [post.id, post.contentType, isLoggedIn, onTrackView, interactionState.viewTracked]);

  // Use centralized view tracker
  useViewTracker(post.id, handleViewTracking, {
    threshold: 0.5,
    trackOnce: true
  });

  // Add a timeout to ensure the skeleton doesn't persist indefinitely
  useEffect(() => {
    if (loadingState.isLoadingMedia) {
      const timeout = setTimeout(() => {
        if (!mediaLoadedRef.current) {
          setLoadingState(prev => ({ ...prev, isLoadingMedia: false }));
        }
      }, 5000);

      return () => clearTimeout(timeout);
    }
  }, [loadingState.isLoadingMedia, post.id]);

  const [isBlockDialogOpen, setIsBlockDialogOpen] = useState(false);

  // Copy post link handler
  const handleCopyPostLink = useCallback(async () => {
    try {
      const postUrl = `${window.location.origin}/post/${post.id}`;
      await navigator.clipboard.writeText(postUrl);
      toast.success('Post link copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy post link');
    }
  }, [post.id]);

  // Block user handler
  const handleBlockUser = useCallback(() => {
    if (!isLoggedIn) {
      toast.error('You must be logged in to block a user');
      return;
    }
    setIsBlockDialogOpen(true);
  }, [isLoggedIn]);

  return (
    <>
      <Card id={`post-${post.id}`} className="mb-4 bg-gray-50 dark:bg-zinc-900 relative shadow-none border border-solid border-gray-300 dark:border-gray-400">
        {loadingState.isDeleting && (
          <div className="absolute inset-0 bg-gray-50/50 dark:bg-zinc-900/50 backdrop-blur-md z-[10] flex items-center justify-center rounded-lg">
            <div className="flex flex-col items-center gap-2">
              <div className="h-8 w-8">
                <span className="mention-search-loader"></span>
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Deleting post...
              </span>
            </div>
          </div>
        )}

        <CardHeader className="flex flex-row items-center gap-[12px] pt-5 pb-2">
          <Link href={`/user/${post?.user?.username}`}>
            <Avatar className="h-14 w-14 border border-solid border-gray-300 dark:border-white">
              <AvatarImage src={post?.user?.profileImage || '/images/user/default-avatar.webp'} alt={post?.user?.name} />
              <AvatarFallback>{post?.user?.username.charAt(0)}</AvatarFallback>
            </Avatar>
          </Link>
          <div className="flex-1 !mt-0">
            <Link href={`/user/${post?.user?.username}`}>
              <h3 className="font-semibold leading-[1.45] dark:text-white">{post?.user?.name}{post?.user?.verified && (
                <Skeleton loading={loadingState.isVerifiedBadgeLoading} width="16px" height="16px" className="inline-block">
                  <img className="w-4 h-4 ml-1 user-post-verified-badge" alt="Verified User" src="/images/user/verified.png" onError={() => setLoadingState(prev => ({ ...prev, isVerifiedBadgeLoading: false }))} onLoad={() => setLoadingState(prev => ({ ...prev, isVerifiedBadgeLoading: false }))} />
                </Skeleton>
              )}</h3>
            </Link>
            <div className="text-sm text-gray-500">
              <span className="text-sm text-gray-400 dark:text-white">@{post?.user?.username}</span>
            </div>
          </div>

          {/* Post Actions */}
          <div className="flex items-start space-x-2 space-y-0 !-mt-3 !-mr-4">
            <p className="text-sm text-blue-gray dark:text-white -mt-[0.5px]">
              <span>{formattedCreatedAt}</span>
              {post.is_edited && (
                <span className="ml-1 italic"> • (edited {formattedEditedAt})</span>
              )}
            </p>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-4 justify-center py-4 !px-4 !-mt-[7px] text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise hover:bg-accent dark:hover:bg-gray-700 rounded-md"
                  disabled={loadingState.isDeleting}
                >
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {(isCurrentUser && isLoggedIn) ? (
                  <>
                    <DropdownMenuItem onClick={() => setUiState(prev => ({ ...prev, isEditDialogOpen: true }))} className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none">
                      <Pencil className="mr-2 h-4 w-4" />
                      Edit Post
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handlePin} className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none">
                      <Pin className="mr-2 h-4 w-4" />
                      Pin Post
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleDelete} className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Post
                    </DropdownMenuItem>
                  </>
                ) : (
                  <>
                    <DropdownMenuItem onClick={handleCopyPostLink} className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none cursor-pointer">
                      <LinkIcon className="mr-2 h-4 w-4" />
                      Copy Post Link
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleReport} className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none cursor-pointer">
                      <Flag className="mr-2 h-4 w-4" />
                      Report Post
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleBlockUser} className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none cursor-pointer">
                      <Ban className="mr-2 h-4 w-4" />
                      Block User
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="pt-[6.5px] pb-[14px]">
          {renderContentWithLinks}

          {(post.media?.length > 0) && (
            renderMediaGallery()
          )}

          <TaggedUsersDialog
            isOpen={uiState.showTaggedUsers}
            onClose={() => setUiState(prev => ({ ...prev, showTaggedUsers: false }))}
            taggedUsers={post.taggedUsers?.map((user: any) => ({
              id: user.id,
              username: user.username,
              name: user.name,
              profileImage: user.profileImage
            })) || []}
          />
        </CardContent>

        <CardFooter className="flex flex-col pb-[14px]">
          {/* Likes, views, comments, save, share, and edit buttons */}
          <div className="flex justify-between w-full pt-0">
            <Tippy content="Views" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                disabled={loadingState.isDeleting}
                className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
              >
                <Eye className="mr-1 h-5 w-5 relative" />
                <span className="relative top-[1px]">{elFormatter(post.views) || 0}</span>
              </Button>
            </Tippy>

            <Tippy content="Like" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLike}
                disabled={loadingState.isLiking || loadingState.isDeleting}
                className={`relative group dark:hover:bg-gray-700 ${isLiked ? "text-hot-pink hover:text-hot-pink dark:text-hot-pink dark:hover:text-hot-pink" : "text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise"} heart-container-parent`}
              >
                <div className="heart-container">
                  <input
                    type="checkbox"
                    className="checkbox"
                    checked={isLiked}
                    onChange={() => { }}
                  />

                  <div className="svg-container">
                    <svg viewBox="0 0 24 24" className="svg-outline w-5 h-5">
                      <path
                        fill="none"
                        stroke="currentColor"
                        strokeWidth={isLiked ? '0' : '2'}
                        d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09 C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5 c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
                      />
                    </svg>

                    <svg viewBox="0 0 24 24" className="svg-filled w-5 h-5">
                      <path
                        d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09 C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5 c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
                      />
                    </svg>

                    <svg viewBox="0 0 24 24" className="svg-celebrate w-5 h-5">
                      <path
                        d="M12,21.35l-1.45-1.32C5.4,15.36 2,12.28 2,8.5 2,5.42 4.42,3 7.5,3c1.74,0 3.41,0.81 4.5,2.09 C13.09,3.81 14.76,3 16.5,3 19.58,3 22,5.42 22,8.5 c0,3.78-3.4,6.86-8.55,11.54L12,21.35z"
                      />
                    </svg>
                  </div>
                </div>
                <span className="ml-1 relative top-[1px]">{elFormatter(post.likes)}</span>
              </Button>
            </Tippy>

            <Tippy content="Comment" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCommentClick}
                disabled={loadingState.isDeleting}
                className={uiState.showComments ? "text-turquoise hover:text-turquoise dark:bg-gray-700" : "text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"}
              >
                <FaRegComment className="mr-1 h-[19px] w-[19px]" />
                <span className="relative top-[1px]">{elFormatter(interactionState.comments.length) || elFormatter(post.comments)}</span>
              </Button>
            </Tippy>

            <Tippy content="Send Tip" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleTip}
                className={"text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"}
              >
                <AiOutlineDollarCircle className="mr-1 h-[22px] w-[22px]" />
                <span className="relative top-[1px]">{elFormatter(interactionState.tipCount)}</span>
              </Button>
            </Tippy>

            <Tippy content="Save" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSave}
                disabled={loadingState.isDeleting || loadingState.isSaving}
                className={post.saved ? "text-turquoise hover:text-turquoise dark:hover:bg-gray-700" : "text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"}
              >
                <label className="ui-bookmark">
                  <input
                    type="checkbox"
                    className="checkbox"
                    checked={post.saved}
                    onChange={() => { }}
                    disabled={loadingState.isSaving}
                  />
                  <div className="bookmark">
                    <Bookmark className={`h-5 w-5 mr-1 ${post.saved ? 'fill-[var(--turquoise)] dark:fill-[var(--turquoise)] text-turquoise' : ''}`} />
                    <span className="relative top-[1px]">{elFormatter(post.saves) || 0}</span>
                  </div>
                </label>
              </Button>
            </Tippy>

            <Tippy content="Share" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
              <Button
                variant="ghost"
                size="sm"
                disabled={loadingState.isDeleting}
                className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
                onClick={handleShare}
              >
                <Share2 className="mr-1 h-5 w-5" />
              </Button>
            </Tippy>
          </div>

          {/* Comments */}
          <AnimatePresence>
            {uiState.showComments && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="w-full mt-[14px]"
              >
                <div className="space-y-4 w-full">
                  <CommentForm
                    postId={post.id}
                    onCommentAdded={handleNewComment}
                  />

                  {interactionState.comments.length > 0 && (
                    <div className="space-y-2 w-full">
                      {interactionState.comments.map((comment: any) => (
                        <CommentCard
                          key={comment.id}
                          comment={comment}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardFooter>
      </Card>

      {uiState.isReportDialogOpen && (
        <ReportPostDialog
          isOpen={uiState.isReportDialogOpen}
          onClose={() => setUiState(prev => ({ ...prev, isReportDialogOpen: false }))}
          postId={post.id}
          onReportSubmit={handleReportSubmit}
        />
      )}

      {uiState.isEditDialogOpen && (
        <EditPostDialog
          isOpen={uiState.isEditDialogOpen}
          onClose={() => setUiState(prev => ({ ...prev, isEditDialogOpen: false }))}
          post={post}
          onPostUpdated={handlePostUpdate}
        />
      )}

      {uiState.isTipDialogOpen && (
        <TipUserDialog
          isOpen={uiState.isTipDialogOpen}
          onClose={() => setUiState(prev => ({ ...prev, isTipDialogOpen: false }))}
          postId={post.id}
          userData={{
            username: post?.user?.username || '',
            displayName: post?.user?.name || '',
            profileImage: post?.user?.profileImage || '/images/user/default-avatar.webp'
          }}
        />
      )}

      {/* Block User Dialog */}
      {post?.user?.id && post?.user?.username && (
        <BlockUserDialog
          isOpen={isBlockDialogOpen}
          onClose={() => setIsBlockDialogOpen(false)}
          userId={post.user.id}
          username={post.user.username}
          profilePhoto={post.user.profileImage}
        />
      )}
    </>
  );
};
export default PostCard;