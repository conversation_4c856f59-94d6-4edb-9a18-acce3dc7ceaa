import { jsxs as n, jsx as s } from "react/jsx-runtime";
import { useMemo as a } from "react";
import o from "../../../store/useStore.js";
import { cn as i } from "../../../lib/utils.js";
import r from "../../../assets/svg/IconSetting.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../tokens.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
import "../../../extend-tailwind-merge-e63b2b56.js";
const M = () => {
  const { slippage: e } = o((h) => h.swapSettingsSlice), { isAutomaticSlippage: p, setIsOpenSwapSetting: d, isOpenSwapSetting: t } = o((h) => h.swapSettingsSlice);
  return a(() => e === -1 ? /* @__PURE__ */ n(
    "div",
    {
      className: i(
        "dhs-rounded-xl dhs-bg-containers dhs-font-semibold dhs-justify-between dhs-h-[40px] sm:dhs-h-[30px] dhs-px-4 sm:dhs-px-3 dhs-gap-2 dhs-cursor-pointer dhs-transition-all dhs-duration-200 dhs-flex dhs-items-center"
      ),
      onClick: () => d(!t),
      children: [
        /* @__PURE__ */ s(
          "img",
          {
            src: "https://storage.googleapis.com/dexhunter-images/public/infinity-red.svg",
            className: "dhs-w-[24px]"
          }
        ),
        /* @__PURE__ */ s(r, { fontSize: 20, className: "dhs-text-destructive" })
      ]
    }
  ) : p ? /* @__PURE__ */ n(
    "div",
    {
      className: i(
        "dhs-rounded-xl dhs-bg-containers dhs-font-semibold dhs-justify-between dhs-h-[40px] sm:dhs-h-[30px] dhs-px-4 sm:dhs-px-3 dhs-gap-2 dhs-cursor-pointer dhs-transition-all dhs-duration-200 dhs-flex dhs-items-center"
      ),
      onClick: () => d(!t),
      children: [
        /* @__PURE__ */ s("span", { className: "dhs-text-sm dhs-text-accent dhs-leading-none", children: "Auto" }),
        /* @__PURE__ */ s(r, { fontSize: 20, className: "dhs-text-accent" })
      ]
    }
  ) : e >= 0 ? /* @__PURE__ */ n(
    "div",
    {
      className: i(
        "dhs-rounded-xl dhs-bg-containers dhs-font-semibold dhs-flex dhs-justify-between dhs-items-center dhs-h-[40px] sm:dhs-h-[30px] dhs-px-4 sm:dhs-px-3 dhs-gap-2 dhs-cursor-pointer dhs-transition-all dhs-duration-200"
      ),
      onClick: () => d(!t),
      children: [
        /* @__PURE__ */ s(
          "div",
          {
            className: i(
              "dhs-text-sm dhs-text-accent dhs-flex dhs-items-center gap-1",
              e >= 10 && "dhs-text-destructive"
            ),
            children: /* @__PURE__ */ n("span", { className: "dhs-leading-none", children: [
              e,
              "%"
            ] })
          }
        ),
        /* @__PURE__ */ s(
          r,
          {
            fontSize: 20,
            className: i(
              "dhs-text-accent dhs-cursor-pointer",
              e >= 10 && "dhs-text-destructive"
            )
          }
        )
      ]
    }
  ) : /* @__PURE__ */ s("div", { className: "dhs-h-[40px] sm:dhs-h-5 dhs-flex dhs-justify-center dhs-items-center", children: /* @__PURE__ */ s(
    r,
    {
      fontSize: 20,
      className: "dhs-text-gray-103 dhs-cursor-pointer",
      onClick: () => d(!t)
    }
  ) }), [e, t]);
};
export {
  M as default
};
