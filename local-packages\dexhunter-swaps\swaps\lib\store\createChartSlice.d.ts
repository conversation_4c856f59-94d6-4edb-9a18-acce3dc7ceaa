import { DexName } from '../constants/dexes';
export interface ChartSlice {
    poolInfo: any | null;
    isLoadingData: boolean;
    isLoadingWidget: boolean;
    selectedLiquidityDexName: DexName;
    setPoolInfo: (data: any) => void;
    setIsLoadingData: (isLoadingData: boolean) => void;
    setSelectedLiquidityDexName: (dexName: DexName) => void;
    setIsLoadingWidget: (isLoadingWidget: boolean) => void;
}
declare const createChartSlice: (set: any) => {
    poolInfo: null;
    symbolInfo: null;
    isLoadingData: boolean;
    isLoadingWidget: boolean;
    selectedLiquidityDexName: "SUNDAESWAPV3" | "SPLASH" | "MINSWAPV2" | "MINSWAP" | "AXO" | "WINGRIDER" | "WINGRIDERV2" | "SNEKFUN" | "SPECTRUM" | "SUNDAESWAP" | "VYFI" | "MUESLISWAP";
    setPoolInfo: (data: any) => void;
    setIsLoadingData: (isLoadingData: boolean) => void;
    setSelectedLiquidityDexName: (dexName: DexName) => void;
    setIsLoadingWidget: (isLoadingWidget: boolean) => void;
};
export default createChartSlice;
