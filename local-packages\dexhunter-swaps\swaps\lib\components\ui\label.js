import { jsx as f } from "react/jsx-runtime";
import * as m from "react";
import { forwardRef as l, createElement as s } from "react";
import { _ as c } from "../../index-1c873780.js";
import { $ as i } from "../../index-c8f2666b.js";
import { c as n } from "../../index-1d6812f7.js";
import { cn as d } from "../../lib.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const p = /* @__PURE__ */ l((o, a) => /* @__PURE__ */ s(i.label, c({}, o, {
  ref: a,
  onMouseDown: (e) => {
    var r;
    (r = o.onMouseDown) === null || r === void 0 || r.call(o, e), !e.defaultPrevented && e.detail > 1 && e.preventDefault();
  }
}))), t = p, b = n(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
), $ = m.forwardRef(({ className: o, ...a }, e) => /* @__PURE__ */ f(
  t,
  {
    ref: e,
    className: d(b(), o),
    ...a
  }
));
$.displayName = t.displayName;
export {
  $ as Label
};
