import { jsx as C } from "react/jsx-runtime";
import { memo as t } from "react";
const e = (o) => /* @__PURE__ */ C(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 10 16",
    fill: "none",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M3.17851 0.82149C2.52764 0.170615 1.47236 0.170614 0.82149 0.821487C0.170615 1.47236 0.170614 2.52764 0.821488 3.17851L3.17851 0.82149ZM6.82149 9.17851C7.47237 9.82939 8.52764 9.82938 9.17851 9.17851C9.82939 8.52763 9.82938 7.47236 9.17851 6.82149L6.82149 9.17851ZM0.821489 12.8215C0.170615 13.4724 0.170615 14.5276 0.821489 15.1785C1.47236 15.8294 2.52764 15.8294 3.17851 15.1785L0.821489 12.8215ZM9.16924 9.18778C9.82011 8.53691 9.82011 7.48164 9.16924 6.83076C8.51836 6.17989 7.46309 6.17989 6.81222 6.83076L9.16924 9.18778ZM2 2C0.821488 3.17851 0.821503 3.17852 0.821533 3.17856C0.821563 3.17859 0.821609 3.17863 0.82167 3.17869C0.821791 3.17881 0.821973 3.179 0.822215 3.17924C0.822698 3.17972 0.823423 3.18044 0.824384 3.18141C0.826308 3.18333 0.829182 3.1862 0.832983 3.19001C0.840586 3.19761 0.8519 3.20892 0.866742 3.22376C0.896427 3.25345 0.940227 3.29725 0.996688 3.35371C1.10961 3.46663 1.27317 3.6302 1.47575 3.83277C1.88089 4.23791 2.44206 4.79908 3.06617 5.4232C4.3144 6.67143 5.8144 8.17143 6.82149 9.17851L9.17851 6.82149C8.17143 5.81441 6.67143 4.31441 5.4232 3.06618C4.79908 2.44206 4.23791 1.88089 3.83277 1.47575C3.6302 1.27318 3.46663 1.10961 3.35371 0.99669C3.29725 0.940229 3.25345 0.896429 3.22377 0.866744C3.20892 0.851902 3.19761 0.840589 3.19001 0.832985C3.18621 0.829184 3.18333 0.82631 3.18141 0.824387C3.18045 0.823424 3.17972 0.822701 3.17924 0.822217C3.179 0.821975 3.17882 0.821794 3.17869 0.821672C3.17863 0.821611 3.17859 0.821566 3.17856 0.821535C3.17853 0.821506 3.17851 0.82149 2 2ZM3.17851 15.1785L9.16924 9.18778L6.81222 6.83076L0.821489 12.8215L3.17851 15.1785Z",
        fill: "currentColor"
      }
    )
  }
), h = t(e);
export {
  h as default
};
