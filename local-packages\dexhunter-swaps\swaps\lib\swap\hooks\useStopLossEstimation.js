import l from "../../store/useStore.js";
import { server as A } from "../../config/axios.js";
import { roundNumber as F } from "../../utils/formatNumber.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
const K = () => {
  const {
    tokenSell: e,
    sellAmount: c,
    setIsTokenPriceLoading: d,
    setSwapDetails: _,
    setIsSwapDetailsLoading: i,
    setEstimationError: s,
    isTransactionLoading: f,
    swapDetails: a,
    setBuyAmount: y,
    limitPrice: g,
    limitMultiples: L
  } = l((o) => o.swapSlice), { userAddress: S, balance: w } = l((o) => o.walletSlice), h = () => {
    const o = a.total_fee + a.partner_fee, r = parseFloat(c);
    return r + o > w && (e == null ? void 0 : e.token_id) === "" ? parseFloat(r - o) : parseFloat(r);
  };
  return { estimateStopLoss: async ({ signal: o }) => {
    var p, n, u, m;
    if (f)
      return !0;
    d(!0), i(!0);
    const r = {
      user_address: S,
      token_in: e == null ? void 0 : e.token_id,
      amount: h(),
      price: parseFloat(g),
      chunks: L || 1,
      max_price_change: 0.5
    };
    try {
      const { data: t } = await A.post("/stoploss/estimate", r, {
        signal: o
      });
      t.splits = [], t.expected_output = t.expected_output_without_slippage || 0, _(t), s(""), y(F(t.total_output));
    } catch (t) {
      if (console.log(t), t.name === "AbortError")
        return !0;
      ((p = t.response) == null ? void 0 : p.data) === "pool_out_of_sync" && s("Pools Out Of Sync"), ((n = t.response) == null ? void 0 : n.data) === "not_enough_liquidity" && s("Not Enough Liquidity"), ((u = t.response) == null ? void 0 : u.data) === "pool_not_found" && s("Pool Not Found"), ((m = t.response) == null ? void 0 : m.data) === "input_too_small" && s("Input Too Small");
    } finally {
      i(!1);
    }
    return !0;
  } };
};
export {
  K as useEstimateStopLoss
};
