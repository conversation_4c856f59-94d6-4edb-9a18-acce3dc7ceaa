import { jsxs as b, jsx as d } from "react/jsx-runtime";
import { memo as i } from "react";
import { cn as o } from "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const f = ({
  left: a = "OFF",
  right: c = "ON",
  initValue: s = !1,
  onChange: r,
  className: l,
  color: n,
  height: t = "40px",
  borderRadius: x = "9px",
  disabled: e = !1
}) => {
  const h = (m) => {
    r(m);
  };
  return /* @__PURE__ */ b(
    "div",
    {
      className: o(
        "dhs-flex dhs-w-21 dhs-rounded-xl dhs-relative dhs-bg-background dhs-bg-opacity-50 dhs-font-proximaSemiBold dhs-text-xs",
        l
      ),
      style: { height: t },
      children: [
        /* @__PURE__ */ d(
          "div",
          {
            className: o(
              "dhs-w-[calc(50%)] dhs-h-10 dhs-bg-accent dhs-absolute dhs-duration-200",
              "dhs-bg-accent",
              s ? n : "dhs-bg-accent"
            ),
            style: {
              transform: `translateX(${s ? "100%" : 0})`,
              height: t,
              borderRadius: x
            }
          }
        ),
        /* @__PURE__ */ d(
          "button",
          {
            className: `dhs-flex-1 dhs-h-10 z-[1] dhs-relative dhs-delay-200 dhs-duration-75 ${s ? "dhs-text-subText" : "dhs-text-buttonText"}`,
            onClick: () => h(!1),
            disabled: e,
            style: { height: t },
            children: a
          }
        ),
        /* @__PURE__ */ d(
          "button",
          {
            className: `dhs-flex-1 dhs-h-10 z-[1] dhs-relative dhs-delay-200 dhs-duration-75 ${s ? "dhs-text-buttonText" : "dhs-text-subText"}`,
            onClick: () => h(!0),
            disabled: e,
            style: { height: t },
            children: c
          }
        )
      ]
    }
  );
}, T = i(f);
export {
  T as default
};
