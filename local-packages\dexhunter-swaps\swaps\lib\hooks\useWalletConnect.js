import { parseAddressesToBech32 as S, getBalance as W } from "../utils/cardanoUtils.js";
import A from "../store/useStore.js";
import "../index-ca8eb9e1.js";
import "../config/axios.js";
import "../axios-ddd885c5.js";
import "react";
import "../_commonjsHelpers-10dfc225.js";
import "../store/createTokenSearchSlice.js";
import "../immer-548168ec.js";
import "../store/createWalletSlice.js";
import "../store/createSwapSettingsSlice.js";
import "../store/createGlobalSettingsSlice.js";
import "../store/createUserOrdersSlice.js";
import "../store/createSwapSlice.js";
import "../store/createChartSlice.js";
import "../store/createBasketSlice.js";
import "../swap/components/tokens.js";
import "../store/createModalWhatsNewSlice.js";
import "../store/createSwapParamsSlice.js";
const q = (r) => {
  const {
    setIsLoadingWallet: a,
    updateSelectedWallet: n,
    resetWallet: d,
    setOnWalletConnect: m,
    onWalletConnect: p
  } = A((e) => e.walletSlice);
  r !== void 0 && m(r);
  const s = r !== void 0 ? r : p, c = async (e) => {
    try {
      a(!0);
      const t = await window.cardano[e].enable();
      let o;
      try {
        o = await t.getUsedAddresses(), e === "eternl" && (o = [await t.getChangeAddress(), ...o].filter((i, g, u) => u.indexOf(i) === g));
      } catch (i) {
        console.log("ERROR GETTING ADDRESSES", i), o = await t.getUsedAddresses();
      }
      const l = S(o), { balance: f } = await W(l);
      n({ api: t, balance: f, selectedWallet: e, userAddress: l[0], addressList: l }), localStorage.setItem("dexhunter-selected-wallet", e), s !== void 0 && s(e);
    } catch (t) {
      console.error(t), d();
    } finally {
      a(!1);
    }
  };
  return { connectWallet: c, reconnectToWallet: async (e = "") => {
    console.log("RECONNECTING TO WALLET", e);
    const t = e || localStorage.getItem("dexhunter-selected-wallet");
    if (t)
      return c(t);
    a(!1), s !== void 0 && s(t);
  } };
};
export {
  q as useWalletConnect
};
