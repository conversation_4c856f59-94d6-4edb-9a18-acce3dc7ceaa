import w from "../../store/useStore.js";
import { server as l } from "../../config/axios.js";
import { useNotify as y } from "../../hooks/useNotify.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
import "react/jsx-runtime";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../hooks/useScreen.js";
const X = () => {
  const { notify: i } = y(), {
    api: t,
    userAddress: c
  } = w((s) => s.walletSlice), { setSelectedOrdersIds: p, setIsOrderCancelLoading: a, setCancellingOrders: g, setPendingOrdersCount: m, pendingOrdersCount: u, cancellingOrders: b } = w((s) => s.userOrdersSlice);
  return { cancelOrder: async (s) => {
    let r = {
      sign: null,
      tx: "",
      address: c,
      order_id: s,
      err: null,
      step: "pre-cancel",
      err_message: ""
    };
    a(!0);
    try {
      const { data: e } = await l.post("/swap/cancel", {
        order_id: s,
        address: c
      });
      r.step = "pre-sign";
      const d = await (t == null ? void 0 : t.signTx(e.cbor, !0));
      r.step = "pre-sign-post";
      const { data: o } = await l.post("/swap/sign", {
        txCbor: e.cbor,
        signatures: d
      });
      r.sign = o, r.step = "pre-submit";
      const n = await (t == null ? void 0 : t.submitTx(o.cbor));
      return r.tx = n, i({
        type: "success",
        title: "Order cancelled",
        desc: "Order cancelled successfully",
        actionName: "View order",
        actionCallback: () => {
          console.log("callback");
        }
      }), g([s, ...b]), m(u + 1), n;
    } catch (e) {
      if (console.log(e), r.err = e, r.err_message = e.message, e.info === "User declined to sign the transaction.")
        return;
      i({
        type: "error",
        title: "Error cancelling order",
        desc: "There was an error cancelling your orders",
        actionCallback: () => {
          navigator.clipboard.writeText(JSON.stringify(r));
        }
      });
    } finally {
      a(!1), p([]);
    }
  }, bundleCancelOrder: async (s) => {
    let r = {
      sign: null,
      tx: "",
      address: c,
      order_ids: s,
      err: null,
      step: "pre-cancel"
    };
    a(!0);
    try {
      const { data: e } = await l.post("/swap/bulkcancel", {
        order_ids: s,
        address: c
      });
      r.step = "pre-sign";
      const d = await (t == null ? void 0 : t.signTx(e.cbor, !0));
      r.step = "pre-sign-post";
      const { data: o } = await l.post("/swap/sign", {
        txCbor: e.cbor,
        signatures: d
      });
      r.sign = o, r.step = "pre-submit";
      const n = await (t == null ? void 0 : t.submitTx(o.cbor));
      return r.tx = n, i({
        type: "success",
        title: "Order cancelled",
        desc: "Order cancelled successfully",
        actionName: "View order",
        actionCallback: () => {
          console.log("callback");
        }
      }), g([...b, ...s]), m(u + s.length), n;
    } catch (e) {
      if (console.log(e), r.err = e.message, e.info === "User declined to sign the transaction.")
        return;
      i({
        type: "error",
        title: "Error cancelling orders",
        desc: "There was an error cancelling your orders",
        actionCallback: () => {
          navigator.clipboard.writeText(JSON.stringify(r));
        }
      });
    } finally {
      a(!1), p([]);
    }
  } };
};
export {
  X as useCancelAction
};
