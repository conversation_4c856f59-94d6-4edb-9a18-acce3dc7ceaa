import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { useSocket } from '@/context/SocketContext';
import { usePing } from '@/hooks/usePing';

export default function SocketTest() {
  const { socket, isConnected, error } = useSocket();
  const { sendPing, lastPongTime, isPinging } = usePing();
  const [messages, setMessages] = useState<string[]>([]);

  const handlePing = () => {
    if (sendPing()) {
      setMessages(prev => [...prev, `Sent ping at ${new Date().toLocaleTimeString()}`]);
    } else {
      setMessages(prev => [...prev, 'Failed to send ping - socket not connected']);
    }
  };

  useEffect(() => {
    if (lastPongTime) {
      setMessages(prev => [...prev, `Received pong at ${lastPongTime.toLocaleTimeString()}`]);
    }
  }, [lastPongTime]);

  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-xl font-bold mb-4">Socket Test</h2>

      <div className="mb-4">
        <p>Connection Status: {isConnected ? 'Connected' : 'Disconnected'}</p>
        {error && <p className="text-red-500">Error: {error}</p>}
      </div>

      <div className="flex gap-2 mb-4">
        <Button onClick={handlePing} disabled={!isConnected || isPinging}>
          {isPinging ? 'Pinging...' : 'Send Ping'}
        </Button>

        <Button
          onClick={() => {
            if (socket) {
              setMessages(prev => [
                ...prev,
                `Socket status: ${isConnected ? 'Connected' : 'Disconnected'}`,
                `Socket ID: ${socket.id || 'None'}`
              ]);
            } else {
              setMessages(prev => [...prev, 'Socket is not initialized']);
            }
          }}
          variant="outline"
        >
          Check Connection
        </Button>

        <Button
          onClick={() => setMessages([])}
          variant="outline"
        >
          Clear
        </Button>
      </div>

      <div className="h-64 overflow-y-auto border p-2 rounded">
        {messages.map((msg, i) => (
          <div key={i} className="mb-1">
            {msg}
          </div>
        ))}
      </div>
    </div>
  );
};