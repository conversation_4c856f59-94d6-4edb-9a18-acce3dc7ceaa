var it = (e, t, i) => {
  if (!t.has(e))
    throw TypeError("Cannot " + i);
};
var s = (e, t, i) => (it(e, t, "read from private field"), i ? i.call(e) : t.get(e)), h = (e, t, i) => {
  if (t.has(e))
    throw TypeError("Cannot add the same private member more than once");
  t instanceof WeakSet ? t.add(e) : t.set(e, i);
}, o = (e, t, i, r) => (it(e, t, "write to private field"), r ? r.call(e, i) : t.set(e, i), i);
var l = (e, t, i) => (it(e, t, "access private method"), i);
import { S as xt, k as Rt, l as O, q as rt, r as Z, b as Tt, t as at, u as mt, v as Ft, g as Ut, w as Dt, x as yt, n as Qt } from "./query-013b86c3.js";
import * as Q from "react";
import { u as Pt } from "./QueryClientProvider-6bcd4331.js";
import "react/jsx-runtime";
var g, a, z, b, F, L, w, C, N, k, j, U, D, x, B, P, H, V, nt, W, ht, K, ot, $, ct, G, ut, J, lt, X, ft, q, It, Et, Mt = (Et = class extends xt {
  constructor(t, i) {
    super();
    h(this, P);
    h(this, V);
    h(this, W);
    h(this, K);
    h(this, $);
    h(this, G);
    h(this, J);
    h(this, X);
    h(this, q);
    h(this, g, void 0);
    h(this, a, void 0);
    h(this, z, void 0);
    h(this, b, void 0);
    h(this, F, void 0);
    h(this, L, void 0);
    h(this, w, void 0);
    h(this, C, void 0);
    h(this, N, void 0);
    h(this, k, void 0);
    // This property keeps track of the last query with defined data.
    // It will be used to pass the previous data and query to the placeholder function between renders.
    h(this, j, void 0);
    h(this, U, void 0);
    h(this, D, void 0);
    h(this, x, void 0);
    h(this, B, /* @__PURE__ */ new Set());
    this.options = i, o(this, g, t), o(this, C, null), o(this, w, Rt()), this.options.experimental_prefetchInRender || s(this, w).reject(
      new Error("experimental_prefetchInRender feature flag is not enabled")
    ), this.bindMethods(), this.setOptions(i);
  }
  bindMethods() {
    this.refetch = this.refetch.bind(this);
  }
  onSubscribe() {
    this.listeners.size === 1 && (s(this, a).addObserver(this), Ct(s(this, a), this.options) ? l(this, P, H).call(this) : this.updateResult(), l(this, $, ct).call(this));
  }
  onUnsubscribe() {
    this.hasListeners() || this.destroy();
  }
  shouldFetchOnReconnect() {
    return dt(
      s(this, a),
      this.options,
      this.options.refetchOnReconnect
    );
  }
  shouldFetchOnWindowFocus() {
    return dt(
      s(this, a),
      this.options,
      this.options.refetchOnWindowFocus
    );
  }
  destroy() {
    this.listeners = /* @__PURE__ */ new Set(), l(this, G, ut).call(this), l(this, J, lt).call(this), s(this, a).removeObserver(this);
  }
  setOptions(t, i) {
    const r = this.options, f = s(this, a);
    if (this.options = s(this, g).defaultQueryOptions(t), this.options.enabled !== void 0 && typeof this.options.enabled != "boolean" && typeof this.options.enabled != "function" && typeof O(this.options.enabled, s(this, a)) != "boolean")
      throw new Error(
        "Expected enabled to be a boolean or a callback that returns a boolean"
      );
    l(this, X, ft).call(this), s(this, a).setOptions(this.options), r._defaulted && !rt(this.options, r) && s(this, g).getQueryCache().notify({
      type: "observerOptionsUpdated",
      query: s(this, a),
      observer: this
    });
    const c = this.hasListeners();
    c && Ot(
      s(this, a),
      f,
      this.options,
      r
    ) && l(this, P, H).call(this), this.updateResult(i), c && (s(this, a) !== f || O(this.options.enabled, s(this, a)) !== O(r.enabled, s(this, a)) || Z(this.options.staleTime, s(this, a)) !== Z(r.staleTime, s(this, a))) && l(this, V, nt).call(this);
    const n = l(this, W, ht).call(this);
    c && (s(this, a) !== f || O(this.options.enabled, s(this, a)) !== O(r.enabled, s(this, a)) || n !== s(this, x)) && l(this, K, ot).call(this, n);
  }
  getOptimisticResult(t) {
    const i = s(this, g).getQueryCache().build(s(this, g), t), r = this.createResult(i, t);
    return Lt(this, r) && (o(this, b, r), o(this, L, this.options), o(this, F, s(this, a).state)), r;
  }
  getCurrentResult() {
    return s(this, b);
  }
  trackResult(t, i) {
    const r = {};
    return Object.keys(t).forEach((f) => {
      Object.defineProperty(r, f, {
        configurable: !1,
        enumerable: !0,
        get: () => (this.trackProp(f), i == null || i(f), t[f])
      });
    }), r;
  }
  trackProp(t) {
    s(this, B).add(t);
  }
  getCurrentQuery() {
    return s(this, a);
  }
  refetch({ ...t } = {}) {
    return this.fetch({
      ...t
    });
  }
  fetchOptimistic(t) {
    const i = s(this, g).defaultQueryOptions(t), r = s(this, g).getQueryCache().build(s(this, g), i);
    return r.fetch().then(() => this.createResult(r, i));
  }
  fetch(t) {
    return l(this, P, H).call(this, {
      ...t,
      cancelRefetch: t.cancelRefetch ?? !0
    }).then(() => (this.updateResult(), s(this, b)));
  }
  createResult(t, i) {
    var gt;
    const r = s(this, a), f = this.options, c = s(this, b), n = s(this, F), S = s(this, L), R = t !== r ? t.state : s(this, z), { state: m } = t;
    let u = { ...m }, T = !1, v;
    if (i._optimisticResults) {
      const p = this.hasListeners(), M = !p && Ct(t, i), _ = p && Ot(t, r, i, f);
      (M || _) && (u = {
        ...u,
        ...Dt(m.data, t.options)
      }), i._optimisticResults === "isRestoring" && (u.fetchStatus = "idle");
    }
    let { error: A, errorUpdatedAt: I, status: E } = u;
    if (i.select && u.data !== void 0)
      if (c && u.data === (n == null ? void 0 : n.data) && i.select === s(this, N))
        v = s(this, k);
      else
        try {
          o(this, N, i.select), v = i.select(u.data), v = yt(c == null ? void 0 : c.data, v, i), o(this, k, v), o(this, C, null);
        } catch (p) {
          o(this, C, p);
        }
    else
      v = u.data;
    if (i.placeholderData !== void 0 && v === void 0 && E === "pending") {
      let p;
      if (c != null && c.isPlaceholderData && i.placeholderData === (S == null ? void 0 : S.placeholderData))
        p = c.data;
      else if (p = typeof i.placeholderData == "function" ? i.placeholderData(
        (gt = s(this, j)) == null ? void 0 : gt.state.data,
        s(this, j)
      ) : i.placeholderData, i.select && p !== void 0)
        try {
          p = i.select(p), o(this, C, null);
        } catch (M) {
          o(this, C, M);
        }
      p !== void 0 && (E = "success", v = yt(
        c == null ? void 0 : c.data,
        p,
        i
      ), T = !0);
    }
    s(this, C) && (A = s(this, C), v = s(this, k), I = Date.now(), E = "error");
    const tt = u.fetchStatus === "fetching", et = E === "pending", st = E === "error", bt = et && tt, vt = v !== void 0, y = {
      status: E,
      fetchStatus: u.fetchStatus,
      isPending: et,
      isSuccess: E === "success",
      isError: st,
      isInitialLoading: bt,
      isLoading: bt,
      data: v,
      dataUpdatedAt: u.dataUpdatedAt,
      error: A,
      errorUpdatedAt: I,
      failureCount: u.fetchFailureCount,
      failureReason: u.fetchFailureReason,
      errorUpdateCount: u.errorUpdateCount,
      isFetched: u.dataUpdateCount > 0 || u.errorUpdateCount > 0,
      isFetchedAfterMount: u.dataUpdateCount > R.dataUpdateCount || u.errorUpdateCount > R.errorUpdateCount,
      isFetching: tt,
      isRefetching: tt && !et,
      isLoadingError: st && !vt,
      isPaused: u.fetchStatus === "paused",
      isPlaceholderData: T,
      isRefetchError: st && vt,
      isStale: pt(t, i),
      refetch: this.refetch,
      promise: s(this, w)
    };
    if (this.options.experimental_prefetchInRender) {
      const p = (Y) => {
        y.status === "error" ? Y.reject(y.error) : y.data !== void 0 && Y.resolve(y.data);
      }, M = () => {
        const Y = o(this, w, y.promise = Rt());
        p(Y);
      }, _ = s(this, w);
      switch (_.status) {
        case "pending":
          t.queryHash === r.queryHash && p(_);
          break;
        case "fulfilled":
          (y.status === "error" || y.data !== _.value) && M();
          break;
        case "rejected":
          (y.status !== "error" || y.error !== _.reason) && M();
          break;
      }
    }
    return y;
  }
  updateResult(t) {
    const i = s(this, b), r = this.createResult(s(this, a), this.options);
    if (o(this, F, s(this, a).state), o(this, L, this.options), s(this, F).data !== void 0 && o(this, j, s(this, a)), rt(r, i))
      return;
    o(this, b, r);
    const f = {}, c = () => {
      if (!i)
        return !0;
      const { notifyOnChangeProps: n } = this.options, S = typeof n == "function" ? n() : n;
      if (S === "all" || !S && !s(this, B).size)
        return !0;
      const d = new Set(
        S ?? s(this, B)
      );
      return this.options.throwOnError && d.add("error"), Object.keys(s(this, b)).some((R) => {
        const m = R;
        return s(this, b)[m] !== i[m] && d.has(m);
      });
    };
    (t == null ? void 0 : t.listeners) !== !1 && c() && (f.listeners = !0), l(this, q, It).call(this, { ...f, ...t });
  }
  onQueryUpdate() {
    this.updateResult(), this.hasListeners() && l(this, $, ct).call(this);
  }
}, g = new WeakMap(), a = new WeakMap(), z = new WeakMap(), b = new WeakMap(), F = new WeakMap(), L = new WeakMap(), w = new WeakMap(), C = new WeakMap(), N = new WeakMap(), k = new WeakMap(), j = new WeakMap(), U = new WeakMap(), D = new WeakMap(), x = new WeakMap(), B = new WeakMap(), P = new WeakSet(), H = function(t) {
  l(this, X, ft).call(this);
  let i = s(this, a).fetch(
    this.options,
    t
  );
  return t != null && t.throwOnError || (i = i.catch(Tt)), i;
}, V = new WeakSet(), nt = function() {
  l(this, G, ut).call(this);
  const t = Z(
    this.options.staleTime,
    s(this, a)
  );
  if (at || s(this, b).isStale || !mt(t))
    return;
  const r = Ft(s(this, b).dataUpdatedAt, t) + 1;
  o(this, U, setTimeout(() => {
    s(this, b).isStale || this.updateResult();
  }, r));
}, W = new WeakSet(), ht = function() {
  return (typeof this.options.refetchInterval == "function" ? this.options.refetchInterval(s(this, a)) : this.options.refetchInterval) ?? !1;
}, K = new WeakSet(), ot = function(t) {
  l(this, J, lt).call(this), o(this, x, t), !(at || O(this.options.enabled, s(this, a)) === !1 || !mt(s(this, x)) || s(this, x) === 0) && o(this, D, setInterval(() => {
    (this.options.refetchIntervalInBackground || Ut.isFocused()) && l(this, P, H).call(this);
  }, s(this, x)));
}, $ = new WeakSet(), ct = function() {
  l(this, V, nt).call(this), l(this, K, ot).call(this, l(this, W, ht).call(this));
}, G = new WeakSet(), ut = function() {
  s(this, U) && (clearTimeout(s(this, U)), o(this, U, void 0));
}, J = new WeakSet(), lt = function() {
  s(this, D) && (clearInterval(s(this, D)), o(this, D, void 0));
}, X = new WeakSet(), ft = function() {
  const t = s(this, g).getQueryCache().build(s(this, g), this.options);
  if (t === s(this, a))
    return;
  const i = s(this, a);
  o(this, a, t), o(this, z, t.state), this.hasListeners() && (i == null || i.removeObserver(this), t.addObserver(this));
}, q = new WeakSet(), It = function(t) {
  Qt.batch(() => {
    t.listeners && this.listeners.forEach((i) => {
      i(s(this, b));
    }), s(this, g).getQueryCache().notify({
      query: s(this, a),
      type: "observerResultsUpdated"
    });
  });
}, Et);
function _t(e, t) {
  return O(t.enabled, e) !== !1 && e.state.data === void 0 && !(e.state.status === "error" && t.retryOnMount === !1);
}
function Ct(e, t) {
  return _t(e, t) || e.state.data !== void 0 && dt(e, t, t.refetchOnMount);
}
function dt(e, t, i) {
  if (O(t.enabled, e) !== !1) {
    const r = typeof i == "function" ? i(e) : i;
    return r === "always" || r !== !1 && pt(e, t);
  }
  return !1;
}
function Ot(e, t, i, r) {
  return (e !== t || O(r.enabled, e) === !1) && (!i.suspense || e.state.status !== "error") && pt(e, i);
}
function pt(e, t) {
  return O(t.enabled, e) !== !1 && e.isStaleByTime(Z(t.staleTime, e));
}
function Lt(e, t) {
  return !rt(e.getCurrentResult(), t);
}
var wt = Q.createContext(!1), kt = () => Q.useContext(wt);
wt.Provider;
function jt() {
  let e = !1;
  return {
    clearReset: () => {
      e = !1;
    },
    reset: () => {
      e = !0;
    },
    isReset: () => e
  };
}
var Bt = Q.createContext(jt()), At = () => Q.useContext(Bt);
function Ht(e, t) {
  return typeof e == "function" ? e(...t) : !!e;
}
function zt() {
}
var Nt = (e, t) => {
  (e.suspense || e.throwOnError) && (t.isReset() || (e.retryOnMount = !1));
}, Vt = (e) => {
  Q.useEffect(() => {
    e.clearReset();
  }, [e]);
}, Wt = ({
  result: e,
  errorResetBoundary: t,
  throwOnError: i,
  query: r
}) => e.isError && !t.isReset() && !e.isFetching && r && Ht(i, [e.error, r]), Kt = (e) => {
  e.suspense && (e.staleTime === void 0 && (e.staleTime = 1e3), typeof e.gcTime == "number" && (e.gcTime = Math.max(e.gcTime, 1e3)));
}, $t = (e, t) => e.isLoading && e.isFetching && !t, Gt = (e, t) => (e == null ? void 0 : e.suspense) && t.isPending, St = (e, t, i) => t.fetchOptimistic(e).catch(() => {
  i.clearReset();
});
function Jt(e, t, i) {
  var m, u, T, v, A;
  const r = Pt(i), f = kt(), c = At(), n = r.defaultQueryOptions(e);
  (u = (m = r.getDefaultOptions().queries) == null ? void 0 : m._experimental_beforeQuery) == null || u.call(
    m,
    n
  ), n._optimisticResults = f ? "isRestoring" : "optimistic", Kt(n), Nt(n, c), Vt(c);
  const S = !r.getQueryCache().get(n.queryHash), [d] = Q.useState(
    () => new t(
      r,
      n
    )
  ), R = d.getOptimisticResult(n);
  if (Q.useSyncExternalStore(
    Q.useCallback(
      (I) => {
        const E = f ? () => {
        } : d.subscribe(Qt.batchCalls(I));
        return d.updateResult(), E;
      },
      [d, f]
    ),
    () => d.getCurrentResult(),
    () => d.getCurrentResult()
  ), Q.useEffect(() => {
    d.setOptions(n, { listeners: !1 });
  }, [n, d]), Gt(n, R))
    throw St(n, d, c);
  if (Wt({
    result: R,
    errorResetBoundary: c,
    throwOnError: n.throwOnError,
    query: r.getQueryCache().get(n.queryHash)
  }))
    throw R.error;
  if ((v = (T = r.getDefaultOptions().queries) == null ? void 0 : T._experimental_afterQuery) == null || v.call(
    T,
    n,
    R
  ), n.experimental_prefetchInRender && !at && $t(R, f)) {
    const I = S ? (
      // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted
      St(n, d, c)
    ) : (
      // subscribe to the "cache promise" so that we can finalize the currentThenable once data comes in
      (A = r.getQueryCache().get(n.queryHash)) == null ? void 0 : A.promise
    );
    I == null || I.catch(zt).finally(() => {
      d.updateResult();
    });
  }
  return n.notifyOnChangeProps ? R : d.trackResult(R);
}
function ee(e, t) {
  return Jt(e, Mt, t);
}
export {
  ee as u
};
