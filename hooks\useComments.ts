// filepath: social-feed/hooks/useComments.ts
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchComments, createComment, deleteComment } from '@/redux/slices/commentsSlice';

const useComments = (postId) => {
  const dispatch = useDispatch();
  const { comments, loading, error } = useSelector((state) => state.comments);

  useEffect(() => {
    if (postId) {
      dispatch(fetchComments(postId));
    }
  }, [dispatch, postId]);

  const handleCreateComment = (commentData) => {
    return dispatch(createComment({ postId, ...commentData })).unwrap();
  };

  const handleDeleteComment = (commentId) => {
    return dispatch(deleteComment(commentId)).unwrap();
  };

  return {
    comments,
    loading,
    error,
    handleCreateComment,
    handleDeleteComment,
  };
};

export default useComments;