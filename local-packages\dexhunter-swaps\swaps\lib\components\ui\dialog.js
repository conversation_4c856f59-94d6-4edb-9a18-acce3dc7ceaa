import { jsx as s, jsxs as x } from "react/jsx-runtime";
import * as o from "react";
import { $ as l, a as i, b as r, c, d as m, e as g, f as b } from "../../index-840f2930.js";
import { cn as t } from "../../lib.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const O = g, P = b, n = ({ container: e, ...a }) => /* @__PURE__ */ s(
  l,
  {
    container: e ?? document.getElementById("dexhunter-root"),
    ...a
  }
);
n.displayName = l.displayName;
const h = o.forwardRef(({ className: e, ...a }, d) => /* @__PURE__ */ s(
  i,
  {
    ref: d,
    className: t(
      "dhs-fixed dhs-inset-0 dhs-z-50 dhs-backdrop-blur-sm data-[state=open]:dhs-animate-in data-[state=closed]:dhs-animate-out data-[state=closed]:dhs-fade-out-0 data-[state=open]:dhs-fade-in-0",
      e
    ),
    ...a
  }
));
h.displayName = i.displayName;
const $ = o.forwardRef(({ className: e, children: a, portalContainer: d, ...p }, f) => /* @__PURE__ */ x(n, { container: d, children: [
  /* @__PURE__ */ s(h, { className: "dhs-dialog-overlay" }),
  /* @__PURE__ */ s(
    r,
    {
      ref: f,
      className: t(
        "dhs-diaglog-content dhs-absolute dhs-left-[50%] dhs-top-5 sm:dhs-left-none dhs-z-50 grid sm:dhs-max-w-[345px] dhs-w-full dhs-max-w-lg dhs-translate-x-[-50%] dhs-bg-background dhs-p-6 dhs-shadow-lg dhs-duration-200 data-[state=open]:dhs-animate-in data-[state=closed]:dhs-animate-out data-[state=closed]:dhs-fade-out-0 data-[state=open]:dhs-fade-in-0 data-[state=closed]:dhs-zoom-out-95 data-[state=open]:dhs-zoom-in-95 data-[state=closed]:dhs-slide-out-to-left-1/2 data-[state=closed]:dhs-slide-out-to-top-[48%] data-[state=open]:dhs-slide-in-from-left-1/2 data-[state=open]:dhs-slide-in-from-top-[48%] sm:dhs-rounded-2xl md:dhs-w-full",
        e
      ),
      ...p,
      children: a
    }
  )
] }));
$.displayName = r.displayName;
const u = ({
  className: e,
  ...a
}) => /* @__PURE__ */ s(
  "div",
  {
    className: t(
      "dhs-flex dhs-flex-col dhs-space-y-1.5 dhs-text-center sm:dhs-text-left",
      e
    ),
    ...a
  }
);
u.displayName = "DialogHeader";
const N = ({
  className: e,
  ...a
}) => /* @__PURE__ */ s(
  "div",
  {
    className: t(
      "dhs-flex dhs-flex-col-reverse sm:dhs-flex-row sm:dhs-justify-end sm:dhs-space-x-2",
      e
    ),
    ...a
  }
);
N.displayName = "DialogFooter";
const y = o.forwardRef(({ className: e, ...a }, d) => /* @__PURE__ */ s(
  c,
  {
    ref: d,
    className: t(
      "dhs-text-lg dhs-font-semibold dhs-leading-none dhs-tracking-tight",
      e
    ),
    ...a
  }
));
y.displayName = c.displayName;
const D = o.forwardRef(({ className: e, ...a }, d) => /* @__PURE__ */ s(
  m,
  {
    ref: d,
    className: t("dhs-text-sm dhs-text-muted-foreground", e),
    ...a
  }
));
D.displayName = m.displayName;
export {
  O as Dialog,
  $ as DialogContent,
  D as DialogDescription,
  N as DialogFooter,
  u as DialogHeader,
  n as DialogPortal,
  y as DialogTitle,
  P as DialogTrigger
};
