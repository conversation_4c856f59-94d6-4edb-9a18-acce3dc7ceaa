const { Server } = require('socket.io');

// Track online users and their socket IDs
const onlineUsers = new Map(); // userId -> socketId
const userSockets = new Map(); // socketId -> userId

const initSocket = (server) => {
  const io = new Server(server, {
    path: '/socket.io',
    cors: {
      origin: process.env.NODE_ENV === 'production'
        ? false
        : ['http://localhost:3000', 'http://localhost:3001'],
      methods: ['GET', 'POST'],
      credentials: true,
    },
    serveClient: false,
    connectTimeout: 30000,
  });

  io.on('connection', (socket) => {
    console.log('Socket Server: Client connected:', socket.id);

    // User authentication and tracking
    socket.on('userConnected', (userId) => {
      if (userId) {
        console.log(`Socket Server: User ${userId} connected with socket ${socket.id}`);

        const existingSocketId = onlineUsers.get(userId);
        if (existingSocketId) {
          userSockets.delete(existingSocketId);
          const existingSocket = io.sockets.sockets.get(existingSocketId);
          if (existingSocket) {
            existingSocket.disconnect(true);
          }
        }

        onlineUsers.set(userId, socket.id);
        userSockets.set(socket.id, userId);
        io.emit('userStatusChanged', { userId, status: 'online' });
      }
    });

    socket.on('disconnect', (reason) => {
      console.log('Socket Server: Client disconnected:', socket.id, 'reason:', reason);
      const userId = userSockets.get(socket.id);
      if (userId) {
        onlineUsers.delete(userId);
        userSockets.delete(socket.id);
        io.emit('userStatusChanged', { userId, status: 'offline' });
      }
    });

    // Messaging events
    socket.on('sendMessage', ({ message, receiverId, senderId }) => {
      console.log(`Socket Server: Message from ${senderId} to ${receiverId}:`, message.id);
      const receiverSocketId = onlineUsers.get(receiverId);
      if (receiverSocketId) {
        io.to(receiverSocketId).emit('newMessage', message);
      }
      socket.emit('messageSent', { messageId: message.id, status: 'sent' });
    });

    socket.on('userTyping', ({ senderId, receiverId, isTyping }) => {
      const receiverSocketId = onlineUsers.get(receiverId);
      if (receiverSocketId) {
        io.to(receiverSocketId).emit('userTyping', { senderId, isTyping });
      }
    });

    socket.on('messageRead', ({ messageId, readerId, senderId }) => {
      const senderSocketId = onlineUsers.get(senderId);
      if (senderSocketId) {
        io.to(senderSocketId).emit('messageRead', { messageId, readerId });
      }
    });

    socket.on('sendNotification', ({ notification, receiverId }) => {
      const receiverSocketId = onlineUsers.get(receiverId);
      if (receiverSocketId) {
        io.to(receiverSocketId).emit('newNotification', notification);
      }
    });

    // Typing events for rooms
    socket.on('typing', (data) => {
      console.log(`Socket Server: ${data.roomId} - user with id ${data.user.id} is typing`);
      socket.to(data.roomId).emit('typing', data.user);
    });

    socket.on('stopped-typing', (data) => {
      console.log(`Socket Server: ${data.roomId} - user with id ${data.user.id} stopped typing`);
      socket.to(data.roomId).emit('stopped-typing', data.user);
    });

    // Post-related events
    socket.on('joinRoom', (postId) => {
      socket.join(`post:${postId}`);
      console.log(`Socket ${socket.id} joined post room: post:${postId}`);
    });

    socket.on('leaveRoom', (postId) => {
      socket.leave(`post:${postId}`);
      console.log(`Socket ${socket.id} left post room: post:${postId}`);
    });

    // Room-based events
    socket.on('join-room', (roomId) => {
      socket.join(roomId);
      console.log(`Socket Server: Client ${socket.id} joined room ${roomId}`);
    });

    socket.on('leave-room', (roomId) => {
      socket.leave(roomId);
      console.log(`Socket Server: Client ${socket.id} left room ${roomId}`);
    });

    // Basic ping/pong
    socket.on('ping', () => {
      console.log('Socket Server: Ping received from:', socket.id);
      socket.emit('pong');
    });

    socket.on('client-ping', () => {
      console.log('Socket Server: Ping received from:', socket.id);
      socket.emit('client-pong');
    });

    socket.on('error', (error) => {
      console.error('Socket Server: Error for client:', socket.id, error);
    });
  });

  return io;
};

module.exports = {
  initSocket
};