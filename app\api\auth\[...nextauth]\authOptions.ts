/* eslint-disable @typescript-eslint/no-explicit-any */
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import FacebookProvider from "next-auth/providers/facebook";
import TwitterProvider from "next-auth/providers/twitter";
import DiscordProvider from "next-auth/providers/discord";
import LinkedInProvider from "next-auth/providers/linkedin";
import RedditProvider from "next-auth/providers/reddit";
import TwitchProvider from "next-auth/providers/twitch";

import { sign } from "jsonwebtoken";
import { ConvexHttpClient } from "convex/browser";
import { api } from "@/convex/_generated/api";

// Initialize Convex client for server-side operations
export const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

interface Credentials {
  address?: string;
  walletAddress?: string;
  userId?: string;
  email?: string;
  password?: string;
  userType?: string;
}

interface User {
  id: string;
  walletAddress?: string;
  userId?: string;
  token: string;
  displayName?: string;
  profilePhoto?: string;
  is_verified?: boolean;
  accountType?: string;
  email?: string;
  convexToken?: string;
}

const authOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        address: { label: "Address", type: "text", placeholder: "0x0", optional: true },
        userId: { label: "User ID", type: "text", placeholder: "User ID", optional: true },
        email: { label: "Email", type: "email", placeholder: "<EMAIL>", optional: true },
        password: { label: "Password", type: "password", optional: true },
        userType: { label: "User Type", type: "text", placeholder: "user", optional: true },
      },
      async authorize(credentials?: Credentials): Promise<User | null> {
        // Wallet-based login
        if (credentials?.address && credentials?.userId) {
          const userAddress = credentials.address;
          const userId = credentials.userId;

          if (userAddress && userId) {
            const token = sign({ sub: userAddress }, process.env.NEXTAUTH_SECRET as string, { expiresIn: "10d" });
            // Generate a convexToken for Convex authentication (example)
            const convexToken = sign({ sub: userId }, process.env.CONVEX_JWT_SECRET || process.env.NEXTAUTH_SECRET!, { expiresIn: "10d" });
            return { id: userAddress, walletAddress: userAddress, token, userId, convexToken };
          }
          return null;
        }

        // Email/Password login using Convex
        if (credentials?.email && credentials?.password) {
          const email = credentials.email;
          const password = credentials.password;
          const userType = credentials.userType || "user";

          try {
            // Call Convex creatorLogin function
            const userLogin = await convex.mutation(api.auth.creatorLogin, {
              email,
              password,
              userType,
              ipAddress: "unknown",
              userAgent: "unknown",
            });

            if (userLogin.success && userLogin.user) {
              const userId = userLogin.user.userId;
              const displayName = userLogin.user.userInfo.account.displayName;
              const profilePhoto = userLogin.user.userInfo.profilePhoto;
              const accountType = userLogin.user.userInfo.account.accountType;
              const is_verified = userLogin.user.userInfo.account.is_verified;

              if (userId) {
                const token = sign({ sub: email }, process.env.NEXTAUTH_SECRET as string, { expiresIn: "10d" });
                // Generate a convexToken for Convex authentication
                const convexToken = sign({ sub: userId }, process.env.CONVEX_JWT_SECRET || process.env.NEXTAUTH_SECRET!, { expiresIn: "10d" });
                return {
                  id: userLogin.id,
                  userId,
                  email,
                  displayName,
                  profilePhoto,
                  accountType,
                  is_verified,
                  token,
                  convexToken,
                };
              }
            }

            console.error("Authentication failed:", userLogin.message || "Invalid credentials");
            return null;
          } catch (error) {
            console.error("Error during email/password login:", error);
            return null;
          }
        }

        return null;
      },
    }),

    // Other OAuth providers (Google, Facebook, etc.) remain unchanged
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),

    // Facebook OAuth
    FacebookProvider({
      clientId: process.env.FACEBOOK_APP_ID!,
      clientSecret: process.env.FACEBOOK_APP_SECRET!,
    }),

    // Twitter OAuth
    TwitterProvider({
      clientId: process.env.X_API_KEY!,
      clientSecret: process.env.X_API_KEY_SECRET!,
      version: "2.0",
    }),

    // Discord OAuth
    DiscordProvider({
      clientId: process.env.DISCORD_CLIENT_ID!,
      clientSecret: process.env.DISCORD_CLIENT_SECRET!,
    }),

    // LinkedIn OAuth
    LinkedInProvider({
      clientId: process.env.LINKEDIN_CLIENT_ID!,
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET!,
    }),

    // Reddit OAuth
    RedditProvider({
      clientId: process.env.REDDIT_CLIENT_ID!,
      clientSecret: process.env.REDDIT_CLIENT_SECRET!,
    }),

    // Twitch OAuth
    TwitchProvider({
      clientId: process.env.TWITCH_CLIENT_ID!,
      clientSecret: process.env.TWITCH_CLIENT_SECRET!,
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 60 * 60 * 24 * 10,
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async jwt({ token, user }: { token: any; user?: User }): Promise<any> {
      if (user) {
        token.id = user.id;
        token.userId = user.userId;
        token.walletAddress = user.walletAddress;
        token.email = user.email;
        token.convexToken = user.convexToken;
        token.accountType = user.accountType;
        token.displayName = user.displayName;
        token.profilePhoto = user.profilePhoto;
        token.is_verified = user.is_verified;
      }
      return token;
    },
    async session({ session, token }: { session: any; token: any }): Promise<any> {
      if (token) {
        session.user = {
          id: token.id,
          userId: token.userId,
          walletAddress: token.walletAddress,
          email: token.email,
          convexToken: token.convexToken,
          accountType: token.accountType,
          displayName: token.displayName,
          profilePhoto: token.profilePhoto,
          is_verified: token.is_verified,
        };
      }
      return session;
    },
  },
} as any;
export default authOptions;