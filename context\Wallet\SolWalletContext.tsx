import React, { createContext, useContext, useState, useEffect, useMemo, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { clearNotifications } from '@/redux/slices/notificationSlice';
import { clearUserInfo } from '@/redux/slices/userInfoSlice';
import { setUserInfo } from '@/redux/slices/userInfoSlice';
import bs58 from 'bs58';
import { truncateText, searchForSubstring, serializeConnector } from '@/public/main';
import Swal from 'sweetalert2';
import { updateUserStatus } from '@/lib/fetchServerFunctions';
import { signIn, signOut } from 'next-auth/react';
import { setWallet, clearWalletItem, clearWallet } from '@/redux/slices/walletSlice';
import { WalletProvider, useWallet } from "@solana/wallet-adapter-react";
import {
    PhantomWalletAdapter,
    SolflareWalletAdapter,
    TrustWalletAdapter,
    CoinbaseWalletAdapter
} from "@solana/wallet-adapter-wallets";
import { WalletModalProvider } from "@solana/wallet-adapter-react-ui";
import "@solana/wallet-adapter-react-ui/styles.css";
import { WalletAdapterNetwork } from "@solana/wallet-adapter-base";
import { getSession, useSession } from 'next-auth/react';
import {
    createSolanaRpc,
    createSolanaRpcSubscriptions,
    address,
    createTransactionMessage,
    setTransactionMessageFeePayerSigner,
    appendTransactionMessageInstructions,
    compileTransaction,
    signTransactionMessageWithSigners,
    sendAndConfirmTransactionFactory,
    getSignatureFromTransaction,
    pipe,
    lamports,
    type Lamports
} from '@solana/kit';
import { getTransferSolInstruction } from '@solana-program/system';
import { fromLegacyPublicKey } from '@solana/compat';

const SolWalletContext = createContext({});

export const useSolWalletContext = () => useContext(SolWalletContext);

export const SolWalletProvider = ({ children }: { children: React.ReactNode }) => {
    const network = WalletAdapterNetwork.Mainnet;
    const endpoint = useMemo(() => "https://api.mainnet-beta.solana.com", []);
    const { autoConnect, signMessage: signSolMessage } = useWallet();

    // Create RPC and RPC Subscriptions objects
    const rpc = useMemo(() => createSolanaRpc(endpoint), [endpoint]);
    const rpcSubscriptions = useMemo(() => createSolanaRpcSubscriptions(endpoint.replace('https', 'wss')), [endpoint]);

    //wallets
    const wallets = useMemo(
        () => [
            new PhantomWalletAdapter(),
            new SolflareWalletAdapter(),
            new TrustWalletAdapter(),
            new CoinbaseWalletAdapter(),
        ],
        [network]
    );

    return (
        <WalletProvider wallets={wallets} autoConnect>
            <WalletModalProvider>
                <SolWalletContextProvider rpc={rpc} rpcSubscriptions={rpcSubscriptions}>
                    {children}
                </SolWalletContextProvider>
            </WalletModalProvider>
        </WalletProvider>
    );
};

interface SolWalletContextProviderProps {
    children: React.ReactNode;
    rpc: ReturnType<typeof createSolanaRpc>;
    rpcSubscriptions: ReturnType<typeof createSolanaRpcSubscriptions>;
}

interface WalletSignatureResponse {
    success: boolean;
    message: string;
    signature?: string;
    publicKey?: string;
}

const SolWalletContextProvider = ({ children, rpc, rpcSubscriptions }: SolWalletContextProviderProps) => {
    const { userId, walletType, blockchain, solWalletAddress, solWalletProvider, isSolConnecting } = useSelector((state: any) => state.wallet);
    const { data: session, status } = useSession();
    const [isUserInitiatedConnection, setIsUserInitiatedConnection] = useState(false);
    const Toast = () => toast('');
    const router = useRouter();
    const activeComponent = usePathname().substring(1) || 'home';
    const isAccountActive = activeComponent.includes('account');
    const dispatch = useDispatch();
    const walletLoginSignMessage = 'Sign and verify to log in with this wallet: ';
    const walletSignupSignMessage = 'Sign and verify to sign up with this wallet: ';
    const fullState = useSelector((state: any) => state.wallet);
    const wallet = useWallet();
    const { publicKey, signMessage } = useWallet();

    const walletConnectionRef = useRef<{ connected: boolean; publicKey: string | null }>({
        connected: wallet.connected,
        publicKey: wallet.publicKey?.toBase58() ?? null,
    }) as any;

    const solanaLogin = async (walletName: any, accountType: string): Promise<{ success: boolean; message: string }> => {
        if (isSolConnecting) throw new Error("Already connecting");

        dispatch(setWallet({
            isSolConnecting: true,
            isConnecting: true
        }));

        try {
            wallet.select(walletName);
            const { success, publicKey } = await waitForWalletConnection();

            if (!success || !publicKey) {
                throw new Error("Wallet connection timed out or failed.");
            }

            const { success: loginSuccess, publicKey: loginPubKey, signature, message } = await connectAndSignSolanaWallet({ walletSignMessage: walletLoginSignMessage.substring(0, walletLoginSignMessage.length - 2) });

            if (!loginSuccess || !signature || !loginPubKey) {
                throw new Error(message || "Wallet signature timed out or failed.");
            }

            const loginResult = await solanaLoginMain(publicKey, walletName, accountType);

            if (!loginResult.success) {
                throw new Error(loginResult?.message || "Login failed");
            }

            return loginResult;
        } catch (error: any) {
            console.error("Error connecting Solana wallet:", error);
            toast.error(error.message || "Failed to connect wallet. Please try again.");
            return { success: false, message: error?.message ?? "Connection failed" };
        } finally {
            dispatch(setWallet({ isSolConnecting: false, isConnecting: false }));
        }
    };

    const solanaSignup = async (walletName: any, accountType: string): Promise<{ success: boolean; message: string }> => {
        if (isSolConnecting) throw new Error("Already connecting");

        dispatch(setWallet({
            isSolConnecting: true,
            isConnecting: true
        }));

        try {
            wallet.select(walletName);
            const { success, publicKey } = await waitForWalletConnection();

            if (!success || !publicKey) {
                throw new Error("Wallet connection timed out or failed.");
            }

            const { success: signupSuccess, publicKey: signupPubKey, signature, message } = await connectAndSignSolanaWallet({ walletSignMessage: walletSignupSignMessage.substring(0, walletSignupSignMessage.length - 2) });

            if (!signupSuccess || !signature || !signupPubKey) {
                throw new Error(message || "Wallet signature timed out or failed.");
            }

            const signupResult = await solanaSignupMain(publicKey, walletName, accountType);

            if (!signupResult?.success) {
                throw new Error(signupResult?.message || "Signup failed");
            }

            return signupResult;
        } catch (error: any) {
            console.error("Error connecting Solana wallet:", error);
            toast.error(error.message || "Failed to connect wallet. Please try again.");
            return { success: false, message: error?.message ?? "Connection failed" };
        } finally {
            console.trace({ fullState });
            // Use the full state here as well
            dispatch(setWallet({
                isSolConnecting: false,
                isConnecting: false
            }));
        }
    };

    const solanaLoginMain = async (address: string, walletName: string, accountType: string) => {
        const headers = new Headers();
        headers.append('Content-Type', 'application/json');

        const url = accountType == 'user' ? '/api/user-login' : '/api/creator-login';

        try {
            const res = await fetch(url, {
                method: 'POST',
                headers,
                body: JSON.stringify({
                    address: address,
                    blockchain: 'Solana'
                }),
            });

            const userLogin = await res.json();

            if (userLogin.success) {
                const currentSession = await signIn('credentials', {
                    redirect: false,
                    address: address,
                    walletAddress: address,
                    userId: userLogin.user[0]?.user_id,
                }) as any;

                if (currentSession.ok) {
                    dispatch(setWallet({
                        isSolConnected: true,
                        userId: userLogin.user[0]?.user_id,
                        walletType: walletName,
                        solWalletAddress: address,
                        blockchain: 'Solana',
                        solWalletProvider: JSON.stringify(wallet, serializeConnector, 2),
                        isSolConnecting: false,
                        isConnecting: false,
                        accountType: userLogin.user[0]?.user_info.accountType
                    }));

                    dispatch(setUserInfo({
                        username: userLogin.user[0].user_info.username,
                        displayName: userLogin.user[0].user_info.displayName,
                        profilePhoto: userLogin.user[0].user_info.profilePhoto,
                    }));
                }
            } else {
                if (userLogin.message.includes('User not found')) {
                    throw new Error('No account found for this wallet. Sign up to get started.');
                } else if (userLogin.message.includes('Invalid credentials') || userLogin.message.includes('Authentication failed')) {
                    throw new Error(`${userLogin.message} Please try again.`);
                } else if (userLogin.message.includes('User already exists')) {
                    throw new Error('This wallet is linked to another account. Please log in or use a different wallet.');
                } else {
                    Swal.fire({
                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                        icon: "error",
                        confirmButtonText: "Close"
                    });
                    throw new Error("Something went wrong. Please try again later.");
                }
            }

            return { success: true, message: "User logged in successfully" };
        } catch (error: any) {
            console.error("Error during login process:", error);
            return { success: false, message: error.message || "Something went wrong. Please try again later." };
        }
    };

    const solanaSignupMain = async (address: string, walletName: string, accountType: string) => {
        const headers = new Headers();
        headers.append('Content-Type', 'application/json');

        const url = accountType == 'user' ? '/api/user-signup' : '/api/creator-signup';

        try {
            const res = await fetch(url, {
                method: 'POST',
                headers,
                body: JSON.stringify({
                    address: address,
                    blockchain: 'Solana'
                }),
            });

            if (!res.ok) {
                throw new Error(res.statusText);
            }

            const userSignup = await res.json();

            if (userSignup.success) {
                const currentSession = await signIn('credentials', {
                    redirect: false,
                    address: address,
                    walletAddress: address,
                    userId: userSignup.user[0]?.user_id,
                }) as any;

                if (currentSession.ok) {
                    console.trace({ fullState });
                    dispatch(setWallet({
                        isSolConnected: true,
                        userId: userSignup.user[0]?.user_id,
                        walletType: walletName,
                        solWalletAddress: address,
                        blockchain: 'Solana',
                        solWalletProvider: JSON.stringify(wallet, serializeConnector, 2),
                        isSolConnecting: false,
                        isConnecting: false,
                        accountType: userSignup.user[0]?.user_info.accountType
                    }));

                    dispatch(setUserInfo({
                        username: userSignup.user[0].user_info.username,
                        displayName: userSignup.user[0].user_info.displayName,
                        profilePhoto: userSignup.user[0].user_info.profilePhoto,
                    }));

                    return { success: true, message: "User account created successfully" };
                }
            } else {
                if (userSignup.message.includes('User not found')) {
                    throw new Error('No account found for this wallet. Sign up to get started.');
                } else if (userSignup.message.includes('Invalid credentials') || userSignup.message.includes('Authentication failed')) {
                    throw new Error(`${userSignup.message} Please try again.`);
                } else if (userSignup.message.includes('User already exists')) {
                    throw new Error('This wallet is linked to another account. Please log in or use a different wallet.');
                } else {
                    Swal.fire({
                        html: "Error logging in! Please refresh and try again. If the error persists, open a Tech Support ticket in the Sugar Club Discord server for assistance.",
                        icon: "error",
                        confirmButtonText: "Close"
                    });
                    throw new Error("Something went wrong. Please try again later.");
                }
            }

        } catch (error: any) {
            console.error("Error during signup process:", error);
            return { success: false, message: error.message || "Something went wrong. Please try again later." };
        }
    };

    const connectAndSignSolanaWallet = async ({ walletSignMessage }: { walletSignMessage: string; }): Promise<WalletSignatureResponse> => {
        try {
            if (!walletConnectionRef.current) throw new Error("No wallet selected");
            if (!walletConnectionRef.current.connected || !walletConnectionRef.current.publicKey) throw new Error("Wallet not connected");
            if (typeof signMessage !== 'function') throw new Error("Wallet not ready to sign, please try again in a few seconds.");

            const message = new TextEncoder().encode(walletSignMessage);
            const signature = await signMessage(message);

            if (!signature) throw new Error("Failed to sign message");

            const signatureBase58 = bs58.encode(signature instanceof Uint8Array ? signature : new Uint8Array(signature));
            const publicKeyBase58 = walletConnectionRef.current.publicKey;

            if (!signatureBase58 || !publicKeyBase58) {
                throw new Error("Signature verification failed");
            }

            return {
                success: true,
                message: "Wallet connected and signed successfully",
                signature: signatureBase58,
                publicKey: publicKeyBase58
            };
        } catch (error: any) {
            console.error("Error:", error);
            return { success: false, message: error?.message || "Unknown error" };
        }
    };

    const waitForWalletConnection = (): Promise<{ success: boolean; publicKey: string | null }> => {
        return new Promise((resolve) => {
            const interval = setInterval(() => {
                if (walletConnectionRef.current.connected && walletConnectionRef.current.publicKey) {
                    clearInterval(interval);
                    resolve({ success: true, publicKey: walletConnectionRef.current.publicKey });
                }
            }, 100);

            setTimeout(() => {
                clearInterval(interval);
                resolve({ success: false, publicKey: null }); // Timeout after 3 minutes
            }, 3 * 60 * 1000);
        });
    };

    const logOut = async (loginType: string = 'wallet') => {
        try {
            if (loginType == 'wallet') {
                // Use the wallet adapter's disconnect method directly
                if (wallet && wallet.disconnect) {
                    await wallet.disconnect();
                }

                signOut({ redirect: false });
                dispatch(clearWallet());
                dispatch(clearNotifications());
                dispatch(clearUserInfo());

                if (isAccountActive) {
                    router.push('/');
                }
            } else if (loginType == 'email') {
                signOut({ redirect: false });
                dispatch(clearNotifications());
                dispatch(clearUserInfo());
            }
            if (userId) await updateUserStatus('offline', userId, dispatch);
        } catch (error: any) {
            console.error('Error during disconnect:', error.message);
        }
    };

    const retrieveSolBalance = async () => {
        if (publicKey) {
            const { value: balance } = await rpc.getBalance(address(publicKey.toBase58())).send();
            return Number(balance) / Number(lamports(BigInt(1e9)));
        }
        return 0;
    };

    const buildSolanaTransaction = async (fromAddress: string, toAddress: string, amount: number, walletName: any) => {
        setIsUserInitiatedConnection(false);

        // Select wallet and wait for connection
        wallet.select(walletName);

        const checkConnection = () => {
            return new Promise((resolve) => {
                const interval = setInterval(() => {
                    if (wallet.connected) {
                        clearInterval(interval);
                        resolve(true);
                    }
                }, 100);

                setTimeout(() => {
                    clearInterval(interval);
                    resolve(false);
                }, 180000); // 3 minute timeout
            });
        };

        const isConnected = await checkConnection();
        if (!isConnected) throw new Error("Wallet not connected");

        // Convert amount from SOL to lamports
        const lamportsAmount = lamports(BigInt(Math.floor(amount * 1e9)));

        // Verify if the wallet's public key matches the fromAddress
        if (!wallet.publicKey || wallet.publicKey.toString() !== fromAddress) {
            throw new Error(`Wallet public key does not match fromAddress!`);
        }

        // Create transfer instruction using the system program
        const transferInstruction = {
            programId: address('********************************'),
            keys: [
                { pubkey: address(fromAddress), isSigner: true, isWritable: true },
                { pubkey: address(toAddress), isSigner: false, isWritable: true }
            ],
            data: Buffer.from([2, ...new Uint8Array(Number(lamportsAmount))])
        };

        // Create and configure transaction message
        const { value: latestBlockhash } = await rpc.getLatestBlockhash().send();        const transactionMessage = pipe(
            createTransactionMessage({
                version: 0,
                blockhash: latestBlockhash // Set blockhash during creation
            }),
            (tx: any) => setTransactionMessageFeePayerSigner(wallet.publicKey, tx),
            (tx: any) => appendTransactionMessageInstructions([transferInstruction], tx)
        );

        // Create send and confirm function
        const sendAndConfirm = sendAndConfirmTransactionFactory({
            rpc: rpc as any,
            rpcSubscriptions: rpcSubscriptions as any
        });

        return { transactionMessage, sendAndConfirm };
    };

    /* Session */
    useEffect(() => {
        const loadSession = async () => {
            if (!session && status == 'unauthenticated') {
                logOut();
                return;
            }
        }

        setTimeout(() => {
            loadSession();
        }, 2000);
    }, [session]);

    useEffect(() => {
        walletConnectionRef.current = {
            connected: wallet.connected,
            publicKey: wallet.publicKey?.toBase58() ?? null,
        };
    }, [wallet.connected, wallet.publicKey]);

    const value = {
        rpc,
        rpcSubscriptions,
        wallet,
        walletType,
        select: wallet.select,
        connect: wallet.connect,
        disconnect: wallet.disconnect,
        signIn: wallet.signIn,
        signMessage: wallet.signMessage,
        wallets: wallet.wallets,
        solanaLogin,
        solanaSignup,
        logOut,
        retrieveSolBalance,
        buildSolanaTransaction,
        solWalletConnecting: wallet.connecting
    };

    return (
        <SolWalletContext.Provider value={value}>
            {children}
        </SolWalletContext.Provider>
    );
};