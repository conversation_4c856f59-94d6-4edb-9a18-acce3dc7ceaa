var U = Symbol.for("immer-nothing"), x = Symbol.for("immer-draftable"), s = Symbol.for("immer-state");
function a(e, ...t) {
  throw new Error(
    `[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`
  );
}
var h = Object.getPrototypeOf;
function p(e) {
  return !!e && !!e[s];
}
function y(e) {
  var t;
  return e ? B(e) || Array.isArray(e) || !!e[x] || !!((t = e.constructor) != null && t[x]) || F(e) || O(e) : !1;
}
var Q = Object.prototype.constructor.toString();
function B(e) {
  if (!e || typeof e != "object")
    return !1;
  const t = h(e);
  if (t === null)
    return !0;
  const r = Object.hasOwnProperty.call(t, "constructor") && t.constructor;
  return r === Object ? !0 : typeof r == "function" && Function.toString.call(r) === Q;
}
function m(e, t) {
  g(e) === 0 ? Object.entries(e).forEach(([r, n]) => {
    t(r, n, e);
  }) : e.forEach((r, n) => t(n, r, e));
}
function g(e) {
  const t = e[s];
  return t ? t.type_ : Array.isArray(e) ? 1 : F(e) ? 2 : O(e) ? 3 : 0;
}
function C(e, t) {
  return g(e) === 2 ? e.has(t) : Object.prototype.hasOwnProperty.call(e, t);
}
function G(e, t, r) {
  const n = g(e);
  n === 2 ? e.set(t, r) : n === 3 ? e.add(r) : e[t] = r;
}
function Y(e, t) {
  return e === t ? e !== 0 || 1 / e === 1 / t : e !== e && t !== t;
}
function F(e) {
  return e instanceof Map;
}
function O(e) {
  return e instanceof Set;
}
function l(e) {
  return e.copy_ || e.base_;
}
function k(e, t) {
  if (F(e))
    return new Map(e);
  if (O(e))
    return new Set(e);
  if (Array.isArray(e))
    return Array.prototype.slice.call(e);
  if (!t && B(e))
    return h(e) ? { ...e } : Object.assign(/* @__PURE__ */ Object.create(null), e);
  const r = Object.getOwnPropertyDescriptors(e);
  delete r[s];
  let n = Reflect.ownKeys(r);
  for (let i = 0; i < n.length; i++) {
    const f = n[i], o = r[f];
    o.writable === !1 && (o.writable = !0, o.configurable = !0), (o.get || o.set) && (r[f] = {
      configurable: !0,
      writable: !0,
      // could live with !!desc.set as well here...
      enumerable: o.enumerable,
      value: e[f]
    });
  }
  return Object.create(h(e), r);
}
function N(e, t = !1) {
  return A(e) || p(e) || !y(e) || (g(e) > 1 && (e.set = e.add = e.clear = e.delete = Z), Object.freeze(e), t && m(e, (r, n) => N(n, !0))), e;
}
function Z() {
  a(2);
}
function A(e) {
  return Object.isFrozen(e);
}
var L = {};
function d(e) {
  const t = L[e];
  return t || a(0, e), t;
}
var P;
function H() {
  return P;
}
function V(e, t) {
  return {
    drafts_: [],
    parent_: e,
    immer_: t,
    // Whenever the modified draft contains a draft from another scope, we
    // need to prevent auto-freezing so the unowned draft can be finalized.
    canAutoFreeze_: !0,
    unfinalizedDrafts_: 0
  };
}
function I(e, t) {
  t && (d("Patches"), e.patches_ = [], e.inversePatches_ = [], e.patchListener_ = t);
}
function v(e) {
  M(e), e.drafts_.forEach(ee), e.drafts_ = null;
}
function M(e) {
  e === P && (P = e.parent_);
}
function j(e) {
  return P = V(P, e);
}
function ee(e) {
  const t = e[s];
  t.type_ === 0 || t.type_ === 1 ? t.revoke_() : t.revoked_ = !0;
}
function W(e, t) {
  t.unfinalizedDrafts_ = t.drafts_.length;
  const r = t.drafts_[0];
  return e !== void 0 && e !== r ? (r[s].modified_ && (v(t), a(4)), y(e) && (e = z(t, e), t.parent_ || S(t, e)), t.patches_ && d("Patches").generateReplacementPatches_(
    r[s].base_,
    e,
    t.patches_,
    t.inversePatches_
  )) : e = z(t, r, []), v(t), t.patches_ && t.patchListener_(t.patches_, t.inversePatches_), e !== U ? e : void 0;
}
function z(e, t, r) {
  if (A(t))
    return t;
  const n = t[s];
  if (!n)
    return m(
      t,
      (i, f) => K(e, n, t, i, f, r)
    ), t;
  if (n.scope_ !== e)
    return t;
  if (!n.modified_)
    return S(e, n.base_, !0), n.base_;
  if (!n.finalized_) {
    n.finalized_ = !0, n.scope_.unfinalizedDrafts_--;
    const i = n.copy_;
    let f = i, o = !1;
    n.type_ === 3 && (f = new Set(i), i.clear(), o = !0), m(
      f,
      (c, _) => K(e, n, i, c, _, r, o)
    ), S(e, i, !1), r && e.patches_ && d("Patches").generatePatches_(
      n,
      r,
      e.patches_,
      e.inversePatches_
    );
  }
  return n.copy_;
}
function K(e, t, r, n, i, f, o) {
  if (p(i)) {
    const c = f && t && t.type_ !== 3 && // Set objects are atomic since they have no keys.
    !C(t.assigned_, n) ? f.concat(n) : void 0, _ = z(e, i, c);
    if (G(r, n, _), p(_))
      e.canAutoFreeze_ = !1;
    else
      return;
  } else
    o && r.add(i);
  if (y(i) && !A(i)) {
    if (!e.immer_.autoFreeze_ && e.unfinalizedDrafts_ < 1)
      return;
    z(e, i), (!t || !t.scope_.parent_) && S(e, i);
  }
}
function S(e, t, r = !1) {
  !e.parent_ && e.immer_.autoFreeze_ && e.canAutoFreeze_ && N(t, r);
}
function te(e, t) {
  const r = Array.isArray(e), n = {
    type_: r ? 1 : 0,
    // Track which produce call this is associated with.
    scope_: t ? t.scope_ : H(),
    // True for both shallow and deep changes.
    modified_: !1,
    // Used during finalization.
    finalized_: !1,
    // Track which properties have been assigned (true) or deleted (false).
    assigned_: {},
    // The parent draft state.
    parent_: t,
    // The base state.
    base_: e,
    // The base proxy.
    draft_: null,
    // set below
    // The base copy with any updated values.
    copy_: null,
    // Called by the `produce` function.
    revoke_: null,
    isManual_: !1
  };
  let i = n, f = T;
  r && (i = [n], f = w);
  const { revoke: o, proxy: c } = Proxy.revocable(i, f);
  return n.draft_ = c, n.revoke_ = o, c;
}
var T = {
  get(e, t) {
    if (t === s)
      return e;
    const r = l(e);
    if (!C(r, t))
      return re(e, r, t);
    const n = r[t];
    return e.finalized_ || !y(n) ? n : n === D(e.base_, t) ? (b(e), e.copy_[t] = E(n, e)) : n;
  },
  has(e, t) {
    return t in l(e);
  },
  ownKeys(e) {
    return Reflect.ownKeys(l(e));
  },
  set(e, t, r) {
    const n = X(l(e), t);
    if (n != null && n.set)
      return n.set.call(e.draft_, r), !0;
    if (!e.modified_) {
      const i = D(l(e), t), f = i == null ? void 0 : i[s];
      if (f && f.base_ === r)
        return e.copy_[t] = r, e.assigned_[t] = !1, !0;
      if (Y(r, i) && (r !== void 0 || C(e.base_, t)))
        return !0;
      b(e), R(e);
    }
    return e.copy_[t] === r && // special case: handle new props with value 'undefined'
    (r !== void 0 || t in e.copy_) || // special case: NaN
    Number.isNaN(r) && Number.isNaN(e.copy_[t]) || (e.copy_[t] = r, e.assigned_[t] = !0), !0;
  },
  deleteProperty(e, t) {
    return D(e.base_, t) !== void 0 || t in e.base_ ? (e.assigned_[t] = !1, b(e), R(e)) : delete e.assigned_[t], e.copy_ && delete e.copy_[t], !0;
  },
  // Note: We never coerce `desc.value` into an Immer draft, because we can't make
  // the same guarantee in ES5 mode.
  getOwnPropertyDescriptor(e, t) {
    const r = l(e), n = Reflect.getOwnPropertyDescriptor(r, t);
    return n && {
      writable: !0,
      configurable: e.type_ !== 1 || t !== "length",
      enumerable: n.enumerable,
      value: r[t]
    };
  },
  defineProperty() {
    a(11);
  },
  getPrototypeOf(e) {
    return h(e.base_);
  },
  setPrototypeOf() {
    a(12);
  }
}, w = {};
m(T, (e, t) => {
  w[e] = function() {
    return arguments[0] = arguments[0][0], t.apply(this, arguments);
  };
});
w.deleteProperty = function(e, t) {
  return w.set.call(this, e, t, void 0);
};
w.set = function(e, t, r) {
  return T.set.call(this, e[0], t, r, e[0]);
};
function D(e, t) {
  const r = e[s];
  return (r ? l(r) : e)[t];
}
function re(e, t, r) {
  var i;
  const n = X(t, r);
  return n ? "value" in n ? n.value : (
    // This is a very special case, if the prop is a getter defined by the
    // prototype, we should invoke it with the draft as context!
    (i = n.get) == null ? void 0 : i.call(e.draft_)
  ) : void 0;
}
function X(e, t) {
  if (!(t in e))
    return;
  let r = h(e);
  for (; r; ) {
    const n = Object.getOwnPropertyDescriptor(r, t);
    if (n)
      return n;
    r = h(r);
  }
}
function R(e) {
  e.modified_ || (e.modified_ = !0, e.parent_ && R(e.parent_));
}
function b(e) {
  e.copy_ || (e.copy_ = k(
    e.base_,
    e.scope_.immer_.useStrictShallowCopy_
  ));
}
var ne = class {
  constructor(e) {
    this.autoFreeze_ = !0, this.useStrictShallowCopy_ = !1, this.produce = (t, r, n) => {
      if (typeof t == "function" && typeof r != "function") {
        const f = r;
        r = t;
        const o = this;
        return function(_ = f, ...q) {
          return o.produce(_, (J) => r.call(this, J, ...q));
        };
      }
      typeof r != "function" && a(6), n !== void 0 && typeof n != "function" && a(7);
      let i;
      if (y(t)) {
        const f = j(this), o = E(t, void 0);
        let c = !0;
        try {
          i = r(o), c = !1;
        } finally {
          c ? v(f) : M(f);
        }
        return I(f, n), W(i, f);
      } else if (!t || typeof t != "object") {
        if (i = r(t), i === void 0 && (i = t), i === U && (i = void 0), this.autoFreeze_ && N(i, !0), n) {
          const f = [], o = [];
          d("Patches").generateReplacementPatches_(t, i, f, o), n(f, o);
        }
        return i;
      } else
        a(1, t);
    }, this.produceWithPatches = (t, r) => {
      if (typeof t == "function")
        return (o, ...c) => this.produceWithPatches(o, (_) => t(_, ...c));
      let n, i;
      return [this.produce(t, r, (o, c) => {
        n = o, i = c;
      }), n, i];
    }, typeof (e == null ? void 0 : e.autoFreeze) == "boolean" && this.setAutoFreeze(e.autoFreeze), typeof (e == null ? void 0 : e.useStrictShallowCopy) == "boolean" && this.setUseStrictShallowCopy(e.useStrictShallowCopy);
  }
  createDraft(e) {
    y(e) || a(8), p(e) && (e = ie(e));
    const t = j(this), r = E(e, void 0);
    return r[s].isManual_ = !0, M(t), r;
  }
  finishDraft(e, t) {
    const r = e && e[s];
    (!r || !r.isManual_) && a(9);
    const { scope_: n } = r;
    return I(n, t), W(void 0, n);
  }
  /**
   * Pass true to automatically freeze all copies created by Immer.
   *
   * By default, auto-freezing is enabled.
   */
  setAutoFreeze(e) {
    this.autoFreeze_ = e;
  }
  /**
   * Pass true to enable strict shallow copy.
   *
   * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.
   */
  setUseStrictShallowCopy(e) {
    this.useStrictShallowCopy_ = e;
  }
  applyPatches(e, t) {
    let r;
    for (r = t.length - 1; r >= 0; r--) {
      const i = t[r];
      if (i.path.length === 0 && i.op === "replace") {
        e = i.value;
        break;
      }
    }
    r > -1 && (t = t.slice(r + 1));
    const n = d("Patches").applyPatches_;
    return p(e) ? n(e, t) : this.produce(
      e,
      (i) => n(i, t)
    );
  }
};
function E(e, t) {
  const r = F(e) ? d("MapSet").proxyMap_(e, t) : O(e) ? d("MapSet").proxySet_(e, t) : te(e, t);
  return (t ? t.scope_ : H()).drafts_.push(r), r;
}
function ie(e) {
  return p(e) || a(10, e), $(e);
}
function $(e) {
  if (!y(e) || A(e))
    return e;
  const t = e[s];
  let r;
  if (t) {
    if (!t.modified_)
      return t.base_;
    t.finalized_ = !0, r = k(e, t.scope_.immer_.useStrictShallowCopy_);
  } else
    r = k(e, !0);
  return m(r, (n, i) => {
    G(r, n, $(i));
  }), t && (t.finalized_ = !1), r;
}
var u = new ne(), fe = u.produce;
u.produceWithPatches.bind(
  u
);
u.setAutoFreeze.bind(u);
u.setUseStrictShallowCopy.bind(u);
u.applyPatches.bind(u);
u.createDraft.bind(u);
u.finishDraft.bind(u);
export {
  fe as p
};
