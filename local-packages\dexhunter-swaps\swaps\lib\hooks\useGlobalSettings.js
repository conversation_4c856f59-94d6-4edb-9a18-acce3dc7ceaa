import p from "../store/useStore.js";
import { useState as S, useEffect as u } from "react";
import "../_commonjsHelpers-10dfc225.js";
import "../store/createTokenSearchSlice.js";
import "../immer-548168ec.js";
import "../store/createWalletSlice.js";
import "../store/createSwapSettingsSlice.js";
import "../store/createGlobalSettingsSlice.js";
import "../store/createUserOrdersSlice.js";
import "../store/createSwapSlice.js";
import "../store/createChartSlice.js";
import "../store/createBasketSlice.js";
import "../swap/components/tokens.js";
import "../store/createModalWhatsNewSlice.js";
import "../store/createSwapParamsSlice.js";
const C = () => {
  const {
    setIsHideSmallBalances: n,
    setDefaultBuySize: o,
    setIsExactTime: r,
    setIsAdvancedMode: a,
    setIsPriceChangeInverted: l,
    setIsAnimationsDisabled: m,
    setIsMyOrdersOnTrends: d,
    setIsPricesFlipped: c
  } = p((t) => t.globalSettingsSlice), g = () => {
    const t = localStorage.getItem("globalSettings");
    return t ? JSON.parse(t) : {};
  }, [e, s] = S({});
  return u(() => {
    const t = g();
    t && (n(t.isHideSmallBalances), o(t.defaultBuySize), r(t.isExactTime), a(t.isAdvancedMode), l(t.isPriceChangeInverted), m(t.isAnimationsDisabled), d(t.isMyOrdersOnTrends), c(t.isPricesFlipped)), s(t);
  }, []), {
    settings: e,
    saveSettings: (t) => {
      const i = { ...e, ...t };
      localStorage.setItem("globalSettings", JSON.stringify(i)), s(i);
    }
  };
};
export {
  C as default
};
