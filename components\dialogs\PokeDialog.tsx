import React, { useState } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';
import Image from 'next/image';
import { HeartPulse, X } from 'lucide-react';

interface PokeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  recipientName: string;
  recipientId: string;
}

const PokeDialog: React.FC<PokeDialogProps> = ({
  isOpen,
  onClose,
  recipientName,
  recipientId,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSendPoke = async () => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Here you would implement the actual API call to send a poke
      // const response = await fetch('/api/messages/poke', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ recipientId }),
      // });

      toast.success(`Poke sent to ${recipientName}!`);
      onClose();
    } catch (error) {
      toast.error('Failed to send poke. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="md"
      closeButton={false}
      header={null}
      footer={null}
    >
      <div className="relative bg-white dark:bg-zinc-900 text-gray-400 dark:text-white rounded-lg overflow-hidden p-6">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 w-10 h-10 rounded-full bg-gray-300 dark:bg-zinc-700 flex items-center justify-center"
        >
          <X size={24} />
        </button>

        {/* Content */}
        <div className="text-center space-y-4">
          <h2 className="text-3xl font-bold uppercase tracking-wide">
            SEND A POKE!
          </h2>

          <p className="text-zinc-400 text-lg">
            Pokes are the best way to stand out from the
            crowd and get people chatting.
          </p>

          <p className="text-zinc-400 text-lg">
            When you send a poke, the other user receives an
            SMS notification.
          </p>

          <div className="mt-6 mb-2 flex items-center justify-center">
            <div className="bg-gray-500 dark:bg-zinc-800 rounded-full px-6 py-2 flex items-center">
              <span className="text-zinc-300 mr-2">Cost:</span>
              <div className="flex items-center">
                <HeartPulse size={18} className="text-white" />
                <span className="ml-1 text-white font-semibold">200 tokens</span>
              </div>
            </div>
          </div>
        </div>

        {/* Send button */}
        <div className="mt-8">
          <Button
            onClick={handleSendPoke}
            disabled={isSubmitting}
            className="w-full py-6 rounded-full bg-turquoise hover:bg-turquoise/90 text-white text-xl font-bold flex items-center justify-center gap-2"
          >
            <span>Send Poke</span>
            <span className="ml-auto">→</span>
          </Button>
        </div>
      </div>
    </AnimatedModal>
  );
};
export default PokeDialog;
