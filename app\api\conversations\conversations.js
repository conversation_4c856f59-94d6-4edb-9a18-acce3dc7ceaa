// pages/api/conversations.js
import { v4 as uuidv4 } from 'uuid';
import xss from 'xss';
import * as MainJS from '../../public/main';
import messaging from '../../utils/Notifications/firebaseAdmin';
import { createClient } from '@supabase/supabase-js';
import cloudinary from 'cloudinary';
import { IncomingForm } from 'formidable';
import { io } from 'socket.io-client';

const socket = io('https://beneficial-mirelle-websocket-server-8923033b.koyeb.app/');

cloudinary.config({
    cloud_name: 'di5nu3rmw',
    api_key: '693629495139743',
    api_secret: 'qTrFryQdt37tinNSkDH-0gSLlTo',
    secure: true,
});

// Initialize Supabase client
const mmSupabase = createClient(process.env.MM_SUPABASE_URL, process.env.MM_SUPABASE_API_KEY);

export const config = {
    api: {
        bodyParser: false,
    },
};

async function parseJsonBody(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(JSON.parse(body));
            } catch (error) {
                reject(error);
            }
        });
    });
};

async function getUserId(userAddress) {
    try {
        const { data: userIdQuery, error: userIdError } = await mmSupabase
            .from('UserWallets')
            .select('local_user_id')
            .eq('address', userAddress);

        if (userIdError) {
            throw new Error(`Error fetching user ID:, ${userIdError}`);
        }

        return userIdQuery ? userIdQuery[0]?.local_user_id : null;
    } catch (err) {
        console.error(err);
    }
};

async function getUserInfo(userId) {
    try {
        const { data: user, error: userError } = await mmSupabase
            .from('Users')
            .select('user_info')
            .eq('local_user_id', userId)
            .single();

        if (userError) {
            throw userError;
        }

        return user || null;
    } catch (err) {
        console.error(err);
        return null;
    }
};

const getUserAddress = async (user_id) => {
    try {
        const { data: user, error: userError } = await mmSupabase
            .from('UserWallets')
            .select('address')
            .eq('local_user_id', user_id)
            .eq('is_primary', true)
            .eq('blockchain', 'Cardano')
            .limit(1);

        if (userError) {
            throw userError;
        }

        return user[0] || null;
    } catch (err) {
        console.error(err);
        return null;
    }
};

const getGlobalUserId = async (userAddress) => {
    try {
        const { data: userIdQuery, error: userIdError } = await mmSupabase
            .from('UserWallets')
            .select('global_user_id')
            .eq('address', userAddress);

        if (userIdError) {
            throw new Error(`Error fetching user ID:, ${userIdError}`);
        }

        return userIdQuery ? userIdQuery[0]?.global_user_id : null;
    } catch (err) {
        console.error(err);
        return null;
    }
};

export default async function handler(req, res) {

    const { headers } = req;
    const authorizationHeader = headers['authorization'];
    const secretToken = process.env.NEXTAUTH_SECRET;

    if (authorizationHeader !== `Bearer ${secretToken}`) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
    }

    const todaysDate = new Date();
    const formattedDate = todaysDate.toISOString();

    if (req.method === 'GET') {
        const { address, page, sort, filter, search } = req.query;
        const queryPage = parseInt(page) || 1;
        const itemsPerPage = 50;
        const querySort = sort || 'newToOld';
        const queryFilter = filter || 'all-messages';
        const searchQuery = search || '';

        try {
            const userId = await getUserId(address);

            if (!userId) {
                return res.status(404).json({ message: 'User not found' });
            }

            const offset = (queryPage - 1) * itemsPerPage;
            const orderCriteria = { column: 'date', ascending: querySort === 'oldToNew' };

            // Fetch all conversations for the user
            const { data: userConversations, error: conversationError } = await mmSupabase
                .from('Messages')
                .select('conversation_id')
                .or(
                    `and(conversation_sender.eq.${userId},conversation_recipient.eq.${partnerId}),` +
                    `and(conversation_sender.eq.${partnerId},conversation_recipient.eq.${userId})`
                )
                .neq('is_deleted', true)
                .order(orderCriteria.column, { ascending: orderCriteria.ascending })
                .range(offset, offset + itemsPerPage - 1);

            if (conversationError) {
                console.error('Error fetching conversations:', conversationError.message);
                throw new Error(conversationError);
            }

            // Get unique conversation IDs
            const conversationIds = [...new Set(userConversations.map(convo => convo.conversation_id))];

            // Fetch messages for each conversation and determine the relevant message
            const conversationMessages = await Promise.all(
                conversationIds.map(async (conversationId) => {
                    const { data: messages, error: messagesError } = await mmSupabase
                        .from('Messages')
                        .select('*')
                        .eq('conversation_id', conversationId)
                        .neq('is_deleted', true)
                        .order('date', { ascending: false }); // Get messages in descending order

                    if (messagesError) {
                        console.error('Error fetching conversation messages:', messagesError.message);
                        throw new Error(messagesError);
                    }

                    // Find the most recent message where the user is the recipient or the sender
                    const mostRecentMessage = messages.find(message => message.recipient_id === userId) || messages[0];
                    return mostRecentMessage;
                })
            );

            // Filter out null values (conversations with no messages)
            const validMessages = conversationMessages.filter(message => message !== null);

            // Apply the queryFilter based on the user's role in the message
            const filteredByStatus = validMessages.filter(message => {
                if (queryFilter === 'all-messages') {
                    return true; // Include all messages
                }

                const isRecipient = message.recipient_id === userId;
                const isRead = message.recipient_status === 'read';
                const isUnread = message.recipient_status === 'unread';

                if (queryFilter === 'read') {
                    return isRecipient && isRead;
                }

                if (queryFilter === 'unread') {
                    return isRecipient && isUnread;
                }

                return true;
            });

            // Fetch user info for each message
            const messagesWithIcons = await Promise.all(
                filteredByStatus.map(async (message) => {
                    const senderInfo = await getUserInfo(message?.sender_id);
                    const recipientInfo = await getUserInfo(message?.recipient_id);

                    return {
                        ...message,
                        senderInfo: senderInfo?.user_info,
                        senderAddress: senderInfo?.address,
                        recipientInfo: recipientInfo?.user_info,
                        recipientAddress: recipientInfo?.address
                    };
                })
            );

            // Apply search filter
            const filteredMessages = messagesWithIcons.filter(message => {
                const sender = message.senderInfo || {};
                const recipient = message.recipientInfo || {};

                return (
                    (message.message && message.message.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (message.subject && message.subject.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (sender.discordUsername && sender.discordUsername.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (sender.username && sender.username.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (recipient.discordUsername && recipient.discordUsername.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (recipient.username && recipient.username.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (message.senderAddress && message.senderAddress.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (message.recipientAddress && message.recipientAddress.toLowerCase().includes(searchQuery.toLowerCase()))
                );
            });

            res.status(200).json({ messages: filteredMessages, totalConversations: filteredMessages.length });
        } catch (error) {
            console.error('Error fetching messages:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    } else if (req.method === 'POST') {

        const form = new IncomingForm();

        form.parse(req, async (err, fields, files) => {
            if (err) {
                res.status(500).json({ error: 'Failed to parse form data' });
                return;
            }

            try {

                const { senderAddress, recipientAddress, messageData, conversationId, subject, attachment_url, attachment_type, attachment_name, attachment_id } = fields;

                const sanitizedMessageData = xss(messageData);

                const globalSenderUserId = await getGlobalUserId(senderAddress);
                const senderUserId = await getUserId(senderAddress);
                const globalRecipientUserId = await getGlobalUserId(recipientAddress);
                const recipientUserId = await getUserId(recipientAddress);

                const { data: sender, error: senderError } = await mmSupabase
                    .from('Users')
                    .select('user_info')
                    .eq('global_user_id', globalSenderUserId)
                    .single();

                if (senderError) throw senderError;

                const { data: recipient, error: recipientError } = await mmSupabase
                    .from('Users')
                    .select('user_info')
                    .eq('global_user_id', globalRecipientUserId)
                    .single();

                if (recipientError) throw recipientError;

                const newConversationId = conversationId || uuidv4();

                const { error: insertError } = await mmSupabase
                    .from('Messages')
                    .insert([{
                        global_sender_id: globalSenderUserId,
                        global_recipient_id: globalRecipientUserId,
                        sender_id: senderUserId,
                        recipient_id: recipientUserId,
                        message: sanitizedMessageData,
                        date: formattedDate,
                        conversation_id: newConversationId,
                        subject,
                        attachment_url,
                        attachment_type,
                        attachment_name,
                        attachment_id,
                        sender_status: 'read',
                        recipient_status: 'unread',
                        conversation_sender: senderUserId,
                        conversation_recipient: recipientUserId
                    }]);

                if (insertError) throw insertError;

                const senderInfo = sender?.user_info || {};
                const recipientInfo = recipient?.user_info || {};

                const senderUsername = senderInfo?.username;
                const senderDiscordUsername = senderInfo?.discordUsername;
                const senderProfilePhoto = senderInfo?.profilePhoto;

                const { error: notificationError } = await mmSupabase
                    .from('Notifications')
                    .insert([{
                        global_user_id: globalRecipientUserId,
                        local_user_id: recipientUserId,
                        title: senderDiscordUsername || senderUsername || MainJS.truncateAddress(senderAddress),
                        message: 'Sent you a message.',
                        type: 'new-message',
                        status: 'unread',
                        created_at: formattedDate,
                        link: `https://chilledkongs.com/user/${recipientAddress}?tab=messages&conversationId=${newConversationId}`,
                        icon: senderProfilePhoto || '/images/user/default-avatar.png'
                    }]);

                if (notificationError) throw notificationError;

                // Check if watchlist user has notifications enabled
                const desktopPushNotifications = recipientInfo.desktopPushNotifications;
                const mobilePushNotifications = recipientInfo.mobilePushNotifications;

                const { mobileRegistrationToken, desktopRegistrationToken } = recipientInfo;

                if (desktopPushNotifications == 'true' && desktopRegistrationToken) {

                    try {
                        const message = {
                            notification: {
                                title: senderDiscordUsername || senderUsername || MainJS.truncateAddress(senderAddress),
                                body: 'Sent you a message.',
                                imageUrl: senderProfilePhoto,
                            },
                            data: {
                                linkUrl: `https://chilledkongs.com/user/${recipientAddress}?tab=messages&conversationId=${newConversationId}`,
                            },
                            token: desktopRegistrationToken,
                        };

                        const notification = await messaging.send(message);
                        console.log(notification);
                        console.log("Push notification sent successfully!");
                    } catch (error) {
                        console.error("Error sending push notification:", error);
                    }
                }

                if (mobilePushNotifications == 'true' && mobileRegistrationToken) {
                    try {
                        const message = {
                            notification: {
                                title: senderDiscordUsername || senderUsername || MainJS.truncateAddress(senderAddress),
                                body: 'Sent you a message.',
                                imageUrl: senderProfilePhoto,
                            },
                            data: {
                                linkUrl: `https://chilledkongs.com/user/${recipientAddress}?tab=messages&conversationId=${newConversationId}`,
                            },
                            token: desktopRegistrationToken,
                        };

                        const notification = await messaging.send(message);
                        //console.log(notification);
                        console.log("Push notification sent successfully!");
                    } catch (error) {
                        console.error("Error sending push notification:", error);
                    }
                }

                socket.emit('newMessage', { title: senderDiscordUsername || senderUsername || MainJS.truncateAddress(senderAddress), message: `Sent you a message.`, icon: senderProfilePhoto, link: `https://chilledkongs.com/user/${recipientAddress}?tab=messages&conversationId=${newConversationId}`, address: recipientAddress });

                res.status(201).json({ message: 'Message created successfully' });
            } catch (error) {
                console.error('Error creating message:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
        });
    } else if (req.method === 'DELETE') {
        try {
            const body = await parseJsonBody(req);
            const { conversationIds } = body;

            if (!Array.isArray(conversationIds) || conversationIds.length === 0) {
                return res.status(400).json({ message: 'No conversation IDs provided' });
            }

            // Fetch message details for each conversation
            const { data: messageData, error: messageError } = await mmSupabase
                .from('Messages')
                .select(`
                    conversation_id,
                    recipient_id,
                    sender_id,
                    recipient:Users!Messages_recipient_id_fkey(address),
                    sender:Users!Messages_sender_id_fkey(address)
                `)
                .in('conversation_id', conversationIds); // Use 'in' to match any of the conversationIds

            if (messageError) throw messageError;

            if (!messageData || messageData.length === 0) {
                return res.status(404).json({ message: 'Conversations not found' });
            }

            // Delete the conversations
            const { error: deleteError } = await mmSupabase
                .from('Messages')
                .delete()
                .in('conversation_id', conversationIds); // Use 'in' to delete any of the conversationIds

            if (deleteError) throw deleteError;

            const io = global.io;

            // Emit notifications for each deleted conversation
            messageData.forEach(message => {
                const recipientAddress = message.recipient.address;
                const senderAddress = message.sender.address;
                socket.emit('conversationDelete', { conversationId: message.conversation_id, recipientAddress, senderAddress });
            });

            res.status(200).json({ message: 'Conversations deleted successfully', success: true });
        } catch (error) {
            console.error('Error deleting messages:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    } else {
        res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
