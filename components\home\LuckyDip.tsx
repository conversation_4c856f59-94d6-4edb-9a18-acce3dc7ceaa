'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Clover } from 'lucide-react';
import { motion } from 'framer-motion';
import { useExpandable } from '@/hooks/useExpandable';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface UserAvatar {
  id: string;
  username: string;
  avatarUrl: string;
  status: 'online' | 'offline';
}

interface LuckyDipProps {
  users?: UserAvatar[];
}

const LuckyDip: React.FC<LuckyDipProps> = ({ users = [] }) => {
  const { isExpanded, toggleExpand, animatedHeight } = useExpandable(true);
  const contentRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Individual hover states for each column
  const [hoverStates, setHoverStates] = useState({
    column1: false,
    column2: false,
    column3: false,
    column4: false
  });

  // Generate random colors for placeholder images
  const getRandomColor = () => {
    const colors = ['4287f5', 'f54242', '42f54e', 'f5d442', 'f542f2', '42f5f5'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // Default users if none provided
  const defaultUsers: UserAvatar[] = Array.from({ length: 40 }, (_, i) => {
    const color = getRandomColor();
    return {
      id: `user-${i}`,
      username: `user${i}`,
      avatarUrl: `https://placehold.co/150x150/${color}/FFFFFF?text=${i + 1}`,
      status: Math.random() > 0.7 ? 'online' : 'offline'
    };
  });

  // Split users into 4 columns
  const displayUsers = users.length > 0 ? users : defaultUsers;
  const column1 = displayUsers.slice(0, 10);
  const column2 = displayUsers.slice(10, 20);
  const column3 = displayUsers.slice(20, 30);
  const column4 = displayUsers.slice(30, 40);

  // Handle avatar click
  const handleAvatarClick = (username: string) => {
    router.push(`/user/${username}`);
  };

  // Update height when expanded/collapsed
  useEffect(() => {
    if (contentRef.current && isExpanded) {
      animatedHeight.set(contentRef.current.scrollHeight);
    } else {
      animatedHeight.set(0);
    }
  }, [isExpanded, animatedHeight]);

  // Helper function to update a specific column's hover state
  const setColumnHover = (column: keyof typeof hoverStates, isHovering: boolean) => {
    setHoverStates(prev => ({
      ...prev,
      [column]: isHovering
    }));
  };

  return (
    <div className="mb-3 w-full max-w-full overflow-hidden">
      <button
        onClick={toggleExpand}
        className="flex items-center justify-between w-full h-10 px-3 rounded-lg bg-transparent transition-colors text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white"
      >
        <div className="flex items-center text-sm">
          <Clover className="h-4 w-4 mr-2" />
          <span>Lucky Dip</span>
        </div>
        <div>
          <svg
            width={16}
            height={16}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`fill-current transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
          >
            <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
          </svg>
        </div>
      </button>

      <motion.div
        className="overflow-hidden w-full"
        style={{ height: animatedHeight }}
      >
        <div
          ref={contentRef}
          className="mt-3 pb-4 w-full"
        >
          <div className="h-[300px] flex gap-1 overflow-hidden">
            {/* Column 1 - Scrolling Down */}
            <div
              className="w-1/4 relative overflow-hidden"
              onMouseEnter={() => setColumnHover('column1', true)}
              onMouseLeave={() => setColumnHover('column1', false)}
            >
              <div
                className="flex flex-col gap-2 items-center animate-scroll-down"
                style={{ animationPlayState: hoverStates.column1 ? 'paused' : 'running' }}
              >
                {[...column1, ...column1].map((user, index) => (
                  <div
                    key={`${user.id}-${index}`}
                    className="w-[70px] h-[70px] rounded-full overflow-visible border border-gray-300 dark:border-white/30 cursor-pointer transform transition-transform hover:scale-110 hover:border-turquoise dark:hover:border-turquoise"
                    onClick={() => handleAvatarClick(user.username)}
                  >
                    <img
                      src={user.avatarUrl}
                      alt={user.username}
                      className="w-full h-full object-cover rounded-full"
                      loading="lazy"
                    />
                    {user.status == 'online' ? (
                      <div className="absolute bottom-0 right-1 w-4 h-4 bg-green-500 rounded-full border border-white"></div>
                    ) : (
                      <div className="absolute bottom-0 right-1 w-4 h-4 bg-red-500 rounded-full border border-white"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Column 2 - Scrolling Up */}
            <div
              className="w-1/4 relative overflow-hidden"
              onMouseEnter={() => setColumnHover('column2', true)}
              onMouseLeave={() => setColumnHover('column2', false)}
            >
              <div
                className="flex flex-col gap-2 items-center animate-scroll-up"
                style={{ animationPlayState: hoverStates.column2 ? 'paused' : 'running' }}
              >
                {[...column2, ...column2].map((user, index) => (
                  <div
                    key={`${user.id}-${index}`}
                    className="w-[70px] h-[70px] rounded-full overflow-visible border border-gray-300 dark:border-white/30 cursor-pointer transform transition-transform hover:scale-110 hover:border-turquoise dark:hover:border-turquoise"
                    onClick={() => handleAvatarClick(user.username)}
                  >
                    <img
                      src={user.avatarUrl}
                      alt={user.username}
                      className="w-full h-full object-cover rounded-full"
                      loading="lazy"
                    />
                    {user.status == 'online' ? (
                      <div className="absolute bottom-0 right-1 w-4 h-4 bg-green-500 rounded-full border border-white"></div>
                    ) : (
                      <div className="absolute bottom-0 right-1 w-4 h-4 bg-red-500 rounded-full border border-white"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Column 3 - Scrolling Down */}
            <div
              className="w-1/4 relative overflow-hidden"
              onMouseEnter={() => setColumnHover('column3', true)}
              onMouseLeave={() => setColumnHover('column3', false)}
            >
              <div
                className="flex flex-col gap-2 items-center animate-scroll-down-slow"
                style={{ animationPlayState: hoverStates.column3 ? 'paused' : 'running' }}
              >
                {[...column3, ...column3].map((user, index) => (
                  <div
                    key={`${user.id}-${index}`}
                    className="w-[70px] h-[70px] rounded-full overflow-visible border border-gray-300 dark:border-white/30 cursor-pointer transform transition-transform hover:scale-110 hover:border-turquoise dark:hover:border-turquoise"
                    onClick={() => handleAvatarClick(user.username)}
                  >
                    <img
                      src={user.avatarUrl}
                      alt={user.username}
                      className="w-full h-full object-cover rounded-full"
                      loading="lazy"
                    />
                    {user.status == 'online' ? (
                      <div className="absolute bottom-0 right-1 w-4 h-4 bg-green-500 rounded-full border border-white"></div>
                    ) : (
                      <div className="absolute bottom-0 right-1 w-4 h-4 bg-red-500 rounded-full border border-white"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Column 4 - Scrolling Up */}
            <div
              className="w-1/4 relative overflow-hidden"
              onMouseEnter={() => setColumnHover('column4', true)}
              onMouseLeave={() => setColumnHover('column4', false)}
            >
              <div
                className="flex flex-col gap-2 items-center animate-scroll-up-slow"
                style={{ animationPlayState: hoverStates.column4 ? 'paused' : 'running' }}
              >
                {[...column4, ...column4].map((user, index) => (
                  <div
                    key={`${user.id}-${index}`}
                    className="w-[70px] h-[70px] rounded-full overflow-visible border border-gray-300 dark:border-white/30 cursor-pointer transform transition-transform hover:scale-110 hover:border-turquoise dark:hover:border-turquoise"
                    onClick={() => handleAvatarClick(user.username)}
                  >
                    <img
                      src={user.avatarUrl}
                      alt={user.username}
                      className="w-full h-full object-cover rounded-full"
                      loading="lazy"
                    />
                    {user.status == 'online' ? (
                      <div className="absolute bottom-0 right-1 w-4 h-4 bg-green-500 rounded-full border border-white"></div>
                    ) : (
                      <div className="absolute bottom-0 right-1 w-4 h-4 bg-red-500 rounded-full border border-white"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
export default LuckyDip;


