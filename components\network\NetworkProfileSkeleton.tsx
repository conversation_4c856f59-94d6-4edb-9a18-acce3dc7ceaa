import { Skeleton } from '@/components/ui/skeleton';

interface NetworkProfileSkeletonProps {
  viewMode?: 'map' | 'grid' | 'list';
}

export default function NetworkProfileSkeleton({ viewMode = 'grid' }: NetworkProfileSkeletonProps) {
  if (viewMode === 'list') {
    return (
      <div className="relative bg-white dark:bg-zinc-700 rounded-lg shadow p-4 flex items-center gap-4 border border-solid border-gray-300 dark:border-white">
        {/* Profile Image */}
        <div className="w-16 h-16 rounded-full overflow-hidden flex-shrink-0">
          <Skeleton className="w-full h-full rounded-full" />
        </div>

        <div className="flex-1 space-y-2">
          {/* Username */}
          <Skeleton className="h-5 w-32" />

          {/* Account Type */}
          <Skeleton className="h-4 w-24" />

          {/* Description */}
          <Skeleton className="h-4 w-full" />
        </div>

        {/* Subscribe Button */}
        <div className="flex-shrink-0">
          <Skeleton className="h-10 w-28 rounded-md" />
        </div>
      </div>
    );
  }

  return (
    <div className="relative bg-white dark:bg-zinc-700 rounded-lg shadow text-center border border-solid border-gray-300 dark:border-white">
      {/* Split Background: Turquoise Top */}
      <div className="relative h-32 rounded-t-lg">
        <Skeleton className="h-full w-full rounded-t-lg" borderRadius="rounded-t-lg" />
      </div>

      <div className="relative bg-white dark:bg-zinc-700 rounded-b-lg pt-16 pb-4">
        {/* Profile Image: Overlaps the Turquoise and White Sections */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 rounded-full overflow-hidden border-4 border-white dark:border-gray-700">
          <Skeleton className="w-full h-full rounded-full" />
        </div>

        <div className="flex flex-col items-center gap-4 mt-2">
          {/* Username */}
          <Skeleton className="h-5 w-32" />

          {/* Account Type Badge */}
          <Skeleton className="h-6 w-24 rounded-md" />

          {/* Subscribe/Unsubscribe Button */}
          <Skeleton className="h-10 w-28 rounded-md" />
        </div>

        {/* More Options: Vertically Centered on the Right */}
        <div className="absolute bottom-2 right-4 transform -translate-y-1/2">
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
      </div>
    </div>
  );
}