'use client';

import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/chat/ui/avatar";
import { Film } from 'lucide-react';
import { SelectDropdown } from '@/components/ui/select-dropdown';
import Tippy from '@tippyjs/react';
import { Skeleton } from '@/components/ui/skeleton';
import { FaTh, FaThList } from "react-icons/fa";

const PhotosTab = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filter, setFilter] = useState<string>('recently-added');
  const [isLoading, setIsLoading] = useState(true);

  const filterOptions = [
    { value: 'recently-added', label: 'Recently Added' },
    { value: 'most-viewed', label: 'Most Viewed' },
    { value: 'most-liked', label: 'Most Liked' },
    { value: 'most-sold', label: 'Most Sold' },
    { value: 'highest-price', label: 'Highest Priced' },
    { value: 'lowest-price', label: 'Lowest Priced' },
    { value: 'free', label: 'Free' },
    { value: 'longest', label: 'Longest' },
    { value: 'oldest', label: 'Oldest' },
  ];

  // Mock data - replace with actual data later
  const photos = [
    {
      id: 1,
      thumbnail: 'https://img.manyvids.com/o/v1/https%3A%2F%2Fods.manyvids.com%2F1002980475%2F380d82b798c74fcca73ecb9f24276c02%2Fscreenshots%2Fcustom_1_360_6785086232414.jpg?w=1080&q=80',
      title: 'A Good Boy\'s Reward JOI',
      duration: '31:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 2,
      thumbnail: 'https://img.manyvids.com/o/v1/https%3A%2F%2Fods.manyvids.com%2F1002980475%2Fab970ea622c342729d0ee74c3987b91d%2Fscreenshots%2Fcustom_1_360_65dcd2aa56b47.jpg?w=1080&q=80',
      title: '🥰  VIDEO SPOTLIGHT 🥰',
      duration: '40:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 3,
      thumbnail: 'https://img.manyvids.com/o/v1/https%3A%2F%2Fods.manyvids.com%2F1002980475%2F6052ba8e5ed84ff292836a141f9474a5%2Fscreenshots%2Fcustom_1_360_67c2d90098076.jpg?w=1080&q=80',
      title: 'A mommy story: Mistaken identity',
      duration: '21:26',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 4,
      thumbnail: 'https://img.manyvids.com/o/v1/https%3A%2F%2Fods.manyvids.com%2F1002980475%2Fefa7942ef3d14bb0a5143be45b720de6%2Fscreenshots%2Fcustom_1_360_67097a263bff3.jpg?w=1080&q=80',
      title: 'Soothed By Mommy, its only natural',
      duration: '41:22',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 5,
      thumbnail: 'https://img.manyvids.com/o/v1/https%3A%2F%2Fods.manyvids.com%2F1002980475%2Fo1h3k04k5fr52jn0ol3148ulids%2Fscreenshots%2Fcustom_1_360_6495a4339472b.jpg?w=1080&q=80',
      title: 'Headmistress gives you a private lesson',
      duration: '31:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 6,
      thumbnail: 'https://img.manyvids.com/o/v1/https%3A%2F%2Fods.manyvids.com%2F1002980475%2F1bf62047f4144caab54b76b028e6a472%2Fscreenshots%2Fcustom_1_360_66055f55d22f6.jpg?w=1080&q=80',
      title: 'Colleauges mock your wife and fuck you in front of her',
      duration: '40:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 7,
      thumbnail: 'https://img.manyvids.com/o/v1/https%3A%2F%2Fcdn5.manyvids.com%2Fphp_uploads%2Fstore%2FPeachySkye%2Fimage%2Fstore_1626306229.png?w=1080&q=80',
      title: 'Girlfriend experience',
      duration: '21:26',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 8,
      thumbnail: 'https://img.manyvids.com/o/v1/https%3A%2F%2Fcdn5.manyvids.com%2Fphp_uploads%2Fstore%2FPeachySkye%2Fimage%2Fstore_1630444740.jpg?w=1080&q=80',
      title: 'Treat me to a day off',
      duration: '41:22',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 10,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Amazing Sunset Time-lapse',
      duration: '31:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 11,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Mountain Climbing Adventure',
      duration: '40:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 12,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Ocean Waves Compilation',
      duration: '21:26',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 13,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'City Lights at Night',
      duration: '41:22',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 14,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Amazing Sunset Time-lapse',
      duration: '31:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 15,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Mountain Climbing Adventure',
      duration: '40:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 16,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Ocean Waves Compilation',
      duration: '21:26',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 17,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'City Lights at Night',
      duration: '41:22',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 18,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Amazing Sunset Time-lapse',
      duration: '31:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 19,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Mountain Climbing Adventure',
      duration: '40:25',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 20,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'Ocean Waves Compilation',
      duration: '21:26',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
    {
      id: 21,
      thumbnail: 'https://picsum.photos/800/450',
      title: 'City Lights at Night',
      duration: '41:22',
      resolution: '4K',
      author: 'Kylie Dark',
      authorAvatar: 'https://10u59dx8uz.ufs.sh/f/TawMGEVff3ANTGd7IQff3ANDjHeSB47RKUIQPOxaC0ZW5Mqk'
    },
  ];

  // For demo purposes - set to true to see the empty state
  const isEmpty = false;

  return (
    <div className="py-4 pl-3 pr-0 pt-0">
      {isEmpty ? (
        <div className="rounded-lg p-12 flex flex-col items-center justify-center text-center">
          <Film className="w-16 h-16 text-gray-500 mb-4" />
          <p className="text-gray-400 text-lg mb-2">No photos uploaded yet</p>
          <p className="text-gray-500 text-sm">Upload your first photo to get started</p>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center mb-4 relative">
            <div className="w-40 z-[1] relative">
              <SelectDropdown
                value={filter}
                onChange={(value) => setFilter(value)}
                options={filterOptions}
                placeholder="Filter by"
                className="w-full"
                buttonClassName="bg-transparent dark:bg-transparent rounded-full"
                dropdownClassName="rounded-lg dark:!bg-[#121212]/80 bg-white/80 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-solid border-[#121212]/20 dark:border-white/20"
                optionClassName=""
              />
            </div>

            <div className="text-center text-gorilla-gray dark:text-white text-sm absolute w-full z-0 top-[50%] left-0 -translate-y-1/2">
              Results: {photos.length}
            </div>

            <div className="flex gap-2 space-x-2 rounded-lg p-1 z-[1] relative">
              <Tippy content="Grid View" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                <button
                  className={`p-0 rounded`}
                  onClick={() => setViewMode('grid')}
                >
                  <FaTh
                    size={32}
                    className={`${viewMode === 'grid'
                        ? 'text-turquoise'
                        : 'text-gorilla-gray dark:text-white'
                      }`}
                  />
                </button>
              </Tippy>
              <Tippy content="List View" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                <button
                  className={`p-0 rounded`}
                  onClick={() => setViewMode('list')}
                >
                  <FaThList
                    size={32}
                    className={`${viewMode === 'list'
                        ? 'text-turquoise'
                        : 'text-gorilla-gray dark:text-white'
                      }`}
                  />
                </button>
              </Tippy>
            </div>
          </div>

          <div className={`${viewMode === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4' : 'space-y-4'}`}>
            {photos.map((photo) => (
              <div key={photo.id} className="bg-transparent overflow-hidden group cursor-pointer p-1 border border-solid border-gray-300 dark:border-white rounded-lg">
                <div className="relative aspect-video rounded-lg overflow-hidden">
                  <Skeleton loading={isLoading} width="100%" height="100%" borderRadius="6px">
                    <img src={photo.thumbnail}
                      alt={photo.title} className="w-full h-full object-cover"
                      onLoad={() => setIsLoading(false)} />
                  </Skeleton>
                  <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-2 py-1 rounded">
                    <span className="mr-1">{photo.resolution}</span>
                    <span>{photo.duration}</span>
                  </div>
                </div>
                <div className="px-0 py-1.5 flex items-center justify-start gap-1.5">
                  <Avatar className="h-9 w-9">
                    <AvatarImage src={photo.authorAvatar} />
                    <AvatarFallback>{photo.author[0]}</AvatarFallback>
                  </Avatar>
                  <div className="flex items-center justify-start flex-col w-full">
                    <h3 className="w-full font-semibold text-sm mb-0 line-clamp-1 text-gray-600 dark:text-white text-ellipsis overflow-hidden">{photo.title}</h3>
                    <p className="text-xs text-gorilla-gray w-full">{photo.author}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
export default PhotosTab;