import { jsx as o, jsxs as n } from "react/jsx-runtime";
import f from "../../hooks/useScreen.js";
import { Dialog as u, DialogTrigger as T, DialogContent as v } from "./dialog.js";
import { TooltipProvider as D, <PERSON>ltip as N, TooltipTrigger as x, <PERSON><PERSON>ipPortal as j, TooltipContent as P } from "./tooltip.js";
import { cn as i } from "../../lib.js";
import "react";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
const U = ({
  trigger: r,
  content: m,
  activeMobile: e = !0,
  triggerClass: t = void 0,
  contentClass: p = void 0,
  delayDuration: d = 200,
  side: c = "top",
  triggerAsChild: l = !1,
  open: a = void 0,
  disable: s = !0
}) => {
  const { isMobile: h } = f();
  return s ? /* @__PURE__ */ o("div", { className: i(t), children: r }) : !h || !e ? /* @__PURE__ */ o(D, { delayDuration: d, children: /* @__PURE__ */ n(N, { open: a, children: [
    /* @__PURE__ */ o(x, { asChild: l, className: i(t), children: r }),
    /* @__PURE__ */ o(j, { container: document.getElementById("dexhunter-root"), children: /* @__PURE__ */ o(P, { side: c, className: i(p), children: m }) })
  ] }) }) : e ? /* @__PURE__ */ n(u, { children: [
    /* @__PURE__ */ o(T, { asChild: l, className: i(t), children: r }),
    /* @__PURE__ */ o(v, { className: i(p), children: m })
  ] }) : /* @__PURE__ */ o("div", { className: i(t), children: r });
};
export {
  U as default
};
