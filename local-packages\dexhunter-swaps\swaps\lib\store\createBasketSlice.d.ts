export interface BasketSlice {
    isAddBasketOpen: boolean;
    setIsAddBasketOpen: (_isAddBasketOpen: boolean) => void;
    isSwapBasketOpen: boolean;
    setIsSwapBasketOpen: (_isSwapBasketOpen: boolean) => void;
    toggleIsAddBasketOpen: () => void;
    toggleIsSwapBasketOpen: () => void;
    swapType: string;
    setSwapType: (type: string) => void;
    totalAmount: number;
    swapDetails: any;
    setTotalAmount: (amount: number) => void;
    estimationError: string;
    setEstimationError: (error: string) => void;
    setSwapDetails: (swapDetails: any) => void;
    isSwapDetailsLoading: boolean;
    setIsSwapDetailsLoading: (isSwapDetailsLoading: boolean) => void;
    selectedBasketId: string;
    setSelectedBasketId: (basketId: string) => void;
    creatingBasketInfo: any;
    setCreatingBasketInfo: (creatingBasketInfo: any) => void;
    activeTokenIndex: number;
    setActiveTokenIndex: (index: number) => void;
    isCreatingBasket: boolean;
    setIsCreatingBasket: (isCreatingBasket: boolean) => void;
    resetCreatingBasketInfo: () => void;
    basketList: any[];
    setBasketList: (basketList: any[]) => void;
    basketBalance: number;
    setBasketBalance: (balance: number) => void;
    isBasketBalanceLoading: boolean;
    setIsBasketBalanceLoading: (isLoading: boolean) => void;
}
declare const createBasketSlice: (set: any) => {
    isCreatingBasket: boolean;
    isAddBasketOpen: boolean;
    isSwapBasketOpen: boolean;
    swapType: string;
    totalAmount: number;
    swapDetails: null;
    estimationError: string;
    isSwapDetailsLoading: boolean;
    selectedBasketId: string;
    creatingBasketInfo: {
        name: string;
        tokens: {
            tokenId: string;
            pct: number;
        }[];
    };
    activeTokenIndex: number;
    basketList: never[];
    basketBalance: number;
    isBasketBalanceLoading: boolean;
    toggleIsAddBasketOpen: () => void;
    toggleIsSwapBasketOpen: () => void;
    setIsAddBasketOpen: (_isAddBasketOpen: boolean) => void;
    setIsSwapBasketOpen: (_isSwapBasketOpen: boolean) => void;
    setSwapType: (type: string) => void;
    setTotalAmount: (amount: number) => void;
    setSwapDetails: (swapDetails: any) => void;
    setEstimationError: (error: string) => void;
    setIsSwapDetailsLoading: (isSwapDetailsLoading: boolean) => void;
    setSelectedBasketId: (basketId: string) => void;
    setCreatingBasketInfo: (creatingBasketInfo: any) => void;
    setActiveTokenIndex: (index: number) => void;
    setIsCreatingBasket: (isCreatingBasket: boolean) => void;
    resetCreatingBasketInfo: () => void;
    setBasketList: (basketList: any[]) => void;
    setBasketBalance: (balance: number) => void;
    setIsBasketBalanceLoading: (isLoading: boolean) => void;
};
export default createBasketSlice;
