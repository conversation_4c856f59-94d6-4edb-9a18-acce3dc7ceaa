import { jsx as t, jsxs as n } from "react/jsx-runtime";
import { useState as l, useMemo as d } from "react";
import a from "../../assets/svg/IconX.js";
import { cn as m } from "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const x = ({ className: o, children: e }) => {
  const [s, r] = l(!1), i = d(() => s ? /* @__PURE__ */ t("div", { className: "dhs-absolute dhs-right-0 dhs-bottom-[70px]", children: e }) : null, [e, s]), h = d(() => s ? /* @__PURE__ */ t(a, { className: "dhs-text-mainText", width: 18, height: 18 }) : /* @__PURE__ */ t(
    "img",
    {
      src: "https://storage.googleapis.com/dexhunter-images/public/hunt-token.svg",
      className: "dhs-w-full dhs-h-full",
      alt: "DexHunter"
    }
  ), [s]);
  return /* @__PURE__ */ t("div", { className: m("dhs-fixed dhs-bottom-6 dhs-right-6", o), children: /* @__PURE__ */ n("div", { className: "dhs-relative", children: [
    /* @__PURE__ */ t(
      "div",
      {
        onClick: () => r(!s),
        className: "dhs-widget-button dhs-h-14 dhs-w-14 dhs-rounded-full dhs-bg-background dhs-flex dhs-items-center dhs-justify-center dhs-cursor-pointer",
        children: h
      }
    ),
    i
  ] }) });
};
export {
  x as default
};
