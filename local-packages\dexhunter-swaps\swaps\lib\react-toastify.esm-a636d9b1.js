import h, { forwardRef as me, useEffect as H, useReducer as fe, useState as se, useRef as S, isValidElement as j, cloneElement as ae, useLayoutEffect as ge } from "react";
function ue(e) {
  var t, s, o = "";
  if (typeof e == "string" || typeof e == "number")
    o += e;
  else if (typeof e == "object")
    if (Array.isArray(e))
      for (t = 0; t < e.length; t++)
        e[t] && (s = ue(e[t])) && (o && (o += " "), o += s);
    else
      for (t in e)
        e[t] && (o && (o += " "), o += t);
  return o;
}
function $() {
  for (var e, t, s = 0, o = ""; s < arguments.length; )
    (e = arguments[s++]) && (t = ue(e)) && (o && (o += " "), o += t);
  return o;
}
const U = (e) => typeof e == "number" && !isNaN(e), q = (e) => typeof e == "string", L = (e) => typeof e == "function", V = (e) => q(e) || L(e) ? e : null, te = (e) => j(e) || q(e) || L(e) || U(e);
function he(e, t, s) {
  s === void 0 && (s = 300);
  const { scrollHeight: o, style: u } = e;
  requestAnimationFrame(() => {
    u.minHeight = "initial", u.height = o + "px", u.transition = `all ${s}ms`, requestAnimationFrame(() => {
      u.height = "0", u.padding = "0", u.margin = "0", setTimeout(t, s);
    });
  });
}
function Z(e) {
  let { enter: t, exit: s, appendPosition: o = !1, collapse: u = !0, collapseDuration: l = 300 } = e;
  return function(n) {
    let { children: a, position: C, preventExitTransition: T, done: y, nodeRef: f, isIn: b } = n;
    const i = o ? `${t}--${C}` : t, d = o ? `${s}--${C}` : s, p = S(0);
    return ge(() => {
      const r = f.current, c = i.split(" "), E = (_) => {
        _.target === f.current && (r.dispatchEvent(new Event("d")), r.removeEventListener("animationend", E), r.removeEventListener("animationcancel", E), p.current === 0 && _.type !== "animationcancel" && r.classList.remove(...c));
      };
      r.classList.add(...c), r.addEventListener("animationend", E), r.addEventListener("animationcancel", E);
    }, []), H(() => {
      const r = f.current, c = () => {
        r.removeEventListener("animationend", c), u ? he(r, y, l) : y();
      };
      b || (T ? c() : (p.current = 1, r.className += ` ${d}`, r.addEventListener("animationend", c)));
    }, [b]), h.createElement(h.Fragment, null, a);
  };
}
function re(e, t) {
  return e != null ? { content: e.content, containerId: e.props.containerId, id: e.props.toastId, theme: e.props.theme, type: e.props.type, data: e.props.data || {}, isLoading: e.props.isLoading, icon: e.props.icon, status: t } : {};
}
const M = { list: /* @__PURE__ */ new Map(), emitQueue: /* @__PURE__ */ new Map(), on(e, t) {
  return this.list.has(e) || this.list.set(e, []), this.list.get(e).push(t), this;
}, off(e, t) {
  if (t) {
    const s = this.list.get(e).filter((o) => o !== t);
    return this.list.set(e, s), this;
  }
  return this.list.delete(e), this;
}, cancelEmit(e) {
  const t = this.emitQueue.get(e);
  return t && (t.forEach(clearTimeout), this.emitQueue.delete(e)), this;
}, emit(e) {
  this.list.has(e) && this.list.get(e).forEach((t) => {
    const s = setTimeout(() => {
      t(...[].slice.call(arguments, 1));
    }, 0);
    this.emitQueue.has(e) || this.emitQueue.set(e, []), this.emitQueue.get(e).push(s);
  });
} }, Y = (e) => {
  let { theme: t, type: s, ...o } = e;
  return h.createElement("svg", { viewBox: "0 0 24 24", width: "100%", height: "100%", fill: t === "colored" ? "currentColor" : `var(--toastify-icon-color-${s})`, ...o });
}, ne = { info: function(e) {
  return h.createElement(Y, { ...e }, h.createElement("path", { d: "M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z" }));
}, warning: function(e) {
  return h.createElement(Y, { ...e }, h.createElement("path", { d: "M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z" }));
}, success: function(e) {
  return h.createElement(Y, { ...e }, h.createElement("path", { d: "M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z" }));
}, error: function(e) {
  return h.createElement(Y, { ...e }, h.createElement("path", { d: "M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z" }));
}, spinner: function() {
  return h.createElement("div", { className: "Toastify__spinner" });
} };
function ye(e) {
  const [, t] = fe((i) => i + 1, 0), [s, o] = se([]), u = S(null), l = S(/* @__PURE__ */ new Map()).current, n = (i) => s.indexOf(i) !== -1, a = S({ toastKey: 1, displayedToast: 0, count: 0, queue: [], props: e, containerId: null, isToastActive: n, getToast: (i) => l.get(i) }).current;
  function C(i) {
    let { containerId: d } = i;
    const { limit: p } = a.props;
    !p || d && a.containerId !== d || (a.count -= a.queue.length, a.queue = []);
  }
  function T(i) {
    o((d) => i == null ? [] : d.filter((p) => p !== i));
  }
  function y() {
    const { toastContent: i, toastProps: d, staleId: p } = a.queue.shift();
    b(i, d, p);
  }
  function f(i, d) {
    let { delay: p, staleId: r, ...c } = d;
    if (!te(i) || function(R) {
      return !u.current || a.props.enableMultiContainer && R.containerId !== a.props.containerId || l.has(R.toastId) && R.updateId == null;
    }(c))
      return;
    const { toastId: E, updateId: _, data: m } = c, { props: g } = a, k = () => T(E), P = _ == null;
    P && a.count++;
    const I = { ...g, style: g.toastStyle, key: a.toastKey++, ...Object.fromEntries(Object.entries(c).filter((R) => {
      let [w, O] = R;
      return O != null;
    })), toastId: E, updateId: _, data: m, closeToast: k, isIn: !1, className: V(c.className || g.toastClassName), bodyClassName: V(c.bodyClassName || g.bodyClassName), progressClassName: V(c.progressClassName || g.progressClassName), autoClose: !c.isLoading && (B = c.autoClose, Q = g.autoClose, B === !1 || U(B) && B > 0 ? B : Q), deleteToast() {
      const R = re(l.get(E), "removed");
      l.delete(E), M.emit(4, R);
      const w = a.queue.length;
      if (a.count = E == null ? a.count - a.displayedToast : a.count - 1, a.count < 0 && (a.count = 0), w > 0) {
        const O = E == null ? a.props.limit : 1;
        if (w === 1 || O === 1)
          a.displayedToast++, y();
        else {
          const D = O > w ? w : O;
          a.displayedToast = D;
          for (let N = 0; N < D; N++)
            y();
        }
      } else
        t();
    } };
    var B, Q;
    I.iconOut = function(R) {
      let { theme: w, type: O, isLoading: D, icon: N } = R, x = null;
      const z = { theme: w, type: O };
      return N === !1 || (L(N) ? x = N(z) : j(N) ? x = ae(N, z) : q(N) || U(N) ? x = N : D ? x = ne.spinner() : ((X) => X in ne)(O) && (x = ne[O](z))), x;
    }(I), L(c.onOpen) && (I.onOpen = c.onOpen), L(c.onClose) && (I.onClose = c.onClose), I.closeButton = g.closeButton, c.closeButton === !1 || te(c.closeButton) ? I.closeButton = c.closeButton : c.closeButton === !0 && (I.closeButton = !te(g.closeButton) || g.closeButton);
    let A = i;
    j(i) && !q(i.type) ? A = ae(i, { closeToast: k, toastProps: I, data: m }) : L(i) && (A = i({ closeToast: k, toastProps: I, data: m })), g.limit && g.limit > 0 && a.count > g.limit && P ? a.queue.push({ toastContent: A, toastProps: I, staleId: r }) : U(p) ? setTimeout(() => {
      b(A, I, r);
    }, p) : b(A, I, r);
  }
  function b(i, d, p) {
    const { toastId: r } = d;
    p && l.delete(p);
    const c = { content: i, props: d };
    l.set(r, c), o((E) => [...E, r].filter((_) => _ !== p)), M.emit(4, re(c, c.props.updateId == null ? "added" : "updated"));
  }
  return H(() => (a.containerId = e.containerId, M.cancelEmit(3).on(0, f).on(1, (i) => u.current && T(i)).on(5, C).emit(2, a), () => {
    l.clear(), M.emit(3, a);
  }), []), H(() => {
    a.props = e, a.isToastActive = n, a.displayedToast = s.length;
  }), { getToastToRender: function(i) {
    const d = /* @__PURE__ */ new Map(), p = Array.from(l.values());
    return e.newestOnTop && p.reverse(), p.forEach((r) => {
      const { position: c } = r.props;
      d.has(c) || d.set(c, []), d.get(c).push(r);
    }), Array.from(d, (r) => i(r[0], r[1]));
  }, containerRef: u, isToastActive: n };
}
function ie(e) {
  return e.targetTouches && e.targetTouches.length >= 1 ? e.targetTouches[0].clientX : e.clientX;
}
function le(e) {
  return e.targetTouches && e.targetTouches.length >= 1 ? e.targetTouches[0].clientY : e.clientY;
}
function ve(e) {
  const [t, s] = se(!1), [o, u] = se(!1), l = S(null), n = S({ start: 0, x: 0, y: 0, delta: 0, removalDistance: 0, canCloseOnClick: !0, canDrag: !1, boundingRect: null, didMove: !1 }).current, a = S(e), { autoClose: C, pauseOnHover: T, closeToast: y, onClick: f, closeOnClick: b } = e;
  function i(m) {
    if (e.draggable) {
      m.nativeEvent.type === "touchstart" && m.nativeEvent.preventDefault(), n.didMove = !1, document.addEventListener("mousemove", c), document.addEventListener("mouseup", E), document.addEventListener("touchmove", c), document.addEventListener("touchend", E);
      const g = l.current;
      n.canCloseOnClick = !0, n.canDrag = !0, n.boundingRect = g.getBoundingClientRect(), g.style.transition = "", n.x = ie(m.nativeEvent), n.y = le(m.nativeEvent), e.draggableDirection === "x" ? (n.start = n.x, n.removalDistance = g.offsetWidth * (e.draggablePercent / 100)) : (n.start = n.y, n.removalDistance = g.offsetHeight * (e.draggablePercent === 80 ? 1.5 * e.draggablePercent : e.draggablePercent / 100));
    }
  }
  function d(m) {
    if (n.boundingRect) {
      const { top: g, bottom: k, left: P, right: I } = n.boundingRect;
      m.nativeEvent.type !== "touchend" && e.pauseOnHover && n.x >= P && n.x <= I && n.y >= g && n.y <= k ? r() : p();
    }
  }
  function p() {
    s(!0);
  }
  function r() {
    s(!1);
  }
  function c(m) {
    const g = l.current;
    n.canDrag && g && (n.didMove = !0, t && r(), n.x = ie(m), n.y = le(m), n.delta = e.draggableDirection === "x" ? n.x - n.start : n.y - n.start, n.start !== n.x && (n.canCloseOnClick = !1), g.style.transform = `translate${e.draggableDirection}(${n.delta}px)`, g.style.opacity = "" + (1 - Math.abs(n.delta / n.removalDistance)));
  }
  function E() {
    document.removeEventListener("mousemove", c), document.removeEventListener("mouseup", E), document.removeEventListener("touchmove", c), document.removeEventListener("touchend", E);
    const m = l.current;
    if (n.canDrag && n.didMove && m) {
      if (n.canDrag = !1, Math.abs(n.delta) > n.removalDistance)
        return u(!0), void e.closeToast();
      m.style.transition = "transform 0.2s, opacity 0.2s", m.style.transform = `translate${e.draggableDirection}(0)`, m.style.opacity = "1";
    }
  }
  H(() => {
    a.current = e;
  }), H(() => (l.current && l.current.addEventListener("d", p, { once: !0 }), L(e.onOpen) && e.onOpen(j(e.children) && e.children.props), () => {
    const m = a.current;
    L(m.onClose) && m.onClose(j(m.children) && m.children.props);
  }), []), H(() => (e.pauseOnFocusLoss && (document.hasFocus() || r(), window.addEventListener("focus", p), window.addEventListener("blur", r)), () => {
    e.pauseOnFocusLoss && (window.removeEventListener("focus", p), window.removeEventListener("blur", r));
  }), [e.pauseOnFocusLoss]);
  const _ = { onMouseDown: i, onTouchStart: i, onMouseUp: d, onTouchEnd: d };
  return C && T && (_.onMouseEnter = r, _.onMouseLeave = p), b && (_.onClick = (m) => {
    f && f(m), n.canCloseOnClick && y();
  }), { playToast: p, pauseToast: r, isRunning: t, preventExitTransition: o, toastRef: l, eventHandlers: _ };
}
function de(e) {
  let { closeToast: t, theme: s, ariaLabel: o = "close" } = e;
  return h.createElement("button", { className: `Toastify__close-button Toastify__close-button--${s}`, type: "button", onClick: (u) => {
    u.stopPropagation(), t(u);
  }, "aria-label": o }, h.createElement("svg", { "aria-hidden": "true", viewBox: "0 0 14 16" }, h.createElement("path", { fillRule: "evenodd", d: "M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z" })));
}
function Te(e) {
  let { delay: t, isRunning: s, closeToast: o, type: u = "default", hide: l, className: n, style: a, controlledProgress: C, progress: T, rtl: y, isIn: f, theme: b } = e;
  const i = l || C && T === 0, d = { ...a, animationDuration: `${t}ms`, animationPlayState: s ? "running" : "paused", opacity: i ? 0 : 1 };
  C && (d.transform = `scaleX(${T})`);
  const p = $("Toastify__progress-bar", C ? "Toastify__progress-bar--controlled" : "Toastify__progress-bar--animated", `Toastify__progress-bar-theme--${b}`, `Toastify__progress-bar--${u}`, { "Toastify__progress-bar--rtl": y }), r = L(n) ? n({ rtl: y, type: u, defaultClassName: p }) : $(p, n);
  return h.createElement("div", { role: "progressbar", "aria-hidden": i ? "true" : "false", "aria-label": "notification timer", className: r, style: d, [C && T >= 1 ? "onTransitionEnd" : "onAnimationEnd"]: C && T < 1 ? null : () => {
    f && o();
  } });
}
const Ee = (e) => {
  const { isRunning: t, preventExitTransition: s, toastRef: o, eventHandlers: u } = ve(e), { closeButton: l, children: n, autoClose: a, onClick: C, type: T, hideProgressBar: y, closeToast: f, transition: b, position: i, className: d, style: p, bodyClassName: r, bodyStyle: c, progressClassName: E, progressStyle: _, updateId: m, role: g, progress: k, rtl: P, toastId: I, deleteToast: B, isIn: Q, isLoading: A, iconOut: R, closeOnClick: w, theme: O } = e, D = $("Toastify__toast", `Toastify__toast-theme--${O}`, `Toastify__toast--${T}`, { "Toastify__toast--rtl": P }, { "Toastify__toast--close-on-click": w }), N = L(d) ? d({ rtl: P, position: i, type: T, defaultClassName: D }) : $(D, d), x = !!k || !a, z = { closeToast: f, type: T, theme: O };
  let X = null;
  return l === !1 || (X = L(l) ? l(z) : j(l) ? ae(l, z) : de(z)), h.createElement(b, { isIn: Q, done: B, position: i, preventExitTransition: s, nodeRef: o }, h.createElement("div", { id: I, onClick: C, className: N, ...u, style: p, ref: o }, h.createElement("div", { ...Q && { role: g }, className: L(r) ? r({ type: T }) : $("Toastify__toast-body", r), style: c }, R != null && h.createElement("div", { className: $("Toastify__toast-icon", { "Toastify--animate-icon Toastify__zoom-enter": !A }) }, R), h.createElement("div", null, n)), X, h.createElement(Te, { ...m && !x ? { key: `pb-${m}` } : {}, rtl: P, theme: O, delay: a, isRunning: t, isIn: Q, closeToast: f, hide: y, type: T, style: _, className: E, controlledProgress: x, progress: k || 0 })));
}, ee = function(e, t) {
  return t === void 0 && (t = !1), { enter: `Toastify--animate Toastify__${e}-enter`, exit: `Toastify--animate Toastify__${e}-exit`, appendPosition: t };
}, Ce = Z(ee("bounce", !0));
Z(ee("slide", !0));
Z(ee("zoom"));
Z(ee("flip"));
const ce = me((e, t) => {
  const { getToastToRender: s, containerRef: o, isToastActive: u } = ye(e), { className: l, style: n, rtl: a, containerId: C } = e;
  function T(y) {
    const f = $("Toastify__toast-container", `Toastify__toast-container--${y}`, { "Toastify__toast-container--rtl": a });
    return L(l) ? l({ position: y, rtl: a, defaultClassName: f }) : $(f, V(l));
  }
  return H(() => {
    t && (t.current = o.current);
  }, []), h.createElement("div", { ref: o, className: "Toastify", id: C }, s((y, f) => {
    const b = f.length ? { ...n } : { ...n, pointerEvents: "none" };
    return h.createElement("div", { className: T(y), style: b, key: `container-${y}` }, f.map((i, d) => {
      let { content: p, props: r } = i;
      return h.createElement(Ee, { ...r, isIn: u(r.toastId), style: { ...r.style, "--nth": d + 1, "--len": f.length }, key: `toast-${r.key}` }, p);
    }));
  }));
});
ce.displayName = "ToastContainer", ce.defaultProps = { position: "top-right", transition: Ce, autoClose: 5e3, closeButton: de, pauseOnHover: !0, pauseOnFocusLoss: !0, closeOnClick: !0, draggable: !0, draggablePercent: 80, draggableDirection: "x", role: "alert", theme: "light" };
let oe, F = /* @__PURE__ */ new Map(), G = [], be = 1;
function pe() {
  return "" + be++;
}
function Ie(e) {
  return e && (q(e.toastId) || U(e.toastId)) ? e.toastId : pe();
}
function W(e, t) {
  return F.size > 0 ? M.emit(0, e, t) : G.push({ content: e, options: t }), t.toastId;
}
function J(e, t) {
  return { ...t, type: t && t.type || e, toastId: Ie(t) };
}
function K(e) {
  return (t, s) => W(t, J(e, s));
}
function v(e, t) {
  return W(e, J("default", t));
}
v.loading = (e, t) => W(e, J("default", { isLoading: !0, autoClose: !1, closeOnClick: !1, closeButton: !1, draggable: !1, ...t })), v.promise = function(e, t, s) {
  let o, { pending: u, error: l, success: n } = t;
  u && (o = q(u) ? v.loading(u, s) : v.loading(u.render, { ...s, ...u }));
  const a = { isLoading: null, autoClose: null, closeOnClick: null, closeButton: null, draggable: null }, C = (y, f, b) => {
    if (f == null)
      return void v.dismiss(o);
    const i = { type: y, ...a, ...s, data: b }, d = q(f) ? { render: f } : f;
    return o ? v.update(o, { ...i, ...d }) : v(d.render, { ...i, ...d }), b;
  }, T = L(e) ? e() : e;
  return T.then((y) => C("success", n, y)).catch((y) => C("error", l, y)), T;
}, v.success = K("success"), v.info = K("info"), v.error = K("error"), v.warning = K("warning"), v.warn = v.warning, v.dark = (e, t) => W(e, J("default", { theme: "dark", ...t })), v.dismiss = (e) => {
  F.size > 0 ? M.emit(1, e) : G = G.filter((t) => e != null && t.options.toastId !== e);
}, v.clearWaitingQueue = function(e) {
  return e === void 0 && (e = {}), M.emit(5, e);
}, v.isActive = (e) => {
  let t = !1;
  return F.forEach((s) => {
    s.isToastActive && s.isToastActive(e) && (t = !0);
  }), t;
}, v.update = function(e, t) {
  t === void 0 && (t = {}), setTimeout(() => {
    const s = function(o, u) {
      let { containerId: l } = u;
      const n = F.get(l || oe);
      return n && n.getToast(o);
    }(e, t);
    if (s) {
      const { props: o, content: u } = s, l = { delay: 100, ...o, ...t, toastId: t.toastId || e, updateId: pe() };
      l.toastId !== e && (l.staleId = e);
      const n = l.render || u;
      delete l.render, W(n, l);
    }
  }, 0);
}, v.done = (e) => {
  v.update(e, { progress: 1 });
}, v.onChange = (e) => (M.on(4, e), () => {
  M.off(4, e);
}), v.POSITION = { TOP_LEFT: "top-left", TOP_RIGHT: "top-right", TOP_CENTER: "top-center", BOTTOM_LEFT: "bottom-left", BOTTOM_RIGHT: "bottom-right", BOTTOM_CENTER: "bottom-center" }, v.TYPE = { INFO: "info", SUCCESS: "success", WARNING: "warning", ERROR: "error", DEFAULT: "default" }, M.on(2, (e) => {
  oe = e.containerId || e, F.set(oe, e), G.forEach((t) => {
    M.emit(0, t.content, t.options);
  }), G = [];
}).on(3, (e) => {
  F.delete(e.containerId || e), F.size === 0 && M.off(0).off(1).off(5);
});
export {
  v as Q,
  ce as k
};
