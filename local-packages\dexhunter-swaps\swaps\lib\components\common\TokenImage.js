import { jsx as r, jsxs as d } from "react/jsx-runtime";
import { Skeleton as g } from "../ui/skeleton.js";
import { cn as o } from "../../lib/utils.js";
import { useState as v, useMemo as y } from "react";
import { middlen as b, hexToAscii as N } from "../../utils/cardanoUtils.js";
import w from "../ui/tooltipDialog.js";
import { u as H } from "../../useQuery-febd7967.js";
import { a as j } from "../../axios-ddd885c5.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../index-ca8eb9e1.js";
import "../../config/axios.js";
import "../../hooks/useScreen.js";
import "../ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../query-013b86c3.js";
import "../../QueryClientProvider-6bcd4331.js";
const et = ({
  token: i,
  isVerified: u,
  size: t = 40,
  verifiedSize: n = 15,
  className: h = "dhs-w-8 dhs-h-8 dhs-rounded-full",
  disableTooltip: f = !0,
  verifiedClass: x = void 0
}) => {
  const e = (i == null ? void 0 : i.token_id) || (i == null ? void 0 : i.tokenId) || "", [a, c] = v(!1), { data: l } = H({
    queryKey: ["tokenImage", e],
    queryFn: async () => {
      try {
        const { data: s } = await j.get(
          `https://api6.splash.trade/asset-info/${e}.json`
        );
        return s != null && s.logoCid ? `https://ipfs.io/ipfs/${s.logoCid}` : null;
      } catch (s) {
        return console.error("Error fetching token details from Splash API:", s), null;
      }
    },
    enabled: !!e,
    // Only run the query if tokenId is available
    refetchOnMount: !1
  }), p = y(() => {
    const s = `https://storage.googleapis.com/dexhunter-images/tokens/${e}.webp`;
    return l || s;
  }, [e, l]), m = u && /* @__PURE__ */ r(
    "img",
    {
      src: "https://storage.googleapis.com/dexhunter-images/public/verified.svg",
      className: o(
        "dhs-absolute @md/appRoot:dhs-bottom-[-2.5px] @md/appRoot:dhs-right-[-2.5px] dhs-z-2 dhs-bottom-[-1px] dhs-right-[-1px]",
        x
      ),
      width: n,
      height: n
    }
  );
  return (e === "" || e === "000000000000000000000000000000000000000000000000000000006c6f76656c616365") && i !== void 0 ? /* @__PURE__ */ d(
    "div",
    {
      className: o(
        "dhs-relative dhs-flex dhs-justify-center dhs-items-center dhs-rounded-full",
        h
      ),
      style: {
        minHeight: t,
        minWidth: t,
        maxHeight: t,
        maxWidth: t
      },
      children: [
        /* @__PURE__ */ r(
          "img",
          {
            src: "https://storage.googleapis.com/dexhunter-images/tokens/cardano.png",
            alt: e,
            width: t,
            height: t,
            className: "dhs-rounded-full"
          }
        ),
        m
      ]
    }
  ) : f ? /* @__PURE__ */ d(
    "div",
    {
      className: o(
        "dhs-relative dhs-flex dhs-justify-center dhs-items-center dhs-rounded-full",
        h
      ),
      style: {
        minHeight: t,
        minWidth: t,
        maxHeight: t,
        maxWidth: t
      },
      children: [
        !a && /* @__PURE__ */ r(
          g,
          {
            className: o("dhs-absolute dhs-inset-0 dhs-rounded-full")
          }
        ),
        /* @__PURE__ */ r(
          "img",
          {
            src: p,
            alt: e,
            width: t,
            height: t,
            onLoad: () => c(!0),
            style: {
              display: a ? "block" : "none",
              maxWidth: t,
              maxHeight: t
            },
            className: "dhs-rounded-full",
            onError: (s) => {
              s.target.src = "https://storage.googleapis.com/dexhunter-images/public/unverified.svg";
            }
          }
        ),
        m
      ]
    }
  ) : /* @__PURE__ */ r(
    w,
    {
      activeMobile: !1,
      triggerAsChild: !0,
      trigger: /* @__PURE__ */ d(
        "div",
        {
          className: o(
            "dhs-relative dhs-flex dhs-justify-center dhs-items-center dhs-rounded-full",
            h
          ),
          style: {
            minHeight: t,
            minWidth: t,
            maxHeight: t,
            maxWidth: t
          },
          children: [
            !a && /* @__PURE__ */ r(
              g,
              {
                className: o("dhs-absolute dhs-inset-0 dhs-rounded-full")
              }
            ),
            /* @__PURE__ */ r(
              "img",
              {
                src: p,
                alt: e,
                width: t,
                height: t,
                onLoad: () => c(!0),
                style: {
                  display: a ? "block" : "none",
                  maxWidth: t,
                  maxHeight: t
                },
                className: "dhs-rounded-full",
                onError: (s) => {
                  s.target.src = "https://storage.googleapis.com/dexhunter-images/public/unverified.svg";
                }
              }
            ),
            m
          ]
        }
      ),
      content: /* @__PURE__ */ d("div", { className: "dhs-flex dhs-flex-col dhs-gap-1.5 dhs-text-gray-103 dhs-text-sm dhs-p-2", children: [
        /* @__PURE__ */ d("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
          /* @__PURE__ */ r("div", { children: "Token Policy" }),
          /* @__PURE__ */ r(
            "div",
            {
              className: "dhs-break-all dhs-text-accent hover:dhs-opacity-90 dhs-cursor-pointer",
              onClick: () => window.open(`https://cardanoscan.io/token/${e}`),
              children: b(e == null ? void 0 : e.slice(0, 56), 10)
            }
          )
        ] }),
        /* @__PURE__ */ d("div", { className: "dhs-flex dhs-items-center dhs-gap-8 dhs-justify-between", children: [
          /* @__PURE__ */ r("div", { children: "Token Name" }),
          /* @__PURE__ */ r("div", { className: "dhs-text-buttonText", children: (i == null ? void 0 : i.token_ascii) || N(e == null ? void 0 : e.slice(56)) })
        ] })
      ] }),
      contentClass: "dhs-text-mainText"
    }
  );
};
export {
  et as TokenImage,
  et as default
};
