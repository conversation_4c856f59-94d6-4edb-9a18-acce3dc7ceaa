// lib/convexServerClient.ts
import { v4 as uuidv4 } from 'uuid';

const CONVEX_URL = process.env.CONVEX_DEPLOYMENT;
const ADMIN_KEY = process.env.CONVEX_ADMIN_KEY;

if (!CONVEX_URL || !ADMIN_KEY) {
  throw new Error(
    'Missing CONVEX_DEPLOYMENT or CONVEX_ADMIN_KEY in environment variables'
  );
}

export async function callConvexMutation(
  mutationName: string,
  args: Record<string, any>
): Promise<any> {
  const response = await fetch(`${CONVEX_URL}/api/mutation`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${ADMIN_KEY}`,
      'Convex-Client': 'jsweb:0.0.0',
    },
    body: JSON.stringify({
      name: mutationName,
      arguments: args,
      requestId: `svr-${uuidv4()}`, // unique ID for tracing
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Convex mutation error:', errorText);
    throw new Error(`Failed to call Convex mutation: ${response.status} ${errorText}`);
  }

  return await response.json();
}