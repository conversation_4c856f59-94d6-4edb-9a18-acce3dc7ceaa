'use client';

import React, { useState, useEffect } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Folder, Image, Video, Search, Plus, X, ChevronLeft } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { useUser } from '@/hooks/useUser';
import { toast } from 'react-toastify';

type MediaItem = {
  media_id: string;
  file_path: string;
  file_type: string;
  thumbnail_url: string;
  created_at: string;
  metadata: Record<string, any>;
};

type Folder = {
  folder_id: string;
  folder_name: string;
  parent_folder_id: string | null;
};

interface MediaLibraryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectMedia: (media: MediaItem) => void;
  allowMultiple?: boolean;
}

export default function MediaLibraryModal({
  isOpen,
  onClose,
  onSelectMedia,
  allowMultiple = false
}: MediaLibraryModalProps) {
  const { isAuthenticated, token: session } = useUser();
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState<Folder[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [selectedMedia, setSelectedMedia] = useState<MediaItem[]>([]);
  const [newFolderName, setNewFolderName] = useState('');
  const [showNewFolderInput, setShowNewFolderInput] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Fetch media items
  const fetchMediaItems = async (reset = false) => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      const newPage = reset ? 1 : page;

      let url = `/api/media-library?page=${newPage}&limit=20`;
      if (currentFolder) url += `&folderId=${currentFolder}`;
      if (activeTab !== 'all') url += `&fileType=${activeTab}`;
      if (searchQuery) url += `&search=${encodeURIComponent(searchQuery)}`;

      const response = await fetch(url);
      const data = await response.json();

      if (!data.success) throw new Error(data.message);

      if (reset || newPage === 1) {
        setMediaItems(data.media);
      } else {
        setMediaItems(prev => [...prev, ...data.media]);
      }

      setHasMore(data.media.length === 20);
      setPage(newPage + 1);
    } catch (error: any) {
      toast.error(error.message || 'Failed to load media');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch folders
  const fetchFolders = async () => {
    try {
      const url = `/api/media-library/folders${currentFolder ? `?parentFolderId=${currentFolder}` : ''}`;
      const response = await fetch(url);
      const data = await response.json();

      if (!data.success) throw new Error(data.message);

      setFolders(data.folders);
    } catch (error: any) {
      toast.error(error.message || 'Failed to load folders');
    }
  };

  // Create new folder
  const createFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error('Folder name cannot be empty');
      return;
    }

    try {
      const response = await fetch('/api/media-library/folders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          folderName: newFolderName,
          parentFolderId: currentFolder
        })
      });

      const data = await response.json();

      if (!data.success) throw new Error(data.message);

      setFolders(prev => [...prev, data.folder]);
      setNewFolderName('');
      setShowNewFolderInput(false);
      toast.success('Folder created successfully');
    } catch (error: any) {
      toast.error(error.message || 'Failed to create folder');
    }
  };

  // Navigate to folder
  const navigateToFolder = (folder: Folder) => {
    setCurrentFolder(folder.folder_id);
    setFolderPath(prev => [...prev, folder]);
    setPage(1);
  };

  // Navigate back
  const navigateBack = () => {
    if (folderPath.length === 0) {
      setCurrentFolder(null);
    } else {
      const newPath = [...folderPath];
      newPath.pop();
      setFolderPath(newPath);
      setCurrentFolder(newPath.length > 0 ? newPath[newPath.length - 1].folder_id : null);
    }
    setPage(1);
  };

  // Toggle media selection
  const toggleMediaSelection = (media: MediaItem) => {
    if (!allowMultiple) {
      setSelectedMedia([media]);
      onSelectMedia(media);
      return;
    }

    setSelectedMedia(prev => {
      const isSelected = prev.some(item => item.media_id === media.media_id);
      if (isSelected) {
        return prev.filter(item => item.media_id !== media.media_id);
      } else {
        return [...prev, media];
      }
    });
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchMediaItems(true);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setPage(1);
    fetchMediaItems(true);
  };

  // Handle confirm selection
  const handleConfirmSelection = () => {
    if (selectedMedia.length === 1) {
      onSelectMedia(selectedMedia[0]);
    } else if (selectedMedia.length > 1 && allowMultiple) {
      // If you need to handle multiple selections
      selectedMedia.forEach(media => onSelectMedia(media));
    }
    onClose();
  };

  // Load more media items when scrolling
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollHeight - scrollTop <= clientHeight * 1.5 && !isLoading && hasMore) {
      fetchMediaItems();
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (isOpen) {
      fetchMediaItems(true);
      fetchFolders();
    } else {
      // Reset state when modal closes
      setSelectedMedia([]);
      setSearchQuery('');
      setActiveTab('all');
    }
  }, [isOpen, currentFolder, activeTab]);

  // Render media item
  const renderMediaItem = (item: MediaItem) => {
    const isSelected = selectedMedia.some(media => media.media_id === item.media_id);
    const isImage = item.file_type.startsWith('image/');
    const isVideo = item.file_type.startsWith('video/');

    return (
      <div
        key={item.media_id}
        className={`relative rounded-md overflow-hidden cursor-pointer transition-all border-2 ${
          isSelected ? 'border-blue-500 scale-95' : 'border-transparent hover:border-gray-300'
        }`}
        onClick={() => toggleMediaSelection(item)}
      >
        <div className="aspect-square relative">
          <img
            src={item.thumbnail_url || item.file_path}
            alt="Media"
            className="object-cover w-full h-full"
          />
          {isVideo && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/30">
              <Video className="w-8 h-8 text-white" />
            </div>
          )}
          {isSelected && (
            <div className="absolute top-2 right-2 bg-blue-500 rounded-full p-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>
        <div className="p-2 text-xs truncate">{item.metadata?.filename || 'Untitled'}</div>
      </div>
    );
  };

  // Custom header component
  const customHeader = (
    <div className="flex items-center justify-between w-full">
      <h2 className="text-xl font-semibold">Media Library</h2>
    </div>
  );

  // Custom footer component
  const customFooter = (
    <div className="flex justify-between items-center w-full">
      <div>
        {selectedMedia.length > 0 && (
          <span className="text-sm">{selectedMedia.length} item{selectedMedia.length !== 1 ? 's' : ''} selected</span>
        )}
      </div>
      <div className="flex gap-2">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button
          onClick={handleConfirmSelection}
          disabled={selectedMedia.length === 0}
        >
          Select
        </Button>
      </div>
    </div>
  );

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="7xl"
      header={customHeader}
      footer={customFooter}
      closeButton={true}
    >
      <div className="flex flex-col flex-1 overflow-hidden h-[calc(80vh-120px)]">
        {/* Search and filters */}
        <div className="p-4 border-b">
          <div className="flex items-center gap-4">
            <form onSubmit={handleSearch} className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <Input
                  placeholder="Search media..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </form>

            <Button
              variant="outline"
              onClick={() => setShowNewFolderInput(!showNewFolderInput)}
              className="gap-1"
            >
              <Plus size={16} />
              New Folder
            </Button>
          </div>

          {showNewFolderInput && (
            <div className="mt-3 flex items-center gap-2">
              <Input
                placeholder="Folder name"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                autoFocus
              />
              <Button onClick={createFolder}>Create</Button>
              <Button variant="ghost" onClick={() => setShowNewFolderInput(false)}>
                Cancel
              </Button>
            </div>
          )}

          <Tabs value={activeTab} onValueChange={handleTabChange} className="mt-4">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="image">Images</TabsTrigger>
              <TabsTrigger value="video">Videos</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Folder navigation */}
        {(currentFolder || folderPath.length > 0) && (
          <div className="px-4 py-2 border-b flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={navigateBack} className="p-1">
              <ChevronLeft size={16} />
            </Button>

            <div className="flex items-center gap-1 text-sm">
              <Button
                variant="ghost"
                size="sm"
                className="p-1"
                onClick={() => {
                  setCurrentFolder(null);
                  setFolderPath([]);
                }}
              >
                Root
              </Button>

              {folderPath.map((folder, index) => (
                <React.Fragment key={folder.folder_id}>
                  <span>/</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1"
                    onClick={() => {
                      const newPath = folderPath.slice(0, index + 1);
                      setFolderPath(newPath);
                      setCurrentFolder(folder.folder_id);
                    }}
                  >
                    {folder.folder_name}
                  </Button>
                </React.Fragment>
              ))}
            </div>
          </div>
        )}

        {/* Content area */}
        <div className="flex-1 overflow-y-auto p-4" onScroll={handleScroll}>
          {/* Folders */}
          {folders.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium mb-3">Folders</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {folders.map(folder => (
                  <div
                    key={folder.folder_id}
                    className="flex flex-col items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-800"
                    onClick={() => navigateToFolder(folder)}
                  >
                    <Folder className="h-12 w-12 text-yellow-500 mb-2" />
                    <span className="text-sm text-center truncate w-full">{folder.folder_name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Media items */}
          <div>
            <h3 className="text-sm font-medium mb-3">Media</h3>
            {isLoading && mediaItems.length === 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {Array.from({ length: 10 }).map((_, i) => (
                  <div key={i} className="flex flex-col">
                    <Skeleton className="aspect-square w-full rounded-md" />
                    <Skeleton className="h-5 w-3/4 mt-2" />
                  </div>
                ))}
              </div>
            ) : mediaItems.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">No media found</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {mediaItems.map(item => renderMediaItem(item))}
              </div>
            )}

            {isLoading && mediaItems.length > 0 && (
              <div className="flex justify-center mt-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 dark:border-white"></div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AnimatedModal>
  );
};