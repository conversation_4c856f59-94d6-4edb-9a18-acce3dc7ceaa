interface TagData {
  id?: string;
  value: string;
  text: string;
  avatar?: string;
  type: 'mention' | 'hashtag';
  prefix: '@' | '#';
}

export function formatPostContent(raw: string): string {
  if (!raw) return '';

  // 1) strip zero-width & NBSP characters
  let str = raw.replace(/[\u200B\u00A0]/g, '');

  // 2) swap out every @{…}# or #{…}# for its clean text
  str = str.replace(/([@#])\{([\s\S]*?)\}#/g, (_match, prefix, json) => {
    try {
      const tagData: TagData = JSON.parse(json);
      if (tagData.type === 'mention') {
        return `@${tagData.value.toLowerCase()}`;
      } else {
        return `#${tagData.value}`;
      }
    } catch {
      // if parsing fails, leave the blob untouched
      return _match;
    }
  });

  // 3) Replace <br> tags with newlines
  str = str.replace(/<br\s*\/?>/g, '\n');

  return str;
}

