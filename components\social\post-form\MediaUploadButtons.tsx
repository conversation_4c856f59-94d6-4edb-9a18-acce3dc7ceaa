import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Image as ImageIcon, Video, Smartphone, Mic, Radio } from 'lucide-react';
import { HiOutlineGif } from "react-icons/hi2";
import Tippy from '@tippyjs/react';

interface MediaUploadButtonsProps {
  isUploading: boolean;
  onTriggerFileInput: (inputType: string) => void;
  setIsChildPopoverOpen: (value: boolean) => void;
  setIsFocused: (value: boolean) => void;
}

export const MediaUploadButtons: React.FC<MediaUploadButtonsProps> = ({
  isUploading,
  onTriggerFileInput,
  setIsChildPopoverOpen,
  setIsFocused,
}) => {
  const renderUploadButton = (icon: React.ReactNode, label: string, inputType: string, options?: { hint?: string }) => (
    <Popover
      onOpenChange={(open) => {
        setIsChildPopoverOpen(open);
        if (open) setIsFocused(true);
      }}
    >
      <Tippy
        content={
          <div>
            <div>{`Upload ${label}`}</div>
            {options?.hint && <div className="text-xs opacity-75">{options.hint}</div>}
          </div>
        }
        animation="shift-toward-subtle"
        placement="top"
        arrow={true}
        theme="sugar"
      >
        <PopoverTrigger asChild>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="h-10 w-10 rounded-lg text-turquoise hover:bg-gray-100 dark:hover:bg-zinc-800 transition-transform hover:scale-105"
            disabled={isUploading}
          >
            {icon}
          </Button>
        </PopoverTrigger>
      </Tippy>
      <PopoverContent
        className="z-50 bg-white dark:bg-zinc-700 rounded-lg shadow-lg p-2 border border-gray-200 dark:border-gray-700 w-full"
        side="top"
        align="center"
      >
        <div className="flex flex-col w-full">
          <Button
            variant="ghost"
            className="justify-start text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 text-sm py-1 px-2"
            onClick={() => onTriggerFileInput(inputType)}
          >
            Upload from Device
          </Button>
          <Button
            variant="ghost"
            className="justify-start text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 text-sm py-1 px-2 opacity-50 cursor-not-allowed"
            disabled
          >
            Upload from Media Library
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );

  return (
    <div className="flex items-center space-x-3">
      {renderUploadButton(<ImageIcon className="h-6 w-6" />, "Image", "image")}
      {renderUploadButton(<Video className="h-6 w-6" />, "Video", "video")}
      {renderUploadButton(
        <Smartphone className="h-6 w-6" />,
        "Clip",
        "clip",
        { hint: "Vertical video (9:16) • 3-60s" }
      )}
      {renderUploadButton(<Mic className="h-6 w-6" />, "Audio", "audio")}
      {renderUploadButton(<HiOutlineGif className="h-6 w-6" />, "GIF", "gif")}

      <Tippy content="Go Live" animation="shift-toward-subtle" placement="top" arrow={true} theme="sugar">
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-lg text-turquoise hover:bg-gray-100 dark:hover:bg-zinc-800 transition-transform hover:scale-105"
          onClick={() => setIsFocused(true)}
        >
          <Radio className="h-6 w-6" />
        </Button>
      </Tippy>
    </div>
  );
};