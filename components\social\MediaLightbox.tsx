'use client';

import React, { useState, useRef, useEffect } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { Share2, Bookmark, X, Eye, ChevronLeft, ChevronRight } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { elFormatter } from '@/public/main';
import { Post, Comment } from '@/types/post';
import { Skeleton } from "@/components/ui/skeleton";
import Tippy from '@tippyjs/react';
import { FaRegComment } from "react-icons/fa";
import { AiOutlineDollarCircle } from "react-icons/ai";
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';
import { useSocket } from '@/context/SocketContext';
import { UserHoverCard } from '@/components/ui/hover-card';
import TipUserDialog from '@/components/dialogs/TipUserDialog';
import CommentForm from '@/components/social/LightboxCommentForm';
import CommentCard from '@/components/social/CommentCard';
import { useUser } from '@/hooks/useUser';
import { useIsCurrentUser } from '@/hooks/useIsCurrentUser';
import { useLikePost, useSavePost, useCreateComment, useDeletePost } from '@/hooks/convexHooks';

interface MediaLightboxProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  post: Post;
  mediaArray: any[] | undefined;
  initialIndex: number;
  onPostUpdated?: (postId: string, updatedPost: Post) => void;
  onPostDeleted?: () => void;
}

const MediaLightbox: React.FC<MediaLightboxProps> = ({ open, onOpenChange, post, mediaArray = [], initialIndex, onPostUpdated, onPostDeleted }) => {
  const { isAuthenticated, token: session } = useUser();
  const isLoggedIn = isAuthenticated;
  const isCurrentUser = useIsCurrentUser(post?.user?.id);
  const { socket, isConnected, joinPostRoom, leavePostRoom } = useSocket();
  const [isLiking, setIsLiking] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isTipDialogOpen, setIsTipDialogOpen] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isMediaLoading, setIsMediaLoading] = useState(true);
  const [mediaAspectRatio, setMediaAspectRatio] = useState<number | null>(null);
  const mediaRef = useRef<HTMLImageElement | HTMLVideoElement | null>(null);
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [showComments, setShowComments] = useState(false);
  const [tipCount, setTipCount] = useState(0);
  const [isLiked, setIsLiked] = useState(post.liked || false);
  const [localLikeCount, setLocalLikeCount] = useState(post.likes || 0);
  const [localSaved, setLocalSaved] = useState(post.saved);
  const [localSavedCount, setLocalSavedCount] = useState(post.saves || 0);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [gifThumbUrl, setGifThumbUrl] = useState<string | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Convex mutations (only those actually used)
  const likePostMutation = useLikePost();
  const savePostMutation = useSavePost();
  const createCommentMutation = useCreateComment();
  const deletePostMutation = useDeletePost();

  const handleLike = async () => {
    if (isLiking) return;
    setIsLiking(true);

    if (!isLoggedIn) {
      toast.error('You must be logged in to perform this action!');
      return;
    }

    try {
      const result = await likePostMutation({ contentId: post.id, contentType: post.contentType || 'post' });
      setIsLiked(result.liked);
      setLocalLikeCount(result.likes);
    } catch (error) {
      console.error('Error liking post:', error);
      toast.error('Failed to like post');
    } finally {
      setIsLiking(false);
    }
  };

  const handleSave = async () => {
    if (!isLoggedIn) {
      toast.error('You must be logged in to perform this action!');
      return;
    }

    setIsSaving(true);
    try {
      const result = await savePostMutation({ contentId: post.id, contentType: post.contentType || 'post' });
      setLocalSaved(result.saved);
      setLocalSavedCount(result.saves);
    } catch (err: any) {
      toast.error(err.message || 'Failed to save post');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCommentClick = () => {
    setShowComments(!showComments);
  };

  const handleNewComment = (newComment: Comment) => {
    setComments([newComment, ...comments]);
  };

  const handleShare = async () => {
    setIsShareDialogOpen(true);
  };

  const handleDelete = async () => {
    const confirmed = await Swal.fire({
      title: 'Delete Post',
      text: 'Are you sure you want to delete this post? This action cannot be undone.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: 'var(--turquoise)',
      cancelButtonColor: '#6e6e6e',
      confirmButtonText: 'Yes, delete it!'
    });

    if (confirmed.isConfirmed) {
      if (isCurrentUser) {
        setIsDeleting(true);
        try {
          await deletePostMutation({ contentId: post.id });
          onPostDeleted?.();
          toast.success('Post deleted successfully');
        } catch (error) {
          toast.error('Failed to delete post');
          console.error('Error deleting post:', error);
          setIsDeleting(false);
        }
      } else {
        toast.error('You must be the creator of this post to delete it!');
      }
    }
  };

  const handleReport = () => {
    setIsReportDialogOpen(true);
  };

  const formatTimestamp = (timestamp: string) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '';

    const diff = Math.floor((currentTime - date.getTime()) / 1000);

    if (diff < 60) return 'just now';
    if (diff < 3600) {
      const minutes = Math.floor(diff / 60);
      return `${minutes}m`;
    }
    if (diff < 86400) {
      const hours = Math.floor(diff / 3600);
      return `${hours}h`;
    }
    if (diff < 604800) {
      const days = Math.floor(diff / 86400);
      return `${days}d`;
    }
    if (diff < 2628000) {
      const weeks = Math.floor(diff / 604800);
      return `${weeks}w`;
    }
    if (diff < 31536000) {
      const months = Math.floor(diff / 2628000);
      return `${months}mo`;
    }
    const years = Math.floor(diff / 31536000);
    return `${years}y`;
  };

  // Function to replace hashtags and mentions with links
  const renderContentWithLinks = (content: string) => {
    if (!content) return null;

    // First, normalize line breaks and remove extra spaces
    let processedContent = content
      .replace(/\r?\n/g, '\n')
      .replace(/[ \t]+/g, ' ')
      .trim();

    // Escape HTML to prevent XSS
    const escapedContent = processedContent.replace(/[&<>"']/g, char => ({
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
    })[char] || char);

    // Split content into parts to handle hashtags and mentions
    const parts = escapedContent.split(/(\s+)/);
    const elements: React.ReactNode[] = [];

    parts.forEach((part, index) => {
      // Handle hashtags
      if (part.match(/^#[\w\u0590-\u05ff]+$/)) {
        const hashtag = part.slice(1);
        elements.push(
          <a
            key={`hashtag-${index}`}
            href={`/hashtag/${hashtag}`}
            className="text-turquoise hover:underline inline-flex"
          >
            {part}
          </a>
        );
      } else if (part.match(/^@[\w]+$/)) {
        const username = part.slice(1);
        elements.push(
          post?.mentions?.find((mention: any) => mention.username === username) ? (
            <UserHoverCard
              key={`mention-${index}`}
              username={username}
              userData={post.mentions.find((mention: any) => mention.username === username)}
            >
              <a
                href={`/user/${username}`}
                className="text-turquoise hover:underline inline-flex"
              >
                {part}
              </a>
            </UserHoverCard>
          ) : (
            <a
              key={`mention-${index}`}
              href={`/user/${username}`}
              className="text-turquoise hover:underline inline-flex"
            >
              {part}
            </a>
          )
        );
      } else {
        elements.push(part);
      }
    });

    return (
      <div className="mb-4 whitespace-pre-wrap break-words text-gray-600 dark:text-white">
        {elements}
      </div>
    );
  };

  const handlePostUpdate = (updatedPost: any) => {
    onPostUpdated?.(post.id, updatedPost);
  };

  const handleTip = () => {
    setIsTipDialogOpen(true);
  };

  // Calculate the aspect ratio of the media when it loads, with safety checks
  useEffect(() => {
    if (!mediaArray || mediaArray.length === 0 || !mediaArray[currentIndex]) {
      setMediaAspectRatio(null);
      setIsMediaLoading(false);
      return;
    }

    if (mediaArray[currentIndex]?.mediaType?.includes('image') && mediaRef.current instanceof HTMLImageElement) {
      const img = mediaRef.current;
      const handleLoad = () => {
        const aspectRatio = img.naturalWidth / img.naturalHeight;
        setMediaAspectRatio(aspectRatio);
      };
      if (img.complete) {
        handleLoad();
      } else {
        img.addEventListener('load', handleLoad);
        return () => img.removeEventListener('load', handleLoad);
      }
    } else if (mediaArray[currentIndex]?.mediaType?.includes('video') && mediaRef.current instanceof HTMLVideoElement) {
      const video = mediaRef.current;
      const handleLoad = () => {
        const aspectRatio = video.videoWidth / video.videoHeight;
        setMediaAspectRatio(aspectRatio);
      };
      if (video.readyState >= 2) {
        handleLoad();
      } else {
        video.addEventListener('loadedmetadata', handleLoad);
        return () => video.removeEventListener('loadedmetadata', handleLoad);
      }
    }
  }, [mediaArray, currentIndex]);

  // Update currentIndex when mediaArray or initialIndex changes
  useEffect(() => {
    if (mediaArray && mediaArray.length > 0) {
      // Ensure initialIndex is within bounds
      const validInitialIndex = Math.max(0, Math.min(initialIndex, mediaArray.length - 1));
      setCurrentIndex(validInitialIndex);

      // Pre-load the current media item
      if (mediaArray[validInitialIndex]?.mediaType?.includes('image')) {
        const img = new Image();
        img.src = mediaArray[validInitialIndex].mediaUrl;
      }
    } else {
      setCurrentIndex(0);
    }

    // Reset loading state for the new media
    setIsMediaLoading(true);
  }, [mediaArray, initialIndex]);

  const handleNext = () => {
    if (!mediaArray || mediaArray.length <= 1) return;
    setIsMediaLoading(true);
    setCurrentIndex((prev) => (prev + 1) % mediaArray.length);

    // Pre-load the next image if it's an image
    const nextIndex = (currentIndex + 1) % mediaArray.length;
    if (mediaArray[nextIndex]?.mediaType?.includes('image')) {
      const img = new Image();
      img.src = mediaArray[nextIndex].mediaUrl;
    }
  };

  const handlePrev = () => {
    if (!mediaArray || mediaArray.length <= 1) return;
    setIsMediaLoading(true);
    setCurrentIndex((prev) => (prev - 1 + mediaArray.length) % mediaArray.length);

    // Pre-load the previous image if it's an image
    const prevIndex = (currentIndex - 1 + mediaArray.length) % mediaArray.length;
    if (mediaArray[prevIndex]?.mediaType?.includes('image')) {
      const img = new Image();
      img.src = mediaArray[prevIndex].mediaUrl;
    }
  };

  // Add this useEffect to handle GIF thumbnails
  useEffect(() => {
    if (!mediaArray?.[currentIndex]?.mediaType?.includes('gif')) {
      setGifThumbUrl(null);
      return;
    }

    const generateThumbnail = async () => {
      try {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.src = mediaArray[currentIndex].mediaUrl;

        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = () => reject(new Error('Failed to load GIF'));
        });

        // Use createImageBitmap to get the first frame
        const bitmap = await createImageBitmap(img, { imageOrientation: 'none' });
        const width = bitmap.width;
        const height = bitmap.height;

        if (canvasRef.current) {
          const canvas = canvasRef.current;
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            console.error('Failed to get canvas context');
            setGifThumbUrl(mediaArray[currentIndex].mediaUrl); // Fallback
            return;
          }

          ctx.drawImage(bitmap, 0, 0, width, height);
          const dataUrl = canvas.toDataURL('image/png');
          console.log('GIF thumbnail generated:', { dataUrl });
          setGifThumbUrl(dataUrl);
          bitmap.close();
        } else {
          console.error('Canvas ref not available');
          setGifThumbUrl(mediaArray[currentIndex].mediaUrl); // Fallback
        }
      } catch (error) {
        console.error('Error generating GIF thumbnail:', error);
        setGifThumbUrl(mediaArray[currentIndex].mediaUrl); // Fallback to original URL
      }
    };

    generateThumbnail();
  }, [mediaArray, currentIndex]);

  return (
    <>
      <AnimatedModal
        open={open}
        onOpenChange={onOpenChange}
        size="9xl"
        closeButton={false}
        header={null}
        footer={null}
      >
        <div className="bg-white dark:bg-zinc-900 text-black dark:text-white rounded-lg overflow-hidden">
          <div className="relative">
            <button
              onClick={() => onOpenChange(false)}
              className="absolute top-4 right-4 z-50 w-8 h-8 flex items-center justify-center rounded-full bg-black/50 text-white"
            >
              <X size={18} />
            </button>

            <div className="flex flex-col md:flex-row h-[80vh]">
              {/* Media Display */}
              <div className="flex-1 relative flex items-center justify-center overflow-hidden">
                <canvas ref={canvasRef} style={{ display: 'none' }} />
                {(mediaArray && mediaArray[currentIndex]?.mediaType?.includes('image') && !mediaArray[currentIndex]?.mediaType?.includes('gif')) && (
                  <img
                    src={mediaArray[currentIndex]?.mediaUrl}
                    alt="Blurred background"
                    className="absolute inset-0 w-full h-full object-cover filter blur-lg scale-110"
                    style={{ zIndex: 1 }}
                  />
                )}
                {(mediaArray && mediaArray[currentIndex]?.mediaType?.includes('video')) && (
                  <video
                    src={mediaArray[currentIndex]?.mediaUrl}
                    className="absolute inset-0 w-full h-full object-cover filter blur-lg scale-110"
                    muted
                    style={{ zIndex: 1 }}
                  />
                )}
                {(mediaArray && mediaArray[currentIndex]?.mediaType?.includes('gif')) && (
                  <img
                    src={gifThumbUrl || mediaArray[currentIndex]?.mediaUrl}
                    alt="Blurred GIF background"
                    className="absolute inset-0 w-full h-full object-cover filter blur-lg scale-110"
                    style={{ zIndex: 1 }}
                  />
                )}
                {(mediaArray && mediaArray[currentIndex]?.mediaType?.includes('gif')) && (
                  <>
                    {/* Top Left Ad */}
                    <div className="absolute top-4 left-4 w-[300px] h-[250px] bg-turquoise rounded-lg flex items-center justify-center z-10">
                      <span className="text-white">Ad Space (300x250)</span>
                    </div>
                    {/* Top Right Ad */}
                    <div className="absolute top-4 right-4 w-[300px] h-[250px] bg-turquoise rounded-lg flex items-center justify-center z-10">
                      <span className="text-white">Ad Space (300x250)</span>
                    </div>
                    {/* Bottom Left Ad */}
                    <div className="absolute bottom-4 left-4 w-[300px] h-[250px] bg-turquoise rounded-lg flex items-center justify-center z-10">
                      <span className="text-white">Ad Space (300x250)</span>
                    </div>
                    {/* Bottom Right Ad */}
                    <div className="absolute bottom-4 right-4 w-[300px] h-[250px] bg-turquoise rounded-lg flex items-center justify-center z-10">
                      <span className="text-white">Ad Space (300x250)</span>
                    </div>
                  </>
                )}

                {/* Navigation Buttons */}
                {mediaArray && mediaArray.length > 1 && (
                  <>
                    <button
                      onClick={handlePrev}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 w-10 h-10 flex items-center justify-center rounded-full bg-black/50 text-white hover:bg-black/70"
                    >
                      <ChevronLeft size={24} />
                    </button>
                    <button
                      onClick={handleNext}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 w-10 h-10 flex items-center justify-center rounded-full bg-black/50 text-white hover:bg-black/70"
                    >
                      <ChevronRight size={24} />
                    </button>
                  </>
                )}

                {/* Image Count Banner */}
                {mediaArray && mediaArray.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/60 text-white px-3 py-1 rounded z-20">
                    {currentIndex + 1} of {mediaArray.length}
                  </div>
                )}

                {/* Main Media */}
                <div
                  className="relative flex items-center justify-center w-full h-full"
                  style={{
                    aspectRatio: mediaAspectRatio || undefined,
                    maxHeight: '100%',
                    maxWidth: '100%',
                    zIndex: 2,
                  }}
                >
                  {(mediaArray && mediaArray[currentIndex]?.mediaType?.includes('image')) && (
                    <Skeleton loading={isMediaLoading} width="100%" height="100%">
                      <img
                        ref={mediaRef as React.RefObject<HTMLImageElement>}
                        src={mediaArray[currentIndex]?.mediaUrl}
                        alt="Post media"
                        className="w-full h-full object-contain"
                        onLoad={() => setIsMediaLoading(false)}
                        onError={() => setIsMediaLoading(false)}
                      />
                    </Skeleton>
                  )}
                  {(mediaArray && mediaArray[currentIndex]?.mediaType?.includes('video')) && (
                    <Skeleton loading={isMediaLoading} width="100%" height="100%">
                      <video
                        ref={mediaRef as React.RefObject<HTMLVideoElement>}
                        src={mediaArray[currentIndex]?.mediaUrl}
                        autoPlay
                        loop
                        controls
                        className="w-full h-full object-contain"
                        onLoadedMetadata={() => setIsMediaLoading(false)}
                        onError={() => setIsMediaLoading(false)}
                      />
                    </Skeleton>
                  )}
                </div>
              </div>

              {/* Comments and Information Section */}
              <div className="w-full md:w-[456px] flex flex-col h-full overflow-y-auto">
                {/* Post Header */}
                <div className="p-4 border-b dark:border-zinc-700 overflow-y-auto">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src={post?.user?.profileImage} alt={post?.user?.username} />
                      <AvatarFallback>{post?.user?.username?.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold">{post?.user?.username}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {post?.createdAt ? formatDistanceToNow(new Date(post.createdAt), { addSuffix: true }) : 'Unknown time'}
                      </p>
                    </div>
                  </div>
                  <p className="mt-3">
                    {post?.content
                      ? renderContentWithLinks(post.content)
                      : null}
                  </p>

                  {/* Post Actions */}
                  <div className="flex justify-between w-full mt-3 mb-3">
                    <Tippy content="Views" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled={isDeleting}
                        className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
                      >
                        <Eye className="mr-1 h-5 w-5 relative" />
                        <span className="relative top-[1px]">{elFormatter(post.views) || 0}</span>
                      </Button>
                    </Tippy>

                    <Tippy content="Like" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleLike}
                        disabled={isLiking || isDeleting}
                        className={`relative group dark:hover:bg-gray-700 ${isLiked ? "text-hot-pink hover:text-hot-pink dark:text-hot-pink dark:hover:text-hot-pink" : "text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise"} heart-container-parent`}
                      >
                        <div className="heart-container">
                          <input
                            type="checkbox"
                            className="checkbox"
                            checked={isLiked}
                            onChange={() => { }}
                          />

                          <div className="svg-container">
                            <svg viewBox="0 0 24 24" className="svg-outline w-5 h-5">
                              <path
                                fill="none"
                                stroke="currentColor"
                                strokeWidth={isLiked ? '0' : '2'}
                                d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09 C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5 c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
                              />
                            </svg>

                            <svg viewBox="0 0 24 24" className="svg-filled w-5 h-5">
                              <path
                                d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09 C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5 c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
                              />
                            </svg>

                            <svg viewBox="0 0 24 24" className="svg-celebrate w-5 h-5">
                              <path
                                d="M12,21.35l-1.45-1.32C5.4,15.36 2,12.28 2,8.5 2,5.42 4.42,3 7.5,3c1.74,0 3.41,0.81 4.5,2.09 C13.09,3.81 14.76,3 16.5,3 19.58,3 22,5.42 22,8.5 c0,3.78-3.4,6.86-8.55,11.54L12,21.35z"
                              />
                            </svg>
                          </div>
                        </div>
                        <span className="ml-1 relative top-[1px]">{elFormatter(localLikeCount)}</span>
                      </Button>
                    </Tippy>

                    <Tippy content="Comment" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCommentClick}
                        disabled={isDeleting}
                        className={showComments ? "text-turquoise hover:text-turquoise dark:bg-gray-700" : "text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"}
                      >
                        <FaRegComment className="mr-1 h-[19px] w-[19px]" />
                        <span className="relative top-[1px]">{elFormatter(comments.length) || elFormatter(post.comments)}</span>
                      </Button>
                    </Tippy>

                    <Tippy content="Send Tip" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleTip}
                        className={"text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"}
                      >
                        <AiOutlineDollarCircle className="mr-1 h-[22px] w-[22px]" />
                        <span className="relative top-[1px]">{elFormatter(tipCount)}</span>
                      </Button>
                    </Tippy>

                    <Tippy content="Save" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleSave}
                        disabled={isDeleting || isSaving}
                        className={localSaved ? "text-turquoise hover:text-turquoise dark:hover:bg-gray-700" : "text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"}
                      >
                        <label className="ui-bookmark">
                          <input
                            type="checkbox"
                            className="checkbox"
                            checked={localSaved}
                            onChange={() => { }}
                            disabled={isSaving}
                          />
                          <div className="bookmark">
                            <Bookmark className={`h-5 w-5 mr-1 ${localSaved ? 'fill-[var(--turquoise)] dark:fill-[var(--turquoise)] text-turquoise' : ''}`} />
                            <span className="relative top-[1px]">{elFormatter(localSavedCount) || 0}</span>
                          </div>
                        </label>
                      </Button>
                    </Tippy>

                    <Tippy content="Share" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled={isDeleting}
                        className="text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise dark:hover:bg-gray-700"
                        onClick={handleShare}
                      >
                        <Share2 className="mr-1 h-5 w-5" />
                      </Button>
                    </Tippy>
                  </div>

                  {/* Comment Form */}
                  <CommentForm
                    postId={post.id}
                    onCommentAdded={handleNewComment}
                  />

                </div>

                {/* Comments List */}
                <ScrollArea className="flex-grow p-4 overflow-y-auto">
                  {comments.length > 0 && (
                    comments.map((comment) => (
                      <CommentCard
                        key={comment.id}
                        comment={comment}
                      />
                    ))
                  )}
                </ScrollArea>
              </div>
            </div>
          </div>
        </div>
      </AnimatedModal>

      <TipUserDialog
        isOpen={isTipDialogOpen}
        onOpenChange={setIsTipDialogOpen}
        userData={post.user}
        onTipSuccess={() => {
          toast.success('Tip sent successfully!');
          setIsTipDialogOpen(false);
        }}
      />

    </>
  );
};
export default MediaLightbox;