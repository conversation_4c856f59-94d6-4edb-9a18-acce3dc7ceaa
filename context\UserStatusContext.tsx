import React, { createContext, useContext, useCallback } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';

interface UserStatusContextValue {
  updateUserStatus: (userId: string, status: string) => Promise<{ success: boolean; data?: any; error?: any }>;
}

const UserStatusContext = createContext<UserStatusContextValue | undefined>(undefined);

export const UserStatusProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const updateUserStatusMutation = useMutation(api.accounts.updateUserStatus);

  const updateUserStatus = useCallback(
    async (userId: string, status: string) => {
      try {
        const result = await updateUserStatusMutation({ user_id: userId, status });
        return { success: !!result?.success };
      } catch (error) {
        console.error('Error updating user status:', error);
        return { success: false, error };
      }
    },
    [updateUserStatusMutation]
  );

  return (
    <UserStatusContext.Provider value={{ updateUserStatus }}>
      {children}
    </UserStatusContext.Provider>
  );
};

export function useUserStatus() {
  const ctx = useContext(UserStatusContext);
  if (!ctx) throw new Error('useUserStatus must be used within a UserStatusProvider');
  return ctx;
}