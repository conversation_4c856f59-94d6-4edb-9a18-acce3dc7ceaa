import { jsx as o, jsxs as e } from "react/jsx-runtime";
import { memo as t } from "react";
const i = (r) => /* @__PURE__ */ o(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 16 16",
    fill: "none",
    ...r,
    children: /* @__PURE__ */ e("g", { id: "magnifer-svgrepo-com", children: [
      /* @__PURE__ */ o(
        "circle",
        {
          id: "Oval",
          cx: 7.65,
          cy: 7.65,
          r: 6.65,
          stroke: "currentColor",
          strokeWidth: 1.86667
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          id: "Path",
          d: "M12.5498 12.55L14.9998 15",
          stroke: "currentColor",
          strokeWidth: 1.86667,
          strokeLinecap: "round"
        }
      )
    ] })
  }
), m = t(i);
export {
  m as default
};
