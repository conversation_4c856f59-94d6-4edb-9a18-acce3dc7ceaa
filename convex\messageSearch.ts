import { query } from './_generated/server';
import { v } from 'convex/values';

// Helper: case-insensitive substring match
function containsInsensitive(haystack: string, needle: string) {
  return haystack.toLowerCase().includes(needle.toLowerCase());
}

interface Message {
  _id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  created_at: string;
  is_deleted?: boolean;
}

interface UserInfo {
  user_id: string;
  username?: string;
  profilePhoto?: string;
  wallet_address?: string;
}

export const searchMessages = query({
  args: {
    query: v.string(),
    type: v.optional(v.string()), // 'direct' only
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) return { success: false, message: 'Unauthorized' };
    const userId = user.subject;
    const search = args.query.trim().toLowerCase();
    const limit = args.limit ?? 30;
    const cursor = args.cursor ?? null;

    // Only direct messages supported
    const result = await ctx.db
      .query('Messages')
      .filter(q =>
        q.or(
          q.eq(q.field('sender_id'), userId),
          q.eq(q.field('receiver_id'), userId)
        )
      )
      .filter(q => q.neq(q.field('is_deleted'), true))
      .order('desc')
      .paginate({ numItems: limit, cursor });

    // Filter by content in memory (Convex can't do substring search)
    const filtered: Message[] = result.page.filter((m: Message) =>
      containsInsensitive(m.content, search)
    );

    // Get unique user IDs
    const userIds = [
      ...new Set([
        ...filtered.map(m => m.sender_id),
        ...filtered.map(m => m.receiver_id),
      ]),
    ];

    // Fetch user info
    const users: UserInfo[] = await Promise.all(
      userIds.map(async id => {
        const acc = await ctx.db
          .query('Accounts')
          .filter(q => q.eq(q.field('user_id'), id))
          .first();
        return acc
          ? {
            user_id: acc.user_id,
            username: acc.user_info?.account?.username,
            profilePhoto: acc.user_info?.profilePhoto,
            wallet_address: acc.wallet_address,
          }
          : { user_id: id };
      })
    );
    const userInfoMap = new Map(users.map(u => [u.user_id, u]));

    // Enhance messages
    const enhanced = filtered.map(m => ({
      ...m,
      sender: userInfoMap.get(m.sender_id) || { user_id: m.sender_id },
      receiver: userInfoMap.get(m.receiver_id) || { user_id: m.receiver_id },
    }));

    // Group by conversation partner, latest message per partner
    const conversationMap = new Map<string, any>();
    enhanced.forEach(m => {
      const partnerId = m.sender_id === userId ? m.receiver_id : m.sender_id;
      if (
        !conversationMap.has(partnerId) ||
        new Date(m.created_at) > new Date(conversationMap.get(partnerId).created_at)
      ) {
        conversationMap.set(partnerId, m);
      }
    });

    return {
      conversations: Array.from(conversationMap.values()),
      cursor: result.continueCursor,
      isDone: result.isDone,
    };
  },
});