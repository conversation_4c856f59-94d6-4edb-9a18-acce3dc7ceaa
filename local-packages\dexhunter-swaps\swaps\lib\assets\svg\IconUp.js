import { jsx as C } from "react/jsx-runtime";
import { memo as t } from "react";
const e = (o) => /* @__PURE__ */ C(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 8 5",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M0.363602 3.3636C0.0121284 3.71507 0.0121279 4.28492 0.363601 4.6364C0.715074 4.98787 1.28492 4.98787 1.6364 4.6364L0.363602 3.3636ZM4.6364 1.6364C4.98787 1.28492 4.98787 0.715073 4.6364 0.3636C4.28492 0.0121274 3.71507 0.0121286 3.3636 0.363603L4.6364 1.6364ZM6.3636 4.6364C6.71507 4.98787 7.28493 4.98787 7.6364 4.6364C7.98787 4.28493 7.98787 3.71507 7.6364 3.3636L6.3636 4.6364ZM4.64103 0.368238C4.28956 0.0167644 3.71971 0.0167644 3.36824 0.368238C3.01676 0.719711 3.01676 1.28956 3.36824 1.64103L4.64103 0.368238ZM1 4C1.6364 4.6364 1.63641 4.63639 1.63642 4.63638C1.63644 4.63636 1.63646 4.63634 1.63649 4.63631C1.63655 4.63625 1.63664 4.63616 1.63676 4.63604C1.637 4.63579 1.63737 4.63543 1.63785 4.63495C1.63881 4.63399 1.64024 4.63255 1.64215 4.63065C1.64595 4.62685 1.6516 4.62119 1.65903 4.61377C1.67387 4.59893 1.69577 4.57703 1.724 4.5488C1.78046 4.49234 1.86224 4.41056 1.96353 4.30927C2.1661 4.1067 2.44669 3.82611 2.75874 3.51406C3.38286 2.88994 4.13286 2.13994 4.6364 1.6364L3.3636 0.363603C2.86006 0.867144 2.11006 1.61714 1.48595 2.24126C1.17389 2.55332 0.893303 2.8339 0.690731 3.03647C0.589445 3.13776 0.507663 3.21954 0.451202 3.276C0.422972 3.30423 0.401072 3.32613 0.386229 3.34097C0.378808 3.34839 0.373151 3.35405 0.369349 3.35785C0.367449 3.35975 0.366012 3.36119 0.36505 3.36215C0.36457 3.36263 0.364207 3.363 0.363966 3.36324C0.363844 3.36336 0.363754 3.36345 0.363693 3.36351C0.363663 3.36354 0.36364 3.36356 0.363625 3.36358C0.36361 3.36359 0.363602 3.3636 1 4ZM7.6364 3.3636L4.64103 0.368238L3.36824 1.64103L6.3636 4.6364L7.6364 3.3636Z",
        fill: "white"
      }
    )
  }
), L = t(e);
export {
  L as default
};
