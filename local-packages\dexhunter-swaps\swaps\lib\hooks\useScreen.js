import { useState as r, useEffect as a } from "react";
const l = () => {
  const [e, t] = r("");
  return a(() => {
    const i = () => {
      const o = navigator.userAgent.toLowerCase(), s = /iphone|ipad|ipod|android|blackberry|windows phone/g.test(o), n = /(ipad|tablet|playbook|silk)|(android(?!.*mobile))/g.test(o);
      t(s ? "mobile" : n ? "tablet" : "desktop");
    };
    return i(), window.addEventListener("resize", i), () => {
      window.removeEventListener("resize", i);
    };
  }, []), {
    isMobile: e === "mobile",
    isTablet: e === "tablet",
    isDesktop: e === "desktop"
  };
};
export {
  l as default
};
