'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  DialogRoot,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  <PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogCloseTrigger,
} from "@/components/ui/dialog";
import { toast } from 'react-toastify';
import React<PERSON><PERSON>, { Crop } from 'react-image-crop';
import StyledButton from '@/components/ui/styled-button';
import { Tabs } from "@/components/ui/tabs";
import AnimatedTabs from '@/components/ui/animated-tabs';
import { Input } from "@/components/ui/input";
import { isValidUrl } from '@/public/main';
import { ConvexHttpClient } from 'convex/browser';
import { api } from "@/convex/_generated/api";
import 'react-image-crop/dist/ReactCrop.css';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

interface CoverImageUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (mediaData: string, mediaType: 'image' | 'video' | 'url') => Promise<{ success: boolean; message: string } | undefined>;
  currentImage?: string;
}

const CoverImageUploadModal = ({ isOpen, onClose, onSave }: CoverImageUploadModalProps) => {
  const [activeTab, setActiveTab] = useState<'upload' | 'url'>('upload');
  const [selectedMedia, setSelectedMedia] = useState<string | null>(null);
  const [mediaType, setMediaType] = useState<'image' | 'video' | null>(null);
  const [urlInput, setUrlInput] = useState<string>('');
  const [isValidatingUrl, setIsValidatingUrl] = useState(false);
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 100,
    height: 30,
    x: 0,
    y: 0
  });
  const [completedCrop, setCompletedCrop] = useState<any>(null);
  const imgRef = useRef<HTMLImageElement | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const TABS = [
    { label: "Upload" },
    { label: "URL" },
  ];
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      const fileType = file.type.split('/')[0];

      // Check file size - 100MB limit (100 * 1024 * 1024 bytes)
      const MAX_FILE_SIZE = 200 * 1024 * 1024; // 100MB in bytes
      if (file.size > MAX_FILE_SIZE) {
        toast.error("File is too large. Maximum size is 100MB.");
        return;
      }

      if (fileType === 'image') {
        setMediaType('image');
        const reader = new FileReader();
        reader.addEventListener('load', () => {
          setSelectedMedia(reader.result as string);
        });
        reader.readAsDataURL(file);
      } else if (fileType === 'video') {
        setMediaType('video');
        const reader = new FileReader();
        reader.addEventListener('load', () => {
          setSelectedMedia(reader.result as string);
        });
        reader.readAsDataURL(file);
      } else {
        toast.error("Unsupported file type. Please upload an image or video.");
      }
    }
  };

  const validateUrl = async (url: string) => {
    setIsValidatingUrl(true);
    try {
      // First check if it's a valid URL format
      if (!isValidUrl(url)) {
        return { valid: false, message: "Invalid URL" };
      }

      // Use Convex action via HTTP client
      const data = await convex.action(api.media.validateMediaUrl, { url });

      if (data.valid) {
        setUrlInput(url);
        setMediaType(data.type as 'image' | 'video');
        return { valid: true, message: data.message };
      } else {
        return { valid: false, message: data.message };
      }
    } catch (error) {
      console.error("Error validating URL:", error);
      return { valid: false, message: "Invalid URL" };
    } finally {
      setIsValidatingUrl(false);
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    imgRef.current = e.currentTarget;

    // Set initial crop to be a wide rectangle
    const { width, height } = e.currentTarget;
    const cropHeight = height * 0.3; // 30% of image height
    const y = (height - cropHeight) / 2;

    const initialCrop = {
      unit: 'px',
      width: width,
      height: cropHeight,
      x: 0,
      y
    } as any;

    setCrop(initialCrop);
    setCompletedCrop(initialCrop); // Set initial completed crop
  };

  const getCroppedImg = () => {
    if (!completedCrop || !imgRef.current || !previewCanvasRef.current) return;

    const image = imgRef.current;
    const canvas = previewCanvasRef.current;
    const crop = completedCrop;

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    // Use the original dimensions from the cropped area
    const pixelCrop = {
      x: crop.x * scaleX,
      y: crop.y * scaleY,
      width: crop.width * scaleX,
      height: crop.height * scaleY
    };

    // Set canvas to the actual dimensions of the cropped area
    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Draw the cropped image at full resolution
    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height
    );

    // Return high quality JPEG
    return canvas.toDataURL('image/webp', 0.95);
  };

  const handleSave = async () => {
    setIsLoading(true);

    try {
      if (activeTab === 'upload' && mediaType === 'image' && selectedMedia) {
        const croppedImageData = getCroppedImg();
        if (croppedImageData) {
          const savedData = await onSave(croppedImageData, 'image');
          if (!savedData?.success) throw new Error(savedData?.message || 'Failed to save image');
          setIsLoading(false);
          onClose();
        }
      } else if (activeTab === 'upload' && mediaType === 'video' && selectedMedia) {
        const savedData = await onSave(selectedMedia, 'video');
        if (!savedData?.success) throw new Error(savedData?.message || 'Failed to save video');
        setIsLoading(false);
        onClose();
      } else if (activeTab === 'url' && urlInput) {
        const isValid = await validateUrl(urlInput);
        if (isValid.valid) {
          const savedData = await onSave(urlInput, 'url');
          console.log({ savedData });
          if (!savedData?.success) throw new Error(savedData?.message || 'Failed to save media URL');
          setIsLoading(false);
          onClose();
        } else {
          throw new Error(isValid.message || 'Invalid URL');
        }
      }
    } catch (error: any) {
      console.error('Error saving media:', error);
      toast.error(error.message || "Failed to save media");
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setSelectedMedia(null);
      setMediaType(null);
      setUrlInput('');
      setIsLoading(false);
      setActiveTab('upload');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <DialogRoot open={isOpen} onOpenChange={onClose} placement="center" motionPreset="slide-in-bottom">
      <DialogContent className="!max-w-4xl w-full bg-white/60 dark:bg-[#121212]/60 backdrop-blur border-2 border-solid border-white/20 p-2">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-[#121212] dark:text-white">
            Update Banner
          </DialogTitle>
          <DialogCloseTrigger />
        </DialogHeader>

        <DialogBody className="flex flex-col items-center gap-6 py-4">
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value as 'upload' | 'url')} className="w-full">

            <div className="w-full flex items-center justify-center gap-2 mb-3">
              <AnimatedTabs
                defaultValue={TABS[0].label}
                className={`rounded-lg bg-zinc-300 ${(isValidatingUrl || isLoading) ? 'pointer-events-none' : ''}`}
                transition={{
                  type: "spring",
                  bounce: 0.2,
                  duration: 0.3,
                }}
                onValueChange={(value) => {
                  setActiveTab(value?.toLowerCase() as 'upload' | 'url');
                }}
              >
                {TABS.map((tab, index) => (
                  <button
                    key={index}
                    data-id={tab.label}
                    type="button"
                    className={`inline-flex h-10 w-full items-center justify-center transition-colors duration-100 focus-visible:outline-2 data-[checked=true]:text-white dark:data-[checked=true]:text-[#121212]`}
                  >
                    {tab.label}
                  </button>
                ))}
              </AnimatedTabs>
            </div>

            {activeTab === 'upload' && (
              <>
                {selectedMedia ? (
                  <div className="flex flex-col items-center justify-center gap-4 w-full">
                    {mediaType === 'image' ? (
                      <ReactCrop
                        crop={crop}
                        onChange={(c) => setCrop(c)}
                        onComplete={(c) => setCompletedCrop(c)}
                        aspect={3.5}
                      >
                        <img
                          ref={imgRef}
                          src={selectedMedia}
                          alt="Upload"
                          className="!max-h-[400px] max-w-full w-full h-auto"
                          onLoad={onImageLoad}
                        />
                      </ReactCrop>
                    ) : (
                      <video
                        ref={videoRef}
                        src={selectedMedia}
                        controls
                        className="!max-h-[400px] max-w-full w-full h-auto"
                      />
                    )}

                    <canvas
                      ref={previewCanvasRef}
                      className="hidden"
                    />

                    <button
                      onClick={() => {
                        setSelectedMedia(null);
                        setMediaType(null);
                      }}
                      className="relative bg-red-500 hover:bg-red-600 text-white w-16 h-10 flex items-center justify-center rounded-lg transition-colors"
                      aria-label="Reset media"
                      disabled={isLoading}
                    >
                      Reset
                    </button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-4 w-full h-full">
                    <div
                      className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:bg-[#121212]/50 hover:bg-gray-100 dark:hover:bg-[#121212]/60"
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onDrop={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        const file = e.dataTransfer.files[0];
                        const fileType = file.type.split('/')[0];

                        // Check file size - 100MB limit
                        const MAX_FILE_SIZE = 200 * 1024 * 1024; // 100MB in bytes
                        if (file.size > MAX_FILE_SIZE) {
                          toast.error("File is too large. Maximum size is 200MB.");
                          return;
                        }
                        if (fileType === 'image' || fileType === 'video') {
                          setMediaType(fileType as 'image' | 'video');
                          const reader = new FileReader();
                          reader.addEventListener('load', () => {
                            setSelectedMedia(reader.result as string);
                          });
                          reader.readAsDataURL(file);
                        } else {
                          toast.error("Unsupported file type. Please upload an image or video.");
                        }
                      }}
                    >
                      <label className="flex flex-col items-center justify-center w-full h-full cursor-pointer">
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <svg className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                          </svg>
                          <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">Drag and drop or click to upload</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF or MP4, WebM</p>
                        </div>
                        <input type="file" className="hidden" onChange={handleFileChange} accept="image/*,video/*" />
                      </label>
                    </div>
                  </div>
                )}
              </>
            )}

            {activeTab === 'url' && (
              <div className="flex flex-col gap-4 w-full">
                <div className="flex gap-2 items-center">
                  <Input
                    type="text"
                    placeholder="Enter image or video URL"
                    value={urlInput}
                    onChange={(e) => {
                      setUrlInput(e.target.value);
                    }}
                    className="flex-1 h-10 w-full"
                  />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Enter a direct URL to <span className="underline">an image</span> (JPG, PNG, GIF, etc...) or <span className="underline">video</span> (MP4, WebM, etc...)
                </p>
              </div>
            )}
          </Tabs>
        </DialogBody>

        <DialogFooter className="flex !justify-start gap-4 mt-2 !mb-3 w-full !px-1">
          <StyledButton
            onClick={handleSave}
            disabled={
              (activeTab === 'upload' && (!selectedMedia || (mediaType === 'image' && !completedCrop))) ||
              (activeTab === 'url' && !urlInput) ||
              isLoading ||
              isValidatingUrl
            }
            buttonText={isLoading ? "Saving..." : "Save Changes"}
            className={`!text-sm w-40 h-10 flex items-center justify-center !p-0 ${isLoading ? 'hover' : ''}`}
          />

          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
          >
            Cancel
          </button>
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  );
};
export default CoverImageUploadModal;