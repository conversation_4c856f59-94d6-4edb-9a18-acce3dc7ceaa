'use client';

import { createContext, useContext, useEffect, useState, Dispatch, SetStateAction, Suspense } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import Loader from '@/components/Loader';

const RouteChangeContext = createContext<{ isRouteChanging: boolean; setIsRouteChanging: Dispatch<SetStateAction<boolean>> }>({
  isRouteChanging: false,
  setIsRouteChanging: () => {},
});

const RouteChangeProviderComponent = ({ children }: { children: React.ReactNode }) => {
  const [isRouteChanging, setIsRouteChanging] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Route change started
    setIsRouteChanging(true);

    // Simulate route change completion after a short delay
    const timer = setTimeout(() => {
      setIsRouteChanging(false);
    }, 300); // Adjust this delay as needed

    return () => clearTimeout(timer);
  }, [pathname, searchParams]); // This will trigger whenever the route changes

  return (
    <RouteChangeContext.Provider value={{ isRouteChanging, setIsRouteChanging }}>
      {children}
    </RouteChangeContext.Provider>
  );
};

export const RouteChangeProvider = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<Loader />}>
    <RouteChangeProviderComponent>{children}</RouteChangeProviderComponent>
  </Suspense>
);

export const useRouteChange = () => useContext(RouteChangeContext);