import { jsx as o } from "react/jsx-runtime";
import { c as n } from "../../index-1d6812f7.js";
import { cn as a } from "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const d = n(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        accent: "border-transparent bg-accent text-accent-foreground",
        gold: "border-transparent bg-gold text-gold-foreground",
        hot: "border-transparent bg-red-101 text-destructive-foreground"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);
function u({ className: r, variant: e, ...t }) {
  return /* @__PURE__ */ o("div", { className: a(d({ variant: e }), r), ...t });
}
export {
  u as Badge,
  d as badgeVariants
};
