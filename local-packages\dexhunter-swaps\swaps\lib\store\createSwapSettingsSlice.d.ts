export interface SwapSettingsSlice {
    isDexSplitting: boolean;
    localRouting: boolean;
    isCustomSlippage: boolean;
    inputcustomSlippage: string;
    inputTransactionDeadline: string;
    slippage: number;
    isOpenSwapSetting: boolean;
    isDetailsOpen: string;
    isPriceFlipped: boolean;
    isOpenSwapOverview: boolean;
    isAutomaticSlippage: boolean;
    setisDexSplitting: (isDexSplitting: boolean) => void;
    setLocalRouting: (localRouting: boolean) => void;
    setIsCustomSlippage: (isCustomSlippage: boolean) => void;
    setInputCustomSlippage: (inputcustomSlippage: string) => void;
    setInputTransactionDeadline: (inputTransactionDeadline: string) => void;
    setSlippage: (slippage: number) => void;
    setIsOpenSwapSetting: (isOpenSwapSetting: boolean) => void;
    setIsDetailsOpen: (isDetailsOpen: string) => void;
    setIsPriceFlipped: (isPriceFlipped: boolean) => void;
    setIsOpenSwapOverview: (isOpenSwapOverview: boolean) => void;
    setIsAutomaticSlippage: (isAutomaticSlippage: boolean) => void;
}
declare const createSwapSettingsSlice: (set: any) => {
    isDexSplitting: boolean;
    localRouting: boolean;
    isCustomSlippage: boolean;
    isAutomaticSlippage: boolean;
    inputcustomSlippage: string;
    inputTransactionDeadline: string;
    slippage: number;
    isOpenSwapSetting: boolean;
    isDetailsOpen: string;
    isPriceFlipped: boolean;
    isOpenSwapOverview: boolean;
    setisDexSplitting: (isDexSplitting: boolean) => void;
    setLocalRouting: (localRouting: boolean) => void;
    setIsCustomSlippage: (isCustomSlippage: boolean) => void;
    setInputCustomSlippage: (inputcustomSlippage: string) => void;
    setInputTransactionDeadline: (inputTransactionDeadline: string) => void;
    setSlippage: (slippage: number) => void;
    setIsOpenSwapSetting: (isOpenSwapSetting: boolean) => void;
    setIsDetailsOpen: (isDetailsOpen: string) => void;
    setIsPriceFlipped: (isPriceFlipped: boolean) => void;
    setIsOpenSwapOverview: (isOpenSwapOverview: boolean) => void;
    setIsAutomaticSlippage: (isAutomaticSlippage: boolean) => void;
};
export default createSwapSettingsSlice;
