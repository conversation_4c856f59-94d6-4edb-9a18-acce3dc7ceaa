'use client';

import { useEffect, useRef, useCallback, useState } from 'react';

interface PreloadOptions {
  priority?: 'high' | 'medium' | 'low';
  maxConcurrent?: number;
  timeout?: number;
  retryAttempts?: number;
}

interface PreloadItem {
  url: string;
  priority: 'high' | 'medium' | 'low';
  retryCount: number;
  timeout?: number;
}

class ImagePreloader {
  private static instance: ImagePreloader;
  private queue: PreloadItem[] = [];
  private loading = new Set<string>();
  private loaded = new Set<string>();
  private failed = new Set<string>();
  private maxConcurrent = 3;
  private isProcessing = false;

  static getInstance(): ImagePreloader {
    if (!ImagePreloader.instance) {
      ImagePreloader.instance = new ImagePreloader();
    }
    return ImagePreloader.instance;
  }

  private constructor() {
    // Start processing queue
    this.processQueue();
  }

  preload(
    urls: string | string[],
    options: PreloadOptions = {}
  ): Promise<void[]> {
    const urlArray = Array.isArray(urls) ? urls : [urls];
    const {
      priority = 'medium',
      timeout = 10000,
      retryAttempts = 2
    } = options;

    const promises = urlArray.map(url => {
      // Skip if already loaded or currently loading
      if (this.loaded.has(url) || this.loading.has(url)) {
        return Promise.resolve();
      }

      // Skip if failed too many times
      if (this.failed.has(url)) {
        return Promise.reject(new Error(`Image failed to load: ${url}`));
      }

      return new Promise<void>((resolve, reject) => {
        const item: PreloadItem = {
          url,
          priority,
          retryCount: 0,
          timeout
        };

        // Add to queue with priority sorting
        this.addToQueue(item, resolve, reject, retryAttempts);
      });
    });

    return Promise.all(promises);
  }

  private addToQueue(
    item: PreloadItem,
    resolve: () => void,
    reject: (error: Error) => void,
    maxRetries: number
  ) {
    // Find insertion point based on priority
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    let insertIndex = this.queue.length;

    for (let i = 0; i < this.queue.length; i++) {
      if (priorityOrder[item.priority] < priorityOrder[this.queue[i].priority]) {
        insertIndex = i;
        break;
      }
    }

    // Store resolve/reject callbacks
    const queueItem = {
      ...item,
      resolve,
      reject,
      maxRetries
    };

    this.queue.splice(insertIndex, 0, queueItem as any);
    this.processQueue();
  }

  private async processQueue() {
    if (this.isProcessing || this.loading.size >= this.maxConcurrent) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0 && this.loading.size < this.maxConcurrent) {
      const item = this.queue.shift();
      if (!item) break;

      this.loadImage(item as any);
    }

    this.isProcessing = false;
  }

  private async loadImage(item: PreloadItem & {
    resolve: () => void;
    reject: (error: Error) => void;
    maxRetries: number;
  }) {
    const { url, timeout = 10000, resolve, reject, maxRetries } = item;

    if (this.loaded.has(url)) {
      resolve();
      return;
    }

    this.loading.add(url);

    try {
      await this.createImagePromise(url, timeout);
      this.loaded.add(url);
      this.loading.delete(url);
      resolve();
    } catch (error) {
      this.loading.delete(url);

      if (item.retryCount < maxRetries) {
        item.retryCount++;
        // Add back to queue with delay
        setTimeout(() => {
          this.addToQueue(item, resolve, reject, maxRetries);
        }, Math.pow(2, item.retryCount) * 1000); // Exponential backoff
      } else {
        this.failed.add(url);
        reject(error as Error);
      }
    }

    // Continue processing queue
    this.processQueue();
  }

  private createImagePromise(url: string, timeout: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      let timeoutId: NodeJS.Timeout;

      const cleanup = () => {
        if (timeoutId) clearTimeout(timeoutId);
        img.onload = null;
        img.onerror = null;
      };

      img.onload = () => {
        cleanup();
        resolve();
      };

      img.onerror = () => {
        cleanup();
        reject(new Error(`Failed to load image: ${url}`));
      };

      // Set timeout
      timeoutId = setTimeout(() => {
        cleanup();
        reject(new Error(`Image load timeout: ${url}`));
      }, timeout);

      // Start loading
      img.src = url;
    });
  }

  isLoaded(url: string): boolean {
    return this.loaded.has(url);
  }

  isLoading(url: string): boolean {
    return this.loading.has(url);
  }

  hasFailed(url: string): boolean {
    return this.failed.has(url);
  }

  getStats() {
    return {
      loaded: this.loaded.size,
      loading: this.loading.size,
      failed: this.failed.size,
      queued: this.queue.length
    };
  }

  setMaxConcurrent(max: number) {
    this.maxConcurrent = Math.max(1, max);
  }

  clear() {
    this.queue = [];
    this.loading.clear();
    this.loaded.clear();
    this.failed.clear();
  }
}

export const useImagePreloader = () => {
  const preloaderRef = useRef<ImagePreloader | undefined>(undefined);
  const [stats, setStats] = useState({
    loaded: 0,
    loading: 0,
    failed: 0,
    queued: 0
  });

  useEffect(() => {
    preloaderRef.current = ImagePreloader.getInstance();
  }, []);

  const preload = useCallback(
    (urls: string | string[], options?: PreloadOptions) => {
      if (!preloaderRef.current) return Promise.resolve([]);
      return preloaderRef.current.preload(urls, options);
    },
    []
  );

  const isLoaded = useCallback((url: string) => {
    return preloaderRef.current?.isLoaded(url) ?? false;
  }, []);

  const isLoading = useCallback((url: string) => {
    return preloaderRef.current?.isLoading(url) ?? false;
  }, []);

  const hasFailed = useCallback((url: string) => {
    return preloaderRef.current?.hasFailed(url) ?? false;
  }, []);

  const updateStats = useCallback(() => {
    if (preloaderRef.current) {
      setStats(preloaderRef.current.getStats());
    }
  }, []);

  const setMaxConcurrent = useCallback((max: number) => {
    preloaderRef.current?.setMaxConcurrent(max);
  }, []);

  const clear = useCallback(() => {
    preloaderRef.current?.clear();
    setStats({ loaded: 0, loading: 0, failed: 0, queued: 0 });
  }, []);

  return {
    preload,
    isLoaded,
    isLoading,
    hasFailed,
    stats,
    updateStats,
    setMaxConcurrent,
    clear
  };
};

// Hook for preloading images in a post
export const usePostImagePreloader = (
  mediaItems: Array<{ mediaUrl?: string; thumbnailUrl?: string }>,
  options?: PreloadOptions
) => {
  const { preload, isLoaded, isLoading } = useImagePreloader();
  const [preloadStatus, setPreloadStatus] = useState<'idle' | 'loading' | 'loaded' | 'error'>('idle');

  useEffect(() => {
    if (!mediaItems.length) return;

    const urls = mediaItems
      .flatMap(item => [item.mediaUrl, item.thumbnailUrl])
      .filter((url): url is string => Boolean(url));

    if (!urls.length) return;

    setPreloadStatus('loading');

    preload(urls, options)
      .then(() => setPreloadStatus('loaded'))
      .catch(() => setPreloadStatus('error'));
  }, [mediaItems, preload, options]);

  const getImageStatus = useCallback((url: string) => {
    if (isLoaded(url)) return 'loaded';
    if (isLoading(url)) return 'loading';
    return 'idle';
  }, [isLoaded, isLoading]);

  return {
    preloadStatus,
    getImageStatus,
    isAllLoaded: preloadStatus === 'loaded'
  };
};

export default useImagePreloader;