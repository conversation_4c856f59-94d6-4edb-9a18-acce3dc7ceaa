import { jsx as _, jsxs as ie } from "react/jsx-runtime";
import * as T from "react";
import c, { forwardRef as I, createElement as h, useCallback as se, useState as le, useRef as y, useEffect as de } from "react";
import { _ as b, a as B } from "../../index-1c873780.js";
import { $ as U } from "../../index-563d1ed8.js";
import { $ as fe } from "../../index-bf605d8a.js";
import { a as L, $ as Y, b as be } from "../../index-c7156e07.js";
import { $ as E } from "../../index-c8f2666b.js";
import { $ as pe } from "../../index-5116e957.js";
import { $ as J } from "../../index-4914f99c.js";
import { $ as $e } from "../../index-1fe761a6.js";
import { cn as j } from "../../lib.js";
import { c as ue } from "../../createLucideIcon-7a477fa6.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const me = ue("ChevronDown", [
  ["path", { d: "m6 9 6 6 6-6", key: "qrunsl" }]
]), Q = "Collapsible", [xe, W] = U(Q), [ve, G] = xe(Q), Ce = /* @__PURE__ */ I((e, n) => {
  const { __scopeCollapsible: o, open: t, defaultOpen: a, disabled: r, onOpenChange: i, ...s } = e, [l = !1, d] = L({
    prop: t,
    defaultProp: a,
    onChange: i
  });
  return /* @__PURE__ */ h(ve, {
    scope: o,
    disabled: r,
    contentId: J(),
    open: l,
    onOpenToggle: se(
      () => d(
        ($) => !$
      ),
      [
        d
      ]
    )
  }, /* @__PURE__ */ h(E.div, b({
    "data-state": H(l),
    "data-disabled": r ? "" : void 0
  }, s, {
    ref: n
  })));
}), ge = "CollapsibleTrigger", Ae = /* @__PURE__ */ I((e, n) => {
  const { __scopeCollapsible: o, ...t } = e, a = G(ge, o);
  return /* @__PURE__ */ h(E.button, b({
    type: "button",
    "aria-controls": a.contentId,
    "aria-expanded": a.open || !1,
    "data-state": H(a.open),
    "data-disabled": a.disabled ? "" : void 0,
    disabled: a.disabled
  }, t, {
    ref: n,
    onClick: Y(e.onClick, a.onOpenToggle)
  }));
}), X = "CollapsibleContent", _e = /* @__PURE__ */ I((e, n) => {
  const { forceMount: o, ...t } = e, a = G(X, e.__scopeCollapsible);
  return /* @__PURE__ */ h(
    pe,
    {
      present: o || a.open
    },
    ({ present: r }) => /* @__PURE__ */ h(he, b({}, t, {
      ref: n,
      present: r
    }))
  );
}), he = /* @__PURE__ */ I((e, n) => {
  const { __scopeCollapsible: o, present: t, children: a, ...r } = e, i = G(X, o), [s, l] = le(t), d = y(null), $ = B(n, d), u = y(0), C = u.current, v = y(0), g = v.current, w = i.open || s, A = y(w), m = y();
  return de(() => {
    const f = requestAnimationFrame(
      () => A.current = !1
    );
    return () => cancelAnimationFrame(f);
  }, []), be(() => {
    const f = d.current;
    if (f) {
      m.current = m.current || {
        transitionDuration: f.style.transitionDuration,
        animationName: f.style.animationName
      }, f.style.transitionDuration = "0s", f.style.animationName = "none";
      const p = f.getBoundingClientRect();
      u.current = p.height, v.current = p.width, A.current || (f.style.transitionDuration = m.current.transitionDuration, f.style.animationName = m.current.animationName), l(t);
    }
  }, [
    i.open,
    t
  ]), /* @__PURE__ */ h(E.div, b({
    "data-state": H(i.open),
    "data-disabled": i.disabled ? "" : void 0,
    id: i.contentId,
    hidden: !w
  }, r, {
    ref: $,
    style: {
      "--radix-collapsible-content-height": C ? `${C}px` : void 0,
      "--radix-collapsible-content-width": g ? `${g}px` : void 0,
      ...e.style
    }
  }), w && a);
});
function H(e) {
  return e ? "open" : "closed";
}
const we = Ce, ye = Ae, Ee = _e, x = "Accordion", Ie = [
  "Home",
  "End",
  "ArrowDown",
  "ArrowUp",
  "ArrowLeft",
  "ArrowRight"
], [K, Re, Ne] = fe(x), [R, so] = U(x, [
  Ne,
  W
]), F = W(), Z = /* @__PURE__ */ c.forwardRef((e, n) => {
  const { type: o, ...t } = e, a = t, r = t;
  return /* @__PURE__ */ c.createElement(K.Provider, {
    scope: e.__scopeAccordion
  }, o === "multiple" ? /* @__PURE__ */ c.createElement(ke, b({}, r, {
    ref: n
  })) : /* @__PURE__ */ c.createElement(De, b({}, a, {
    ref: n
  })));
});
Z.propTypes = {
  type(e) {
    const n = e.value || e.defaultValue;
    return e.type && ![
      "single",
      "multiple"
    ].includes(e.type) ? new Error("Invalid prop `type` supplied to `Accordion`. Expected one of `single | multiple`.") : e.type === "multiple" && typeof n == "string" ? new Error("Invalid prop `type` supplied to `Accordion`. Expected `single` when `defaultValue` or `value` is type `string`.") : e.type === "single" && Array.isArray(n) ? new Error("Invalid prop `type` supplied to `Accordion`. Expected `multiple` when `defaultValue` or `value` is type `string[]`.") : null;
  }
};
const [ee, Pe] = R(x), [oe, Oe] = R(x, {
  collapsible: !1
}), De = /* @__PURE__ */ c.forwardRef((e, n) => {
  const { value: o, defaultValue: t, onValueChange: a = () => {
  }, collapsible: r = !1, ...i } = e, [s, l] = L({
    prop: o,
    defaultProp: t,
    onChange: a
  });
  return /* @__PURE__ */ c.createElement(ee, {
    scope: e.__scopeAccordion,
    value: s ? [
      s
    ] : [],
    onItemOpen: l,
    onItemClose: c.useCallback(
      () => r && l(""),
      [
        r,
        l
      ]
    )
  }, /* @__PURE__ */ c.createElement(oe, {
    scope: e.__scopeAccordion,
    collapsible: r
  }, /* @__PURE__ */ c.createElement(te, b({}, i, {
    ref: n
  }))));
}), ke = /* @__PURE__ */ c.forwardRef((e, n) => {
  const { value: o, defaultValue: t, onValueChange: a = () => {
  }, ...r } = e, [i = [], s] = L({
    prop: o,
    defaultProp: t,
    onChange: a
  }), l = c.useCallback(
    ($) => s(
      (u = []) => [
        ...u,
        $
      ]
    ),
    [
      s
    ]
  ), d = c.useCallback(
    ($) => s(
      (u = []) => u.filter(
        (C) => C !== $
      )
    ),
    [
      s
    ]
  );
  return /* @__PURE__ */ c.createElement(ee, {
    scope: e.__scopeAccordion,
    value: i,
    onItemOpen: l,
    onItemClose: d
  }, /* @__PURE__ */ c.createElement(oe, {
    scope: e.__scopeAccordion,
    collapsible: !0
  }, /* @__PURE__ */ c.createElement(te, b({}, r, {
    ref: n
  }))));
}), [Se, N] = R(x), te = /* @__PURE__ */ c.forwardRef((e, n) => {
  const { __scopeAccordion: o, disabled: t, dir: a, orientation: r = "vertical", ...i } = e, s = c.useRef(null), l = B(s, n), d = Re(o), u = $e(a) === "ltr", C = Y(e.onKeyDown, (v) => {
    var g;
    if (!Ie.includes(v.key))
      return;
    const w = v.target, A = d().filter((S) => {
      var V;
      return !((V = S.ref.current) !== null && V !== void 0 && V.disabled);
    }), m = A.findIndex(
      (S) => S.ref.current === w
    ), f = A.length;
    if (m === -1)
      return;
    v.preventDefault();
    let p = m;
    const P = 0, O = f - 1, D = () => {
      p = m + 1, p > O && (p = P);
    }, k = () => {
      p = m - 1, p < P && (p = O);
    };
    switch (v.key) {
      case "Home":
        p = P;
        break;
      case "End":
        p = O;
        break;
      case "ArrowRight":
        r === "horizontal" && (u ? D() : k());
        break;
      case "ArrowDown":
        r === "vertical" && D();
        break;
      case "ArrowLeft":
        r === "horizontal" && (u ? k() : D());
        break;
      case "ArrowUp":
        r === "vertical" && k();
        break;
    }
    const ae = p % f;
    (g = A[ae].ref.current) === null || g === void 0 || g.focus();
  });
  return /* @__PURE__ */ c.createElement(Se, {
    scope: o,
    disabled: t,
    direction: a,
    orientation: r
  }, /* @__PURE__ */ c.createElement(K.Slot, {
    scope: o
  }, /* @__PURE__ */ c.createElement(E.div, b({}, i, {
    "data-orientation": r,
    ref: l,
    onKeyDown: t ? void 0 : C
  }))));
}), M = "AccordionItem", [Ve, q] = R(M), Me = /* @__PURE__ */ c.forwardRef((e, n) => {
  const { __scopeAccordion: o, value: t, ...a } = e, r = N(M, o), i = Pe(M, o), s = F(o), l = J(), d = t && i.value.includes(t) || !1, $ = r.disabled || e.disabled;
  return /* @__PURE__ */ c.createElement(Ve, {
    scope: o,
    open: d,
    disabled: $,
    triggerId: l
  }, /* @__PURE__ */ c.createElement(we, b({
    "data-orientation": r.orientation,
    "data-state": ne(d)
  }, s, a, {
    ref: n,
    disabled: $,
    open: d,
    onOpenChange: (u) => {
      u ? i.onItemOpen(t) : i.onItemClose(t);
    }
  })));
}), Te = "AccordionHeader", Le = /* @__PURE__ */ c.forwardRef((e, n) => {
  const { __scopeAccordion: o, ...t } = e, a = N(x, o), r = q(Te, o);
  return /* @__PURE__ */ c.createElement(E.h3, b({
    "data-orientation": a.orientation,
    "data-state": ne(r.open),
    "data-disabled": r.disabled ? "" : void 0
  }, t, {
    ref: n
  }));
}), z = "AccordionTrigger", je = /* @__PURE__ */ c.forwardRef((e, n) => {
  const { __scopeAccordion: o, ...t } = e, a = N(x, o), r = q(z, o), i = Oe(z, o), s = F(o);
  return /* @__PURE__ */ c.createElement(K.ItemSlot, {
    scope: o
  }, /* @__PURE__ */ c.createElement(ye, b({
    "aria-disabled": r.open && !i.collapsible || void 0,
    "data-orientation": a.orientation,
    id: r.triggerId
  }, s, t, {
    ref: n
  })));
}), Ge = "AccordionContent", He = /* @__PURE__ */ c.forwardRef((e, n) => {
  const { __scopeAccordion: o, ...t } = e, a = N(x, o), r = q(Ge, o), i = F(o);
  return /* @__PURE__ */ c.createElement(Ee, b({
    role: "region",
    "aria-labelledby": r.triggerId,
    "data-orientation": a.orientation
  }, i, t, {
    ref: n,
    style: {
      "--radix-accordion-content-height": "var(--radix-collapsible-content-height)",
      "--radix-accordion-content-width": "var(--radix-collapsible-content-width)",
      ...e.style
    }
  }));
});
function ne(e) {
  return e ? "open" : "closed";
}
const Ke = Z, Fe = Me, qe = Le, re = je, ce = He, lo = Ke, ze = T.forwardRef(({ className: e, ...n }, o) => /* @__PURE__ */ _(
  Fe,
  {
    ref: o,
    className: j(
      "bg-gray-100 px-4 rounded-lg shadow",
      // Add the background color and padding here
      e
    ),
    ...n
  }
));
ze.displayName = "AccordionItem";
const Be = T.forwardRef(({ className: e, children: n, ...o }, t) => /* @__PURE__ */ ie(qe, { className: "flex flex-1 items-center justify-between py-4 sm:py-[14px] font-medium w-full", children: [
  n,
  /* @__PURE__ */ _(
    re,
    {
      ref: t,
      className: j(
        "flex flex-1 items-center justify-end font-medium transition-all [&[data-state=open]>div>svg]:rotate-180 ml-auto",
        e
      ),
      ...o,
      children: /* @__PURE__ */ _("div", { className: "flex justify-center items-center w-8 h-8 sm:w-4 sm:h-4 rounded-xl cursor-pointer", children: /* @__PURE__ */ _(me, { className: "h-6 w-6 shrink-0 transition-transform duration-200" }) })
    }
  )
] }));
Be.displayName = re.displayName;
const Ue = T.forwardRef(({ className: e, children: n, ...o }, t) => /* @__PURE__ */ _(
  ce,
  {
    ref: t,
    className: j(
      "overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",
      e
    ),
    ...o,
    children: /* @__PURE__ */ _("div", { className: "pb-4 pt-0", children: n })
  }
));
Ue.displayName = ce.displayName;
export {
  lo as Accordion,
  Ue as AccordionContent,
  ze as AccordionItem,
  Be as AccordionTrigger
};
