"use client";

import React, { useState } from "react";
import { AnimatedModal } from "@/components/ui/animated-modal";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CreditCardIcon, PayPalIcon, CryptoIcon } from "@/components/icons/PaymentIcons";

interface PaymentMethodDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (method: PaymentMethod) => void;
  selectedMethod: PaymentMethod;
}

export type PaymentMethod = {
  id: string;
  name: string;
  icon: React.ReactNode;
  fee: string;
};

const PaymentMethodDialog = ({
  isOpen,
  onClose,
  onSave,
  selectedMethod,
}: PaymentMethodDialogProps) => {
  const [selected, setSelected] = useState<string>(selectedMethod.id);

  const paymentMethods: PaymentMethod[] = [
    {
      id: "card",
      name: "Payment card",
      icon: <CreditCardIcon />,
      fee: "Excl. $11.50 VAT + $2.45 transaction fee",
    },
    {
      id: "paypal",
      name: "PayPal",
      icon: <PayPalIcon />,
      fee: "Excl. $11.50 VAT + $5 transaction fee",
    },
    {
      id: "crypto",
      name: "Crypto",
      icon: <CryptoIcon />,
      fee: "Excl. $11.50 VAT + $1 transaction fee",
    },
  ];

  const handleSave = () => {
    const method = paymentMethods.find((m) => m.id === selected);
    if (method) {
      onSave(method);
    }
    onClose();
  };

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="md"
      header={
        <div className="flex items-center justify-between w-full px-4 py-3">
          <Button
            onClick={onClose}
            variant="ghost"
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 bg-gray-200 dark:bg-zinc-600"
          >
            Cancel
          </Button>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Payment Method
          </h2>
          <Button
            onClick={handleSave}
            className="bg-turquoise hover:bg-turquoise/90 text-white font-semibold"
          >
            Save
          </Button>
        </div>
      }
    >
      <div className="p-4 space-y-3">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={cn(
              "flex items-center justify-between p-4 rounded-lg cursor-pointer",
              "bg-gray-100 dark:bg-zinc-800 hover:bg-gray-200 dark:hover:bg-zinc-700"
            )}
            onClick={() => setSelected(method.id)}
          >
            <div className="flex items-center space-x-4">
              {method.icon}
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {method.name}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {method.fee}
                </p>
              </div>
            </div>
            <div
              className={cn(
                "w-6 h-6 rounded-full border-2",
                selected === method.id
                  ? "border-[#E6FF73] bg-[#E6FF73]"
                  : "border-gray-300 dark:border-gray-600"
              )}
            >
              {selected === method.id && (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-black"></div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </AnimatedModal>
  );
};

export default PaymentMethodDialog;
