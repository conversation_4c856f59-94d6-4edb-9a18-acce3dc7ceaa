import { p as a } from "../immer-548168ec.js";
const o = {
  splits: [],
  average_price: 0,
  total_output: 0,
  total_fee: 0,
  possible_routes: {},
  total_output_without_slippage: 0,
  total_input: 0,
  total_input_without_slippage: 0,
  batcher_fee: 0,
  partner_fee: 0,
  price_ab: 0,
  price_ba: 0,
  deposits: 0,
  net_price: 0,
  net_price_reverse: 0,
  dexhunter_fee: 0
}, p = {
  average_price: 0,
  price_ab: 0,
  price_ba: 0
}, c = (l) => ({
  open: !1,
  searchInput: "",
  inputMode: "SELL",
  tokenSell: null,
  tokenBuy: null,
  tokenPrice: p,
  sellAmount: null,
  buyAmount: 0,
  dexSplits: [],
  isTokenPriceLoading: !1,
  isTransactionLoading: !1,
  isSwapDetailsLoading: !1,
  isOrderCancelLoading: !1,
  isSwapSubmitted: !1,
  swapDetails: o,
  estimationError: "",
  dexBlacklist: [],
  bonusOutput: "0",
  orderType: "SWAP",
  limitPrice: 0,
  limitMultiples: 1,
  fixedToken: void 0,
  orderTypes: ["SWAP", "LIMIT"],
  onSwapSuccess: void 0,
  onSwapError: void 0,
  onViewOrder: void 0,
  timesAmount: 1,
  buyInterval: "daily",
  intervalLength: 1,
  autoFocus: !1,
  setTokenSell: (e) => {
    l(
      a((i) => {
        i.swapSlice.tokenSell = e;
      })
    );
  },
  setTokenBuy: (e) => {
    l(
      a((i) => {
        i.swapSlice.tokenBuy = e;
      })
    );
  },
  setTokenPrice: (e) => {
    l(
      a((i) => {
        i.swapSlice.tokenPrice = e;
      })
    );
  },
  setIsTokenPriceLoading: (e) => {
    l(
      a((i) => {
        i.swapSlice.isTokenPriceLoading = e;
      })
    );
  },
  flipTokens: () => {
    l(
      a((e) => {
        const i = e.swapSlice.tokenSell, s = e.swapSlice.tokenBuy;
        e.swapSlice.tokenSell = s, e.swapSlice.tokenBuy = i;
        let t = e.swapSlice.swapDetails.total_output_without_slippage;
        e.swapSlice.sellAmount = t ? Math.round(t) : 0, e.tokenSearchSlice.swapType = e.tokenSearchSlice.swapType === "SELL" ? "BUY" : "SELL";
      })
    );
  },
  setSellAmount: (e) => {
    l(
      a((i) => {
        i.swapSlice.sellAmount = e;
      })
    );
  },
  setDexSplits: (e) => {
    l(
      a((i) => {
        i.swapSlice.dexSplits = e;
      })
    );
  },
  setIsTransactionLoading: (e) => {
    l(
      a((i) => {
        i.swapSlice.isTransactionLoading = e;
      })
    );
  },
  setSwapDetails: (e) => {
    l(
      a((i) => {
        if (!e) {
          i.swapSlice.swapDetails = o;
          return;
        }
        i.swapSlice.swapDetails = e;
      })
    );
  },
  setIsSwapDetailsLoading: (e) => {
    l(
      a((i) => {
        i.swapSlice.isSwapDetailsLoading = e;
      })
    );
  },
  setEstimationError: (e) => {
    l(
      a((i) => {
        i.swapSlice.estimationError = e;
      })
    );
  },
  setBuyAmount: (e) => {
    l(
      a((i) => {
        i.swapSlice.buyAmount = e;
      })
    );
  },
  setInputMode: (e) => {
    l(
      a((i) => {
        i.swapSlice.inputMode = e;
      })
    );
  },
  toggleDexBlacklist: (e) => {
    l(
      a((i) => {
        const s = i.swapSlice.dexBlacklist.indexOf(e);
        s === -1 ? i.swapSlice.dexBlacklist.push(e) : i.swapSlice.dexBlacklist.splice(s, 1);
      })
    );
  },
  setBonusOutput: (e) => {
    l(
      a((i) => {
        i.swapSlice.bonusOutput = e;
      })
    );
  },
  setOrderType: (e) => {
    l(
      a((i) => {
        i.swapSlice.orderType = e;
      })
    );
  },
  setLimitPrice: (e) => {
    l(
      a((i) => {
        i.swapSlice.limitPrice = e;
      })
    );
  },
  setLimitMultiples: (e) => {
    l(
      a((i) => {
        i.swapSlice.limitMultiples = e;
      })
    );
  },
  setDexBlacklist: (e) => {
    l(
      a((i) => {
        i.swapSlice.dexBlacklist = e;
      })
    );
  },
  setIsSwapSubmitted: (e) => {
    l(
      a((i) => {
        i.swapSlice.isSwapSubmitted = e;
      })
    );
  },
  resetTokenPrice: () => {
    l(
      a((e) => {
        e.swapSlice.tokenPrice = p;
      })
    );
  },
  setFixedToken: (e) => {
    l(
      a((i) => {
        i.swapSlice.fixedToken = e;
      })
    );
  },
  setOrderTypes: (e) => {
    l(
      a((i) => {
        i.swapSlice.orderTypes = e, i.swapSlice.orderType = e[0];
      })
    );
  },
  setOnSwapSuccess: (e) => {
    l(
      a((i) => {
        i.swapSlice.onSwapSuccess = e;
      })
    );
  },
  setOnSwapError: (e) => {
    l(
      a((i) => {
        i.swapSlice.onSwapError = e;
      })
    );
  },
  setOnViewOrder: (e) => {
    l(
      a((i) => {
        i.swapSlice.onViewOrder = e;
      })
    );
  },
  setTimesAmout: (e) => {
    l(
      a((i) => {
        i.swapSlice.timesAmount = e;
      })
    );
  },
  setBuyInterval: (e) => {
    l(
      a((i) => {
        i.swapSlice.buyInterval = e;
      })
    );
  },
  setIntervalLength: (e) => {
    l(
      a((i) => {
        i.swapSlice.intervalLength = e;
      })
    );
  },
  setAutoFocus: (e) => {
    l(
      a((i) => {
        i.swapSlice.autoFocus = e;
      })
    );
  }
});
export {
  c as default
};
