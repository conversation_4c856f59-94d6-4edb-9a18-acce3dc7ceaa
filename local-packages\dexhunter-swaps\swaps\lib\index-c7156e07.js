import { useLayoutEffect as b, useRef as s, useEffect as a, useMemo as h, useCallback as v, useState as p } from "react";
function m(e, c, { checkForDefaultPrevented: n = !0 } = {}) {
  return function(o) {
    if (e == null || e(o), n === !1 || !o.defaultPrevented)
      return c == null ? void 0 : c(o);
  };
}
const R = globalThis != null && globalThis.document ? b : () => {
};
function d(e) {
  const c = s(e);
  return a(() => {
    c.current = e;
  }), h(
    () => (...n) => {
      var t;
      return (t = c.current) === null || t === void 0 ? void 0 : t.call(c, ...n);
    },
    []
  );
}
function k({ prop: e, defaultProp: c, onChange: n = () => {
} }) {
  const [t, o] = P({
    defaultProp: c,
    onChange: n
  }), u = e !== void 0, i = u ? e : t, l = d(n), $ = v((f) => {
    if (u) {
      const r = typeof f == "function" ? f(e) : f;
      r !== e && l(r);
    } else
      o(f);
  }, [
    u,
    e,
    o,
    l
  ]);
  return [
    i,
    $
  ];
}
function P({ defaultProp: e, onChange: c }) {
  const n = p(e), [t] = n, o = s(t), u = d(c);
  return a(() => {
    o.current !== t && (u(t), o.current = t);
  }, [
    t,
    o,
    u
  ]), n;
}
export {
  m as $,
  k as a,
  R as b,
  d as c
};
