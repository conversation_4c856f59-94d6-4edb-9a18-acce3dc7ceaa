import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
  }
});

export async function uploadToS3(file: Buffer, mimeType: string, key: string): Promise<string> {
  try {
    // Check file size - 200MB limit
    const MAX_FILE_SIZE = 200 * 1024 * 1024; // 200MB in bytes
    if (file.byteLength > MAX_FILE_SIZE) {
      throw new Error("File is too large. Maximum size is 200MB.");
    }

    const command = new PutObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET!,
      Key: key, // Use the provided key instead of generating one
      Body: file,
      ContentType: mimeType,
      ACL: 'public-read'
    });

    await s3Client.send(command);
    return `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
  } catch (error) {
    console.error('S3 upload error:', error);
    throw new Error('Failed to upload file to S3');
  }
}