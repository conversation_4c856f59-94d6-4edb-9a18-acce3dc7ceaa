import { Metadata } from 'next';
import Link from 'next/link';
import { Search } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Help Center | Sugar Club',
  description: 'Find answers to your questions and learn how to use Sugar Club',
};

// Define help categories and articles
const helpCategories = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    icon: '🚀',
    description: 'Learn the basics of using Sugar Club',
    articles: [
      { id: 'create-account', title: 'How to create an account' },
      { id: 'profile-setup', title: 'Setting up your profile' },
      { id: 'navigation', title: 'Navigating the platform' },
    ],
  },
  {
    id: 'account-settings',
    title: 'Account & Settings',
    icon: '⚙️',
    description: 'Manage your account and preferences',
    articles: [
      { id: 'change-password', title: 'How to change your password' },
      { id: 'privacy-settings', title: 'Privacy settings explained' },
      { id: 'notifications', title: 'Managing notifications' },
    ],
  },
  {
    id: 'messaging',
    title: 'Messaging & Communication',
    icon: '💬',
    description: 'Learn about messaging features',
    articles: [
      { id: 'direct-messages', title: 'Sending direct messages' },
      { id: 'video-calls', title: 'Making video calls' },
      { id: 'blocking-users', title: 'Blocking and reporting users' },
    ],
  },
  {
    id: 'content',
    title: 'Content & Posts',
    icon: '📷',
    description: 'Creating and managing your content',
    articles: [
      { id: 'create-post', title: 'How to create a post' },
      { id: 'content-guidelines', title: 'Content guidelines' },
      { id: 'monetization', title: 'Content monetization options' },
    ],
  },
  {
    id: 'payments',
    title: 'Payments & Billing',
    icon: '💰',
    description: 'Information about payments and billing',
    articles: [
      { id: 'payment-methods', title: 'Supported payment methods' },
      { id: 'subscription-management', title: 'Managing subscriptions' },
      { id: 'payout-options', title: 'Payout options for creators' },
    ],
  },
  {
    id: 'troubleshooting',
    title: 'Troubleshooting',
    icon: '🔧',
    description: 'Solutions to common issues',
    articles: [
      { id: 'login-issues', title: 'Login problems' },
      { id: 'content-not-loading', title: 'Content not loading' },
      { id: 'payment-issues', title: 'Payment issues' },
    ],
  },
];

export default function HelpCenter() {
  return (
    <div className="min-h-screen bg-white dark:bg-[#18181b] py-12">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-[#18181b] dark:text-white mb-4">
            How can we help you?
          </h1>
          <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
            Find answers to your questions and learn how to make the most of your Sugar Club experience.
          </p>

          {/* Search bar */}
          <div className="mt-8 max-w-2xl mx-auto relative">
            <div className="relative">
              <input
                type="text"
                placeholder="Search for help articles..."
                className="w-full py-3 px-4 pl-12 rounded-full border border-gray-300 dark:border-gray-700 bg-white dark:bg-[#242427] text-[#18181b] dark:text-white focus:outline-none focus:ring-2 focus:ring-turquoise"
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            </div>
          </div>
        </div>

        {/* Popular articles */}
        <div className="mb-16">
          <h2 className="text-2xl font-semibold text-[#18181b] dark:text-white mb-6">
            Popular Articles
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { title: 'Modern Slavery and Anti-Human Trafficking Policy', id: 'create-post', category: 'content', link: 'https://sospoilt.com/info/modern-slavery' },
              { title: 'Buy Sugar Credits', id: 'payment-methods', category: 'payments', link: 'https://bentbox.co/my-bentbucks' },
              { title: 'Payment Gateways', id: 'privacy-settings', category: 'account-settings', link: 'https://bentbox.co/buybox' },
              { title: 'Challenges', id: 'privacy-settings', category: 'account-settings', link: 'https://uplust.com/go/challenge/295/socks' },
              { title: 'Submit A Ticket', id: 'privacy-settings', category: 'account-settings' },
              { title: 'Independent Creator Agreement', id: 'privacy-settings', category: 'account-settings', link: 'https://sugarfans.com/site/legal/ica' },
              { title: 'User Code Of Conduct', id: 'privacy-settings', category: 'account-settings', link: 'https://www.sextpanther.com/user/code-of-conduct' },
            ].map((article) => (
              <Link
                key={article.id}
                href={article.link ? article.link : `/help-support/help-center/${article.category}/${article.id}`}
                className="p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-turquoise dark:hover:border-turquoise transition-colors"
              >
                <h3 className="text-lg font-medium text-[#18181b] dark:text-white mb-2">
                  {article.title}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  Read article →
                </p>
              </Link>
            ))}
          </div>
        </div>

        {/* Help categories */}
        <div>
          <h2 className="text-2xl font-semibold text-[#18181b] dark:text-white mb-6">
            Browse by Category
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {helpCategories.map((category) => (
              <div
                key={category.id}
                className="p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-turquoise dark:hover:border-turquoise transition-colors"
              >
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">{category.icon}</span>
                  <h3 className="text-xl font-semibold text-[#18181b] dark:text-white">
                    {category.title}
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {category.description}
                </p>
                <ul className="space-y-2">
                  {category.articles.map((article) => (
                    <li key={article.id}>
                      <Link
                        href={`/help-support/help-center/${category.id}/${article.id}`}
                        className="text-turquoise hover:underline flex items-center"
                      >
                        <span className="text-xs mr-2">•</span>
                        {article.title}
                      </Link>
                    </li>
                  ))}
                </ul>
                <Link
                  href={`/help-support/help-center/${category.id}`}
                  className="inline-block mt-4 text-sm text-turquoise hover:underline"
                >
                  View all articles →
                </Link>
              </div>
            ))}
          </div>
        </div>

        {/* Contact support */}
        <div className="mt-16 text-center p-8 rounded-lg bg-gray-100 dark:bg-[#242427]">
          <h2 className="text-2xl font-semibold text-[#18181b] dark:text-white mb-4">
            Still need help?
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
            Can't find what you're looking for? Our support team is here to help you with any questions or issues.
          </p>
          <Link
            href="/help-support/contact"
            className="inline-flex items-center justify-center px-6 py-3 bg-turquoise text-white rounded-full hover:bg-cyan-600 transition-colors font-medium"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </div>
  );
}