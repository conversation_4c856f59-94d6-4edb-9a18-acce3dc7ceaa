'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Gift, Eye, ChevronLeft, ChevronRight, MoreVertical } from 'lucide-react';
import { motion, useMotionValue, useAnimation, PanInfo } from 'framer-motion';

interface VideoItem {
  id: string;
  thumbnailUrl: string;
  title: string;
  channel?: string;
  views: string;
  timeAgo?: string;
  duration: string;
  isVerified?: boolean;
  isNew?: boolean;
}

interface SpecialOffersProps {
  videos?: VideoItem[];
  onVideoClick?: (videoId: string) => void;
  onOpenLightbox?: (post: any, mediaArray: any[], initialIndex: number) => void;
}

const SpecialOffers: React.FC<SpecialOffersProps> = ({
  videos = [],
  onVideoClick,
  onOpenLightbox
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});
  const contentRef = useRef<HTMLDivElement>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const [animatedHeight, setAnimatedHeight] = useState<number | 'auto'>(0);

  // Motion values for drag gesture
  const x = useMotionValue(0);
  const controls = useAnimation();

  // Track container dimensions
  const [containerWidth, setContainerWidth] = useState(0);
  const [scrollWidth, setScrollWidth] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  // Toggle expand/collapse
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Update height for animation
  useEffect(() => {
    if (contentRef.current) {
      if (isExpanded) {
        // Add a small buffer to ensure nothing gets cut off
        setAnimatedHeight(contentRef.current.scrollHeight + 20);

        // Use ResizeObserver to handle dynamic content height changes
        const resizeObserver = new ResizeObserver(() => {
          if (contentRef.current && isExpanded) {
            setAnimatedHeight(contentRef.current.scrollHeight + 20);
          }
        });

        resizeObserver.observe(contentRef.current);
        return () => resizeObserver.disconnect();
      } else {
        setAnimatedHeight(0);
      }
    }
  }, [isExpanded, loadedImages]);

  // Handle image load
  const handleImageLoad = (id: string) => {
    setLoadedImages(prev => ({ ...prev, [id]: true }));
  };

  // Update container dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (carouselRef.current) {
        const carousel = carouselRef.current;
        const parent = carousel.parentElement;
        if (parent) {
          setContainerWidth(parent.clientWidth);
          setScrollWidth(carousel.scrollWidth);
        }
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, [videos, isExpanded]);

  // Update dimensions when slides change
  useEffect(() => {
    if (carouselRef.current) {
      const carousel = carouselRef.current;
      const parent = carousel.parentElement;
      if (parent) {
        setContainerWidth(parent.clientWidth);
        setScrollWidth(carousel.scrollWidth);
      }
    }
  }, [currentSlide]);

  // Mock data for carousel
  const carouselVideos: VideoItem[] = [
    {
      id: '1',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_42045800_1740534804_20250226015324_320_320_sq.jpg',
      title: 'Pharrell Admits He Couldn\'t Produce Stevie Wonder',
      channel: 'Classic Hip Hop',
      views: '1.9M',
      timeAgo: '1 week ago',
      duration: '1:23'
    },
    {
      id: '2',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_42053300_1740534804_20250226015324_320_320_sq.jpg',
      title: 'Lucas and Tyler the Creator',
      channel: 'Music',
      views: '2.4M',
      timeAgo: '2 days ago',
      duration: '2:15'
    },
    {
      id: '3',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_42038200_1740534804_20250226015324_320_320_sq.jpg',
      title: 'How Chrome Hearts took over',
      channel: 'Fashion',
      views: '998K',
      timeAgo: '1 month ago',
      duration: '3:45'
    },
    {
      id: '4',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_42022300_1740534804_20250226015324_320_320_sq.jpg',
      title: 'Tom Cruise On Brad Pitt, Paul Newman and "Mission"',
      channel: 'Letterman',
      views: '165K',
      timeAgo: '2 days ago',
      duration: '15:57',
      isVerified: true,
      isNew: true
    },
    {
      id: '5',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_96787000_1742609049_20250322020409_320_320_sq.jpg',
      title: 'Times 50 Cent Destroyed Rappers',
      channel: 'Louaista',
      views: '342K',
      timeAgo: '4 months ago',
      duration: '42:10',
      isVerified: true
    },
    {
      id: '6',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_67591400_1742780155_20250324013555_320_320_sq.jpg',
      title: 'Why Drake Abandoned DJ Khaled',
      channel: 'Diverse Lifestyle',
      views: '208K',
      timeAgo: '1 month ago',
      duration: '28:04',
      isVerified: true
    },
    {
      id: '7',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_03319900_1742950407_20250326005327_320_320_sq.jpg',
      title: 'Kanye West on the Ellen Show',
      channel: 'Ellen',
      views: '1.2M',
      timeAgo: '3 weeks ago',
      duration: '12:30',
      isVerified: true
    },
    {
      id: '8',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_92771600_1743045564_20250327031924_320_320_sq.jpg',
      title: 'Travis Scott on the Ellen Show',
      channel: 'Ellen',
      views: '1.5M',
      timeAgo: '1 week ago',
      duration: '10:45'
    },
    {
      id: '9',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_21067400_1743386029_20250331015349_320_320_sq.jpg',
      title: 'Kylie Jenner on the Ellen Show',
      channel: 'Ellen',
      views: '2.1M',
      timeAgo: '5 days ago',
      duration: '8:20'
    }
  ];

  const listVideos: VideoItem[] = [
    {
      id: '1',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_69865500_1739511644_20250214054044_320_320_sq.jpg',
      title: 'Pharrell Admits He Couldn\'t Produce Stevie Wonder',
      channel: 'Classic Hip Hop',
      views: '1.9M',
      timeAgo: '6 weeks ago',
      duration: '1:23',
      isVerified: true,
      isNew: true
    },
    {
      id: '2',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_92034100_1739479281_20250213204121_320_320_sq.jpg',
      title: 'Lucas and Tyler the Creator',
      channel: 'Music',
      views: '2.4M',
      timeAgo: '1 month ago',
      duration: '2:15'
    },
    {
      id: '3',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_29362500_1739093699_20250209093459_320_320_sq.jpg',
      title: 'How Chrome Hearts took over',
      channel: 'Fashion',
      views: '998K',
      timeAgo: '1 month ago',
      duration: '3:45'
    },
    {
      id: '4',
      thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_71618400_1737082604_20250117025644_320_320_sq.jpg',
      title: 'Tom Cruise On Brad Pitt, Paul Newman and "Mission"',
      channel: 'Letterman',
      views: '165K',
      timeAgo: '2 days ago',
      duration: '15:57',
      isVerified: true,
      isNew: true
    },
  ];

  const handleVideoClick = (videoId: string, video: VideoItem) => {
    if (onOpenLightbox) {
      // Create a mock post object with the video data
      const mockPost = {
        id: video.id,
        user: {
          id: 'creator',
          name: video.channel || 'Creator',
          username: video.channel || 'creator',
          profileImage: video.thumbnailUrl
        },
        content: video.title,
        createdAt: new Date().toISOString(),
        views: parseInt(video.views.replace(/[^0-9]/g, '')) || 0,
        likes: 0,
        comments: 0,
        saves: 0
      };

      // Create media array with the video
      const mediaArray = [{
        mediaUrl: video.thumbnailUrl,
        mediaType: 'video/mp4',
        thumbnailUrl: video.thumbnailUrl,
        duration: video.duration
      }];

      onOpenLightbox(mockPost, mediaArray, 0);
    } else if (onVideoClick) {
      onVideoClick(videoId);
    }
  };

  const displayCarouselVideos = videos.length > 0 ? videos : carouselVideos;
  const displayListVideos = videos.length > 0 ? videos.slice(3) : listVideos;

  const totalSlides = Math.ceil(displayCarouselVideos.length / 3);
  const hasMultipleSlides = displayCarouselVideos.length > 3;

  // Handle drag start
  const handleDragStart = () => {
    setIsDragging(true);
  };

  // Handle drag end with inertia
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false);

    // Apply inertia based on velocity
    const velocity = info.velocity.x;
    const currentX = x.get();

    // Calculate drag constraints
    const dragConstraints = {
      right: 0,
      left: -(scrollWidth - containerWidth)
    };

    // If velocity is high enough, apply inertia
    if (Math.abs(velocity) > 500) {
      const projection = currentX + velocity * 0.2; // Project where it would end up
      const clampedProjection = Math.max(dragConstraints.left, Math.min(dragConstraints.right, projection));

      controls.start({
        x: clampedProjection,
        transition: {
          type: 'spring',
          velocity: velocity * 0.01,
          stiffness: 300,
          damping: 40
        }
      });

      // Update current slide based on position
      if (containerWidth > 0) {
        const newSlide = Math.min(
          totalSlides - 1,
          Math.max(0, Math.round(-clampedProjection / containerWidth))
        );
        setCurrentSlide(newSlide);
      }
    } else {
      // Just make sure we're within constraints
      const clampedX = Math.max(dragConstraints.left, Math.min(dragConstraints.right, currentX));

      if (clampedX !== currentX) {
        controls.start({
          x: clampedX,
          transition: { type: 'spring', stiffness: 500, damping: 30 }
        });
      }

      // Snap to nearest slide
      if (containerWidth > 0) {
        const newSlide = Math.min(
          totalSlides - 1,
          Math.max(0, Math.round(-currentX / containerWidth))
        );
        setCurrentSlide(newSlide);

        controls.start({
          x: -newSlide * containerWidth,
          transition: { type: 'spring', stiffness: 500, damping: 30 }
        });
      }
    }
  };

  const nextSlide = () => {
    const newSlide = Math.min(totalSlides - 1, currentSlide + 1);
    setCurrentSlide(newSlide);

    controls.start({
      x: -newSlide * containerWidth,
      transition: { type: 'spring', stiffness: 300, damping: 30 }
    });
  };

  const prevSlide = () => {
    const newSlide = Math.max(0, currentSlide - 1);
    setCurrentSlide(newSlide);

    controls.start({
      x: -newSlide * containerWidth,
      transition: { type: 'spring', stiffness: 300, damping: 30 }
    });
  };

  return (
    <div className="mb-4 w-full max-w-full overflow-hidden h-auto pb-3">
      <button
        onClick={toggleExpand}
        className="flex items-center justify-between w-full h-10 px-3 rounded-lg bg-transparent transition-colors text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white"
      >
        <div className="flex items-center text-sm">
          <Gift strokeWidth={2.75} className="h-4 w-4 mr-2" />
          <span>Special Offers</span>
        </div>
        <div>
          <svg
            width={16}
            height={16}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`fill-current transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
          >
            <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
          </svg>
        </div>
      </button>

      <motion.div
        className="overflow-hidden w-full -mb-2"
        style={{ height: animatedHeight }}
        initial={false}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <div
          ref={contentRef}
          className="mt-4 w-full"
        >
          {/* Carousel for top row videos */}
          <div className="relative w-full">
            {hasMultipleSlides && (
              <>
                <button
                  onClick={prevSlide}
                  className="absolute left-0 top-[35%] -translate-y-1/2 z-10 bg-white/80 dark:bg-black/80 rounded-full p-1 shadow-md"
                  aria-label="Previous videos"
                  disabled={currentSlide === 0}
                  style={{ opacity: currentSlide === 0 ? 0.5 : 1 }}
                >
                  <ChevronLeft className="h-5 w-5 text-gray-800 dark:text-white" />
                </button>

                <button
                  onClick={nextSlide}
                  className="absolute right-0 top-[35%] -translate-y-1/2 z-10 bg-white/80 dark:bg-black/80 rounded-full p-1 shadow-md"
                  aria-label="Next videos"
                  disabled={currentSlide === totalSlides - 1}
                  style={{ opacity: currentSlide === totalSlides - 1 ? 0.5 : 1 }}
                >
                  <ChevronRight className="h-5 w-5 text-gray-800 dark:text-white" />
                </button>
              </>
            )}

            <div className="overflow-hidden w-full">
              <motion.div
                ref={carouselRef}
                className="flex w-full"
                style={{ x }}
                animate={controls}
                drag="x"
                dragConstraints={{
                  right: 0,
                  left: -(scrollWidth - containerWidth)
                }}
                dragElastic={0.1}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
                whileTap={{ cursor: 'grabbing' }}
              >
                {Array.from({ length: totalSlides }).map((_, slideIndex) => (
                  <div key={`slide-${slideIndex}`} className="grid grid-cols-3 gap-1.5 w-full flex-shrink-0">
                    {displayCarouselVideos.slice(slideIndex * 3, slideIndex * 3 + 3).map((video) => (
                      <div
                        key={video.id}
                        className="cursor-pointer w-full"
                        onClick={() => !isDragging && handleVideoClick(video.id, video)}
                      >
                        <div className="relative aspect-[9/15] rounded-lg overflow-hidden w-full mb-2">
                          <img
                            src={video.thumbnailUrl}
                            alt={video.title}
                            className="object-cover w-full h-full hover:scale-105 transition-transform duration-300 ease-in-out"
                            onLoad={() => handleImageLoad(video.id)}
                            onError={() => handleImageLoad(video.id)}
                            draggable={false}
                          />
                          <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 py-0.5 rounded">
                            {video.duration}
                          </div>
                        </div>
                        <div className="flex flex-col w-full">
                          <h3 className="font-semibold text-[12px] line-clamp-2 text-gray-600 dark:text-white leading-tight mb-1">
                            {video.title}
                          </h3>
                          <div className="flex items-center">
                            <span className="text-xs text-gorilla-gray flex items-center gap-1">
                              <Eye className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                              {video.views}
                            </span>
                            <MoreVertical className="h-4 w-4 text-gray-600 dark:text-gray-400 ml-auto" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </motion.div>
            </div>

            {/* Carousel indicators */}
            {/* hasMultipleSlides && (
              <div className="flex justify-center mt-2 gap-1 mb-2">
                {Array.from({ length: totalSlides }).map((_, index) => (
                  <button
                    key={`indicator-${index}`}
                    className={`h-1.5 rounded-full transition-all ${currentSlide === index ? 'w-4 bg-gray-800 dark:bg-white' : 'w-1.5 bg-gray-300 dark:bg-gray-600'
                      }`}
                    onClick={() => setCurrentSlide(index)}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            ) */}
          </div>
        </div>
      </motion.div>
    </div>
  );
};
export default SpecialOffers;