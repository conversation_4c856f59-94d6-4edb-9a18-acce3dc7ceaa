'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { X } from 'lucide-react';
import {
  CookieConsentStatus,
  saveConsentPreferences
} from '@/utils/cookieConsent';

export default function CookieBanner() {
  const [consentStatus, setConsentStatus] = useState<CookieConsentStatus>('pending');
  const [isVisible, setIsVisible] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [preferences, setPreferences] = useState({
    essential: true,
    analytics: false,
    marketing: false
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const cookieConsent = localStorage.getItem('cookie-consent');

      if (!cookieConsent) {
        setIsVisible(true);
      } else {
        setConsentStatus(cookieConsent as CookieConsentStatus);
      }
    }
  }, []);

  const acceptAll = () => {
    saveConsentPreferences('accepted');
    setConsentStatus('accepted');
    setIsVisible(false);
  };

  const declineAll = () => {
    saveConsentPreferences('declined');
    setConsentStatus('declined');
    setIsVisible(false);
  };

  const handleToggle = (category: string) => {
    if (category === 'essential') return;

    setPreferences(prev => ({
      ...prev,
      [category]: !prev[category as keyof typeof prev]
    }));
  };

  const handleSave = () => {
    saveConsentPreferences('customized', preferences);
    setIsModalOpen(false);
    setIsVisible(false);
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      essential: true,
      analytics: true,
      marketing: true
    };
    setPreferences(allAccepted);
    saveConsentPreferences('accepted', allAccepted);
    setIsModalOpen(false);
    setIsVisible(false);
  };

  const handleRejectAll = () => {
    const allRejected = {
      essential: true,
      analytics: false,
      marketing: false
    };
    setPreferences(allRejected);
    saveConsentPreferences('declined', allRejected);
    setIsModalOpen(false);
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Cookie Banner */}
      <div className="fixed bottom-0 left-0 right-0 bg-white/90 dark:bg-[#4a4a4a] text-gray-600 dark:text-white p-4 z-50 text-sm flex items-center justify-between">
        <div className="flex items-center justify-center pr-4 text-center text-xxs  m-auto">
          <div className="w-full m-auto max-w-[131.5em]">
            We use essential cookies to make our site work. With your consent, we may also use non-essential cookies to improve user experience, personalize content, customize advertisements, and analyze website traffic. For these reasons, we may share your site usage data with our social media, advertising, and analytics partners. By clicking "Accept," you agree to our website's cookie use as described in our <Link href="/cookies" className="underline text-white-400">Cookie Policy</Link>. You can change your cookie settings at any time by clicking <a onClick={() => setIsModalOpen(true)} className="cursor-pointer underline text-white-400">Preferences</a>.
          </div>

          <div className="flex items-center justify-center gap-2 shrink-0 w-1/4">
            <button
              onClick={acceptAll}
              className="bg-turquoise text-white rounded hover:bg-turquoise/90 px-7 py-2"
            >
              Accept
            </button>

            <button
              onClick={declineAll}
              className="p-1 text-gray-600 dark:text-white hover:text-gray-300"
            >
              <X size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Cookie Preferences Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[100]">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-[500px] w-full mx-4">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h2 className="text-xl font-semibold">Cookie Preferences</h2>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X size={20} />
              </button>
            </div>
            <div className="p-4 space-y-6">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Manage your cookie preferences. Essential cookies are necessary for the website to function and cannot be disabled.
              </p>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-base font-medium">Essential Cookies</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      These cookies are necessary for the website to function and cannot be disabled.
                    </p>
                  </div>
                  <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-400">
                    <span className="translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition" />
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-base font-medium">Analytics Cookies</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      These cookies help us understand how visitors interact with our website.
                    </p>
                  </div>
                  <button
                    onClick={() => handleToggle('analytics')}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full ${preferences.analytics ? 'bg-turquoise' : 'bg-gray-300 dark:bg-gray-600'}`}
                  >
                    <span
                      className={`${preferences.analytics ? 'translate-x-6' : 'translate-x-1'} inline-block h-4 w-4 transform rounded-full bg-white transition`}
                    />
                  </button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-base font-medium">Marketing Cookies</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      These cookies are used to track visitors across websites to display relevant advertisements.
                    </p>
                  </div>
                  <button
                    onClick={() => handleToggle('marketing')}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full ${preferences.marketing ? 'bg-turquoise' : 'bg-gray-300 dark:bg-gray-600'}`}
                  >
                    <span
                      className={`${preferences.marketing ? 'translate-x-6' : 'translate-x-1'} inline-block h-4 w-4 transform rounded-full bg-white transition`}
                    />
                  </button>
                </div>
              </div>
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-base font-medium mb-2">More Information</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  For more details about how we use cookies and your personal data, please read our{' '}
                  <Link href="/privacy" className="text-turquoise hover:underline">Privacy Policy</Link> and{' '}
                  <Link href="/cookies" className="text-turquoise hover:underline">Cookie Policy</Link>.
                </p>
              </div>
            </div>
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row gap-2 sm:justify-between">
              <div className="flex gap-2">
                <button
                  onClick={handleRejectAll}
                  className="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  Reject All
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="px-4 py-2 text-sm bg-turquoise text-white rounded-md hover:bg-turquoise/90"
                >
                  Accept All
                </button>
              </div>
              <button
                onClick={handleSave}
                className="px-4 py-2 text-sm bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-800 rounded-md hover:bg-gray-700 dark:hover:bg-gray-300"
              >
                Save Preferences
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};