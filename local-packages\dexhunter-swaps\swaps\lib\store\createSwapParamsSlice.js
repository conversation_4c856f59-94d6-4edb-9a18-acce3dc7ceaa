import { p as o } from "../immer-548168ec.js";
const r = (l) => ({
  tokenIdSell: "",
  tokenIdBuy: "",
  setTokens: (e, a) => {
    l(
      o((s) => {
        s.swapParamsSlice.tokenIdSell = e, s.swapParamsSlice.tokenIdBuy = a;
      })
    );
  },
  setTokenSell: (e) => {
    l(
      o((a) => {
        a.swapParamsSlice.tokenIdSell = e;
      })
    );
  },
  setTokenBuy: (e) => {
    l(
      o((a) => {
        a.swapParamsSlice.tokenIdBuy = e;
      })
    );
  }
});
export {
  r as default
};
