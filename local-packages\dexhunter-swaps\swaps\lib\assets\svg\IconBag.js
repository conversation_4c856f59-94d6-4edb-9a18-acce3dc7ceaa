import { jsxs as t, jsx as o } from "react/jsx-runtime";
import { memo as e } from "react";
const n = (r) => /* @__PURE__ */ t(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 22 22",
    fill: "none",
    ...r,
    children: [
      /* @__PURE__ */ o(
        "path",
        {
          d: "M1.77743 13.2938C0.992813 9.63219 0.600502 7.80144 1.58398 6.5849C2.56746 5.36835 4.43981 5.36835 8.18452 5.36835H13.8155C17.5602 5.36835 19.4325 5.36835 20.4161 6.5849C21.3995 7.80144 21.0072 9.63219 20.2225 13.2938L19.7546 15.478C19.2227 17.9598 18.9568 19.2007 18.0557 19.9292C17.1546 20.6576 15.8856 20.6576 13.3474 20.6576H8.65255C6.11439 20.6576 4.84531 20.6576 3.94426 19.9292C3.04321 19.2007 2.7773 17.9598 2.24548 15.478L1.77743 13.2938Z",
          stroke: "currentColor",
          strokeWidth: 1.63813
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M6.63086 10.8288H15.3676",
          stroke: "currentColor",
          strokeWidth: 1.63813,
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M8.81641 14.1051H13.1848",
          stroke: "currentColor",
          strokeWidth: 1.63813,
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M17.5517 7.55253L14.2754 1",
          stroke: "currentColor",
          strokeWidth: 1.63813,
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M4.44727 7.55253L7.72353 1",
          stroke: "currentColor",
          strokeWidth: 1.63813,
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }
      )
    ]
  }
), d = e(n);
export {
  d as default
};
