import { jsx as p } from "react/jsx-runtime";
import * as l from "react";
import { forwardRef as $, createElement as m } from "react";
import { _ as u } from "../../index-1c873780.js";
import { $ as h } from "../../index-c8f2666b.js";
import { cn as v } from "../../lib.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const i = "horizontal", x = [
  "horizontal",
  "vertical"
], d = /* @__PURE__ */ $((r, o) => {
  const { decorative: t, orientation: e = i, ...a } = r, n = c(e) ? e : i, s = t ? {
    role: "none"
  } : {
    "aria-orientation": n === "vertical" ? n : void 0,
    role: "separator"
  };
  return /* @__PURE__ */ m(h.div, u({
    "data-orientation": n
  }, s, a, {
    ref: o
  }));
});
d.propTypes = {
  orientation(r, o, t) {
    const e = r[o], a = String(e);
    return e && !c(e) ? new Error(O(a, t)) : null;
  }
};
function O(r, o) {
  return `Invalid prop \`orientation\` of value \`${r}\` supplied to \`${o}\`, expected one of:
  - horizontal
  - vertical

Defaulting to \`${i}\`.`;
}
function c(r) {
  return x.includes(r);
}
const f = d, N = l.forwardRef(
  ({ className: r, orientation: o = "horizontal", decorative: t = !0, ...e }, a) => /* @__PURE__ */ p(
    f,
    {
      ref: a,
      decorative: t,
      orientation: o,
      className: v(
        "dhs-shrink-0 dhs-bg-border",
        o === "horizontal" ? "dhs-h-[1px] dhs-w-full" : "dhs-h-full dhs-w-[1px]",
        r
      ),
      ...e
    }
  )
);
N.displayName = f.displayName;
export {
  N as Separator
};
