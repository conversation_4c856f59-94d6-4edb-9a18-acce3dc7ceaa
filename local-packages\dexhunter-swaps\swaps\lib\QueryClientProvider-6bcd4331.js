import * as t from "react";
import { jsx as u } from "react/jsx-runtime";
var o = t.createContext(
  void 0
), s = (r) => {
  const e = t.useContext(o);
  if (r)
    return r;
  if (!e)
    throw new Error("No QueryClient set, use QueryClientProvider to set one");
  return e;
}, i = ({
  client: r,
  children: e
}) => (t.useEffect(() => (r.mount(), () => {
  r.unmount();
}), [r]), /* @__PURE__ */ u(o.Provider, { value: r, children: e }));
export {
  i as Q,
  s as u
};
