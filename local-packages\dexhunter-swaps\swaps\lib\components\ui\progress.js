import { jsx as c } from "react/jsx-runtime";
import * as V from "react";
import { forwardRef as m, createElement as i } from "react";
import { _ as p } from "../../index-1c873780.js";
import { $ as E } from "../../index-563d1ed8.js";
import { $ as v } from "../../index-c8f2666b.js";
import { cn as f } from "../../lib.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const x = "Progress", u = 100, [w, q] = E(x), [y, I] = w(x), g = /* @__PURE__ */ m((e, r) => {
  const { __scopeProgress: t, value: a, max: n, getValueLabel: s = A, ...N } = e, l = $(n) ? n : u, o = h(a, l) ? a : null, _ = d(o) ? s(o, l) : void 0;
  return /* @__PURE__ */ i(y, {
    scope: t,
    value: o,
    max: l
  }, /* @__PURE__ */ i(v.div, p({
    "aria-valuemax": l,
    "aria-valuemin": 0,
    "aria-valuenow": d(o) ? o : void 0,
    "aria-valuetext": _,
    role: "progressbar",
    "data-state": b(o, l),
    "data-value": o ?? void 0,
    "data-max": l
  }, N, {
    ref: r
  })));
});
g.propTypes = {
  max(e, r, t) {
    const a = e[r], n = String(a);
    return a && !$(a) ? new Error(L(n, t)) : null;
  },
  value(e, r, t) {
    const a = e[r], n = String(a), s = $(e.max) ? e.max : u;
    return a != null && !h(a, s) ? new Error(S(n, t)) : null;
  }
};
const M = "ProgressIndicator", R = /* @__PURE__ */ m((e, r) => {
  var t;
  const { __scopeProgress: a, ...n } = e, s = I(M, a);
  return /* @__PURE__ */ i(v.div, p({
    "data-state": b(s.value, s.max),
    "data-value": (t = s.value) !== null && t !== void 0 ? t : void 0,
    "data-max": s.max
  }, n, {
    ref: r
  }));
});
function A(e, r) {
  return `${Math.round(e / r * 100)}%`;
}
function b(e, r) {
  return e == null ? "indeterminate" : e === r ? "complete" : "loading";
}
function d(e) {
  return typeof e == "number";
}
function $(e) {
  return d(e) && !isNaN(e) && e > 0;
}
function h(e, r) {
  return d(e) && !isNaN(e) && e <= r && e >= 0;
}
function L(e, r) {
  return `Invalid prop \`max\` of value \`${e}\` supplied to \`${r}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${u}\`.`;
}
function S(e, r) {
  return `Invalid prop \`value\` of value \`${e}\` supplied to \`${r}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${u} if no \`max\` prop is set)
  - \`null\` if the progress is indeterminate.

Defaulting to \`null\`.`;
}
const P = g, D = R, T = V.forwardRef(({ className: e, value: r, ...t }, a) => /* @__PURE__ */ c(
  P,
  {
    ref: a,
    className: f(
      "dhs-relative dhs-h-4 dhs-w-full dhs-overflow-hidden dhs-rounded-full dhs-bg-gray-105",
      e
    ),
    ...t,
    children: /* @__PURE__ */ c(
      D,
      {
        className: f(
          "dhs-h-full dhs-w-full dhs-flex-1 dhs-bg-primary dhs-transition-all",
          t.color
        ),
        style: { transform: `translateX(-${100 - (r || 0)}%)` }
      }
    )
  }
));
T.displayName = P.displayName;
export {
  T as Progress
};
