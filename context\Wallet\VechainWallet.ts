"use client";
import { useState, useEffect, useCallback } from 'react';
import {
  useWallet as useVechainWallet
} from '@vechain/dapp-kit-react';
import { WalletSource as VeChainWalletSource } from '@vechain/dapp-kit';
import { Framework } from '@vechain/connex-framework';

/**
 * Custom hook that encapsulates all VeChain wallet functionality on the client side
 */
const useVechainWalletIntegration = () => {
  const {
    account,
    source,
    connectionCertificate,
    setSource,
    connect,
    availableWallets,
    disconnect,
  } = useVechainWallet();

  const [connex, setConnex] = useState<Framework | null>(null);

  /**
   * Initialize Connex Framework with the VeChain wallet certificate
   */
  useEffect(() => {
    if (!account || !connectionCertificate) {
      setConnex(null);
      return;
    }

    const f = new Framework({
      network: process.env.NEXT_PUBLIC_VECHAIN_NETWORK as 'mainnet' | 'testnet' || 'testnet',
      node: process.env.NEXT_PUBLIC_VECHAIN_RPC || 'https://testnet.veblocks.net',
      wallet: {
        source,
        connexion: {
          authority: async () => connectionCertificate,
        },
      },
    });

    setConnex(f);
  }, [account, connectionCertificate, source]);

  /**
   * Wrapper including source selection and connect
   */
  const connectToVechainWallet = useCallback(
    async (walletSource?: VeChainWalletSource) => {
      const src = walletSource || source;
      if (src) {
        setSource(src);
        await connect(src);
      }
    },
    [connect, setSource, source]
  );

  return {
    account,
    source,
    connectionCertificate,
    setSource,
    connect: connectToVechainWallet,
    availableWallets,
    disconnect,
    connex,
  };
};
export default useVechainWalletIntegration;