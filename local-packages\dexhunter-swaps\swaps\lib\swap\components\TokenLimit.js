import { jsxs as i, jsx as s, Fragment as U } from "react/jsx-runtime";
import u from "../../store/useStore.js";
import { memo as K, useState as y, useEffect as P, useMemo as x } from "react";
import { formatBalance as S, formatInput as M } from "../../utils/formatNumber.js";
import { cn as b } from "../../lib/utils.js";
import { C as W } from "../../index.esm-fb2f5862.js";
import { Slider as Y } from "../../components/ui/slider.js";
import q from "../../components/ui/switchWithText.js";
import N from "../../components/ui/tooltipDialog.js";
import O from "../../trends/components/PriceFormatter.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "./tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-1fe761a6.js";
import "../../index-6460524a.js";
import "../../index-bcfeaad9.js";
import "../../index-c8f2666b.js";
import "../../index-bf605d8a.js";
import "../../lib.js";
import "../../hooks/useScreen.js";
import "../../components/ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../components/ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-f7426637.js";
const z = () => {
  const {
    tokenBuy: h,
    tokenSell: l,
    tokenPrice: t,
    limitPrice: d,
    setLimitPrice: c,
    limitMultiples: v,
    setLimitMultiples: T,
    orderType: r
  } = u((e) => e.swapSlice), { slippage: f } = u((e) => e.swapSettingsSlice), { swapType: o } = u((e) => e.tokenSearchSlice), [m, _] = y(!1), [L, C] = y(0);
  P(() => {
    c(0);
  }, [h, l]), P(() => {
    C(parseFloat(S(t.price_ba)));
  }, [t]);
  const B = (e) => {
    if (e > t.price_ba * 1001) {
      c(M((t.price_ba * 1001).toString()));
      return;
    }
    c(M(e));
  }, I = x(() => {
    var w;
    if (!d)
      return null;
    const e = t.price_ba, n = (d - e) / e * 100;
    if (Math.abs(n) < 0.01)
      return null;
    const a = n >= 0 ? "+" : "-", p = n >= 0 ? "dhs-text-green-101" : "dhs-text-red-101", $ = n > 0 ? "above market" : "below market";
    return /* @__PURE__ */ i("span", { className: b("dhs-text-xs dhs-font-proximaBold", p), children: [
      a,
      (w = Math.abs(n || 0)) == null ? void 0 : w.toFixed(2),
      "%",
      " ",
      /* @__PURE__ */ s("span", { className: "sm:dhs-hidden", children: $ })
    ] });
  }, [t, d]), g = x(() => o === "SELL" ? l == null ? void 0 : l.ticker : h == null ? void 0 : h.ticker, [o, h, l]), j = [5, 10, 25, 50], k = (e) => {
    const n = t.price_ba;
    let a;
    o === "SELL" && r !== "STOP_LOSS" ? a = n * (1 + e / 100) : a = n * (1 - e / 100), c(a.toFixed(6));
  }, D = x(() => {
    const e = o === "BUY" || r === "STOP_LOSS", n = o === "SELL" && r !== "STOP_LOSS", a = e ? "-" : "+";
    return /* @__PURE__ */ s("div", { className: "dhs-flex dhs-gap-2", children: j.map((p) => /* @__PURE__ */ i(
      "span",
      {
        className: b(
          "dhs-text-accent dhs-text-xs dhs-font-proximaSemiBold dhs-cursor-pointer",
          n && "dhs-text-green-101",
          e && "dhs-text-red-101"
        ),
        onClick: () => k(p),
        children: [
          a,
          p,
          "%"
        ]
      },
      p
    )) });
  }, [o, t, r]), E = (e) => {
    T(e[0]);
  }, F = () => {
    _(!m), T(1);
  }, R = (e) => {
    if (e.key === ",") {
      if (e.target.value.includes("."))
        return;
      e.preventDefault(), e.target.value = d.toString() + ".", e.target.selectionEnd = e.target.value.length, c(e.target.value);
      return;
    }
  }, V = x(() => r === "LIMIT" ? "Limit Price" : r === "STOP_LOSS" ? "Trigger Price" : "", [r]), A = x(() => {
    if (r === "LIMIT")
      return /* @__PURE__ */ s(
        N,
        {
          trigger: /* @__PURE__ */ i("div", { className: "dhs-text-subText dhs-text-xs dhs-font-proximaSemiBold dhs-gap-1", children: [
            /* @__PURE__ */ s("span", { className: "sm:dhs-hidden", children: "Market Price = " }),
            /* @__PURE__ */ i("span", { children: [
              /* @__PURE__ */ s(O, { price: t == null ? void 0 : t.price_ba }),
              " ",
              g
            ] })
          ] }),
          content: `Current market price: 1 ADA = ${S(
            t == null ? void 0 : t.price_ba
          )} ${g}`,
          contentClass: "dhs-text-white"
        }
      );
    if (r === "STOP_LOSS") {
      const e = d > 0 ? d * (1 - f / 100) : t == null ? void 0 : t.price_ba;
      return f === -1 ? /* @__PURE__ */ s(
        N,
        {
          trigger: /* @__PURE__ */ i("div", { className: "dhs-flex dhs-items-center dhs-gap-1", children: [
            /* @__PURE__ */ s(
              "img",
              {
                src: "https://storage.googleapis.com/dexhunter-images/public/infinity-red.svg",
                className: "dhs-w-[20px]"
              }
            ),
            /* @__PURE__ */ s("span", { className: "dhs-text-red-101 dhs-text-xs dhs-font-proximaSemiBold", children: "Any Price" })
          ] }),
          content: "Sell at any price if triggered",
          contentClass: "dhs-text-white"
        }
      ) : /* @__PURE__ */ s(
        N,
        {
          trigger: /* @__PURE__ */ i("div", { className: "dhs-text-subText dhs-text-xs dhs-font-proximaSemiBold dhs-gap-1", children: [
            /* @__PURE__ */ s("span", { className: "sm:dhs-hidden", children: "Min. Price = " }),
            /* @__PURE__ */ i("span", { children: [
              /* @__PURE__ */ s(O, { price: e }),
              " ",
              g
            ] })
          ] }),
          content: "Minimum execution price, adjusted by slippage",
          contentClass: "dhs-text-white"
        }
      );
    }
    return null;
  }, [r, t, g, d, f]);
  return /* @__PURE__ */ s("div", { className: "dhs-bg-containers dhs-px-4 dhs-py-4 @sm/appRoot:dhs-px-8 sm/appRoot:dhs-py-5 dhs-rounded-[20px]  dhs-w-full", children: /* @__PURE__ */ i("div", { className: "dhs-flex dhs-flex-col dhs-justify-center dhs-gap-[4px]", children: [
    /* @__PURE__ */ i("div", { className: "dhs-flex dhs-justify-between", children: [
      /* @__PURE__ */ s("div", { className: "dhs-flex dhs-gap-1 dhs-text-xs dhs-font-proximaSemiBold", children: /* @__PURE__ */ i("span", { className: "dhs-text-subText", children: [
        V,
        " ",
        I
      ] }) }),
      D
    ] }),
    /* @__PURE__ */ i("div", { className: "dhs-flex dhs-justify-between dhs-items-center dhs-mb-[3px]", children: [
      /* @__PURE__ */ s(
        W,
        {
          type: "text",
          placeholder: L.toString() || "0",
          className: b(
            "dhs-px-0 dhs-border-b-2 dhs-border-solid dhs-border-gray-104 focus-visible:dhs-border-transparent focus-visible:dhs-ring-0 focus-visible:dhs-ring-offset-0 dhs-text-left dhs-bg-transparent dhs-w-20 dhs-text-lg @md/appRoot:dhs-w-36 @md/appRoot:dhs-text-xl dhs-text-mainText dhs-font-proximaMedium dhs-tracking-tighter",
            // isSmallDisplay && "dhs-text-[22px] sm:dhs-text-md"
            // if limit price equals to market price, then the placeholder is the market price
            (!d || d === L) && "!dhs-text-subText",
            d && "dhs-border-b-0"
          ),
          onValueChange: B,
          value: d || S(t == null ? void 0 : t.price_ba),
          maxLength: 12,
          allowNegativeValue: !1,
          decimalsLimit: 8,
          onKeyDown: R,
          intlConfig: { locale: "en-US", currency: "" }
        }
      ),
      /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-gap-1", children: A })
    ] }),
    /* @__PURE__ */ i("div", { className: "dhs-flex dhs-justify-between dhs-items-center dhs-gap-[15px]", children: [
      /* @__PURE__ */ s("div", { className: "dhs-flex dhs-gap-2 dhs-items-center", children: /* @__PURE__ */ s("div", { className: "dhs-text-gray-105 dhs-text-xs dhs-font-proximaSemiBold", children: /* @__PURE__ */ s(
        q,
        {
          left: "ONE",
          right: "SPLIT",
          color: "dhs-bg-accent",
          className: "dhs-bg-background dhs-w-[80px] dhs-h-[22px] dhs-rounded-[6px]",
          initValue: m,
          onChange: F,
          height: "22px",
          borderRadius: "6px"
        }
      ) }) }),
      m && /* @__PURE__ */ i(U, { children: [
        /* @__PURE__ */ s("div", { className: "dhs-flex dhs-flex-1 dhs-gap-1 dhs-items-center dhs-text-subText dhs-text-xs dhs-font-proximaSemiBold dhs-leading-none dhs-h-[10px]", children: /* @__PURE__ */ s(
          Y,
          {
            min: 1,
            max: r === "STOP_LOSS" ? 10 : 40,
            step: 1,
            value: [v],
            onValueChange: E,
            className: "dhs-w-full",
            disabled: !m
          }
        ) }),
        /* @__PURE__ */ i("div", { className: "dhs-text-subText dhs-text-md dhs-font-proximaSemiBold dhs-w-8 dhs-text-right dhs-flex dhs-gap-1 dhs-leading-none", children: [
          /* @__PURE__ */ s("span", { className: "dhs-text-mainText dhs-leading-none", children: v }),
          /* @__PURE__ */ s("span", { className: "dhs-leading-none", children: "/" }),
          /* @__PURE__ */ s("span", { className: "dhs-leading-none", children: r === "STOP_LOSS" ? 10 : 40 })
        ] })
      ] }),
      !m && /* @__PURE__ */ s("div", { className: "dhs-flex dhs-gap-1 dhs-items-center dhs-text-subText dhs-text-xs dhs-font-proximaSemiBold dhs-leading-none dhs-h-[10px] sm:dhs-hidden", children: "Single Order" })
    ] })
  ] }) });
}, Fe = K(z);
export {
  Fe as default
};
