import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ChevronLeft, Mail, MessageCircle, Clock } from 'lucide-react';
import TicketSubmissionForm from './TicketForm';

export const metadata: Metadata = {
  title: 'Contact Support | Sugar Club',
  description: 'Get in touch with our support team for assistance',
};

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-[#18181b] py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link
            href="/help-support/help-center"
            className="flex items-center text-turquoise hover:underline"
          >
            <ChevronLeft size={16} className="mr-1" />
            Back to Help Center
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-[#18181b] dark:text-white mb-4">
            Contact Support
          </h1>
          <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
            Our support team is here to help you with any questions or issues you may have.
          </p>
        </div>

        {/* Ticket submission form */}
        <div className="mb-12">
          <TicketSubmissionForm />
        </div>

        {/* Contact options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Email support */}
          <div className="p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-turquoise/10 flex items-center justify-center mr-4">
                <Mail size={24} className="text-turquoise" />
              </div>
              <h2 className="text-xl font-semibold text-[#18181b] dark:text-white">
                Email Support
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Send us an email and we'll get back to you within 24 hours.
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center justify-center w-full px-5 py-3 bg-turquoise text-white rounded-full hover:bg-cyan-600 transition-colors font-medium"
            >
              Email Us
            </a>
          </div>

          {/* Live chat */}
          <div className="p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-turquoise/10 flex items-center justify-center mr-4">
                <MessageCircle size={24} className="text-turquoise" />
              </div>
              <h2 className="text-xl font-semibold text-[#18181b] dark:text-white">
                Live Chat
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Chat with our support team in real-time for immediate assistance.
            </p>
            <button
              className="inline-flex items-center justify-center w-full px-5 py-3 bg-turquoise text-white rounded-full hover:bg-cyan-600 transition-colors font-medium"
            >
              Start Chat
            </button>
          </div>
        </div>

        {/* Support hours */}
        <div className="p-6 rounded-lg bg-gray-100 dark:bg-[#242427] mb-12">
          <div className="flex items-center mb-4">
            <Clock size={24} className="text-turquoise mr-3" />
            <h2 className="text-xl font-semibold text-[#18181b] dark:text-white">
              Support Hours
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-[#18181b] dark:text-white mb-2">
                Email Support
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                24/7 - Response within 24 hours
              </p>
            </div>
            <div>
              <h3 className="font-medium text-[#18181b] dark:text-white mb-2">
                Live Chat
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Monday - Friday: 9am - 8pm EST<br />
                Saturday - Sunday: 10am - 6pm EST
              </p>
            </div>
          </div>
        </div>

        {/* FAQ section */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-[#18181b] dark:text-white mb-6">
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            {[
              {
                question: 'How long does it take to get a response?',
                answer: 'We aim to respond to all email inquiries within 24 hours. Live chat support is typically immediate during our operating hours.'
              },
              {
                question: 'What information should I include in my support request?',
                answer: 'Please include your username, a detailed description of the issue, any error messages you received, and steps to reproduce the problem if applicable.'
              },
              {
                question: 'How do I report inappropriate content?',
                answer: 'You can report inappropriate content directly from the post or profile by clicking the "Report" button. For urgent matters, please contact our support team.'
              },
            ].map((faq, index) => (
              <div key={index} className="p-5 rounded-lg border border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-[#18181b] dark:text-white mb-2">
                  {faq.question}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Help center link */}
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Looking for self-help resources?
          </p>
          <Link
            href="/help-support/help-center"
            className="inline-flex items-center justify-center px-5 py-2 border border-turquoise text-turquoise rounded-full hover:bg-turquoise/10 transition-colors font-medium"
          >
            Browse Help Center
          </Link>
        </div>
      </div>
    </div>
  );
}

