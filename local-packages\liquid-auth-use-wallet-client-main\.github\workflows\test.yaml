name: "Test"
on:
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest

    permissions:
      # Required to checkout the code
      contents: read
      # Required to put a comment into the pull-request
      pull-requests: write

    steps:
      - uses: actions/checkout@v4
      - name: "Install Node"
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
      - name: "Install pnpm"
        run: npm install -g pnpm
      - name: "Install dependencies"
        run: pnpm install
      - name: "Build"
        run: pnpm build
      - name: "Test"
        run: pnpm run coverage
      - name: "Report Coverage"
        # Set if: always() to also generate the report if tests are failing
        # Only works if you set `reportOnFailure: true` in your vite config as specified above
        if: always()
        uses: davelosert/vitest-coverage-report-action@v2
