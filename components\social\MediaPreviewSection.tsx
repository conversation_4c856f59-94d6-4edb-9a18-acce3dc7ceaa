'use client';

import React, {useEffect, useState} from 'react';
import { X, Play, Edit, Plus, Music, Smartphone } from 'lucide-react';
import { MediaItem } from '@/types/post';

interface MediaPreviewSectionProps {
  mediaPreviews: MediaItem[];
  handleRemoveMedia: (index: number) => void;
  openImageEditor: () => void;
  onAddMoreImages: () => void;
  maxImages?: number;
}

const MediaPreviewSection: React.FC<MediaPreviewSectionProps> = ({
  mediaPreviews,
  handleRemoveMedia,
  openImageEditor,
  onAddMoreImages,
  maxImages = 10
}) => {
  if (mediaPreviews.length === 0) return null;

  const hasOnlyImages = mediaPreviews.length > 0 && mediaPreviews[0].mediaType?.startsWith('image/');
  const canAddMore = hasOnlyImages && mediaPreviews.length < maxImages;

  // Generate a simple waveform visualization
  const generateWaveform = () => {
    const bars = 20;
    const heights = Array.from({ length: bars }, () => Math.random() * 0.6 + 0.2);
    return heights;
  };

  useEffect(() => {
    console.log(mediaPreviews);
  }, [mediaPreviews]);

  const isClip = (mediaType: string) => mediaType === 'video/clip';

  return (
    <div className="mt-4 relative">
      <div className="flex flex-wrap gap-2">
        {mediaPreviews.map((preview, index) => (
          <div key={`preview-${index}`} className="relative group">
            <div className="relative w-24 h-24 rounded-md overflow-hidden border border-gray-200 dark:border-gray-700">
              {preview.mediaType?.startsWith('image/') ? (
                <img
                  src={preview.mediaUrl}
                  alt={`Preview ${index}`}
                  className="w-full h-full object-cover"
                />
              ) : preview.mediaType?.startsWith('video/') ? (
                <div className="relative w-full h-full">
                  <video
                    src={preview.mediaUrl}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                    <Play className="h-8 w-8 text-white" />
                  </div>
                </div>
              ) : preview.mediaType?.startsWith('audio/') ? (
                <div className="relative w-full h-full bg-gradient-to-br from-turquoise/10 to-turquoise/5 dark:from-turquoise/20 dark:to-turquoise/10">
                  <div className="absolute inset-0 flex flex-col items-center justify-center p-2">
                    <Music className="h-8 w-8 text-turquoise mb-1" />
                    <div className="w-full flex items-end justify-center gap-[2px] h-8">
                      {generateWaveform().map((height, i) => (
                        <div
                          key={i}
                          className="w-[2px] bg-turquoise rounded-full"
                          style={{ height: `${height * 100}%` }}
                        />
                      ))}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 truncate w-full text-center">
                      {preview.file?.name || 'Audio file'}
                    </div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Play className="h-8 w-8 text-white" />
                  </div>
                </div>
              ) : null}
            </div>

            {/* Remove button */}
            <button
              type="button"
              onClick={() => handleRemoveMedia(index)}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <X className="h-3 w-3" />
            </button>

            {/* Edit button - only for images */}
            {preview.mediaType?.startsWith('image/') && (
              <button
                type="button"
                onClick={openImageEditor}
                className="absolute bottom-0 right-0 bg-gray-800/70 text-white rounded-tl-md p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <Edit className="h-3 w-3" />
              </button>
            )}

            {/* Add clip indicator */}
            {preview.mediaType && isClip(preview.mediaType) && (
              <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded flex items-center gap-1">
                <Smartphone className="h-4 w-4" />
                <span>Clip</span>
              </div>
            )}
          </div>
        ))}

        {/* Add more images button */}
        {canAddMore && (
          <button
            type="button"
            onClick={onAddMoreImages}
            className="w-24 h-24 flex items-center justify-center border border-dashed border-gray-300 dark:border-gray-700 rounded-md hover:border-turquoise dark:hover:border-turquoise transition-colors"
          >
            <Plus className="h-6 w-6 text-gray-400 dark:text-gray-600" />
          </button>
        )}
      </div>
    </div>
  );
};
export default MediaPreviewSection;