'use client';

import { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import { useDispatch, useSelector } from 'react-redux';
import { useWalletContext } from '@/context/Wallet/WalletContext';
//import { useSolWalletContext } from '@/context/Wallet/SolWalletContext';
import { useChatVisibility } from '@/context/Chat/ChatContext';
import { useRouter, usePathname } from 'next/navigation';
import { useIdle } from 'react-use';
import { clearNotifications, setNotifications, setUnreadNotifications } from '@/redux/slices/notificationSlice';
import { clearAllUsers, setAllUsers } from "@/redux/slices/allUsersSlice";
import { setWallet, clearWalletItem, clearWallet } from '@/redux/slices/walletSlice';
import { useQuery, useMutation } from 'convex/react';
import { fromLovelace, truncateAddress } from "@/public/main";
import { Skeleton } from "@/components/ui/skeleton";
import { useUser } from '@/hooks/useUser';
import { useSocket } from '@/context/SocketContext';
import { resetConfirmations } from '@/redux/slices/confirmationSlice';
import { setLastActive, setStatus } from '@/redux/slices/userActivitySlice';
import { clearActiveConversation, setActiveConversation } from '@/redux/slices/activeConversationsSlice';
import { Menu, X } from 'lucide-react';
import io from 'socket.io-client';
import Link from 'next/link';
import Toaster from '@/components/Toaster';
import Image from 'next/image';
import ThemeToggle from './ThemeToggle';
import LoginPopup from '@/components/dialogs/LoginDialog';
import SignUpDialog from '@/components/dialogs/SignUpDialog';
import LanguageSelector from './LanguageSelector';
import UserDropdown from './UserDropdown';
import Marquee from './Marquee';
import SearchBar from './SearchBar';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { api } from '@/convex/_generated/api';
import { useUserStatus } from '@/context/UserStatusContext';
import { useThrottledStatusUpdate } from '@/hooks/useThrottledStatusUpdate';

interface HeaderProps {
  isMobile?: boolean;
}

const Header = ({ isMobile = false }: HeaderProps) => {
  const { retrieveBalance, handleDisconnect, logOut } = useWalletContext() as any;
  //const { handleSolDisconnect, retrieveSolBalance, solLogout } = useSolWalletContext() as any;
  const { visibleConversations } = useChatVisibility() as any;
  const { isAuthenticated, user } = useUser();
  const isLoggedIn = isAuthenticated;

  const [loading, setLoading] = useState(true);
  const [tokenBalance, setTokenBalance] = useState({ ticker: '', balance: 0 }) as any;
  const [userWalletAddress, setUserWalletAddress] = useState('');
  const [userConnecting, setUserConnecting] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [notificationAudio, setNotificationAudio] = useState(null) as any;
  const [isOwnProfile, setIsOwnProfile] = useState(false);
  const [isCopying, setIsCopying] = useState(false);
  const [sessionLoading, setSessionLoading] = useState(false);
  const navRef = useRef(null);
  const notificationBellRef = useRef(null) as any;
  const notificationPanelRef = useRef(null) as any;
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isLoginOpen, setIsLoginOpen] = useState(false);
  const [isSignUpOpen, setIsSignUpOpen] = useState(false);

  const pathname = usePathname();
  const dispatch = useDispatch();

  const unreadOtherNotifications = useQuery(
    api.notifications.getUnreadOtherNotifications,
    user?.user_id ? { user_id: user.user_id } : "skip"
  )?.notifications || [];

  const userInfo = useSelector((state: any) => state.userInfo);
  // allUsers = useSelector((state: any) => state.allUsers);
  const isHomePage = pathname === '/';

  const { accountType, walletAddress, solWalletAddress, blockchain, isConnecting, isSolConnecting, isConnected, isSolConnected } = useSelector((state: any) => state.wallet);
  const { socket, isConnected: isSocketConnected } = useSocket();

  const Toast = () => toast('');

  const isIdle = useIdle(60e3, true);

  const [isSticky, setIsSticky] = useState(false);
  const scrollThreshold = 100; // Adjust this value as needed

  const updateStatus = useThrottledStatusUpdate(user?.user_id);

  const openLoginPopup = () => {
    setIsLoginOpen(true);
  };

  const openSignUpPopup = () => {
    setIsSignUpOpen(true);
  };

  const toggleNotifications = async () => {
    setShowNotifications((prev: boolean) => {
      if (!prev) {
        markNotificationsAsRead();
      }
      return !prev;
    });
  };

  const markNotificationsAsReadMutation = useMutation(api.notifications.markNotificationAsRead);
  const markNotificationsAsRead = async () => {
    try {
      await markNotificationsAsReadMutation({ user_id: user?.user_id });
      dispatch(setUnreadNotifications(0));
    } catch (error) {
      console.error('Error marking notifications as read:', error);
    }
  };

  const getCurrentBalance = async (chain: any) => {
    try {
      switch (chain) {
        case 'Cardano':
          const currentADABalanceInLovelace = await retrieveBalance('lovelace', chain);
          const currentADABalance = fromLovelace(currentADABalanceInLovelace);

          setTokenBalance({ ticker: 'ADA', balance: currentADABalance });
          break;
        case 'Bitcoin':
          const bitcoinBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'BTC', balance: bitcoinBalance });
          break;
        case 'Ethereum':
        case 'Linea':
        case 'Base':
          const ethereumBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'ETH', balance: ethereumBalance });
          break;
        case 'Polygon':
          const polygonBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'MATIC', balance: polygonBalance });
          break;
        case 'BNB Smart Chain':
          const bnbBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'BNB', balance: bnbBalance });
          break;
        case 'Blast':
          const blastBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'BLAST', balance: blastBalance });
          break;
        case 'Optimism':
          const optimismBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'OP', balance: optimismBalance });
          break;
        case 'Arbitrum':
          const arbitrumBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'ARB', balance: arbitrumBalance });
          break;
        case 'Avalanche':
          const avalancheBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'AVAX', balance: avalancheBalance });
          break;
        case 'Immutable':
          const immutableBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'IMX', balance: immutableBalance });
          break;
        case 'EOS':
          const eosBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'EOS', balance: eosBalance });
          break;
        case 'Vechain':
          const vechainBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'VET', balance: vechainBalance });
          break;
        /* case 'Solana':
          const solanaBalance = await retrieveSolBalance();
          setTokenBalance({ ticker: 'SOL', balance: solanaBalance });
          break; */
        //case 'Tron':
        case 'Sei':
          const seiBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'SEI', balance: seiBalance });
          break;
        case 'Algorand':
          const algoBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'ALGO', balance: algoBalance });
          break;
        case 'XRP':
          const xrpBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'XRP', balance: xrpBalance });
          break;
        case 'Polkadot':
          const polkadotBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'DOT', balance: polkadotBalance });
          break;
        case 'Cronos':
          const cronosBalance = await retrieveBalance('', chain);
          setTokenBalance({ ticker: 'CRO', balance: cronosBalance });
          break;
        default:
          setTokenBalance({ ticker: '', balance: 0 });
      }
    } catch (error) {
      console.error('Error retrieving balance:', error);
      setTokenBalance({ ticker: '', balance: 0 });
    }
  };

  const handleLogout = async (loginType: string) => {
    if (blockchain && blockchain.toLowerCase() == 'solana') {
      //solLogout(loginType);
    } else {
      try {
        await updateStatus('offline');
      } catch (e) {
        console.error('Failed to update user status to offline:', e);
      }
      logOut(loginType);
    }
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const clickHandler = (event: MouseEvent) => {
      // If the click is inside the message or notification panel or on their toggling buttons, do nothing
      if (
        notificationPanelRef.current?.contains(event.target as Node) ||
        notificationBellRef.current?.contains(event.target as Node)
      ) {
        return;
      }
      setShowNotifications(false);
    };

    document.addEventListener("mousedown", clickHandler);
    return () => document.removeEventListener("mousedown", clickHandler);
  }, []);

  // Close dropdowns when pressing ESC
  useEffect(() => {
    const keyHandler = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setShowNotifications(false);
      }
    };

    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  }, []);

  // Update user status when user is online/offline
  useEffect(() => {
    if (isAuthenticated && user?.user_id) {
      updateStatus(isIdle ? 'offline' : 'online');
    }
  }, [isAuthenticated, isIdle, user?.user_id, updateStatus]);

  // Listen for status updates
  useEffect(() => {
    if (!socket || !isSocketConnected) return;

    const handleStatusUpdate = (data: any) => {
      dispatch(setStatus({
        userId: data.userId,
        status: data.status
      }));
      dispatch(setLastActive({
        userId: data.userId,
        lastActive: data.last_active
      }));
    };

    socket.on('userStatusUpdate', handleStatusUpdate);
    socket.on('userStatusChanged', handleStatusUpdate);

    return () => {
      socket.off('userStatusUpdate', handleStatusUpdate);
      socket.off('userStatusChanged', handleStatusUpdate);
    };
  }, [socket, isSocketConnected, dispatch]);

  useEffect(() => {
    if (blockchain == 'Solana') {
      setUserWalletAddress(solWalletAddress);
      setUserConnecting(isSolConnecting);
      //setUserConnected(isSolConnected);
    } else {
      setUserWalletAddress(walletAddress);
      setUserConnecting(isConnecting);
      //setUserConnected(isConnected);
    }
  }, [blockchain, isConnected, isSolConnected]);

  useEffect(() => {
    const handleBeforeUnload = () => {
      dispatch(clearAllUsers());
      dispatch(clearNotifications());
      dispatch(setWallet({
        isConnecting: false,
        isSolConnecting: false
      }))
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [dispatch]);

  useEffect(() => {
    if (userWalletAddress) {
      getCurrentBalance(blockchain);
    }
  }, [userWalletAddress]);

  /* Websockets */
  useEffect(() => {
    if (!socket || !isSocketConnected) {
      console.error('Frontend: Socket not available or not connected');
      return;
    }

    if (socket && isSocketConnected) {
      socket.emit('userConnected', user?.user_id);
    }

    const notificationAudioInstance = new Audio('/media/notify.mp3') as HTMLAudioElement;
    notificationAudioInstance.load();
    setNotificationAudio(notificationAudioInstance);

    socket.on('newGiveaway', (data) => {
      if (accountType === 'verified') {
        //fetchNotifications();
        if (notificationAudio) {
          notificationAudio.play();
        }

        Toaster.info(
          {
            url: data.details?.imageUrl,
            title: data.details?.prize,
            text: 'A new giveaway is starting now!',
          },
          { autoClose: 3000 }
        );
      }
    });

    socket.on('newMessage', (data) => {
      if (user?.user_id && data.userId == user?.user_id) {
        const { title, message, icon, link } = data;
        // fetchMessages(user?.user_id);
        // fetchNotifications(user?.user_id);
        if (notificationAudio) {
          notificationAudio.play();
        }
        Toaster.info(
          {
            url: icon,
            title: title,
            text: message,
          },
          { autoClose: 3000 }
        );
      }
    });

    socket.on('newReply', (data) => {
      if (userWalletAddress && data.address === userWalletAddress) {
        if (!visibleConversations.has(data.conversationId)) {
          // fetchMessages(user?.user_id);
          // fetchNotifications(user?.user_id);
          if (notificationAudio) {
            notificationAudio.play();
          }

          Toaster.info(
            {
              url: data.icon,
              title: data.senderInfo?.username || truncateAddress(data.senderAddress),
              text: `Replied to your message.`,
            },
            { autoClose: 3000 }
          );
        }
      }
    });

    socket.on('setActiveConversation', (data) => {
      if (user?.user_id && data.userId === user?.user_id) {
        dispatch(setActiveConversation({ userId: data.userId, conversationId: data.conversationId }));
      }
    });

    socket.on('clearActiveConversation', (data) => {
      if (user?.user_id && data.userId === user?.user_id) {
        dispatch(clearActiveConversation({ userId: data.userId }));
      }
    });

    socket.on('userStatusUpdate', (data: any) => {
      if (user?.user_id && data.userId === user?.user_id) {
        dispatch(setStatus({ userId: data.userId, status: data.status }));
        dispatch(setLastActive({ userId: data.userId, lastActive: data.last_active }));
      }
    });

    socket.on('creatorStatusUpdate', (data: any) => {
      if (user?.user_id && data.userId === user?.user_id) {
        dispatch(setStatus({ userId: data.userId, status: data.status }));
        dispatch(setLastActive({ userId: data.userId, lastActive: data.last_active }));
      }
    });

    socket.on('reduxStateUpdate', (data: any) => {
      if (data.instruction == 'resetTransactionState' && data.userId == user?.user_id) {
        dispatch(resetConfirmations());
      }
    });

    return () => {
      socket.off('userStatusChanged');
      socket.off('userStatusUpdate');
      socket.off('creatorStatusUpdate');
      socket.off('userConnected');
      socket.off('newGiveaway');
      socket.off('newMessage');
      socket.off('newReply');
      socket.off('setActiveConversation');
      socket.off('clearActiveConversation');
      socket.off('reduxStateUpdate');
    };
  }, [dispatch, user?.user_id, isConnected, isSolConnected, visibleConversations]);

  useEffect(() => {
    const loadSession = async () => {
      setSessionLoading(true);
      if (!isAuthenticated && (!isConnected || !isSolConnected)) {
        handleLogout('wallet');
      }
      setSessionLoading(false);
    };

    loadSession();
  }, [isAuthenticated, isConnected, isSolConnected]);

  const marqueeItems = [
    { text: "From April 22nd to May 22nd, official Sugar Club representatives will be scouting and contacting models they feel have potential to be among the first 1,000 to join our platform and receive a free diamond account, which offers 90% earnings for 1 year, amongst other soon to be announced features & perks. If you feel you've got what it takes, feel free to fill out and submit our online", linkText: "model application", link: "", color: "text-gray-600" },
  ];

  return (
    <nav
      ref={navRef}
      className={cn(
        "fixed bg-white dark:bg-[#18181b] top-0 w-full border-solid border-t-0 border-l-0 border-r-0 border-b border-gray-300 dark:border-white/50 z-10 py-2 pb-0 -mb-[81px]",
        {
          "relative": !isSticky,
        }
      )}
    >
      <div className="container mx-auto px-4 mb-2">
        <div className="flex items-center justify-between h-20 gap-4">
          {/* Logo - Fixed width */}
          <div className="w-40 flex justify-start">
            <Link href="/" className="text-2xl font-bold text-foreground">
              <Skeleton loading={loading} width="100%" height="72px">
                <Image placeholder="blur" blurDataURL="/images/logo.webp" src="/images/logo.webp" alt="Sugar Club Logo" width={100} height={100} onLoad={() => setLoading(false)} />
              </Skeleton>
            </Link>
          </div>

          {/* Search bar - Matches main content width */}
          <div className="grow max-w-4xl">
            <SearchBar />
          </div>

          {/* Theme, Language and Auth - Dynamic width */}
          <div className="flex items-center justify-end space-x-3">
            <ThemeToggle />
            <LanguageSelector />

            {sessionLoading ? (
              // Show loading skeleton while session is being determined
              <div className="flex items-center space-x-3">
                <Skeleton className="w-24 h-10 rounded-full" />
                <Skeleton className="w-24 h-10 rounded-full" />
              </div>
            ) : isLoggedIn ? (
              <>
                {/* Notifications Icon */}
                <div className="relative flex-shrink-0">
                  <button
                    ref={notificationBellRef}
                    onClick={toggleNotifications}
                    className={`w-11 h-11 flex items-center justify-center p-2 rounded-full border-solid border-[1px] border-gray-300 dark:border-gray-300 focus-visible:border-solid focus-visible:border notification-bell ${unreadOtherNotifications.length > 99 ? "over-ninety-nine" : unreadOtherNotifications.length > 9 ? "over-nine" : ""}`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" className="fill-gorilla-gray dark:fill-white w-6 h-6 bell">
                      <path d="M224 0c-17.7 0-32 14.3-32 32l0 19.2C119 66 64 130.6 64 208l0 18.8c0 47-17.3 92.4-48.5 127.6l-7.4 8.3c-8.4 9.4-10.4 22.9-5.3 34.4S19.4 416 32 416l384 0c12.6 0 24-7.4 29.2-18.9s3.1-25-5.3-34.4l-7.4-8.3C401.3 319.2 384 273.9 384 226.8l0-18.8c0-77.4-55-142-128-156.8L256 32c0-17.7-14.3-32-32-32zm45.3 493.3c12-12 18.7-28.3 18.7-45.3l-64 0-64 0c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7z" />
                    </svg>
                    {unreadOtherNotifications.length > 0 && (
                      <i className="notification-alert-icon">
                        {unreadOtherNotifications.length > 99 ? "99+" : unreadOtherNotifications.length}
                      </i>
                    )}
                  </button>
                  {showNotifications && (
                    <div className="z-[3] absolute -right-24 top-11 mt-2 w-64 bg-white/80 dark:bg-[#18181b]/80 backdrop-blur-md rounded-lg pt-3 border-solid border-black/20 dark:border-white/20">
                      {unreadOtherNotifications.length === 0 ? (
                        <p className="w-full text-center text-[#18181b] dark:text-white text-md p-2 uppercase">No notifications yet</p>
                      ) : (
                        unreadOtherNotifications.map((notif: any) => (
                          <div key={notif.id} className="p-2 border-b border-gray-800 flex items-center">
                            <Image src={notif.image} width={40} height={40} alt="Notif" className="rounded-full mr-2" />
                            <div className="flex flex-col">
                              <p className="text-white text-sm">{notif.title}</p>
                              <p className="text-gray-400 text-xs">{notif.time}</p>
                            </div>
                          </div>
                        ))
                      )}
                      <Link href="/notifications" className="w-full inline-block uppercase font-bold py-2 bg-gray-100 dark:bg-zinc-900 border-t border-r-0 border-l-0 border-b-0 rounded rounded-t-none border-solid border-black/20 dark:border-white/20 text-gray-600 dark:text-white text-md text-center mt-2 cursor-pointer">View all notifications</Link>
                    </div>
                  )}
                </div>

                <UserDropdown />
              </>
            ) : (
              <>
                <Button
                  onClick={openLoginPopup}
                  className="text-sm px-6 py-2 rounded-lg bg-turquoise hover:bg-turquoise/80 text-white h-10 uppercase"
                >
                  Login
                </Button>
                <Button
                  onClick={openSignUpPopup}
                  className="text-sm px-6 py-2 rounded-lg border-solid border-gray-300 dark:border-white text-foreground bg-transparent h-10 uppercase"
                >
                  Join Free
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {isHomePage && <Marquee items={marqueeItems} speed={45} />}

      {isLoginOpen && (
        <LoginPopup
          isOpen={isLoginOpen}
          onClose={() => setIsLoginOpen(false)}
          handleOpenSignUpPopup={() => {
            setIsLoginOpen(false);
            setIsSignUpOpen(true);
          }}
        />
      )}

      {isSignUpOpen && (
        <SignUpDialog
          isOpen={isSignUpOpen}
          onClose={() => setIsSignUpOpen(false)}
          handleOpenLoginPopup={() => {
            setIsSignUpOpen(false);
            setIsLoginOpen(true);
          }}
        />
      )}
    </nav>
  );
};
export default Header;