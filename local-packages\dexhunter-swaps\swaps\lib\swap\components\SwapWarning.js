import { jsx as r, Fragment as a, jsxs as s } from "react/jsx-runtime";
import d from "../../components/ui/tooltipDialog.js";
import { u as m } from "../../useQuery-febd7967.js";
import { a as n } from "../../axios-ddd885c5.js";
import { useMemo as p } from "react";
import { cn as h } from "../../lib/utils.js";
import "../../hooks/useScreen.js";
import "../../components/ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../components/ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../query-013b86c3.js";
import "../../QueryClientProvider-6bcd4331.js";
import "../../index-ca8eb9e1.js";
const $ = () => {
  const { data: i, isLoading: e } = m({
    queryKey: ["dh-chain-load"],
    queryFn: async () => {
      try {
        const { data: t } = await n.get("https://pool.pm/total.json");
        return Math.round(
          Math.max(t == null ? void 0 : t.load_1h, t == null ? void 0 : t.load_5m) * 100
        );
      } catch {
        return 0;
      }
    },
    refetchInterval: 1e4
  }), o = p(() => i || 0, [i]);
  return /* @__PURE__ */ r(a, { children: !e && o >= 90 && /* @__PURE__ */ r("div", { className: "dhs-flex dhs-w-full dhs-py-[2px] dhs-px-5 dhs-justify-center dhs-items-center dhs-gap-2 dhs-h-[30px] dhs-font-proximaBold dhs-text-warning", children: /* @__PURE__ */ r(
    d,
    {
      trigger: /* @__PURE__ */ r(
        "div",
        {
          className: h(
            "dhs-flex dhs-rounded-[15px] dhs-hover:bg-gray-109 dhs-hover:bg-opacity-30 dhs-text-accent dhs-font-proximaBold dhs-gap-1 dhs-items-center dhs-text-md",
            o >= 90 && "dhs-text-red-101"
          ),
          children: /* @__PURE__ */ s("span", { children: [
            "High chain load at ",
            o.toFixed(0),
            "%. Swaps may take longer."
          ] })
        }
      ),
      content: `Cardano chain load is ${o.toFixed(0)}%`,
      contentClass: "dhs-text-white"
    }
  ) }) });
};
export {
  $ as default
};
