import { jsx as V } from "react/jsx-runtime";
import * as Y from "react";
import { forwardRef as L, createElement as p, useRef as T, useState as I, useCallback as v, useEffect as y, useMemo as q } from "react";
import { _ as w, a as F, b as X } from "../../index-1c873780.js";
import { a as J, $ as C } from "../../index-c7156e07.js";
import { $ as Q } from "../../index-563d1ed8.js";
import { a as W, $ as Z } from "../../index-67500cd3.js";
import { $ as ee } from "../../index-4914f99c.js";
import { $ as j, a as te, b as oe, c as ne } from "../../index-0ce202b9.js";
import { $ as B } from "../../index-5116e957.js";
import { $ as re } from "../../index-c8f2666b.js";
import { $ as ae } from "../../index-f7426637.js";
import { cn as ce } from "../../lib.js";
import "../../index-bcfeaad9.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const [R, Ve] = Q("Tooltip", [
  j
]), A = j(), se = "TooltipProvider", ie = 700, M = "tooltip.open", [le, k] = R(se), de = (t) => {
  const { __scopeTooltip: o, delayDuration: e = ie, skipDelayDuration: n = 300, disableHoverableContent: r = !1, children: c } = t, [s, l] = I(!0), a = T(!1), d = T(0);
  return y(() => {
    const i = d.current;
    return () => window.clearTimeout(i);
  }, []), /* @__PURE__ */ p(le, {
    scope: o,
    isOpenDelayed: s,
    delayDuration: e,
    onOpen: v(() => {
      window.clearTimeout(d.current), l(!1);
    }, []),
    onClose: v(() => {
      window.clearTimeout(d.current), d.current = window.setTimeout(
        () => l(!0),
        n
      );
    }, [
      n
    ]),
    isPointerInTransitRef: a,
    onPointerInTransitChange: v((i) => {
      a.current = i;
    }, []),
    disableHoverableContent: r
  }, c);
}, H = "Tooltip", [pe, O] = R(H), ue = (t) => {
  const { __scopeTooltip: o, children: e, open: n, defaultOpen: r = !1, onOpenChange: c, disableHoverableContent: s, delayDuration: l } = t, a = k(H, t.__scopeTooltip), d = A(o), [i, u] = I(null), h = ee(), f = T(0), $ = s ?? a.disableHoverableContent, b = l ?? a.delayDuration, x = T(!1), [g = !1, m] = J({
    prop: n,
    defaultProp: r,
    onChange: (S) => {
      S ? (a.onOpen(), document.dispatchEvent(new CustomEvent(M))) : a.onClose(), c == null || c(S);
    }
  }), P = q(() => g ? x.current ? "delayed-open" : "instant-open" : "closed", [
    g
  ]), _ = v(() => {
    window.clearTimeout(f.current), x.current = !1, m(!0);
  }, [
    m
  ]), E = v(() => {
    window.clearTimeout(f.current), m(!1);
  }, [
    m
  ]), N = v(() => {
    window.clearTimeout(f.current), f.current = window.setTimeout(() => {
      x.current = !0, m(!0);
    }, b);
  }, [
    b,
    m
  ]);
  return y(() => () => window.clearTimeout(f.current), []), /* @__PURE__ */ p(oe, d, /* @__PURE__ */ p(pe, {
    scope: o,
    contentId: h,
    open: g,
    stateAttribute: P,
    trigger: i,
    onTriggerChange: u,
    onTriggerEnter: v(() => {
      a.isOpenDelayed ? N() : _();
    }, [
      a.isOpenDelayed,
      N,
      _
    ]),
    onTriggerLeave: v(() => {
      $ ? E() : window.clearTimeout(f.current);
    }, [
      E,
      $
    ]),
    onOpen: _,
    onClose: E,
    disableHoverableContent: $
  }, e));
}, G = "TooltipTrigger", fe = /* @__PURE__ */ L((t, o) => {
  const { __scopeTooltip: e, ...n } = t, r = O(G, e), c = k(G, e), s = A(e), l = T(null), a = F(o, l, r.onTriggerChange), d = T(!1), i = T(!1), u = v(
    () => d.current = !1,
    []
  );
  return y(() => () => document.removeEventListener("pointerup", u), [
    u
  ]), /* @__PURE__ */ p(ne, w({
    asChild: !0
  }, s), /* @__PURE__ */ p(re.button, w({
    // We purposefully avoid adding `type=button` here because tooltip triggers are also
    // commonly anchors and the anchor `type` attribute signifies MIME type.
    "aria-describedby": r.open ? r.contentId : void 0,
    "data-state": r.stateAttribute
  }, n, {
    ref: a,
    onPointerMove: C(t.onPointerMove, (h) => {
      h.pointerType !== "touch" && !i.current && !c.isPointerInTransitRef.current && (r.onTriggerEnter(), i.current = !0);
    }),
    onPointerLeave: C(t.onPointerLeave, () => {
      r.onTriggerLeave(), i.current = !1;
    }),
    onPointerDown: C(t.onPointerDown, () => {
      d.current = !0, document.addEventListener("pointerup", u, {
        once: !0
      });
    }),
    onFocus: C(t.onFocus, () => {
      d.current || r.onOpen();
    }),
    onBlur: C(t.onBlur, r.onClose),
    onClick: C(t.onClick, r.onClose)
  })));
}), z = "TooltipPortal", [$e, he] = R(z, {
  forceMount: void 0
}), xe = (t) => {
  const { __scopeTooltip: o, forceMount: e, children: n, container: r } = t, c = O(z, o);
  return /* @__PURE__ */ p($e, {
    scope: o,
    forceMount: e
  }, /* @__PURE__ */ p(B, {
    present: e || c.open
  }, /* @__PURE__ */ p(Z, {
    asChild: !0,
    container: r
  }, n)));
}, D = "TooltipContent", be = /* @__PURE__ */ L((t, o) => {
  const e = he(D, t.__scopeTooltip), { forceMount: n = e.forceMount, side: r = "top", ...c } = t, s = O(D, t.__scopeTooltip);
  return /* @__PURE__ */ p(B, {
    present: n || s.open
  }, s.disableHoverableContent ? /* @__PURE__ */ p(K, w({
    side: r
  }, c, {
    ref: o
  })) : /* @__PURE__ */ p(ve, w({
    side: r
  }, c, {
    ref: o
  })));
}), ve = /* @__PURE__ */ L((t, o) => {
  const e = O(D, t.__scopeTooltip), n = k(D, t.__scopeTooltip), r = T(null), c = F(o, r), [s, l] = I(null), { trigger: a, onClose: d } = e, i = r.current, { onPointerInTransitChange: u } = n, h = v(() => {
    l(null), u(!1);
  }, [
    u
  ]), f = v(($, b) => {
    const x = $.currentTarget, g = {
      x: $.clientX,
      y: $.clientY
    }, m = ge(g, x.getBoundingClientRect()), P = Te(g, m), _ = ye(b.getBoundingClientRect()), E = we([
      ...P,
      ..._
    ]);
    l(E), u(!0);
  }, [
    u
  ]);
  return y(() => () => h(), [
    h
  ]), y(() => {
    if (a && i) {
      const $ = (x) => f(x, i), b = (x) => f(x, a);
      return a.addEventListener("pointerleave", $), i.addEventListener("pointerleave", b), () => {
        a.removeEventListener("pointerleave", $), i.removeEventListener("pointerleave", b);
      };
    }
  }, [
    a,
    i,
    f,
    h
  ]), y(() => {
    if (s) {
      const $ = (b) => {
        const x = b.target, g = {
          x: b.clientX,
          y: b.clientY
        }, m = (a == null ? void 0 : a.contains(x)) || (i == null ? void 0 : i.contains(x)), P = !Ce(g, s);
        m ? h() : P && (h(), d());
      };
      return document.addEventListener("pointermove", $), () => document.removeEventListener("pointermove", $);
    }
  }, [
    a,
    i,
    s,
    d,
    h
  ]), /* @__PURE__ */ p(K, w({}, t, {
    ref: c
  }));
}), [me, Ye] = R(H, {
  isInside: !1
}), K = /* @__PURE__ */ L((t, o) => {
  const { __scopeTooltip: e, children: n, "aria-label": r, onEscapeKeyDown: c, onPointerDownOutside: s, ...l } = t, a = O(D, e), d = A(e), { onClose: i } = a;
  return y(() => (document.addEventListener(M, i), () => document.removeEventListener(M, i)), [
    i
  ]), y(() => {
    if (a.trigger) {
      const u = (h) => {
        const f = h.target;
        f != null && f.contains(a.trigger) && i();
      };
      return window.addEventListener("scroll", u, {
        capture: !0
      }), () => window.removeEventListener("scroll", u, {
        capture: !0
      });
    }
  }, [
    a.trigger,
    i
  ]), /* @__PURE__ */ p(W, {
    asChild: !0,
    disableOutsidePointerEvents: !1,
    onEscapeKeyDown: c,
    onPointerDownOutside: s,
    onFocusOutside: (u) => u.preventDefault(),
    onDismiss: i
  }, /* @__PURE__ */ p(te, w({
    "data-state": a.stateAttribute
  }, d, l, {
    ref: o,
    style: {
      ...l.style,
      "--radix-tooltip-content-transform-origin": "var(--radix-popper-transform-origin)",
      "--radix-tooltip-content-available-width": "var(--radix-popper-available-width)",
      "--radix-tooltip-content-available-height": "var(--radix-popper-available-height)",
      "--radix-tooltip-trigger-width": "var(--radix-popper-anchor-width)",
      "--radix-tooltip-trigger-height": "var(--radix-popper-anchor-height)"
    }
  }), /* @__PURE__ */ p(X, null, n), /* @__PURE__ */ p(me, {
    scope: e,
    isInside: !0
  }, /* @__PURE__ */ p(ae, {
    id: a.contentId,
    role: "tooltip"
  }, r || n))));
});
function ge(t, o) {
  const e = Math.abs(o.top - t.y), n = Math.abs(o.bottom - t.y), r = Math.abs(o.right - t.x), c = Math.abs(o.left - t.x);
  switch (Math.min(e, n, r, c)) {
    case c:
      return "left";
    case r:
      return "right";
    case e:
      return "top";
    case n:
      return "bottom";
    default:
      throw new Error("unreachable");
  }
}
function Te(t, o, e = 5) {
  const n = [];
  switch (o) {
    case "top":
      n.push({
        x: t.x - e,
        y: t.y + e
      }, {
        x: t.x + e,
        y: t.y + e
      });
      break;
    case "bottom":
      n.push({
        x: t.x - e,
        y: t.y - e
      }, {
        x: t.x + e,
        y: t.y - e
      });
      break;
    case "left":
      n.push({
        x: t.x + e,
        y: t.y - e
      }, {
        x: t.x + e,
        y: t.y + e
      });
      break;
    case "right":
      n.push({
        x: t.x - e,
        y: t.y - e
      }, {
        x: t.x - e,
        y: t.y + e
      });
      break;
  }
  return n;
}
function ye(t) {
  const { top: o, right: e, bottom: n, left: r } = t;
  return [
    {
      x: r,
      y: o
    },
    {
      x: e,
      y: o
    },
    {
      x: e,
      y: n
    },
    {
      x: r,
      y: n
    }
  ];
}
function Ce(t, o) {
  const { x: e, y: n } = t;
  let r = !1;
  for (let c = 0, s = o.length - 1; c < o.length; s = c++) {
    const l = o[c].x, a = o[c].y, d = o[s].x, i = o[s].y;
    a > n != i > n && e < (d - l) * (n - a) / (i - a) + l && (r = !r);
  }
  return r;
}
function we(t) {
  const o = t.slice();
  return o.sort((e, n) => e.x < n.x ? -1 : e.x > n.x ? 1 : e.y < n.y ? -1 : e.y > n.y ? 1 : 0), Pe(o);
}
function Pe(t) {
  if (t.length <= 1)
    return t.slice();
  const o = [];
  for (let n = 0; n < t.length; n++) {
    const r = t[n];
    for (; o.length >= 2; ) {
      const c = o[o.length - 1], s = o[o.length - 2];
      if ((c.x - s.x) * (r.y - s.y) >= (c.y - s.y) * (r.x - s.x))
        o.pop();
      else
        break;
    }
    o.push(r);
  }
  o.pop();
  const e = [];
  for (let n = t.length - 1; n >= 0; n--) {
    const r = t[n];
    for (; e.length >= 2; ) {
      const c = e[e.length - 1], s = e[e.length - 2];
      if ((c.x - s.x) * (r.y - s.y) >= (c.y - s.y) * (r.x - s.x))
        e.pop();
      else
        break;
    }
    e.push(r);
  }
  return e.pop(), o.length === 1 && e.length === 1 && o[0].x === e[0].x && o[0].y === e[0].y ? o : o.concat(e);
}
const _e = de, Ee = ue, De = fe, Oe = xe, U = be, qe = _e, Xe = Ee, Je = De, Qe = Oe, Le = Y.forwardRef(({ className: t, sideOffset: o = 4, ...e }, n) => /* @__PURE__ */ V(
  U,
  {
    ref: n,
    sideOffset: o,
    className: ce(
      "dhs-z-50 dhs-max-h-[170px] dhs-overflow-auto dhs-border dhs-bg-popover dhs-px-3 dhs-py-1.5 dhs-text-sm dhs-text-buttonText dhs-shadow-md dhs-animate-in dhs-fade-in-0 dhs-zoom-in-95 data-[state=closed]:dhs-animate-out data-[state=closed]:dhs-fade-out-0 data-[state=closed]:dhs-zoom-out-95 data-[side=bottom]:dhs-slide-in-from-top-2 data-[side=left]:dhs-slide-in-from-right-2 data-[side=right]:dhs-slide-in-from-left-2 data-[side=top]:dhs-slide-in-from-bottom-2 dhs-border-none dhs-rounded-2xl dhs-max-w-[320px] dhs-font-proximaMedium",
      t
    ),
    style: {
      background: "var(--glass-effect, linear-gradient(135deg, rgba(48, 50, 59, 1) 0%, rgba(59, 61, 73, 1) 100%))",
      boxShadow: "0px 4px 50px 0px rgba(0, 0, 0, 0.25)",
      backdropFilter: "dhs-blur(22px)"
    },
    ...e
  }
));
Le.displayName = U.displayName;
export {
  Xe as Tooltip,
  Le as TooltipContent,
  Qe as TooltipPortal,
  qe as TooltipProvider,
  Je as TooltipTrigger
};
