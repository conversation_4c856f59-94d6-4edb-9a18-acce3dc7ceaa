/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect, useRef } from 'react';
import { updateUserSettings } from '@/lib/api/updateUserSettings';
import { toast } from 'react-toastify';
import StyledButton from "@/components/ui/styled-button";
import { Label } from "@/components/ui/label";
import { motion } from 'framer-motion';
import { debounce } from 'lodash';
import { LabelInputContainer } from '@/components/ui/label-input-container';
import { useDispatch } from 'react-redux';
import { setUserInfo } from '@/redux/slices/userInfoSlice';

interface ProfileTabProps {
  userData: any;
  onFormChange: (hasChanges: boolean, updatedData?: any) => void;
};

interface LocationSuggestion {
  display_name: string;
  place_id: number;
  lat: string;
  lon: string;
  type: string;
}

const ProfileTab = ({ userData, onFormChange }: ProfileTabProps) => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    displayName: userData?.profile?.displayName || '',
    bio: userData?.profile?.bio || '',
    countryOfOrigin: userData?.profile?.countryOfOrigin || '',
    latitude: userData?.profile?.latitude || '',
    longitude: userData?.profile?.longitude || '',
    amazonWishlist: userData?.profile?.amazonWishlist || '',
  });
  const [locationSuggestions, setLocationSuggestions] = useState<LocationSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);

  const initialFormData = useRef(formData);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  const [amazonUrlError, setAmazonUrlError] = useState<string | null>(null);

  // Validate Amazon URL
  const validateAmazonUrl = (url: string): boolean => {
    if (!url) return true; // Empty is valid (not required)

    // Check if it's a valid URL
    try {
      new URL(url);
    } catch (e) {
      setAmazonUrlError('Please enter a valid URL');
      return false;
    }

    // Check if it's an Amazon URL
    const amazonDomains = [
      'amazon.com', 'amazon.co.uk', 'amazon.ca', 'amazon.de', 'amazon.fr',
      'amazon.it', 'amazon.es', 'amazon.co.jp', 'amazon.cn', 'amazon.in',
      'amazon.com.mx', 'amazon.com.br', 'amazon.com.au', 'amazon.nl', 'amazon.sg',
      'amazon.ae', 'amazon.sa', 'amazon.se', 'amazon.pl', 'amazon.eg',
      'amzn.to', 'a.co' // Amazon shortlinks
    ];

    const urlObj = new URL(url);
    const isAmazonUrl = amazonDomains.some(domain =>
      urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
    );

    if (!isAmazonUrl) {
      setAmazonUrlError('Please enter a valid Amazon URL');
      return false;
    }

    setAmazonUrlError(null);
    return true;
  };

  // Create debounced validation function at component level
  const debouncedValidateAmazonUrl = useRef(
    debounce((value: string) => {
      validateAmazonUrl(value);
    }, 300)
  ).current;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    if (name === 'countryOfOrigin' && value.length > 1) { // Reduced to 1 character to start searching earlier
      debouncedFetchLocations(value);
      setShowSuggestions(true);
    } else if (name === 'countryOfOrigin') {
      setLocationSuggestions([]);
      setShowSuggestions(false);
    } else if (name === 'amazonWishlist') {
      debouncedValidateAmazonUrl(value);
    }
  };

  // Fetch location suggestions from Nominatim API
  const fetchLocationSuggestions = async (query: string) => {
    setIsLoadingSuggestions(true);
    try {
      const response = await fetch(
        `https://api.locationiq.com/v1/autocomplete?key=${process.env.NEXT_PUBLIC_LOCATION_IQ_API_KEY}&q=${encodeURIComponent(query)}&limit=10&countrycodes=true`,
        {
          headers: {
            'Accept-Language': 'en',
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch location suggestions');
      }

      const data = await response.json();
      console.log({ data });

      const countries = data
        .filter((item: any) => item.type === 'country')
        .map((item: any) => ({
          place_id: item.place_id,
          display_name: item.display_name.split(',')[0].trim(),
          lat: item.lat,
          lon: item.lon,
          type: item.type
        }));

      console.log({ countries });
      setLocationSuggestions(countries);
    } catch (error: any) {
      console.error('Error fetching location suggestions:', error);
      toast.error('Error fetching location suggestions. Please try again.');
      setLocationSuggestions([]);
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  // Debounce the API call to prevent too many requests
  const debouncedFetchLocations = useRef(
    debounce((query: string) => {
      fetchLocationSuggestions(query);
    }, 300)
  ).current;

  const handleLocationSelect = (suggestion: LocationSuggestion) => {
    setFormData(prev => ({
      ...prev,
      countryOfOrigin: suggestion.display_name,
      latitude: suggestion.lat,
      longitude: suggestion.lon
    }));
    setShowSuggestions(false);
  };

  const handleSave = async () => {
    // Validate Amazon URL before saving
    if (!validateAmazonUrl(formData.amazonWishlist)) {
      toast.error('Please enter a valid Amazon Wishlist URL');
      return;
    }

    setIsLoading(true);
    try {
      const result = await updateUserSettings(userData.userId, userData.accountType, 'profile', { profile: formData });
      if (result.success) {
        // Update Redux store with new display name
        dispatch(setUserInfo({ ...userData, displayName: formData.displayName }));

        toast.success('Changes to account settings saved successfully!');
        initialFormData.current = { ...formData };
        if (onFormChange) onFormChange(false);
      } else {
        throw new Error('Failed to update settings');
      }
    } catch (error: any) {
      console.error(error);
      toast.error(error?.message || 'An error occurred while saving settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Check for changes whenever formData changes
  useEffect(() => {
    const changed = JSON.stringify(initialFormData.current) !== JSON.stringify(formData);

    // Notify parent component about changes
    if (onFormChange) {
      onFormChange(changed);
    }
  }, [formData, onFormChange]);

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedFetchLocations.cancel();
      debouncedValidateAmazonUrl.cancel();
    };
  }, [debouncedFetchLocations, debouncedValidateAmazonUrl]);

  return (
    <motion.div
      key="privacy"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="space-y-6"
    >
      {/* Profile Info Label and Divider */}
      <LabelInputContainer className="mb-6">
        <h3 className="text-xl font-semibold text-[#121212] dark:text-white mb-1">Profile Info</h3>
        <div className="mb-6 border border-solid border-b border-l-0 border-r-0 border-t-0 border-black/60 dark:border-white/60"></div>
      </LabelInputContainer>

      {/* Display Name Input */}
      <LabelInputContainer>
        <Label htmlFor="displayName">Display Name</Label>
        <input
          id="displayName"
          name="displayName"
          placeholder="Enter your display name"
          value={formData.displayName}
          onChange={handleInputChange}
          className="flex h-10 w-full border-none bg-gray-100 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
        />
      </LabelInputContainer>

      {/* Bio Input */}
      <LabelInputContainer>
        <Label htmlFor="bio">Bio</Label>
        <textarea
          id="bio"
          name="bio"
          placeholder="Tell us about yourself"
          value={formData.bio}
          onChange={handleInputChange}
          style={{ resize: 'none' }}
          className="flex w-full h-40 border-none bg-gray-100 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
        />
      </LabelInputContainer>

      {/* Nationality Input */}
      <LabelInputContainer>
        <Label htmlFor="countryOfOrigin">Nationality</Label>
        <div className="relative">
          <input
            id="countryOfOrigin"
            name="countryOfOrigin"
            placeholder="Select the country you were born in"
            value={formData.countryOfOrigin}
            onChange={handleInputChange}
            autoComplete="off"
            className="flex h-10 w-full border-none bg-gray-100 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400"
          />
          {showSuggestions && (
            <div
              ref={suggestionsRef}
              className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto"
            >
              {isLoadingSuggestions ? (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  Loading suggestions...
                </div>
              ) : locationSuggestions.length > 0 ? (
                locationSuggestions.map((suggestion) => (
                  <div
                    key={suggestion.place_id}
                    className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm"
                    onClick={() => handleLocationSelect(suggestion)}
                  >
                    {suggestion.display_name}
                  </div>
                ))
              ) : (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  No countries found. Try typing something different.
                </div>
              )}
            </div>
          )}
        </div>
      </LabelInputContainer>

      {/* Amazon Wishlist Input */}
      <LabelInputContainer>
        <Label htmlFor="amazonWishlist">Amazon Wishlist</Label>
        <input
          id="amazonWishlist"
          name="amazonWishlist"
          placeholder="Enter your Amazon Wishlist URL"
          value={formData.amazonWishlist}
          onChange={handleInputChange}
          className={`flex h-10 w-full border-none bg-gray-100 dark:bg-zinc-800 text-[#121212] dark:text-white shadow-input rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 dark:placeholder-text-neutral-600 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 dark:focus-visible:ring-neutral-600 disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] group-hover/input:shadow-none transition duration-400 ${amazonUrlError ? 'border-red-500' : ''}`}
        />
        {amazonUrlError && (
          <p className="text-red-500 text-sm mt-1">{amazonUrlError}</p>
        )}
      </LabelInputContainer>

      {/* Save Button */}
      <div className="flex justify-start mt-6">
        <StyledButton
          onClick={handleSave}
          disabled={isLoading || !!amazonUrlError}
          className={`!text-sm w-40 h-10 flex items-center justify-center !p-0 ${isLoading ? 'hover' : ''}`}
          buttonText={isLoading ? 'Saving...' : 'Save Changes'}
        />
      </div>
    </motion.div>
  );
};
export default ProfileTab;