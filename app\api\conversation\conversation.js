// pages/api/conversation.js
import { v4 as uuidv4 } from 'uuid';
import xss from 'xss';
import * as MainJS from '../../public/main';
import messaging from '../../utils/Notifications/firebaseAdmin';
import { createClient } from '@supabase/supabase-js';
import cloudinary from 'cloudinary';
import { IncomingForm } from 'formidable';
import { io } from 'socket.io-client';

const socket = io('https://beneficial-mirelle-websocket-server-8923033b.koyeb.app/');

cloudinary.config({
  cloud_name: 'di5nu3rmw',
  api_key: '693629495139743',
  api_secret: 'qTrFryQdt37tinNSkDH-0gSLlTo',
  secure: true,
});

// Initialize Supabase client
const mmSupabase = createClient(process.env.MM_SUPABASE_URL, process.env.MM_SUPABASE_API_KEY);

export const config = {
  api: {
    bodyParser: false,
  },
};

async function parseJsonBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(JSON.parse(body));
      } catch (error) {
        reject(error);
      }
    });
  });
};

async function deleteFromCloudinary(publicId, resourceType = 'auto') {
  return new Promise((resolve, reject) => {
    cloudinary.v2.uploader.destroy(publicId, { resource_type: resourceType }, (error, result) => {
      if (error) {
        console.error(`Cloudinary deletion error for ${publicId}:`, error);
        reject(error);
      } else {
        console.log(`Cloudinary deletion result for ${publicId}:`, result);
        resolve(result);
      }
    });
  });
};

async function getUserId(userAddress) {
    try {
        const { data: userIdQuery, error: userIdError } = await mmSupabase
            .from('UserWallets')
            .select('local_user_id')
            .eq('address', userAddress);

        if (userIdError) {
            throw new Error(`Error fetching user ID:, ${userIdError}`);
        }

        return userIdQuery ? userIdQuery[0]?.local_user_id : null;
    } catch (err) {
        console.error(err);
    }
};

async function getUserInfo(userId) {
  try {
    const { data: user, error: userError } = await mmSupabase
      .from('Users')
      .select('user_info')
      .eq('local_user_id', userId)
      .single();

    if (userError) {
      throw userError;
    }

    return user || null;
  } catch (err) {
    console.error(err);
    return null;
  }
};

const getUserAddress = async (user_id) => {
  try {
    const { data: user, error: userError } = await mmSupabase
      .from('UserWallets')
      .select('address')
      .eq('local_user_id', user_id)
      .eq('is_primary', true)
      .eq('blockchain', 'Cardano')
      .limit(1);

    if (userError) {
      throw userError;
    }

    return user[0] || null;
  } catch (err) {
    console.error(err);
    return null;
  }
};

const getGlobalUserId = async (userAddress) => {
  try {
    const { data: userIdQuery, error: userIdError } = await mmSupabase
      .from('UserWallets')
      .select('global_user_id')
      .eq('address', userAddress);

    if (userIdError) {
      throw new Error(`Error fetching user ID:, ${userIdError}`);
    }

    return userIdQuery ? userIdQuery[0]?.global_user_id : null;
  } catch (err) {
    console.error(err);
    return null;
  }
};

export default async function handler(req, res) {

  const { headers } = req;
  const authorizationHeader = headers['authorization'];
  const secretToken = process.env.NEXTAUTH_SECRET;

  if (authorizationHeader !== `Bearer ${secretToken}`) {
    res.status(401).json({ message: 'Unauthorized' });
    return;
  }

  const todaysDate = new Date();
  const formattedDate = todaysDate.toISOString();

  if (req.method === 'GET') {
    const { address, conversationId, page } = req.query;
    const queryPage = parseInt(page) || 1;
    const itemsPerPage = 50;

    try {
      const userId = await getUserId(address);

      if (!userId) {
        return res.status(404).json({ message: 'User not found' });
      }

      const offset = (queryPage - 1) * itemsPerPage;

      const { data: messages, error: messageError } = await mmSupabase
        .from('Messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('date', { ascending: false })
        .range(offset, offset + itemsPerPage - 1);

      if (messageError) {
        console.error('Error fetching messages:', messageError.message);
        throw new Error(messageError);
      }

      const unreadMessageIds = messages
        .filter(message => message.recipient_status === 'unread' && message.recipient_id === userId)
        .map(message => message.message_id);

      if (unreadMessageIds.length > 0) {
        const { error: updateStatusError } = await mmSupabase
          .from('Messages')
          .update({ recipient_status: 'read' })
          .in('message_id', unreadMessageIds);

        if (updateStatusError) {
          console.error('Error updating message status', updateStatusError.message);
          throw new Error(updateStatusError);
        }

        socket.emit('messageRead', { messageIds: unreadMessageIds, conversationId });
      }

      const messagesWithIcons = await Promise.all(
        messages.map(async (message) => {
          const senderInfo = await getUserInfo(message.sender_id);
          const recipientInfo = await getUserInfo(message.recipient_id);
          const senderAddress = await getUserAddress(message.sender_id);
          const recipientAddress = await getUserAddress(message.recipient_id);

          return {
            ...message,
            senderInfo: senderInfo?.user_info,
            senderAddress: senderAddress?.address,
            recipientInfo: recipientInfo?.user_info,
            recipientAddress: recipientAddress?.address,
          };
        })
      );

      res.status(200).json({ messages: messagesWithIcons });
    } catch (error) {
      console.error('Error fetching messages:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else if (req.method === 'POST') {
    const form = new IncomingForm();

    form.parse(req, async (err, fields, files) => {
      if (err) {
        res.status(500).json({ error: 'Failed to parse form data' });
        return;
      }

      try {
        const { senderAddress, recipientAddress, message, conversationId, subject, attachment_url, attachment_type, attachment_name, attachment_id, activeConversations } = fields;
        const sanitizedMessageData = xss(message);

        const senderId = await getUserId(senderAddress);
        const recipientId = await getUserId(recipientAddress);
        const globalSenderId = await getGlobalUserId(senderAddress);
        const globalRecipientId = await getGlobalUserId(recipientAddress);

        const { data: sender, error: senderError } = await mmSupabase
          .from('Users')
          .select('user_info')
          .eq('local_user_id', senderId)
          .single();

        if (senderError) throw senderError;

        const { data: recipient, error: recipientError } = await mmSupabase
          .from('Users')
          .select('user_info')
          .eq('local_user_id', recipientId)
          .single();

        if (recipientError) throw recipientError;

        const newConversationId = conversationId || uuidv4();

        let conversationSender, conversationRecipient;
        if (!conversationId) {
          conversationSender = senderId;
          conversationRecipient = recipientId;
        } else {
          const { data: conversationData, error: conversationError } = await mmSupabase
            .from('Messages')
            .select('conversation_sender, conversation_recipient')
            .eq('conversation_id', conversationId);

          if (conversationError) throw conversationError;

          conversationSender = conversationData[0]?.conversation_sender;
          conversationRecipient = conversationData[0]?.conversation_recipient;
        }

        const { data: messageData, error: insertError } = await mmSupabase
          .from('Messages')
          .insert([
            {
              global_sender_id: globalSenderId,
              global_recipient_id: globalRecipientId,
              sender_id: senderId,
              recipient_id: recipientId,
              message: sanitizedMessageData,
              date: formattedDate,
              conversation_id: newConversationId,
              recipient_status: 'unread',
              sender_status: 'read',
              attachment_url,
              attachment_type,
              attachment_name,
              attachment_id,
              conversation_sender: conversationSender,
              conversation_recipient: conversationRecipient
            },
          ]).select();

        if (insertError) throw insertError;

        const messageId = messageData[0].message_id;

        const senderInfo = sender?.user_info || {};
        const recipientInfo = recipient?.user_info || {};

        const senderUsername = senderInfo?.username;
        const senderDiscordUsername = senderInfo?.discordUsername;
        const senderProfilePhoto = senderInfo?.profilePhoto;

        const isViewingConversation = activeConversations[recipientAddress] === newConversationId;

        if (!isViewingConversation) {
          const { error: notificationError } = await mmSupabase
            .from('Notifications')
            .insert([
              {
                global_user_id: globalRecipientId,
                local_user_id: recipientId,
                title: senderDiscordUsername || senderUsername || MainJS.truncateAddress(senderAddress),
                message: 'Replied to your message.',
                type: 'message-reply',
                status: 'unread',
                created_at: formattedDate,
                link: `https://chilledkongs.com/user/${recipientAddress}?tab=messages&conversationId=${newConversationId}`,
                icon: senderProfilePhoto || '/images/user/default-avatar.png',
              },
            ]);

          if (notificationError) throw notificationError;

          // Check if watchlist user has notifications enabled
          const desktopPushNotifications = recipientInfo.desktopPushNotifications;
          const mobilePushNotifications = recipientInfo.mobilePushNotifications;

          const { mobileRegistrationToken, desktopRegistrationToken } = recipientInfo;

          if (desktopPushNotifications == 'true' && desktopRegistrationToken) {
            try {
              const message = {
                notification: {
                  title: senderDiscordUsername || senderUsername || MainJS.truncateAddress(senderAddress),
                  body: 'Replied to your message.',
                  imageUrl: senderProfilePhoto,
                },
                data: {
                  linkUrl: `https://chilledkongs.com/user/${recipientAddress}?tab=messages&conversationId=${newConversationId}`,
                },
                token: desktopRegistrationToken,
              };

              const notification = await messaging.send(message);
              console.log(notification);
              console.log('Push notification sent successfully!');
            } catch (error) {
              console.error('Error sending push notification:', error);
            }
          }

          if (mobilePushNotifications == 'true' && mobileRegistrationToken) {
            try {
              const message = {
                notification: {
                  title: senderDiscordUsername || senderUsername || MainJS.truncateAddress(senderAddress),
                  body: 'Replied to your message.',
                  imageUrl: senderProfilePhoto,
                },
                data: {
                  linkUrl: `https://chilledkongs.com/user/${recipientAddress}?tab=messages&conversationId=${newConversationId}`,
                },
                token: desktopRegistrationToken,
              };

              const notification = await messaging.send(message);
              console.log('Push notification sent successfully!');
            } catch (error) {
              console.error('Error sending push notification:', error);
            }
          }
        }

        socket.emit('newReply', {
          address: recipientAddress,
          conversationId: newConversationId,
          link: `https://chilledkongs.com/user/${recipientAddress}?tab=messages&conversationId=${newConversationId}`,
          icon: senderProfilePhoto,
          message: {
            message_id: messageId,
            senderAddress,
            recipientAddress,
            message: sanitizedMessageData,
            date: formattedDate,
            attachment_url,
            attachment_type,
            attachment_name,
            attachment_id,
            recipient_status: 'unread',
            sender_status: 'read',
          },
          senderInfo: senderInfo,
          recipientInfo: recipientInfo
        });

        res.status(201).json({ message: 'Message created successfully', success: true, messageId });
      } catch (error) {
        console.error('Error creating message:', error);
        res.status(500).json({ message: 'Internal server error' });
      }
    });
  } else if (req.method === 'DELETE') {
    try {
      const body = await parseJsonBody(req);
      const { messageId, recipientAddress, senderAddress } = body;

      const { data: messageData, error: messageError } = await mmSupabase
        .from('Messages')
        .select('conversation_id, attachment_id, attachment_type')
        .eq('message_id', messageId)
        .single();

      if (messageError) throw messageError;

      const { conversation_id: conversationId, attachment_id: attachmentId, attachment_type: attachmentType } = messageData;

      if (attachmentId) {
        const resourceType = attachmentType.startsWith('image/') ? 'image' :
          attachmentType.startsWith('video/') ? 'video' :
            attachmentType.startsWith('audio/') ? 'audio' :
              'raw';

        await deleteFromCloudinary(`direct_messages/${attachmentId}`, resourceType);
      }

      // Mark the specific message as deleted
      const { error: updateError } = await mmSupabase
        .from('Messages')
        .update({ is_deleted: true })
        .eq('message_id', messageId);

      if (updateError) throw updateError;

      // Check the number of remaining messages that are not marked as deleted
      const { data: remainingMessages, error: remainingMessagesError } = await mmSupabase
        .from('Messages')
        .select('message_id')
        .eq('conversation_id', conversationId)
        .eq('is_deleted', false);

      if (remainingMessagesError) throw remainingMessagesError;

      if (remainingMessages.length === 0) {
        // All messages are marked as deleted, delete the conversation
        const { error: deleteConversationError } = await mmSupabase
          .from('Messages')
          .delete()
          .eq('conversation_id', conversationId);

        if (deleteConversationError) throw deleteConversationError;
      }

      socket.emit('messageDelete', { conversationId, recipientAddress, senderAddress, remainingMessagesCount: remainingMessages.length });

      res.status(200).json({ message: 'Message marked as deleted successfully', success: true, remainingMessagesCount: remainingMessages.length });
    } catch (error) {
      console.error('Error marking message as deleted:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else if (req.method === 'PATCH') {
    try {
      const body = await parseJsonBody(req);
      const { messageId, newMessage, removeAttachment, attachmentId } = body;

      const todaysDate = new Date();
      const formattedDate = todaysDate.toISOString();

      if (removeAttachment && attachmentId) {
        await deleteFromCloudinary(`direct_messages/${attachmentId}`, 'auto');
      }

      const { error: updateError } = await mmSupabase
        .from('Messages')
        .update({
          message: xss(newMessage),
          edited_at: formattedDate,
          ...(removeAttachment && { attachment_url: null, attachment_type: null, attachment_name: null, attachment_id: null })
        })
        .eq('message_id', messageId);

      if (updateError) throw updateError;

      const { data: conversationData } = await mmSupabase
        .from('Messages')
        .select('conversation_id')
        .eq('message_id', messageId)
        .single();

      socket.emit('messageEdit', { conversationId: conversationData?.conversation_id });

      res.status(200).json({ message: 'Message edited successfully', success: true });
    } catch (error) {
      console.error('Error editing message:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST', 'DELETE', 'PATCH']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}