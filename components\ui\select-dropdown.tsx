import React, { useEffect, useRef, useState, FC } from "react";
import { Search, Users, Building2 } from "lucide-react";

type UseClickOutsideHandler = () => void;

const useClickOutside = (handler: UseClickOutsideHandler): React.RefObject<HTMLButtonElement> => {
    const domNode = useRef<HTMLButtonElement>(null);

    useEffect(() => {
        const maybeHandler = (event: MouseEvent) => {
            if (domNode.current && !domNode.current.contains(event.target as Node)) {
                handler();
            }
        };

        document.addEventListener("mousedown", maybeHandler);

        return () => {
            document.removeEventListener("mousedown", maybeHandler);
        };
    }, [handler]);

    return domNode as React.RefObject<HTMLButtonElement>;
};

export interface SelectOption {
    value: string;
    label: string;
    icon?: React.ReactNode;
    [key: string]: any;
}

export interface SelectProps {
    value: string;
    onValueChange: (value: string) => void;
    children: React.ReactNode;
}

export const Select: FC<SelectProps> = ({ value, onValueChange, children }) => {
    const [isOpen, setIsOpen] = useState(false);
    const triggerRef = useRef<HTMLButtonElement>(null);

    return (
        <div className="relative inline-block w-full">
            {React.Children.map(children, child => {
                if (React.isValidElement(child)) {
                    return React.cloneElement(child as React.ReactElement<any>, {
                        value,
                        onChange: onValueChange,
                        isOpen,
                        setIsOpen,
                        triggerRef
                    });
                }
                return child;
            })}
        </div>
    );
};

export const SelectTrigger: FC<{ children: React.ReactNode; isOpen?: boolean; setIsOpen?: (isOpen: boolean) => void; triggerRef: React.RefObject<HTMLButtonElement> }> = ({
    children,
    isOpen,
    setIsOpen,
    triggerRef
}) => {
    return (
        <button
            ref={triggerRef}
            onClick={() => setIsOpen?.(!isOpen)}
            className="flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 text-sm dark:bg-transparent text-gorilla-gray dark:text-white"
            type="button"
        >
            {children}
            <span className="ml-2">
                <svg
                    width={16}
                    height={16}
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className={`fill-current transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
                >
                    <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
                </svg>
            </span>
        </button>
    );
};

export const SelectContent: FC<{ children: React.ReactNode; isOpen?: boolean; triggerRef: React.RefObject<HTMLButtonElement> }> = ({ children, isOpen, triggerRef }) => {
    if (!isOpen) return null;

    const rect = triggerRef.current.getBoundingClientRect();
    const style: React.CSSProperties = {
        position: 'absolute',
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
        zIndex: 9999,
        maxHeight: 200,
        overflowY: 'auto'
    };

    return (
        <div
            className="rounded-lg bg-white dark:bg-black"
            style={style}
        >
            <div className="py-1" style={{
                scrollbarWidth: 'thin'
            }}>
                {children}
            </div>
        </div>
    );
};

export const SelectItem: FC<{ value: string; children: React.ReactNode; onChange?: (value: string) => void }> = ({
    value,
    children,
    onChange
}) => {
    return (
        <div
            className="cursor-pointer px-3 py-2 text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent"
            onClick={() => onChange?.(value)}
        >
            {children}
        </div>
    );
};

export const SelectValue: FC<{ placeholder?: string; children: React.ReactNode }> = ({ placeholder, children }) => {
    return <span>{children || placeholder}</span>;
};

export interface SelectDropdownProps {
    value: string;
    onChange: (value: string) => void;
    options: SelectOption[];
    placeholder?: string;
    fixedPlaceholder?: string;
    className?: string;
    buttonClassName?: string;
    dropdownClassName?: string;
    optionClassName?: string;
    disabled?: boolean;
    renderOption?: (option: SelectOption) => React.ReactNode;
    renderSelected?: (option: SelectOption | undefined) => React.ReactNode;
}

export const SelectDropdown: FC<SelectDropdownProps> = ({
    value,
    onChange,
    options,
    placeholder = "Select option",
    fixedPlaceholder,
    className = "",
    buttonClassName = "",
    dropdownClassName = "",
    optionClassName = "",
    disabled = false,
    renderOption,
    renderSelected,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [dropdownDirection, setDropdownDirection] = useState<'down' | 'up'>('down');
    const wrapperRef = useClickOutside(() => setIsOpen(false)) as React.RefObject<HTMLButtonElement>;

    const selectedOption = options.find((opt) => opt.value === value);

    const handleSelect = (option: SelectOption) => {
        onChange(option.value);
        setIsOpen(false);
    };

    // Calculate dropdown direction based on available space
    const calculateDirection = () => {
        if (!wrapperRef.current) return 'down';

        const rect = wrapperRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;
        const menuHeight = 200; // max-h-[200px]

        // Open downward if enough space below, otherwise upward if enough space above
        if (spaceBelow >= menuHeight) {
            return 'down';
        } else if (spaceAbove >= menuHeight) {
            return 'up';
        }
        // Fallback to down if neither has enough space
        return 'down';
    };

    const renderDefaultOption = (option: SelectOption) => {
        return (
            <div className="flex items-center gap-2">
                <span>{option.label}</span>
                {option.icon}
            </div>
        );
    };

    const renderDefaultSelected = (option: SelectOption | undefined) => {
        const display = option ? option.label : (value === 'all' ? 'All' : placeholder);
        return (
            <div className="flex items-center gap-2">
                <span>
                    {fixedPlaceholder
                        ? `${fixedPlaceholder} ${display}`
                        : display}
                </span>
                {option?.icon}
            </div>
        );
    };

    const handleTriggerClick = () => {
        if (disabled) return;
        setDropdownDirection(calculateDirection());
        setIsOpen(prev => !prev);
    };

    return (
        <div ref={wrapperRef as any} className={`relative inline-block w-full ${className}`}>
            <button
                onClick={handleTriggerClick}
                disabled={disabled}
                className={`flex w-full items-center justify-between rounded-md bg-transparent px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white ${buttonClassName}`}
                type="button"
            >
                <span className="flex items-center gap-1">
                    {renderSelected
                        ? renderSelected(selectedOption)
                        : renderDefaultSelected(selectedOption)
                    }
                </span>
                <span className="ml-2">
                    <svg
                        width={16}
                        height={16}
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className={`fill-current transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
                    >
                        <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
                    </svg>
                </span>
            </button>

            {isOpen && (
                <div
                    className={`absolute left-0 z-40 w-full overflow-y-auto max-h-[200px] rounded-lg bg-white dark:bg-black border border-solid border-gray-400 dark:border-white ${dropdownClassName}`}
                    style={{
                        [dropdownDirection === 'down' ? 'top' : 'bottom']: 'calc(100% + 4px)',
                        [dropdownDirection === 'down' ? 'marginTop' : 'marginBottom']: '4px',
                    }}
                >
                    <div className="py-1">
                        {options.map((option) => (
                            <div
                                key={option.value}
                                className={`cursor-pointer px-3 py-2 text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-300 dark:hover:text-white hover:bg-transparent dark:hover:bg-transparent flex items-center gap-1 border border-solid border-gray-300 dark:border-white border-t-0 border-l-0 border-r-0 first:border-t-0 last:border-b-0 ${optionClassName}`}
                                onClick={() => {
                                    console.log('Option clicked:', option);
                                    handleSelect(option);
                                }}
                            >
                                {renderOption
                                    ? renderOption(option)
                                    : renderDefaultOption(option)
                                }
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};