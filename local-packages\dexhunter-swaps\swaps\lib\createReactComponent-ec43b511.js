import { forwardRef as w, createElement as m } from "react";
import { g as R } from "./_commonjsHelpers-10dfc225.js";
var u = { exports: {} }, k = "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED", S = k, x = S;
function v() {
}
function h() {
}
h.resetWarningCache = v;
var E = function() {
  function e(o, c, l, i, f, a) {
    if (a !== x) {
      var p = new Error(
        "Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types"
      );
      throw p.name = "Invariant Violation", p;
    }
  }
  e.isRequired = e;
  function r() {
    return e;
  }
  var t = {
    array: e,
    bigint: e,
    bool: e,
    func: e,
    number: e,
    object: e,
    string: e,
    symbol: e,
    any: e,
    arrayOf: r,
    element: e,
    elementType: e,
    instanceOf: r,
    node: e,
    objectOf: r,
    oneOf: r,
    oneOfType: r,
    shape: r,
    exact: r,
    checkPropTypes: h,
    resetWarningCache: v
  };
  return t.PropTypes = t, t;
};
u.exports = E();
var j = u.exports;
const n = /* @__PURE__ */ R(j);
var C = {
  xmlns: "http://www.w3.org/2000/svg",
  width: 24,
  height: 24,
  viewBox: "0 0 24 24",
  fill: "none",
  stroke: "currentColor",
  strokeWidth: 2,
  strokeLinecap: "round",
  strokeLinejoin: "round"
}, N = Object.defineProperty, W = Object.defineProperties, I = Object.getOwnPropertyDescriptors, s = Object.getOwnPropertySymbols, d = Object.prototype.hasOwnProperty, O = Object.prototype.propertyIsEnumerable, y = (e, r, t) => r in e ? N(e, r, { enumerable: !0, configurable: !0, writable: !0, value: t }) : e[r] = t, _ = (e, r) => {
  for (var t in r || (r = {}))
    d.call(r, t) && y(e, t, r[t]);
  if (s)
    for (var t of s(r))
      O.call(r, t) && y(e, t, r[t]);
  return e;
}, D = (e, r) => W(e, I(r)), F = (e, r) => {
  var t = {};
  for (var o in e)
    d.call(e, o) && r.indexOf(o) < 0 && (t[o] = e[o]);
  if (e != null && s)
    for (var o of s(e))
      r.indexOf(o) < 0 && O.call(e, o) && (t[o] = e[o]);
  return t;
}, $ = (e, r, t) => {
  const o = w(
    (c, l) => {
      var i = c, { color: f = "currentColor", size: a = 24, stroke: p = 2, children: g } = i, P = F(i, ["color", "size", "stroke", "children"]);
      return m(
        "svg",
        _(D(_({
          ref: l
        }, C), {
          width: a,
          height: a,
          stroke: f,
          strokeWidth: p,
          className: `tabler-icon tabler-icon-${e}`
        }), P),
        [...t.map(([T, b]) => m(T, b)), ...g || []]
      );
    }
  );
  return o.propTypes = {
    color: n.string,
    size: n.oneOfType([n.string, n.number]),
    stroke: n.oneOfType([n.string, n.number])
  }, o.displayName = `${r}`, o;
};
export {
  $ as c
};
