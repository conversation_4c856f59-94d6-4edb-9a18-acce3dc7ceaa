name: Publish Package
on:
  push:
    branches: ['main', 'release/*']
jobs:
  npm-publish:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      contents: write
      packages: write
      pull-requests: write
      id-token: write
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ secrets.BOT_ID }}
          private-key: ${{ secrets.BOT_SK }}
      - uses: actions/checkout@v3
        with:
          token: ${{ steps.app-token.outputs.token }}
      - uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'npm'
      - run: npm install
      - run: npm run release
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
      - name: Merge Release -> Trunk
        uses: devmasx/merge-branch@854d3ac71ed1e9deb668e0074781b81fdd6e771f
        if: github.ref == 'refs/heads/release/1.x'
        with:
          type: now
          from_branch: release/1.x
          target_branch: main
          github_token: ${{ steps.app-token.outputs.token }}
