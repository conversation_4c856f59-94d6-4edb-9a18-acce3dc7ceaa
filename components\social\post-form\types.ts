export interface ExtendedMediaItem {
  mediaUrl: string;
  file?: File;
  mediaType: string;
  thumbnailUrl?: string;
  thumbnailFile?: File;
  edited?: boolean;
  serverMediaUrl?: string;
  tempMediaId?: string;
  metadata?: {
    dimensions?: { width: number; height: number };
    duration?: number;
    resolution?: string;
    format?: string;
  };
}

export interface PrivacySettings {
  privacy: 'default' | 'public' | 'private';
  privateType?: 'subscription' | 'payPerView';
  ppvPrice?: string;
}

export interface PostFormState {
  content: string;
  mediaItems: ExtendedMediaItem[];
  mediaPreviews: ExtendedMediaItem[];
  isUploading: boolean;
  uploadProgresses: number[];
  isSubmitting: boolean;
  hasUnsavedChanges: boolean;
  tagData: {
    hashtags: string[];
    mentions: any[];
  };
  privacySettings: PrivacySettings;
  saveToLibrary: boolean;
}