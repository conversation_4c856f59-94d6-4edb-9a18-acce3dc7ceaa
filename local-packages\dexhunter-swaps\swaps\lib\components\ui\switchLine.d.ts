/// <reference types="react" />
interface SwitchLineType {
    left?: string;
    right?: string;
    initValue?: boolean;
    onChange: (value: boolean) => void;
    className?: string;
    color?: string;
    keyId?: string;
}
declare const _default: import("react").MemoExoticComponent<({ initValue, onChange, color, keyId, }: SwitchLineType) => import("react/jsx-runtime").JSX.Element>;
export default _default;
