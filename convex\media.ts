import { v } from "convex/values";
import { mutation, query, internalAction, action } from "./_generated/server";
import { UTApi } from "uploadthing/server";

const utapi = new UTApi();

export const validateMediaUrl = action({
  args: {
    url: v.string(),
  },
  handler: async (ctx, { url }) => {
    "use node";

    if (!url) {
      return { valid: false, message: "URL is required" };
    }

    // Check if URL is valid
    let parsedUrl;
    try {
      parsedUrl = new URL(url);
    } catch (e) {
      return { valid: false, message: "Invalid URL format" };
    }

    const checkExtension = (path: string) => {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.tiff'];
      const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.wmv', '.flv', '.mkv'];

      if (imageExtensions.some(ext => path.endsWith(ext))) {
        return { valid: true, type: 'image', message: 'Valid image URL' };
      }
      if (videoExtensions.some(ext => path.endsWith(ext))) {
        return { valid: true, type: 'video', message: 'Valid video URL' };
      }
      return null;
    };

    const extensionCheck = checkExtension(parsedUrl.pathname.toLowerCase());
    if (extensionCheck) return extensionCheck;

    try {
      const response = await fetch(url, { method: 'HEAD' });
      if (!response.ok) {
        return { valid: false, message: `URL returned status ${response.status}` };
      }

      const contentType = response.headers.get('content-type');
      if (!contentType) {
        return { valid: false, message: "Could not determine content type" };
      }

      if (contentType.startsWith('image/')) {
        return { valid: true, type: 'image', contentType, message: 'Valid image URL' };
      }
      if (contentType.startsWith('video/')) {
        return { valid: true, type: 'video', contentType, message: 'Valid video URL' };
      }
      if (contentType === 'application/octet-stream') {
        const dataRes = await fetch(url, { method: 'GET', headers: { Range: 'bytes=0-50' } });
        const buffer = await dataRes.arrayBuffer();
        const bytes = new Uint8Array(buffer);

        if (bytes.length > 8 && bytes[4] === 0x66 && bytes[5] === 0x74 && bytes[6] === 0x79 && bytes[7] === 0x70) {
          return { valid: true, type: 'video', contentType, message: 'MP4 detected' };
        }
        if (bytes.length > 4 && bytes[0] === 0x1a && bytes[1] === 0x45 && bytes[2] === 0xdf && bytes[3] === 0xa3) {
          return { valid: true, type: 'video', contentType, message: 'WebM detected' };
        }

        return { valid: true, type: 'video', contentType, message: 'Assumed video (octet-stream)' };
      }

      return { valid: false, message: `Unsupported content type: ${contentType}` };
    } catch (error) {
      console.error("Error validating URL:", error);
      return { valid: false, message: "Failed to access URL" };
    }
  },
});

export const uploadToUploadThing = internalAction({
  args: {
    file: v.string(),
    fileType: v.string(),
    fileName: v.string(),
  },
  handler: async (ctx, { file, fileType, fileName }) => {
    "use node";

    try {
      const buffer = Buffer.from(file, "base64");
      const fileObj = new File([buffer], fileName, { type: fileType });

      const res = await utapi.uploadFiles([fileObj]);

      if (res[0]?.error) {
        throw new Error(`UploadThing error: ${res[0].error.message}`);
      }

      const uploadData = res[0]?.data;
      if (!uploadData || !uploadData.ufsUrl || !uploadData.key) {
        throw new Error("Invalid response from UploadThing");
      }

      return {
        url: uploadData.ufsUrl,
        key: uploadData.key,
      };
    } catch (error) {
      console.error("UploadThing upload error:", error);
      throw new Error(`Upload failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

export const deleteFromUploadThing = internalAction({
  args: { key: v.string() },
  handler: async (ctx, { key }) => {
    try {
      await utapi.deleteFiles(key);
      return { success: true };
    } catch (error) {
      console.error("Delete from UploadThing failed:", error);
      throw new Error("Failed to delete file from UploadThing");
    }
  },
});

// Keep your S3 actions as-is
export const uploadToS3 = internalAction({
  args: {
    file: v.string(),
    fileType: v.string(),
    fileName: v.string(),
  },
  handler: async (ctx, args) => {
    const buffer = Buffer.from(args.file, 'base64');
    const { uploadToS3 } = await import("../lib/aws-upload");
    const url = await uploadToS3(buffer, args.fileType, args.fileName);
    return { url };
  },
});

export const deleteFromS3 = internalAction({
  args: { key: v.string() },
  handler: async (ctx, args) => {
    const { deleteFromS3 } = await import("../lib/s3");
    await deleteFromS3(args.key);
    return { success: true };
  },
});

// Fixed uploadFile mutation – now correctly imports internal actions
export const uploadFile = mutation({
  args: {
    userId: v.string(),
    file: v.string(),
    fileType: v.string(),
    fileName: v.string(),
    privacy: v.string(),
    saveToLibrary: v.boolean(),
    metadata: v.optional(v.any()),
    privateType: v.optional(v.string()),
    ppvPrice: v.optional(v.string()),
    thumbnail: v.optional(
      v.object({
        file: v.string(),
        fileType: v.string(),
        fileName: v.string(),
      })
    ),
  },
  handler: async (ctx, args) => {
    try {
      let fileUrl = '';
      let thumbnailUrl: string | null = null;
      let fileKey = '';
      let thumbnailKey: string | null = null;
      let type = args.fileType;
      let metadata = args.metadata || {};

      if (type.startsWith('image/')) {
        // Use UploadThing
        const result = await ctx.runAction(uploadToUploadThing, {
          file: args.file,
          fileType: args.fileType,
          fileName: args.fileName,
        });
        fileUrl = result.url;
        fileKey = result.key;
        thumbnailUrl = result.url;
        thumbnailKey = result.key;
      } else if (type.startsWith('audio/')) {
        // Use S3
        fileKey = args.fileName;
        const { url } = await ctx.runAction(uploadToS3, {
          file: args.file,
          fileType: args.fileType,
          fileName: args.fileName,
        });
        fileUrl = url;
        thumbnailUrl = null;
        thumbnailKey = null;
      } else if (type.startsWith('video/')) {
        // Video on S3
        fileKey = args.fileName;
        const { url } = await ctx.runAction(uploadToS3, {
          file: args.file,
          fileType: args.fileType,
          fileName: args.fileName,
        });
        fileUrl = url;

        // Optional thumbnail via UploadThing
        if (args.thumbnail) {
          const result = await ctx.runAction(uploadToUploadThing, {
            file: args.thumbnail.file,
            fileType: args.thumbnail.fileType,
            fileName: args.thumbnail.fileName,
          });
          thumbnailUrl = result.url;
          thumbnailKey = result.key;
        } else {
          thumbnailUrl = null;
          thumbnailKey = null;
        }
      } else {
        throw new Error('Unsupported file type');
      }

      // Add privacy metadata
      if (args.privacy === 'private') {
        metadata.privacy_settings = {
          type: args.privateType || 'subscription',
          price: args.privateType === 'payPerView' ? args.ppvPrice : null,
        };
      }

      // Save to TemporaryMedia
      await ctx.db.insert('TemporaryMedia', {
        id: fileKey,
        url: fileUrl,
        thumbnail_url: thumbnailUrl,
        thumbnail_key: thumbnailKey,
        user_id: args.userId,
        type,
        metadata,
        is_used: false,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      });

      return {
        success: true,
        url: fileUrl,
        thumbnailUrl,
        type,
        tempMediaId: fileKey,
        metadata,
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      return { success: false, message: 'Failed to upload file' };
    }
  },
});