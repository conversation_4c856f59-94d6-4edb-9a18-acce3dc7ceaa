'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { EmojiPicker } from '@/components/ui/chat/emoji-picker';
import { cn } from '@/lib/utils';
import Tippy from '@tippyjs/react';
import DescriptionDialog from '@/components/dialogs/AddDescriptionDialog';
import TagPeopleDialog from '@/components/dialogs/TagPeopleDialog';
import MixedTagsInput from '@/components/social/MixedTagsInput';
import { Post, PostData, TaggedUser, Suggestion, ExtendedMediaItem, ExtendedPostData } from '@/types/post';
import { MediaUploader } from '@/components/media-library/MediaUploader';
import ThumbnailSelectionDialog from '@/components/dialogs/ThumbnailSelectionDialog';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import MultiImageUploadDialog from '@/components/dialogs/MultiImageUploadDialog';
import MediaPreviewSection from '@/components/social/MediaPreviewSection';
import PostPermissionsDialog, { clearPersistedPermissions } from '@/components/dialogs/PostPermissionsDialog';
import { PostFormHeader } from '@/components/social/post-form/PostFormHeader';
import { CircleProgress } from '@/components/ui/circle-progress';
import { Calendar as CalendarIcon, ChevronDown } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { extractMetadata } from '@/lib/utils';
import { MediaUploadButtons } from '@/components/social/post-form/MediaUploadButtons';
import { createPostFormFunctions, TempVideoFile } from './post-form/functions';
import VideoEditDialog from '@/components/dialogs/VideoEditDialog';
import { toast } from 'react-toastify';
import { PermissionRule } from '@/types/post';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useDebounce } from '@/hooks/useDebounce';

interface CreatePostFormProps {
  onSubmit: (postData: ExtendedPostData) => Promise<Post | undefined>;
  setHasUnsavedChanges: (value: boolean) => void;
  isFocused: boolean;
  setIsFocused: (value: boolean) => void;
  setIsChildPopoverOpen: (value: boolean) => void;
  setIsFileInputActive: (value: boolean) => void;
  editorRef: React.MutableRefObject<any>;
  user: any;
}

interface PrivacySettings {
  privacy: 'default' | 'public' | 'private';
  privateType?: 'subscription' | 'payPerView';
  ppvPrice?: string;
  timelinePermission?: 'public' | 'followers' | 'subscribers';
  mediaPermissions: PermissionRule[];
}

const privacyOptions = [
  { value: 'default', label: 'Select Privacy' },
  { value: 'public', label: 'Public' },
  { value: 'private', label: 'Private' },
];

const CreatePostForm: React.FC<CreatePostFormProps> = ({
  onSubmit,
  setHasUnsavedChanges,
  isFocused,
  setIsFocused,
  setIsChildPopoverOpen,
  setIsFileInputActive,
  editorRef,
  user,
}) => {
  const [content, setContent] = useState('');
  const [mediaItems, setMediaItems] = useState<ExtendedMediaItem[]>([]);
  const [mediaPreviews, setMediaPreviews] = useState<ExtendedMediaItem[]>([]);
  const [mediaType, setMediaType] = useState<string | null>(null);
  const [mediaUrl, setMediaUrl] = useState<string | null>(null);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgresses, setUploadProgresses] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const imageInputRef = useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const videoInputRef = useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const clipInputRef = useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const audioInputRef = useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const gifInputRef = useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const [showGifPicker, setShowGifPicker] = useState(false);
  const [showImageUploadMenu, setShowImageUploadMenu] = useState(false);
  const [isImageEditModalOpen, setIsImageEditModalOpen] = useState(false);
  const [editingImageIndex, setEditingImageIndex] = useState<number | null>(null);
  const [isDescriptionModalOpen, setIsDescriptionModalOpen] = useState(false);
  const [isTagPeopleModalOpen, setIsTagPeopleModalOpen] = useState(false);
  const [mediaDescription, setMediaDescription] = useState('');
  const [taggedUsers, setTaggedUsers] = useState<TaggedUser[]>([]);
  const [tagData, setTagData] = useState<{
    hashtags: string[];
    mentions: Suggestion[];
  }>({
    hashtags: [],
    mentions: [],
  });

  // Use a ref to store the initial ID for the default media permission rule
  const initialMediaPermissionRuleId = useRef(crypto.randomUUID());

  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>(() => {
    const initialPrivacy = user?.privacySettings?.accountVisibility === 'private'
      ? 'private'
      : user?.privacySettings?.accountVisibility === 'public'
        ? 'public'
        : 'default';

    // Initialize timelinePermission based on initial user settings or default
    let initialTimelinePermission: 'public' | 'followers' | 'subscribers' = 'public';
    if (user?.privacySettings?.accountVisibility === 'private') {
      // Assuming 'private' default for user implies 'followers' for timeline if no other info
      initialTimelinePermission = 'followers';
    } else if (user?.privacySettings?.accountVisibility === 'public') {
      initialTimelinePermission = 'public';
    }

    return {
      privacy: initialPrivacy,
      privateType: initialPrivacy === 'private' ? 'subscription' : undefined,
      ppvPrice: '',
      mediaPermissions: [],
      timelinePermission: initialTimelinePermission,
    };
  });
  const [isVideoEditModalOpen, setIsVideoEditModalOpen] = useState(false);
  const [tempVideoFile, setTempVideoFile] = useState<TempVideoFile | null>(null);
  const [isClipMode, setIsClipMode] = useState(false);
  const [isVerifiedBadgeLoading, setIsVerifiedBadgeLoading] = useState(true);
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [saveToLibrary, setSaveToLibrary] = useState(true);
  const [tempMediaId, setTempMediaId] = useState<string | null>(null);
  const [thumbnailOptions, setThumbnailOptions] = useState<string[]>([]);
  const [selectedThumbnailIndex, setSelectedThumbnailIndex] = useState(0);
  const [isThumbnailModalOpen, setIsThumbnailModalOpen] = useState(false);
  const [isMultiImageModalOpen, setIsMultiImageModalOpen] = useState<boolean>(false);
  const [tempSelectedImages, setTempSelectedImages] = useState<ExtendedMediaItem[]>([]);
  const [scheduledDate, setScheduledDate] = useState<Date | undefined>(undefined);
  const [scheduledTime, setScheduledTime] = useState<string>('');
  const [isPostDropdownOpen, setIsPostDropdownOpen] = useState(false);

  // State for hashtag search query
  const [hashtagSearchQuery, setHashtagSearchQuery] = useState('');
  const debouncedHashtagQuery = useDebounce(hashtagSearchQuery, 300);

  // Use Convex query for hashtag search
  const hashtagSearchResults = useQuery(
    api.hashtags.searchHashtags,
    debouncedHashtagQuery && debouncedHashtagQuery.length >= 2
      ? { query: debouncedHashtagQuery, limit: 10 }
      : 'skip'
  );

  const [userSuggestionQuery, setUserSuggestionQuery] = useState("");
  const userSuggestionsConvex = useQuery(
    api.users.searchUsers,
    userSuggestionQuery && userSuggestionQuery.length >= 3 ? { query: userSuggestionQuery } : "skip"
  );

  const MAX_CHARS = 400;
  const MAX_IMAGES = 10;

  const uploadFileMutation = useMutation(api.media.uploadFile);

  const handleFileChange = async (file: File, index: number, thumbnailFile?: File): Promise<ExtendedMediaItem | null> => {
    setIsUploading(true);
    setUploadProgresses((prev) => {
      const newProgresses = [...prev];
      newProgresses[index] = 0;
      return newProgresses;
    });
    try {
      // Convert file to base64
      const fileAsBase64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve((reader.result as string).split(',')[1]);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
      // If thumbnailFile is provided, convert to base64
      let thumbnailData = undefined;
      if (thumbnailFile) {
        thumbnailData = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve((reader.result as string).split(',')[1]);
          reader.onerror = reject;
          reader.readAsDataURL(thumbnailFile);
        });
      }
      // Call Convex mutation
      const result = await uploadFileMutation({
        userId: user.id, // or userId from context
        file: fileAsBase64,
        fileType: file.type,
        fileName: file.name,
        privacy: privacySettings.privacy,
        saveToLibrary,
        metadata: mediaPreviews[index]?.metadata,
        privateType: privacySettings.privateType,
        ppvPrice: privacySettings.ppvPrice,
        thumbnail: thumbnailData,
      });

      if (!result.success) {
        throw new Error(result.message || 'Upload failed');
      }

      const metadata = mediaPreviews[index]?.metadata || (await extractMetadata(file));
      const mediaItem: ExtendedMediaItem = {
        mediaUrl: result.url,
        thumbnailUrl: result.thumbnailUrl,
        mediaType: file.type,
        tempMediaId: result.tempMediaId,
        edited: false,
        metadata,
      };
      setMediaItems((prev) => {
        const newItems = [...prev];
        newItems[index] = mediaItem;
        return newItems;
      });
      setUploadProgresses((prev) => {
        const newProgresses = [...prev];
        newProgresses[index] = 100;
        return newProgresses;
      });
      toast.success(`Media ${index + 1} uploaded successfully!`);
      return mediaItem;
    } catch (error: any) {
      toast.error(`Failed to upload media ${index + 1}: ${error.message}`);
      setMediaPreviews((prev) => prev.filter((_, i) => i !== index));
      setMediaItems((prev) => prev.filter((_, i) => i !== index));
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const resetForm = () => {
    if (fileInputRef.current) fileInputRef.current.value = '';
    if (imageInputRef.current) imageInputRef.current.value = '';
    setContent('');
    setTagData({ hashtags: [], mentions: [] });
    setMediaItems([]);
    setMediaPreviews([]);
    setPrivacySettings({
      privacy: 'default',
      privateType: 'subscription',
      ppvPrice: '',
      mediaPermissions: [], // Reset mediaPermissions
      timelinePermission: 'public',
    });
    setMediaDescription('');
    setTaggedUsers([]);
    setUploadProgresses([]);
    setMediaType(null);
    setMediaUrl(null);
    setThumbnailUrl(null);
    setTempMediaId(null);
    setThumbnailOptions([]);
    setIsThumbnailModalOpen(false);
    setTempVideoFile(null);
    setShowGifPicker(false);
    setIsImageEditModalOpen(false);
    setEditingImageIndex(null);
    setScheduledDate(undefined);
    setScheduledTime('');
    setSaveToLibrary(false);
    setHasUnsavedChanges(false);
    clearPersistedPermissions();
  };

  const baseContext = {
    // State setters
    setIsFileInputActive,
    setIsFocused,
    setMediaPreviews,
    setMediaItems,
    setHasUnsavedChanges,
    setIsUploading,
    setUploadProgresses,
    setTempSelectedImages,
    setIsMultiImageModalOpen,
    setThumbnailOptions,
    setSelectedThumbnailIndex,
    setIsThumbnailModalOpen,
    setTempVideoFile,
    setContent,
    setShowGifPicker,
    setMediaDescription,
    setIsDescriptionModalOpen,
    setTaggedUsers,
    setIsTagPeopleModalOpen,
    setIsSubmitting,
    setTagData,
    setPrivacySettings,
    setIsPermissionsDialogOpen,
    setMediaUrl,
    setMediaType,
    setThumbnailUrl,
    setTempMediaId,
    setShowImageUploadMenu,
    setEditingImageIndex,
    setIsImageEditModalOpen,
    setScheduledDate,
    setScheduledTime,
    setIsPostDropdownOpen,
    setIsClipMode,
    setIsVideoEditModalOpen,
    setSaveToLibrary,

    // Refs
    fileInputRef,
    imageInputRef,
    videoInputRef,
    audioInputRef,
    clipInputRef,
    gifInputRef,

    // State values
    mediaPreviews,
    content,
    privacySettings,
    saveToLibrary,
    isFocused,
    mediaItems,
    tempVideoFile,
    mediaDescription,
    tagData,
    taggedUsers,
    scheduledDate,
    scheduledTime,
    selectedThumbnailIndex,
    mediaType,
    mediaUrl,
    thumbnailUrl,
    tempMediaId,
    isClipMode,

    // Functions
    onSubmit,
    handleFileChange,
    resetForm,

    // Mock functions (if needed)
    setGifInputRef: () => { },
    setClipInputRef: () => { },
    setAudioInputRef: () => { },
    setVideoInputRef: () => { },
    setFileInputRef: () => { },

    // Add validateClip function
    validateClip: async (file: File) => {
      // Implement or import the actual validateClip function
      return {
        isValid: true,
        metadata: {
          width: 0,
          height: 0,
          duration: 0,
          aspectRatio: 0
        }
      };
    }
  };

  // Initialize functions using base context
  const {
    handleImageUpload,
    handleVideoInputChange,
    handleGifInputChange,
    handleClipInputChange,
    handleAudioInputChange,
    handleRemoveMedia,
    handleEmojiSelect,
    handleGifSelect,
    handleEditedImage,
    handleDescriptionSave,
    handleTaggedPeopleSave,
    handleMultiImageSave,
    handleSubmit,
    handleContentChange,
    handleMediaLibrarySelect,
    handleThumbnailConfirm,
    handlePrivacyChange,
    triggerFileInput,
  } = createPostFormFunctions(baseContext);

  // Create full context object with both state and functions
  const functionContext = {
    ...baseContext,
    handleRemoveMedia,
    handleEmojiSelect,
    handleGifSelect,
    handleEditedImage,
    handleDescriptionSave,
    handleTaggedPeopleSave,
    handleMultiImageSave,
    handleSubmit,
    handleContentChange,
    handleMediaLibrarySelect,
    handleThumbnailConfirm,
    handlePrivacyChange,
    triggerFileInput,
  };

  // Update popover state when modals or popovers change
  useEffect(() => {
    const isAnyPopoverOpen =
      showGifPicker ||
      showImageUploadMenu ||
      isImageEditModalOpen ||
      isDescriptionModalOpen ||
      isTagPeopleModalOpen ||
      isPermissionsDialogOpen ||
      isThumbnailModalOpen ||
      isMultiImageModalOpen ||
      isPostDropdownOpen;
    setIsChildPopoverOpen(isAnyPopoverOpen);
  }, [
    showGifPicker,
    showImageUploadMenu,
    isImageEditModalOpen,
    isDescriptionModalOpen,
    isTagPeopleModalOpen,
    isPermissionsDialogOpen,
    isThumbnailModalOpen,
    isMultiImageModalOpen,
    isPostDropdownOpen,
    setIsChildPopoverOpen,
  ]);

  return (
    <div className="relative p-4" onClick={() => setIsFocused(true)}>
      <PostFormHeader
        user={user}
        privacySettings={privacySettings}
        onPrivacyChange={handlePrivacyChange}
        onConfigurePrivacy={() => setIsPermissionsDialogOpen(true)}
        isVerifiedBadgeLoading={isVerifiedBadgeLoading}
        setVerifiedBadgeLoading={setIsVerifiedBadgeLoading}
        showConfigureButton={privacySettings.privacy === 'private' ||
          (privacySettings.privacy === 'public' &&
            (privacySettings.privateType === 'payPerView' ||
              privacySettings.privateType === 'subscription'))}
      />

      <div className="flex space-x-4">
        <div className="flex-grow">
          <form className="space-y-4">
            {/* Text Input */}
            <div className="w-full">
              <div className="relative">
                <div className="w-full" >
                  <MixedTagsInput
                    ref={editorRef}
                    value={content}
                    onChange={handleContentChange}
                    onTagData={(data) => {
                      setTagData({
                        hashtags: data.hashtags,
                        mentions: data.mentions.map(mention => ({
                          id: mention.userId,
                          label: mention.username,
                          value: mention.username,
                          display: mention.username,
                          username: mention.username,
                          type: 'mention'
                        }))
                      });
                    }}
                    fetchUserSuggestions={async (query) => {
                      setUserSuggestionQuery(query);
                      if (!userSuggestionsConvex || !userSuggestionsConvex.success) return [];
                      return (userSuggestionsConvex.data || []).map((user: any) => ({
                        id: user.id,
                        label: user.display_name,
                        value: user.username,
                        display: user.display_name,
                        username: '@' + user.username,
                        avatar: user.profilePhoto,
                        type: 'mention',
                      }));
                    }}
                    fetchHashtagSuggestions={useCallback(async (query: string) => {
                      setHashtagSearchQuery(query);
                      const tags = hashtagSearchResults?.results?.tags || [];
                      return tags.map((tag: any) => ({
                        id: tag.id,
                        label: tag.name,
                        value: tag.name,
                        display: `#${tag.name} (${tag.postCount} posts)`,
                        type: 'hashtag',
                      }));
                    }, [hashtagSearchResults])}
                  />
                </div>
                <div className="absolute bottom-2 right-2">
                  <Tippy content="Insert Emoji" animation="shift-toward-subtle" placement="top" arrow={true} theme="sugar">
                    <div className="inline-flex">
                      <EmojiPicker
                        onChange={handleEmojiSelect}
                        className="h-5 w-5 rounded-full text-turquoise hover:bg-gray-100 dark:hover:bg-zinc-800 transition-transform hover:scale-105"
                      />
                    </div>
                  </Tippy>
                </div>
              </div>

              {/* Character Counter (Progress Bar) */}
              <div className="my-2 flex items-center justify-start gap-2">
                <CircleProgress
                  value={content?.length || 0}
                  maxValue={MAX_CHARS}
                  size={32}
                  strokeWidth={3}
                  className="mr-2"
                  // Custom color function based on character count
                  getColor={(fillPercentage) => {
                    if (fillPercentage > 0.9) return 'stroke-red-500';
                    if (fillPercentage > 0.8) return 'stroke-amber-500';
                    return 'stroke-turquoise';
                  }}
                  // Show the remaining characters
                  showValue={false}
                  suffix=""
                  animationDuration={300}
                />
                <span
                  className={cn(
                    'text-xs',
                    content?.length > MAX_CHARS
                      ? 'text-red-500'
                      : content?.length > MAX_CHARS * 0.8
                        ? 'text-amber-500'
                        : 'text-gray-500 dark:text-gray-400'
                  )}
                >
                  {MAX_CHARS - (content?.length || 0)}
                </span>
              </div>

              {/* Upload Progress & Media Preview */}
              <div className="w-full flex flex-col items-start justify-start gap-2">
                {isUploading &&
                  mediaPreviews.map((item, index) => (
                    <div
                      key={item.id || `upload-${index}`}
                      className="w-full bg-gray-200 dark:bg-zinc-700 rounded-full h-2 mt-2"
                    >
                      <div
                        className="bg-turquoise h-2 rounded-full transition-all"
                        style={{ width: `${uploadProgresses[index] || 0}%` }}
                      ></div>
                    </div>
                  ))}

                {/* Media Preview Section */}
                <MediaPreviewSection
                  mediaPreviews={mediaPreviews}
                  handleRemoveMedia={handleRemoveMedia}
                  openImageEditor={() => {
                    setTempSelectedImages(
                      mediaPreviews.map((item, i) => ({
                        id: `existing-${Date.now()}-${i}-${Math.random().toString(36).substring(2, 10)}`,
                        file: item.file,
                        mediaUrl: item.mediaUrl,
                        mediaType: item.mediaType,
                        editedUrl: item.mediaUrl,
                        edited: item.edited || false,
                        metadata: item.metadata,
                      }))
                    );
                    setIsMultiImageModalOpen(true);
                  }}
                  onAddMoreImages={() => {
                    triggerFileInput(imageInputRef);
                  }}
                  maxImages={MAX_IMAGES}
                />
              </div>

              {/* Bottom Section */}
              <div className="w-full flex items-center justify-between pt-4 border-t border-gray-200 dark:border-white/30">
                {/* Replace all the individual button components with: */}
                <MediaUploadButtons
                  isUploading={isUploading}
                  onTriggerFileInput={(inputType) => {
                    switch (inputType) {
                      case 'image':
                        triggerFileInput(imageInputRef);
                        break;
                      case 'video':
                        triggerFileInput(videoInputRef);
                        break;
                      case 'clip':
                        triggerFileInput(clipInputRef);
                        break;
                      case 'audio':
                        triggerFileInput(audioInputRef);
                        break;
                      case 'gif':
                        triggerFileInput(gifInputRef);
                        break;
                    }
                  }}
                  setIsChildPopoverOpen={setIsChildPopoverOpen}
                  setIsFocused={setIsFocused}
                />

                {/* Keep the file input elements */}
                <input
                  type="file"
                  ref={imageInputRef}
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  disabled={isUploading}
                  className="hidden"
                />
                <input
                  type="file"
                  ref={videoInputRef}
                  accept="video/*"
                  onChange={handleVideoInputChange}
                  disabled={isUploading}
                  className="hidden"
                />
                <input
                  type="file"
                  ref={clipInputRef}
                  accept="video/*"
                  onChange={handleClipInputChange}
                  disabled={isUploading}
                  className="hidden"
                />
                <input
                  type="file"
                  ref={audioInputRef}
                  accept="audio/*"
                  onChange={handleAudioInputChange}
                  disabled={isUploading}
                  className="hidden"
                />
                <input
                  type="file"
                  ref={gifInputRef}
                  accept="image/gif"
                  onChange={handleGifInputChange}
                  disabled={isUploading}
                  className="hidden"
                />

                {/* Keep the rest of the bottom section */}
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="saveToLibrary"
                    checked={saveToLibrary}
                    onChange={(e) => {
                      setSaveToLibrary(e.target.checked);
                      setIsFocused(true);
                    }}
                    className="h-4 w-4 text-turquoise focus:ring-turquoise"
                  />
                  <label htmlFor="saveToLibrary" className="text-sm text-gray-900 dark:text-gray-300">
                    Save to Media Library
                  </label>
                </div>

                {/* Create Post Button/Popover */}
                <Popover
                  onOpenChange={(open) => {
                    setIsPostDropdownOpen(open);
                    setIsChildPopoverOpen(
                      open ||
                      isImageEditModalOpen ||
                      isDescriptionModalOpen ||
                      isTagPeopleModalOpen ||
                      isPermissionsDialogOpen ||
                      isThumbnailModalOpen ||
                      isMultiImageModalOpen
                    );
                    if (open) setIsFocused(true);
                  }}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="default"
                      className="!bg-turquoise text-white rounded-lg px-4 py-0 !text-sm font-medium shadow-md transition-all flex items-center gap-2"
                      disabled={
                        isSubmitting ||
                        (!content?.trim() && !mediaPreviews.length && !mediaItems.length) ||
                        content?.length > MAX_CHARS
                      }
                    >
                      Create Post
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48 bg-white dark:bg-zinc-700 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-2">
                    <div className="flex flex-col w-full">
                      <Button
                        variant="ghost"
                        className="justify-start text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 text-sm py-1 px-2"
                        onClick={() => handleSubmit(false)}
                      >
                        Post Now
                      </Button>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="ghost"
                            className="justify-start text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 text-sm py-1 px-2"
                          >
                            Schedule Post
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-80 bg-white dark:bg-zinc-700 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4">
                          <div className="space-y-4">
                            <div className="flex items-center gap-2">
                              <CalendarIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                              <span className="text-sm font-medium text-gray-900 dark:text-white">Schedule Post</span>
                            </div>
                            <Calendar
                              mode="single"
                              selected={scheduledDate}
                              onSelect={setScheduledDate}
                              disabled={(date) => date < new Date()}
                              initialFocus
                              className="border border-gray-200 dark:border-gray-700 rounded-md"
                            />
                            <div className="flex items-center gap-2">
                              <Input
                                type="time"
                                value={scheduledTime}
                                onChange={(e) => setScheduledTime(e.target.value)}
                                className="w-full bg-gray-100 dark:bg-zinc-800 border border-gray-200 dark:border-gray-700 rounded-md px-2 py-1 text-sm text-gray-900 dark:text-white"
                              />
                            </div>
                            <Button
                              variant="default"
                              className="!bg-turquoise text-white rounded-lg px-4 py-0 !text-sm font-medium"
                              onClick={() => handleSubmit(true)}
                              disabled={!scheduledDate || !scheduledTime || isSubmitting}
                            >
                              Schedule
                            </Button>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Modals */}

      <DescriptionDialog
        isOpen={isDescriptionModalOpen}
        onClose={() => setIsDescriptionModalOpen(false)}
        onSave={handleDescriptionSave}
        initialDescription={mediaDescription}
      />

      <TagPeopleDialog
        isOpen={isTagPeopleModalOpen}
        onClose={() => setIsTagPeopleModalOpen(false)}
        onSave={handleTaggedPeopleSave}
        initialTags={taggedUsers.map(user => ({ ...user, profilePhoto: user.profilePhoto || '' }))}
      />

      {isThumbnailModalOpen &&
        <ThumbnailSelectionDialog
          isOpen={isThumbnailModalOpen}
          onClose={() => setIsThumbnailModalOpen(false)}
          thumbnailOptions={thumbnailOptions}
          selectedThumbnailIndex={selectedThumbnailIndex}
          setSelectedThumbnailIndex={setSelectedThumbnailIndex}
          onConfirm={handleThumbnailConfirm}
          tempVideoFile={tempVideoFile}
          setThumbnailOptions={setThumbnailOptions}
        />
      }

      {isMultiImageModalOpen &&
        <MultiImageUploadDialog
          isOpen={isMultiImageModalOpen}
          onClose={() => {
            setIsMultiImageModalOpen(false);
            setTempSelectedImages([]);
          }}
          initialImages={tempSelectedImages}
          onSave={handleMultiImageSave}
        />
      }

      {isPermissionsDialogOpen &&
        <PostPermissionsDialog
          isOpen={isPermissionsDialogOpen}
          onClose={() => setIsPermissionsDialogOpen(false)}
          initialSettings={React.useMemo(() => ({
            timelinePermission: privacySettings.timelinePermission || 'public',
            mediaPermissions: privacySettings.mediaPermissions,
            price: privacySettings.ppvPrice || '0.00',
          }), [privacySettings.timelinePermission, privacySettings.mediaPermissions, privacySettings.ppvPrice])}
          onSave={React.useCallback((settings) => {
            console.log('Dialog saved settings:', settings);

            const newPrivacy = settings.timelinePermission === 'public' ? 'public' : 'private';

            let newPrivateType: 'subscription' | 'payPerView' | undefined;
            if (settings.timelinePermission === 'subscribers') {
              newPrivateType = 'subscription';
            } else if (settings.timelinePermission === 'followers') {
              newPrivateType = 'subscription';
            } else if (settings.mediaPermissions.some(p => p.conditions.some(c => c.type === 'price'))) {
              newPrivateType = 'payPerView';
            } else {
              newPrivateType = undefined;
            }

            setPrivacySettings((prevSettings: any) => {
              const updatedSettings = {
                ...prevSettings,
                privacy: newPrivacy,
                privateType: newPrivateType,
                ppvPrice: settings.mediaPermissions.find(p => p.conditions.some(c => c.type === 'price'))?.conditions.find(c => c.type === 'price')?.price || undefined,
                mediaPermissions: settings.mediaPermissions,
                timelinePermission: settings.timelinePermission, // Crucial: directly use the setting from the dialog
              };
              console.log('privacySettings after update:', updatedSettings);
              return updatedSettings;
            });
            setIsPermissionsDialogOpen(false);
          }, [setIsPermissionsDialogOpen])}
        />
      }

      {/* Video Edit Modal */}
      {isVideoEditModalOpen && tempVideoFile && (
        <VideoEditDialog
          isOpen={isVideoEditModalOpen}
          onClose={() => {
            setIsVideoEditModalOpen(false);
            setTempVideoFile(null);
          }}
          videoFile={tempVideoFile.file}
          isClip={isClipMode}
          onSave={(videoData) => {
            const { file, thumbnailFile, thumbnailUrl, metadata } = videoData;

            // Create media preview
            const newMediaPreview: ExtendedMediaItem = {
              mediaUrl: URL.createObjectURL(file),
              file,
              mediaType: isClipMode ? 'video/clip' : file.type,
              thumbnailUrl,
              thumbnailFile,
              edited: false,
              metadata,
            };

            setMediaPreviews([newMediaPreview]);
            setIsVideoEditModalOpen(false);
            setTempVideoFile(null);
          }}
        />
      )}
    </div>
  );
};
export default CreatePostForm;