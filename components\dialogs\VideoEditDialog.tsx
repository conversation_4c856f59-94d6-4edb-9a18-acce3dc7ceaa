'use client';

import React, { useState, useRef, useEffect } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { toast } from 'react-toastify';
import { cn } from '@/lib/utils';
import { Camera, Upload, Play, Pause, Image as ImageIcon } from 'lucide-react';
import ImageEditDialog from './ImageEditDialog';
import { generateVideoThumbnails } from '@rajesh896/video-thumbnails-generator';
import Tippy from '@tippyjs/react';

interface VideoEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (videoData: {
    file: File;
    thumbnailFile: File;
    thumbnailUrl: string;
    metadata: {
      duration: number;
      resolution: string;
      aspectRatio: number;
    };
  }) => void;
  videoFile: File;
  isClip?: boolean;
}

const VideoEditDialog: React.FC<VideoEditDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  videoFile,
  isClip = false,
}) => {
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(null);
  const [customThumbnail, setCustomThumbnail] = useState<File | null>(null);
  const [isEditingThumbnail, setIsEditingThumbnail] = useState(false);
  const [thumbnailFrames, setThumbnailFrames] = useState<string[]>([]);
  const [isGeneratingThumbnails, setIsGeneratingThumbnails] = useState(false);
  const [randomThumbnailCount, setRandomThumbnailCount] = useState<number>(5);
  const [videoMetadata, setVideoMetadata] = useState<{
    duration: number;
    resolution: string;
    aspectRatio: number;
  } | null>(null);
  const [videoSrc, setVideoSrc] = useState<string>('');

  const videoRef = useRef<HTMLVideoElement>(null);
  const thumbnailInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && videoFile) {
      const url = URL.createObjectURL(videoFile);
      setVideoSrc(url);
      return () => {
        URL.revokeObjectURL(url);
        setVideoSrc('');
      };
    }
  }, [isOpen, videoFile]);

  const generateRandomThumbnails = async () => {
    if (randomThumbnailCount < 1 || randomThumbnailCount > 10) {
      toast.error('Please enter a number between 1 and 10');
      return;
    }
    setIsGeneratingThumbnails(true);
    try {
      const thumbnails = await captureVideoThumbnails(videoFile, randomThumbnailCount, true);
      setThumbnailFrames(thumbnails);
      setSelectedThumbnail(null);
      setCustomThumbnail(null);
      toast.success('Random thumbnails generated successfully');
    } catch (error) {
      console.error('Error generating random thumbnails:', error);
      toast.error('Failed to generate random thumbnails');
    } finally {
      setIsGeneratingThumbnails(false);
    }
  };

  const captureVideoThumbnails = async (
    file: File,
    count: number,
    random: boolean = false
  ): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      if (typeof window === 'undefined') {
        reject(new Error('Thumbnail generation requires browser environment'));
        return;
      }

      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      video.src = URL.createObjectURL(file);
      video.muted = true;
      video.preload = 'metadata';

      video.onloadedmetadata = async () => {
        const duration = video.duration;
        const times = random
          ? Array.from({ length: count }, () => Math.random() * duration)
          : Array.from({ length: count }, (_, i) => (i + 1) * (duration / (count + 1)));
        const thumbnails: string[] = [];

        const seekAndCapture = (time: number) =>
          new Promise<void>((resolveSeek) => {
            let resolved = false;
            const timeout = setTimeout(() => {
              if (!resolved) {
                resolved = true;
                resolveSeek();
              }
            }, 2000);

            video.currentTime = time;
            video.onseeked = () => {
              if (!resolved) {
                clearTimeout(timeout);
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                thumbnails.push(canvas.toDataURL('image/jpeg'));
                resolved = true;
                resolveSeek();
              }
            };
            video.onerror = () => {
              if (!resolved) {
                clearTimeout(timeout);
                resolved = true;
                resolveSeek();
              }
            };
          });

        for (let t of times) {
          await seekAndCapture(t);
        }

        URL.revokeObjectURL(video.src);
        resolve(thumbnails);
      };

      video.onerror = () => {
        URL.revokeObjectURL(video.src);
        reject(new Error('Failed to load video'));
      };
    });
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      const video = videoRef.current;
      setDuration(video.duration || 0);
      setVideoMetadata({
        duration: video.duration || 0,
        resolution: video.videoWidth && video.videoHeight ? `${video.videoWidth}x${video.videoHeight}` : 'Unknown',
        aspectRatio: video.videoWidth && video.videoHeight ? video.videoWidth / video.videoHeight : 0,
      });
    }
  };

  const handleVideoClick = async () => {
    if (videoRef.current) {
      try {
        if (isPlaying) {
          await videoRef.current.pause();
          setIsPlaying(false);
        } else {
          await videoRef.current.play();
          setIsPlaying(true);
        }
      } catch (err) {
        console.error('Error toggling video playback:', err);
        toast.error('Failed to toggle video playback');
        setIsPlaying(false);
      }
    }
  };

  const handleScrubberClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (videoRef.current && duration > 0) {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = x / rect.width;
      const newTime = percentage * duration;
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
      if (isPlaying) {
        videoRef.current.play().catch((err) => {
          console.error('Error resuming video:', err);
          toast.error('Failed to resume video');
        });
      }
    }
  };

  const captureThumbnail = async () => {
    if (videoRef.current) {
      try {
        const canvas = document.createElement('canvas');
        canvas.width = videoRef.current.videoWidth;
        canvas.height = videoRef.current.videoHeight;
        const ctx = canvas.getContext('2d');
        if (!ctx) throw new Error('Failed to get canvas context');
        ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
        const thumbnailUrl = canvas.toDataURL('image/jpeg');
        setThumbnailFrames([thumbnailUrl]);
        setSelectedThumbnail(thumbnailUrl);
        setCustomThumbnail(null);
        toast.success('Thumbnail captured');
      } catch (error) {
        console.error('Error capturing thumbnail:', error);
        toast.error('Failed to capture thumbnail');
      }
    }
  };

  const handleCustomThumbnailUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Please upload an image file');
        return;
      }
      try {
        const reader = new FileReader();
        reader.onload = (e) => {
          const thumbnailUrl = e.target?.result as string;
          setThumbnailFrames([thumbnailUrl]);
          setSelectedThumbnail(thumbnailUrl);
          setCustomThumbnail(file);
          toast.success('Custom thumbnail uploaded');
        };
        reader.onerror = () => {
          toast.error('Failed to read custom thumbnail');
        };
        reader.readAsDataURL(file);
      } catch (error) {
        console.error('Error uploading custom thumbnail:', error);
        toast.error('Failed to upload custom thumbnail');
      }
    }
    if (thumbnailInputRef.current) {
      thumbnailInputRef.current.value = '';
    }
  };

  const handleThumbnailSelect = (thumbnailUrl: string) => {
    setSelectedThumbnail(thumbnailUrl);
    if (thumbnailFrames.indexOf(thumbnailUrl) === thumbnailFrames.length - 1 && customThumbnail) {
      // Keep customThumbnail for uploaded thumbnails
    } else {
      setCustomThumbnail(null);
    }
  };

  const handleEditThumbnail = async () => {
    if (selectedThumbnail && !customThumbnail) {
      try {
        const res = await fetch(selectedThumbnail);
        const blob = await res.blob();
        const thumbnailFile = new File([blob], `thumbnail-${Date.now()}.jpg`, { type: 'image/jpeg' });
        setCustomThumbnail(thumbnailFile);
      } catch (error) {
        console.error('Error preparing thumbnail for editing:', error);
        toast.error('Failed to prepare thumbnail for editing');
        return;
      }
    }
    setIsEditingThumbnail(true);
  };

  const handleSave = async () => {
    if (!selectedThumbnail || !videoMetadata || !customThumbnail) {
      toast.error('Please select and edit a thumbnail first');
      return;
    }

    try {
      // Ensure metadata includes all required fields
      const enhancedMetadata = {
        ...videoMetadata,
        format: videoFile.type,
        size: videoFile.size,
        filename: videoFile.name,
        // Add any additional metadata the backend expects
      };

      onSave({
        file: videoFile,
        thumbnailFile: customThumbnail,
        thumbnailUrl: selectedThumbnail,
        metadata: enhancedMetadata,
      });
      toast.success('Video saved successfully');
      onClose();
    } catch (error) {
      console.error('Error saving video:', error);
      toast.error('Failed to save video');
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleVideoError = () => {
    toast.error('Failed to load video. Please try a different file.');
    setIsPlaying(false);
  };

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handlePause = () => {
    setIsPlaying(false);
  };

  const handleRandomThumbnailCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value)) {
      setRandomThumbnailCount(value);
    } else {
      setRandomThumbnailCount(0);
    }
  };

  return (
    <>
      <AnimatedModal
        open={isOpen}
        onOpenChange={onClose}
        size="6xl"
        title={isClip ? 'Edit Clip' : 'Edit Video'}
        footer={
          <div className="flex justify-between gap-2 w-full">
            <Button
              onClick={handleSave}
              className="w-full bg-turquoise hover:bg-turquoise/80 text-white"
              disabled={!selectedThumbnail || !customThumbnail}
            >
              Save Changes
            </Button>
            <Button onClick={onClose} variant="outline" className="w-full">
              Cancel
            </Button>
          </div>
        }
      >
        <div className="flex flex-col gap-6 p-4">
          {/* Video Preview */}
          <div
            className="relative w-full bg-black rounded-lg overflow-hidden"
            style={{ maxHeight: 'min(500px, 80vh)' }}
          >
            <video
              ref={videoRef}
              src={videoSrc}
              className="w-full h-auto"
              preload="metadata"
              onClick={handleVideoClick}
              onTimeUpdate={handleTimeUpdate}
              onLoadedMetadata={handleLoadedMetadata}
              onError={handleVideoError}
              onPlay={handlePlay}
              onPause={handlePause}
              style={{ aspectRatio: videoMetadata?.aspectRatio || '16/9' }}
            />

            {/* Play/Pause Overlay */}
            <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="icon"
                className="w-16 h-16 bg-black/50 hover:bg-black/60 text-white rounded-full"
                onClick={handleVideoClick}
              >
                {isPlaying ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
              </Button>
            </div>

            {/* Video Progress Bar */}
            <div
              className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700 cursor-pointer"
              onClick={handleScrubberClick}
            >
              <div
                className="h-full bg-turquoise"
                style={{ width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%' }}
              />
            </div>
          </div>

          {/* Video Info */}
          <div className="flex items-center justify-between text-sm">
            <span>
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
            {videoMetadata && (
              <span>
                {videoMetadata.resolution} • {videoMetadata.aspectRatio.toFixed(2)}:1
              </span>
            )}
          </div>

          {/* Thumbnail Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Select Thumbnail</h3>
              <div className="flex gap-2 items-center">
                <Tippy content="Capture current frame" placement="top" theme="sugar">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={captureThumbnail}
                    disabled={isGeneratingThumbnails || !videoMetadata}
                  >
                    <Camera className="h-4 w-4" />
                  </Button>
                </Tippy>
                <Tippy content="Upload custom thumbnail" placement="top" theme="sugar">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => thumbnailInputRef.current?.click()}
                    disabled={isGeneratingThumbnails}
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                </Tippy>
                <Tippy content="Number of random thumbnails (1-10)" placement="top" theme="sugar">
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={randomThumbnailCount}
                    onChange={handleRandomThumbnailCountChange}
                    className="w-16 p-1 border rounded-md text-sm"
                    disabled={isGeneratingThumbnails}
                  />
                </Tippy>
                <Tippy content="Generate random thumbnails" placement="top" theme="sugar">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={generateRandomThumbnails}
                    disabled={isGeneratingThumbnails || !videoMetadata}
                  >
                    Generate Random
                  </Button>
                </Tippy>
              </div>
            </div>

            <input
              ref={thumbnailInputRef}
              type="file"
              accept="image/*"
              onChange={handleCustomThumbnailUpload}
              className="hidden"
            />

            {/* Thumbnail Grid */}
            {isGeneratingThumbnails ? (
              <div className="space-y-2">
                <div className="text-sm text-gray-500">Generating thumbnails...</div>
                <Progress value={50} />
              </div>
            ) : thumbnailFrames.length > 0 ? (
              <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-7 gap-2">
                {thumbnailFrames.map((frame, index) => (
                  <div
                    key={index}
                    className={cn(
                      'relative aspect-video rounded-md overflow-hidden cursor-pointer border-solid border-2',
                      selectedThumbnail === frame ? 'border-turquoise' : 'border-transparent hover:border-gray-300'
                    )}
                    onClick={() => handleThumbnailSelect(frame)}
                  >
                    <img src={frame} alt={`Thumbnail ${index + 1}`} className="w-full h-full object-cover" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-gray-500">
                Capture a frame, upload an image, or generate random thumbnails.
              </div>
            )}

            {/* Edit Thumbnail Button */}
            {selectedThumbnail && (
              <Button
                variant="outline"
                className="mt-2"
                onClick={handleEditThumbnail}
              >
                <ImageIcon className="h-4 w-4 mr-2" />
                Edit Thumbnail
              </Button>
            )}
          </div>
        </div>
      </AnimatedModal>

      {/* Thumbnail Edit Dialog */}
      {isEditingThumbnail && selectedThumbnail && (
        <ImageEditDialog
          isOpen={isEditingThumbnail}
          onClose={() => setIsEditingThumbnail(false)}
          imageUrl={selectedThumbnail}
          imageIndex={0}
          onSave={async (editedThumbnail) => {
            try {
              setSelectedThumbnail(editedThumbnail);
              const res = await fetch(editedThumbnail);
              const blob = await res.blob();
              const thumbnailFile = new File([blob], `thumbnail-edited-${Date.now()}.jpg`, {
                type: 'image/jpeg',
              });
              setCustomThumbnail(thumbnailFile);
              setThumbnailFrames([editedThumbnail]);
              setIsEditingThumbnail(false);
              toast.success('Thumbnail edited successfully');
            } catch (error) {
              console.error('Error saving edited thumbnail:', error);
              toast.error('Failed to save edited thumbnail');
            }
          }}
        />
      )}
    </>
  );
};
export default VideoEditDialog;