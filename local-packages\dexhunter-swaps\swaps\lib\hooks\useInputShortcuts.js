import o from "../store/useStore.js";
import { useSwapAction as L } from "../swap/hooks/useSwapAction.js";
import { s as N } from "../shallow-27fd7e97.js";
import { useNotify as W } from "./useNotify.js";
import { round as f } from "../utils/formatNumber.js";
import { useMemo as q } from "react";
import "../_commonjsHelpers-10dfc225.js";
import "../store/createTokenSearchSlice.js";
import "../immer-548168ec.js";
import "../store/createWalletSlice.js";
import "../store/createSwapSettingsSlice.js";
import "../store/createGlobalSettingsSlice.js";
import "../store/createUserOrdersSlice.js";
import "../store/createSwapSlice.js";
import "../store/createChartSlice.js";
import "../store/createBasketSlice.js";
import "../swap/components/tokens.js";
import "../store/createModalWhatsNewSlice.js";
import "../store/createSwapParamsSlice.js";
import "../config/axios.js";
import "../axios-ddd885c5.js";
import "../index-ca8eb9e1.js";
import "../QueryClientProvider-6bcd4331.js";
import "react/jsx-runtime";
import "../react-toastify.esm-a636d9b1.js";
import "../assets/svg/IconCopy.js";
import "../assets/svg/IconX.js";
import "../assets/svg/IconCheckNotify.js";
import "../assets/svg/IconAlertTriangleNotify.js";
import "../assets/svg/IconArrowUpRightNotify.js";
import "../lib.js";
import "../extend-tailwind-merge-e63b2b56.js";
import "./useScreen.js";
const we = () => {
  var c;
  const { buyToken: S } = L(), { notify: u } = W(), {
    tokenSell: t,
    setSellAmount: n,
    toggleModal: g,
    flipTokens: y,
    setOrderType: d,
    orderType: k
  } = o(
    (e) => ({
      tokenBuy: e.swapSlice.tokenBuy,
      tokenSell: e.swapSlice.tokenSell,
      setSellAmount: e.swapSlice.setSellAmount,
      toggleModal: e.tokenSearchSlice.toggleModal,
      flipTokens: e.swapSlice.flipTokens,
      setOrderType: e.swapSlice.setOrderType,
      orderType: e.swapSlice.orderType
    }),
    N
  ), { defaultBuySize: D, toggleOpenShortcuts: w, toggleIsGlobalSettingsOpen: v, isShortcutsDisabled: l } = o((e) => e.globalSettingsSlice), { userTokens: T, balance: h } = o((e) => e.walletSlice), {
    setIsOpenSwapSetting: O,
    isOpenSwapSetting: I,
    isDetailsOpen: M,
    setIsDetailsOpen: b,
    isPriceFlipped: A,
    setIsPriceFlipped: F,
    slippage: p,
    setSlippage: r,
    isCustomSlippage: B
  } = o((e) => e.swapSettingsSlice), s = q(() => !!l, [l]), a = (t == null ? void 0 : t.token_ascii) === "Cardano" ? h : (c = T.find((e) => e.token_id === (t == null ? void 0 : t.token_id))) == null ? void 0 : c.amount, P = () => {
    a && n(parseFloat(a));
  }, m = (e) => {
    const i = {
      slippage: p,
      isCustomSlippage: B,
      ...e
    };
    localStorage.setItem("swapSettings", JSON.stringify(i));
  }, x = () => {
    const e = f(Math.min(p + 1, 99), 1);
    r(e), m({ slippage: e });
  }, _ = () => {
    const e = f(Math.max(p - 1, 1), 1);
    r(e), m({ slippage: e });
  }, C = () => {
    r(-1), u({
      type: "warning",
      title: "Slippage saved",
      desc: "Slippage tolerance is set to unlimited!"
    });
  }, K = () => {
    s || d(k === "SWAP" ? "LIMIT" : "SWAP");
  };
  return { handleKeyDown: (e) => {
    if (!s) {
      if (e.key === "Enter" && (e.preventDefault(), S()), e.key === "/" && (e.preventDefault(), g()), e.key === "q" && (e.preventDefault(), _()), e.key === "l" && (e.preventDefault(), K()), e.key === "u" && (e.preventDefault(), C()), e.key === "e" && (e.preventDefault(), x()), e.key === "k" && (e.preventDefault(), y()), e.key === "g" && (e.preventDefault(), v()), e.key === "i" && (e.preventDefault(), w()), e.key === "o" && e.preventDefault(), e.key === "p") {
        e.preventDefault();
        const i = !A;
        F(i), localStorage.setItem("priceFlipSetting", i.toString());
      }
      e.key === "s" && (e.preventDefault(), O(!I)), e.key === "d" && (e.preventDefault(), b(M ? "" : "open")), e.key === "n" && (e.preventDefault(), n(D)), e.key === "m" && (e.preventDefault(), P());
    }
  } };
};
export {
  we as useInputShortcuts
};
