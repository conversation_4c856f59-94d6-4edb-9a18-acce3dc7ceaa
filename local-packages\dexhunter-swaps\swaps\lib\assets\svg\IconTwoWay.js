import { jsx as o } from "react/jsx-runtime";
import { memo as e } from "react";
const C = (t) => /* @__PURE__ */ o(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 21 20",
    fill: "none",
    ...t,
    children: /* @__PURE__ */ o(
      "path",
      {
        d: "M20.0839 12.2727H0.228567C0.103026 12.2727 0.000311782 12.375 0.000311782 12.5V14.2045C0.000311782 14.3295 0.103026 14.4318 0.228567 14.4318H17.496L13.3789 19.6307C13.2619 19.7784 13.3675 20 13.5587 20H15.6272C15.767 20 15.8983 19.9375 15.9867 19.8267L20.8029 13.7443C21.2737 13.1477 20.8485 12.2727 20.0839 12.2727ZM20.7715 5.56818H3.50402L7.62117 0.369318C7.73815 0.221591 7.63258 0 7.44142 0H5.37286C5.23305 0 5.10181 0.0625002 5.01336 0.173296L0.197181 6.25568C-0.273594 6.85227 0.151531 7.72727 0.913331 7.72727H20.7715C20.897 7.72727 20.9998 7.625 20.9998 7.5V5.79545C20.9998 5.67045 20.897 5.56818 20.7715 5.56818Z",
        fill: "currentColor"
      }
    )
  }
), i = e(C);
export {
  i as default
};
