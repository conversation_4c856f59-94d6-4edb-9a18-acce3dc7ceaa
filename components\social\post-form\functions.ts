import { ExtendedMediaItem, ExtendedPostData, TaggedUser, PrivacySettings, Suggestion } from '@/types/post';
import { toast } from 'react-toastify';
import { format } from 'date-fns';
import { formatPostContent } from '@/lib/formatters';
import { extractMetadata } from '@/lib/utils';
import { generateVideoThumbnails, importFileandPreview } from '@rajesh896/video-thumbnails-generator';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';

const VIDEO_CONSTRAINTS = {
  maxDuration: 300, // 5 minutes
  maxSize: 150 * 1024 * 1024, // 150MB
};

const CLIP_CONSTRAINTS = {
  aspectRatio: 9 / 16, // Vertical video ratio (e.g., 1080x1920)
  tolerance: 0.1, // 10% tolerance for aspect ratio
  maxDuration: 60, // 60 seconds max
  minDuration: 3, // 3 seconds min
  maxSize: 100 * 1024 * 1024, // 100MB
};

export interface ClipValidationResult {
  isValid: boolean;
  error?: string;
  metadata?: {
    width: number;
    height: number;
    duration: number;
    aspectRatio: number;
  };
}

export interface TempVideoFile {
  file: File;
  videoUrl: string;
  thumbnails: string[];
  selectedThumbnail: string;
  thumbnailFile: File;
  metadata: ExtendedMediaItem['metadata'];
}

// Create a context interface for all the state setters and refs
export interface PostFormContext {
  setIsFileInputActive: (value: boolean) => void;
  setIsFocused: (value: boolean) => void;
  setMediaPreviews: React.Dispatch<React.SetStateAction<ExtendedMediaItem[]>>;
  setMediaItems: React.Dispatch<React.SetStateAction<ExtendedMediaItem[]>>;
  mediaItems: ExtendedMediaItem[];
  setHasUnsavedChanges: (value: boolean) => void;
  setIsUploading: (value: boolean) => void;
  setUploadProgresses: React.Dispatch<React.SetStateAction<number[]>>;
  setTempSelectedImages: React.Dispatch<React.SetStateAction<ExtendedMediaItem[]>>;
  setIsMultiImageModalOpen: (value: boolean) => void;
  setThumbnailOptions: React.Dispatch<React.SetStateAction<string[]>>;
  setSelectedThumbnailIndex: React.Dispatch<React.SetStateAction<number>>;
  setIsThumbnailModalOpen: (value: boolean) => void;
  setTempVideoFile: React.Dispatch<React.SetStateAction<TempVideoFile | null>>;
  tempVideoFile: TempVideoFile | null;
  setContent: (value: string) => void;
  setShowGifPicker: (value: boolean) => void;
  setMediaDescription: (value: string) => void;
  mediaDescription: string;
  setIsDescriptionModalOpen: (value: boolean) => void;
  setTaggedUsers: React.Dispatch<React.SetStateAction<TaggedUser[]>>;
  setIsTagPeopleModalOpen: (value: boolean) => void;
  setIsSubmitting: (value: boolean) => void;
  isFocused: boolean;
  setTagData: React.Dispatch<React.SetStateAction<{ hashtags: string[]; mentions: Suggestion[] }>>;
  setPrivacySettings: React.Dispatch<React.SetStateAction<PrivacySettings>>;
  setIsPermissionsDialogOpen: (value: boolean) => void;
  setIsImageEditModalOpen: (value: boolean) => void;
  setEditingImageIndex: React.Dispatch<React.SetStateAction<number | null>>;
  setScheduledDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  setScheduledTime: React.Dispatch<React.SetStateAction<string>>;
  setMediaType: React.Dispatch<React.SetStateAction<string | null>>;
  setMediaUrl: React.Dispatch<React.SetStateAction<string | null>>;
  setThumbnailUrl: React.Dispatch<React.SetStateAction<string | null>>;
  setTempMediaId: React.Dispatch<React.SetStateAction<string | null>>;
  tagData: { hashtags: string[]; mentions: Suggestion[] };
  taggedUsers: TaggedUser[];
  scheduledDate?: Date;
  scheduledTime: string;
  saveToLibrary: boolean;
  setSaveToLibrary: (value: boolean) => void;
  selectedThumbnailIndex: number;
  handleFileChange: (file: File, index: number, thumbnailFile?: File) => Promise<ExtendedMediaItem | null>;
  fileInputRef: React.RefObject<HTMLInputElement>;
  imageInputRef: React.RefObject<HTMLInputElement>;
  videoInputRef: React.RefObject<HTMLInputElement>;
  audioInputRef: React.RefObject<HTMLInputElement>;
  clipInputRef: React.RefObject<HTMLInputElement>;
  gifInputRef: React.RefObject<HTMLInputElement>;
  mediaPreviews: ExtendedMediaItem[];
  content: string;
  privacySettings: PrivacySettings;
  onSubmit: (postData: ExtendedPostData) => Promise<any>;
  validateClip: (file: File) => Promise<ClipValidationResult>;
  resetForm: () => void;
  setIsClipMode: (value: boolean) => void;
  setIsVideoEditModalOpen: (value: boolean) => void;
};

// Export all functions
export const createPostFormFunctions = (context: PostFormContext) => {
  const uploadFileMutation = useMutation(api.media.uploadFile);

  // Move handleFileChange inside createPostFormFunctions
  const handleFileChange = async (file: File, index: number, thumbnailFile?: File): Promise<ExtendedMediaItem | null> => {
    context.setIsUploading(true);
    context.setUploadProgresses((prev) => {
      const newProgresses = [...prev];
      newProgresses[index] = 0;
      return newProgresses;
    });
    try {
      // Convert file to base64
      const fileAsBase64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve((reader.result as string).split(',')[1]);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
      // If thumbnailFile is provided, convert to base64
      let thumbnailData = undefined;
      if (thumbnailFile) {
        const thumbBase64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve((reader.result as string).split(',')[1]);
          reader.onerror = reject;
          reader.readAsDataURL(thumbnailFile);
        });
        thumbnailData = {
          file: thumbBase64,
          fileType: thumbnailFile.type,
          fileName: thumbnailFile.name,
        };
      }
      // Call Convex mutation
      const result = await uploadFileMutation({
        userId: context.user.id, // or userId from context
        file: fileAsBase64,
        fileType: file.type,
        fileName: file.name,
        privacy: context.privacySettings.privacy,
        saveToLibrary: context.saveToLibrary,
        metadata: context.mediaPreviews[index]?.metadata,
        privateType: context.privacySettings.privateType,
        ppvPrice: context.privacySettings.ppvPrice,
        thumbnail: thumbnailData,
      });
      if (!result.success) {
        throw new Error(result.message || 'Upload failed');
      }
      const metadata = context.mediaPreviews[index]?.metadata || (await extractMetadata(file));
      const mediaItem: ExtendedMediaItem = {
        mediaUrl: result.url,
        thumbnailUrl: result.thumbnailUrl,
        mediaType: file.type,
        tempMediaId: result.tempMediaId,
        edited: false,
        metadata,
      };
      context.setMediaItems((prev) => {
        const newItems = [...prev];
        newItems[index] = mediaItem;
        return newItems;
      });
      context.setUploadProgresses((prev) => {
        const newProgresses = [...prev];
        newProgresses[index] = 100;
        return newProgresses;
      });
      toast.success(`Media ${index + 1} uploaded successfully!`);
      return mediaItem;
    } catch (error: any) {
      toast.error(`Failed to upload media ${index + 1}: ${error.message}`);
      context.setMediaPreviews((prev) => prev.filter((_, i) => i !== index));
      context.setMediaItems((prev) => prev.filter((_, i) => i !== index));
      return null;
    } finally {
      context.setIsUploading(false);
    }
  };

  return {
    handleImageUpload: async (e: React.ChangeEvent<HTMLInputElement>) => {
      context.setIsFileInputActive(false);
      e.preventDefault();
      const files = e.target.files;
      if (!files || files.length === 0) return;

      const newImagesPromises = Array.from(files).map(async (file) => {
        if (file.size > 10 * 1024 * 1024) {
          toast.error(`File ${file.name} exceeds 10MB limit`);
          return null;
        }
        if (!file.type.startsWith('image/')) {
          toast.error(`File ${file.name} is not an image`);
          return null;
        }
        try {
          const metadata = await extractMetadata(file);
          const uniqueId = `image-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
          return {
            id: uniqueId,
            file,
            mediaUrl: URL.createObjectURL(file),
            localUrl: URL.createObjectURL(file),
            mediaType: file.type,
            edited: false,
            metadata,
          };
        } catch (error) {
          toast.error(`Failed to extract metadata for ${file.name}`);
          return null;
        }
      });

      const newImages = (await Promise.all(newImagesPromises)).filter(
        (item): item is NonNullable<typeof item> => item !== null
      );

      if (newImages.length === 0) return;

      context.setTempSelectedImages(newImages);
      context.setIsMultiImageModalOpen(true);

      if (context.imageInputRef.current) {
        context.imageInputRef.current.value = '';
      }
    },

    handleVideoInputChange: async (e: React.ChangeEvent<HTMLInputElement>) => {
      context.setIsFileInputActive(false);
      const file = e.target.files?.[0] as any;
      if (!file) return;

      if (file.size > VIDEO_CONSTRAINTS.maxSize) {
        toast.error('File size exceeds 150MB limit');
        return;
      }

      const supportedFormats = ['video/mp4', 'video/webm', 'video/ogg'];
      if (!file.type.startsWith('video/') || !supportedFormats.includes(file.type)) {
        toast.error('Only MP4, WebM, and OGG video files are supported');
        return;
      }

      // Set video file and open edit dialog
      context.setTempVideoFile(file);
      context.setIsClipMode(false);
      context.setIsVideoEditModalOpen(true);
    },

    handleGifInputChange: async (e: React.ChangeEvent<HTMLInputElement>) => {
      context.setIsFileInputActive(false);
      const file = e.target.files?.[0];
      if (!file) return;

      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size exceeds 10MB limit');
        return;
      }

      if (!file.type.startsWith('image/gif')) {
        toast.error('Only GIF files are supported');
        return;
      }

      context.setIsUploading(true);
      try {
        const fileUrl = URL.createObjectURL(file);
        const metadata = await extractMetadata(file);
        const newMediaPreview: ExtendedMediaItem = {
          mediaUrl: fileUrl,
          file,
          mediaType: file.type,
          edited: false,
          metadata,
        };

        context.setMediaPreviews([newMediaPreview]);
        context.setMediaItems([newMediaPreview]);
        context.setHasUnsavedChanges(true);
      } catch (error) {
        console.error('GIF upload failed:', error);
        toast.error('Failed to upload GIF');
      } finally {
        context.setIsUploading(false);
      }
    },

    handleClipInputChange: async (e: React.ChangeEvent<HTMLInputElement>) => {
      context.setIsFileInputActive(false);
      const file = e.target.files?.[0] as any;
      if (!file) return;

      if (file.size > CLIP_CONSTRAINTS.maxSize) {
        toast.error(`File size exceeds ${CLIP_CONSTRAINTS.maxSize / (1024 * 1024)}MB limit`);
        return;
      }

      if (!file.type.startsWith('video/')) {
        toast.error('Only video files are supported');
        return;
      }

      // Set video file and open edit dialog in clip mode
      context.setTempVideoFile(file);
      context.setIsClipMode(true);
      context.setIsVideoEditModalOpen(true);
    },

    handleAudioInputChange: async (e: React.ChangeEvent<HTMLInputElement>) => {
      context.setIsFileInputActive(false);
      const file = e.target.files?.[0];
      if (!file) return;

      if (file.size > 50 * 1024 * 1024) {
        toast.error('File size exceeds 50MB limit');
        return;
      }

      if (!file.type.startsWith('audio/')) {
        toast.error('Only audio files are supported');
        return;
      }

      if (context.mediaPreviews.length > 0) {
        toast.error('Cannot mix audio with other media types in a single post');
        return;
      }

      context.setIsUploading(true);
      try {
        const fileUrl = URL.createObjectURL(file);
        const metadata = await extractMetadata(file);
        const newMediaPreview: ExtendedMediaItem = {
          mediaUrl: fileUrl,
          file,
          mediaType: file.type,
          edited: false,
          metadata,
        };

        context.setMediaPreviews([newMediaPreview]);
        context.setMediaItems([newMediaPreview]);
        context.setHasUnsavedChanges(true);
      } catch (error) {
        console.error('Audio upload failed:', error);
        toast.error('Failed to upload audio');
      } finally {
        context.setIsUploading(false);
      }
    },

    handleRemoveMedia: (index: number) => {
      context.setMediaPreviews((prev) => {
        const item = prev[index];
        if (item.mediaUrl) {
          URL.revokeObjectURL(item.mediaUrl);
        }
        if (item.thumbnailUrl) {
          URL.revokeObjectURL(item.thumbnailUrl);
        }
        return prev.filter((_, i) => i !== index);
      });
      context.setMediaItems((prev) => prev.filter((_, i) => i !== index));
      context.setUploadProgresses((prev) => prev.filter((_, i) => i !== index));
      context.setHasUnsavedChanges(context.mediaPreviews.length > 1 || !!context.content);
      if (context.fileInputRef.current) {
        context.fileInputRef.current.value = '';
      }
    },

    handleEmojiSelect: (emoji: string) => {
      context.setIsFocused(true);
      const editorDiv = document.querySelector('[contenteditable]');
      if (!editorDiv) return;

      const selection = window.getSelection();
      const range = selection?.getRangeAt(0);

      if (!selection || !range) return;

      const emojiNode = document.createTextNode(emoji);
      range.deleteContents();
      range.insertNode(emojiNode);

      range.setStartAfter(emojiNode);
      range.setEndAfter(emojiNode);
      selection.removeAllRanges();
      selection.addRange(range);

      const event = new Event('input', { bubbles: true });
      editorDiv.dispatchEvent(event);

      (editorDiv as HTMLElement).focus();
    },

    handleGifSelect: (gif: { images: { original: { url: string } } } | null) => {
      if (gif && gif.images) {
        context.setMediaPreviews([
          {
            mediaUrl: gif.images.original.url,
            file: undefined,
            mediaType: 'image/gif',
            edited: false,
            metadata: { format: 'gif' },
          },
        ]);
        context.setMediaItems([]);
        context.setShowGifPicker(false);
        context.setHasUnsavedChanges(true);
      }
    },

    handleEditedImage: (editedImageData: string, imageIndex: number) => {
      const byteString = atob(editedImageData.split(',')[1]);
      const mimeString = editedImageData.split(',')[0].split(':')[1].split(';')[0];
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([ab], { type: mimeString });
      const file = new File([blob], `edited-image-${imageIndex}.jpg`, { type: 'image/jpeg' });

      context.setMediaPreviews((prev) =>
        prev.map((item, i) =>
          i === imageIndex
            ? {
              ...item,
              mediaUrl: editedImageData,
              file,
              mediaType: 'image/jpeg',
              edited: true,
              metadata: { ...item.metadata, format: 'jpeg' },
            }
            : item
        )
      );
      context.setMediaItems((prev) =>
        prev.map((item, i) =>
          i === imageIndex
            ? {
              ...item,
              mediaUrl: editedImageData,
              mediaType: 'image/jpeg',
              edited: true,
              metadata: { ...item.metadata, format: 'jpeg' },
            }
            : item
        )
      );
      context.setHasUnsavedChanges(true);
    },

    handleDescriptionSave: (description: string) => {
      context.setMediaDescription(description);
      context.setIsDescriptionModalOpen(false);
    },

    handleTaggedPeopleSave: (tags: TaggedUser[]) => {
      context.setTaggedUsers(tags);
      context.setIsTagPeopleModalOpen(false);
    },

    handleFileChange,

    handleMultiImageSave: async (images: ExtendedMediaItem[]) => {
      const uploadPromises = images.map(async (img, index) => {
        if (!img.file) return img; // Already uploaded (e.g., from MediaLibrary)
        const uploadedItem = await context.handleFileChange(img.file, index);
        return uploadedItem || img;
      });

      const uploadedImages = (await Promise.all(uploadPromises)).filter(
        (item): item is ExtendedMediaItem => item !== null
      );

      const newMediaPreviews = uploadedImages.map((img) => ({
        ...img,
        mediaUrl: img.localUrl || img.mediaUrl, // Use localUrl for display
        serverMediaUrl: img.mediaUrl, // Store server URL
      }));

      context.setMediaPreviews(newMediaPreviews);
      context.setMediaItems(uploadedImages);
      context.setHasUnsavedChanges(true);
      context.setTempSelectedImages([]);
      context.setIsMultiImageModalOpen(false);
    },

    handleSubmit: async (isScheduled: boolean = false) => {
      const contentStr = context.content || '';
      if (!contentStr.trim() && context.mediaPreviews.length === 0 && context.mediaItems.length === 0) {
        toast.error('Please add some content or media to your post');
        return;
      }

      if (
        context.privacySettings.privacy === 'private' &&
        context.privacySettings.privateType === 'payPerView' &&
        !context.privacySettings.ppvPrice
      ) {
        toast.error('Please set a price for Pay-Per-View posts');
        return;
      }

      if (isScheduled) {
        if (!context.scheduledDate || !context.scheduledTime) {
          toast.error('Please select a date and time for scheduling');
          return;
        }
        const scheduledDateTime = new Date(`${format(context.scheduledDate, 'yyyy-MM-dd')}T${context.scheduledTime}`);
        if (scheduledDateTime < new Date()) {
          toast.error('Scheduled time cannot be in the past');
          return;
        }
      }

      context.setIsSubmitting(true);

      try {
        const { hashtags, mentions } = context.tagData;
        const uniqueHashtags = [...new Set(hashtags)];

        let finalMediaItems: ExtendedMediaItem[] = [];
        if (context.mediaPreviews.length > 0) {
          const uploadPromises = context.mediaPreviews.map(async (preview, i) => {
            if (preview.tempMediaId && preview.serverMediaUrl) {
              return {
                mediaUrl: preview.serverMediaUrl,
                mediaType: preview.mediaType,
                thumbnailUrl: preview.thumbnailUrl,
                tempMediaId: preview.tempMediaId,
                edited: preview.edited || false,
                metadata: preview.metadata,
              };
            }

            if (context.mediaItems[i]?.mediaUrl && !context.mediaItems[i].mediaUrl.startsWith('blob:')) {
              return context.mediaItems[i];
            }

            if (preview.file || preview?.mediaUrl?.startsWith('blob:') || preview.mediaType === 'image/gif') {
              let fileToUpload = preview.file;

              if (!fileToUpload && preview.mediaUrl) {
                try {
                  const response = await fetch(preview.mediaUrl);
                  const blob = await response.blob();
                  fileToUpload = new File([blob], `gif-${Date.now()}.gif`, { type: 'image/gif' });
                } catch (error) {
                  console.error('Error converting GIF URL to file:', error);
                  throw new Error('Failed to process GIF');
                }
              }

              if (fileToUpload) {
                const uploadedItem = await context.handleFileChange(fileToUpload, i);
                if (uploadedItem) {
                  return uploadedItem;
                }
              }
            }

            toast.error(`Failed to process media item ${i + 1}`);
            return null;
          });

          finalMediaItems = (await Promise.all(uploadPromises)).filter(
            (item): item is ExtendedMediaItem => item !== null
          );
        }

        finalMediaItems = finalMediaItems.map((item) => ({
          ...item,
          mediaUrl: item?.mediaUrl?.startsWith('blob:') && item.serverMediaUrl ? item.serverMediaUrl : item.mediaUrl,
        }));

        const normalizedContent = contentStr
          .split('\n')
          .map((line) => line.trim())
          .join('\n')
          .trim();

        const postData: ExtendedPostData = {
          content: normalizedContent,
          media:
            finalMediaItems.length > 0
              ? finalMediaItems.map((item) => ({
                mediaUrl: item.mediaUrl,
                thumbnailUrl: item.thumbnailUrl,
                mediaType: item.mediaType,
                tempMediaId: item.tempMediaId,
                description: context.mediaDescription.trim() || undefined,
                metadata: item.metadata,
              }))
              : undefined,
          hashtags: uniqueHashtags,
          mentions: mentions.map((m) => ({
            userId: m.id,
            username: m.value,
            profilePhoto: m.avatar,
          })),
          taggedUsers: context.taggedUsers,
          privacy: context.privacySettings.privacy === 'default' ? undefined : context.privacySettings.privacy,
          privateType: context.privacySettings.privacy !== 'public' ? context.privacySettings.privateType : undefined,
          ppvPrice:
            context.privacySettings.privacy === 'private' && context.privacySettings.privateType === 'payPerView'
              ? context.privacySettings.ppvPrice
              : undefined,
          scheduledDate: isScheduled && context.scheduledDate && context.scheduledTime
            ? new Date(`${format(context.scheduledDate, 'yyyy-MM-dd')}T${context.scheduledTime}`).toISOString()
            : undefined,
        };

        const newPost = await context.onSubmit(postData);
        if (newPost) {
          context.setHasUnsavedChanges(false);
          context.resetForm();
        }
      } catch (error) {
        console.error('Error creating post:', error);
        toast.error('Failed to create post. Please try again.');
      } finally {
        context.setIsSubmitting(false);
      }
    },

    resetForm: () => {
      if (context.fileInputRef.current) context.fileInputRef.current.value = '';
      if (context.imageInputRef.current) context.imageInputRef.current.value = '';
      context.setContent('');
      context.setTagData({ hashtags: [], mentions: [] });
      context.setMediaItems([]);
      context.setMediaPreviews([]);
      context.setPrivacySettings({
        privacy: 'default',
        privateType: 'subscription',
        ppvPrice: '',
      });
      context.setMediaDescription('');
      context.setTaggedUsers([]);
      context.setUploadProgresses([]);
      context.setMediaType(null);
      context.setMediaUrl(null);
      context.setThumbnailUrl(null);
      context.setTempMediaId(null);
      context.setThumbnailOptions([]);
      context.setIsThumbnailModalOpen(false);
      context.setTempVideoFile(null);
      context.setShowGifPicker(false);
      context.setIsImageEditModalOpen(false);
      context.setEditingImageIndex(null);
      context.setScheduledDate(undefined);
      context.setScheduledTime('');
      context.setSaveToLibrary(false);
      context.setHasUnsavedChanges(false); // Moved to end
    },

    handleContentChange: (newContent: string) => {
      const formattedContent = formatPostContent(newContent);
      context.setContent(formattedContent);
      context.setHasUnsavedChanges(!!newContent || context.mediaPreviews.length > 0);
      if (!context.isFocused) context.setIsFocused(true);
    },

    handleMediaLibrarySelect: (
      mediaUrl: string,
      mediaType: string,
      thumbnailUrl: string,
      tempMediaId?: string,
      metadata?: ExtendedMediaItem['metadata']
    ) => {
      if (mediaType.startsWith('video/') && context.mediaPreviews.length > 0) {
        toast.error('Cannot mix images and videos in a single post');
        return;
      }
      if (mediaType.startsWith('image/') && context.mediaPreviews.some((item) => item.mediaType?.startsWith('video/'))) {
        toast.error('Cannot mix images and videos in a single post');
        return;
      }
      context.setMediaItems((prev) => [...prev, { mediaUrl, mediaType, thumbnailUrl, tempMediaId, edited: false, metadata }]);
      context.setMediaPreviews((prev) => [...prev, { mediaUrl, file: undefined, mediaType, edited: false, metadata }]);
      context.setHasUnsavedChanges(true);
    },

    handleThumbnailConfirm: async () => {
      if (!context.tempVideoFile) return;

      const { file, videoUrl, thumbnails, metadata } = context.tempVideoFile;

      try {
        const thumbnailBlob = await fetch(thumbnails[context.selectedThumbnailIndex]).then((res) => res.blob());
        const thumbnailFile = new File([thumbnailBlob], `thumbnail-${file.name}.jpg`, { type: 'image/jpeg' });

        const newMediaPreview: ExtendedMediaItem = {
          mediaUrl: videoUrl,
          file,
          mediaType: file.type,
          thumbnailUrl: thumbnails[context.selectedThumbnailIndex],
          thumbnailFile,
          edited: false,
          metadata,
        };

        const mediaItem = await context.handleFileChange(file, 0, thumbnailFile);

        if (mediaItem) {
          context.setMediaPreviews([newMediaPreview]);
          context.setMediaItems([mediaItem]);
          context.setHasUnsavedChanges(true);
          context.setIsThumbnailModalOpen(false);
          context.setTempVideoFile(null);
        } else {
          throw new Error('Failed to upload video');
        }
      } catch (err) {
        console.error('Error processing thumbnail:', err);
        toast.error('Failed to process thumbnail or upload video');
      }
    },

    handlePrivacyChange: (value: string) => {
      context.setPrivacySettings(prevSettings => ({
        ...prevSettings,
        privacy: value as 'default' | 'public' | 'private',
      }));

      if (value === 'private') {
        context.setIsPermissionsDialogOpen(true);
      }
    },

    // Handle file input triggers with timeout for cancellation
    triggerFileInput: (inputRef: React.RefObject<HTMLInputElement>) => {
      context.setIsFileInputActive(true);
      context.setIsFocused(true);
      inputRef.current?.click();
      // Fallback for dialog cancellation
      setTimeout(() => context.setIsFileInputActive(false), 5000);
    },
  };
};

// Move validateClip outside and export it
export const validateClip = async (file: File): Promise<ClipValidationResult> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.preload = 'metadata';

    video.onloadedmetadata = () => {
      URL.revokeObjectURL(video.src);

      const width = video.videoWidth;
      const height = video.videoHeight;
      const duration = video.duration;
      const aspectRatio = width / height;
      const targetRatio = CLIP_CONSTRAINTS.aspectRatio;
      const tolerance = CLIP_CONSTRAINTS.tolerance;

      // Check aspect ratio
      const isRatioValid = Math.abs(aspectRatio - targetRatio) <= tolerance;

      if (!isRatioValid) {
        resolve({
          isValid: false,
          error: 'Video must be in vertical format (9:16 aspect ratio) like TikTok/Reels',
          metadata: { width, height, duration, aspectRatio }
        });
        return;
      }

      // Check duration
      if (duration > CLIP_CONSTRAINTS.maxDuration) {
        resolve({
          isValid: false,
          error: `Video must be shorter than ${CLIP_CONSTRAINTS.maxDuration} seconds`,
          metadata: { width, height, duration, aspectRatio }
        });
        return;
      }

      if (duration < CLIP_CONSTRAINTS.minDuration) {
        resolve({
          isValid: false,
          error: `Video must be longer than ${CLIP_CONSTRAINTS.minDuration} seconds`,
          metadata: { width, height, duration, aspectRatio }
        });
        return;
      }

      resolve({
        isValid: true,
        metadata: { width, height, duration, aspectRatio }
      });
    };

    video.onerror = () => {
      resolve({
        isValid: false,
        error: 'Failed to read video metadata'
      });
    };

    video.src = URL.createObjectURL(file);
  });
};