'use client';

import { useState, useEffect } from 'react';
import { useId } from 'react';
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

interface SearchableDropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  label: string;
  fixedPlaceholder?: string;
}

// SearchableDropdown component
const SearchableDropdown = ({ value, onChange, options, label, fixedPlaceholder }: SearchableDropdownProps) => {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');

  // Filter options based on search input (case-insensitive substring match on label)
  const filteredOptions = options.filter((option: any) =>
    option?.label?.toLowerCase().includes(search.toLowerCase())
  );

  // Find the selected option to display its label
  const selectedOption = options.find((option) => option.value === value);

  return (
    <div className="space-y-2 w-full">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            role="combobox"
            aria-expanded={open}
            className="flex w-full items-center justify-between rounded-md bg-transparent hover:bg-transparent font-normal capitalize px-3 py-2 h-10 text-sm dark:bg-transparent text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white max-w-full truncate"
          >
            <span className="truncate max-w-full">
              {fixedPlaceholder
                ? `${fixedPlaceholder}${selectedOption ? selectedOption.label : 'All'}`
                : selectedOption
                ? selectedOption.label
                : `Select ${label?.toLowerCase()}`}
            </span>
            <span className="ml-2">
              <svg
                width={16}
                height={16}
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className={`fill-current transition-transform duration-200 text-gorilla-gray dark:text-white ${open ? 'rotate-180' : ''}`}
              >
                <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
              </svg>
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full mt-1 rounded-lg bg-white dark:bg-black border border-solid border-gray-400 dark:border-white py-2 px-0 overflow-hidden max-w-[222px]"
          align="start"
        >
          <Command>
            <CommandInput
              placeholder={`Search ${label?.toLowerCase()}...`}
              includeIcon={false}
              className="bg-transparent px-3 py-2 text-sm text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white rounded-lg mb-2"
              value={search}
              onValueChange={setSearch}
            />
            <CommandList className="overflow-y-auto max-h-[200px]">
              <CommandEmpty className="px-3 py-2 text-sm text-gorilla-gray dark:text-gray-300">
                No option found.
              </CommandEmpty>
              <CommandGroup>
                {filteredOptions.map((option: any) => (
                  <CommandItem
                    key={option.value}
                    value={option.label}
                    onSelect={(currentLabel) => {
                      const selectedOption = options.find((opt) => opt.label === currentLabel);
                      onChange(selectedOption ? selectedOption.value : 'all');
                      setOpen(false);
                      setSearch('');
                    }}
                    className="cursor-pointer px-3 py-2 text-sm text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white focus:text-gray-600 dark:focus:text-white hover:bg-transparent dark:hover:bg-transparent border border-solid border-gray-300 dark:border-white border-t-0 border-l-0 border-r-0 first:border-t-0 last:border-b-0 shadow-none rounded-none"
                  >
                    {option.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};
export default SearchableDropdown;