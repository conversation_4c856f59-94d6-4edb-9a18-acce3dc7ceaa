import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import bcrypt from "bcryptjs";
import { api } from './_generated/api';
import { generateRandomId } from './utils';

export const checkUsername = query({
  args: { username: v.string() },
  handler: async ({ db }, { username }) => {
    const userDoc = await db
      .query("Accounts")
      .withIndex("by_username", (q) => q.eq("user_info.account.username", username.toLowerCase()))
      .first();

    return userDoc !== null;
  }
});

export const listCreators = query({
  args: {
    limit: v.number(),
    cursor: v.union(v.string(), v.null()),
  },
  handler: async (ctx, args) => {
    // 1. Fetch a paginated list of creators, sorted by creation time.
    // This is much more efficient than fetching the whole table.
    const paginationResult = await ctx.db
      .query("Accounts")
      .withIndex("by_type_and_reg_date", (q) => q.eq("account_type", "creator"))
      .order("desc")
      .paginate({ numItems: args.limit, cursor: args.cursor });

    const { page: creators, isDone, continueCursor } = paginationResult;

    // 2. For the current page of creators, fetch their post and follower counts.
    // This is more efficient than reading the entire Follows and Content tables.
    const enrichedCreators = await Promise.all(
      creators.map(async (creator) => {
        // Fetch post count using the new index
        const postCount = (
          await ctx.db
            .query("Content")
            .withIndex("by_creator", (q) => q.eq("creator_id", creator.user_id))
            .collect()
        ).length;

        // Fetch follower count using the new index
        const followerCount = (
          await ctx.db
            .query("Follows")
            .withIndex("by_followed_id", (q) => q.eq("followed_id", creator.user_id))
            .collect()
        ).length;

        return {
          ...creator,
          posts: postCount,
          followers: followerCount,
        };
      })
    );

    return {
      creators: enrichedCreators,
      isDone,
      cursor: continueCursor,
    };
  },
});

export const creatorLogin = mutation({
  args: {
    email: v.optional(v.string()),
    password: v.optional(v.string()),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    address: v.optional(v.string()),
    blockchain: v.optional(v.string()),
    userType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { email, password, address, blockchain } = args;

    // Validate required fields
    if (!email && !password && !address && !blockchain) {
      throw new Error("Email/password or wallet address/blockchain are required");
    }

    let user;

    // Email/password authentication
    if (email && password) {
      const userDoc = await ctx.db
        .query("Accounts")
        .filter((q) => q.eq(q.field("email"), email))
        .first();

      if (!userDoc) {
        throw new Error("Invalid email credentials");
      }

      // Check if password exists before comparing
      if (!userDoc.password) {
        throw new Error("Invalid password credentials");
      }

      // Compare the provided password hash with the stored hash
      const match = require('bcryptjs').compareSync(password, userDoc.password);
      if (!match) {
        throw new Error("Invalid credentials, no match");
      }

      user = userDoc;
    }
    // Web3 authentication
    else if (address && blockchain) {
      const userDoc = await ctx.db
        .query("Accounts")
        .filter((q) =>
          q.and(
            q.eq(q.field("wallet_address"), address),
            q.eq(q.field("blockchain"), blockchain)
          )
        )
        .first();

      if (!userDoc) {
        throw new Error("User not found");
      }

      user = userDoc;
    }

    if (!user) {
      throw new Error("Authentication failed");
    }

    // Log the successful login
    await ctx.db.insert("LoginLogs", {
      id: Date.now(),
      user_id: user.user_id,
      status: "success",
      reason: "User logged in",
      login_time: new Date().toISOString(),
      ip_address: args.ipAddress || "unknown",
      user_agent: args.userAgent || "unknown",
    });

    // Update user_info.status and user_info.last_active
    const updatedUserInfo = {
      ...user.user_info,
      status: 'online',
      last_active: Date.now(),
    };
    await ctx.db.patch(user._id, { user_info: updatedUserInfo });

    // Return user data without sensitive information
    return {
      success: true,
      user: {
        userId: user._id,
        email: user.email,
        walletAddress: user.wallet_address,
        blockchain: user.blockchain,
        accountType: user.account_type,
        userInfo: user.user_info,
      },
      message: "Login successful",
    };
  },
});

export const creatorSignup = mutation({
  args: {
    email: v.optional(v.string()),
    password: v.optional(v.string()),
    address: v.optional(v.string()),
    blockchain: v.optional(v.string()),
    username: v.optional(v.string()),
    userType: v.optional(v.string()),
    user_info: v.optional(v.any()), // Accepts full user_info object
    ip_address: v.optional(v.string()),
    user_agent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const {
      email = '',
      password = '',
      address = '',
      blockchain = '',
      username = '',
      userType = 'creator',
      user_info = {},
      ip_address = '',
      user_agent = '',
    } = args;

    // Validation
    if (!address && (!email || !password)) {
      await ctx.db.insert("LoginLogs", {
        user_id: null,
        ip_address,
        user_agent,
        id: Date.now(),
        login_time: new Date().toISOString(),
        status: "failed",
        reason: "Email and password are required for email signup",
      });
      throw new Error("Email and password are required for email signup");
    }
    if (address && !blockchain) {
      await ctx.db.insert("LoginLogs", {
        user_id: null,
        ip_address,
        user_agent,
        id: Date.now(),
        login_time: new Date().toISOString(),
        status: "failed",
        reason: "Blockchain is required for Web3 signup",
      });
      throw new Error("Blockchain is required for Web3 signup");
    }

    // Check for existing user by email or wallet_address
    const existingUser = await ctx.db
      .query("Accounts")
      .filter((q) => {
        const conditions = [];
        if (email) {
          conditions.push(q.eq(q.field("email"), email));
        }
        if (address) {
          conditions.push(q.eq(q.field("wallet_address"), address));
        }
        return q.or(...conditions);
      })
      .first();
    if (existingUser) {
      await ctx.db.insert("LoginLogs", {
        user_id: existingUser.user_id,
        ip_address,
        user_agent,
        id: Date.now(),
        login_time: new Date().toISOString(),
        status: "failed",
        reason: "User already exists",
      });
      return { success: false, message: "User already exists" };
    }

    // Check for username uniqueness
    if (username) {
      const userWithUsername = await ctx.db
        .query("Accounts")
        .filter((q) => q.eq(q.field("user_info.account.username"), username))
        .first();
      if (userWithUsername) {
        await ctx.db.insert("LoginLogs", {
          user_id: null,
          ip_address,
          user_agent,
          id: Date.now(),
          login_time: new Date().toISOString(),
          status: "failed",
          reason: "Username already taken",
        });
        return { success: false, message: "Username already taken" };
      }
    }

    // Username generation (if not provided)
    function randomUsername(base: string) {
      const randomNumber = Math.floor(100000 + Math.random() * 900000);
      return `${base}${randomNumber}`;
    }
    let finalUsername = username;
    if (!finalUsername) {
      let baseUsername = email ? email.split("@")[0] : address ? `SC${address.slice(2, 6)}${address.slice(-4)}` : "SC";
      let unique = false;
      while (!unique) {
        finalUsername = randomUsername(baseUsername);
        const exists = await ctx.db
          .query("Accounts")
          .filter((q) => q.eq(q.field("user_info.account.username"), finalUsername))
          .first();
        if (!exists) unique = true;
      }
    }

    // Hash password (if provided)
    const hashedPassword = password ? bcrypt.hashSync(password, 10) : ''; // Hash the password with bcrypt

    // Generate user_id (UUID-like)
    function uuidv4() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
    const user_id = uuidv4();

    // Compose user_info
    const fullUserInfo = {
      ...user_info,
      account: {
        ...(user_info.account || {}),
        username: finalUsername,
        email,
        accountType: userType,
      },
      nonce: '', // JWT not supported in Convex
      status: 'online',
      last_active: Date.now(),
    };

    // Insert user
    await ctx.db.insert("Accounts", {
      user_id,
      email,
      password: hashedPassword,
      wallet_address: address,
      blockchain,
      account_type: userType,
      registration_date: new Date().toISOString(),
      user_info: fullUserInfo,
    });

    // Log signup
    await ctx.db.insert("LoginLogs", {
      user_id,
      ip_address,
      user_agent,
      id: Date.now(),
      login_time: new Date().toISOString(),
      status: "success",
      reason: "User signed up",
    });

    return {
      success: true,
      user: {
        user_id,
        email,
        wallet_address: address,
        blockchain,
        user_info: {
          account: {
            username: finalUsername,
            displayName: fullUserInfo.account.displayName || '',
            accountType: userType,
          },
          profilePhoto: fullUserInfo.profilePhoto || '',
        },
      },
      blockchain,
      message: "Account created successfully",
    };
  },
});

export const getNonce = query({
  args: {
    userAddress: v.string(),
    accountType: v.optional(v.string()),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{ nonce: string; userId: string | null }> => {
    // 1. Find user by stake key in user_info
    const accounts = await ctx.db
      .query("Accounts")
      .withIndex("by_stake_key", (q) =>
        q.eq("user_info.stake_key", args.userAddress)
      )
      .collect();

    const userId = accounts[0]?.user_id || null;

    // 2. Generate nonce directly (no need for Node.js action for simple string generation)
    const nonce = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    return {
      nonce,
      userId,
    };
  },
});

export const userLogin = mutation({
  args: {
    email: v.optional(v.string()),
    password: v.optional(v.string()),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    address: v.optional(v.string()),
    blockchain: v.optional(v.string()),
    userType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { email, password, address, blockchain } = args;

    if (!email && !password && !address && !blockchain) {
      throw new Error("Email/password or wallet address/blockchain are required");
    }

    let user;

    if (email && password) {
      const userDoc = await ctx.db
        .query("Accounts")
        .filter((q) => q.eq(q.field("email"), email))
        .first();
      if (!userDoc || !userDoc.password) {
        throw new Error("Invalid credentials");
      }
      const match = require('bcryptjs').compareSync(password, userDoc.password);
      if (!match) {
        throw new Error("Invalid credentials");
      }
      user = userDoc;
    } else if (address && blockchain) {
      const userDoc = await ctx.db
        .query("Accounts")
        .filter((q) =>
          q.and(
            q.eq(q.field("wallet_address"), address),
            q.eq(q.field("blockchain"), blockchain)
          )
        )
        .first();
      if (!userDoc) {
        throw new Error("User not found");
      }
      user = userDoc;
    }
    if (!user) {
      throw new Error("Authentication failed");
    }
    await ctx.db.insert("LoginLogs", {
      id: Date.now(),
      user_id: user.user_id,
      status: "success",
      reason: "User logged in",
      login_time: new Date().toISOString(),
      ip_address: args.ipAddress || "unknown",
      user_agent: args.userAgent || "unknown",
    });

    // Update user_info.status and user_info.last_active
    const updatedUserInfo = {
      ...user.user_info,
      status: 'online',
      last_active: Date.now(),
    };
    await ctx.db.patch(user._id, { user_info: updatedUserInfo });

    return {
      success: true,
      user: {
        userId: user._id,
        email: user.email,
        walletAddress: user.wallet_address,
        blockchain: user.blockchain,
        accountType: user.account_type,
        userInfo: user.user_info,
      },
      message: "Login successful",
    };
  },
});

export const userSignup = mutation({
  args: {
    email: v.optional(v.string()),
    password: v.optional(v.string()),
    address: v.optional(v.string()),
    blockchain: v.optional(v.string()),
    username: v.optional(v.string()),
    userType: v.optional(v.string()),
    user_info: v.optional(v.any()),
    ip_address: v.optional(v.string()),
    user_agent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const {
      email = '',
      password = '',
      address = '',
      blockchain = '',
      username = '',
      userType = 'user',
      user_info = {},
      ip_address = '',
      user_agent = '',
    } = args;
    if (!address && (!email || !password)) {
      await ctx.db.insert("LoginLogs", {
        user_id: null,
        ip_address,
        user_agent,
        id: Date.now(),
        login_time: new Date().toISOString(),
        status: "failed",
        reason: "Email and password are required for email signup",
      });
      throw new Error("Email and password are required for email signup");
    }
    if (address && !blockchain) {
      await ctx.db.insert("LoginLogs", {
        user_id: null,
        ip_address,
        user_agent,
        id: Date.now(),
        login_time: new Date().toISOString(),
        status: "failed",
        reason: "Blockchain is required for Web3 signup",
      });
      throw new Error("Blockchain is required for Web3 signup");
    }
    const existingUser = await ctx.db
      .query("Accounts")
      .filter((q) => {
        const conditions = [];
        if (email) {
          conditions.push(q.eq(q.field("email"), email));
        }
        if (address) {
          conditions.push(q.eq(q.field("wallet_address"), address));
        }
        return q.or(...conditions);
      })
      .first();
    if (existingUser) {
      await ctx.db.insert("LoginLogs", {
        user_id: existingUser.user_id,
        ip_address,
        user_agent,
        id: Date.now(),
        login_time: new Date().toISOString(),
        status: "failed",
        reason: "User already exists",
      });
      return { success: false, message: "User already exists" };
    }
    if (username) {
      const userWithUsername = await ctx.db
        .query("Accounts")
        .filter((q) => q.eq(q.field("user_info.account.username"), username))
        .first();
      if (userWithUsername) {
        await ctx.db.insert("LoginLogs", {
          user_id: null,
          ip_address,
          user_agent,
          id: Date.now(),
          login_time: new Date().toISOString(),
          status: "failed",
          reason: "Username already taken",
        });
        return { success: false, message: "Username already taken" };
      }
    }
    function randomUsername(base: string) {
      const randomNumber = Math.floor(100000 + Math.random() * 900000);
      return `${base}${randomNumber}`;
    }
    let finalUsername = username;
    if (!finalUsername) {
      let baseUsername = email ? email.split("@")[0] : address ? `SC${address.slice(2, 6)}${address.slice(-4)}` : "SC";
      let unique = false;
      while (!unique) {
        finalUsername = randomUsername(baseUsername);
        const exists = await ctx.db
          .query("Accounts")
          .filter((q) => q.eq(q.field("user_info.account.username"), finalUsername))
          .first();
        if (!exists) unique = true;
      }
    }
    const hashedPassword = password ? require('bcryptjs').hashSync(password, 10) : '';
    function uuidv4() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
    const user_id = uuidv4();
    const fullUserInfo = {
      ...user_info,
      account: {
        ...(user_info.account || {}),
        username: finalUsername,
        email,
        accountType: userType,
      },
      nonce: '',
      status: 'online',
      last_active: Date.now(),
    };
    await ctx.db.insert("Accounts", {
      user_id,
      email,
      password: hashedPassword,
      wallet_address: address,
      blockchain,
      account_type: userType,
      registration_date: new Date().toISOString(),
      user_info: fullUserInfo,
    });
    await ctx.db.insert("LoginLogs", {
      user_id,
      ip_address,
      user_agent,
      id: Date.now(),
      login_time: new Date().toISOString(),
      status: "success",
      reason: "User signed up",
    });
    return {
      success: true,
      user: {
        user_id,
        email,
        wallet_address: address,
        blockchain,
        user_info: {
          account: {
            username: finalUsername,
            displayName: fullUserInfo.account.displayName || '',
            accountType: userType,
          },
          profilePhoto: fullUserInfo.profilePhoto || '',
        },
      },
      blockchain,
      message: "Account created successfully",
    };
  },
});

export const updateUserStatus = mutation({
  args: {
    user_id: v.string(),
    status: v.string(),
    last_active: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const account = await ctx.db
      .query("Accounts")
      .filter(q => q.eq(q.field("user_id"), args.user_id))
      .first();
    if (!account) throw new Error("Account not found");

    // Only patch the minimal fields, not the whole user_info object!
    await ctx.db.patch(account._id, {
      user_info: {
        ...account.user_info,
        status: args.status,
        last_active: args.last_active ?? account.user_info.last_active,
      }
    });

    return { success: true };
  },
});

export const uploadBannerVideo = mutation({
  args: {
    userId: v.string(),
    file: v.string(), // base64 encoded
    fileType: v.string(),
    fileName: v.string(),
  },
  handler: async (ctx, args): Promise<{ success: boolean; url?: string; error?: string }> => {
    try {
      // For now, return a placeholder response
      // The actual S3 upload would need to be handled differently
      const placeholderUrl = `https://placeholder.com/${args.fileName}`;

      // Update user account
      const account = await ctx.db
        .query("Accounts")
        .filter((q) => q.eq(q.field("user_id"), args.userId))
        .first();
      if (!account) throw new Error("Account not found");

      const updatedUserInfo = {
        ...account.user_info,
        coverBanner: placeholderUrl,
        coverBannerType: "video",
      };
      await ctx.db.patch(account._id, { user_info: updatedUserInfo });
      return { success: true, url: placeholderUrl };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : "Upload failed" };
    }
  },
});

export const updateUserImage = mutation({
  args: {
    userId: v.string(),
    imageUrl: v.string(),
    imageType: v.union(v.literal('profile'), v.literal('cover')),
  },
  handler: async (ctx, args) => {
    const { userId, imageUrl, imageType } = args;
    const account = await ctx.db
      .query('Accounts')
      .filter((q) => q.eq(q.field('user_id'), userId))
      .first();
    if (!account) throw new Error('Account not found');
    const updatedUserInfo = {
      ...account.user_info,
      [imageType === 'profile' ? 'profilePhoto' : 'coverBanner']: imageUrl,
    };
    await ctx.db.patch(account._id, { user_info: updatedUserInfo });
    return { success: true };
  },
});

export const getConnectedAccounts = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const account = await ctx.db
      .query('Accounts')
      .filter((q) => q.eq(q.field('user_id'), args.userId))
      .first();
    if (!account) return [];
    let connectedSocials = [];
    const userInfo = account.user_info || {};
    if (userInfo.connected_socials) {
      if (typeof userInfo.connected_socials === 'string') {
        try {
          connectedSocials = JSON.parse(userInfo.connected_socials);
        } catch {
          connectedSocials = [];
        }
      } else if (Array.isArray(userInfo.connected_socials)) {
        connectedSocials = userInfo.connected_socials;
      }
    }
    return connectedSocials;
  },
});

export const updateUserBanner = mutation({
  args: {
    userId: v.string(),
    data: v.any(),
  },
  handler: async (ctx, args) => {
    const { userId, data } = args;
    const account = await ctx.db
      .query('Accounts')
      .filter((q) => q.eq(q.field('user_id'), userId))
      .first();
    if (!account) throw new Error('Account not found');
    const updatedUserInfo = {
      ...account.user_info,
      ...data,
    };
    await ctx.db.patch(account._id, { user_info: updatedUserInfo });
    return { success: true, data: updatedUserInfo };
  },
});

export const getFollowCount = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const followingCount = await ctx.db
      .query('Follows')
      .filter((q) => q.eq(q.field('followed_id'), args.userId))
      .collect();
    const followersCount = await ctx.db
      .query('Follows')
      .filter((q) => q.eq(q.field('follower_id'), args.userId))
      .collect();
    return {
      following: followingCount.length,
      followers: followersCount.length,
    };
  },
});

export const getSubscriberCount = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const subscriberCount = await ctx.db
      .query('Subscriptions')
      .filter((q) => q.eq(q.field('creator_id'), args.userId))
      .collect();
    return {
      subscribers: subscriberCount.length,
    };
  },
});

export const getUserDetails = query({
  args: { user: v.string() },
  handler: async (ctx, args) => {
    const { user } = args;
    let account = null;
    // If user is a wallet address (starts with 0x or similar)
    if (/^(0x)?[0-9a-fA-F]{40,}$/.test(user)) {
      account = await ctx.db
        .query('Accounts')
        .filter((q) => q.eq(q.field('wallet_address'), user))
        .first();
    } else {
      // Try by user_id or username
      account = await ctx.db
        .query('Accounts')
        .filter((q) =>
          q.or(
            q.eq(q.field('user_id'), user),
            q.eq(q.field('user_info.account.username'), user)
          )
        )
        .first();
    }
    if (!account) return { success: false, message: 'User not found' };
    let counts = {};
    try {
      if (account.account_type === 'user') {
        counts = await ctx.runQuery(api.accounts.getFollowCount, { userId: account.user_id });
      } else {
        counts = await ctx.runQuery(api.accounts.getSubscriberCount, { userId: account.user_id });
      }
    } catch (error) {
      console.error('Error fetching counts:', error);
      return {
        success: false,
        message: 'Failed to fetch user counts',
      };
    }
    return {
      success: true,
      data: {
        user_id: account.user_id,
        email: account.email,
        registration_date: account.registration_date,
        wallet_address: account.wallet_address,
        user_info: account.user_info,
        blockchain: account.blockchain,
        account_type: account.account_type,
        model_id: account.model_id,
        ...counts,
      },
      message: 'User found',
    };
  },
});

export const verifySignature = mutation({
  args: {
    userAddress: v.string(),
    signature: v.optional(v.any()),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, { userAddress, signature, reason }): Promise<any> => {
    if (!userAddress) {
      throw new Error("User address is required");
    }

    // Look up user by stake_key
    const accounts = await ctx.db
      .query("Accounts")
      .withIndex("by_stake_key", (q) =>
        q.eq("user_info.stake_key", userAddress)
      )
      .collect();

    const user = accounts[0];

    // For now, return a basic response structure
    // The actual signature verification would need to be handled differently
    // since we can't use @meshsdk/core directly in mutations

    if (!user) {
      // New user case
      if (!signature) {
        throw new Error("Signature is required for new user");
      }

      return {
        success: true,
        message: 'User not found, signature verification would be handled externally',
        isNewUser: true
      };
    }

    // Existing user case
    if (!signature) {
      throw new Error("Signature is required for login");
    }

    // Update user's verification status
    const updatedUserInfo = {
      ...user.user_info,
      verified: true,
      last_verified: new Date().toISOString(),
    };

    await ctx.db.patch(user._id, { user_info: updatedUserInfo });

    return {
      success: true,
      message: 'Signature verification would be handled externally',
      isNewUser: false
    };
  },
});

export const linkAuthAccount = mutation({
  args: {
    authAccountId: v.id("authAccounts"),
    userId: v.id("Accounts"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.authAccountId, {
      userId: args.userId,
    });
    return { success: true };
  },
});

export const createAuthUser = mutation({
  args: {
    accountId: v.id("Accounts"),
    email: v.optional(v.string()),
    accountType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log('createAuthUser - args:', args);

    // Check if a user already exists for this account using the index
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_account_id", (q) => q.eq("accountId", args.accountId))
      .first();

    console.log('createAuthUser - existingUser:', existingUser);

    let userId;
    if (existingUser) {
      userId = existingUser._id;
      console.log('createAuthUser - using existing userId:', userId);
    } else {
      // Create a new user in the users table for Convex Auth
      userId = await ctx.db.insert("users", {
        accountId: args.accountId,
        email: args.email || null,
        accountType: args.accountType || "standard",
      });
      console.log('createAuthUser - created new userId:', userId);
    }

    // Patch the Accounts table to store the Convex Auth user _id
    console.log('createAuthUser - patching account with auth_user_id:', userId);

    // Get the current account to preserve existing data
    const currentAccount = await ctx.db.get(args.accountId);
    if (!currentAccount) {
      throw new Error('Account not found');
    }

    // Update only the auth_user_id field
    await ctx.db.patch(args.accountId, {
      auth_user_id: userId
    });

    // Verify the patch worked
    const updatedAccount = await ctx.db.get(args.accountId);
    console.log('createAuthUser - updated account auth_user_id:', updatedAccount?.auth_user_id);
    console.log('createAuthUser - updated account auth_user_id type:', typeof updatedAccount?.auth_user_id);

    return { userId };
  },
});

export const upsertAuthAccount = mutation({
  args: {
    provider: v.string(),
    providerAccountId: v.string()
  },
  handler: async (ctx, { provider, providerAccountId }) => {
    const existing = await ctx.db
      .query("authAccounts")
      .withIndex("by_provider_and_providerAccountId", (q: any) =>
        q.eq("provider", provider).eq("providerAccountId", providerAccountId)
      )
      .first();

    if (existing) return existing._id;

    const newAccountId = await ctx.db.insert("authAccounts", {
      provider,
      providerAccountId,
      userId: providerAccountId
    });

    return newAccountId;
  },
});

export const fixAccountAuthLinks = mutation({
  args: {},
  handler: async (ctx, args) => {
    console.log('fixAccountAuthLinks - Starting migration...');

    // Get all accounts without auth_user_id
    const accountsWithoutAuthId = await ctx.db
      .query("Accounts")
      .filter((q) => q.eq(q.field("auth_user_id"), undefined))
      .collect();

    console.log('fixAccountAuthLinks - Found accounts without auth_user_id:', accountsWithoutAuthId.length);

    let fixed = 0;
    let errors = 0;

    for (const account of accountsWithoutAuthId) {
      try {
        // Check if there's already a user record for this account
        const existingUser = await ctx.db
          .query("users")
          .withIndex("by_account_id", (q) => q.eq("accountId", account._id))
          .first();

        let userId;
        if (existingUser) {
          userId = existingUser._id;
          console.log(`fixAccountAuthLinks - Found existing user for account ${account.user_id}:`, userId);
        } else {
          // Create a new user record
          userId = await ctx.db.insert("users", {
            accountId: account._id,
            email: account.email || null,
            accountType: account.account_type || "standard",
          });
          console.log(`fixAccountAuthLinks - Created new user for account ${account.user_id}:`, userId);
        }

        // Update the account with the auth_user_id
        await ctx.db.patch(account._id, {
          auth_user_id: userId
        });

        fixed++;
        console.log(`fixAccountAuthLinks - Fixed account ${account.user_id} -> ${userId}`);

      } catch (error) {
        errors++;
        console.error(`fixAccountAuthLinks - Error fixing account ${account.user_id}:`, error);
      }
    }

    console.log(`fixAccountAuthLinks - Migration complete. Fixed: ${fixed}, Errors: ${errors}`);

    return {
      success: true,
      totalAccounts: accountsWithoutAuthId.length,
      fixed,
      errors,
      message: `Migration complete. Fixed ${fixed} accounts, ${errors} errors.`
    };
  },
});

export const getAccountByUserId = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const account = await ctx.db
      .query('Accounts')
      .filter((q) => q.eq(q.field('user_id'), args.userId))
      .first();
    return account || null;
  },
});

export const populateModelIdColumn = mutation({
  args: {},
  handler: async (ctx) => {
    const accounts = await ctx.db.query('Accounts').collect();
    let updated = 0;
    for (const account of accounts) {
      if (typeof account.model_id === 'undefined') {
        await ctx.db.patch(account._id, { model_id: generateRandomId(15) });
        updated++;
      }
    }
    return { updated, total: accounts.length };
  },
});