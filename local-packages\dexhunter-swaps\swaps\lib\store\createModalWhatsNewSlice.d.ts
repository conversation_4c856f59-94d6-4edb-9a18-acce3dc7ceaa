export interface ModalWhatsNewSlice {
    isOpenModalWhatsNew: boolean;
    setIsOpenModalWhatsNew: (isOpenModalWhatsNew: boolean) => void;
    toggleOpenNewModal: () => void;
}
declare const createModalWhatsNewSlice: (set: any) => {
    isOpenModalWhatsNew: boolean;
    setIsOpenModalWhatsNew: (isOpenModalWhatsNew: boolean) => void;
    toggleOpenNewModal: () => void;
};
export default createModalWhatsNewSlice;
