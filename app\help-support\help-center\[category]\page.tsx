import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ChevronLeft } from 'lucide-react';
import { notFound } from 'next/navigation';

// Define help categories and articles (same as in the main help center page)
const helpCategories = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    icon: '🚀',
    description: 'Learn the basics of using Sugar Club',
    articles: [
      { 
        id: 'create-account', 
        title: 'How to create an account',
        excerpt: 'Learn how to sign up and create your Sugar Club account in just a few steps.'
      },
      { 
        id: 'profile-setup', 
        title: 'Setting up your profile',
        excerpt: 'Customize your profile with photos, bio, and other information to attract followers.'
      },
      { 
        id: 'navigation', 
        title: 'Navigating the platform',
        excerpt: 'Get familiar with the Sugar Club interface and learn how to find what you need.'
      },
    ],
  },
  // ... other categories (same as in the main help center page)
];

export async function generateMetadata({ params }: { params: { category: string } }): Promise<Metadata> {
  const category = helpCategories.find(cat => cat.id === params.category);
  
  if (!category) {
    return {
      title: 'Category Not Found | Help Center',
      description: 'The requested help category could not be found.',
    };
  }
  
  return {
    title: `${category.title} | Help Center`,
    description: category.description,
  };
}

export default function CategoryPage({ params }: { params: { category: string } }) {
  const category = helpCategories.find(cat => cat.id === params.category);
  
  if (!category) {
    notFound();
  }
  
  return (
    <div className="min-h-screen bg-white dark:bg-[#18181b] py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link 
            href="/help-support/help-center"
            className="flex items-center text-turquoise hover:underline"
          >
            <ChevronLeft size={16} className="mr-1" />
            Back to Help Center
          </Link>
        </div>
        
        {/* Header */}
        <div className="mb-12">
          <div className="flex items-center mb-4">
            <span className="text-3xl mr-4">{category.icon}</span>
            <h1 className="text-3xl font-bold text-[#18181b] dark:text-white">
              {category.title}
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300 text-lg">
            {category.description}
          </p>
        </div>
        
        {/* Articles list */}
        <div className="space-y-6">
          {category.articles.map((article) => (
            <Link 
              key={article.id}
              href={`/help-support/help-center/${category.id}/${article.id}`}
              className="block p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-turquoise dark:hover:border-turquoise transition-colors"
            >
              <h2 className="text-xl font-semibold text-[#18181b] dark:text-white mb-2">
                {article.title}
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {article.excerpt}
              </p>
              <span className="text-turquoise">Read article →</span>
            </Link>
          ))}
        </div>
        
        {/* Related categories */}
        <div className="mt-16">
          <h2 className="text-2xl font-semibold text-[#18181b] dark:text-white mb-6">
            Other Categories
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {helpCategories
              .filter(cat => cat.id !== category.id)
              .map((cat) => (
                <Link 
                  key={cat.id}
                  href={`/help-support/help-center/${cat.id}`}
                  className="p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-turquoise dark:hover:border-turquoise transition-colors"
                >
                  <div className="flex items-center">
                    <span className="text-xl mr-2">{cat.icon}</span>
                    <h3 className="font-medium text-[#18181b] dark:text-white">
                      {cat.title}
                    </h3>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}