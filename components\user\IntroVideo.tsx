'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Clapperboard, Play } from 'lucide-react';
import { motion } from 'framer-motion';
import { useExpandable } from '@/hooks/useExpandable';
import VideoLightbox from '@/components/ui/video-lightbox';

interface VideoItem {
  id: string;
  thumbnailUrl: string;
  videoUrl: string;
  title: string;
  channel: string;
  views: string;
  timeAgo: string;
  duration: string;
  isVerified?: boolean;
  isNew?: boolean;
}

interface IntroVideoProps {
  video?: VideoItem;
  onVideoClick?: (videoId: string) => void;
}

const IntroVideo: React.FC<IntroVideoProps> = ({
  video,
  onVideoClick
}) => {
  const { isExpanded, toggleExpand, animatedHeight } = useExpandable(true);
  const contentRef = useRef<HTMLDivElement>(null);
  const [lightboxOpen, setLightboxOpen] = useState(false);

  const defaultVideo: any = {
    id: '1',
    thumbnailUrl: 'https://public-cdn2.loyalfans.com/images/XDpy6wiQMC7We0kAoZn8mYZqv02LpOM7XyFkyXLkYCY/timeline/0_62438300_1653107494_20220521043134.jpg?width=1200&height=630&quality=80&mode=crop&format=webp',
    videoUrl: 'https://cdn2.loyalfans.com/videos/short/n9QbmLJ5MgfEvZxbgx-2Bu5lzkEIfQ-2BjrMGzwArRCcrcw/t/0_57036900_1747072108_20250512174828.mp4?Expires=1747704953&Signature=NuCHyVVr-QPI~lI4zHMoiqFlWur3KAt~JbgDKtvBBeAKsWrJUfWMOrTdoTDXK0svXg0uXiJAe3PwdvTRmWR6YIrhotMNrPNZ-3RHiAyGO4t5X8AB9mdKUuaYG2vepbiH7UYJWjJoIalWXpHw24uUAd-u8jCv4KqSov~PMHtStRFxWoKPJJIuddu0jh0Cn9W7aWAVdM~Z-5cy5tg7b-pQnanxx2qFn7-rGLyPq04PNJIHc690bG338xw0gfTlsdjfgZOfWjDqL2L7~i14EJHI88VEF65HUk6oLW2sn0lDEjVk1mbwoPYMwAsB8l52ZxCwGAptt4j3~anMjJdXI4Em6g__&Key-Pair-Id=K2JGZ5P2K94MB8',
  };

  const displayVideo = video || defaultVideo;

  const handleVideoClick = () => {
    setLightboxOpen(true);
    if (onVideoClick) {
      onVideoClick(displayVideo.id);
    }
  };

  useEffect(() => {
    if (contentRef.current && isExpanded) {
      animatedHeight.set(contentRef.current.scrollHeight);
    } else {
      animatedHeight.set(0);
    }
  }, [isExpanded, animatedHeight]);

  return (
    <>
      <div className="mt-[27px] mb-6 w-full max-w-full overflow-hidden">
        <button
          onClick={toggleExpand}
          className="flex items-center justify-between w-full h-10 px-3 rounded-lg bg-transparent transition-colors text-gorilla-gray dark:text-white border border-solid border-gray-400 dark:border-white"
        >
          <div className="flex items-center text-sm">
            <Clapperboard strokeWidth={2.75} className="h-4 w-4 mr-2" />
            <span>Introduction Video</span>
          </div>
          <div>
            <svg
              width={16}
              height={16}
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className={`fill-current transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
            >
              <path d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4063 5.65625 17.6875 5.9375C17.9687 6.21875 17.9687 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1563 10.1875 14.25 10 14.25Z" />
            </svg>
          </div>
        </button>

        <motion.div
          className="overflow-hidden w-full"
          style={{ height: animatedHeight }}
        >
          <div
            ref={contentRef}
            className="mt-4 pb-4 w-full"
          >
            {/* Intro Video */}
            <div className="space-y-1.5 w-full">
              <div
                className="flex cursor-pointer w-full relative"
                onClick={handleVideoClick}
              >
                <div className="relative w-full h-[220px] rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={displayVideo.thumbnailUrl}
                    alt="Introduction Video"
                    loading="lazy"
                    className="object-cover w-full h-full hover:scale-105 transition-transform duration-300 ease-in-out"
                  />
                  <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 py-0.5 rounded">
                    {displayVideo.duration}
                  </div>
                </div>

                {/* Play button overlay */}
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center z-0 rounded-lg">
                  <div className="relative">
                    <div className="absolute inset-0 bg-turquoise rounded-full opacity-30 animate-ping-slow"></div>
                    <button
                      className="relative z-10 w-12 h-12 bg-turquoise rounded-full flex items-center justify-center hover:bg-cyan-600 transition-colors"
                    >
                      <Play className="h-6 w-6 text-white fill-white ml-1" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Video Lightbox */}
      <VideoLightbox
        open={lightboxOpen}
        onOpenChange={setLightboxOpen}
        videoSrc={displayVideo.videoUrl}
        posterSrc={displayVideo.thumbnailUrl}
        title={displayVideo.title}
        thumbnail={displayVideo.thumbnailUrl}
      />
    </>
  );
};
export default IntroVideo;