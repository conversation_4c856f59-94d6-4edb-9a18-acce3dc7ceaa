import { jsxs as r, jsx as o } from "react/jsx-runtime";
import { ToastProvider as a, Toast as n, ToastTitle as c, ToastDescription as T, ToastClose as d, ToastViewport as l } from "./toast.js";
import { useToast as f } from "./use-toast.js";
import "react";
import "../../index-1c873780.js";
import "../../index-c8f2666b.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-c7156e07.js";
import "../../index-bf605d8a.js";
import "../../index-563d1ed8.js";
import "../../index-67500cd3.js";
import "../../index-5116e957.js";
import "../../index-f7426637.js";
import "../../index-1d6812f7.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../lib.js";
import "../../x-9e07c78a.js";
import "../../createLucideIcon-7a477fa6.js";
function B() {
  const { toasts: m } = f();
  return /* @__PURE__ */ r(a, { children: [
    m.map(function({ id: p, title: t, description: i, action: s, ...e }) {
      return /* @__PURE__ */ r(n, { ...e, children: [
        /* @__PURE__ */ r("div", { className: "grid gap-1", children: [
          t && /* @__PURE__ */ o(c, { children: t }),
          i && /* @__PURE__ */ o(T, { children: i })
        ] }),
        s,
        /* @__PURE__ */ o(d, {})
      ] }, p);
    }),
    /* @__PURE__ */ o(l, {})
  ] });
}
export {
  B as Toaster
};
