'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import {
  Cookie<PERSON>ategory,
  CookieConsentStatus,
  saveConsentPreferences,
  getCookie
} from '@/utils/cookieConsent';

interface CookiePreferencesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CookiePreferencesModal({ isOpen, onClose }: CookiePreferencesModalProps) {
  const [preferences, setPreferences] = useState<Record<CookieCategory, boolean>>({
    essential: true, // Essential cookies are always enabled
    analytics: false,
    marketing: false
  });

  useEffect(() => {
    if (isOpen && typeof window !== 'undefined') {
      // Load current preferences when modal opens
      setPreferences({
        essential: true, // Always true
        analytics: getCookie('analytics') === 'true',
        marketing: getCookie('marketing') === 'true'
      });
    }
  }, [isOpen]);

  const handleToggle = (category: CookieCategory) => {
    if (category === 'essential') return; // Essential cookies can't be toggled

    setPreferences(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  const handleSave = () => {
    saveConsentPreferences('customized', preferences);
    onClose();
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      essential: true,
      analytics: true,
      marketing: true
    };
    setPreferences(allAccepted);
    saveConsentPreferences('accepted', allAccepted);
    onClose();
  };

  const handleRejectAll = () => {
    const allRejected = {
      essential: true, // Essential always true
      analytics: false,
      marketing: false
    };
    setPreferences(allRejected);
    saveConsentPreferences('declined', allRejected);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-[500px] w-full mx-4">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold">Cookie Preferences</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4 space-y-6">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Manage your cookie preferences. Essential cookies are necessary for the website to function and cannot be disabled.
          </p>

          {/* Essential Cookies */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-medium">Essential Cookies</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  These cookies are necessary for the website to function and cannot be disabled.
                </p>
              </div>
              <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-400">
                <span className="translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition" />
              </div>
            </div>

            {/* Analytics Cookies */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-medium">Analytics Cookies</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  These cookies help us understand how visitors interact with our website.
                </p>
              </div>
              <button
                onClick={() => handleToggle('analytics')}
                className={`relative inline-flex h-6 w-11 items-center rounded-full ${preferences.analytics ? 'bg-turquoise' : 'bg-gray-300 dark:bg-gray-600'}`}
              >
                <span
                  className={`${preferences.analytics ? 'translate-x-6' : 'translate-x-1'} inline-block h-4 w-4 transform rounded-full bg-white transition`}
                />
              </button>
            </div>

            {/* Marketing Cookies */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-medium">Marketing Cookies</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  These cookies are used to track visitors across websites to display relevant advertisements.
                </p>
              </div>
              <button
                onClick={() => handleToggle('marketing')}
                className={`relative inline-flex h-6 w-11 items-center rounded-full ${preferences.marketing ? 'bg-turquoise' : 'bg-gray-300 dark:bg-gray-600'}`}
              >
                <span
                  className={`${preferences.marketing ? 'translate-x-6' : 'translate-x-1'} inline-block h-4 w-4 transform rounded-full bg-white transition`}
                />
              </button>
            </div>
          </div>

          <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-base font-medium mb-2">More Information</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              For more details about how we use cookies and your personal data, please read our{' '}
              <a href="/privacy" className="text-turquoise hover:underline">Privacy Policy</a> and{' '}
              <a href="/cookies" className="text-turquoise hover:underline">Cookie Policy</a>.
            </p>
          </div>
        </div>

        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row gap-2 sm:justify-between">
          <div className="flex gap-2">
            <button
              onClick={handleRejectAll}
              className="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              Reject All
            </button>
            <button
              onClick={handleAcceptAll}
              className="px-4 py-2 text-sm bg-turquoise text-white rounded-md hover:bg-turquoise/90"
            >
              Accept All
            </button>
          </div>
          <button
            onClick={handleSave}
            className="px-4 py-2 text-sm bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-800 rounded-md hover:bg-gray-700 dark:hover:bg-gray-300"
          >
            Save Preferences
          </button>
        </div>
      </div>
    </div>
  );
}

