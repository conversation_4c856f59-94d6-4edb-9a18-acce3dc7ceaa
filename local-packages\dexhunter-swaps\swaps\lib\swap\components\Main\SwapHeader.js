import { jsxs as o, jsx as e } from "react/jsx-runtime";
import { useState as u } from "react";
import { cn as t } from "../../../lib/utils.js";
import { u as f } from "../../../QueryClientProvider-6bcd4331.js";
import S from "../Settings/Slippage.js";
import g from "../../../components/ui/tooltipDialog.js";
import T from "../../../store/useStore.js";
import w from "../../../assets/svg/IconRefresh.js";
import "../../../extend-tailwind-merge-e63b2b56.js";
import "../../../assets/svg/IconSetting.js";
import "../../../hooks/useScreen.js";
import "../../../components/ui/dialog.js";
import "../../../index-840f2930.js";
import "../../../index-1c873780.js";
import "../../../index-c7156e07.js";
import "../../../index-563d1ed8.js";
import "../../../index-4914f99c.js";
import "../../../index-67500cd3.js";
import "../../../index-c8f2666b.js";
import "../../../_commonjsHelpers-10dfc225.js";
import "../../../index-27cadef5.js";
import "../../../index-5116e957.js";
import "../../../lib.js";
import "../../../components/ui/tooltip.js";
import "../../../index-0ce202b9.js";
import "../../../index-bcfeaad9.js";
import "../../../index-f7426637.js";
import "../../../store/createTokenSearchSlice.js";
import "../../../immer-548168ec.js";
import "../../../store/createWalletSlice.js";
import "../../../store/createSwapSettingsSlice.js";
import "../../../store/createGlobalSettingsSlice.js";
import "../../../store/createUserOrdersSlice.js";
import "../../../store/createSwapSlice.js";
import "../../../store/createChartSlice.js";
import "../../../store/createBasketSlice.js";
import "../tokens.js";
import "../../../store/createModalWhatsNewSlice.js";
import "../../../store/createSwapParamsSlice.js";
const pe = () => {
  const { orderType: s, setOrderType: i, setLimitMultiples: p, orderTypes: r } = T(
    (x) => x.swapSlice
  ), m = f(), [a, d] = u(!1), h = async () => {
    d(!0), await Promise.all([
      m.refetchQueries({ queryKey: ["swapDetails"] }),
      m.refetchQueries({ queryKey: ["swapPrice"] })
    ]), setTimeout(() => {
      d(!1);
    }, 800);
  }, n = () => {
    i("SWAP"), p(0);
  }, l = () => {
    i("LIMIT"), p(0);
  }, c = () => {
    i("DCA");
  };
  return /* @__PURE__ */ o("div", { className: "dhs-flex dhs-justify-between dhs-gap-2 dhs-items-center dhs-py-2 dhs-px-4 @sm/appRoot:dhs-pb-2 @sm/appRoot:dhs-pt-2 @sm/appRoot:dhs-px-8 dhs-pr-0 @sm/appRoot:dhs-pr-0", children: [
    /* @__PURE__ */ o("div", { className: "dhs-flex dhs-gap-2 dhs-text-subText", children: [
      /* @__PURE__ */ e(
        "span",
        {
          className: t(
            "dhs-text-lg sm:dhs-text-base/[19px] dhs-font-proximaMedium sm:dhs-font-proximaBold dhs-cursor-pointer",
            s === "SWAP" && "dhs-text-mainText",
            r.includes("SWAP") ? void 0 : "dhs-hidden"
          ),
          onClick: n,
          children: "Swap"
        }
      ),
      /* @__PURE__ */ e(
        "span",
        {
          className: t(
            "dhs-text-lg sm:dhs-text-base/[19px] dhs-font-proximaMedium sm:dhs-font-proximaBold dhs-cursor-pointer",
            (s === "LIMIT" || s === "STOP_LOSS") && "dhs-text-mainText",
            r.includes("LIMIT") ? void 0 : "dhs-hidden"
          ),
          onClick: l,
          children: "Limit"
        }
      ),
      /* @__PURE__ */ e(
        "span",
        {
          className: t(
            "dhs-text-lg sm:dhs-text-base/[19px] dhs-font-proximaMedium sm:dhs-font-proximaBold dhs-cursor-pointer",
            s === "DCA" && "dhs-text-mainText",
            r.includes("DCA") ? void 0 : "dhs-hidden"
          ),
          onClick: c,
          children: "DCA"
        }
      )
    ] }),
    /* @__PURE__ */ o("div", { className: "dhs-flex dhs-gap-2 dhs-items-center", children: [
      /* @__PURE__ */ e("div", { className: "dhs-spinner-grow", role: "status" }),
      /* @__PURE__ */ e(
        g,
        {
          activeMobile: !1,
          trigger: /* @__PURE__ */ e(
            w,
            {
              className: t(
                "dhs-text-mainText dhs-cursor-pointer hover:dhs-text-gray-103 dhs-w-[18px] dhs-min-w-[18px] sm:dhs-w-[16px] sm:dhs-min-w-[16px]",
                a && "dhs-animate-reverse-spin"
              ),
              onClick: h
            }
          ),
          content: "Refresh details",
          contentClass: "dhs-text-mainText"
        }
      ),
      /* @__PURE__ */ e("div", { className: "dhs-flex dhs-gap-[10px] dhs-items-center", children: /* @__PURE__ */ e(S, {}) })
    ] })
  ] });
};
export {
  pe as default
};
