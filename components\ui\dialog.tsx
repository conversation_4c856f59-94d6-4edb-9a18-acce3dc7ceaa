/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dialog as ChakraDialog, Portal } from "@chakra-ui/react";
import { CloseButton } from "./close-button";
import * as React from "react";
import { cn } from "@/lib/utils";

interface DialogContentProps extends ChakraDialog.ContentProps {
  portalled?: boolean;
  portalRef?: React.RefObject<HTMLElement>;
  backdrop?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const DialogContent = React.forwardRef<
  HTMLDivElement,
  DialogContentProps
>(function DialogContent(props, ref) {
  const {
    children,
    portalled = true,
    portalRef,
    backdrop = true,
    className,
    ...rest
  } = props

  return (
    <Portal disabled={!portalled} container={portalRef}>
      {backdrop && (
        <ChakraDialog.Backdrop
          className="blur-sm z-[999]" // Set a lower z-index for the backdrop
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
          }}
        />
      )}
      <ChakraDialog.Positioner>
        <ChakraDialog.Content
          ref={ref}
          className={cn(
            "z-[1000]", // Set a lower z-index for the dialog content
            "bg-white/60 dark:bg-[#121212]/60 backdrop-blur",
            "border-2 border-solid border-white/20",
            "p-2",
            "rounded-lg",
            className
          )}
          {...rest}
          asChild={false}
        >
          {children}
        </ChakraDialog.Content>
      </ChakraDialog.Positioner>
    </Portal>
  )
})

export const DialogCloseTrigger = React.forwardRef<
  HTMLButtonElement,
  ChakraDialog.CloseTriggerProps
>(function DialogCloseTrigger(props, ref) {
  return (
    <ChakraDialog.CloseTrigger
      position="absolute"
      top="2"
      insetEnd="2"
      {...props}
      asChild
    >
      <CloseButton size="sm" ref={ref}>
        {props.children}
      </CloseButton>
    </ChakraDialog.CloseTrigger>
  );
});

export const DialogRoot = (props: any) => {
  return (
    <ChakraDialog.Root {...props} closeOnInteractOutside={false}>
      {props.children}
    </ChakraDialog.Root>
  );
};

export const DialogFooter = ChakraDialog.Footer;
export const DialogHeader = ChakraDialog.Header;
export const DialogBody = ChakraDialog.Body;
export const DialogBackdrop = ChakraDialog.Backdrop;
export const DialogTitle = ChakraDialog.Title;
export const DialogDescription = ChakraDialog.Description;
export const DialogTrigger = ChakraDialog.Trigger;
export const DialogActionTrigger = ChakraDialog.ActionTrigger;