export type SelectedWallet = 'nami' | 'eternl' | 'flint' | 'gerowallet' | 'typhoncip30' | 'nufi' | 'lace' | 'vespr' | 'begin' | 'yoroi' | 'exodus' | 'tokeo';
export interface CardanoApi {
    isEnabled: () => Promise<boolean>;
    getBalance: () => Promise<string>;
    getUtxos: (_amount?: string, _paginate?: {
        page: number;
        limit: number;
    }) => Promise<string[]>;
    getCollateral: () => Promise<string[] | null>;
    getUsedAddresses: () => Promise<string[]>;
    getUnusedAddresses: () => Promise<string[]>;
    getChangeAddress: () => Promise<string>;
    getRewardAddress: () => Promise<string>;
    getRewardAddresses: () => Promise<string[]>;
    getNetworkId: () => Promise<number>;
    signData: (_address: string, _payload: string) => Promise<any>;
    signTx: (_tx: string, _partialSign?: boolean) => Promise<string>;
    signTxs?: (txs: string[]) => Promise<string[]>;
    submitTx: (_cbor: string) => Promise<string>;
    experimental: any;
}
