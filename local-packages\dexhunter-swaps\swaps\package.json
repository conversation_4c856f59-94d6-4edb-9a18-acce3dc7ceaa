{"name": "@dexhunterio/swaps", "version": "0.0.180", "type": "module", "main": "./lib/main.js", "module": "./lib/main.js", "types": "./lib/main.d.ts", "files": ["lib"], "scripts": {"dev": "vite", "build": "tsc && vite build", "build-lib": "tsc && vite build --config vite.config.lib.ts && vite build --config vite.config.umd.ts", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "peerDependencies": {"@formkit/auto-animate": "^0.8.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-tooltip": "^1.0.6", "@tabler/icons-react": "^2.30.0", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-devtools": "^5.59.16", "@tanstack/react-table": "^8.9.3", "@tanstack/react-virtual": "^3.0.0-beta.54", "@vercel/analytics": "^1.0.2", "axios": "^1.4.0", "bech32": "^2.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "i": "^0.3.7", "immer": "^10.0.2", "lucide-react": "^0.263.1", "moment": "^2.29.4", "npm": "^9.8.1", "overlayscrollbars-react": "^0.5.1", "react": "^18.2.0", "react-confetti-explosion": "^2.1.2", "react-currency-input-field": "^3.6.11", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.4.1", "react-spinners": "^0.13.8", "react-toastify": "^9.1.3", "react-virtuoso": "^4.4.2", "sort-by": "^1.2.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "use-debounce": "^9.0.4", "zustand": "^4.4.0"}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@types/node": "20.4.7", "@types/react": "18.2.18", "@types/react-dom": "18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "8.46.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "glob": "^10.3.10", "postcss": "^8.4.31", "sass": "^1.64.2", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-dts": "^3.6.0", "vite-plugin-lib-inject-css": "^1.3.0", "vite-plugin-node-polyfills": "^0.15.0", "vite-tsconfig-paths": "^4.2.1"}, "dependencies": {"cbor-x": "^1.5.7"}}