import { p as c } from "../immer-548168ec.js";
const a = (o) => ({
  open: !1,
  searchInput: "",
  openModal: () => {
    o(
      c((e) => {
        e.tokenSearchSlice.searchInput = "", e.tokenSearchSlice.open = !0;
      })
    );
  },
  closeModal: (e) => {
    o(
      c((n) => {
        n.tokenSearchSlice.open = !1;
      })
    ), e && e();
  },
  toggleModal: () => {
    o(
      c((e) => {
        e.tokenSearchSlice.searchInput = "", e.tokenSearchSlice.open = !e.tokenSearchSlice.open;
      })
    );
  },
  setSearchInput: (e) => {
    o(
      c((n) => {
        n.tokenSearchSlice.searchInput = e;
      })
    );
  }
});
export {
  a as default
};
