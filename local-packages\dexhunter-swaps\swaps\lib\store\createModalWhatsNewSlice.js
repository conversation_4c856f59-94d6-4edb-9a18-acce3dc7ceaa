import { p as l } from "../immer-548168ec.js";
const t = (a) => ({
  isOpenModalWhatsNew: !1,
  setIsOpenModalWhatsNew: (e) => {
    a(
      l((s) => {
        s.modalWhatsNewSlice.isOpenModalWhatsNew = e;
      })
    );
  },
  toggleOpenNewModal: () => {
    a(
      l((e) => {
        e.modalWhatsNewSlice.isOpenModalWhatsNew = !e.modalWhatsNewSlice.isOpenModalWhatsNew;
      })
    );
  }
});
export {
  t as default
};
