// convex/functions/normalizeUsernames.ts
import { mutation } from "../_generated/server";

export const normalizeUsernames = mutation({
  handler: async ({ db }) => {
    // 1) Grab all Accounts
    const accounts = await db.query("Accounts").collect();
    let count = 0;

    for (const acct of accounts) {
      // 2) Drill into the nested username
      const info = acct.user_info;
      const nested = info?.account;
      if (nested && typeof nested.username === "string") {
        const lower = nested.username.toLowerCase();

        // 3) Build a new user_info object
        const newUserInfo = {
          ...info,
          account: {
            ...nested,
            username: lower
          }
        };

        // 4) Patch only the user_info field
        await db.patch(acct._id, {
          user_info: newUserInfo
        });
        count++;
      }
    }

    return { updated: count };
  }
});