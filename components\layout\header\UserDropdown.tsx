import React, { useEffect, useRef, useState } from 'react';
import { useAuthActions } from "@convex-dev/auth/react";
import { useUser } from "@/hooks/useUser";
import { Skeleton } from '@/components/ui/skeleton';
import { Globe } from 'lucide-react';
import { HiCurrencyDollar } from 'react-icons/hi2';
import { FaBookmark } from "react-icons/fa6";
import { GiPieChart } from 'react-icons/gi';
import { HiOutlineBuildingLibrary } from 'react-icons/hi2';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useSelector } from "react-redux";
import { useUserStatus } from '@/context/UserStatusContext';

interface UserDropdownProps {
  className?: string;
}

export default function UserDropdown({ className }: UserDropdownProps) {
  const pathname = usePathname();
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoading2, setIsLoading2] = useState<boolean>(false);
  const [isVerifiedBadgeLoading, setIsVerifiedBadgeLoading] = useState<boolean>(true);

  const { signOut } = useAuthActions();
  const { user, isLoading: userLoading } = useUser();
  const isAuthenticated = useSelector((state: any) => state.userInfo.isAuthenticated);
  const { updateUserStatus } = useUserStatus();

  const userInfo = user?.user_info || {};
  const accountInfo = userInfo.account || {};
  const username = accountInfo.username;
  const displayName = accountInfo.displayName;
  const profilePhoto = userInfo.profilePhoto;
  const is_verified = userInfo.is_verified;
  const subscribers = userInfo.subscribers;
  const accountType = user?.account_type;

  const trigger = useRef<HTMLButtonElement | null>(null);
  const dropdown = useRef<HTMLDivElement | null>(null);

  const userProfilePath = username ? `/user/${username}` : '';

  const isCurrentPath = (path: string) => {
    if (path === userProfilePath) {
      return pathname === path;
    }
    return pathname.startsWith(path);
  };

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }: MouseEvent) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target as Node) ||
        trigger.current?.contains(target as Node)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  }, [dropdownOpen]);

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }: KeyboardEvent) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  }, [dropdownOpen]);

  const handleLogout = async () => {
    if (user?.user_id) {
      try {
        await updateUserStatus(user.user_id, 'offline');
      } catch (e) {
        console.error("Failed to update user status on logout", e);
      }
    }
    await signOut();
    // Optionally: clear redux state or redirect
  };

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
    if (dropdownOpen) {
      setDropdownOpen(false);
    }
  };

  // Show loading state when user is being authenticated or data is loading
  if (userLoading || (isAuthenticated && !user)) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <Skeleton width="44px" borderRadius="50%" loading={true}>
          <div className="h-11 w-11 rounded-full bg-gray-200 dark:bg-zinc-700" />
        </Skeleton>
      </div>
    );
  }

  // If not authenticated, don't show anything
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <section className={cn("bg-gray-2 py-0 flex items-center justify-center", className)}>
      <div className="container">
        <div className="relative flex justify-center items-center gap-2">
          <button
            ref={trigger}
            aria-label="User menu"
            onClick={toggleDropdown}
            className="relative inline-flex items-center justify-center"
          >
            <Skeleton width="44px" borderRadius="50%" loading={isLoading}>
              <img
                src={profilePhoto || "/images/user/default-avatar.webp"}
                alt={username || "User Avatar"}
                className="h-11 w-11 rounded-full border border-solid border-gray-300 dark:border-gray-300 focus-visible:border-solid focus-visible:border object-cover"
                onLoad={() => setIsLoading(false)}
              />
            </Skeleton>
            <span
              className={`ml-2 transform duration-100 ${dropdownOpen ? "rotate-180" : ""}`}
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-gorilla-gray dark:text-white"
              >
                <path
                  d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4062 5.65625 17.6875 5.9375C17.9688 6.21875 17.9688 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1562 10.1875 14.25 10 14.25Z"
                  fill="currentColor"
                />
              </svg>
            </span>
          </button>

          <div
            ref={dropdown}
            onFocus={() => setDropdownOpen(true)}
            onBlur={() => setDropdownOpen(false)}
            className={`absolute -right-12 top-14 z-[30] w-[240px] divide-y divide-stroke overflow-hidden rounded-lg rounded-tl-none rounded-tr-none border-t-0 bg-white/80 dark:bg-[#18181b]/80 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-solid border-gray-300 dark:border-white/20 transition-opacity duration-400 ${dropdownOpen ? "opacity-100" : "opacity-0 pointer-events-none"}`}
          >
            <div className="flex flex-col items-center gap-3 px-4 py-2.5 border-b border-solid border-gray-300 border-l-0 border-r-0 border-t-0 dark:border-white/20">
              <div className="flex items-center justify-center gap-3 w-full h-full">
                <div className="relative aspect-square w-14 rounded-full">
                  <Skeleton borderRadius="50%" loading={isLoading2}>
                    <img
                      src={profilePhoto || "/images/user/default-avatar.webp"}
                      alt={username || "User Avatar"}
                      className="w-full rounded-full object-cover object-center"
                      onLoad={() => setIsLoading2(false)}
                    />
                  </Skeleton>
                </div>

                <div className="flex flex-col gap-0 w-full h-full">
                  <span className="text-sm font-semibold text-gray-600 dark:text-white flex items-center justify-start gap-1 relative top-[2px]">
                    {displayName || "User"} {is_verified &&
                      <Skeleton loading={isVerifiedBadgeLoading} width="15px" height="15px">
                        <img className="w-[15px] h-[15px] user-dropdown-verified-badge" alt="Verified User" src="/images/user/verified.png" onError={() => setIsVerifiedBadgeLoading(false)} onLoad={() => setIsVerifiedBadgeLoading(false)} />
                      </Skeleton>}
                  </span>

                  <p className="text-[10px] text-gorilla-gray dark:text-white">
                    @{username || "User"}
                  </p>

                  <div className="flex items-center justify-between gap-3 w-full h-full">
                    {accountType == 'user' ?
                      <></>
                      :
                      <span className="text-xs font-semibold text-gorilla-gray dark:text-white">
                        Subscribers:  {subscribers || 0}
                      </span>
                    }
                  </div>

                </div>
              </div>

            </div>

            <div className="user-dropdown-links">
              {/* Profile */}
              <Link
                href={userProfilePath}
                className={cn(
                  "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-1",
                  isCurrentPath(userProfilePath)
                    ? "active text-turquoise dark:text-turquoise"
                    : "text-gorilla-gray dark:text-white"
                )}
                onClick={() => setDropdownOpen(false)}
              >
                <span className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" className={cn("fill-gorilla-gray w-4 h-4 group-hover:fill-turquoise", isCurrentPath(userProfilePath) ? "fill-turquoise" : "fill-gorilla-gray dark:fill-white")}>
                    <path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304l-91.4 0z" />
                  </svg>
                  My Profile
                </span>
              </Link>

              {/* Earnings */}
              {/* <Link
                href="/account/earnings"
                className={cn(
                  "group flex w-full items-center justify-between px-[27px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-2",
                  isCurrentPath('/account/earnings')
                    ? "active text-turquoise dark:text-turquoise"
                    : "text-gorilla-gray dark:text-white"
                )}
                onClick={() => setDropdownOpen(false)}
              >
                <span className="flex items-center gap-1.5 w-full">
                  <HiCurrencyDollar className={cn("fill-gorilla-gray w-5 h-5 group-hover:fill-turquoise", isCurrentPath('/account/earnings') ? "fill-turquoise" : "fill-gorilla-gray dark:fill-white")} />
                  Earnings
                </span>
              </Link> */}

              {/* Messages */}
              <Link
                href="/messages"
                className={cn(
                  "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-3",
                  isCurrentPath('/messages')
                    ? "active text-turquoise dark:text-turquoise"
                    : "text-gorilla-gray dark:text-white"
                )}
                onClick={() => setDropdownOpen(false)}
              >
                <span className="flex items-center gap-2 w-full">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className={cn("fill-gorilla-gray w-4 h-4 group-hover:fill-turquoise", isCurrentPath('/messages') ? "fill-turquoise" : "fill-gorilla-gray dark:fill-white")}>
                    <path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48L48 64zM0 176L0 384c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-208L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z" />
                  </svg>
                  Messages
                </span>
              </Link>

              {/* Bookmarks */}
              <Link
                href="/account/bookmarks"
                className={cn(
                  "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-3",
                  isCurrentPath('/account/bookmarks')
                    ? "active text-turquoise dark:text-turquoise"
                    : "text-gorilla-gray dark:text-white"
                )}
                onClick={() => setDropdownOpen(false)}
              >
                <span className="flex items-center gap-2 w-full">
                  <FaBookmark className={cn("fill-gorilla-gray w-4 h-4 group-hover:fill-turquoise", isCurrentPath('/account/bookmarks') ? "fill-turquoise" : "fill-gorilla-gray dark:fill-white")} />
                  Bookmarks
                </span>
              </Link>

              {/* Control Panel */}
              <Link
                href="/control-panel"
                className={cn(
                  "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-5",
                  isCurrentPath('/control-panel')
                    ? "active text-turquoise dark:text-turquoise"
                    : "text-gorilla-gray dark:text-white"
                )}
                onClick={() => setDropdownOpen(false)}
              >
                <span className="flex items-center gap-2 w-full">
                  <GiPieChart className={cn("fill-gorilla-gray w-4 h-4 group-hover:fill-turquoise", isCurrentPath('/control-panel') ? "fill-turquoise" : "fill-gorilla-gray dark:fill-white")} />
                  Control Panel
                </span>
              </Link>

              {/* Become A Creator */}
              {accountType == "user" &&
                <Link
                  href="https://sugarfans.com/site/settings/config/creator"
                  className={cn(
                    "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-5",
                    isCurrentPath('/network')
                      ? "active text-turquoise dark:text-turquoise"
                      : "text-gorilla-gray dark:text-white"
                  )}
                  onClick={() => setDropdownOpen(false)}
                >
                  <span className="flex items-center gap-2 w-full">
                    <HiOutlineBuildingLibrary className={cn("fill-gorilla-gray w-4 h-4 group-hover:fill-turquoise", isCurrentPath('/network') ? "fill-turquoise" : "fill-gorilla-gray dark:fill-white")} />
                    Become A Creator
                  </span>
                </Link>
              }

              {/* Creator Network */}
              {accountType == "creator" &&
                <Link
                  href="/network"
                  className={cn(
                    "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-5",
                    isCurrentPath('/network')
                      ? "active text-turquoise dark:text-turquoise"
                      : "text-gorilla-gray dark:text-white"
                  )}
                  onClick={() => setDropdownOpen(false)}
                >
                  <span className="flex items-center gap-2 w-full">
                    <Globe className={cn("text-gorilla-gray w-4 h-4 group-hover:text-turquoise relative -top-[1px]", isCurrentPath('/network') ? "text-turquoise" : "text-gorilla-gray dark:text-white")} />
                    Creator Network
                  </span>
                </Link>
              }

              {/* Community Hub */}
              <Link
                href="/account/bookmarks"
                className={cn(
                  "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-4",
                  isCurrentPath('/account/bookmarks')
                    ? "active text-turquoise dark:text-turquoise"
                    : "text-gorilla-gray dark:text-white"
                )}
                onClick={() => setDropdownOpen(false)}
              >
                <span className="flex items-center gap-2 w-full">
                  <Globe className={cn("text-gorilla-gray w-4 h-4 group-hover:text-turquoise relative -top-[1px]", isCurrentPath('/community-hub') ? "text-turquoise" : "text-gorilla-gray dark:text-white")} />
                  Community Hub
                </span>
              </Link>

              {/* Account Settings */}
              <Link
                href="/account/settings"
                className={cn(
                  "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm dark:hover:text-turquoise hover:text-turquoise indicator relative dropdown-item dropdown-item-6",
                  isCurrentPath('/account/settings')
                    ? "active text-turquoise dark:text-turquoise"
                    : "text-gorilla-gray dark:text-white"
                )}
                onClick={() => setDropdownOpen(false)}
              >
                <span className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className={cn("fill-gorilla-gray w-4 h-4 group-hover:fill-turquoise", isCurrentPath('/account/settings') ? "fill-turquoise" : "fill-gorilla-gray dark:fill-white")}>
                    <path d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z" />
                  </svg>
                  Account Settings
                </span>
              </Link>

            </div>

            {/* Logout */}
            <div className="user-dropdown-links">
              <button
                onClick={() => handleLogout()}
                className={cn(
                  "group flex w-full items-center justify-between px-[29px] py-2.5 text-sm text-gorilla-gray hover:text-red-500 bg-[#e5e5e5] hover:bg-[#e5e5e5cc] dark:text-white dark:bg-zinc-900 dark:hover:bg-zinc-800 dark:hover:text-red-500 indicator relative logout dropdown-item dropdown-item-7"
                )}
              >
                <span className="flex items-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 512 512"
                    className="w-4 h-4 fill-gorilla-gray dark:fill-white group-hover:fill-red-500"
                  >
                    <path d="M377.9 105.9L500.7 228.7c7.2 7.2 11.3 17.1 11.3 27.3s-4.1 20.1-11.3 27.3L377.9 406.1c-6.4 6.4-15 9.9-24 9.9c-18.7 0-33.9-15.2-33.9-33.9l0-62.1-128 0c-17.7 0-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32l128 0 0-62.1c0-18.7 15.2-33.9 33.9-33.9c9 0 17.6 3.6 24 9.9zM160 96L96 96c-17.7 0-32 14.3-32 32l0 256c0 17.7 14.3 32 32 32l64 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-64 0c-53 0-96-43-96-96L0 128C0 75 43 32 96 32l64 0c17.7 0 32 14.3 32 32s-14.3 32-32 32z" />
                  </svg>
                  Log out
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};