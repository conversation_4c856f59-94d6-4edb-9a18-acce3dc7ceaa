import { InjectedWindowProvider } from '@polkadot/extension-inject/types';

export interface ExtensionMetadata {
  id: string;
  title: string;
  description?: string;
  urls?: { main?: string; browsers?: Record<string, string> };
  iconUrl?: string;
  version?: string;
}

export interface ExtensionConfiguration {
  disallowed?: string[];
  supported?: ExtensionMetadata[];
}

export interface WalletExtension extends InjectedWindowProvider {
  metadata: ExtensionMetadata;
}

// Export a constant conforming to ExtensionConfiguration
export const extensionConfiguration: ExtensionConfiguration = {
  disallowed: [],
  supported: [
    {
      id: 'chilled-kongs',
      title: 'Chilled Kongs',
      description: 'An example wallet extension.',
      urls: {
        main: 'https://example.com',
        browsers: {
          chrome: 'https://chrome.example.com',
          firefox: 'https://firefox.example.com'
        }
      },
      iconUrl: 'https://example.com/icon.png',
      version: '1.0.0'
    }
  ]
};