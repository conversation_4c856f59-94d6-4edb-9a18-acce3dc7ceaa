import React, { useState, useEffect, useCallback, forwardRef, ForwardedRef, useMemo } from 'react';
import Editor from '@draft-js-plugins/editor';
import createMentionPlugin from '@draft-js-plugins/mention';
import { EditorState, ContentState, convertFromRaw, convertToRaw, RichUtils, Modifier, SelectionState, DraftHandleValue } from 'draft-js';
import debounce from 'lodash/debounce';
import editorStyles from '@/styles/MultiMentionTriggers.module.css';
import { EntryComponentProps } from '@draft-js-plugins/mention/lib/MentionSuggestions/Entry';

export type EditorRefType = React.ComponentRef<typeof Editor>;

interface Suggestion {
    id: string;
    value: string;
    display: string;
    avatar?: string;
    username?: string;
    hashtag?: string;
    type: 'mention' | 'hashtag';
}

interface InitialTagData {
    mentions: Array<{ userId: string; username: string; profilePhoto?: string }>;
    hashtags: string[];
}

interface MixedTagInputProps {
    value: string;
    rawState?: string | null;
    fetchUserSuggestions: (query: string) => Promise<Suggestion[]>;
    fetchHashtagSuggestions: (query: string) => Promise<Suggestion[]>;
    onChange: (value: string) => void;
    onTagData?: (data: {
        hashtags: string[];
        mentions: Array<{ userId: string; username: string; profilePhoto?: string }>;
    }) => void;
    initialTagData?: InitialTagData;
}

const createEditorStateStore = (initialEditorState: EditorState) => {
    let editorState: EditorState = initialEditorState;
    let onChangeCallback: ((state: EditorState) => void) | null = null;

    return {
        getEditorState: () => {
            console.log('store.getEditorState called, returning:', editorState ? 'valid EditorState' : 'null');
            if (!editorState) {
                console.warn('EditorState is null, reinitializing');
                editorState = EditorState.createEmpty();
            }
            return editorState;
        },
        setEditorState: (state: EditorState) => {
            console.log('store.setEditorState called with:', state ? 'valid EditorState' : 'null');
            editorState = state;
            if (onChangeCallback) {
                onChangeCallback(state);
            }
        },
        onChange: (callback: (state: EditorState) => void) => {
            console.log('store.onChange callback set');
            onChangeCallback = callback;
        },
    };
};

const singletonStore = createEditorStateStore(EditorState.createEmpty());

const MentionComponent = (props: any) => {
    const { mention, className, entityKey, decoratedText, ...rest } = props;
    console.log('MentionComponent rendered:', { mention, entityKey, decoratedText });

    const displayText = mention.type === 'hashtag' ? `#${mention.name || mention.hashtag || mention.display}` : `@${mention.name || mention.display || mention.username}`;

    return (
        <span
            {...rest}
            className={`${className} ${editorStyles.mention}`}
            title={displayText}
        >
            {displayText}
        </span>
    );
};

const SafeMentionSuggestions = (props: any) => {
    if (!props.store || typeof props.store.getEditorState !== 'function' || typeof props.store.setEditorState !== 'function') {
        console.error('Invalid store detected:', props.store);
        return null;
    }
    return <props.MentionSuggestions {...props} />;
};

const initializeEditorState = (
    text: string,
    tagData?: InitialTagData
): EditorState => {
    let contentState = ContentState.createFromText(text);
    let offsetAdjustments: Array<{ start: number; length: number }> = [];

    const applyTag = (
        symbol: '@' | '#',
        entries: Array<{ id: string; name: string; avatar?: string }>
    ) => {
        entries.forEach(entry => {
            const raw = `${symbol}${entry.name}`;
            const regex = new RegExp(`\\b${symbol}${entry.name}\\b`, 'g');
            let match;
            while ((match = regex.exec(text)) !== null) {
                const start = match.index;
                const end = match.index + raw.length;

                const isOverlapping = offsetAdjustments.some(
                    adj => start < adj.start + adj.length && end > adj.start
                );
                if (isOverlapping) return;

                contentState = contentState.createEntity('mention', 'IMMUTABLE', {
                    mention: {
                        id: entry.id,
                        name: entry.name,
                        link: raw,
                        avatar: entry.avatar,
                        type: symbol === '@' ? 'mention' : 'hashtag',
                        display: raw,
                    },
                });
                const entityKey = contentState.getLastCreatedEntityKey();

                const blockKey = contentState.getFirstBlock().getKey();
                const selection = SelectionState.createEmpty(blockKey).merge({
                    anchorOffset: start,
                    focusOffset: end,
                });
                contentState = Modifier.applyEntity(contentState, selection, entityKey);

                offsetAdjustments.push({ start, length: raw.length });
            }
        });
    };

    offsetAdjustments = [];

    if (tagData?.mentions?.length) {
        applyTag(
            '@',
            tagData.mentions.map(m => ({
                id: m.userId,
                name: m.username,
                avatar: m.profilePhoto,
            }))
        );
    }

    if (tagData?.hashtags?.length) {
        applyTag(
            '#',
            tagData.hashtags.map(h => ({
                id: `#${h}`,
                name: h,
            }))
        );
    }

    return EditorState.createWithContent(contentState);
};

const initializeFromRawState = (rawState: string, mentionPlugin: any): EditorState | null => {
    try {
        const raw = JSON.parse(rawState);
        console.log('Parsed rawState:', raw);
        Object.keys(raw.entityMap).forEach(key => {
            const entity = raw.entityMap[key];
            if (entity.type === '#mention' || entity.type === '@mention' || entity.type === 'mention') {
                entity.type = 'mention';
                entity.data.mention.type = entity.data.mention.type || (entity.type === '#mention' ? 'hashtag' : 'mention');
                entity.data.mention.name = entity.data.mention.name || entity.data.mention.display || entity.data.mention.username || entity.data.mention.hashtag;
                entity.data.mention.display = entity.data.mention.display || entity.data.mention.name || entity.data.mention.username || entity.data.mention.hashtag;
            }
        });
        const contentState = convertFromRaw(raw);
        // Reapply plugin decorators to ensure entities are recognized
        return EditorState.createWithContent(contentState, mentionPlugin ? mentionPlugin.decorators : undefined);
    } catch (error) {
        console.error('Error parsing rawState:', error);
        return null;
    }
};

const customAddMention = (
    editorState: EditorState,
    mention: any,
    mentionPrefix: (trigger: string) => string,
    trigger: string,
    mutability: 'IMMUTABLE' | 'MUTABLE' | 'SEGMENTED'
): EditorState => {
    const contentState = editorState.getCurrentContent();
    const selection = editorState.getSelection();

    const contentStateWithEntity = contentState.createEntity('mention', mutability, {
        mention: {
            id: mention.id,
            name: mention.name || mention.display || mention.value,
            link: mention.link || mention.username || mention.hashtag,
            avatar: mention.avatar,
            type: mention.type,
            display: mention.display || mention.name || mention.value,
            ...(mention.type === 'mention' ? { username: mention.username || mention.value } : { hashtag: mention.hashtag || mention.value }),
        },
    });
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();

    const mentionText = mentionPrefix(trigger) + (mention.name || mention.display || mention.value);
    let newContentState = Modifier.replaceText(
        contentStateWithEntity,
        selection,
        mentionText,
        undefined,
        entityKey
    );

    const newSelection = selection.merge({
        anchorOffset: selection.getAnchorOffset() + mentionText.length,
        focusOffset: selection.getAnchorOffset() + mentionText.length,
    });

    newContentState = Modifier.insertText(newContentState, newSelection, ' ');

    return EditorState.push(editorState, newContentState, 'insert-characters');
};

const MixedTagInput = forwardRef<EditorRefType, MixedTagInputProps>(
    (
        {
            value,
            rawState,
            fetchUserSuggestions,
            fetchHashtagSuggestions,
            onChange,
            onTagData,
            initialTagData,
        }: MixedTagInputProps,
        ref: ForwardedRef<EditorRefType>
    ) => {
        const [editorState, setEditorState] = useState(() => {
            if (rawState) {
                const mentionPlugin = createMentionPlugin({
                    store: singletonStore,
                    mentionTrigger: ['@', '#'],
                    mentionPrefix: (trigger) => trigger,
                    entityMutability: 'IMMUTABLE',
                    supportWhitespace: true,
                    mentionComponent: MentionComponent,
                });
                const state = initializeFromRawState(rawState, mentionPlugin);
                if (state) {
                    console.log('Initialized editorState from rawState:', convertToRaw(state.getCurrentContent()));
                    singletonStore.setEditorState(state);
                    return state;
                }
            }
            const state = initializeEditorState(value, initialTagData);
            console.log('Initialized editorState from text and tagData:', convertToRaw(state.getCurrentContent()));
            singletonStore.setEditorState(state);
            return state;
        });
        const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
        const [open, setOpen] = useState(false);
        const [currentTrigger, setCurrentTrigger] = useState<'@' | '#' | null>(null);
        const [isLoading, setIsLoading] = useState(false);
        const [isInitializing, setIsInitializing] = useState(true);

        const editorRef = ref || React.createRef<EditorRefType>();
        const store = singletonStore;

        const mentionPlugin = useMemo(() => {
            console.log('Creating mention plugin with store:', store);
            return createMentionPlugin({
                store,
                mentionTrigger: ['@', '#'],
                mentionPrefix: (trigger) => trigger,
                entityMutability: 'IMMUTABLE',
                supportWhitespace: true,
                mentionComponent: MentionComponent,
            });
        }, []);

        const { MentionSuggestions } = mentionPlugin;
        const plugins = useMemo(() => [mentionPlugin], [mentionPlugin]);

        const debouncedSearch = useMemo(() => {
            console.log('Creating new debouncedSearch function');
            return debounce(async (trigger: '@' | '#', query: string) => {
                console.log('debouncedSearch executed with:', { trigger, query });
                try {
                    setIsLoading(true);
                    let results: Suggestion[] = [];
                    if (trigger === '@') {
                        results = await fetchUserSuggestions(query);
                    } else {
                        results = await fetchHashtagSuggestions(query);
                    }
                    console.log('Fetched suggestions:', results);
                    setSuggestions(results);
                    setCurrentTrigger(trigger);
                    setOpen(true);
                } catch (error) {
                    console.error('Search error:', error);
                    setSuggestions([]);
                    setCurrentTrigger(null);
                    setOpen(false);
                } finally {
                    setIsLoading(false);
                }
            }, 1000, { leading: false, trailing: true });
        }, [fetchUserSuggestions, fetchHashtagSuggestions]);

        const onSearchChange = useCallback(
            ({ value: query, trigger }: { value: string; trigger: string }) => {
                console.log('onSearchChange called:', { query, trigger, isInitializing });

                if (isInitializing) {
                    setOpen(false);
                    setCurrentTrigger(null);
                    setSuggestions([]);
                    return;
                }

                if (trigger === '@' && query.length < 3) {
                    setSuggestions([]);
                    setCurrentTrigger(trigger as '@' | '#');
                    setOpen(true);
                    setIsLoading(false);
                    return;
                }

                if (trigger === '#' && query.length < 2) {
                    setSuggestions([]);
                    setCurrentTrigger(trigger as '@' | '#');
                    setOpen(true);
                    setIsLoading(false);
                    return;
                }

                setOpen(true);
                setIsLoading(true);
                console.log('Calling debouncedSearch with:', { trigger, query });
                debouncedSearch(trigger as '@' | '#', query);
            },
            [debouncedSearch, isInitializing]
        );

        const onOpenChange = useCallback(
            (newOpen: boolean) => {
                console.log('onOpenChange called:', {
                    newOpen,
                    currentTrigger,
                    suggestionsLength: suggestions.length,
                    isLoading,
                    isInitializing,
                });
                if (isInitializing || (!newOpen && !currentTrigger && suggestions.length === 0 && !isLoading)) {
                    setOpen(false);
                    setCurrentTrigger(null);
                } else {
                    setOpen(newOpen);
                }
            },
            [currentTrigger, suggestions.length, isLoading, isInitializing]
        );

        const onAddMention = (mention: any) => {
            console.log('onAddMention called:', { mention });
            const newState = customAddMention(
                editorState,
                mention,
                (trigger) => trigger,
                currentTrigger || '@',
                'IMMUTABLE'
            );
            setEditorState(newState);
            setOpen(false);
            setCurrentTrigger(null);
            setSuggestions([]);
        };

        const handleKeyCommand = (command: string, editorState: EditorState) => {
            const newState = RichUtils.handleKeyCommand(editorState, command);
            if (newState) {
                setEditorState(newState);
                return 'handled';
            }
            return 'not-handled';
        };

        const handleBeforeInput = (
            chars: string,
            editorState: EditorState
        ): DraftHandleValue => {
            if (isInitializing) {
                return 'not-handled';
            }

            let selection = editorState.getSelection();
            const content = editorState.getCurrentContent();
            const block = content.getBlockForKey(selection.getStartKey());
            const textBeforeCursor = block.getText().slice(0, selection.getStartOffset());

            console.log('handleBeforeInput:', { chars, textBeforeCursor });

            if (chars === '@' || chars === '#') {
                const trigger = chars;
                setCurrentTrigger(trigger as '@' | '#');
                setOpen(true);
                setSuggestions([]);
                setIsLoading(false);
                return 'not-handled';
            }

            const lastTriggerIndex = Math.max(
                textBeforeCursor.lastIndexOf('@'),
                textBeforeCursor.lastIndexOf('#')
            );
            if (lastTriggerIndex !== -1) {
                const lastTriggerChar = textBeforeCursor[lastTriggerIndex];
                if (lastTriggerChar === '@' || lastTriggerChar === '#') {
                    const textAfterTrigger = textBeforeCursor.slice(lastTriggerIndex + 1);
                    const query = textAfterTrigger + chars;
                    console.log('Manual trigger detected:', { lastTriggerChar, query, textAfterTrigger });
                    onSearchChange({ value: query, trigger: lastTriggerChar });
                    return 'not-handled';
                }
            }

            setOpen(false);
            setCurrentTrigger(null);
            setSuggestions([]);
            return 'not-handled';
        };

        const onChangeEditor = (newEditorState: EditorState) => {
            console.log('onChangeEditor called, new content:', newEditorState.getCurrentContent().getPlainText());

            const currentContent = editorState.getCurrentContent();
            const newContent = newEditorState.getCurrentContent();
            const currentText = currentContent.getPlainText();
            const newText = newContent.getPlainText();
            const currentRaw = JSON.stringify(convertToRaw(currentContent));
            const newRaw = JSON.stringify(convertToRaw(newContent));

            if (currentText === newText && currentRaw === newRaw) {
                console.log('No content change, skipping setEditorState');
                return;
            }

            setEditorState(newEditorState);
            store.setEditorState(newEditorState);

            const rawContent = convertToRaw(newContent);
            const plainText = rawContent.blocks.map((block: any) => block.text).join('\n');
            onChange(plainText);

            const entityMap = rawContent.entityMap;
            const mentions: Array<{ userId: string; username: string; profilePhoto?: string }> = [];
            const hashtags: string[] = [];

            Object.values(entityMap).forEach((entity: any) => {
                console.log('Entity found:', { entity });
                if (entity.type === 'mention' || entity.type === '#mention' || entity.type === '@mention') {
                    const mentionData = entity.data.mention;
                    if (mentionData.type === 'mention' || entity.type === '@mention') {
                        mentions.push({
                            userId: mentionData.id,
                            username: mentionData.name || mentionData.display || mentionData.username,
                            profilePhoto: mentionData.avatar || '',
                        });
                    } else if (mentionData.type === 'hashtag' || entity.type === '#mention') {
                        hashtags.push(mentionData.name || mentionData.hashtag);
                    }
                }
            });

            console.log('Extracted mentions and hashtags:', { mentions, hashtags });
            onTagData?.({ mentions, hashtags });
        };

        const Entry = (props: EntryComponentProps) => {
            const { mention, theme, isFocused, searchValue, selectMention, ...parentProps } = props;
            const suggestion = mention as any;
            const isMention = suggestion.type === 'mention';

            return (
                <div
                    {...parentProps}
                    className={`flex items-center gap-2 p-2 ${isFocused ? 'bg-gray-100 dark:bg-zinc-700' : ''} hover:bg-gray-100 dark:hover:bg-zinc-400 transition-colors cursor-pointer`}
                >
                    {isMention && suggestion.avatar && (
                        <img
                            src={suggestion.avatar}
                            alt={suggestion.display}
                            className="w-8 h-8 rounded-full object-cover"
                            onError={(e) => {
                                (e.target as HTMLImageElement).src = '/images/user/default-avatar.webp';
                            }}
                        />
                    )}
                    <div className="flex flex-col">
                        <span className="font-medium text-[#121212] dark:text-white">
                            {isMention ? suggestion.display : suggestion.display}
                        </span>
                        {isMention && suggestion.username && (
                            <span className="text-sm text-gray-500 dark:text-gray-400 dark:hover:text-white">
                                {suggestion.username}
                            </span>
                        )}
                    </div>
                </div>
            );
        };

        useEffect(() => {
            const timer = setTimeout(() => {
                setIsInitializing(false);
            }, 100);
            return () => clearTimeout(timer);
        }, []);

        useEffect(() => {
            return () => {
                console.log('Flushing debouncedSearch on unmount');
                debouncedSearch.flush();
            };
        }, [debouncedSearch]);

        useEffect(() => {
            console.log('MixedTagInput mounted');
            return () => {
                console.log('MixedTagInput unmounted');
            };
        }, []);

        useEffect(() => {
            console.log('Dropdown state:', { open, currentTrigger, isLoading, suggestions });
        }, [open, currentTrigger, isLoading, suggestions]);

        useEffect(() => {
            console.log('Syncing editorState to store');
            store.setEditorState(editorState);
        }, [editorState]);

        useEffect(() => {
            const currentContent = editorState.getCurrentContent();
            const currentText = currentContent.getPlainText();
            const currentRaw = JSON.stringify(convertToRaw(currentContent));
            console.log('Editor content updated:', { value, currentText, rawState, initialTagData });

            // Only update if rawState is provided and differs significantly
            if (!isInitializing && rawState && rawState !== currentRaw) {
                console.log('Updating editorState from rawState');
                const newEditorState = initializeFromRawState(rawState, mentionPlugin);
                if (newEditorState) {
                const newRaw = JSON.stringify(convertToRaw(newEditorState.getCurrentContent()));
                if (newRaw !== currentRaw) {
                    console.log('Setting new editorState:', newEditorState.getCurrentContent().getPlainText());
                setEditorState(EditorState.forceSelection(newEditorState, editorState.getSelection()));
                        store.setEditorState(newEditorState);
                } else {
                    console.log('No significant change in editorState, skipping update');
                }
            }
            }
        }, [rawState, isInitializing, editorState, mentionPlugin]);

        return (
            <div
                className={editorStyles.editor}
                onClick={() => {
                    if (editorRef && 'current' in editorRef && editorRef.current) {
                        editorRef.current.focus();
                    }
                }}
            >
                <Editor
                    editorState={editorState}
                    onChange={onChangeEditor}
                    plugins={plugins}
                    ref={editorRef}
                    placeholder="What's on your mind?"
                    handleKeyCommand={handleKeyCommand}
                    handleBeforeInput={handleBeforeInput}
                />

                <SafeMentionSuggestions
                    MentionSuggestions={MentionSuggestions}
                    open={open}
                    onOpenChange={onOpenChange}
                    onSearchChange={onSearchChange}
                    suggestions={suggestions.map((suggestion) => ({
                        id: suggestion.id,
                        name: suggestion.value,
                        link: suggestion.username || suggestion.hashtag,
                        avatar: suggestion.avatar,
                        type: suggestion.type,
                        display: suggestion.display,
                        ...(suggestion.type === 'mention'
                            ? { username: `@${suggestion.value}` }
                            : { hashtag: `#${suggestion.value}` }),
                    }))}
                    entryComponent={Entry}
                    onAddMention={onAddMention}
                    store={store}
                />
            </div>
        );
    }
);
export default MixedTagInput;