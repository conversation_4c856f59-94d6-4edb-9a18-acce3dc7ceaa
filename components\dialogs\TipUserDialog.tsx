"use client";

import React, { useState, useRef, useEffect } from "react";
import { X, CreditCard, ChevronDown, ChevronUp } from "lucide-react";
import { AnimatedModal } from "@/components/ui/animated-modal";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { CustomTextarea } from "@/components/ui/chat/custom-textarea";
import { cn } from "@/lib/utils";
import { MessagesSquare } from "lucide-react";
import { useExpandable } from "@/hooks/useExpandable";
import { motion } from "framer-motion";
import { CreditCardIcon, PayPalIcon, CryptoIcon } from "@/components/icons/PaymentIcons";
import { Checkbox } from "@/components/ui/checkbox";

export type PaymentMethod = {
  id: string;
  name: string;
  icon: React.ReactNode;
  fee: string;
};

interface TipUserDialogProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    username: string;
    displayName?: string;
    profileImage?: string;
  };
  onTipSuccess: () => void;
}

const predefinedAmounts = [5, 10, 25, 50, 100];

// Define default payment method
const defaultPaymentMethod: PaymentMethod = {
  id: "default",
  name: "Select payment method",
  icon: <CreditCard className="w-5 h-4 mr-2" />,
  fee: "",
};

// Define actual payment methods for selection
const paymentMethodOptions: PaymentMethod[] = [
  {
    id: "credit-card",
    name: "Credit Card",
    icon: <CreditCardIcon className="w-5 h-4 mr-2" />,
    fee: "2.9% + $0.30 transaction fee",
  },
  {
    id: "paypal",
    name: "PayPal",
    icon: <PayPalIcon className="w-5 h-4 mr-2" />,
    fee: "3.49% + $0.49 transaction fee",
  },
  {
    id: "crypto",
    name: "Cryptocurrency",
    icon: <CryptoIcon className="w-5 h-4 mr-2" />,
    fee: "1% transaction fee",
  },
];

const TipUserDialog = ({ isOpen, onClose, userData, onTipSuccess }: TipUserDialogProps) => {
  const [selectedAmount, setSelectedAmount] = useState(5);
  const [customAmount, setCustomAmount] = useState("");
  const [comment, setComment] = useState("");
  const [isCustom, setIsCustom] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(defaultPaymentMethod);
  const [ageConfirmed, setAgeConfirmed] = useState(false);

  const { isExpanded, toggleExpand, animatedHeight } = useExpandable(false);
  const paymentMethodsContentRef = useRef<HTMLDivElement>(null);

  const inputRef = useRef<HTMLInputElement>(null);

  // Update animated height when expanded state changes
  useEffect(() => {
    if (paymentMethodsContentRef.current) {
      if (isExpanded) {
        animatedHeight.set(paymentMethodsContentRef.current.scrollHeight);
      } else {
        animatedHeight.set(0);
      }
    }
  }, [isExpanded, animatedHeight]);

  useEffect(() => {
    if (isCustom && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isCustom]);

  const handleAmountSelect = (amt: number) => {
    setIsCustom(false);
    setCustomAmount("");
    setSelectedAmount(amt);
  };

  const handleCustomSelect = () => setIsCustom(true);

  const handleCustomChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = e.target.value.replace(/[^0-9.]/g, "");
    setCustomAmount(v);
    setSelectedAmount(+v || 0);
  };

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setPaymentMethod(method);
    toggleExpand();
  };

  const handleSend = async () => {
    setIsLoading(true);
    try {
      await new Promise((r) => setTimeout(r, 1200));
      onTipSuccess();
    } finally {
      setIsLoading(false);
    }
  };

  // Footer content with age confirmation checkbox and send button
  const footerContent = (
    <div className="space-y-4">
      {/* Age confirmation checkbox */}
      <div className="flex items-start space-x-3 bg-zinc-900 dark:bg-zinc-800 p-3 rounded-lg">
        <Checkbox
          id="age-confirmation"
          checked={ageConfirmed}
          onCheckedChange={(checked) => setAgeConfirmed(checked === true)}
          className="mt-1"
        />
        <label
          htmlFor="age-confirmation"
          className="text-white dark:text-white text-sm font-medium leading-tight cursor-pointer"
        >
          You confirm that you are at least 18 years of age and have reached the age of majority in your place of residence.
        </label>
      </div>

      {/* Send button */}
      <Button
        onClick={handleSend}
        disabled={isLoading || selectedAmount <= 0 || !ageConfirmed || paymentMethod.id === "default"}
        className={cn(
          "w-full py-3 text-lg font-semibold rounded-full transition-colors",
          isLoading || selectedAmount <= 0 || !ageConfirmed || paymentMethod.id === "default"
            ? `bg-turquoise opacity-50 cursor-not-allowed`
            : `bg-turquoise hover:bg-turquoise/90 text-white dark:bg-turquoise/90 dark:hover:bg-turquoise/90`
        )}
      >
        {isLoading ? "Processing..." : `Send Tip`}
      </Button>
    </div>
  );

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="2xl"
      closeButton={false}
      header={null}
      footer={footerContent}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-zinc-700">
          <div className="flex items-center space-x-2">
            <Avatar className="h-16 w-16">
              <AvatarImage
                src={userData.profileImage}
                alt={userData.displayName || userData.username}
              />
              <AvatarFallback className="bg-gray-200 dark:bg-zinc-700 text-gray-800 dark:text-gray-200">
                {userData.username.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Show support for</p>
              <h2 className="mt-0 text-xl font-bold text-gray-900 dark:text-white">
                {userData.displayName || userData.username}
              </h2>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Main Content */}
        <div className="p-4 overflow-y-auto flex-1">
          {/* Comment Box */}
          <div className="mb-4">
            <CustomTextarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Add a comment if you wish..."
              icon={<MessagesSquare size={18} className="text-gray-500 dark:text-gray-400" />}
              className="w-full rounded-lg bg-gray-100 focus:bg-white dark:bg-zinc-700 border border-solid border-transparent focus:border-gray-300 dark:focus:border-white dark:hover:bg-zinc-700 dark:focus:bg-zinc-800 min-h-[100px] resize-none focus:outline-none focus:ring-none transition-colors"
            />
          </div>

          {/* Amount Selection */}
          <div className="grid grid-cols-6 gap-2 mb-4">
            {predefinedAmounts.map((amt) => (
              <button
                key={amt}
                onClick={() => handleAmountSelect(amt)}
                className={cn(
                  "p-2 rounded-lg text-sm font-semibold transition-colors border border-solid border-transparent",
                  selectedAmount === amt && !isCustom
                    ? "bg-gray-300 dark:bg-zinc-500 text-white border border-solid border-gray-700 dark:border-white"
                    : "bg-gray-200 hover:bg-gray-100 text-gorilla-gray dark:text-white dark:bg-zinc-700 dark:hover:bg-zinc-600"
                )}
              >
                ${amt}
              </button>
            ))}

            <button
              onClick={handleCustomSelect}
              className={cn(
                "py-3 px-4 rounded-lg text-sm font-semibold border transition-colors border-solid border-transparent",
                isCustom
                  ? "bg-gray-300 dark:bg-zinc-500 text-white border border-solid border-gray-700 dark:border-white"
                  : "bg-gray-200 hover:bg-gray-100 text-gorilla-gray dark:text-white dark:bg-zinc-700 dark:hover:bg-zinc-600"
              )}
            >
              Custom
            </button>
          </div>

          {/* Custom Amount Input */}
          {isCustom && (
            <div className="mb-4">
              <div className="relative">
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                  $
                </span>
                <input
                  ref={inputRef}
                  type="text"
                  value={isCustom ? customAmount : selectedAmount}
                  onChange={handleCustomChange}
                  onClick={() => setIsCustom(true)}
                  placeholder="Enter amount"
                  className="w-full pl-8 pr-4 py-3 rounded-lg bg-gray-100 focus:bg-white dark:bg-zinc-700 border border-solid border-transparent focus:border-gray-300 dark:focus:border-white dark:hover:bg-zinc-700 dark:focus:bg-zinc-800 text-lg font-semibold text-gorilla-gray dark:text-white focus:outline-none focus:ring-none transition-colors"
                />
              </div>
            </div>
          )}

          {/* Payment Method Section */}
          <div className="mb-4">
            <div className="flex flex-col">
              {/* Payment Method Toggle Button */}
              <button
                onClick={toggleExpand}
                className="flex items-center justify-between w-full p-3 rounded-lg bg-gray-100 dark:bg-zinc-700 hover:bg-gray-200 dark:hover:bg-zinc-600 transition-colors"
              >
                <div className="flex items-center text-gray-700 dark:text-gray-200">
                  {paymentMethod.icon}
                  <span className="font-medium">{paymentMethod.name}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">
                    {paymentMethod.fee}
                  </span>
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  )}
                </div>
              </button>

              {/* Expandable Payment Methods List */}
              <motion.div
                className="overflow-hidden"
                style={{ height: animatedHeight }}
              >
                <div
                  ref={paymentMethodsContentRef}
                  className="mt-2 max-h-48 overflow-y-auto"
                >
                  {paymentMethodOptions.map((method) => (
                    <div
                      key={method.id}
                      className="flex items-center justify-between p-3 hover:bg-gray-100 dark:hover:bg-zinc-600 rounded-lg cursor-pointer transition-colors"
                      onClick={() => handlePaymentMethodSelect(method)}
                    >
                      <div className="flex items-center space-x-4">
                        {method.icon}
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {method.name}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {method.fee}
                          </p>
                        </div>
                      </div>
                      <div
                        className={cn(
                          "w-6 h-6 rounded-full border-2",
                          paymentMethod.id === method.id
                            ? "border-[#E6FF73] bg-[#E6FF73]"
                            : "border-gray-300 dark:border-gray-600"
                        )}
                      >
                        {paymentMethod.id === method.id && (
                          <div className="w-full h-full flex items-center justify-center">
                            <div className="w-2 h-2 rounded-full bg-black"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>

        </div>
      </div>
    </AnimatedModal>
  );
};
export default TipUserDialog;