import { jsxs as o, jsx as C } from "react/jsx-runtime";
import { memo as t } from "react";
const r = (e) => /* @__PURE__ */ o(
  "svg",
  {
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...e,
    children: [
      /* @__PURE__ */ C(
        "path",
        {
          "fill-rule": "evenodd",
          "clip-rule": "evenodd",
          d: "M1 8C1 4.70017 1 3.05025 2.02513 2.02513C3.05025 1 4.70017 1 8 1C11.2998 1 12.9498 1 13.9748 2.02513C15 3.05025 15 4.70017 15 8C15 11.2998 15 12.9498 13.9748 13.9748C12.9498 15 11.2998 15 8 15C4.70017 15 3.05025 15 2.02513 13.9748C1 12.9498 1 11.2998 1 8Z",
          stroke: "#88919E",
          "stroke-width": "1.86667"
        }
      ),
      /* @__PURE__ */ C(
        "path",
        {
          d: "M7.636 9.552C7.384 9.276 7.24 8.916 7.24 8.496C7.24 7.068 8.992 6.9 8.992 6.072C8.992 5.7 8.716 5.388 8.056 5.388C7.456 5.388 6.952 5.688 6.58 6.132L5.608 5.04C6.244 4.296 7.204 3.876 8.236 3.876C9.784 3.876 10.732 4.656 10.732 5.772C10.732 7.524 8.728 7.656 8.728 8.664C8.728 8.856 8.824 9.06 8.944 9.168L7.636 9.552ZM8.212 12.132C7.684 12.132 7.24 11.688 7.24 11.16C7.24 10.632 7.684 10.188 8.212 10.188C8.74 10.188 9.184 10.632 9.184 11.16C9.184 11.688 8.74 12.132 8.212 12.132Z",
          fill: "#88919E"
        }
      )
    ]
  }
), s = t(r);
export {
  s as default
};
