/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */
import Image from 'next/image';

const ReelGrid = () => {
  const reels = [
    { id: 1, thumbnail: 'https://picsum.photos', title: '<PERSON>el 1', author: 'Author 1' },
    { id: 2, thumbnail: 'https://picsum.photos', title: '<PERSON>el 2', author: 'Author 2' },
    { id: 3, thumbnail: 'https://picsum.photos', title: 'Reel 3', author: 'Author 3' },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {reels.map((reel) => (
        <div key={reel.id} className="bg-gray-900 rounded-lg overflow-hidden group">
          <div className="relative aspect-[9/16]">
            <img
              src={reel.thumbnail}
              alt={reel.title}
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <button className="px-4 py-2 bg-cyan-500 rounded-full text-sm hover:bg-cyan-600 transition-colors">
                Play
              </button>
            </div>
          </div>
          <div className="p-4">
            <h3 className="font-semibold mb-1">{reel.title}</h3>
            <p className="text-sm text-gray-400">{reel.author}</p>
          </div>
        </div>
      ))}
    </div>
  )
};
export default ReelGrid;