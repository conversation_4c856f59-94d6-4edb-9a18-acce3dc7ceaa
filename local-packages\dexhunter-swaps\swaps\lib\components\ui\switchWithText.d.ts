/// <reference types="react" />
interface SwitchWithTextType {
    left: string;
    right: string;
    initValue?: boolean;
    onChange: (value: boolean) => void;
    className?: string;
    color?: string;
    height?: string;
    borderRadius?: string;
    disabled?: boolean;
}
declare const _default: import("react").MemoExoticComponent<({ left, right, initValue, onChange, className, color, height, borderRadius, disabled, }: SwitchWithTextType) => import("react/jsx-runtime").JSX.Element>;
export default _default;
