import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { NetworkProfile } from '@/types/network';
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Bookmark, Share2, MoreVertical, Mail, Link as LinkIcon } from 'lucide-react';
import { countryOptions } from "@/components/network/filter-options";
import { mapLocationToISO } from '@/public/main';

interface ProfileCardProps {
  profile: NetworkProfile;
}

const NetworkProfileCard = ({ profile }: ProfileCardProps) => {
  const { id, username, displayName, profilePhoto, coverBanner, coverBannerType, location, isVerified } = profile;
  const [bannerLoading, setBannerLoading] = useState(true);

  // Placeholder rating data (update with actual data from profile when available)
  const rating = 4.1;
  const filledStars = Math.floor(rating);
  const totalStars = 5;

  return (
    <div className="relative bg-white dark:bg-zinc-700 rounded-lg shadow text-center cursor-pointer group border border-solid border-gray-300 dark:border-white">
      {/* Cover Banner (image or video) */}
      <div className="relative h-32 rounded-t-lg overflow-hidden">
        {location && <img src={`/images/flags/ISO-flags/${mapLocationToISO(location)}.svg`} alt={location} className="absolute top-2 left-2 h-8 w-10 rounded-md [filter:drop-shadow(1px_1px_1px_rgba(0,_0,_0,_0.5))] object-cover" />}
        {coverBannerType === 'video' ? (
          <Skeleton className="h-full w-full rounded-t-lg" borderRadius="rounded-t-lg" loading={bannerLoading}>
            <video
              src={coverBanner}
              autoPlay
              muted
              loop
              playsInline
              className="object-cover w-full h-full"
              onLoadedData={() => setBannerLoading(false)}
              onError={() => setBannerLoading(false)}
            />
          </Skeleton>
        ) : (
          <Skeleton className="h-full w-full rounded-t-lg" borderRadius="rounded-t-lg" loading={bannerLoading}>
            <Image
              src={coverBanner || '/images/user/default-banner.webp'}
              alt={username}
              width={1000}
              height={320}
              className="object-cover w-full h-full rounded-t-lg"
              onLoad={() => setBannerLoading(false)}
              onError={() => setBannerLoading(false)}
            />
          </Skeleton>
        )}
      </div>

      {/* Profile Card Content */}
      <div className="relative bg-white dark:bg-zinc-900 rounded-b-lg pt-12 pb-4">
        {/* Profile Image: Overlaps the White and White Sections */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center border border-solid border-white dark:border-white overflow-hidden">
          <Image
            src={profilePhoto || '/images/user/default-avatar.webp'}
            alt={username}
            width={220}
            height={220}
            className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-200"
          />
        </div>

        <div className="flex flex-col items-center gap-1">
          {/* Username */}
          <Link href={`/user/${username}`} ><h2 className="flex items-center gap-1 text-[16px] font-semibold text-gray-700 dark:text-white -mb-2">{displayName || 'Anonymous'} {isVerified && (
            <img className="w-5 h-5" alt="Verified User" src="/images/user/verified.png" />
          )}</h2></Link>

          <Link href={`/user/${username}`} ><h3 className="text-[12px] font-normal text-gray-300 dark:text-white">@{username || 'User'} </h3></Link>

          {/* Star Rating */}
          <div className="flex flex-col items-center gap-1 mb-3 rounded-lg p-2">
            {/* Render filled and outlined stars */}
            <div className="flex gap-1">
              {[...Array(totalStars)].map((_, index) => (
                index < filledStars ? (
                  <svg
                    key={`filled-${index}`}
                    className="w-5 h-5 text-turquoise"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                ) : (
                  <svg
                    key={`outlined-${index}`}
                    className="w-5 h-5 text-turquoise fill-none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                )
              ))}
            </div>
            <span className="text-xs text-gorilla-gray dark:text-white ml-2">
              {rating} of 5 (Collab Rating)
            </span>
          </div>

          {/* View Profile Button */}
          <Link href={`/user/${username}`} className="w-full max-w-40 text-white hover:text-white dark:text-white dark:hover:text-white bg-turquoise dark:bg-turquoise rounded-md py-2 px-4 flex items-center justify-center space-x-1 mx-auto">
            View Profile
          </Link>
        </div>

        {/* More Options: Vertically Centered on the Right */}
        <div className="absolute bottom-2 right-4 transform -translate-y-1/2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-4 justify-center py-4 !px-4 !-mt-[7px] text-blue-gray dark:text-white hover:text-turquoise dark:hover:text-turquoise hover:bg-accent dark:hover:bg-gray-700 rounded-md"
              //disabled={isDeleting}
              >
                <MoreVertical className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none">
                <Mail className="mr-2 h-4 w-4" />
                Request Collab
              </DropdownMenuItem>

              <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />

              <DropdownMenuItem className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none">
                <Share2 className="mr-2 h-4 w-4" />
                Share Profile
              </DropdownMenuItem>

              <DropdownMenuSeparator className="h-px bg-gray-300 dark:bg-white" />

              <DropdownMenuItem className="text-gorilla-gray hover:text-gray-600 dark:text-gray-400 dark:hover:text-white bg-transparent hover:bg-transparent dark:hover:bg-transparent focus:bg-transparent border-none">
                <Bookmark className="mr-2 h-4 w-4" />
                Bookmark
              </DropdownMenuItem>

            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};
export default NetworkProfileCard;