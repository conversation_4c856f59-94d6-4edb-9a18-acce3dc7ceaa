import Image from 'next/image';
import Link from 'next/link';

interface ProfileCardProps {
  id: string;
  username: string;
  profilePhoto: string;
}

export function ProfileCard({ id, username, profilePhoto }: ProfileCardProps) {
  return (
    <Link href={`/user/${username}`}>
      <div className="relative group cursor-pointer">
        <div className="aspect-square overflow-hidden rounded-lg">
          <Image
            src={profilePhoto}
            alt={username}
            width={200}
            height={200}
            className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-200"
          />
        </div>
        <div className="mt-2 text-center">
          <h3 className="text-sm font-medium truncate">{username}</h3>
        </div>
      </div>
    </Link>
  );
}