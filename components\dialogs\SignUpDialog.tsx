import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { AnimatedModal } from "@/components/ui/animated-modal";
import Wallets from '@/components/Wallets';
import { SignupForm } from '@/components/ui/signup-form';
import { Button } from "@/components/ui/button";
import { setWallet, clearWallet } from '@/redux/slices/walletSlice';
import { setUserInfo } from '@/redux/slices/userInfoSlice';
import { useUser } from '@/hooks/useUser';

const SignUpDialog = ({
    isOpen,
    onClose,
    handleOpenLoginPopup,
}: {
    isOpen: boolean;
    onClose: () => void;
    handleOpenLoginPopup: () => void;
}) => {
    const [step, setStep] = useState(1);
    const [userType, setUserType] = useState("user");
    const [error, setError] = useState<string | null>(null);
    const dispatch = useDispatch();
    const { user } = useUser();

    const { isConnecting, isSolConnecting, walletAddress } = useSelector(
        (state: any) => state.wallet
    );

    const handleNextStep = (nextStep: number, selectedUserType?: string, isWeb3?: boolean) => {
        if (selectedUserType) setUserType(selectedUserType);
        setStep(isWeb3 ? 4 : nextStep);
        setError(null);
    };

    const handleBackStep = () => {
        setStep((prev) => Math.max(1, prev - 1));
        setError(null);
    };

    const isButtonsDisabled = isConnecting || isSolConnecting;

    useEffect(() => {
        if (!isOpen) {
            setStep(1);
            setUserType("user");
            setError(null);
            if (walletAddress) {
                dispatch(clearWallet());
            }
        }
    }, [isOpen, walletAddress, dispatch]);

    const handleSignupSuccess = (userData: any) => {
        if (user) {
            dispatch(setUserInfo({
                username: user.user_info?.account?.username,
                displayName: user.user_info?.account?.displayName,
                profilePhoto: user.user_info?.profilePhoto,
                accountType: user.account_type,
                is_verified: user.is_verified,
            }));
        }
        onClose();
    };

    const renderStepContent = () => {
        switch (step) {
            case 1:
                return (
                    <div className="grid grid-cols-2 gap-4 w-full p-4">
                        <Button
                            onClick={() => handleNextStep(2, "user")}
                            className="h-52 bg-gray-300 dark:bg-zinc-600 border border-white text-black dark:text-white hover:bg-gray-400 dark:hover:bg-zinc-700 transition-transform hover:-translate-y-1"
                        >
                            User Account
                        </Button>

                        <Button
                            onClick={() => handleNextStep(2, "creator")}
                            className="h-52 bg-gray-300 dark:bg-zinc-600 border border-white text-black dark:text-white hover:bg-gray-400 dark:hover:bg-zinc-700 transition-transform hover:-translate-y-1"
                        >
                            Creator Account
                        </Button>
                    </div>
                );
            case 2:
                return (
                    <div className="grid grid-cols-2 gap-4 w-full h-full p-4">
                        <Button
                            onClick={() => handleNextStep(3)}
                            className="h-52 bg-gray-300 dark:bg-zinc-600 text-black dark:text-white hover:bg-gray-400 dark:hover:bg-zinc-700 transition-transform hover:-translate-y-1"
                        >
                            Email/Password
                        </Button>

                        <Button
                            onClick={() => handleNextStep(4, undefined, true)}
                            className="h-52 bg-gray-300 dark:bg-zinc-600 text-black dark:text-white hover:bg-gray-400 dark:hover:bg-zinc-700 transition-transform hover:-translate-y-1"
                        >
                            Web3 Wallet
                        </Button>
                    </div>
                );
            case 3:
                return (
                    <div className="w-full">
                        {error && <p className="text-red-500 text-center mb-4">{error}</p>}
                        <SignupForm onSuccess={handleSignupSuccess} userType={userType} isCreator={userType === "creator"} />
                    </div>
                );
            case 4:
                return (
                    <div className="w-full">
                        {error && <p className="text-red-500 text-center mb-4">{error}</p>}
                        <Wallets authType="signup" userType={userType} onClose={onClose} />
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <AnimatedModal
            title="Sign Up"
            open={isOpen}
            onOpenChange={onClose}
            size="2xl"
            closeButton={false}
            header={null}
            footer={
                <div className="w-full flex flex-col items-center gap-4">
                    {step === 3 || step === 4 ? (
                        <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                            Already have an account?{" "}
                            <button
                                onClick={handleOpenLoginPopup}
                                className="text-turquoise underline"
                            >
                                Log in here
                            </button>
                        </p>
                    ) : null}

                    <div className="flex gap-2 w-full">
                        <Button
                            variant="outline"
                            onClick={onClose}
                            className="text-white dark:text-white hover:text-white bg-red-500 hover:bg-red-600 w-full"
                        >
                            Cancel
                        </Button>
                        {step > 1 && (
                            <Button
                                onClick={handleBackStep}
                                disabled={isButtonsDisabled}
                                variant="outline"
                                className="text-black dark:text-white bg-gray-300 dark:bg-zinc-600 hover:bg-gray-400 dark:hover:bg-zinc-700 hover:text-white w-full"
                            >
                                Back
                            </Button>
                        )}
                    </div>
                </div>
            }
        >
            <div className="flex flex-col items-center justify-center gap-3 rounded-lg">
                <div className="w-full flex items-center justify-center">
                    {renderStepContent()}
                </div>
            </div>
        </AnimatedModal>
    );
};
export default SignUpDialog;