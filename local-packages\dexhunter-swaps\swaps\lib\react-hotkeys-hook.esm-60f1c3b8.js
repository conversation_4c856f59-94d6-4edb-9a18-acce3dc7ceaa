import { useRef as K, use<PERSON><PERSON>back as F, useContext as B, useLayoutEffect as W, useEffect as $, createContext as O } from "react";
import "react/jsx-runtime";
function P() {
  return P = Object.assign ? Object.assign.bind() : function(e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = arguments[t];
      for (var o in n)
        Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
    }
    return e;
  }, P.apply(this, arguments);
}
var _ = ["shift", "alt", "meta", "mod", "ctrl"], z = {
  esc: "escape",
  return: "enter",
  ".": "period",
  ",": "comma",
  "-": "slash",
  " ": "space",
  "`": "backquote",
  "#": "backslash",
  "+": "bracketright",
  ShiftLeft: "shift",
  ShiftRight: "shift",
  AltLeft: "alt",
  AltRight: "alt",
  MetaLeft: "meta",
  MetaRight: "meta",
  OSLeft: "meta",
  OSRight: "meta",
  ControlLeft: "ctrl",
  ControlRight: "ctrl"
};
function l(e) {
  return (z[e] || e).trim().toLowerCase().replace(/key|digit|numpad|arrow/, "");
}
function G(e) {
  return _.includes(e);
}
function H(e, t) {
  return t === void 0 && (t = ","), e.split(t);
}
function A(e, t, n) {
  t === void 0 && (t = "+");
  var o = e.toLocaleLowerCase().split(t).map(function(a) {
    return l(a);
  }), u = {
    alt: o.includes("alt"),
    ctrl: o.includes("ctrl") || o.includes("control"),
    shift: o.includes("shift"),
    meta: o.includes("meta"),
    mod: o.includes("mod")
  }, d = o.filter(function(a) {
    return !_.includes(a);
  });
  return P({}, u, {
    keys: d,
    description: n
  });
}
(function() {
  typeof document < "u" && (document.addEventListener("keydown", function(e) {
    e.key !== void 0 && M([l(e.key), l(e.code)]);
  }), document.addEventListener("keyup", function(e) {
    e.key !== void 0 && D([l(e.key), l(e.code)]);
  })), typeof window < "u" && window.addEventListener("blur", function() {
    c.clear();
  });
})();
var c = /* @__PURE__ */ new Set();
function S(e) {
  return Array.isArray(e);
}
function J(e, t) {
  t === void 0 && (t = ",");
  var n = S(e) ? e : e.split(t);
  return n.every(function(o) {
    return c.has(o.trim().toLowerCase());
  });
}
function M(e) {
  var t = Array.isArray(e) ? e : [e];
  c.has("meta") && c.forEach(function(n) {
    return !G(n) && c.delete(n.toLowerCase());
  }), t.forEach(function(n) {
    return c.add(n.toLowerCase());
  });
}
function D(e) {
  var t = Array.isArray(e) ? e : [e];
  e === "meta" ? c.clear() : t.forEach(function(n) {
    return c.delete(n.toLowerCase());
  });
}
function Q(e, t, n) {
  (typeof n == "function" && n(e, t) || n === !0) && e.preventDefault();
}
function T(e, t, n) {
  return typeof n == "function" ? n(e, t) : n === !0 || n === void 0;
}
function U(e) {
  return I(e, ["input", "textarea", "select"]);
}
function I(e, t) {
  var n = e.target;
  t === void 0 && (t = !1);
  var o = n && n.tagName;
  return S(t) ? !!(o && t && t.some(function(u) {
    return u.toLowerCase() === o.toLowerCase();
  })) : !!(o && t && t === !0);
}
function V(e, t) {
  return e.length === 0 && t ? (console.warn('A hotkey has the "scopes" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'), !0) : t ? e.some(function(n) {
    return t.includes(n);
  }) || e.includes("*") : !0;
}
var X = function(t, n, o) {
  o === void 0 && (o = !1);
  var u = n.alt, d = n.meta, a = n.mod, y = n.shift, m = n.ctrl, f = n.keys, h = t.key, r = t.code, w = t.ctrlKey, k = t.metaKey, v = t.shiftKey, E = t.altKey, b = l(r), s = h.toLowerCase();
  if (!o) {
    if (u === !E && s !== "alt" || y === !v && s !== "shift")
      return !1;
    if (a) {
      if (!k && !w)
        return !1;
    } else if (d === !k && s !== "meta" && s !== "os" || m === !w && s !== "ctrl" && s !== "control")
      return !1;
  }
  return f && f.length === 1 && (f.includes(s) || f.includes(b)) ? !0 : f ? J(f) : !f;
}, Y = /* @__PURE__ */ O(void 0), Z = function() {
  return B(Y);
};
function q(e, t) {
  return e && t && typeof e == "object" && typeof t == "object" ? Object.keys(e).length === Object.keys(t).length && //@ts-ignore
  Object.keys(e).reduce(function(n, o) {
    return n && q(e[o], t[o]);
  }, !0) : e === t;
}
var ee = /* @__PURE__ */ O({
  hotkeys: [],
  enabledScopes: [],
  toggleScope: function() {
  },
  enableScope: function() {
  },
  disableScope: function() {
  }
}), te = function() {
  return B(ee);
};
function re(e) {
  var t = K(void 0);
  return q(t.current, e) || (t.current = e), t.current;
}
var x = function(t) {
  t.stopPropagation(), t.preventDefault(), t.stopImmediatePropagation();
}, ne = typeof window < "u" ? W : $;
function ue(e, t, n, o) {
  var u = K(null), d = K(!1), a = n instanceof Array ? o instanceof Array ? void 0 : o : n, y = S(e) ? e.join(a == null ? void 0 : a.splitKey) : e, m = n instanceof Array ? n : o instanceof Array ? o : void 0, f = F(t, m ?? []), h = K(f);
  m ? h.current = f : h.current = t;
  var r = re(a), w = te(), k = w.enabledScopes, v = Z();
  return ne(function() {
    if (!((r == null ? void 0 : r.enabled) === !1 || !V(k, r == null ? void 0 : r.scopes))) {
      var E = function(i, L) {
        var j;
        if (L === void 0 && (L = !1), !(U(i) && !I(i, r == null ? void 0 : r.enableOnFormTags)) && !(r != null && r.ignoreEventWhen != null && r.ignoreEventWhen(i))) {
          if (u.current !== null && document.activeElement !== u.current && !u.current.contains(document.activeElement)) {
            x(i);
            return;
          }
          (j = i.target) != null && j.isContentEditable && !(r != null && r.enableOnContentEditable) || H(y, r == null ? void 0 : r.splitKey).forEach(function(N) {
            var R, g = A(N, r == null ? void 0 : r.combinationKey);
            if (X(i, g, r == null ? void 0 : r.ignoreModifiers) || (R = g.keys) != null && R.includes("*")) {
              if (L && d.current)
                return;
              if (Q(i, g, r == null ? void 0 : r.preventDefault), !T(i, g, r == null ? void 0 : r.enabled)) {
                x(i);
                return;
              }
              h.current(i, g), L || (d.current = !0);
            }
          });
        }
      }, b = function(i) {
        i.key !== void 0 && (M(l(i.code)), ((r == null ? void 0 : r.keydown) === void 0 && (r == null ? void 0 : r.keyup) !== !0 || r != null && r.keydown) && E(i));
      }, s = function(i) {
        i.key !== void 0 && (D(l(i.code)), d.current = !1, r != null && r.keyup && E(i, !0));
      }, C = u.current || (a == null ? void 0 : a.document) || document;
      return C.addEventListener("keyup", s), C.addEventListener("keydown", b), v && H(y, r == null ? void 0 : r.splitKey).forEach(function(p) {
        return v.addHotkey(A(p, r == null ? void 0 : r.combinationKey, r == null ? void 0 : r.description));
      }), function() {
        C.removeEventListener("keyup", s), C.removeEventListener("keydown", b), v && H(y, r == null ? void 0 : r.splitKey).forEach(function(p) {
          return v.removeHotkey(A(p, r == null ? void 0 : r.combinationKey, r == null ? void 0 : r.description));
        });
      };
    }
  }, [y, r, k]), u;
}
export {
  ue as u
};
