import { defaultSettingsProps } from './swap/page';
import { orderTypesProps } from './store/createSwapSlice';
import { supportedTokensType } from './swap/components/tokens';
import { SelectedWallet } from './typescript/cardano-api';
type HEX = `#${string}`;
type CUSTOME_COLORS = 'mainText' | 'subText' | 'background' | 'containers' | 'buttonText' | 'accent';
type colorsProps = {
    [key in CUSTOME_COLORS]?: HEX;
};
interface AppProps {
    defaultToken?: string;
    width?: string;
    height?: string;
    theme?: 'light' | 'dark';
    className?: string;
    style?: object;
    orderTypes?: orderTypesProps;
    supportedTokens?: supportedTokensType;
    partnerName: string;
    partnerCode: string;
    colors?: colorsProps;
    onSwapSuccess?: (data: any) => void;
    onSwapError?: (err: any) => void;
    selectedWallet?: SelectedWallet;
    inputs?: string[];
    onWalletConnect?: (data: any) => void;
    onClickWalletConnect?: () => void;
    onViewOrder?: (data: any) => void;
    displayType?: 'BUTTON' | 'DEFAULT' | 'WIDGET';
    buttonText?: string;
    orderTypeOnButtonClick?: 'SWAP' | 'LIMIT' | 'DCA';
    buttonStyle?: object;
    buttonClassName?: string;
    widgetButtonClass?: object;
    defaultSettings?: defaultSettingsProps;
    autoFocus?: boolean;
}
declare function Swap({ defaultToken, width, height, theme, className, style, orderTypes, supportedTokens, partnerName, partnerCode, colors, onSwapSuccess, onSwapError, selectedWallet, inputs, onWalletConnect, onClickWalletConnect, onViewOrder, displayType, buttonText, orderTypeOnButtonClick, buttonStyle, buttonClassName, widgetButtonClass, defaultSettings, autoFocus }: AppProps): import("react/jsx-runtime").JSX.Element;
export default Swap;
