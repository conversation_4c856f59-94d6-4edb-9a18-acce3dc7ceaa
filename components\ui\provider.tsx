import { ChakraProvider } from "@chakra-ui/react";
import { PropsWithChildren } from "react";

export const Provider: React.FC<PropsWithChildren<{}>> = ({ children, ...props }) => {
  return (
    <ChakraProvider
      {...props}
      resetCSS={false}
      disableGlobalStyle
      theme={{
        config: {
          useSystemColorMode: false,
          initialColorMode: 'light',
        },
      }}
    >
      {children}
    </ChakraProvider>
  );
};
