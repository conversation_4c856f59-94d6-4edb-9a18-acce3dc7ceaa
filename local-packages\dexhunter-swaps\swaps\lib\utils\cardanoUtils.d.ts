import { CardanoApi } from '../typescript/cardano-api';
export declare const fromHex: (hex: string) => Buffer;
export declare const toHex: (bytes: any) => string;
export declare const addressToBech32: (addressRaw: string) => string;
export declare const parseAddressesToBech32: (addresses: string[]) => string[];
export declare const addressFromBech32: (bech32Address: string) => string;
export declare const hexToAscii: (hexString: string) => string;
export declare const getBalance: (addresses: string[]) => Promise<any>;
export declare const getWalletBalance: (api: CardanoApi) => Promise<string>;
export declare const middlen: (address: string | null, length?: number) => string;
export declare const middlenBetween: (str: any, n: number) => any;
export declare const updateSearchParams: (key: string, value: string) => string;
