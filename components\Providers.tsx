'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import dynamic from 'next/dynamic';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '@/redux/store';
import { PostHogProvider } from 'posthog-js/react';
import posthog from 'posthog-js';
import { ThemeProvider } from 'next-themes';
import { MeshProvider } from "@meshsdk/react";
import { WagmiProvider } from 'wagmi';
import { config } from '../config';
import { WalletProvider } from "@/context/Wallet/WalletContext";
//import { SolWalletProvider } from '@/context/Wallet/SolWalletContext';
import { ChatVisibilityProvider } from '@/context/Chat/ChatContext';
import { NotificationProvider } from '@/context/NotificationContext';
import { Provider as ChakraProvider } from '@/components/ui/provider';
import { createSystem, defaultConfig, defineConfig } from '@chakra-ui/react';
import { PrimeReactProvider } from 'primereact/api';
import { MantineProvider } from "@mantine/core";
import { NetworkId, WalletId, WalletManager, WalletProvider as AlgoWalletProvider } from '@txnlab/use-wallet-react';
import { useState, useEffect, useMemo } from "react";
import AOS from 'aos';
import VeChainProvider from './VeChainProvider';
import { SocketProvider } from '@/context/SocketContext';
import { ToastContainer, cssTransition } from 'react-toastify';
import 'animate.css';
import { ModalProvider } from '@/components/ui/modal-context';
import { ConvexClientProvider } from '@/components/providers/ConvexClientProvider';
import { UserStatusProvider } from '@/context/UserStatusContext';

// Move DAppKitProvider to a separate client component
/* const VeChainDAppProvider = dynamic(
    () => import('./VeChainDAppProvider'),
    { ssr: false }
); */

// Dynamically import CookieBanner with SSR disabled
const CookieBanner = dynamic(() => import('./CookieBanner'), {
    ssr: false
});

export default function Providers({ children }: { children: React.ReactNode }) {
    const [isDarkTheme, setIsDarkTheme] = useState(false);
    const queryClient = new QueryClient();

    const value = {
        ripple: true,
    };

    useEffect(() => {
        // Check system/user preference for dark mode
        const darkModePreference = window.matchMedia('(prefers-color-scheme: dark)').matches;
        setIsDarkTheme(darkModePreference);

        // Initialize PostHog
        if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_POSTHOG_KEY) {
            posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
                api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://eu.i.posthog.com',
                person_profiles: 'identified_only',
                loaded: (posthog) => {
                    if (process.env.NODE_ENV === 'development') posthog.debug();
                },
            });
        }
    }, []);

    const fade = cssTransition({
        enter: 'animate__animated animate__fadeIn',
        exit: 'animate__animated animate__fadeOut',
    });

    const customConfig = defineConfig({
        ...defaultConfig,
        colorScheme: isDarkTheme ? 'dark' : 'light',
        disableLayers: true,
        cssReset: false,
        disableGlobalStyle: true,
        cssVarsRoot: ':root',
        layers: {
            reset: 'false',
            base: 'false',
            tokens: 'true',
            recipes: 'true',
        },
        globalCss: {
            "*": {
                "margin": "revert-layer",
                "padding": "revert-layer",
                "font": "revert-layer",
                "border": "revert-layer",
            }
        },
    });

    const system = createSystem(customConfig);

    const walletManager = new WalletManager({
        wallets: [
            WalletId.DEFLY,
            WalletId.PERA,
            WalletId.EXODUS,
            {
                id: WalletId.LUTE,
                options: { siteName: 'Sugar Club' }
            }
        ],
        network: NetworkId.MAINNET
    });

    useEffect(() => {
        AOS.init();

        return () => {
            window.scrollTo(0, 0);
        };
    }, []);

    return (
        <>
            <Provider store={store}>
                <ConvexClientProvider>
                    <UserStatusProvider>
                        <PersistGate loading={null} persistor={persistor}>
                            <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
                                <ModalProvider>
                                    <PostHogProvider client={posthog}>
                                        <ChakraProvider value={system} resetCSS={false} disableInlineStyles>
                                            <MantineProvider withCssVariables={false}>
                                                <PrimeReactProvider value={value}>
                                                    <AlgoWalletProvider manager={walletManager}>
                                                        <VeChainProvider>
                                                            <MeshProvider>
                                                                <WagmiProvider config={config}>
                                                                    {/* <VeChainDAppProvider isDarkTheme={isDarkTheme}> */}
                                                                    <QueryClientProvider client={queryClient}>
                                                                        {/* <SolWalletProvider> */}
                                                                        <WalletProvider>
                                                                            <ChatVisibilityProvider>
                                                                                <NotificationProvider>
                                                                                    <SocketProvider>
                                                                                        {children}
                                                                                    </SocketProvider>
                                                                                </NotificationProvider>
                                                                            </ChatVisibilityProvider>
                                                                        </WalletProvider>
                                                                        {/* </SolWalletProvider> */}
                                                                    </QueryClientProvider>
                                                                    {/* </VeChainDAppProvider> */}
                                                                </WagmiProvider>
                                                            </MeshProvider>
                                                        </VeChainProvider>
                                                    </AlgoWalletProvider>
                                                </PrimeReactProvider>
                                            </MantineProvider>
                                        </ChakraProvider>
                                    </PostHogProvider>
                                </ModalProvider>
                            </ThemeProvider>
                        </PersistGate>
                    </UserStatusProvider>
                </ConvexClientProvider>
            </Provider>

            <ToastContainer
                position="bottom-left"
                autoClose={5000}
                style={{ zIndex: 100001 }}
                icon={false}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick={true}
                rtl={false}
                draggable={true}
                pauseOnHover={true}
                theme="colored"
                transition={fade}
            />

            {/* Render CookieBanner client-side only */}
            <CookieBanner />
        </>
    );
};