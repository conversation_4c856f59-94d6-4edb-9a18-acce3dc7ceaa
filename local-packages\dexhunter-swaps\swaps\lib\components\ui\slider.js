import { jsxs as me, jsx as k } from "react/jsx-runtime";
import * as X from "react";
import { forwardRef as _, useState as z, useRef as D, createElement as p, useMemo as $e, useEffect as Q } from "react";
import { a as R, _ as P } from "../../index-1c873780.js";
import { a as pe, $ as K } from "../../index-c7156e07.js";
import { $ as he } from "../../index-563d1ed8.js";
import { $ as Se } from "../../index-1fe761a6.js";
import { $ as be } from "../../index-6460524a.js";
import { $ as ve } from "../../index-bcfeaad9.js";
import { $ as N } from "../../index-c8f2666b.js";
import { $ as ge } from "../../index-bf605d8a.js";
import { cn as G } from "../../lib.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../extend-tailwind-merge-e63b2b56.js";
function Z(e, [t, n]) {
  return Math.min(n, Math.max(t, e));
}
const ee = [
  "PageUp",
  "PageDown"
], te = [
  "ArrowUp",
  "ArrowDown",
  "ArrowLeft",
  "ArrowRight"
], ne = {
  "from-left": [
    "Home",
    "PageDown",
    "ArrowDown",
    "ArrowLeft"
  ],
  "from-right": [
    "Home",
    "PageDown",
    "ArrowDown",
    "ArrowRight"
  ],
  "from-bottom": [
    "Home",
    "PageDown",
    "ArrowDown",
    "ArrowLeft"
  ],
  "from-top": [
    "Home",
    "PageDown",
    "ArrowUp",
    "ArrowLeft"
  ]
}, T = "Slider", [L, xe, we] = ge(T), [oe, dt] = he(T, [
  we
]), [ye, O] = oe(T), Pe = /* @__PURE__ */ _((e, t) => {
  const { name: n, min: o = 0, max: r = 100, step: a = 1, orientation: c = "horizontal", disabled: i = !1, minStepsBetweenThumbs: d = 0, defaultValue: f = [
    o
  ], value: $, onValueChange: s = () => {
  }, onValueCommit: l = () => {
  }, inverted: x = !1, ...b } = e, [h, S] = z(null), w = R(
    t,
    (m) => S(m)
  ), M = D(/* @__PURE__ */ new Set()), u = D(0), y = c === "horizontal", V = h ? !!h.closest("form") : !0, U = y ? De : _e, [v = [], de] = pe({
    prop: $,
    defaultProp: f,
    onChange: (m) => {
      var g;
      (g = [
        ...M.current
      ][u.current]) === null || g === void 0 || g.focus(), s(m);
    }
  }), Y = D(v);
  function le(m) {
    const g = Te(v, m);
    I(m, g);
  }
  function fe(m) {
    I(m, u.current);
  }
  function ue() {
    const m = Y.current[u.current];
    v[u.current] !== m && l(v);
  }
  function I(m, g, { commit: B } = {
    commit: !1
  }) {
    const j = Ne(a), H = Oe(Math.round((m - o) / a) * a + o, j), A = Z(H, [
      o,
      r
    ]);
    de((C = []) => {
      const E = Ie(C, A, g);
      if (ze(E, d * a)) {
        u.current = E.indexOf(A);
        const W = String(E) !== String(C);
        return W && B && l(E), W ? E : C;
      } else
        return C;
    });
  }
  return /* @__PURE__ */ p(ye, {
    scope: e.__scopeSlider,
    disabled: i,
    min: o,
    max: r,
    valueIndexToChangeRef: u,
    thumbs: M.current,
    values: v,
    orientation: c
  }, /* @__PURE__ */ p(L.Provider, {
    scope: e.__scopeSlider
  }, /* @__PURE__ */ p(L.Slot, {
    scope: e.__scopeSlider
  }, /* @__PURE__ */ p(U, P({
    "aria-disabled": i,
    "data-disabled": i ? "" : void 0
  }, b, {
    ref: w,
    onPointerDown: K(b.onPointerDown, () => {
      i || (Y.current = v);
    }),
    min: o,
    max: r,
    inverted: x,
    onSlideStart: i ? void 0 : le,
    onSlideMove: i ? void 0 : fe,
    onSlideEnd: i ? void 0 : ue,
    onHomeKeyDown: () => !i && I(o, 0, {
      commit: !0
    }),
    onEndKeyDown: () => !i && I(r, v.length - 1, {
      commit: !0
    }),
    onStepKeyDown: ({ event: m, direction: g }) => {
      if (!i) {
        const H = ee.includes(m.key) || m.shiftKey && te.includes(m.key) ? 10 : 1, A = u.current, C = v[A], E = a * H * g;
        I(C + E, A, {
          commit: !0
        });
      }
    }
  })))), V && v.map(
    (m, g) => /* @__PURE__ */ p(Ve, {
      key: g,
      name: n ? n + (v.length > 1 ? "[]" : "") : void 0,
      value: m
    })
  ));
}), [ae, re] = oe(T, {
  startEdge: "left",
  endEdge: "right",
  size: "width",
  direction: 1
}), De = /* @__PURE__ */ _((e, t) => {
  const { min: n, max: o, dir: r, inverted: a, onSlideStart: c, onSlideMove: i, onSlideEnd: d, onStepKeyDown: f, ...$ } = e, [s, l] = z(null), x = R(
    t,
    (u) => l(u)
  ), b = D(), h = Se(r), S = h === "ltr", w = S && !a || !S && a;
  function M(u) {
    const y = b.current || s.getBoundingClientRect(), V = [
      0,
      y.width
    ], v = F(V, w ? [
      n,
      o
    ] : [
      o,
      n
    ]);
    return b.current = y, v(u - y.left);
  }
  return /* @__PURE__ */ p(ae, {
    scope: e.__scopeSlider,
    startEdge: w ? "left" : "right",
    endEdge: w ? "right" : "left",
    direction: w ? 1 : -1,
    size: "width"
  }, /* @__PURE__ */ p(se, P({
    dir: h,
    "data-orientation": "horizontal"
  }, $, {
    ref: x,
    style: {
      ...$.style,
      "--radix-slider-thumb-transform": "translateX(-50%)"
    },
    onSlideStart: (u) => {
      const y = M(u.clientX);
      c == null || c(y);
    },
    onSlideMove: (u) => {
      const y = M(u.clientX);
      i == null || i(y);
    },
    onSlideEnd: () => {
      b.current = void 0, d == null || d();
    },
    onStepKeyDown: (u) => {
      const V = ne[w ? "from-left" : "from-right"].includes(u.key);
      f == null || f({
        event: u,
        direction: V ? -1 : 1
      });
    }
  })));
}), _e = /* @__PURE__ */ _((e, t) => {
  const { min: n, max: o, inverted: r, onSlideStart: a, onSlideMove: c, onSlideEnd: i, onStepKeyDown: d, ...f } = e, $ = D(null), s = R(t, $), l = D(), x = !r;
  function b(h) {
    const S = l.current || $.current.getBoundingClientRect(), w = [
      0,
      S.height
    ], u = F(w, x ? [
      o,
      n
    ] : [
      n,
      o
    ]);
    return l.current = S, u(h - S.top);
  }
  return /* @__PURE__ */ p(ae, {
    scope: e.__scopeSlider,
    startEdge: x ? "bottom" : "top",
    endEdge: x ? "top" : "bottom",
    size: "height",
    direction: x ? 1 : -1
  }, /* @__PURE__ */ p(se, P({
    "data-orientation": "vertical"
  }, f, {
    ref: s,
    style: {
      ...f.style,
      "--radix-slider-thumb-transform": "translateY(50%)"
    },
    onSlideStart: (h) => {
      const S = b(h.clientY);
      a == null || a(S);
    },
    onSlideMove: (h) => {
      const S = b(h.clientY);
      c == null || c(S);
    },
    onSlideEnd: () => {
      l.current = void 0, i == null || i();
    },
    onStepKeyDown: (h) => {
      const w = ne[x ? "from-bottom" : "from-top"].includes(h.key);
      d == null || d({
        event: h,
        direction: w ? -1 : 1
      });
    }
  })));
}), se = /* @__PURE__ */ _((e, t) => {
  const { __scopeSlider: n, onSlideStart: o, onSlideMove: r, onSlideEnd: a, onHomeKeyDown: c, onEndKeyDown: i, onStepKeyDown: d, ...f } = e, $ = O(T, n);
  return /* @__PURE__ */ p(N.span, P({}, f, {
    ref: t,
    onKeyDown: K(e.onKeyDown, (s) => {
      s.key === "Home" ? (c(s), s.preventDefault()) : s.key === "End" ? (i(s), s.preventDefault()) : ee.concat(te).includes(s.key) && (d(s), s.preventDefault());
    }),
    onPointerDown: K(e.onPointerDown, (s) => {
      const l = s.target;
      l.setPointerCapture(s.pointerId), s.preventDefault(), $.thumbs.has(l) ? l.focus() : o(s);
    }),
    onPointerMove: K(e.onPointerMove, (s) => {
      s.target.hasPointerCapture(s.pointerId) && r(s);
    }),
    onPointerUp: K(e.onPointerUp, (s) => {
      const l = s.target;
      l.hasPointerCapture(s.pointerId) && (l.releasePointerCapture(s.pointerId), a(s));
    })
  }));
}), Ee = "SliderTrack", Me = /* @__PURE__ */ _((e, t) => {
  const { __scopeSlider: n, ...o } = e, r = O(Ee, n);
  return /* @__PURE__ */ p(N.span, P({
    "data-disabled": r.disabled ? "" : void 0,
    "data-orientation": r.orientation
  }, o, {
    ref: t
  }));
}), q = "SliderRange", Ce = /* @__PURE__ */ _((e, t) => {
  const { __scopeSlider: n, ...o } = e, r = O(q, n), a = re(q, n), c = D(null), i = R(t, c), d = r.values.length, f = r.values.map(
    (l) => ie(l, r.min, r.max)
  ), $ = d > 1 ? Math.min(...f) : 0, s = 100 - Math.max(...f);
  return /* @__PURE__ */ p(N.span, P({
    "data-orientation": r.orientation,
    "data-disabled": r.disabled ? "" : void 0
  }, o, {
    ref: i,
    style: {
      ...e.style,
      [a.startEdge]: $ + "%",
      [a.endEdge]: s + "%"
    }
  }));
}), J = "SliderThumb", Ke = /* @__PURE__ */ _((e, t) => {
  const n = xe(e.__scopeSlider), [o, r] = z(null), a = R(
    t,
    (i) => r(i)
  ), c = $e(
    () => o ? n().findIndex(
      (i) => i.ref.current === o
    ) : -1,
    [
      n,
      o
    ]
  );
  return /* @__PURE__ */ p(Re, P({}, e, {
    ref: a,
    index: c
  }));
}), Re = /* @__PURE__ */ _((e, t) => {
  const { __scopeSlider: n, index: o, ...r } = e, a = O(J, n), c = re(J, n), [i, d] = z(null), f = R(
    t,
    (S) => d(S)
  ), $ = ve(i), s = a.values[o], l = s === void 0 ? 0 : ie(s, a.min, a.max), x = Ae(o, a.values.length), b = $ == null ? void 0 : $[c.size], h = b ? Be(b, l, c.direction) : 0;
  return Q(() => {
    if (i)
      return a.thumbs.add(i), () => {
        a.thumbs.delete(i);
      };
  }, [
    i,
    a.thumbs
  ]), /* @__PURE__ */ p("span", {
    style: {
      transform: "var(--radix-slider-thumb-transform)",
      position: "absolute",
      [c.startEdge]: `calc(${l}% + ${h}px)`
    }
  }, /* @__PURE__ */ p(L.ItemSlot, {
    scope: e.__scopeSlider
  }, /* @__PURE__ */ p(N.span, P({
    role: "slider",
    "aria-label": e["aria-label"] || x,
    "aria-valuemin": a.min,
    "aria-valuenow": s,
    "aria-valuemax": a.max,
    "aria-orientation": a.orientation,
    "data-orientation": a.orientation,
    "data-disabled": a.disabled ? "" : void 0,
    tabIndex: a.disabled ? void 0 : 0
  }, r, {
    ref: f,
    style: s === void 0 ? {
      display: "none"
    } : e.style,
    onFocus: K(e.onFocus, () => {
      a.valueIndexToChangeRef.current = o;
    })
  }))));
}), Ve = (e) => {
  const { value: t, ...n } = e, o = D(null), r = be(t);
  return Q(() => {
    const a = o.current, c = window.HTMLInputElement.prototype, d = Object.getOwnPropertyDescriptor(c, "value").set;
    if (r !== t && d) {
      const f = new Event("input", {
        bubbles: !0
      });
      d.call(a, t), a.dispatchEvent(f);
    }
  }, [
    r,
    t
  ]), /* @__PURE__ */ p("input", P({
    style: {
      display: "none"
    }
  }, n, {
    ref: o,
    defaultValue: t
  }));
};
function Ie(e = [], t, n) {
  const o = [
    ...e
  ];
  return o[n] = t, o.sort(
    (r, a) => r - a
  );
}
function ie(e, t, n) {
  const a = 100 / (n - t) * (e - t);
  return Z(a, [
    0,
    100
  ]);
}
function Ae(e, t) {
  return t > 2 ? `Value ${e + 1} of ${t}` : t === 2 ? [
    "Minimum",
    "Maximum"
  ][e] : void 0;
}
function Te(e, t) {
  if (e.length === 1)
    return 0;
  const n = e.map(
    (r) => Math.abs(r - t)
  ), o = Math.min(...n);
  return n.indexOf(o);
}
function Be(e, t, n) {
  const o = e / 2, a = F([
    0,
    50
  ], [
    0,
    o
  ]);
  return (o - a(t) * n) * n;
}
function ke(e) {
  return e.slice(0, -1).map(
    (t, n) => e[n + 1] - t
  );
}
function ze(e, t) {
  if (t > 0) {
    const n = ke(e);
    return Math.min(...n) >= t;
  }
  return !0;
}
function F(e, t) {
  return (n) => {
    if (e[0] === e[1] || t[0] === t[1])
      return t[0];
    const o = (t[1] - t[0]) / (e[1] - e[0]);
    return t[0] + o * (n - e[0]);
  };
}
function Ne(e) {
  return (String(e).split(".")[1] || "").length;
}
function Oe(e, t) {
  const n = Math.pow(10, t);
  return Math.round(e * n) / n;
}
const ce = Pe, He = Me, Le = Ce, Fe = Ke;
function Ue(e, t, n) {
  return e <= t ? t : e >= n ? n : e;
}
function Ye(e, t, n) {
  const o = je(e, t, n), r = We(11, o, 1);
  return `calc(${o}% + ${r}px - 2px)`;
}
function je(e, t, n) {
  const a = 100 / (n - t) * (e - t);
  return Ue(a, 0, 100);
}
function We(e, t, n) {
  const o = e / 2, a = Xe([0, 50], [0, o]);
  return (o - a(t) * n) * n;
}
function Xe(e, t) {
  return (n) => {
    if (e[0] === e[1] || t[0] === t[1])
      return t[0];
    const o = (t[1] - t[0]) / (e[1] - e[0]);
    return t[0] + o * (n - e[0]);
  };
}
const Ge = X.forwardRef(({ className: e, ...t }, n) => {
  const o = t.value || t.defaultValue, { min: r, max: a, step: c } = t, i = X.useMemo(() => {
    const d = (a - r) / c + 1;
    return Array.from({ length: d }, (f, $) => r + $ * c);
  }, [a, r, c]);
  return /* @__PURE__ */ me(
    ce,
    {
      ref: n,
      className: G(
        "dhs-relative dhs-flex dhs-w-full dhs-toucdhs-h-none dhs-select-none dhs-items-center dhs-px-1",
        e
      ),
      ...t,
      children: [
        i.map((d, f) => /* @__PURE__ */ k(
          "span",
          {
            className: G(
              "dhs-absolute dhs-h-[5px] dhs-w-[0px] dhs-rounded-full dhs-z-10",
              d > o ? "dhs-bg-subText" : "dhs-bg-accent"
            ),
            style: {
              left: Ye(d, r, a)
            }
          },
          f
        )),
        /* @__PURE__ */ k(He, { className: "dhs-relative dhs-h-[2px] dhs-w-full grow overflodhs-w-hidden dhs-rounded-full dhs-bg-gray-103", children: /* @__PURE__ */ k(Le, { className: "dhs-absolute dhs-h-full dhs-bg-accent" }) }),
        /* @__PURE__ */ k(Fe, { className: "dhs-relative dhs-z-20 dhs-block dhs-h-[15px] dhs-w-[11px] dhs-rounded-[3px] dhs-border-none dhs-bg-accent dhs-ring-offset-background dhs-transition-colors focus-visible:dhs-outline-none focus-visible:dhs-ring-2 focus-visible:dhs-ring-ring focus-visible:dhs-ring-offset-2 disabled:dhs-pointer-events-none disabled:dhs-opacity-50" })
      ]
    }
  );
});
Ge.displayName = ce.displayName;
export {
  Ge as Slider
};
