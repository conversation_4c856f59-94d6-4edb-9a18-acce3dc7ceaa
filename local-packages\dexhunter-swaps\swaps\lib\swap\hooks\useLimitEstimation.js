import m from "../../store/useStore.js";
import { server as b } from "../../config/axios.js";
import { roundNumber as k, getBonusOutput as x } from "../../utils/formatNumber.js";
import { findHighestLiquidity as T } from "../../utils/formatToken.js";
import "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../axios-ddd885c5.js";
import "../../index-ca8eb9e1.js";
const it = () => {
  const {
    tokenSell: s,
    tokenBuy: a,
    sellAmount: n,
    setIsTokenPriceLoading: B,
    setSwapDetails: u,
    setIsSwapDetailsLoading: d,
    setEstimationError: i,
    isTransactionLoading: h,
    inputMode: l,
    buyAmount: p,
    dexBlacklist: A,
    setBuyAmount: c,
    setSellAmount: f,
    setBonusOutput: _,
    limitPrice: N,
    limitMultiples: P
  } = m((e) => e.swapSlice), { defaultBuySize: I } = m((e) => e.globalSettingsSlice), { poolInfo: O } = m((e) => e.chartSlice);
  return { estimateLimit: async ({ signal: e, newTokenSell: $, newTokenBuy: q }) => {
    var L, S, g, w, E, F;
    if (h)
      return !0;
    B(!0), d(!0);
    const r = T(O), y = {
      token_in: (L = $ || s) == null ? void 0 : L.token_id,
      token_out: (S = q || a) == null ? void 0 : S.token_id,
      blacklisted_dexes: A,
      wanted_price: parseFloat(N),
      multiples: P || 1,
      to_split: !(r != null && r.dexName),
      dex: r == null ? void 0 : r.dexName
    };
    try {
      if (l === "SELL" && n && parseFloat(n) > 0) {
        const t = {
          amount_in: parseFloat(n || I),
          ...y
        }, { data: o } = await b.post("/swap/limit/estimate", t, {
          signal: e
        });
        return delete o.total_input, u(o), i(""), c(k(o.total_output)), _(`+${x(o.possible_routes)} ${a == null ? void 0 : a.ticker}`), !0;
      }
      if (l === "BUY" && p && parseFloat(p) > 0) {
        const t = {
          amount_out: parseFloat(p),
          ...y
        }, { data: o } = await b.post("/swap/reverseEstimate", t, {
          signal: e
        });
        return delete o.total_output, u(o), i(""), f(k(o.total_input)), _(`+${x(o.possible_routes)} ${s == null ? void 0 : s.ticker}`), !0;
      }
      parseFloat(n) === 0 && l === "SELL" && (u(null), c(0)), parseFloat(p) === 0 && l === "BUY" && (u(null), f(null)), i("");
    } catch (t) {
      if (console.log(t), t.name === "AbortError")
        return !0;
      ((g = t.response) == null ? void 0 : g.data) === "pool_out_of_sync" && i("Pools Out Of Sync"), ((w = t.response) == null ? void 0 : w.data) === "not_enough_liquidity" && i("Not Enough Liquidity"), ((E = t.response) == null ? void 0 : E.data) === "pool_not_found" && i("Pool Not Found"), ((F = t.response) == null ? void 0 : F.data) === "input_too_small" && i("Input Too Small");
    } finally {
      d(!1);
    }
    return !0;
  } };
};
export {
  it as useEstimateLimit
};
