/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @next/next/no-img-element */
import Image from 'next/image'

const ImageGrid = () => {
  const images = [
    { id: 1, src: 'https://picsum.photos/200', title: 'Image 1', author: 'Author 1' },
    { id: 2, src: 'https://picsum.photos/200', title: 'Image 2', author: 'Author 2' },
    { id: 3, src: 'https://picsum.photos/200', title: 'Image 3', author: 'Author 3' },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {images.map((image) => (
        <div key={image.id} className="bg-gray-900 rounded-lg overflow-hidden group">
          <div className="relative aspect-[4/5]">
            <img
              src={image.src}
              alt={image.title}
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <button className="px-4 py-2 bg-cyan-500 rounded-full text-sm hover:bg-cyan-600 transition-colors">
                View
              </button>
            </div>
          </div>
          <div className="p-4">
            <h3 className="font-semibold mb-1">{image.title}</h3>
            <p className="text-sm text-gray-400">{image.author}</p>
          </div>
        </div>
      ))}
    </div>
  )
}

export default ImageGrid
