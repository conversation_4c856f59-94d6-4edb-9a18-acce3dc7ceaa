import { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  fps: number;
  frameDrops: number;
  averageFrameTime: number;
  scrollVelocity: number;
}

interface PerformanceMonitorOptions {
  targetFPS?: number;
  onPerformanceUpdate?: (metrics: PerformanceMetrics) => void;
  onPerformanceWarning?: (issue: string, metrics: PerformanceMetrics) => void;
  enableLogging?: boolean;
}

export const usePerformanceMonitor = (options: PerformanceMonitorOptions = {}) => {
  const {
    targetFPS = 60,
    onPerformanceUpdate,
    onPerformanceWarning,
    enableLogging = false
  } = options;

  const frameTimesRef = useRef<number[]>([]);
  const lastFrameTimeRef = useRef(performance.now());
  const frameDropsRef = useRef(0);
  const animationFrameRef = useRef<number | undefined>(undefined);
  const metricsRef = useRef<PerformanceMetrics>({
    fps: 60,
    frameDrops: 0,
    averageFrameTime: 16.67,
    scrollVelocity: 0
  });

  const lastScrollPositionRef = useRef(0);
  const lastScrollTimeRef = useRef(Date.now());

  const wasBelow20Ref = useRef(false); // <-- move here

  const measureFrame = useCallback(() => {
    const now = performance.now();
    const frameTime = now - lastFrameTimeRef.current;

    // Store frame times for averaging (keep last 60 frames)
    frameTimesRef.current.push(frameTime);
    if (frameTimesRef.current.length > 60) {
      frameTimesRef.current.shift();
    }

    // Calculate metrics
    const averageFrameTime = frameTimesRef.current.reduce((a, b) => a + b, 0) / frameTimesRef.current.length;
    const currentFPS = Math.round(1000 / averageFrameTime);

    // Detect frame drops (frames taking longer than target)
    const targetFrameTime = 1000 / targetFPS;
    if (frameTime > targetFrameTime * 1.5) {
      frameDropsRef.current++;
    }

    // Update metrics
    metricsRef.current = {
      fps: currentFPS,
      frameDrops: frameDropsRef.current,
      averageFrameTime,
      scrollVelocity: metricsRef.current.scrollVelocity || 0,
    };

    // Performance warnings
    if (currentFPS < targetFPS * 0.8) { // 80% of target FPS
      onPerformanceWarning?.('Low FPS detected', metricsRef.current);
    }

    if (frameDropsRef.current > 10 && frameTimesRef.current.length >= 60) {
      onPerformanceWarning?.('Frequent frame drops detected', metricsRef.current);
      frameDropsRef.current = 0; // Reset counter
    }

    // Callback with current metrics
    onPerformanceUpdate?.(metricsRef.current);

    // Logging
    if (enableLogging) {
      const fps = metricsRef.current.fps;
      if (fps < 20 && !wasBelow20Ref.current) {
        console.log('Performance Metrics:', metricsRef.current);
        wasBelow20Ref.current = true;
      } else if (fps >= 20 && wasBelow20Ref.current) {
        wasBelow20Ref.current = false;
      }
    }

    lastFrameTimeRef.current = now;
    animationFrameRef.current = requestAnimationFrame(measureFrame);
  }, [targetFPS, onPerformanceUpdate, onPerformanceWarning, enableLogging]);

  const trackScrollVelocity = useCallback((scrollPosition: number) => {
    const now = Date.now();
    const distance = Math.abs(scrollPosition - lastScrollPositionRef.current);
    const timeElapsed = now - lastScrollTimeRef.current;

    if (timeElapsed > 0) {
      const velocity = distance / timeElapsed;
      metricsRef.current.scrollVelocity = velocity;
    }

    lastScrollPositionRef.current = scrollPosition;
    lastScrollTimeRef.current = now;
  }, []);

  const startMonitoring = useCallback(() => {
    if (!animationFrameRef.current) {
      animationFrameRef.current = requestAnimationFrame(measureFrame);
    }
  }, [measureFrame]);

  const stopMonitoring = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = undefined;
    }
  }, []);

  const getMetrics = useCallback(() => metricsRef.current, []);

  const resetMetrics = useCallback(() => {
    frameTimesRef.current = [];
    frameDropsRef.current = 0;
    metricsRef.current = {
      fps: 60,
      frameDrops: 0,
      averageFrameTime: 16.67,
      scrollVelocity: 0
    };
  }, []);

  // Auto-start monitoring
  useEffect(() => {
    startMonitoring();
    return stopMonitoring;
  }, [startMonitoring, stopMonitoring]);

  return {
    startMonitoring,
    stopMonitoring,
    trackScrollVelocity,
    getMetrics,
    resetMetrics,
    currentMetrics: metricsRef.current
  };
};

// Hook for scroll-specific performance monitoring
export const useScrollPerformanceMonitor = (options: PerformanceMonitorOptions = {}) => {
  const monitor = usePerformanceMonitor(options);

  const handleScroll = useCallback((event: Event) => {
    const target = event.target as HTMLElement;
    if (target) {
      monitor.trackScrollVelocity(target.scrollTop);
    }
  }, [monitor]);

  const attachScrollListener = useCallback((element: HTMLElement | null) => {
    if (element) {
      element.addEventListener('scroll', handleScroll, { passive: true });
      return () => element.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  return {
    ...monitor,
    attachScrollListener,
    handleScroll
  };
};