import fetch from 'node-fetch';

const resetMessageCount = async (baseUrl) => {
    try {
        const headers = new Headers();
        headers.append('Authorization', `Bearer ${process.env.NEXTAUTH_SECRET}`);
        headers.append('Content-Type', 'application/json');
        const response = await fetch(`${baseUrl}/api/reset-member-message-count`, {
            method: 'POST',
            headers,
            body: JSON.stringify({}),
        });

        if (!response.ok) {
            throw new Error('Failed to reset message counts');
        }

    } catch (err) {
        console.error('Failed to reset message counts:', err);
        throw err;
    }
};

export default async (req, res) => {
    try {
        const { headers } = req;
        const authorizationHeader = headers['authorization'];
        const secretToken = process.env.NEXTAUTH_SECRET;

        if (authorizationHeader !== `Bearer ${secretToken}`) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }

        const baseUrl = req.headers.host.startsWith('localhost')
            ? 'http://localhost:3000'
            : 'https://chilledkongs.com';

        resetMessageCount(baseUrl);
        res.status(200).json({ message: 'Scheduled.' });
    } catch (error) {
        console.error('Error scheduling:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};