import { jsx as C } from "react/jsx-runtime";
import { memo as t } from "react";
const e = (o) => /* @__PURE__ */ C(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 8 5",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M7.6364 1.6364C7.98787 1.28493 7.98787 0.715076 7.6364 0.363602C7.28493 0.0121284 6.71508 0.0121278 6.3636 0.363601L7.6364 1.6364ZM3.3636 3.3636C3.01213 3.71508 3.01213 4.28493 3.3636 4.6364C3.71508 4.98787 4.28493 4.98787 4.6364 4.6364L3.3636 3.3636ZM1.6364 0.363601C1.28493 0.0121281 0.715075 0.0121281 0.363601 0.363601C0.0121281 0.715075 0.0121281 1.28493 0.363601 1.6364L1.6364 0.363601ZM3.35897 4.63176C3.71044 4.98324 4.28029 4.98324 4.63176 4.63176C4.98324 4.28029 4.98324 3.71044 4.63176 3.35897L3.35897 4.63176ZM7 1C6.3636 0.363601 6.36359 0.363608 6.36358 0.363623C6.36356 0.363639 6.36354 0.363661 6.36351 0.363692C6.36345 0.363752 6.36336 0.363843 6.36324 0.363964C6.363 0.364206 6.36263 0.364568 6.36215 0.365049C6.36119 0.366011 6.35976 0.367448 6.35785 0.369348C6.35405 0.37315 6.3484 0.378807 6.34097 0.386228C6.32613 0.40107 6.30423 0.42297 6.276 0.451201C6.21954 0.507662 6.13776 0.589444 6.03647 0.69073C5.8339 0.893301 5.55331 1.17389 5.24126 1.48594C4.61714 2.11006 3.86714 2.86006 3.3636 3.3636L4.6364 4.6364C5.13994 4.13286 5.88994 3.38286 6.51405 2.75874C6.82611 2.44668 7.1067 2.1661 7.30927 1.96353C7.41055 1.86224 7.49234 1.78046 7.5488 1.724C7.57703 1.69577 7.59893 1.67387 7.61377 1.65903C7.62119 1.65161 7.62685 1.64595 7.63065 1.64215C7.63255 1.64025 7.63399 1.63881 7.63495 1.63785C7.63543 1.63737 7.63579 1.637 7.63603 1.63676C7.63616 1.63664 7.63625 1.63655 7.63631 1.63649C7.63634 1.63646 7.63636 1.63644 7.63638 1.63642C7.63639 1.63641 7.6364 1.6364 7 1ZM0.363601 1.6364L3.35897 4.63176L4.63176 3.35897L1.6364 0.363601L0.363601 1.6364Z",
        fill: "currentColor"
      }
    )
  }
), n = t(e);
export {
  n as default
};
