'use client';

import React, { memo, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Play } from 'lucide-react';
import { Video, Smartphone } from "lucide-react";
import NextImage from 'next/image';

interface Video {
  id: string;
  thumbnail: string;
  title: string;
  duration: string;
  views?: number;
  isPaid?: boolean;
  price?: string;
}

interface VideoGridItemProps {
  video: Video;
  onView: (video: Video) => void;
  isClip?: boolean;
}

const VideoGridItem = memo(({
  video,
  onView,
  isClip
}: VideoGridItemProps) => {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <div className="bg-transparent overflow-hidden group cursor-pointer rounded-lg aspect-[1]" onClick={() => onView(video)}>
      <Skeleton loading={isLoading} width="100%" height="100%" borderRadius="6px" aspectRatio={1}>
        {video.isPaid && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-[1]">
            <span className="bg-black/70 text-white text-xs px-2 py-1 rounded">
              {video.price || 'Premium'}
            </span>
          </div>
        )}

        {isClip && <div className="flex items-center justify-center gap-1 absolute top-1 left-1 bg-black/70 text-white text-xs px-2 py-1 rounded-md z-[1]">
          <Smartphone className="h-4 w-4 dark:text-white" />  1
        </div>}

        {!isClip && <div className="flex items-center justify-center gap-1 absolute top-1 left-1 bg-black/70 text-white text-xs px-2 pt-[3px] pb-[1px] rounded-md z-[1]">
          {isClip ? <Smartphone className="h-4 w-4 dark:text-white" /> : <Video className="h-auto w-[22px] relative -top-[1px] dark:text-white" />} 1
        </div>}

        <NextImage
          src={video.thumbnail || '/images/user/default-avatar.webp'}
          alt={video.title}
          width={600}
          height={600}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          loading="lazy"
          placeholder="empty"
          onLoad={() => setIsLoading(false)}
        />

        {/* Duration badge */}
        <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-2 py-1 rounded-md z-[1]">
          <span>{video.duration}</span>
        </div>

        {/* Play button overlay */}
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center z-[1] rounded-lg">
          <div className="relative">
            <div className="absolute inset-0 bg-turquoise rounded-full opacity-30 animate-ping-slow"></div>
            <button
              className="relative w-12 h-12 bg-turquoise rounded-full flex items-center justify-center hover:bg-turquoise-hover transition-colors"
            >
              <Play className="h-6 w-6 text-white fill-white ml-1" />
            </button>
          </div>
        </div>
      </Skeleton>
    </div>
  );
}, (prevProps, nextProps) =>
  prevProps.video.id === nextProps.video.id
);
export default VideoGridItem;