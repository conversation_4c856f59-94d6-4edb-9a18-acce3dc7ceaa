import { toast } from 'react-toastify';
import NotificationMessage from './NotificationMessage'; // Import the custom component

const toaster = (props, toastProps) =>
  toast(<NotificationMessage {...props} />, { ...toastProps });

toaster.success = (props, toastProps) =>
  toast.success(<NotificationMessage {...props} />, { ...toastProps });

toaster.error = (props, toastProps) =>
  toast.error(<NotificationMessage {...props} />, { ...toastProps });

toaster.info = (props, toastProps) =>
  toast.info(<NotificationMessage {...props} />, { ...toastProps });

toaster.warning = (props, toastProps) =>
  toast.warning(<NotificationMessage {...props} />, { ...toastProps });

export default toaster;