const i = (e) => e.charAt(0).toUpperCase() + e.slice(1), n = (e) => {
  if (!e)
    return null;
  let s = e[0];
  for (const r of e)
    parseInt(r.token1Amount) > parseInt(s.token1Amount) && (s = r);
  return s;
}, t = (e) => {
  if (!e)
    return null;
  let s = e[0].pool;
  for (const r of e)
    parseInt(r.pool.amount_1) > parseInt(s.amount_1) && (s = r.pool);
  return s.dex_name;
}, f = (e) => e < 100 ? e / 100 * 5 : e < 500 ? 5 + e / 500 * 5 : e < 1e3 ? 10 + e / 1e3 * 5 : e < 2500 ? 15 + e / 2500 * 10 : e < 5e3 ? 25 + e / 5e3 * 15 : e < 1e4 ? 40 + e / 1e4 * 15 : e < 25e3 ? 55 + e / 25e3 * 15 : e < 5e4 ? 70 + e / 5e4 * 15 : 85 + e / 1e5 * 15, p = (e, s) => ["3m", "5m", "30m", "1h"].includes(s) ? e <= 5 ? 20 : e <= 10 ? 20 + e / 40 * 20 : e <= 15 ? 40 + e / 60 * 20 : e <= 25 ? 60 + e / 80 * 20 : 80 + e / 50 * 20 : e <= 25 ? 20 : e <= 50 ? 20 + e / 100 * 20 : e <= 100 ? 40 + e / 150 * 20 : e <= 150 ? 60 + e / 200 * 20 : 80 + e / 250 * 20;
function c(e) {
  const s = e.match(/\.(\d{6,})/);
  return s && s[1].length > 6 ? e.replace(/\.(\d{3})(\d+)(\d{4})$/, (...r) => `.${r[1]}..${r[3]}`) : e;
}
function u(e) {
  return e ? e.length > 6 ? `${e.slice(0, 2)}..${e.slice(-2)}` : e : "";
}
export {
  i as capitalize,
  n as findHighestLiquidity,
  t as findHighestLiquidityChart,
  u as formatTokenName,
  c as formatTokenValue,
  p as getTradeAmountWidth,
  f as progressBarFill
};
