'use client';

import { useState, useEffect, useRef, useCallback, useMemo, memo } from 'react';
import { useUser } from '@/hooks/useUser';
import { useInfiniteQuery } from '@tanstack/react-query';
import { SelectDropdown } from '@/components/ui/select-dropdown';
import { LayoutGrid, PanelBottom, Eye, Bookmark, Share2, Lock, Users as UsersIcon } from 'lucide-react';
import Tippy from '@tippyjs/react';
import { Post } from '@/types/post';
import { Virtuoso, VirtuosoGrid } from 'react-virtuoso';
import { Profile } from '@/types/user';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import PostCard from '@/components/social/PostCard';
import GridPostCard from '@/components/social/GridPostCard';
import MediaLightbox from '@/components/social/MediaLightbox';
import { toast } from 'react-toastify';
import CreatorSpotlight from './CreatorSpotlight';
import SearchBar from './SearchBar';
import IntroVideos from './IntroVideos';
//import Series from './Series';
import LuckyDip from './LuckyDip';
import { IoMdMale, IoMdFemale, IoMdTransgender } from 'react-icons/io';
import {
  useLikePost,
  useSavePost,
  useDeletePost,
  useReportPost,
  useCreateComment,
  useTrackPostView,
} from '@/hooks/convexHooks';
import { throttle } from 'lodash';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

interface HomeContentProps {
  initialProfiles: Profile[];
  initialPosts: Post[] | null | undefined;
}

// Base filter options
const baseFilterOptions = [
  { value: 'recently-added', label: 'Recently Added' },
  { value: 'most-viewed', label: 'Most Viewed' },
  { value: 'most-liked', label: 'Most Liked' },
];

// Additional filter options for media content
const mediaFilterOptions = [
  { value: 'highest-price', label: 'Highest Priced' },
  { value: 'lowest-price', label: 'Lowest Priced' },
  { value: 'free', label: 'Free' },
  { value: 'longest', label: 'Longest' },
  { value: 'trending', label: 'Trending' },
];

// Panel options
const panelOptions = [
  { value: 'all-posts', label: 'All Posts' },
  { value: 'videos', label: 'Videos' },
  { value: 'photos', label: 'Photos' },
  { value: 'audio', label: 'Audio' },
  { value: 'clips', label: 'Clips' },
  { value: 'gifs', label: 'GIFs' },
];

const genderOptions = [
  {
    value: 'female',
    label: 'Female',
    icon: <IoMdFemale className="w-4 h-4" />
  },
  {
    value: 'male',
    label: 'Male',
    icon: <IoMdMale className="w-4 h-4" />
  },
  {
    value: 'trans',
    label: 'Trans',
    icon: <IoMdTransgender className="w-5 h-5" />
  },
  {
    value: 'couples',
    label: 'Couples',
    icon: <UsersIcon className="w-4 h-4" />
  },
];

// Skeleton for Post View
const PostSkeleton = () => (
  <div className="py-4 mb-4">
    <div className="flex items-center gap-4 mb-4">
      <Skeleton className="h-12 w-12 !rounded-full" />
      <div className="space-y-2 flex-1">
        <Skeleton className="h-4 w-[200px]" />
        <Skeleton className="h-4 w-[150px]" />
      </div>
    </div>
    <Skeleton className="h-4 w-full mb-2" />
    <Skeleton className="h-4 w-3/4 mb-4" />
    <Skeleton className="h-[300px] w-full mb-4 rounded-lg" />
    <div className="flex justify-between">
      <Skeleton className="h-8 w-[100px]" />
      <Skeleton className="h-8 w-[100px]" />
      <Skeleton className="h-8 w-[100px]" />
    </div>
  </div>
);

// Skeleton for Grid View
const GridSkeleton = () => (
  <Skeleton className="w-full h-full rounded-md aspect-[1]" />
);

// PostItem definition (move here)
interface PostItemProps {
  post: Post;
  index: number;
  viewMode: 'grid' | 'post';
  onEditPost: (postId: string, updatedPost: any) => void;
  onOpenLightbox: (post: Post, mediaArray: any[], initialIndex: number) => void;
  onDeletePost: (postId: string) => void;
  onLike: (postId: string) => void;
  onSave: (post: Post) => void;
  onShare: (postId: string) => void;
  onReport: (postId: string, reason: string) => void;
  panel?: string;
  isLiked: boolean;
  likeCount: number;
  isSaved: boolean;
  saveCount: number;
}

const PostItem = memo(
  ({
    post,
    index,
    viewMode,
    onEditPost,
    onOpenLightbox,
    onDeletePost,
    onLike,
    onSave,
    onShare,
    onReport,
    panel,
    isLiked,
    likeCount,
    isSaved,
    saveCount,
  }: PostItemProps) => {
    const uniqueKey = post.id || `post-${index}`;
    return (
      <div key={uniqueKey}>
        {viewMode === 'post' ? (
          <PostCard
            post={post}
            isLiked={isLiked}
            likeCount={likeCount}
            isSaved={isSaved}
            saveCount={saveCount}
            onPostUpdated={(postId, updatedPost) => onEditPost(postId, updatedPost)}
            onPostDeleted={() => onDeletePost(post.id)}
            onOpenLightbox={onOpenLightbox}
            onLike={onLike}
            onSave={onSave}
            onShare={onShare}
            onReport={onReport}
          />
        ) : (
          <GridPostCard
            post={post}
            onOpenLightbox={onOpenLightbox}
            panel={panel}
          />
        )}
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.post.id === nextProps.post.id &&
      prevProps.viewMode === nextProps.viewMode &&
      prevProps.panel === nextProps.panel &&
      prevProps.isLiked === nextProps.isLiked &&
      prevProps.likeCount === nextProps.likeCount &&
      prevProps.isSaved === nextProps.isSaved &&
      prevProps.saveCount === nextProps.saveCount &&
      prevProps.post.content === nextProps.post.content &&
      prevProps.post.likes === nextProps.post.likes &&
      prevProps.post.saves === nextProps.post.saves &&
      prevProps.post.liked === nextProps.post.liked &&
      prevProps.post.saved === nextProps.post.saved &&
      JSON.stringify(prevProps.post.media) === JSON.stringify(nextProps.post.media)
    );
  }
);

const HomeContent = ({ initialProfiles, initialPosts }: HomeContentProps) => {
  const { isAuthenticated, token: userData } = useUser();

  // Local state for posts and postStates
  const [posts, setPosts] = useState<Post[]>(initialPosts || []);
  const [postStates, setPostStates] = useState<Record<string, { isLiked: boolean; localSaved: boolean; localSavedCount: number }>>({});

  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentPost, setCurrentPost] = useState<Post | null>(null);
  const [panel, setPanel] = useState<string>('all-posts');
  const [filter, setFilter] = useState<string>('recently-added');
  const [gender, setGender] = useState<string>('female');
  const [viewMode, setViewMode] = useState<'grid' | 'post'>('post');
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [mediaArray, setMediaArray] = useState<any[]>([]);
  const [initialIndex, setInitialIndex] = useState(0);
  const [page, setPage] = useState(1);

  // Remove useFetchExplorePosts and use Convex useQuery directly
  const explorePosts = useQuery(api.explore.explorePosts, {
    filter,
    page,
    gender: gender || undefined,
    direction: 'forward',
    tag: selectedTag || undefined,
    panel,
    // DO NOT include mediaOnly
  });

  // Loading state
  const isLoading = explorePosts === undefined;
  const isFetching = isLoading;
  // Pagination: isDone means no more pages
  const hasNextPage = explorePosts ? !explorePosts.isDone : false;
  // Make error type any for error?.message
  const error: any = null; // Placeholder for error handling

  // Map posts to ensure contentType is string | undefined
  const convexPosts = (explorePosts?.posts ?? []).map((post: any) => ({
    ...post,
    contentType: post.contentType ?? undefined,
  }));

  // Update posts and postStates when explorePosts changes
  useEffect(() => {
    if (explorePosts?.posts) {
      if (page === 1) {
        setPosts(convexPosts);
      } else {
        setPosts(prevPosts => {
          const existingIds = new Set(prevPosts.map(p => p.id));
          const newPosts = convexPosts.filter((p: any) => !existingIds.has(p.id));
          return [...prevPosts, ...newPosts];
        });
      }
      // Initialize post states for new posts
      const newPostStates: Record<string, { isLiked: boolean; localSaved: boolean; localSavedCount: number }> = {};
      convexPosts.forEach((post: any) => {
        if (!postStates[post.id]) {
          newPostStates[post.id] = { isLiked: post.liked, localSaved: post.saved, localSavedCount: post.saves };
        }
      });
      if (Object.keys(newPostStates).length > 0) {
        setPostStates(prev => ({ ...prev, ...newPostStates }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [explorePosts, page]);

  const fetchNextPage = () => {
    if (hasNextPage && !isLoading) {
      setPage(p => p + 1);
    }
  };

  const refetch = () => {
    setPage(1);
    setPosts([]);
  };

  /* const handleEditPost = useCallback(
    async (postId: string, updatedData: any) => {
      try {
        const response = await fetch(`/api/posts/${postId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedData),
        });
        if (!response.ok) throw new Error('Failed to update post');
        toast.success('Post updated successfully');
        dispatch(setPosts(filteredPosts.map((post: Post) => (post.id === postId ? { ...post, ...updatedData } : post))));
      } catch (err: any) {
        toast.error(`Failed to update post: ${err.message}`);
      }
    },
    [dispatch, filteredPosts]
  ); */

  const likePost = useLikePost();
  const handleLike = useCallback(
    async (postId: string) => {
      try {
        const currentState = postStates[postId] || { isLiked: false, localSaved: false, localSavedCount: 0 };
        const newIsLiked = !currentState.isLiked;
        setPostStates(prev => ({ ...prev, [postId]: { ...currentState, isLiked: newIsLiked } }));
        await likePost({ contentId: postId, contentType: 'post' });
      } catch (err: any) {
        setPostStates(prev => ({ ...prev, [postId]: postStates[postId] }));
        toast.error(`Failed to like post: ${err.message}`);
      }
    },
    [likePost, postStates]
  );

  const savePost = useSavePost();
  const handleSave = useCallback(
    async (post: Post) => {
      try {
        const currentState = postStates[post.id] || { isLiked: false, localSaved: false, localSavedCount: 0 };
        const newLocalSaved = !currentState.localSaved;
        const newSavedCount = newLocalSaved ? currentState.localSavedCount + 1 : currentState.localSavedCount - 1;
        setPostStates(prev => ({ ...prev, [post.id]: { ...currentState, localSaved: newLocalSaved, localSavedCount: newSavedCount } }));
        await savePost({ contentId: post.id, contentType: post?.media?.[0]?.mediaType || '' });
      } catch (err: any) {
        setPostStates(prev => ({ ...prev, [post.id]: postStates[post.id] }));
        toast.error(`Failed to save post: ${err.message}`);
      }
    },
    [savePost, postStates]
  );

  const handleShare = useCallback(
    (postId: string) => {
      const url = `${window.location.origin}/post/${postId}`;
      navigator.clipboard.writeText(url).then(() => {
        toast.success('Link copied to clipboard!');
      }).catch(() => {
        toast.error('Failed to copy link');
      });
    },
    []
  );

  const handleSearchExpand = useCallback((expanded: boolean) => {
    setIsSearchExpanded(expanded);
  }, []);

  const handleViewModeChange = useCallback((newViewMode: 'grid' | 'post') => {
    setViewMode(newViewMode);
  }, []);

  const handleFilterChange = useCallback((newFilter: string) => {
    setFilter(newFilter);
    refetch();
  }, [refetch]);

  const handlePanelChange = useCallback((newPanel: string) => {
    setPanel(newPanel);
    refetch();
  }, [refetch]);

  const handleGenderChange = useCallback((newGender: string) => {
    setGender(newGender);
    refetch();
  }, [refetch]);

  const handleTagSelect = useCallback(
    (tagName: string) => {
      setSelectedTag(tagName);
      setPanel('all-posts');
      setFilter('recently-added');
      setGender('');
      refetch();
    },
    [refetch]
  );

  const getFilterOptions = useCallback(() => {
    switch (panel) {
      case 'videos':
      case 'photos':
      case 'clips':
        return [...baseFilterOptions, ...mediaFilterOptions];
      default:
        return [...baseFilterOptions, { value: 'trending', label: 'Trending' }];
    }
  }, [panel]);

  const throttledFetchNextPage = useCallback(
    throttle(() => {
      if (hasNextPage && !isFetching) {
        fetchNextPage();
      }
    }, 500),
    [hasNextPage, isFetching, fetchNextPage]
  );

  const renderItem = useCallback(
    (index: number, post: Post) => {
      if (!post) return <PostSkeleton />;
      const postState = postStates[post.id] || { isLiked: post.liked, localSaved: post.saved, localSavedCount: post.saves };
      return (
        <PostItem
          post={post}
          index={index}
          viewMode="post"
          onEditPost={() => {}}
          onOpenLightbox={(post: Post, mediaArray: any, initialIndex: number) => {
            setCurrentPost(post);
            setMediaArray(mediaArray);
            setInitialIndex(initialIndex);
            setLightboxOpen(true);
          }}
          onDeletePost={(postId: string) => setPosts(posts.filter((p: Post) => p.id !== postId))}
          onLike={handleLike}
          onSave={handleSave}
          onShare={handleShare}
          onReport={() => { }} // Placeholder for report functionality
          panel={panel}
          isLiked={postState.isLiked}
          likeCount={post.likes || 0}
          isSaved={postState.localSaved}
          saveCount={postState.localSavedCount}
        />
      );
    },
    [handleLike, handleSave, handleShare, panel, postStates, posts]
  );

  const renderGridItem = useCallback(
    (index: number, post: Post) => {
      if (!post) return <GridSkeleton />;
      const postState = postStates[post.id] || { isLiked: false, localSaved: false, localSavedCount: 0 };
      return (
        <PostItem
          post={{
            ...post,
            liked: postState.isLiked,
            saved: postState.localSaved,
            saves: postState.localSavedCount,
          }}
          index={index}
          viewMode="grid"
          onEditPost={() => {}}
          onOpenLightbox={(post: Post, mediaArray: any, initialIndex: number) => {
            setCurrentPost(post);
            setMediaArray(mediaArray);
            setInitialIndex(initialIndex);
            setLightboxOpen(true);
          }}
          onDeletePost={(postId: string) => setPosts(posts.filter((p: Post) => p.id !== postId))}
          onLike={handleLike}
          onSave={handleSave}
          onShare={handleShare}
          onReport={() => { }} // Placeholder for report functionality
          panel={panel}
          isLiked={postState.isLiked}
          likeCount={post.likes || 0}
          isSaved={postState.localSaved}
          saveCount={postState.localSavedCount}
        />
      );
    },
    [handleLike, handleSave, handleShare, panel, postStates, posts]
  );

  // Preload images for upcoming posts
  useEffect(() => {
    const preloadImages = () => {
      posts.slice(0, 10).forEach((post: Post) => {
        if (post.media?.[0]?.mediaUrl) {
          const img = new Image();
          img.src = post.media[0].mediaUrl;
        }
      });
    };
    preloadImages();
  }, [posts]);

  return (
    <div className="container mx-auto px-4 py-4 relative">
      <div className="flex gap-4">
        {/* Left Sidebar */}
        <div className="w-80 flex-shrink-0">
          <div className="sticky top-[120px]">
            <CreatorSpotlight creators={initialProfiles} />
            {/* <Series /> */}
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 max-w-4xl">
          <div className="space-y-4">
            {(error as any) ? (
              <div className="flex items-center justify-center h-64 flex-col gap-4">
                <p className="text-red-500">Error loading content: {error?.message}</p>
                <Button onClick={() => refetch()}>Retry</Button>
              </div>
            ) : (
              <>
                {/* Top bar with search and panel options */}
                <div className="flex items-center justify-between mt-[0px] mb-0.5 z-[2] relative gap-[15px]">
                  <div className={`flex items-center gap-[16px] w-full ${isSearchExpanded ? 'xl:max-w-[74.8%] 2xl:max-w-[81.85%]' : 'max-w-[72.6%]'} transition-[max-width] duration-300 ease-in-out`}>
                    <SelectDropdown
                      value={panel}
                      onChange={handlePanelChange}
                      options={panelOptions}
                      placeholder="Media"
                      fixedPlaceholder="Media: "
                      className="w-full max-w-40"
                      buttonClassName="bg-transparent dark:bg-transparent rounded-full"
                      dropdownClassName="rounded-lg dark:!bg-[#18181b]/80 bg-white/80 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-solid border-[#18181b]/20 dark:border-white/20"
                    />

                    <SelectDropdown
                      value={gender}
                      onChange={handleGenderChange}
                      options={genderOptions}
                      placeholder="Gender"
                      className="w-full max-w-32"
                      buttonClassName="bg-transparent dark:bg-transparent rounded-full"
                      dropdownClassName="rounded-lg dark:!bg-[#18181b]/80 bg-white/80 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-solid border-[#18181b]/20 dark:border-white/20"
                      renderOption={(option) => (
                        <div className="flex items-center gap-1">
                          <span>{option.label}</span>
                          {option.icon}
                        </div>
                      )}
                    />

                    <SearchBar
                      onTagSelect={handleTagSelect}
                      isExpanded={isSearchExpanded}
                      onExpand={handleSearchExpand}
                    />
                  </div>

                  <div className="flex items-center justify-center gap-2">
                    <div className={`flex items-center justify-center gap-[9px] -ml-[2px] rounded-lg p-1 pl-0 transition-all duration-300 ease-in-out ${isSearchExpanded ? 'opacity-0 invisible hidden' : 'opacity-100 visible'}`}>
                      <Tippy
                        content="Post View"
                        animation="shift-toward-subtle"
                        followCursor={false}
                        placement="top"
                        arrow={true}
                        theme="sugar"
                      >
                        <button className="p-0 rounded" onClick={() => handleViewModeChange('post')}>
                          <PanelBottom
                            size={32}
                            className={`${viewMode === 'post'
                              ? 'text-turquoise'
                              : 'text-gorilla-gray dark:text-white'
                              }`}
                          />
                        </button>
                      </Tippy>

                      <Tippy
                        content="Grid View"
                        animation="shift-toward-subtle"
                        followCursor={false}
                        placement="top"
                        arrow={true}
                        theme="sugar"
                      >
                        <button
                          className="p-0 rounded"
                          onClick={() => handleViewModeChange('grid')}
                        >
                          <LayoutGrid
                            size={32.5}
                            className={`${viewMode === 'grid'
                              ? 'text-turquoise'
                              : 'text-gorilla-gray dark:text-white'
                              }`}
                          />
                        </button>
                      </Tippy>
                    </div>

                    <SelectDropdown
                      value={filter}
                      onChange={handleFilterChange}
                      options={getFilterOptions()}
                      className="w-full min-w-[146px]"
                      buttonClassName="bg-transparent dark:bg-transparent rounded-full min-w-[146px] whitespace-nowrap"
                      dropdownClassName="rounded-lg dark:!bg-[#18181b]/80 bg-white/80 backdrop-blur dark:divide-dark-3 dark:bg-dark-2 border border-solid border-[#18181b]/20 dark:border-white/20"
                    />
                  </div>
                </div>

                {/* Display selected tag */}
                {selectedTag && (
                  <div className="flex items-center gap-2 mb-4 w-full">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      Selected tag: <span className="w-auto inline-flex items-center justify-center rounded-full px-4 py-1 bg-gray-400 dark:bg-white text-white dark:text-[#18181b] ml-2">#{selectedTag}</span>
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedTag(null);
                        setPanel('all-posts');
                        setFilter('recently-added');
                        setGender('female');
                        refetch();
                      }}
                      className="bg-red-500 dark:bg-red-500 text-white dark:text-white rounded-full px-4 py-1 ml-2"
                    >
                      Clear
                    </Button>
                  </div>
                )}

                {/* Content Feed */}
                <div className="w-full max-w-[1200px] mx-auto">
                  {isLoading && posts.length === 0 ? (
                    <div className={viewMode === 'grid' ? 'grid grid-cols-2 md:grid-cols-3 gap-4' : 'space-y-4'}>
                      {Array.from({ length: viewMode === 'grid' ? 6 : 2 }).map((_, index) => (
                        viewMode === 'grid' ? <GridSkeleton key={index} /> : <PostSkeleton key={index} />
                      ))}
                    </div>
                  ) : posts.length > 0 ? (
                    viewMode === 'grid' ? (
                      <VirtuosoGrid
                        data={posts}
                        itemContent={(index, post) => renderGridItem(index, post)}
                        endReached={() => {
                          if (hasNextPage && !isFetching) throttledFetchNextPage();
                        }}
                        overscan={800}
                        style={{ width: '100%' }}
                        useWindowScroll
                        listClassName="grid grid-cols-2 md:grid-cols-3 gap-2"
                      />
                    ) : (
                      <Virtuoso
                        data={posts}
                        itemContent={(index, post) => renderItem(index, post)}
                        endReached={() => {
                          if (hasNextPage && !isFetching) throttledFetchNextPage();
                        }}
                        overscan={800}
                        style={{ width: '100%' }}
                        useWindowScroll
                      />
                    )
                  ) : (
                    <div className="p-8 text-center">
                      <p className="text-gray-500 dark:text-gray-400 text-2xl font-bold uppercase">
                        {selectedTag ? `No posts found for #${selectedTag}` : 'No posts found'}
                      </p>
                    </div>
                  )}
                </div>

                {currentPost && (
                  <MediaLightbox
                    open={lightboxOpen}
                    onOpenChange={setLightboxOpen}
                    post={currentPost}
                    mediaArray={mediaArray}
                    initialIndex={initialIndex}
                  />
                )}
              </>
            )}
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="w-80 flex-shrink-0">
          <div className="sticky top-[120px]">
            <IntroVideos />
            <LuckyDip users={[]} />
          </div>
        </div>
      </div>
    </div>
  );
};
export default memo(HomeContent);