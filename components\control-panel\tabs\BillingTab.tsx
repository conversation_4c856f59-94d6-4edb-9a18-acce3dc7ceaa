'use client';

import { motion } from 'framer-motion';

const BillingTab = () => {
  return (
    <motion.div
      key="billing"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
    >
      <h2 className="text-xl font-semibold text-white mb-4">Billing</h2>
      <input type="text" placeholder="Billing Address" className="w-full bg-gray-700 text-white p-2 rounded mb-2" />
      <input type="text" placeholder="Postal Code" className="w-full bg-gray-700 text-white p-2 rounded" />
    </motion.div>
  );
};

export default BillingTab;