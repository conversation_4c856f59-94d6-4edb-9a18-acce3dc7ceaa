'use client';

import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Search, X, Clock, Trash2 } from 'lucide-react';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useDebounce } from '@/hooks/useDebounce';

// Constants
const RECENT_SEARCHES_KEY = 'recent-creator-searches';
const MAX_RECENT_SEARCHES = 8;

interface SearchResult {
  id: string;
  type: 'creator';
  username: string;
  display_name: string;
  profilePhoto?: string;
  is_verified: boolean;
}

interface RecentSearch {
  query: string;
  timestamp: number;
  creator?: {
    id: string;
    type: 'creator';
    username: string;
    display_name: string;
    profilePhoto?: string;
    is_verified: boolean;
  };
}

const SearchBar = () => {
  const router = useRouter();
  const [query, setQuery] = useState('');
  const [isVerifiedBadgeLoading, setIsVerifiedBadgeLoading] = useState(true);
  const [showDropdown, setShowDropdown] = useState(false);
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);
  const [showRecentSearches, setShowRecentSearches] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounce the search query
  const debouncedQuery = useDebounce(query, 800);

  // Use Convex query for search
  const searchResults = useQuery(
    api.users.searchUsers,
    debouncedQuery && debouncedQuery.length >= 1 // changed from 3 to 1
      ? {
        query: debouncedQuery,
        limit: 15,
        creatorType: 'creator'
      }
      : 'skip'
  );

  // Add a search to recent searches
  const addToRecentSearches = useCallback((searchQuery: string, creator?: SearchResult) => {
    if (!searchQuery || !searchQuery.trim()) return;
    const trimmedQuery = searchQuery.trim();

    setRecentSearches(prev => {
      // Remove the query if it already exists
      const filtered = prev.filter(item => item.query.trim().toLowerCase() !== trimmedQuery.toLowerCase());

      // Add the new query at the beginning
      const newSearches = [
        {
          query: trimmedQuery,
          timestamp: Date.now(),
          creator: creator ? {
            id: creator.id,
            type: creator.type,
            username: creator.username,
            display_name: creator.display_name,
            profilePhoto: creator.profilePhoto,
            is_verified: creator.is_verified
          } : undefined
        },
        ...filtered
      ].slice(0, MAX_RECENT_SEARCHES);

      return newSearches;
    });
  }, []);

  // Remove a specific recent search
  const removeRecentSearch = useCallback((searchQuery: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation(); // Prevent triggering the parent button click
    }

    setRecentSearches(prev =>
      prev.filter(item => item.query.trim().toLowerCase() !== searchQuery.trim().toLowerCase())
    );
  }, []);

  // Clear all recent searches
  const clearAllRecentSearches = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setRecentSearches([]);
    try {
      localStorage.removeItem(RECENT_SEARCHES_KEY);
    } catch (error) {
      console.error('Failed to clear recent searches:', error);
    }
  }, []);

  const handleResultClick = (result: SearchResult) => {

    // Add to recent searches when a creator is selected
    addToRecentSearches(result.display_name, result);

    setShowDropdown(false);
    setQuery('');
    router.push(`/user/${result.username}`);
  };

  const handleRecentSearchClick = (recentSearch: RecentSearch) => {
    // If we have creator info, navigate directly to their profile
    if (recentSearch.creator) {
      // Move this search to the top of recent searches
      addToRecentSearches(recentSearch.query, recentSearch.creator);

      // Navigate to creator profile
      router.push(`/user/${recentSearch.creator.username}`);
      setShowDropdown(false);
      setQuery('');
    } else {
      // Otherwise, perform the search with the query
      setQuery(recentSearch.query);
      setShowDropdown(true);
      setShowRecentSearches(false);

      // Move this search to the top of recent searches
      addToRecentSearches(recentSearch.query);
    }
  };

  // Load recent searches from localStorage on component mount
  useEffect(() => {
    try {
      const storedSearches = localStorage.getItem(RECENT_SEARCHES_KEY);
      if (storedSearches) {
        const parsedSearches = JSON.parse(storedSearches) as RecentSearch[];
        setRecentSearches(parsedSearches);
      }
    } catch (error) {
      console.error('Failed to load recent searches:', error);
    }
  }, []);

  // Save recent searches to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(recentSearches));
    } catch (error) {
      console.error('Failed to save recent searches:', error);
    }
  }, [recentSearches]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Extract results from the Convex query
  const results = searchResults?.data || [];

  return (
    <div className="relative w-full z-10" ref={dropdownRef}>
      <div className="w-full flex items-center justify-end space-x-2 gap-0">
        <div className="relative w-full">
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => {
              const newQuery = e.target.value;
              setQuery(newQuery);
              setShowDropdown(true);
              setShowRecentSearches(false);

              // Show recent searches if query is cleared
              if (newQuery === '' && recentSearches.length > 0) {
                setShowRecentSearches(true);
              }
            }}
            onFocus={() => {
              setShowDropdown(true);
              // Show recent searches if we have any and query is empty
              if (recentSearches.length > 0 && !query) {
                setShowRecentSearches(true);
              }
            }}
            placeholder="Search Creators..."
            className="flex h-10 w-full bg-transparent dark:bg-transparent placeholder:text-sm text-gorilla-gray dark:text-white border border-solid border-gray-300 dark:border-white rounded-full px-4 py-2 focus:outline-none focus:ring-0 focus:ring-transparent focus:ring-offset-0 focus:ring-offset-background"
          />

          {query ? (
            <button
              onClick={() => {
                setQuery('');
                // Show recent searches when clearing the input
                if (recentSearches.length > 0) {
                  setShowRecentSearches(true);
                }
              }}
              className="absolute h-10 right-7 top-0 text-gorilla-gray hover:text-gray-600 transition-colors border border-solid border-gray-300 dark:border-white border-r-0 border-t-0 border-b-0 pl-6"
            >
              <X className="h-5 w-5" />
            </button>
          ) : (
            <button
              className="absolute h-10 right-7 top-0 text-gorilla-gray hover:text-gray-600 transition-colors border border-solid border-gray-300 dark:border-white border-r-0 border-t-0 border-b-0 pl-6"
            >
              <Search className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {showDropdown && (
        <div className="absolute z-10 w-full mt-1 bg-white/80 dark:bg-[#18181b]/80 backdrop-blur shadow-lg border border-[#18181b]/20 dark:border-white/20 max-h-60 overflow-auto">
          <div className="max-h-[300px] overflow-y-auto select-dropdown-content">
            {/* Recent Searches Section */}
            {showRecentSearches && recentSearches.length > 0 && !query && (
              <div className="p-1">
                <div className="px-2 py-1.5 flex items-center justify-between">
                  <div className="text-xs text-gorilla-gray flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Recent Searches
                  </div>
                  <button
                    onClick={clearAllRecentSearches}
                    className="text-xs text-gorilla-gray hover:text-red-500 flex items-center gap-1"
                  >
                    <Trash2 className="h-3 w-3" />
                    Clear All
                  </button>
                </div>

                {recentSearches.map((item) => (
                  <a
                    key={`${item.query}-${item.timestamp}`}
                    onClick={() => handleRecentSearchClick(item)}
                    className="w-full px-2 py-2 hover:bg-accent hover:text-accent-foreground flex items-center justify-between transition-colors cursor-pointer border border-solid border-gray-300 dark:border-white border-t-0 border-l-0 border-r-0 last:border-b-0"
                  >
                    <div className="flex items-center gap-2">
                      <Skeleton loading={false} width="36px" height="36px" className="rounded-full border border-solid border-gray-300 dark:border-white">
                        <Image
                          src={item?.creator?.profilePhoto || '/images/user/default-avatar.webp'}
                          alt={item?.creator?.username}
                          width={36}
                          height={36}
                          className="rounded-full border border-solid border-gray-300 dark:border-white"
                        />
                      </Skeleton>

                      <div className="text-sm font-medium">{item.query} {item.creator?.is_verified && <Skeleton loading={isVerifiedBadgeLoading} width="16px" height="16px" className="inline-block">
                        <img className="w-4 h-4 user-post-verified-badge" alt="Verified User" src="/images/user/verified.png" onError={() => setIsVerifiedBadgeLoading(false)} onLoad={() => setIsVerifiedBadgeLoading(false)} />
                      </Skeleton>}</div>
                    </div>
                    <button
                      onClick={(e) => removeRecentSearch(item.query, e)}
                      className="text-muted-foreground hover:text-red-500"
                    >
                      <X className="h-3.5 w-3.5" />
                    </button>
                  </a>
                ))}
              </div>
            )}

            {/* Search Results Section */}
            {query.length > 0 && (
              <>
                {searchResults === undefined ? (
                  <div className="flex items-center justify-center p-4">
                    <div className="top-search-bar-loader" />
                  </div>
                ) : results.length > 0 ? (
                  <div className="p-1">
                    {results.map((creator: any) => (
                      <button
                        key={creator.id}
                        onClick={() => handleResultClick(creator)}
                        className="w-full px-2 py-2 hover:bg-accent hover:text-accent-foreground rounded-none flex items-center justify-between transition-colors border border-solid border-gray-300 dark:border-white border-t-0 border-l-0 border-r-0 last:border-b-0"
                      >
                        {/* Profile photo, display name, and username */}
                        <div className="w-1/2 flex items-center gap-2">
                          <Image
                            src={creator.profilePhoto || '/images/user/default-avatar.webp'}
                            alt={creator.username}
                            width={36}
                            height={36}
                            className="rounded-full border border-solid border-gray-300 dark:border-white"
                          />
                          <div className="flex flex-col gap-0 text-left w-full items-start justify-center">
                            <div className="flex gap-1 text-sm font-medium">{creator.display_name} {creator.is_verified && <Skeleton loading={isVerifiedBadgeLoading} width="16px" height="16px" className="inline-block">
                              <img className="w-4 h-4 user-post-verified-badge" alt="Verified User" src="/images/user/verified.png" onError={() => setIsVerifiedBadgeLoading(false)} onLoad={() => setIsVerifiedBadgeLoading(false)} />
                            </Skeleton>}</div>
                            <div className="text-[11px] text-muted-foreground -mt-[1px]">@{creator.username}</div>
                          </div>
                        </div>

                        {/* Stats */}
                        <div className="w-full ml-auto flex items-center justify-start gap-4 text-muted-foreground dark:text-white">
                          <div className="flex flex-col items-center justify-center gap-0">
                            <span className="text-[12px] font-bold">{creator.stats?.photos ?? 0}</span>
                            <span className="text-[10px] capitalize">Photos</span>
                          </div>
                          <div className="flex flex-col items-center justify-center gap-0">
                            <span className="text-[12px] font-bold">{creator.stats?.videos ?? 0}</span>
                            <span className="text-[10px] capitalize">Videos</span>
                          </div>
                          <div className="flex flex-col items-center justify-center gap-0">
                            <span className="text-[12px] font-bold">{creator.stats?.audio ?? 0}</span>
                            <span className="text-[10px] capitalize">Audio</span>
                          </div>
                          <div className="flex flex-col items-center justify-center gap-0">
                            <span className="text-[12px] font-bold">{creator.stats?.clips ?? 0}</span>
                            <span className="text-[10px] capitalize">Clips</span>
                          </div>
                          <div className="flex flex-col items-center justify-center gap-0">
                            <span className="text-[12px] font-bold">{creator.stats?.gifs ?? 0}</span>
                            <span className="text-[10px] capitalize">GIFs</span>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 text-center text-gorilla-gray dark:text-white">No results found</div>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
export default memo(SearchBar);