import { jsx as t, jsxs as m } from "react/jsx-runtime";
import { Sheet as p, Sheet<PERSON>ontent as e, SheetClose as s } from "./components/ui/sheet-connect-wallet.js";
import d from "./components/common/WalletPopover.js";
import h from "./store/useStore.js";
import l from "./assets/svg/IconRemove.js";
import "react";
import "./index-840f2930.js";
import "./index-1c873780.js";
import "./index-c7156e07.js";
import "./index-563d1ed8.js";
import "./index-4914f99c.js";
import "./index-67500cd3.js";
import "./index-c8f2666b.js";
import "./_commonjsHelpers-10dfc225.js";
import "./index-27cadef5.js";
import "./index-5116e957.js";
import "./index-1d6812f7.js";
import "./extend-tailwind-merge-e63b2b56.js";
import "./lib.js";
import "./components/ui/checkbox.js";
import "./index-6460524a.js";
import "./index-bcfeaad9.js";
import "./createLucideIcon-7a477fa6.js";
import "./hooks/useWalletConnect.js";
import "./utils/cardanoUtils.js";
import "./index-ca8eb9e1.js";
import "./config/axios.js";
import "./axios-ddd885c5.js";
import "./lib/utils.js";
import "./constants/wallets.js";
import "./store/createTokenSearchSlice.js";
import "./immer-548168ec.js";
import "./store/createWalletSlice.js";
import "./store/createSwapSettingsSlice.js";
import "./store/createGlobalSettingsSlice.js";
import "./store/createUserOrdersSlice.js";
import "./store/createSwapSlice.js";
import "./store/createChartSlice.js";
import "./store/createBasketSlice.js";
import "./swap/components/tokens.js";
import "./store/createModalWhatsNewSlice.js";
import "./store/createSwapParamsSlice.js";
const $ = () => {
  const { isOpenWallet: r, setIsOpenWallet: o } = h(
    (i) => i.walletSlice
  );
  return /* @__PURE__ */ t("div", { children: /* @__PURE__ */ t(p, { open: r, onOpenChange: o, children: /* @__PURE__ */ m(e, { side: "bottom", className: "dhs-mx-0 dhs-translate-y-0 dhs-top-0 dhs-bottom-0 dhs-rounded-[26px]", children: [
    /* @__PURE__ */ t(d, { closeOpenWallet: () => o(!1) }),
    /* @__PURE__ */ t(s, { className: "dhs-absolute dhs-flex dhs-justify-center dhs-items-center dhs-right-7 dhs-top-5  dhs-w-8 dhs-h-8 dhs-rounded-xl dhs-bg-containers dhs-cursor-pointer", children: /* @__PURE__ */ t(l, { className: "dhs-text-mainText dhs-flex dhs-w-[12px] dhs-h-[12px]" }) })
  ] }) }) });
};
export {
  $ as default
};
