import w, { useDebugValue as b } from "react";
import { g as P } from "../_commonjsHelpers-10dfc225.js";
import R from "./createTokenSearchSlice.js";
import A from "./createWalletSlice.js";
import V from "./createSwapSettingsSlice.js";
import O from "./createGlobalSettingsSlice.js";
import T from "./createUserOrdersSlice.js";
import L from "./createSwapSlice.js";
import U from "./createChartSlice.js";
import B from "./createBasketSlice.js";
import C from "./createModalWhatsNewSlice.js";
import $ from "./createSwapParamsSlice.js";
import "../immer-548168ec.js";
import "../swap/components/tokens.js";
const y = (e) => {
  let t;
  const r = /* @__PURE__ */ new Set(), o = (u, d) => {
    const c = typeof u == "function" ? u(t) : u;
    if (!Object.is(c, t)) {
      const S = t;
      t = d ?? typeof c != "object" ? c : Object.assign({}, t, c), r.forEach((m) => m(t, S));
    }
  }, n = () => t, l = { setState: o, getState: n, subscribe: (u) => (r.add(u), () => r.delete(u)), destroy: () => {
    r.clear();
  } };
  return t = e(o, n, l), l;
}, j = (e) => e ? y(e) : y;
var g = { exports: {} }, x = {}, D = { exports: {} }, I = {};
/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var p = w;
function W(e, t) {
  return e === t && (e !== 0 || 1 / e === 1 / t) || e !== e && t !== t;
}
var k = typeof Object.is == "function" ? Object.is : W, q = p.useState, F = p.useEffect, M = p.useLayoutEffect, G = p.useDebugValue;
function z(e, t) {
  var r = t(), o = q({ inst: { value: r, getSnapshot: t } }), n = o[0].inst, i = o[1];
  return M(function() {
    n.value = r, n.getSnapshot = t, v(n) && i({ inst: n });
  }, [e, r, t]), F(function() {
    return v(n) && i({ inst: n }), e(function() {
      v(n) && i({ inst: n });
    });
  }, [e]), G(r), r;
}
function v(e) {
  var t = e.getSnapshot;
  e = e.value;
  try {
    var r = t();
    return !k(e, r);
  } catch {
    return !0;
  }
}
function N(e, t) {
  return t();
}
var H = typeof window > "u" || typeof window.document > "u" || typeof window.document.createElement > "u" ? N : z;
I.useSyncExternalStore = p.useSyncExternalStore !== void 0 ? p.useSyncExternalStore : H;
D.exports = I;
var J = D.exports;
/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var E = w, K = J;
function Q(e, t) {
  return e === t && (e !== 0 || 1 / e === 1 / t) || e !== e && t !== t;
}
var X = typeof Object.is == "function" ? Object.is : Q, Y = K.useSyncExternalStore, Z = E.useRef, ee = E.useEffect, te = E.useMemo, re = E.useDebugValue;
x.useSyncExternalStoreWithSelector = function(e, t, r, o, n) {
  var i = Z(null);
  if (i.current === null) {
    var a = { hasValue: !1, value: null };
    i.current = a;
  } else
    a = i.current;
  i = te(function() {
    function u(s) {
      if (!d) {
        if (d = !0, c = s, s = o(s), n !== void 0 && a.hasValue) {
          var f = a.value;
          if (n(f, s))
            return S = f;
        }
        return S = s;
      }
      if (f = S, X(c, s))
        return f;
      var h = o(s);
      return n !== void 0 && n(f, h) ? f : (c = s, S = h);
    }
    var d = !1, c, S, m = r === void 0 ? null : r;
    return [function() {
      return u(t());
    }, m === null ? void 0 : function() {
      return u(m());
    }];
  }, [t, r, o, n]);
  var l = Y(e, i[0], i[1]);
  return ee(function() {
    a.hasValue = !0, a.value = l;
  }, [l]), re(l), l;
};
g.exports = x;
var ne = g.exports;
const oe = /* @__PURE__ */ P(ne), { useSyncExternalStoreWithSelector: ie } = oe;
function ue(e, t = e.getState, r) {
  const o = ie(
    e.subscribe,
    e.getState,
    e.getServerState || e.getState,
    t,
    r
  );
  return b(o), o;
}
const _ = (e) => {
  const t = typeof e == "function" ? j(e) : e, r = (o, n) => ue(t, o, n);
  return Object.assign(r, t), r;
}, ce = (e) => e ? _(e) : _, ge = ce((e) => ({
  tokenSearchSlice: R(e),
  walletSlice: A(e),
  swapSettingsSlice: V(e),
  globalSettingsSlice: O(e),
  userOrdersSlice: T(e),
  swapSlice: L(e),
  chartSlice: U(e),
  modalWhatsNewSlice: C(e),
  basketSlice: B(e),
  swapParamsSlice: $(e)
}));
export {
  ge as default
};
