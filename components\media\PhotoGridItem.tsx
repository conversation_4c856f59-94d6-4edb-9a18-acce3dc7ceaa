'use client';

import React, { memo, useState, useRef, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Lock, Play, Image as ImageIcon } from 'lucide-react';
import { LuAudioLines } from "react-icons/lu";
import { HiOutlineGif } from "react-icons/hi2";
import NextImage from 'next/image';

interface Photo {
  id: string;
  thumbnail: string;
  title: string;
  isPaid?: boolean;
  price?: string;
  views?: number;
}

interface PhotoGridItemProps {
  photo: Photo;
  mediaCount?: number;
  onView: (photo: Photo) => void;
  fileType?: string;
}

function fileTypeIcons(fileType?: string, mediaCount?: number) {
  if (fileType == 'audio') return <div className="flex items-center justify-center gap-1 absolute top-1 left-1 bg-black/70 text-white text-xs px-2 pt-[4px] pb-[3px] rounded-md z-[1]"><LuAudioLines className="h-[16px] w-auto dark:text-white" /> {mediaCount}</div>;
  if (fileType == 'image') return <div className="flex items-center justify-center gap-1 absolute top-1 left-1 bg-black/70 text-white text-xs px-2 py-1 rounded-md z-[1]"><ImageIcon className="h-4 w-4 dark:text-white" /> {mediaCount}</div>;
  if (fileType == 'gif') return <div className="flex items-center justify-center gap-1 absolute top-1 left-1 bg-black/70 text-white text-xs px-2 pt-[3px] pb-[2.2px] rounded-md z-[1]"><HiOutlineGif className="h-auto w-5 dark:text-white" /> {mediaCount}</div>;
}

function getBaseType(fileType?: string): 'audio' | 'video' | 'image' | 'gif' {
  if (!fileType) return 'image';
  if (fileType === 'image/gif' || fileType.endsWith('/gif')) return 'gif';
  if (fileType.startsWith('audio/')) return 'audio';
  if (fileType.startsWith('video/')) return 'video';
  if (fileType.startsWith('image/')) return 'image';
  return 'image';
}

const PhotoGridItem = memo(({
  photo,
  mediaCount,
  onView,
  fileType
}: PhotoGridItemProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [gifThumbUrl, setGifThumbUrl] = useState<string | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Extract first frame for GIFs
  useEffect(() => {
    if (getBaseType(fileType) === 'gif' && photo.thumbnail) {
      const img = new window.Image();
      img.crossOrigin = 'anonymous';
      img.src = photo.thumbnail;
      img.onload = () => {
        if (canvasRef.current) {
          canvasRef.current.width = img.naturalWidth;
          canvasRef.current.height = img.naturalHeight;
          const ctx = canvasRef.current.getContext('2d');
          if (ctx) {
            ctx.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight);
            const dataUrl = canvasRef.current.toDataURL('image/png');
            setGifThumbUrl(dataUrl);
          }
        }
        setIsLoading(false);
      };
      img.onerror = () => setIsLoading(false);
    } else {
      setIsLoading(false);
    }
  }, [fileType, photo.thumbnail]);

  const isGif = getBaseType(fileType) === 'gif';

  return (
    <div className="bg-transparent overflow-hidden group cursor-pointer rounded-md aspect-[1]">
      {photo.isPaid && (
        <div className="absolute inset-0 flex flex-col gap-3 items-center justify-center bg-black/50 z-[1]">
          <Lock className="text-white h-8 w-8" />
          <span className="bg-black/70 text-white text-xs px-2 py-1 rounded">
            {photo.price || 'Premium'}
          </span>
        </div>
      )}

      <Skeleton loading={isLoading} width="100%" height="100%" borderRadius="6px">
        {mediaCount && fileTypeIcons(getBaseType(fileType), mediaCount)}

        {/* For GIFs, show the first frame extracted via canvas; otherwise, show the image */}
        {isGif ? (
          <>
            <canvas ref={canvasRef} style={{ display: 'none' }} />
            {gifThumbUrl && (
              <>
                <NextImage
                  src={gifThumbUrl || '/images/user/default-avatar.webp'}
                  alt={photo.title}
                  width={600}
                  height={600}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  loading="lazy"
                  placeholder="empty"
                  onClick={() => onView(photo)}
                />

                {/* Play button overlay */}
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center z-[1] rounded-lg">
                  <div className="relative">
                    <div className="absolute inset-0 bg-turquoise rounded-full opacity-30 animate-ping-slow"></div>
                    <button
                      className="relative w-12 h-12 bg-turquoise rounded-full flex items-center justify-center hover:bg-turquoise-hover transition-colors"
                    >
                      <Play className="h-6 w-6 text-white fill-white ml-1" />
                    </button>
                  </div>
                </div>
              </>
            )}
          </>
        ) : (
          <NextImage
            src={photo.thumbnail || '/images/user/default-avatar.webp'}
            alt={photo.title}
            width={600}
            height={600}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            loading="lazy"
            placeholder="empty"
            onClick={() => onView(photo)}
            onLoad={() => setIsLoading(false)}
          />
        )}
      </Skeleton>
    </div>
  );
}, (prevProps, nextProps) =>
  prevProps.photo.id === nextProps.photo.id
);
export default PhotoGridItem;