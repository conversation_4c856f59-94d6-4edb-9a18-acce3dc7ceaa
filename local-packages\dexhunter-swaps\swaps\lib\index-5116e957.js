import { Children as $, cloneElement as v, useState as E, useRef as N, useEffect as M, use<PERSON><PERSON>back as O, useReducer as T } from "react";
import { r as h } from "./index-c8f2666b.js";
import { a as U } from "./index-1c873780.js";
import { b as p } from "./index-c7156e07.js";
function I(n, t) {
  return T((r, o) => {
    const i = t[r][o];
    return i ?? r;
  }, n);
}
const P = (n) => {
  const { present: t, children: r } = n, o = S(t), i = typeof r == "function" ? r({
    present: o.isPresent
  }) : $.only(r), c = U(o.ref, i.ref);
  return typeof r == "function" || o.isPresent ? /* @__PURE__ */ v(i, {
    ref: c
  }) : null;
};
P.displayName = "Presence";
function S(n) {
  const [t, r] = E(), o = N({}), i = N(n), c = N("none"), l = n ? "mounted" : "unmounted", [f, a] = I(l, {
    mounted: {
      UNMOUNT: "unmounted",
      ANIMATION_OUT: "unmountSuspended"
    },
    unmountSuspended: {
      MOUNT: "mounted",
      ANIMATION_END: "unmounted"
    },
    unmounted: {
      MOUNT: "mounted"
    }
  });
  return M(() => {
    const e = d(o.current);
    c.current = f === "mounted" ? e : "none";
  }, [
    f
  ]), p(() => {
    const e = o.current, s = i.current;
    if (s !== n) {
      const A = c.current, m = d(e);
      n ? a("MOUNT") : m === "none" || (e == null ? void 0 : e.display) === "none" ? a("UNMOUNT") : a(s && A !== m ? "ANIMATION_OUT" : "UNMOUNT"), i.current = n;
    }
  }, [
    n,
    a
  ]), p(() => {
    if (t) {
      const e = (u) => {
        const m = d(o.current).includes(u.animationName);
        u.target === t && m && h.flushSync(
          () => a("ANIMATION_END")
        );
      }, s = (u) => {
        u.target === t && (c.current = d(o.current));
      };
      return t.addEventListener("animationstart", s), t.addEventListener("animationcancel", e), t.addEventListener("animationend", e), () => {
        t.removeEventListener("animationstart", s), t.removeEventListener("animationcancel", e), t.removeEventListener("animationend", e);
      };
    } else
      a("ANIMATION_END");
  }, [
    t,
    a
  ]), {
    isPresent: [
      "mounted",
      "unmountSuspended"
    ].includes(f),
    ref: O((e) => {
      e && (o.current = getComputedStyle(e)), r(e);
    }, [])
  };
}
function d(n) {
  return (n == null ? void 0 : n.animationName) || "none";
}
export {
  P as $
};
