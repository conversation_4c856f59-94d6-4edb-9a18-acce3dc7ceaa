'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Music, Play, Pause, Volume2, VolumeX } from 'lucide-react';
import WaveForm from './WaveForm';

interface AudioPlayerProps {
  audioUrl: string;
  duration?: number | null;
  onLoad?: () => void;
  onError?: () => void;
}

interface AnalyzerData {
  analyser: AnalyserNode;
  bufferLength: number;
  dataArray: Uint8Array;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ audioUrl, duration, onLoad, onError }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [audioDuration, setAudioDuration] = useState<number | null>(duration || null);
  const [hasError, setHasError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [useAudioContext, setUseAudioContext] = useState(true);
  const [analyzerData, setAnalyzerData] = useState<AnalyzerData | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const sourceRef = useRef<MediaElementAudioSourceNode | null>(null);

  const initializeAudioContext = () => {
    if (!audioRef.current || sourceRef.current) return;

    if (sourceRef.current || audioContextRef.current) return;

    console.log('creating media element source', {
      audioSrc: audioRef.current?.src,
      crossOrigin: audioRef.current?.crossOrigin,
    });

    const audioCtx = new (window.AudioContext || window.AudioContext)();
    audioContextRef.current = audioCtx;

    const analyser = audioCtx.createAnalyser();
    analyser.fftSize = 2048;

    const src = audioCtx.createMediaElementSource(audioRef.current);
    src.connect(analyser);
    analyser.connect(audioCtx.destination);

    sourceRef.current = src;
    setAnalyzerData({
      analyser,
      bufferLength: analyser.frequencyBinCount,
      dataArray: new Uint8Array(analyser.frequencyBinCount),
    });
    console.log(sourceRef.current?.mediaElement?.crossOrigin); // should be 'anonymous'
  };

  useEffect(() => {
    if (!audioRef.current) return;

    // Event listeners
    const handleLoadedMetadata = () => {
      if (audioRef.current && !isNaN(audioRef.current.duration)) {
        setAudioDuration(audioRef.current.duration);
        setIsLoading(false);
        onLoad?.();
      } else {
        console.error('Invalid duration:', audioRef.current?.duration);
        setHasError('Failed to load audio metadata');
        setIsLoading(false);
        onError?.();
      }
    };

    const handleCanPlay = () => {
      setIsLoading(false);
      // Attempt to play if user has interacted
      if (isPlaying && audioRef.current) {
        audioRef.current.play().catch((err) => {
          console.error('Auto-play failed:', err);
          setHasError('Playback failed: ' + err.message);
          onError?.();
        });
      }
    };

    const handleError = () => {
      const error = audioRef.current?.error;
      console.error('Audio error:', error);
      let errorMessage = 'Failed to load audio';
      if (error?.code === 4) {
        errorMessage = 'Unsupported audio format. Please upload an MP3, WAV, or OGG file.';
      } else if (error?.code === 3) {
        errorMessage = 'Error decoding audio file.';
      } else if (error?.code === 2) {
        errorMessage = 'Network error: Unable to access audio file.';
      }
      setHasError(errorMessage);
      setIsLoading(false);
      onError?.();
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audioRef.current?.currentTime || 0);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      if (sourceRef.current) {
        sourceRef.current.disconnect();
        sourceRef.current = null;
      }
    };

    audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata);
    audioRef.current.addEventListener('canplay', handleCanPlay);
    audioRef.current.addEventListener('error', handleError);
    audioRef.current.addEventListener('timeupdate', handleTimeUpdate);
    audioRef.current.addEventListener('ended', handleEnded);

    // Timeout to catch loading failures
    const timeout = setTimeout(() => {
      if (isLoading && !hasError && audioRef.current?.readyState < 2) {
        console.error('Audio loading timed out');
        setHasError('Audio failed to load: Timeout');
        setIsLoading(false);
        onError?.();
      }
    }, 10000);

    // Debug audio state
    /* const debugInterval = setInterval(() => {
      console.log('Audio debug:', {
        src: audioRef.current?.src,
        currentSrc: audioRef.current?.currentSrc,
        readyState: audioRef.current?.readyState,
        networkState: audioRef.current?.networkState,
        duration: audioRef.current?.duration,
        paused: audioRef.current?.paused,
        error: audioRef.current?.error,
      });
    }, 5000); */

    // Cleanup
    return () => {
      clearTimeout(timeout);
      //clearInterval(debugInterval);
      
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);
        audioRef.current.removeEventListener('canplay', handleCanPlay);
        audioRef.current.removeEventListener('error', handleError);
        audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);
        audioRef.current.removeEventListener('ended', handleEnded);
      }
      if (sourceRef.current) {
        sourceRef.current.disconnect();
        sourceRef.current = null;
      }
      if (audioContextRef.current) {
        audioContextRef.current.close().catch((err) => console.error('Error closing AudioContext:', err));
        audioContextRef.current = null;
      }
    };
  }, [audioUrl, onLoad, onError, useAudioContext]);

  const togglePlay = async () => {
    if (!audioRef.current || hasError) return;

    try {
      // Initialize AudioContext on user interaction
      if (useAudioContext && !sourceRef.current && audioContextRef.current?.state !== 'running') {
        initializeAudioContext();
        if (audioContextRef.current?.state === 'suspended') {
          await audioContextRef.current.resume();
        }
      }

      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (err) {
      console.error('Error toggling play:', err);
      setHasError('Playback failed: ' + (err as Error).message);
      setUseAudioContext(false); // Disable AudioContext on failure
      onError?.();
    }
  };

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const newTime = parseFloat(e.target.value);
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const toggleMute = () => {
    if (!audioRef.current) return;
    const newMuted = !isMuted;
    audioRef.current.muted = newMuted;
    setIsMuted(newMuted);
    setVolume(newMuted ? 0 : audioRef.current.volume);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const newVolume = parseFloat(e.target.value);
    audioRef.current.volume = newVolume;
    audioRef.current.muted = newVolume === 0;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const formatTime = (time: number) => {
    if (isNaN(time) || !isFinite(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (hasError) {
    return (
      <div className="w-full bg-gray-100 dark:bg-zinc-800 rounded-lg p-4 flex items-center justify-center text-gray-500 dark:text-gray-400">
        {hasError}
      </div>
    );
  }

  return (
    <div className="relative w-full bg-gradient-to-br from-turquoise/10 to-turquoise/5 dark:from-turquoise/20 dark:to-turquoise/10 rounded-lg p-4">
      {/* isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
          Loading audio…
        </div>
      ) */}

      <audio
        ref={audioRef}
        src={audioUrl}
        preload="metadata"
        crossOrigin={audioUrl.startsWith('http') ? 'anonymous' : undefined}
      />

      <div className="flex flex-col gap-4">
        <div className="relative h-16 w-full">
          {useAudioContext && analyzerData && (
            <WaveForm
              analyzerData={analyzerData}
              isPlaying={isPlaying}
            />
          )}
        </div>

        <div className="flex items-center gap-4">
          <button
            onClick={togglePlay}
            disabled={hasError || isLoading}
            className="flex-shrink-0 w-10 h-10 rounded-full bg-turquoise/20 hover:bg-turquoise/30 disabled:bg-gray-200 dark:disabled:bg-zinc-700 flex items-center justify-center transition-colors"
          >
            {isPlaying ? (
              <Pause className="h-5 w-5 text-turquoise" />
            ) : (
              <Play className="h-5 w-5 text-turquoise" />
            )}
          </button>

          <div className="flex-grow flex items-center gap-2">
            <Music className="h-4 w-4 text-turquoise" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {formatTime(currentTime)} / {formatTime(audioDuration || duration || 0)}
            </span>
          </div>

          <button
            onClick={toggleMute}
            className="flex-shrink-0 w-8 h-8 rounded-full bg-turquoise/20 hover:bg-turquoise/30 flex items-center justify-center transition-colors"
            disabled={hasError || isLoading}
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="h-4 w-4 text-turquoise" />
            ) : (
              <Volume2 className="h-4 w-4 text-turquoise" />
            )}
          </button>
        </div>

        <div className="flex items-center gap-4">
          <input
            type="range"
            min="0"
            max={audioDuration || duration || 0}
            value={currentTime}
            onChange={handleProgressChange}
            className="flex-grow h-1 bg-turquoise/20 rounded-full accent-turquoise cursor-pointer"
            disabled={hasError || isLoading}
          />
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={isMuted ? 0 : volume}
            onChange={handleVolumeChange}
            className="w-16 h-1 bg-turquoise/20 rounded-full accent-turquoise cursor-pointer"
            disabled={hasError || isLoading}
          />
        </div>
      </div>
    </div>
  );
};
export default AudioPlayer;