'use client';

import React from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { X } from 'lucide-react';
import { MediaPlayer, MediaProvider, Poster, Track } from "@vidstack/react";
import { DefaultVideoLayout, defaultLayoutIcons } from '@vidstack/react/player/layouts/default';
// Styles from Vidstack’s default themes/layouts
import '@vidstack/react/player/styles/default/theme.css';
import '@vidstack/react/player/styles/default/layouts/video.css';

interface VideoLightboxProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  videoSrc: string;
  posterSrc?: string;
  title?: string;
  thumbnail?: string;
}

const VideoLightbox: React.FC<VideoLightboxProps> = ({
  open,
  onOpenChange,
  thumbnail,
  videoSrc,
  posterSrc,
  title = 'Video'
}) => {
  return (
    <AnimatedModal
      open={open}
      onOpenChange={onOpenChange}
      size="5xl"
      closeButton={false}
      header={null}
      footer={null}
    >
      <div className="bg-black text-white rounded-lg overflow-hidden relative">
        <button
          onClick={() => onOpenChange(false)}
          className="absolute top-4 right-4 z-50 w-8 h-8 flex items-center justify-center rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
        >
          <X size={18} />
        </button>

        <div className="w-full aspect-video">
          <MediaPlayer
            src={videoSrc}
            poster={posterSrc || thumbnail}
            className="w-full h-full"
            viewType='video'
            logLevel='warn'
            preload="auto"
            autoPlay={true}
            //crossOrigin
            playsInline
            title={title}
          >
            <MediaProvider>
              <Poster className="vds-poster" />
            </MediaProvider>
            <DefaultVideoLayout
              icons={defaultLayoutIcons}
            />
          </MediaPlayer>
        </div>
      </div>
    </AnimatedModal>
  );
};
export default VideoLightbox;