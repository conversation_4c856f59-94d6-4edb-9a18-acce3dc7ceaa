import { jsxs as a, jsx as o } from "react/jsx-runtime";
import k from "./swap/page.js";
import G from "./utils/provider.js";
import $ from "./components/common/TokenSearch.js";
import { cn as p } from "./lib/utils.js";
import q from "./Layout.js";
import { useState as z, useEffect as C, useMemo as s } from "react";
import { Button as F } from "./components/ui/button.js";
import { Dialog as H, DialogTrigger as K, DialogContent as Q } from "./components/ui/dialog.js";
import V from "./hooks/useScreen.js";
import X from "./components/ui/widgetFloatButton.js";
import { k as Y } from "./react-toastify.esm-a636d9b1.js";
import "./swap/components/Setup.js";
import "./swap/components/Main/useSwapSetup.js";
import "./store/useStore.js";
import "./_commonjsHelpers-10dfc225.js";
import "./store/createTokenSearchSlice.js";
import "./immer-548168ec.js";
import "./store/createWalletSlice.js";
import "./store/createSwapSettingsSlice.js";
import "./store/createGlobalSettingsSlice.js";
import "./store/createUserOrdersSlice.js";
import "./store/createSwapSlice.js";
import "./store/createChartSlice.js";
import "./store/createBasketSlice.js";
import "./swap/components/tokens.js";
import "./store/createModalWhatsNewSlice.js";
import "./store/createSwapParamsSlice.js";
import "./hooks/useWalletConnect.js";
import "./utils/cardanoUtils.js";
import "./index-ca8eb9e1.js";
import "./config/axios.js";
import "./axios-ddd885c5.js";
import "./constants/wallets.js";
import "./useQuery-febd7967.js";
import "./query-013b86c3.js";
import "./QueryClientProvider-6bcd4331.js";
import "./swap/components/Swap.js";
import "./swap/components/Details/SwapDetails.js";
import "./components/ui/skeleton.js";
import "./lib.js";
import "./extend-tailwind-merge-e63b2b56.js";
import "./shallow-27fd7e97.js";
import "./constants/dexes.js";
import "./react-hotkeys-hook.esm-60f1c3b8.js";
import "./swap/components/Details/SwapDetailSplit.js";
import "./swap/components/Details/OrderPreview.js";
import "./components/ui/progress.js";
import "./index-1c873780.js";
import "./index-563d1ed8.js";
import "./index-c8f2666b.js";
import "./utils/formatNumber.js";
import "./components/ui/tooltipDialog.js";
import "./components/ui/tooltip.js";
import "./index-c7156e07.js";
import "./index-67500cd3.js";
import "./index-4914f99c.js";
import "./index-0ce202b9.js";
import "./index-bcfeaad9.js";
import "./index-5116e957.js";
import "./index-f7426637.js";
import "./index-840f2930.js";
import "./index-27cadef5.js";
import "./assets/svg/IconSquareInfo.js";
import "./x-9e07c78a.js";
import "./createLucideIcon-7a477fa6.js";
import "./swap/components/Details/SwapDetailOutput.js";
import "./components/common/TokenPrice.js";
import "./utils/formatToken.js";
import "./trends/components/PriceFormatter.js";
import "./assets/svg/IconTwoWay.js";
import "./assets/svg/IconX.js";
import "./assets/svg/IconDown.js";
import "./components/common/RealtimePulse.js";
import "./components/ui/separator.js";
import "./swap/components/SwapConfim.js";
import "./swap/hooks/useSwapAction.js";
import "./hooks/useNotify.js";
import "./assets/svg/IconCopy.js";
import "./assets/svg/IconCheckNotify.js";
import "./assets/svg/IconAlertTriangleNotify.js";
import "./assets/svg/IconArrowUpRightNotify.js";
import "./swap/hooks/useLimitAction.js";
import "./swap/hooks/useStopLossAction.js";
import "./assets/svg/IconSquaredSpinner.js";
import "./swap/hooks/useDCAAction.js";
import "./index-1d6812f7.js";
import "./swap/hooks/useSwapEstimation.js";
import "./swap/components/Settings/SwapSettings.js";
import "./assets/svg/IconSetting.js";
import "./components/ui/input.js";
import "./components/ui/switchWithText.js";
import "./assets/svg/IconRemove.js";
import "./swap/components/Main/SwapHeader.js";
import "./swap/components/Settings/Slippage.js";
import "./assets/svg/IconRefresh.js";
import "./swap/components/Main/SwapSelect.js";
import "./swap/components/TokenSell.js";
import "./assets/svg/IconChevronDown.js";
import "./components/common/TokenImage.js";
import "./hooks/useInputShortcuts.js";
import "./swap/hooks/useUsdPrices.js";
import "./index.esm-fb2f5862.js";
import "./IconTilde-bf643edd.js";
import "./createReactComponent-ec43b511.js";
import "./assets/svg/IconArrowDown.js";
import "./swap/components/TokenBuy.js";
import "./swap/components/TokenLimit.js";
import "./components/ui/slider.js";
import "./index-1fe761a6.js";
import "./index-6460524a.js";
import "./index-bf605d8a.js";
import "./swap/components/TokenDCA.js";
import "./orders/components/Filters/DCAInterval.js";
import "./components/ui/dropdown-menu.js";
import "./assets/svg/IconCheck.js";
import "./swap/components/Main/useSwapShortcuts.js";
import "./swap/hooks/useLimitEstimation.js";
import "./swap/hooks/useStopLossEstimation.js";
import "./hooks/useGlobalSettings.js";
import "./assets/svg/IconWallet.js";
import "./assets/svg/IconWalletCross.js";
import "./assets/svg/IconMagnifier.js";
import "./components/ui/sheet-connect-wallet.js";
import "./components/common/WalletPopover.js";
import "./components/ui/checkbox.js";
function gt({
  defaultToken: l,
  width: i,
  height: m,
  theme: n = "dark",
  className: c,
  style: f,
  orderTypes: x = ["SWAP", "LIMIT", "DCA"],
  supportedTokens: u,
  partnerName: g,
  partnerCode: v,
  colors: d,
  onSwapSuccess: E,
  onSwapError: j,
  selectedWallet: w,
  inputs: N,
  onWalletConnect: P,
  onClickWalletConnect: T,
  onViewOrder: U,
  displayType: r,
  buttonText: b,
  orderTypeOnButtonClick: S,
  buttonStyle: D,
  buttonClassName: O,
  widgetButtonClass: R,
  defaultSettings: M,
  autoFocus: y = !1
}) {
  const B = {}, [A, L] = z(!1), { isMobile: t } = V();
  C(() => {
    L(!0);
  }, []), d && Object.keys(d).forEach(function(J) {
    B[`--dhs-${J}`] = d[J];
  });
  const e = s(() => /* @__PURE__ */ a("div", { id: "swap-main", children: [
    /* @__PURE__ */ o(
      k,
      {
        defaultToken: l,
        orderTypes: x,
        supportedTokens: u,
        partnerName: g,
        partnerCode: v,
        onSwapSuccess: E,
        onSwapError: j,
        selectedWallet: w,
        inputs: N,
        onWalletConnect: P,
        onClickWalletConnect: T,
        onViewOrder: U,
        orderTypeOnButtonClick: S,
        defaultSettings: M,
        autoFocus: y
      }
    ),
    /* @__PURE__ */ o(Y, { className: "!dhs-absolute !dhs-max-w-full !dhs-right-0" })
  ] }), [
    l,
    JSON.stringify(x),
    JSON.stringify(u),
    g,
    v,
    w,
    JSON.stringify(N),
    S,
    JSON.stringify(M),
    y
  ]), I = s(() => r === "BUTTON" ? /* @__PURE__ */ a(H, { children: [
    /* @__PURE__ */ o(K, { className: "dhs-w-full", children: /* @__PURE__ */ o(
      F,
      {
        className: p(
          "dhs-shadow dhs-h-12 @sm/appRoot:dhs-h-16 sm:dhs-h-[60px] dhs-rounded-3xl dhs-bg-accent hover:dhs-bg-accent hover:dhs-bg-opacity-90 dhs-transition-colors dhs-duration-200 dhs-ease-in-out dhs-text-buttonText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-text-xl",
          t ? "dhs-w-[375px]" : "dhs-w-[450px]",
          O
        ),
        style: D,
        variant: "default",
        children: b ?? "Swap"
      }
    ) }),
    /* @__PURE__ */ o(
      Q,
      {
        portalContainer: document.getElementById("dexhunter-container"),
        className: p(
          "dhs-@container/appRoot dhs-border-2 dhs-border-solid dhs-border-containers dhs-fixed dhs-rounded-[26px] dhs-p-0 sm:dhs-max-w-none dhs-top-1/2 dhs-translate-y-[-50%]",
          t ? "dhs-w-[375px]" : "dhs-w-[450px]"
        ),
        style: { width: i, height: m },
        children: e
      }
    )
  ] }) : e, [
    r,
    O,
    JSON.stringify(D),
    A,
    t,
    e,
    i,
    m,
    b
  ]), h = s(() => /* @__PURE__ */ o(
    "div",
    {
      id: "dexhunter-root",
      className: p(
        "dhs-@container/appRoot dhs-bg-transparent dhs-inline-block dhs-text-mainText dhs-relative dhs-overflow-y-auto dhs-overflow-x-hidden ",
        r === "BUTTON" ? "dhs-h-full" : "dhs-min-h-[406px] @md/appRoot:dhs-min-h-[504px]",
        r === "BUTTON" ? "dhs-inline-table" : t ? "dhs-w-[375px]" : "dhs-w-[450px]",
        n === "light" ? "light" : void 0,
        c
      ),
      style: { width: i, height: m, ...f },
      children: /* @__PURE__ */ a(G, { children: [
        /* @__PURE__ */ o($, {}),
        I,
        /* @__PURE__ */ o(q, {})
      ] })
    }
  ), [I, t, n, c, f, i, m]), W = s(() => r === "WIDGET" ? /* @__PURE__ */ o(X, { className: R, children: h }) : h, [r, h, R]);
  return /* @__PURE__ */ o(
    "div",
    {
      id: "dexhunter-container",
      style: { display: "inline-flex", ...B },
      className: p(n === "light" ? "light" : void 0),
      children: W
    }
  );
}
export {
  gt as default
};
