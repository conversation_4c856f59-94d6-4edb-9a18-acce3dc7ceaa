import { jsx as r } from "react/jsx-runtime";
import * as t from "react";
import { cn as l } from "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
const s = t.forwardRef(({ className: e, ...a }, o) => (
  // <div className="w-full">
  /* @__PURE__ */ r(
    "table",
    {
      ref: o,
      className: l("w-full caption-bottom text-sm", e),
      ...a
    }
  )
));
s.displayName = "Table";
const d = t.forwardRef(({ className: e, ...a }, o) => /* @__PURE__ */ r(
  "thead",
  {
    ref: o,
    className: l("[&_tr]:border-b bg-gray-118 z-10", e),
    ...a
  }
));
d.displayName = "TableHeader";
const m = t.forwardRef(({ className: e, ...a }, o) => /* @__PURE__ */ r(
  "tbody",
  {
    ref: o,
    className: l("[&_tr:last-child]:border-0", e),
    ...a
  }
));
m.displayName = "TableBody";
const b = t.forwardRef(({ className: e, ...a }, o) => /* @__PURE__ */ r(
  "tfoot",
  {
    ref: o,
    className: l("bg-primary font-medium text-primary-foreground", e),
    ...a
  }
));
b.displayName = "TableFooter";
const p = t.forwardRef(({ className: e, ...a }, o) => /* @__PURE__ */ r(
  "tr",
  {
    ref: o,
    className: l(
      "hover:bg-gray108/50 data-[state=selected]:bg-108 border-b-[1px] border-gray-114",
      e
    ),
    ...a
  }
));
p.displayName = "TableRow";
const f = t.forwardRef(({ className: e, ...a }, o) => /* @__PURE__ */ r(
  "th",
  {
    ref: o,
    className: l(
      "h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 first:pl-12",
      e
    ),
    ...a
  }
));
f.displayName = "TableHead";
const c = t.forwardRef(({ className: e, ...a }, o) => /* @__PURE__ */ r(
  "td",
  {
    ref: o,
    className: l(
      "px-4 py-2.5 sm:px-[15px] sm:py-[15px] align-middle [&:has([role=checkbox])]:pr-0",
      e
    ),
    ...a
  }
));
c.displayName = "TableCell";
const i = t.forwardRef(({ className: e, ...a }, o) => /* @__PURE__ */ r(
  "caption",
  {
    ref: o,
    className: l("mt-4 text-sm text-muted-foreground", e),
    ...a
  }
));
i.displayName = "TableCaption";
export {
  s as Table,
  m as TableBody,
  i as TableCaption,
  c as TableCell,
  b as TableFooter,
  f as TableHead,
  d as TableHeader,
  p as TableRow
};
