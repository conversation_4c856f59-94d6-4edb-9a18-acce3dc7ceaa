import { useQuery, useMutation } from 'convex/react';
import { useCallback } from 'react';
import { api } from '@/convex/_generated/api';
import { PostData, Comment } from '@/types/post';
import { Creator } from '@/types/user';

// Fetch posts feed (Convex)
export const useFetchPostsFeed = (args: {
  filter?: string;
  page?: number;
  limit?: number;
  profileUserId?: string;
  userId?: string;
  panel?: string;
  gender?: string;
  mediaOnly?: boolean;
  direction?: 'forward' | 'backward';
  tag?: string;
}) => useQuery(api.content.fetchPostsFeed, args);

export const useFetchExplorePosts = (args: { tab?: string; filter?: string; page?: number; limit?: number; mediaOnly?: boolean; gender?: string; direction?: 'forward' | 'backward'; tag?: string; _ts?: string; userId?: string; panel?: string }) => {
  const effectivePanel = args.panel || args.tab;
  return useQuery(api.explore.explorePosts, { ...args, panel: effectivePanel });
};

// Convex Mutations
export const useCreatePost = () => useMutation(api.content.createContent);
export const useUpdatePost = () => useMutation(api.content.updatePost); // Assumes updatePost mutation exists
export const useUpdatePostPrivacy = () => useMutation(api.content.updatePostVisibility);
export const useDeletePost = () => useMutation(api.content.deletePost); // Assumes deletePost mutation exists
export const useLikePost = () => useMutation(api.content.toggleLike);
export const useSavePost = () => useMutation(api.content.toggleSave);
export const useReportPost = () => useMutation(api.content.reportPost); // Assumes reportPost mutation exists
export const useCreateComment = () => useMutation(api.content.createComment);
export const useTrackPostView = () => useMutation(api.content.trackContentView);

// REST API Fallbacks (for endpoints not yet migrated to Convex)
export function useCreatePostREST() {
  return useCallback(async (postData: PostData) => {
    const response = await fetch('/api/posts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(postData),
    });
    if (!response.ok) throw new Error('Failed to create post');
    return await response.json();
  }, []);
};

// Update post (REST fallback)
export function useUpdatePostREST() {
  return useCallback(async (postData: PostData) => {
    const response = await fetch(`/api/posts/${postData.id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(postData),
    });
    if (!response.ok) throw new Error('Failed to update post');
    return await response.json();
  }, []);
};

// Delete post (REST fallback)
export function useDeletePostREST() {
  return useCallback(async (id: string) => {
    const response = await fetch(`/api/posts/${id}`, { method: 'DELETE' });
    if (!response.ok) throw new Error('Failed to delete post');
    return id;
  }, []);
};

// Report post (REST fallback)
export function useReportPostREST() {
  return useCallback(async ({ postId, reason }: { postId: string; reason: string }) => {
    const response = await fetch(`/api/posts/${postId}/report`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ reason }),
    });
    if (!response.ok) throw new Error('Failed to report post');
    return await response.json();
  }, []);
};