import { SkeletonCircle, SkeletonText } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface ChatSkeletonProps {
  variant?: "sent" | "received";
  className?: string;
}

export function ChatSkeleton({ variant = "received", className }: ChatSkeletonProps) {
  return (
    <div
      className={cn(
        "flex gap-2 max-w-[90%] items-end relative mb-8 w-full",
        variant === "sent" ? "self-end flex-row-reverse" : "self-start",
        className
      )}
    >
      <SkeletonCircle size="14" />
      <SkeletonText noOfLines={3} />
    </div>
  );
}

export function ChatListSkeleton() {
  return (
    <div className="flex flex-col gap-4 p-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <div
          key={i}
          className="flex items-center gap-3 p-2 rounded-lg hover:bg-secondary/20"
        >
          <SkeletonCircle size="10" />
          <div className="flex-1 space-y-2">
            <div className="h-4 w-24 bg-foreground/20 rounded" />
            <div className="h-3 w-32 bg-foreground/20 rounded" />
          </div>
        </div>
      ))}
    </div>
  );
}