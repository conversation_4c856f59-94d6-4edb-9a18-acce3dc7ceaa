'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/chat/ui/avatar";
import { ChatInput } from "@/components/ui/chat/chat-input";
import { Button, buttonVariants } from "@/components/ui/chat/ui/button";
import { EmojiPicker } from "@/components/ui/chat/emoji-picker";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/chat/ui/popover";
import { FileImage, Paperclip, SendHorizontal, Smile, PlusCircle, Mic, ThumbsUp, PhoneCall, Video } from "lucide-react";
import { toast } from "react-toastify";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { BottombarIcons } from '@/components/messages/MessagesClient';
import Tippy from '@tippyjs/react';
import { VideoCallDialog } from "@/components/dialogs/VideoCallDialog";

export default function NewMessageClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userId = searchParams.get('userId');
  const [recipient, setRecipient] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showVideoDialog, setShowVideoDialog] = useState(false);
  const [shareCamera, setShareCamera] = useState(true);
  const [shareAudio, setShareAudio] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const formRef = useRef<HTMLFormElement>(null);

  // Convex mutation for sending messages
  const sendMessage = useMutation(api.messages.sendMessage);

  // Convex query for recipient info (replace with your actual Convex user info query)
  const recipientInfo = useQuery(api.users.getUserInfo, userId ? { userId } : 'skip');

  useEffect(() => {
    setIsMobile(window.innerWidth < 768);
    const handleResize = () => setIsMobile(window.innerWidth < 768);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (!userId) {
      router.push('/messages');
      return;
    }
    if (recipientInfo) {
      setRecipient(recipientInfo);
      setLoading(false);
    }
  }, [userId, recipientInfo, router]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !userId) return;
    setIsLoading(true);
    try {
      await sendMessage({ content: message, receiverId: userId });
      setMessage('');
      toast.success('Message sent successfully');
      router.push(`/messages/${userId}`);
    } catch (error) {
      toast.error('Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setMessage((prev) => prev + emoji);
    if (inputRef.current) inputRef.current.focus();
  };

  const handleThumbsUp = async () => {
    if (!userId || isLoading) return;
    setIsLoading(true);
    try {
      await sendMessage({ content: "👍", receiverId: userId });
      toast.success('Message sent successfully');
      router.push(`/messages/${userId}`);
    } catch (error) {
      toast.error('Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    if (inputRef.current) {
      inputRef.current.style.height = "auto";
      const newHeight = Math.min(inputRef.current.scrollHeight, 150);
      inputRef.current.style.height = `${newHeight}px`;
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (!isLoading && message.trim()) {
        formRef.current?.requestSubmit();
      }
    }
    if (event.key === "Enter" && event.shiftKey) {
      event.preventDefault();
      setMessage((prev) => prev + "\n");
    }
  };

  if (loading) {
    return (
      <div className="flex w-full items-center justify-center h-screen">
        <div className="flex-1 flex items-center justify-center">
          <div className="h-8 w-8">
            <span className="new-message-loader"></span>
          </div>
        </div>
      </div>
    );
  }

  if (!recipient) {
    return null;
  }

  return (
    <main className="flex h-full w-full flex-col items-center">
      {/* Header */}
      <div className="w-full border-b border-t-0 border-r-0 border-l-0 border-solid border-[#18181b]/30 dark:border-white/30 p-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10 mr-3">
            <AvatarImage src={recipient.profilePhoto} />
            <AvatarFallback>
              {(recipient.username || recipient.user_id?.slice(0, 2) || '?').charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="font-medium">
              {recipient.username || recipient.user_id?.slice(0, 8) || 'Unknown User'}
            </h2>
            <p className="text-sm text-gray-500">New message</p>
          </div>
        </div>
        <div className="flex items-center justify-center gap-2">
          <Tippy content="Voice Call" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
            <button className="h-9 w-9 shrink-0 rounded-full">
              <PhoneCall className="text-muted-foreground" />
            </button>
          </Tippy>
          <Tippy content="Video Call" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
            <button
              className="h-9 w-9 shrink-0 rounded-full"
              onClick={() => setShowVideoDialog(true)}
            >
              <Video className="text-muted-foreground" />
            </button>
          </Tippy>
        </div>
      </div>

      {/* Empty message area */}
      <div className="w-full flex-1 p-4 flex flex-col items-center justify-center">
        <div className="text-gray-400 mb-4">
          Start a new conversation with {recipient.username || 'this user'}
        </div>
        {isLoading && (
          <div className="flex items-center justify-center mb-4">
            <span className="new-message-loader"></span>
            <span className="ml-2 text-sm text-gray-500">Sending message...</span>
          </div>
        )}
      </div>

      {/* Message input */}
      <form className="w-full" ref={formRef} onSubmit={handleSendMessage}>
        <div className="px-2 py-4 flex w-full items-center gap-2 bg-neutral-200 dark:bg-neutral-800">
          <div className="flex">
            <Popover>
              <PopoverTrigger asChild>
                <a className={cn(buttonVariants({ variant: "ghost", size: "icon" }), "h-9 w-9", "shrink-0")}>
                  <PlusCircle size={22} className="text-muted-foreground" />
                </a>
              </PopoverTrigger>
              <PopoverContent side="top" className="w-full p-2 bg-neutral-200 dark:bg-neutral-800">
                {message.trim() || isMobile ? (
                  <div className="flex gap-2">
                    <Tippy content="Voice Message" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                      <a className={cn(buttonVariants({ variant: "ghost", size: "icon" }), "h-9 w-9", "shrink-0")}>
                        <Mic size={22} className="text-muted-foreground" />
                      </a>
                    </Tippy>
                    {BottombarIcons.map((icon: any, index: number) => (
                      <Tippy
                        key={index}
                        content={icon.label || icon.name}
                        animation="shift-toward-subtle"
                        followCursor={false}
                        placement="top"
                        arrow={true}
                        theme="sugar"
                      >
                        <a className={cn(buttonVariants({ variant: "ghost", size: "icon" }), "h-9 w-9", "shrink-0")}>
                          <icon.icon size={22} className="text-muted-foreground" />
                        </a>
                      </Tippy>
                    ))}
                  </div>
                ) : (
                  <Tippy content="Voice Message" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                    <a className={cn(buttonVariants({ variant: "ghost", size: "icon" }), "h-9 w-9", "shrink-0")}>
                      <Mic size={22} className="text-muted-foreground" />
                    </a>
                  </Tippy>
                )}
              </PopoverContent>
            </Popover>
            {!message.trim() && !isMobile && (
              <div className="flex">
                {BottombarIcons.map((icon, index) => (
                  <Tippy
                    key={index}
                    content={icon.label}
                    animation="shift-toward-subtle"
                    followCursor={false}
                    placement="top"
                    arrow={true}
                    theme="sugar"
                  >
                    <a className={cn(buttonVariants({ variant: "ghost", size: "icon" }), "h-9 w-9", "shrink-0")}>
                      <icon.icon size={22} className="text-muted-foreground" />
                    </a>
                  </Tippy>
                ))}
              </div>
            )}
          </div>

          <AnimatePresence initial={false}>
            <motion.div
              key="input"
              className="w-full relative"
              layout
              initial={{ opacity: 0, scale: 1 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1 }}
              transition={{
                opacity: { duration: 0.05 },
                layout: { type: "spring", bounce: 0.15 },
              }}
            >
              <ChatInput
                value={message}
                ref={inputRef}
                onKeyDown={handleKeyPress}
                onChange={handleInputChange}
                placeholder="Type a message..."
                className="rounded-full h-10 min-h-0 focus:ring-[var(--turquoise)] overflow-auto"
                disabled={isLoading}
              />
              <Tippy content="Choose Emoji" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                <div className="absolute right-4 bottom-2">
                  <EmojiPicker onChange={handleEmojiSelect} />
                </div>
              </Tippy>
            </motion.div>

            {message.trim() ? (
              <Tippy content="Send Message" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                <Button
                  className="h-9 w-9 shrink-0"
                  onClick={() => formRef.current?.requestSubmit()}
                  disabled={isLoading}
                  variant="ghost"
                  size="icon"
                  type="submit"
                >
                  <SendHorizontal size={22} className={isLoading ? "text-gray-400" : "text-muted-foreground"} />
                </Button>
              </Tippy>
            ) : (
              <Tippy content="Send Thumbs Up" animation="shift-toward-subtle" followCursor={false} placement="top" arrow={true} theme="sugar">
                <Button
                  className="h-9 w-9 shrink-0"
                  onClick={handleThumbsUp}
                  disabled={isLoading}
                  variant="ghost"
                  size="icon"
                  type="button"
                >
                  <ThumbsUp size={22} className={isLoading ? "text-gray-400" : "text-muted-foreground"} />
                </Button>
              </Tippy>
            )}
          </AnimatePresence>
        </div>
      </form>

      <VideoCallDialog
        open={showVideoDialog}
        onClose={() => setShowVideoDialog(false)}
        onAddFunds={() => {/* handle add funds */ }}
        onSelectSource={() => {/* handle select source */ }}
        balance={0}
        pricePerMinute={3.99}
        minCallTime={20}
        username={recipient?.username || "User"}
        shareCamera={shareCamera}
        setShareCamera={setShareCamera}
        shareAudio={shareAudio}
        setShareAudio={setShareAudio}
      />
    </main>
  );
}
