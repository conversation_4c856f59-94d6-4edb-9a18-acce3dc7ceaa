import { jsx as s, jsxs as o } from "react/jsx-runtime";
import { Q as u } from "../react-toastify.esm-a636d9b1.js";
import x from "../assets/svg/IconCopy.js";
import g from "../assets/svg/IconX.js";
import w from "../assets/svg/IconCheckNotify.js";
import b from "../assets/svg/IconAlertTriangleNotify.js";
import S from "../assets/svg/IconArrowUpRightNotify.js";
import { cn as l } from "../lib.js";
import N from "./useScreen.js";
import y from "../store/useStore.js";
import "react";
import "../extend-tailwind-merge-e63b2b56.js";
import "../_commonjsHelpers-10dfc225.js";
import "../store/createTokenSearchSlice.js";
import "../immer-548168ec.js";
import "../store/createWalletSlice.js";
import "../store/createSwapSettingsSlice.js";
import "../store/createGlobalSettingsSlice.js";
import "../store/createUserOrdersSlice.js";
import "../store/createSwapSlice.js";
import "../store/createChartSlice.js";
import "../store/createBasketSlice.js";
import "../swap/components/tokens.js";
import "../store/createModalWhatsNewSlice.js";
import "../store/createSwapParamsSlice.js";
const v = ({
  closeToast: r,
  type: e,
  title: h,
  desc: p,
  actionName: d,
  actionCallback: t
}) => {
  const a = {
    warning: /* @__PURE__ */ s(b, { className: "dhs-text-buttonText dhs-w-3.5 dhs-h-3.5" }),
    success: /* @__PURE__ */ s(w, { className: "dhs-text-buttonText dhs-w-2.5 dhs-h-[7px]" }),
    error: /* @__PURE__ */ s(g, { className: "dhs-text-buttonText dhs-w-2.5 dhs-h-2.5" })
  }, i = {
    warning: "dhs-bg-red-102 dhs-bg-opacity-[0.2015]",
    success: "dhs-bg-accent dhs-bg-opacity-[0.2015]",
    error: "dhs-bg-red-101 dhs-bg-opacity-[0.2015]"
  }, n = {
    warning: "dhs-text-red-102",
    success: "dhs-text-mainText",
    error: "dhs-text-red-101"
  }, m = {
    warning: "Warning!",
    success: "Success",
    error: "Failed"
  }, c = () => {
    t !== void 0 && t(r);
  }, f = {
    warning: /* @__PURE__ */ o(
      "button",
      {
        onClick: c,
        className: "dhs-flex dhs-gap-2 dhs-justify-center dhs-items-center dhs-bg-gray-104 dhs-rounded-xl dhs-text-gray-101 dhs-font-proximaSemiBold dhs-text-sm dhs-w-full",
        children: [
          /* @__PURE__ */ s("span", { children: d || "Copy message" }),
          /* @__PURE__ */ s(x, {})
        ]
      }
    ),
    success: /* @__PURE__ */ o(
      "button",
      {
        className: l(
          "dhs-flex dhs-gap-2 dhs-justify-center dhs-items-center dhs-rounded-xl dhs-font-proximaSemiBold dhs-text-buttonText dhs-text-sm dhs-w-full",
          i[e],
          n[e]
        ),
        onClick: () => {
          t !== void 0 && t(r);
        },
        children: [
          /* @__PURE__ */ s("span", { children: d || "Transaction" }),
          /* @__PURE__ */ s(S, { className: "dhs-w-2 dhs-h-2" })
        ]
      }
    ),
    error: /* @__PURE__ */ o(
      "button",
      {
        onClick: c,
        className: "dhs-flex dhs-gap-2 dhs-justify-center dhs-items-center dhs-bg-gray-104 dhs-rounded-xl dhs-text-gray-101 dhs-font-proximaSemiBold dhs-text-sm dhs-w-full",
        children: [
          /* @__PURE__ */ s("span", { children: d || "Copy message" }),
          /* @__PURE__ */ s(x, {})
        ]
      }
    )
  };
  return /* @__PURE__ */ o("div", { className: "dhs-flex dhs-flex-col dhs-justify-between dhs-min-h-[109px] dhs-gap-3", children: [
    /* @__PURE__ */ o("div", { className: "dhs-flex dhs-gap-[9px] dhs-items-center sm:dhs-items-start", children: [
      /* @__PURE__ */ s(
        "div",
        {
          className: l(
            "dhs-flex dhs-items-center dhs-justify-center dhs-w-8.5 dhs-h-8.5 dhs-rounded-xl",
            i[e]
          ),
          children: a[e]
        }
      ),
      /* @__PURE__ */ o("div", { className: "dhs-flex-1 dhs-leading-none dhs-gap-1 dhs-dhs-flex dhs-flex-col", children: [
        /* @__PURE__ */ s("div", { className: l("dhs-font-proximaSemiBold dhs-text-mainText", n[e]), children: h || m[e] }),
        /* @__PURE__ */ s("div", { className: "dhs-font-proximaMedium dhs-text-subText dhs-text-sm", children: p })
      ] })
    ] }),
    /* @__PURE__ */ o("div", { className: "dhs-flex dhs-gap-2.5 dhs-justify-end", children: [
      t !== void 0 && f[e],
      /* @__PURE__ */ s(
        "button",
        {
          className: "dhs-w-[100px] dhs-h-8.5 dhs-rounded-xl dhs-bg-gray-104 dhs-text-gray-101 dhs-font-proximaSemiBold dhs-text-sm",
          onClick: r,
          children: "Close"
        }
      )
    ] })
  ] });
}, V = () => {
  const { isMobile: r } = N(), { onSwapSuccess: e, onSwapError: h } = y((d) => ({
    onSwapSuccess: d.swapSlice.onSwapSuccess,
    onSwapError: d.swapSlice.onSwapError
  }));
  return { notify: ({ type: d, title: t, desc: a, actionName: i, actionCallback: n, dataCallback: m }) => {
    if (d === "success" && e !== void 0) {
      e({ data: m });
      return;
    }
    if (d === "error" && h !== void 0) {
      h({ data: m });
      return;
    }
    u(
      ({ closeToast: c }) => /* @__PURE__ */ s(
        v,
        {
          closeToast: c,
          type: d,
          title: t,
          desc: a,
          actionName: i,
          actionCallback: n
        }
      ),
      {
        position: r ? "bottom-center" : "bottom-right",
        closeOnClick: !1,
        draggable: !1,
        hideProgressBar: !0,
        autoClose: !1,
        icon: !1,
        closeButton: !1,
        className: "!dhs-bg-inherit !dhs-bg-containers !dhs-shadow-notify !dhs-backdrop-blur-[27px] !dhs-rounded-[21px] !dhs-w-[319px] dhs-max-w-full",
        style: { margin: r ? "0 auto" : "" },
        bodyClassName: "!dhs-min-h-[139px] !dhs-p-[15px] sm:!dhs-p-[7px]"
      }
    );
  } };
};
export {
  V as useNotify
};
