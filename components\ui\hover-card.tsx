"use client";

import React, { useState, useEffect } from "react";
import * as HoverCardPrimitive from "@radix-ui/react-hover-card";

import { cn } from "@/lib/utils";
import { Skeleton } from "./skeleton";
import { truncateText } from "@/public/main";
import Link from "next/link";

// Define user data type for type safety
export interface UserData {
  username: string;
  displayName?: string;
  profilePhoto?: string;
  coverBanner?: string;
  coverBannerType?: 'image' | 'video';
  bio?: string;
  isVerified?: boolean;
  photoCount?: number;
  videoCount?: number;
  clipCount?: number;
  gifCount?: number;
  accountType?: string;
}

const HoverCard = HoverCardPrimitive.Root;

const HoverCardTrigger = HoverCardPrimitive.Trigger;

const HoverCardContent = React.forwardRef<
  React.ElementRef<typeof HoverCardPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>
>(({ className, align = "center", sideOffset = 4, ...props }, ref) => (
  <HoverCardPrimitive.Content
    ref={ref}
    align={align}
    sideOffset={sideOffset}
    className={cn(
      "z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
    )}
    {...props}
  />
));
HoverCardContent.displayName = HoverCardPrimitive.Content.displayName;

// Create a UserHoverCard component for @mentions
export interface UserHoverCardProps {
  username: string;
  userData?: UserData; // Optional user data for pre-fetched data or cached data
  children: React.ReactNode;
  openDelay?: number;
  closeDelay?: number;
}

export const UserHoverCard = ({ username, userData, children, openDelay = 200, closeDelay = 200 }: UserHoverCardProps) => {
  const [bannerLoading, setBannerLoading] = useState(true);
  const [isVerifiedBadgeLoading, setIsVerifiedBadgeLoading] = useState(true);

  return (
    <HoverCard openDelay={openDelay} closeDelay={closeDelay}>
      <HoverCardTrigger asChild>
        <span className="inline-block">{children}</span>
      </HoverCardTrigger>
      <HoverCardContent className="w-80 p-0 overflow-hidden border border-solid border-gray-300 dark:border-white/20">
        {userData ? (
          <div className="flex flex-col">
            <div className="relative h-20 w-full overflow-hidden z-0">
              {userData.coverBannerType === 'video' ? (
                <Skeleton loading={bannerLoading} width="100%" height="200px">
                  <video
                    src={userData.coverBanner}
                    autoPlay
                    muted
                    loop
                    playsInline
                    className="w-full h-full object-cover"
                    onLoadedData={() => setBannerLoading(false)}
                    onError={() => setBannerLoading(false)}
                  />
                </Skeleton>
              ) : userData.coverBannerType === 'image' && userData.coverBanner ? (
                <Skeleton loading={bannerLoading} width="100%" height="200px">
                  <img
                    src={userData.coverBanner}
                    alt={`${userData.username}'s banner`}
                    className="w-full h-full object-cover"
                    onLoad={() => setBannerLoading(false)}
                    onError={() => setBannerLoading(false)}
                  />
                </Skeleton>
              ) : (
                <div className="w-full h-full bg-gradient-to-r from-blue-400 to-turquoise" />
              )}
            </div>
            <div className="px-4 pb-4 -mt-8">
              <div className="flex items-center mb-2 relative z-[1] gap-2">
                <Link href={`/user/${userData.username}`} className="block">
                  <img
                    src={userData.profilePhoto || "/images/user/default-avatar.webp"}
                    alt={userData.displayName || userData.username}
                    className="w-16 h-16 rounded-full border-4 border-white dark:border-black object-cover"
                  />
                </Link>
                <div>
                  <Link href={`/user/${userData.username}`} className="block">
                    <h3 className="flex items-center font-bold text-base hover:text-turquoise gap-1 transition-colors">
                      {userData.displayName || userData.username} {userData.isVerified && (
                        <Skeleton loading={isVerifiedBadgeLoading} width="16px" height="16px" className="inline-block">
                          <img className="w-4 h-4 user-post-verified-badge" alt="Verified User" src="/images/user/verified.png" onError={() => setIsVerifiedBadgeLoading(false)} onLoad={() => setIsVerifiedBadgeLoading(false)} />
                        </Skeleton>
                      )}
                    </h3>
                  </Link>
                  <Link href={`/user/${userData.username}`} className="block">
                    <p className="text-sm text-gray-500 dark:text-gray-400 hover:text-turquoise dark:hover:text-turquoise transition-colors">
                      @{userData.username}
                    </p>
                  </Link>
                </div>
              </div>

              {userData.bio && (
                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                  {truncateText(userData.bio, 150)}
                </p>
              )}

              <div className="space-y-1">
                <div className="flex items-center justify-between gap-4 mt-4 text-sm w-full">
                  <a href={`/user/${userData.username}/following`} className="hover:text-turquoise flex flex-col items-center justify-center gap-1">
                    <span className="font-semibold">{userData.photoCount || 0}</span> <span>Photos</span>
                  </a>
                  <a href={`/user/${userData.username}/followers`} className="hover:text-turquoise flex flex-col items-center justify-center gap-1">
                    <span className="font-semibold">{userData.videoCount || 0}</span> <span>Videos</span>
                  </a>
                  <a href={`/user/${userData.username}/followers`} className="hover:text-turquoise flex flex-col items-center justify-center gap-1">
                    <span className="font-semibold">{userData.clipCount || 0}</span> <span>Clips</span>
                  </a>
                  <a href={`/user/${userData.username}/followers`} className="hover:text-turquoise flex flex-col items-center justify-center gap-1">
                    <span className="font-semibold">{userData.gifCount || 0}</span> <span>GIFs</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="p-4">Loading user information...</div>
        )}
      </HoverCardContent>
    </HoverCard>
  );
};

export { HoverCard, HoverCardTrigger, HoverCardContent };
