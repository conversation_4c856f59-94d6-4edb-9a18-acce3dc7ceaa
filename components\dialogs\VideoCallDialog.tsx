import React from "react";
import { AnimatedModal } from "../ui/animated-modal";

interface VideoCallDialogProps {
  open: boolean;
  onClose: () => void;
  onAddFunds: () => void;
  onSelectSource: () => void;
  balance: number;
  pricePerMinute: number;
  minCallTime: number;
  username: string;
  shareCamera: boolean;
  setShareCamera: (v: boolean) => void;
  shareAudio: boolean;
  setShareAudio: (v: boolean) => void;
}

export const VideoCallDialog: React.FC<VideoCallDialogProps> = ({
  open,
  onClose,
  onAddFunds,
  onSelectSource,
  balance,
  pricePerMinute,
  minCallTime,
  username,
  shareCamera,
  setShareCamera,
  shareAudio,
  setShareAudio,
}) => {
  const total = (pricePerMinute * minCallTime).toFixed(2);

  // Footer for the modal (optional, you can customize as needed)
  const footer = (
    <>
      <button
        className="w-full flex items-center justify-center gap-2 bg-black/5 dark:bg-white/5 text-[#18181b] dark:text-white rounded-lg py-2 mb-4 font-medium hover:bg-gray-300 dark:hover:bg-zinc-700 transition"
        onClick={onSelectSource}
        type="button"
      >
        <svg
          className="w-5 h-5 fill-[#18181b] dark:fill-white"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
        >
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" />
        </svg>
        <span>Select Source</span>
      </button>
      <button
        className="w-full !bg-turquoise hover:bg-turquoise/85 text-white rounded-lg py-2 font-semibold text-lg transition"
        onClick={onAddFunds}
        type="button"
      >
        Add funds
      </button>
    </>
  );

  return (
    <AnimatedModal
      open={open}
      onOpenChange={(v) => !v && onClose()}
      title={`Video Call with ${username}`}
      footer={footer}
      closeButton={true}
      size="md"
    >
      <div className="px-3">
        <div className="mb-4">
          <div className="flex justify-between items-center bg-black/5 dark:bg-white/5 rounded-lg px-4 py-3 mb-2">
            <span className="text-base text-[#18181b] dark:text-white font-medium">
              Price Per Minute:
            </span>
            <span className="text-base text-[#18181b] dark:text-white font-semibold">
              ${pricePerMinute.toFixed(2)}
            </span>
          </div>
          <div className="flex justify-between items-center bg-black/5 dark:bg-white/5 rounded-lg px-4 py-3">
            <span className="text-base text-[#18181b] dark:text-white font-medium">
              Minimum Call Time:
            </span>
            <span className="text-base text-[#18181b] dark:text-white font-semibold">
              {minCallTime} Minute(s)
            </span>
          </div>
        </div>
        <div className="mb-4 border border-turquoise rounded-lg p-4 bg-white/5 dark:bg-[#18181b]/5">
          <div className="flex items-center justify-between mb-2">
            <span className="flex items-center gap-2 text-[#18181b] dark:text-white font-medium">
              <svg
                className="w-5 h-5 fill-[#18181b] dark:fill-white"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
              >
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" />
              </svg>
              Wallet
              <button
                className="text-turquoise hover:underline ml-2"
                onClick={onAddFunds}
                type="button"
              >
                (Top Up)
              </button>
            </span>
            <span className="text-[#18181b] dark:text-white font-semibold">
              Balance: <span className="font-bold">${balance.toFixed(2)}</span>
            </span>
          </div>
        </div>
        <p className="text-xs text-gray-700 dark:text-gray-300 mb-4">
          You&apos;ll be charged <span className="font-semibold">${total}</span> for the first {minCallTime} minutes from your wallet. Extra minutes will incur a ${pricePerMinute.toFixed(2)} charge, calculated at 15-second intervals. If the connection drops, you will still be able to use the remaining minutes.
        </p>
        <div className="flex flex-col gap-3 mb-4">
          <label className="flex items-center gap-2 cursor-pointer">
            <div className="relative">
              <input
                type="checkbox"
                checked={shareCamera}
                onChange={() => setShareCamera(!shareCamera)}
                className="sr-only"
              />
              <div
                className={`w-10 h-5 rounded-full transition-colors duration-200 ${shareCamera ? "bg-turquoise" : "bg-gray-400 dark:bg-gray-600"
                  }`}
              >
                <div
                  className={`absolute top-0.5 left-0.5 w-4 h-4 bg-white rounded-full transition-transform duration-200 ${shareCamera ? "translate-x-5" : "translate-x-0"
                    }`}
                />
              </div>
            </div>
            <span className="text-[#18181b] dark:text-white">Share my camera</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <div className="relative">
              <input
                type="checkbox"
                checked={shareAudio}
                onChange={() => setShareAudio(!shareAudio)}
                className="sr-only"
              />
              <div
                className={`w-10 h-5 rounded-full transition-colors duration-200 ${shareAudio ? "bg-turquoise" : "bg-gray-400 dark:bg-gray-600"
                  }`}
              >
                <div
                  className={`absolute top-0.5 left-0.5 w-4 h-4 bg-white rounded-full transition-transform duration-200 ${shareAudio ? "translate-x-5" : "translate-x-0"
                    }`}
                />
              </div>
            </div>
            <span className="text-[#18181b] dark:text-white">Share my audio</span>
          </label>
        </div>

      </div>
    </AnimatedModal>
  );
};