// pages/api/mark-as-read.js
import { createClient } from '@supabase/supabase-js';
import { io } from 'socket.io-client';

const socket = io('https://beneficial-mirelle-websocket-server-8923033b.koyeb.app/');

const mmSupabase = createClient(process.env.MM_SUPABASE_URL, process.env.MM_SUPABASE_API_KEY);

export default async function handler(req, res) {
    const { headers } = req;
    const authorizationHeader = headers['authorization'];
    const secretToken = process.env.NEXTAUTH_SECRET;

    if (authorizationHeader !== `Bearer ${secretToken}`) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
    }

    if (req.method === 'POST') {
        try {
            const { messageIds } = req.body;

            const { error: updateError } = await mmSupabase
                .from('Messages')
                .update({ recipient_status: 'read' })
                .in('message_id', messageIds);

            if (updateError) throw updateError;

            const { data: updatedMessages } = await mmSupabase
                .from('Messages')
                .select('conversation_id')
                .in('message_id', messageIds);

            const conversationId = updatedMessages[0]?.conversation_id;

            socket.emit('messageRead', { messageIds, conversationId });

            res.status(200).json({ message: 'Messages marked as read', success: true });
        } catch (error) {
            console.error('Error marking messages as read:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}