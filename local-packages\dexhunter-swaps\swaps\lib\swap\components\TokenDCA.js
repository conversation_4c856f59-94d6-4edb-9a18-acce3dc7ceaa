import { jsxs as s, jsx as e } from "react/jsx-runtime";
import { C as x } from "../../index.esm-fb2f5862.js";
import { cn as n } from "../../lib/utils.js";
import f from "../../assets/svg/IconChevronDown.js";
import { useState as u, useMemo as g } from "react";
import b from "../../store/useStore.js";
import v, { intervalOptions as N } from "../../orders/components/Filters/DCAInterval.js";
import { Input as y } from "../../components/ui/input.js";
import { formatNumber as w } from "../../utils/formatNumber.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "./tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../components/ui/dropdown-menu.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-c8f2666b.js";
import "../../index-bf605d8a.js";
import "../../index-1fe761a6.js";
import "../../index-67500cd3.js";
import "../../index-27cadef5.js";
import "../../index-4914f99c.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-5116e957.js";
import "../../lib.js";
import "../../assets/svg/IconCheck.js";
import "../../createLucideIcon-7a477fa6.js";
const os = () => {
  const {
    sellAmount: l,
    tokenSell: d,
    timesAmount: h,
    setTimesAmout: m,
    buyInterval: r,
    setBuyInterval: p,
    intervalLength: i
  } = b((t) => t.swapSlice), [c, o] = u(!1), a = g(() => N.find((t) => t.value === r), [i, r]);
  return /* @__PURE__ */ s("div", { className: "dhs-flex dhs-flex-col dhs-gap-[6px]", children: [
    /* @__PURE__ */ s("div", { className: "dhs-flex dhs-font-proximaSemiBold dhs-gap-2.5", children: [
      /* @__PURE__ */ s("div", { className: "dhs-flex dhs-flex-col dhs-gap-y-[5px] dhs-w-[50%]", children: [
        /* @__PURE__ */ e("span", { className: "dhs-pl-8 dhs-sm:pl-6 dhs-text-gray-103 dhs-text-sm dhs-tracking-[-0.175px]", children: "Every" }),
        /* @__PURE__ */ e("div", { className: "dhs-flex dhs-gap-3 dhs-items-center dhs-justify-between dhs-border-2 dhs-border-solid dhs-border-gray-110 dhs-p-[6px] dhs-h-[50px] dhs-rounded-[21px] dhs-text-xl dhs-leading-none dhs-text-white dhs-tracking-[-0.56px]", children: /* @__PURE__ */ e(
          v,
          {
            buyAmount: r,
            selectedValue: r,
            onSetSelected: p,
            className: "dhs-text-gray-101 dhs-font-proximaSemiBold dhs-w-full dhs-text-sm dhs-tracking-normal dhs-rounded-[15px]",
            children: /* @__PURE__ */ s("div", { className: "dhs-flex dhs-h-[38px] dhs-items-center dhs-justify-between dhs-rounded-[15px] dhs-border-input dhs-bg-gray-114/50 dhs-px-5 dhs-py-3 dhs-text-xs dhs-shadow-sm dhs-ring-offset-background dhs-placeholder:text-muted-foreground dhs-focus:outline-none dhs-disabled:cursor-not-allowed dhs-disabled:opacity-50 dhs-cursor-pointer", children: [
              /* @__PURE__ */ e("div", { className: "dhs-flex dhs-items-center dhs-gap-2 dhs-pl-1.5 dhs-text-mainText", children: /* @__PURE__ */ s("span", { children: [
                a == null ? void 0 : a.title,
                i > 1 && "s"
              ] }) }),
              /* @__PURE__ */ e(
                f,
                {
                  className: `dhs-sm:ml-0 dhs-text-subText ${c ? "dhs-rotate-180 dhs-transition dhs-duration-150" : "dhs-transition dhs-duration-150"}`,
                  width: 9,
                  height: 6,
                  onMouseEnter: () => o(!0),
                  onMouseLeave: () => o(!1)
                }
              )
            ] })
          }
        ) })
      ] }),
      /* @__PURE__ */ s("div", { className: "dhs-flex dhs-flex-col dhs-gap-y-[5px] dhs-w-[50%]", children: [
        /* @__PURE__ */ e("span", { className: "dhs-pl-8 dhs-sm:pl-6 dhs-text-gray-103 dhs-text-sm dhs-tracking-[-0.175px]", children: "For" }),
        /* @__PURE__ */ s("div", { className: "dhs-flex dhs-items-center dhs-border-2 dhs-border-solid dhs-border-gray-110 dhs-p-[6px] dhs-h-[50px] dhs-pl-8 dhs-sm:pl-6 dhs-rounded-[21px] dhs-text-xl dhs-leading-none dhs-text-white dhs-tracking-[-0.56px]", children: [
          /* @__PURE__ */ e(
            x,
            {
              type: "text",
              placeholder: "0",
              className: n(
                "dhs-px-0 dhs-text-mainText dhs-border-none dhs-border-transparent dhs-focus-visible:border-transparent dhs-focus-visible:ring-0 dhs-focus-visible:ring-offset-0 dhs-bg-transparent dhs-w-full dhs-sm:h-7.5"
              ),
              maxLength: 12,
              allowNegativeValue: !1,
              decimalsLimit: 8,
              intlConfig: { locale: "en-US", currency: "" },
              value: h,
              onValueChange: (t) => m(Math.max(0, Number(t || 0)))
            }
          ),
          /* @__PURE__ */ e(
            y,
            {
              type: "text",
              placeholder: "0",
              className: n(
                "dhs-px-0 dhs-text-subText dhs-border-none dhs-border-transparent dhs-focus-visible:border-transparent dhs-focus-visible:ring-0 dhs-focus-visible:ring-offset-0 dhs-bg-transparent dhs-w-[60px] dhs-sm:h-7.5"
              ),
              value: "Times",
              disabled: !0
            }
          )
        ] })
      ] })
    ] }),
    /* @__PURE__ */ e("div", { className: "dhs-w-full dhs-flex dhs-flex-col dhs-justify-between dhs-text-gray-103 dhs-font-proximaSemiBold dhs-tracking-[-0.175px] dhs-text-xs dhs-px-2 dhs-mt-[5px] dhs-sm:text-xxs", children: /* @__PURE__ */ s("div", { className: "dhs-flex dhs-gap-1 dhs-justify-between dhs-text-xs", children: [
      /* @__PURE__ */ s("div", { className: "dhs-col-span-1 dhs-gap-2 dhs-flex dhs-items-start dhs-sm:gap-[2px]", children: [
        /* @__PURE__ */ e("span", { className: "dhs-text-gray-103 dhs-font-semibold dhs-sm:font-proximaRegular", children: "Total:" }),
        /* @__PURE__ */ s("span", { className: "dhs-text-white dhs-sm:text-gray-101", children: [
          (d == null ? void 0 : d.ticker) === "ADA" && "₳ ",
          w(h * Number(l)),
          " ",
          (d == null ? void 0 : d.ticker) !== "ADA" && (d == null ? void 0 : d.ticker)
        ] })
      ] }),
      /* @__PURE__ */ s("div", { className: "dhs-col-span-1 dhs-gap-2 dhs-flex dhs-items-start dhs-sm:gap-[2px]", children: [
        /* @__PURE__ */ e("span", { className: "dhs-text-gray-103 dhs-font-semibold dhs-sm:font-proximaRegular", children: "Dexes:" }),
        /* @__PURE__ */ s("span", { className: "dhs-text-white dhs-sm:text-gray-101", children: [
          "₳ ",
          2 * h
        ] })
      ] }),
      /* @__PURE__ */ s("div", { className: "dhs-col-span-1 dhs-gap-2 dhs-flex dhs-items-start dhs-sm:gap-[2px]", children: [
        /* @__PURE__ */ e("span", { className: "dhs-text-gray-103 dhs-font-semibold dhs-sm:font-proximaRegular", children: "Returned:" }),
        /* @__PURE__ */ s("span", { className: "dhs-text-white dhs-sm:text-gray-101", children: [
          "₳ ",
          5.5 + 2 * h
        ] })
      ] }),
      /* @__PURE__ */ s("div", { className: "dhs-col-span-1 dhs-gap-2 dhs-flex dhs-items-start dhs-sm:gap-[2px]", children: [
        /* @__PURE__ */ e("span", { className: "dhs-text-gray-103 dhs-font-semibold dhs-sm:font-proximaRegular", children: "Service" }),
        /* @__PURE__ */ e("span", { className: "dhs-text-white dhs-sm:text-gray-101", children: "0.85%" })
      ] })
    ] }) })
  ] });
};
export {
  os as default
};
