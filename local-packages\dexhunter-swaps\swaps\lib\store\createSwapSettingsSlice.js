import { p } from "../immer-548168ec.js";
const a = (t) => ({
  isDexSplitting: !0,
  localRouting: !1,
  isCustomSlippage: !0,
  isAutomaticSlippage: !1,
  inputcustomSlippage: "2.5",
  inputTransactionDeadline: "30",
  slippage: 0,
  isOpenSwapSetting: !1,
  isDetailsOpen: "",
  isPriceFlipped: !1,
  isOpenSwapOverview: !1,
  setisDexSplitting: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.isDexSplitting = i;
      })
    );
  },
  setLocalRouting: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.localRouting = i;
      })
    );
  },
  setIsCustomSlippage: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.isCustomSlippage = i;
      })
    );
  },
  setInputCustomSlippage: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.inputcustomSlippage = i;
      })
    );
  },
  setInputTransactionDeadline: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.inputTransactionDeadline = i;
      })
    );
  },
  setSlippage: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.slippage = i;
      })
    );
  },
  setIsOpenSwapSetting: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.isOpenSwapSetting = i;
      })
    );
  },
  setIsDetailsOpen: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.isDetailsOpen = i;
      })
    );
  },
  setIsPriceFlipped: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.isPriceFlipped = i;
      })
    );
  },
  setIsOpenSwapOverview: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.isOpenSwapOverview = i;
      })
    );
  },
  setIsAutomaticSlippage: (i) => {
    t(
      p((e) => {
        e.swapSettingsSlice.isAutomaticSlippage = i;
      })
    );
  }
});
export {
  a as default
};
