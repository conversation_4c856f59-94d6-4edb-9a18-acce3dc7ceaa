// api/socket/route.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { initializeSocket } from '@/lib/socketInstance';

declare module 'net' {
  interface Socket {
    server: any;
  }
}

const ioHandler = (req: NextApiRequest, res: NextApiResponse) => {
  if (!(res?.socket?.server as any).io) {
    const io = initializeSocket(res?.socket?.server as any);
    io.on('connection', (socket: any) => {
      console.log(`Socket ${socket.id} connected`);

      socket.on('joinRoom', (room: any) => {
        socket.join(room);
        console.log(`Socket ${socket.id} joined room ${room}`);
      });

      socket.on('leaveRoom', (room: any) => {
        socket.leave(room);
        console.log(`Socket ${socket.id} left room ${room}`);
      });
    });
    const server = (res.socket as any).server;
    server.io = io;
  }
  res.end();
};
export default ioHandler;