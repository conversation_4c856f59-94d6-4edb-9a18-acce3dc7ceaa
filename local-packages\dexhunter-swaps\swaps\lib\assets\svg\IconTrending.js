import { jsx as C } from "react/jsx-runtime";
import { memo as t } from "react";
const e = (o) => /* @__PURE__ */ C(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 20 24",
    fill: "none",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M3.69349 21.0291C4.61838 21.8456 5.71573 22.4242 6.898 22.7145C7.39713 22.8588 7.79524 22.5549 7.9518 22.2643C8.09767 21.9936 8.14931 21.53 7.80404 21.176L7.80407 21.176L7.79862 21.1705C6.84739 20.2134 6.67216 19.2805 6.78145 18.4849C6.8972 17.6422 7.34621 16.8836 7.74758 16.398L7.74792 16.3976C8.29282 15.7375 9.1071 14.642 9.3749 13.1741C9.97408 13.8182 10.4017 14.6951 10.546 15.4656L10.8382 17.0246L11.9412 15.8848C12.1426 15.6767 12.293 15.4396 12.4037 15.1926C12.714 15.7241 12.9961 16.3958 13.1197 17.1332C13.3332 18.4067 13.0791 19.8738 11.6128 21.2118L11.5909 21.2318L11.5705 21.2533C11.2398 21.6021 11.2963 22.037 11.3945 22.2674C11.4483 22.3935 11.5535 22.5624 11.7463 22.6845C11.9425 22.8086 12.2166 22.8679 12.4903 22.7743C13.61 22.4593 14.6606 21.9238 15.5869 21.198L15.593 21.1932L15.599 21.1883C18.6657 18.6907 19.1663 15.2261 18.6344 12.1556C18.1073 9.11306 16.5539 6.3203 15.2868 4.89238L15.2761 4.88033L15.265 4.86872C14.4494 4.01928 13.0215 4.59171 13.0069 5.76941C12.999 5.97689 12.9784 6.18186 12.9444 6.38034C12.0212 4.3748 10.391 2.42995 8.26241 1.32593C7.3299 0.841449 6.29312 1.60274 6.35394 2.58291C6.37879 4.01729 5.95362 5.20359 5.28632 6.38227C4.94737 6.98096 4.54981 7.57106 4.11565 8.19473C4.01895 8.33364 3.92011 8.47466 3.81985 8.61771C3.47614 9.10809 3.11574 9.62229 2.76746 10.1572L2.76745 10.1571L2.76497 10.161C1.9157 11.4814 1.21372 13.3506 1.18118 15.3101C1.14823 17.2938 1.80571 19.3862 3.69349 21.0291Z",
        stroke: "currentColor",
        strokeWidth: 1.64
      }
    )
  }
), m = t(e);
export {
  m as default
};
