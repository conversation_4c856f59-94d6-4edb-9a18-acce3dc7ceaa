import { v } from "convex/values";
import { mutation, query } from "../_generated/server";

export const migrateUserIds = mutation({
  args: {},
  handler: async (ctx) => {
    // Get all accounts
    const accounts = await ctx.db.query("Accounts").collect();

    // For each account, create a new document with the correct structure
    for (const account of accounts) {
      const { user_id, ...rest } = account;

      // Create new document with the same data but without user_id
      // Insert new document with user_id as the primary key, omitting _id (Convex will generate it)
      await ctx.db.insert("Accounts", {
        ...rest,
        user_id, // Retain user_id as a field if needed by schema
      });

      // Delete the old document
      await ctx.db.delete(account._id);
    }

    return { success: true, message: "Migration completed" };
  },
});

export const exportTable = query({
  args: { table: v.string() },
  handler: async (ctx, args) => {
    const records = await ctx.db.query(args.table).collect();
    // Exclude all fields with an _ prefix
    const sanitized = records.map((record: any) => {
      const clean: any = {};
      for (const key in record) {
        if (!key.startsWith('_') || key.includes('auth_user_id')) {
          clean[key] = record[key];
        }
      }
      return clean;
    });
    return sanitized;
  },
});

export const importTable = mutation({
  args: {
    table: v.string(),
    records: v.array(v.any()), // Accepts an array of objects
  },
  handler: async (ctx, args) => {
    let inserted = 0;
    for (const record of args.records) {
      // Insert as new (no upsert logic, as unique key is not known generically)
      await ctx.db.insert(args.table, record);
      inserted++;
    }
    return { inserted, total: args.records.length };
  },
});