import { p as t } from "../immer-548168ec.js";
import { DEFAULT_TOKEN as k } from "../swap/components/tokens.js";
const i = {
  name: "",
  tokens: [{
    tokenId: k.token_id,
    pct: 0
  }]
}, c = (s) => ({
  isCreatingBasket: !1,
  isAddBasketOpen: !1,
  isSwapBasketOpen: !1,
  swapType: "BUY",
  totalAmount: 0,
  swapDetails: null,
  estimationError: "",
  isSwapDetailsLoading: !1,
  selectedBasketId: "",
  creatingBasketInfo: i,
  activeTokenIndex: 0,
  basketList: [],
  basketBalance: 0,
  isBasketBalanceLoading: !1,
  toggleIsAddBasketOpen: () => {
    s(
      t((e) => {
        e.basketSlice.isAddBasketOpen = !e.basketSlice.isAddBasketOpen, e.globalSettingsSlice.isShortcutsDisabled = !e.basketSlice.isAddBasketOpen;
      })
    );
  },
  toggleIsSwapBasketOpen: () => {
    s(
      t((e) => {
        e.basketSlice.isSwapBasketOpen = !e.basketSlice.isSwapBasketOpen;
      })
    );
  },
  setIsAddBasketOpen: (e) => {
    s(
      t((a) => {
        a.basketSlice.isAddBasketOpen = e;
      })
    );
  },
  setIsSwapBasketOpen: (e) => {
    s(
      t((a) => {
        a.basketSlice.isSwapBasketOpen = e;
      })
    );
  },
  setSwapType: (e) => {
    s(
      t((a) => {
        a.basketSlice.swapType = e;
      })
    );
  },
  setTotalAmount: (e) => {
    s(
      t((a) => {
        a.basketSlice.totalAmount = e;
      })
    );
  },
  setSwapDetails: (e) => {
    s(
      t((a) => {
        a.basketSlice.swapDetails = e;
      })
    );
  },
  setEstimationError: (e) => {
    s(
      t((a) => {
        a.basketSlice.estimationError = e;
      })
    );
  },
  setIsSwapDetailsLoading: (e) => {
    s(
      t((a) => {
        a.basketSlice.isSwapDetailsLoading = e;
      })
    );
  },
  setSelectedBasketId: (e) => {
    s(
      t((a) => {
        a.basketSlice.selectedBasketId = e;
      })
    );
  },
  setCreatingBasketInfo: (e) => {
    s(
      t((a) => {
        a.basketSlice.creatingBasketInfo = e;
      })
    );
  },
  setActiveTokenIndex: (e) => {
    s(
      t((a) => {
        a.basketSlice.activeTokenIndex = e;
      })
    );
  },
  setIsCreatingBasket: (e) => {
    s(
      t((a) => {
        a.basketSlice.isCreatingBasket = e;
      })
    );
  },
  resetCreatingBasketInfo: () => {
    s(
      t((e) => {
        e.basketSlice.creatingBasketInfo = i;
      })
    );
  },
  setBasketList: (e) => {
    s(
      t((a) => {
        a.basketSlice.basketList = e;
      })
    );
  },
  setBasketBalance: (e) => {
    s(
      t((a) => {
        a.basketSlice.basketBalance = e;
      })
    );
  },
  setIsBasketBalanceLoading: (e) => {
    s(
      t((a) => {
        a.basketSlice.isBasketBalanceLoading = e;
      })
    );
  }
});
export {
  c as default
};
