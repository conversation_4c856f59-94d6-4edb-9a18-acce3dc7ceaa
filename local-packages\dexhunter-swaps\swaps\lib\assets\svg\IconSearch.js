import { jsxs as e, jsx as o } from "react/jsx-runtime";
import { memo as t } from "react";
const s = (r) => /* @__PURE__ */ e(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 16 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...r,
    children: [
      /* @__PURE__ */ o(
        "circle",
        {
          cx: 7.65,
          cy: 7.65,
          r: 6.65,
          stroke: "currentColor",
          strokeWidth: 1.86667
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M12.5498 12.55L14.9998 15",
          stroke: "currentColor",
          strokeWidth: 1.86667,
          strokeLinecap: "round"
        }
      )
    ]
  }
), n = t(s);
export {
  n as default
};
