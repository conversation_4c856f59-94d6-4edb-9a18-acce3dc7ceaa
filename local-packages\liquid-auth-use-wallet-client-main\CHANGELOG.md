# [1.2.0](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/compare/v1.1.0...v1.2.0) (2025-01-29)


### Features

* upgrade to  algosdk v3 ([7206908](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/7206908e05f644b1aa6908aacbd13807861f3821))

# [1.2.0-canary.1](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/compare/v1.1.0...v1.2.0-canary.1) (2025-01-29)


### Features

* upgrade to  algosdk v3 ([7206908](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/7206908e05f644b1aa6908aacbd13807861f3821))

# [1.1.0](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/compare/v1.0.0...v1.1.0) (2024-10-15)


### Bug Fixes

* npm package ([d4a544a](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/d4a544a6d56daf7108b0199137668b179061b46b))


### Features

* adds doc ([f25e1af](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/f25e1aff09e9b5b87d8416eb4d47ae3a15c34810))

# [1.1.0-canary.1](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/compare/v1.0.0...v1.1.0-canary.1) (2024-10-15)


### Bug Fixes

* npm package ([d4a544a](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/d4a544a6d56daf7108b0199137668b179061b46b))


### Features

* adds doc ([f25e1af](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/f25e1aff09e9b5b87d8416eb4d47ae3a15c34810))

# 1.0.0 (2024-10-10)


### Bug Fixes

* add extension ([f692afb](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/f692afbe1f9a6fdac2ef890d2e083d3b03cac8a5))
* ensure close button also triggers cleanup ([5c12328](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/5c123287e7820b7d386b379ce95352043ec66ce0))
* make type declarations available ([8fa57e0](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/8fa57e0f5435a6aaff8d05f999a8ae9c87a7875c))
* path ([076ef10](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/076ef102d93ec5b17af4f5a519b84e18a26706f0))
* path to types/index ([a73524b](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/a73524b2d50aa9c4bf9867eb01d35169bc0a6d0e))
* tsconfig ([9fbd473](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/9fbd47353af25b0ddab31ff9c08fdeb56e065b4c))


### Features

* add [@algorandfoundation](https://github.com/algorandfoundation) at the start + removes postinstall script ([6468046](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/6468046d155077c758e74ca1b7e7adee26aa005c))
* adds postinstall script ([a62440b](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/a62440b794650f3b0b063dd5269c23beac1c0810))
* first commit ([8bed96e](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/8bed96ea700ffaec495cee6d1a076c60c3b8aade))

# [1.0.0-canary.2](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/compare/v1.0.0-canary.1...v1.0.0-canary.2) (2024-10-04)


### Bug Fixes

* ensure close button also triggers cleanup ([5c12328](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/5c123287e7820b7d386b379ce95352043ec66ce0))

# 1.0.0-canary.1 (2024-10-02)


### Bug Fixes

* add extension ([f692afb](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/f692afbe1f9a6fdac2ef890d2e083d3b03cac8a5))
* make type declarations available ([8fa57e0](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/8fa57e0f5435a6aaff8d05f999a8ae9c87a7875c))
* path ([076ef10](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/076ef102d93ec5b17af4f5a519b84e18a26706f0))
* path to types/index ([a73524b](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/a73524b2d50aa9c4bf9867eb01d35169bc0a6d0e))
* tsconfig ([9fbd473](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/9fbd47353af25b0ddab31ff9c08fdeb56e065b4c))


### Features

* add [@algorandfoundation](https://github.com/algorandfoundation) at the start + removes postinstall script ([6468046](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/6468046d155077c758e74ca1b7e7adee26aa005c))
* adds postinstall script ([a62440b](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/a62440b794650f3b0b063dd5269c23beac1c0810))
* first commit ([8bed96e](https://github.com/algorandfoundation/liquid-auth-use-wallet-client/commit/8bed96ea700ffaec495cee6d1a076c60c3b8aade))
