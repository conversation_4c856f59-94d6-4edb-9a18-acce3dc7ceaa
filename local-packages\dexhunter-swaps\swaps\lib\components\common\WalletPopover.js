import { jsx as s, jsxs as t } from "react/jsx-runtime";
import { useState as y, useEffect as N, useMemo as p } from "react";
import w from "../../store/useStore.js";
import { Checkbox as S } from "../ui/checkbox.js";
import { useWalletConnect as T } from "../../hooks/useWalletConnect.js";
import { cn as x } from "../../lib/utils.js";
import { wallets as W } from "../../constants/wallets.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../swap/components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../index-1c873780.js";
import "../../index-563d1ed8.js";
import "../../index-c7156e07.js";
import "../../index-6460524a.js";
import "../../index-bcfeaad9.js";
import "../../index-5116e957.js";
import "../../index-c8f2666b.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../createLucideIcon-7a477fa6.js";
import "../../utils/cardanoUtils.js";
import "../../index-ca8eb9e1.js";
import "../../config/axios.js";
import "../../axios-ddd885c5.js";
const is = ({ closeOpenWallet: a }) => {
  const [d, n] = y(!1), { isLoadingWallet: l, selectedWallet: o, resetWallet: f, availableWallets: r } = w(
    (e) => e.walletSlice
  ), { connectWallet: u } = T(), b = (e) => {
    d && u(e).then(() => {
      a && a();
    });
  }, m = (e) => {
    e.stopPropagation(), e.preventDefault(), n(!d), localStorage.setItem("acceptedTerms", JSON.stringify(!d));
  };
  N(() => {
    const e = localStorage.getItem("acceptedTerms");
    e && n(JSON.parse(e));
  }, []);
  const i = p(() => {
    const e = r.length === 0 ? W : r;
    return /* @__PURE__ */ s("div", { className: "@sm/appRoot:dhs-justify-between @sm/appRoot:dhs-ml-0 @sm/appRoot:dhs-gap-5 @md/appRoot:dhs-flex @md/appRoot:dhs-flex-wrap @md/appRoot:dhs-justify-start @md/appRoot:dhs-gap-4 @md/appRoot:dhs-ml-6 dhs-grid dhs-grid-cols-4 dhs-gap-y-2 dhs-max-h-[300px] dhs-overflow-auto", children: e.map((h, g) => {
      const c = l || !d, v = o === h.windowName;
      return /* @__PURE__ */ t(
        "div",
        {
          className: "dhs-flex dhs-flex-col dhs-items-center dhs-gap-2 dhs-col-span-1 sm:dhs-w-[60px]",
          onClick: () => b(h.windowName),
          children: [
            /* @__PURE__ */ s(
              "div",
              {
                className: x(
                  "dhs-flex dhs-items-center dhs-justify-center dhs-p-4 dhs-rounded-lg sm:dhs-rounded-xl dhs-bg-background sm:dhs-bg-gray-114 sm:dhs-bg-dhs-opacity-50 dhs-shadow-md dhs-cursor-pointer dhs-transition-all dhs-duration-200 dhs-border-2 dhs-border-transparent",
                  c && "dhs-opacity-50 dhs-cursor-not-allowed hover:dhs-bg-background",
                  v && "dhs-border-2 dhs-border-accent"
                ),
                children: /* @__PURE__ */ s(
                  "img",
                  {
                    src: `https://storage.googleapis.com/dexhunter-images/public/${h.iconName}.svg`,
                    alt: "",
                    className: "dhs-w-8 dhs-h-8 sm:dhs-w-[50px] sm:dhs-h-[50px]"
                  }
                )
              }
            ),
            /* @__PURE__ */ s(
              "span",
              {
                className: x(
                  "dhs-text-sm dhs-font-proximaSemiBold dhs-text-mainText dhs-font-semibold dhs-transition-all dhs-duration-200",
                  c && "dhs-text-subText dhs-cursor-not-allowed"
                ),
                children: h.prettyName
              }
            )
          ]
        },
        g
      );
    }) });
  }, [r, l, d, o]);
  return p(() => o ? /* @__PURE__ */ t("div", { className: "dhs-text-mainText dhs-py-2 dhs-px-4 dhs-w-full sm:dhs-px-0", children: [
    /* @__PURE__ */ s("div", { className: "dhs-text-left dhs-text-lg dhs-font-bold dhs-mb-4", children: "Change Wallet" }),
    /* @__PURE__ */ t("div", { className: "dhs-text-left dhs-text-md dhs-font-semibold dhs-mb-4 dhs-flex dhs-items-center", children: [
      /* @__PURE__ */ s("div", { className: "dhs-text-xs sm:dhs-text-sm sm:dhs-font-proximaSemiBold dhs-w-[20px] dhs-h-[20px] dhs-bg-accent dhs-rounded-[6px] dhs-inline-block dhs-mr-2 dhs-inline-flex dhs-justify-center dhs-items-center dhs-text-buttonText", children: "1" }),
      "Choose Wallet"
    ] }),
    i,
    /* @__PURE__ */ s(
      "div",
      {
        className: "dhs-flex dhs-flex-wrap dhs-text-md dhs-justify-start dhs-text-destructive dhs-mt-4 dhs-cursor-pointer hover:dhs-opacity-90 dhs-transition-all dhs-duration-200 dhs-font-semibold",
        onClick: f,
        children: "Disconnect Wallet"
      }
    )
  ] }) : /* @__PURE__ */ t("div", { className: "dhs-text-mainText dhs-py-3 dhs-px-7 dhs-w-ful", children: [
    /* @__PURE__ */ s("div", { className: "dhs-text-center @md/appRoot:dhs-text-lg dhs-font-bold dhs-mb-4 dhs-text-xl", children: "Connect Wallet" }),
    /* @__PURE__ */ t("div", { className: "dhs-text-left dhs-text-md dhs-font-semibold dhs-mb-2", children: [
      /* @__PURE__ */ s("div", { className: "@md/appRoot:dhs-text-xs dhs-text-sm dhs-font-proximaSemiBold dhs-w-[24px] dhs-h-[24px] dhs-bg-accent dhs-rounded-[6px] dhs-inline-block dhs-mr-2 dhs-inline-flex dhs-justify-center dhs-items-center dhs-text-buttonText", children: "1" }),
      /* @__PURE__ */ t("span", { className: "dhs-text-base dhs-font-proximaSemiBold dhs-text-mainText", children: [
        "Accept the",
        " ",
        /* @__PURE__ */ t(
          "span",
          {
            className: "dhs-text-accent dhs-cursor-pointer hover:dhs-opacity-90",
            onClick: () => window.open(
              "https://storage.googleapis.com/dexhunter-papers/DexHunter_Privacy_Policy.pdf"
            ),
            children: [
              "Privacy Policy",
              " "
            ]
          }
        ),
        /* @__PURE__ */ s(
          "a",
          {
            href: "/terms",
            className: "dhs-text-accent dhs-cursor-pointer hover:dhs-opacity-90 dhs-hidden sm:dhs-inline",
            children: "and T&U"
          }
        ),
        /* @__PURE__ */ s(
          "span",
          {
            className: "dhs-text-accent dhs-cursor-pointer hover:dhs-opacity-90 sm:dhs-hidden dhs-inline sm:dhs-text-accent",
            onClick: () => window.open(
              "https://storage.googleapis.com/dexhunter-papers/DexHunter_Terms_of_Use.pdf"
            ),
            children: "and Terms of Use"
          }
        )
      ] })
    ] }),
    " ",
    /* @__PURE__ */ t("div", { className: "dhs-flex dhs-justify-start dhs-items-center dhs-gap-2 dhs-md-3 dhs-ml-4 dhs-mb-4 @sm/appRoot:dhs-ml-6", children: [
      /* @__PURE__ */ s(
        S,
        {
          id: "terms",
          className: "dhs-border-gray dhs-border-2 dhs-w-[24px] dhs-h-[24px]",
          checked: d,
          onClick: m
        }
      ),
      /* @__PURE__ */ s(
        "label",
        {
          htmlFor: "terms",
          className: "dhs-text-sm dhs-font-medium dhs-font-proximaSemiBold dhs-leading-none dhs-peer-disabled:dhs-cursor-not-allowed dhs-peer-disabled:dhs-opacity-70 dhs-cursor-pointer dhs-max-w-[350px]",
          onClick: m,
          children: "I have read and accepted the terms of the DexHunter Privacy Policy and Terms of Use"
        }
      )
    ] }),
    /* @__PURE__ */ t("div", { className: "dhs-text-left dhs-text-base dhs-font-proximaSemiBold dhs-font-semibold dhs-mb-2 @sm/appRoot:dhs-mb-4", children: [
      /* @__PURE__ */ s("div", { className: "dhs-text-xs dhs-font-proximaSemiBold dhs-w-[24px] dhs-h-[24px] dhs-bg-accent dhs-rounded-[6px] dhs-inline-block dhs-mr-2 dhs-inline-flex dhs-justify-center dhs-items-center dhs-text-buttonText", children: "2" }),
      "Choose Wallet"
    ] }),
    i
  ] }), [i, o, d]);
};
export {
  is as default
};
