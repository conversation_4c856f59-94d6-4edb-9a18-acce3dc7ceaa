import React, { useRef, useState, useEffect } from 'react';
import { motion, useMotionValue, useAnimation, PanInfo } from 'framer-motion';
import Link from 'next/link';

const collabs = [
  { username: 'Kellegabe20', img: 'https://avatar.erome.com/2501/ygAnlrBr.jpeg?t=1748110943' },
  { username: '<PERSON>Wilko95', img: 'https://avatar.erome.com/17/qb847fRe.jpeg?t=1723055347' },
  { username: '<PERSON><PERSON>', img: 'https://avatar.erome.com/2737/UbpjNA75.jpeg?t=1752853113' },
  { username: '<PERSON><PERSON><PERSON>', img: 'https://avatar.erome.com/2654/EsGkO2ir.jpeg?t=1751368635' },
  { username: '<PERSON><PERSON><PERSON><PERSON>', img: 'https://avatar.erome.com/1886/Gibk90kA.jpeg?t=1734386490' },
  { username: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', img: 'https://avatar.erome.com/1386/SKDDvQwN.png?t=1711393763' },
  { username: 'EnjoyX', img: 'https://avatar.erome.com/2185/x7HfzP4c.png?t=1744494901' },
  { username: 'sweet_babe', img: 'https://avatar.erome.com/1510/FrQANkBv.png?t=1718017630' },
  { username: 'grimmylily', img: 'https://avatar.erome.com/1801/pAPZNszU.png?t=1731296260' },
  { username: 'Susy1011', img: 'https://avatar.erome.com/1659/IKY5f5mA.jpeg?t=1725217998' },
  { username: 'BlondeAuss...', img: 'https://avatar.erome.com/1326/7JClSHFo.jpeg?t=1708277907' },
  { username: 'Queen_Sara', img: 'https://avatar.erome.com/1840/Im6Z0kSt.png?t=1732721230' },
  { username: 'Kellegabe20', img: 'https://avatar.erome.com/2501/ygAnlrBr.jpeg?t=1748110943' },
  { username: 'LexiWilko95', img: 'https://avatar.erome.com/17/qb847fRe.jpeg?t=1723055347' },
  { username: 'Kristil', img: 'https://avatar.erome.com/2737/UbpjNA75.jpeg?t=1752853113' },
  { username: 'KittyKate', img: 'https://avatar.erome.com/2654/EsGkO2ir.jpeg?t=1751368635' },
  { username: 'RosieCozy', img: 'https://avatar.erome.com/1886/Gibk90kA.jpeg?t=1734386490' },
  { username: 'LyalitVelia', img: 'https://avatar.erome.com/1386/SKDDvQwN.png?t=1711393763' },
  { username: 'EnjoyX', img: 'https://avatar.erome.com/2185/x7HfzP4c.png?t=1744494901' },
  { username: 'sweet_babe', img: 'https://avatar.erome.com/1510/FrQANkBv.png?t=1718017630' },
  { username: 'grimmylily', img: 'https://avatar.erome.com/1801/pAPZNszU.png?t=1731296260' },
  { username: 'Susy1011', img: 'https://avatar.erome.com/1659/IKY5f5mA.jpeg?t=1725217998' },
  { username: 'BlondeAuss...', img: 'https://avatar.erome.com/1326/7JClSHFo.jpeg?t=1708277907' },
  { username: 'Queen_Sara', img: 'https://avatar.erome.com/1840/Im6Z0kSt.png?t=1732721230' },
];

export function CollabsCarousel() {
  const containerRef = useRef<HTMLDivElement>(null);
  const innerRef = useRef<HTMLDivElement>(null);
  const x = useMotionValue(0);
  const controls = useAnimation();
  const [containerWidth, setContainerWidth] = useState(0);
  const [scrollWidth, setScrollWidth] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [atStart, setAtStart] = useState(true);
  const [atEnd, setAtEnd] = useState(false);
  const [wasDragging, setWasDragging] = useState(false);

  useEffect(() => {
    const update = () => {
      if (containerRef.current && innerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
        setScrollWidth(innerRef.current.scrollWidth);
      }
    };
    update();
    window.addEventListener('resize', update);
    return () => window.removeEventListener('resize', update);
  }, []);

  const dragConstraints = {
    right: 0,
    left: scrollWidth > containerWidth ? -(scrollWidth - containerWidth) : 0,
  };

  useEffect(() => {
    const unsub = x.onChange((latest) => {
      setAtStart(Math.abs(latest) < 5);
      setAtEnd(dragConstraints.left < 0 && Math.abs(latest - dragConstraints.left) < 5);
    });
    return () => unsub();
  }, [x, dragConstraints.left]);

  const handleScroll = (direction: 'left' | 'right') => {
    const scrollAmount = Math.min(containerWidth * 0.7, 300);
    const currentX = x.get();
    let newX = direction === 'left' ? currentX + scrollAmount : currentX - scrollAmount;
    newX = Math.max(dragConstraints.left, Math.min(dragConstraints.right, newX));
    controls.start({
      x: newX,
      transition: { type: 'spring', stiffness: 400, damping: 35 },
    });
  };

  const handleDragStart = () => {
    setIsDragging(true);
    setWasDragging(false);
  };
  const handleDrag = () => {
    setWasDragging(true);
  };
  const handleDragEnd = (
    _: MouseEvent | TouchEvent | PointerEvent,
    info: PanInfo
  ) => {
    setIsDragging(false);
    const velocity = info.velocity.x;
    const currentX = x.get();
    if (Math.abs(velocity) > 500) {
      const projection = currentX + velocity * 0.2;
      const clamped = Math.max(dragConstraints.left, Math.min(dragConstraints.right, projection));
      controls.start({
        x: clamped,
        transition: { type: 'spring', velocity: velocity * 0.01, stiffness: 300, damping: 40 },
      });
    } else {
      const clamped = Math.max(dragConstraints.left, Math.min(dragConstraints.right, currentX));
      if (clamped !== currentX) {
        controls.start({
          x: clamped,
          transition: { type: 'spring', stiffness: 500, damping: 30 },
        });
      }
    }
  };

  return (
    <div className="relative w-full overflow-hidden" ref={containerRef}>
      {!atStart && (
        <button
          onClick={() => handleScroll('left')}
          className="absolute left-0 top-[40%] -translate-y-1/2 z-10 bg-white/80 dark:bg-[#18181b]/80 rounded-full w-8 h-8 flex items-center justify-center shadow border border-solid border-gray-300 dark:border-white/50"
        >
          ‹
        </button>
      )}
      <motion.div
        ref={innerRef}
        className="flex gap-[19.5px] py-2"
        drag="x"
        dragConstraints={dragConstraints}
        dragElastic={0.1}
        style={{ x }}
        animate={controls}
        onDragStart={handleDragStart}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        whileTap={{ cursor: 'grabbing' }}
      >
        {collabs.map((user, idx) => (
          <Link
            key={idx}
            href={`/user/${user.username}`}
            className="flex flex-col items-center min-w-[70px] pointer-events-auto"
            draggable={false}
            onClick={e => {
              if (wasDragging) e.preventDefault();
            }}
          >
            <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center">
              <img
                src={user.img}
                alt={user.username}
                className="w-full h-full object-cover rounded-full"
                draggable={false}
              />
            </div>
            <span className="mt-2 text-xs text-gorillaGray dark:text-white font-medium truncate max-w-[70px] text-center">
              {user.username}
            </span>
          </Link>
        ))}
      </motion.div>
      {!atEnd && (
        <button
          onClick={() => handleScroll('right')}
          className="absolute right-0 top-[40%] -translate-y-1/2 z-10 bg-white/80 dark:bg-[#18181b]/80 rounded-full w-8 h-8 flex items-center justify-center shadow border border-solid border-gray-300 dark:border-white/50"
        >
          ›
        </button>
      )}
    </div>
  );
}