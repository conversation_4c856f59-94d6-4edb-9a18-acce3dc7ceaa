import w from "../../store/useStore.js";
import { s as b } from "../../shallow-27fd7e97.js";
import { useMemo as a } from "react";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
const q = () => {
  const {
    tokenBuy: p,
    tokenPrice: r,
    swapDetails: o,
    adaUsdPrice: t,
    sellAmount: i,
    swapType: n,
    tokenSell: u,
    buyAmount: s,
    orderType: c
  } = w(
    (e) => ({
      tokenBuy: e.swapSlice.tokenBuy,
      tokenSell: e.swapSlice.tokenSell,
      tokenPrice: e.swapSlice.tokenPrice,
      swapDetails: e.swapSlice.swapDetails,
      sellAmount: e.swapSlice.sellAmount,
      buyAmount: e.swapSlice.buyAmount,
      swapType: e.tokenSearchSlice.swapType,
      adaUsdPrice: e.tokenSearchSlice.adaUsdPrice,
      orderType: e.swapSlice.orderType
    }),
    b
  ), { userTokens: l, balance: m } = w((e) => e.walletSlice), T = a(() => {
    var e;
    return (p == null ? void 0 : p.token_ascii) === "Cardano" ? m : (e = l.find((S) => S.token_id === (p == null ? void 0 : p.token_id))) == null ? void 0 : e.amount;
  }, [p, m, l]), y = a(() => {
    var e;
    return (u == null ? void 0 : u.token_ascii) === "Cardano" ? m : (e = l.find((S) => S.token_id === (u == null ? void 0 : u.token_id))) == null ? void 0 : e.amount;
  }, [u, m, l]), _ = a(() => (c === "STOP_LOSS" || c === "DCA") && o.expected_output ? o.expected_output * t : n === "BUY" ? o.total_output_without_slippage ? o.total_output_without_slippage * (r == null ? void 0 : r.price_ba) * t : s ? s * (r == null ? void 0 : r.price_ba) * t : 0 : n === "SELL" ? o.total_output_without_slippage ? o.total_output_without_slippage * t : s * t : 0, [r, t, s, n, o, c]), f = a(() => c === "STOP_LOSS" ? (i || 0) * (r == null ? void 0 : r.price_ba) * t : n === "BUY" ? i || i ? i * t : 0 : n === "SELL" && (i || i) ? i * (r == null ? void 0 : r.price_ba) * t : 0, [i, r, t, n, o]), d = a(() => !_ || !f ? 0 : _ - f, [_, f]), U = a(() => d > 0 ? "+" : "", [d]);
  return {
    userTokenBuyBalance: T,
    tokeBuyUsdPrice: _,
    tokenSellUsdPrice: f,
    tokenUsdPriceDifference: d,
    tokenUsdPriceDifferenceSign: U,
    userTokenSellBalance: y
  };
};
export {
  q as useUsdPrices
};
