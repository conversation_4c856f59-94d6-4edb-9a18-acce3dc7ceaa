// filepath: social-feed/hooks/useReports.ts
import { useDispatch, useSelector } from 'react-redux';
import { useEffect } from 'react';
import { submitReport, fetchReports } from '@/redux/slices/reportsSlice';

const useReports = () => {
  const dispatch = useDispatch();
  const reports = useSelector((state) => state.reports.items);
  const loading = useSelector((state) => state.reports.loading);
  const error = useSelector((state) => state.reports.error);

  useEffect(() => {
    dispatch(fetchReports());
  }, [dispatch]);

  const reportPost = (postId, reason) => {
    return dispatch(submitReport({ postId, reason })).unwrap();
  };

  return {
    reports,
    loading,
    error,
    reportPost,
  };
};

export default useReports;