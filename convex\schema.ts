import { defineSchema, defineTable } from "convex/server";
import { authTables } from "@convex-dev/auth/server";
import { v } from "convex/values";

export default defineSchema({
  ...authTables,
  authAccounts: defineTable({
    provider: v.string(),
    providerAccountId: v.string(),
    userId: v.string(),
  })
    .index("by_provider_and_providerAccountId", ["provider", "providerAccountId"]),

  Accounts: defineTable({
    user_id: v.string(),
    auth_user_id: v.optional(v.id("users")),
    email: v.union(v.string(), v.null()),
    password: v.union(v.string(), v.null()),
    wallet_address: v.union(v.string(), v.null()),
    blockchain: v.union(v.string(), v.null()),
    account_type: v.union(v.string(), v.null()),
    registration_date: v.union(v.string(), v.null()),
    user_info: v.any(),
    model_id: v.optional(v.union(v.string(), v.null())),
  })
    .index("by_stake_key", ["user_info.stake_key"])
    .index("by_wallet", ["wallet_address"])
    .index("by_user_id", ["user_id"])
    .index("by_type_and_reg_date", ["account_type", "registration_date"])
    .index("by_username", ["user_info.account.username"])
    .index("by_auth_user_id", ["auth_user_id"])
    .index("by_provider_and_providerAccountId", ["account_type", "user_id"]),

  Categories: defineTable({
    name: v.string(),
  }),

  Comments: defineTable({
    user_id: v.string(),
    content_id: v.string(),
    comment_text: v.string(),
    created_at: v.string(),
  })
    .index('by_content_id', ['content_id']),

  Content: defineTable({
    description: v.string(),
    content_type: v.union(v.string(), v.null()),
    is_paid: v.boolean(),
    created_at: v.string(),
    creator_id: v.string(),
    is_edited: v.boolean(),
    id: v.string(),
    edited_at: v.union(v.string(), v.null()),
    visibility: v.string(),
    metadata: v.union(v.any(), v.null()),
    media_id: v.union(v.string(), v.null()),
  })
    .index("by_creator", ["creator_id"])
    .index("by_created_at", ["created_at"])
    .index("by_content_type", ["content_type"])
    .index("by_visibility", ["visibility"]),

  ContentCategories: defineTable({
    content_id: v.string(),
    category_id: v.string(),
  }),

  ContentMentions: defineTable({
    content_id: v.string(),
    mentioned_user_id: v.string(),
    id: v.string(),
    created_at: v.string(),
  })
    .index('by_content_id', ['content_id'])
    .index('by_mentioned_user', ['mentioned_user_id']),

  ContentTags: defineTable({
    content_id: v.string(),
    tagged_user_id: v.string(),
    created_at: v.string(),
    updated_at: v.string(),
  }),

  ContentViews: defineTable({
    content_id: v.string(),
    user_id: v.union(v.string(), v.null()),
    ip_address: v.string(),
    user_agent: v.string(),
    id: v.string(),
    session_id: v.optional(v.union(v.string(), v.null())),
    viewed_at: v.string(),
    content_type: v.string(),
    created_at: v.optional(v.string()),
  })
    .index("by_created_at", ["created_at"])
    .index("by_content_id", ["content_id"]),

  Folders: defineTable({
    creator_id: v.string(),
    folder_name: v.string(),
    parent_folder_id: v.union(v.string(), v.null()),
    created_at: v.string(),
    updated_at: v.string(),
  }),

  Follows: defineTable({
    follower_id: v.string(),
    followed_id: v.string(),
    created_at: v.string(),
  })
    .index("by_followed_id", ["followed_id"])
    .index("by_follower", ["follower_id"]),

  Hashtags: defineTable({
    name: v.string(),
    created_at: v.string(),
    content_id: v.string(),
    post_count: v.number(),
  })
    .index('by_name', ['name'])
    .index('by_content_id', ['content_id']),


  Likes: defineTable({
    user_id: v.string(),
    content_id: v.string(),
    created_at: v.string(),
    content_type: v.string(),
  })
    .index('by_content_id', ['content_id'])
    .index('by_user_content', ['user_id', 'content_id']),

  LoginLogs: defineTable({
    user_id: v.union(v.string(), v.null()),
    ip_address: v.string(),
    user_agent: v.string(),
    id: v.number(),
    login_time: v.string(),
    status: v.string(),
    reason: v.string(),
  }),

  MediaLibrary: defineTable({
    creator_id: v.string(),
    media_id: v.string(),
    file_path: v.string(),
    file_type: v.string(),
    thumbnail_url: v.union(v.string(), v.null()),
    metadata: v.optional(v.any()),
    privacy_settings: v.string(),
    tags: v.array(v.string()),
    folder_id: v.union(v.string(), v.null()),
    created_at: v.string(),
    updated_at: v.string(),
  }),

  Messages: defineTable({
    id: v.string(),
    sender_id: v.string(),
    receiver_id: v.string(),
    content: v.string(),
    media_url: v.union(v.string(), v.null()),
    media_type: v.union(v.string(), v.null()),
    is_read: v.boolean(),
    is_deleted: v.boolean(),
    is_chat_deleted: v.union(v.any(), v.null()),
    created_at: v.string(),
    is_edited: v.boolean(),
    edited_at: v.union(v.string(), v.null()),
    metadata: v.optional(v.any()),
    sender_type: v.union(v.string(), v.null()),
  })
    .index('by_created_at', ['created_at'])
    .index('by_sender_id', ['sender_id']),

  Nonces: defineTable({
    address: v.string(),
    blockchain: v.string(),
    nonce: v.string(),
    createdAt: v.number(),
  }).index("by_address", ["address"]),

  Notifications: defineTable({
    user_id: v.string(),
    title: v.optional(v.string()),
    message: v.string(),
    type: v.string(),
    link: v.optional(v.string()),
    icon: v.optional(v.string()),
    metadata: v.optional(v.any()),
    status: v.string(),
    created_at: v.string(),
  }).index("by_user_id", ["user_id"]),

  Payouts: defineTable({
    creator_id: v.string(),
    amount: v.number(),
    currency: v.string(),
    payout_method: v.string(),
    status: v.string(),
    requested_at: v.string(),
    processed_at: v.union(v.string(), v.null()),
  }),

  ProfileViewers: defineTable({
    user_id: v.string(),
    viewer_id: v.string(),
    viewer_type: v.string(),
    created_at: v.string(),
  }),

  Purchases: defineTable({
    user_id: v.string(),
    content_id: v.string(),
    amount: v.number(),
    purchased_at: v.string(),
  })
    .index('by_content_id', ['content_id']),

  Reports: defineTable({
    reporter_id: v.string(),
    content_id: v.string(),
    reason: v.string(),
    status: v.string(),
    created_at: v.string(),
  }),

  SavedContent: defineTable({
    id: v.number(),
    user_id: v.string(),
    content_id: v.string(),
    created_at: v.string(),
    content_type: v.string(),
  })
    .index('by_content_id', ['content_id'])
    .index('by_user_content', ['user_id', 'content_id']),

  Subscriptions: defineTable({
    subscriber_id: v.string(),
    creator_id: v.string(),
    price: v.number(),
    status: v.string(),
    start_date: v.string(),
    end_date: v.string(),
  }),

  TemporaryMedia: defineTable({
    id: v.string(),
    url: v.string(),
    thumbnail_url: v.union(v.string(), v.null()),
    user_id: v.string(),
    created_at: v.string(),
    is_used: v.boolean(),
    expires_at: v.string(),
    type: v.string(),
    thumbnail_key: v.union(v.string(), v.null()),
    metadata: v.union(v.any(), v.null())
  })
    .index("by_expires_at", ["expires_at"])
    .index("by_is_used", ["is_used"]),

  Transactions: defineTable({
    user_id: v.string(),
    creator_id: v.string(),
    amount: v.number(),
    currency: v.string(),
    payment_method: v.string(),
    status: v.string(),
    created_at: v.string(),
  }),

  UserDevices: defineTable({
    user_id: v.string(),
    device_id: v.string(),
    account_type: v.string(),
    created_at: v.string(),
    last_used: v.string(),
  }),

  UserReports: defineTable({
    reported_user_id: v.string(),
    reporter_user_id: v.string(),
    reason: v.string(),
    details: v.string(),
    status: v.string(),
    created_at: v.string(),
    updated_at: v.string(),
  }),

  Pokes: defineTable({
    sender_id: v.string(),
    recipient_id: v.string(),
    created_at: v.string(),
  }),

  UserBlocks: defineTable({
    blocker_id: v.string(),
    blocked_id: v.string(),
    created_at: v.optional(v.string()),
  })
    .index("by_blocker", ["blocker_id"])
    .index("by_blocked", ["blocked_id"]),

  users: defineTable({
    accountId: v.id("Accounts"),
    email: v.union(v.string(), v.null()),
    accountType: v.union(v.string(), v.null()),
  }).index("by_account_id", ["accountId"]),
});