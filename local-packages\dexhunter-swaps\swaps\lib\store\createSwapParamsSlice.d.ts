export interface SwapParamsSlice {
    tokenIdSell: string;
    tokenIdBuy: string;
    setTokens: (tokenIdSell: string, tokenIdBuy: string) => void;
    setTokenSell: (tokenIdSell: string) => void;
    setTokenBuy: (tokenIdBuy: string) => void;
}
declare const createSwapParamsSlice: (set: any) => {
    tokenIdSell: string;
    tokenIdBuy: string;
    setTokens: (tokenIdSell: string, tokenIdBuy: string) => void;
    setTokenSell: (tokenIdSell: string) => void;
    setTokenBuy: (tokenIdBuy: string) => void;
};
export default createSwapParamsSlice;
