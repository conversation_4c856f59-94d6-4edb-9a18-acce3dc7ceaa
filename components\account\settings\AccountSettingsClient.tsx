'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { Skeleton, SkeletonCircle } from "@/components/ui/skeleton";
import { Spotlight } from '@/components/ui/spotlight';
import { genUploader } from "uploadthing/client";
import type { OurFileRouter } from "@/app/api/uploadthing/core";
import { randomImageNameGenerator } from "@/public/main";
import Swal from 'sweetalert2';
import { useDispatch } from 'react-redux';
import { setUserInfo } from '@/redux/slices/userInfoSlice';
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import { updateUserBanner } from "@/lib/api/updateUserSettings";
import AccountTab from './tabs/AccountTab/AccountTab';
import NotificationsTab from './tabs/NotificationsTab';
import PaymentsTab from './tabs/PaymentsTab';
import BillingTab from './tabs/BillingTab';
import PrivacySafetyTab from './tabs/PrivacySafetyTab';
import ProfileImageUploadModal from './modals/ProfileImageUploadModal';
import CoverBannerUploadModal from './modals/CoverBannerUploadModal';
import { useMutation } from 'convex/react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useUser } from '@/hooks/useUser';

interface AccountSettingsClientProps {
    initialUserData?: any;
    connectedAccounts?: any;
}

const AccountSettingsClient = ({ initialUserData, connectedAccounts }: AccountSettingsClientProps) => {
    const router = useRouter();
    const wallet = useSelector((state: any) => state.wallet);

    // Convex session management
    const { isAuthenticated, user: convexUser, signOut, isLoading: sessionLoading } = useUser();

    const [activeTab, setActiveTab] = useState('Account');
    const [bannerLoading, setBannerLoading] = useState<boolean>(true);
    const [profileLoading, setProfileLoading] = useState<boolean>(true);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [userData, setUserData] = useState(initialUserData || {});
    const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
    const [isCoverModalOpen, setIsCoverModalOpen] = useState(false);
    const [offset, setOffset] = useState(0);
    const contentRef = useRef<HTMLDivElement>(null);
    const sidebarRef = useRef(null) as any;
    const dispatch = useDispatch();

    const Toast = () => toast('');

    const { uploadFiles } = genUploader<OurFileRouter>();

    const uploadBannerVideo = useMutation(api.accounts.uploadBannerVideo);

    // Sidebar menu options
    const menuItems = [
        'My Profile',
        'My Earnings',
        'My Messages',
        'My Bookmarks',
        'My Control Panel',
        'Account Settings',
        'Become A Creator',
        'Account',
        'Privacy',
        'Notifications',
        'Payments',
        'Wallet',
        'Decentralized Advertisement Agency',
        'Multi Chain Wallet Sync',
        'Help Center',
    ];

    // Handle form changes
    const handleFormChange = (hasChanges: boolean, updatedData?: any, loading?: boolean) => {
        setHasUnsavedChanges(hasChanges);
        setIsLoading(loading || false);
        if (updatedData) {
            // Make sure website is included in connected_socials
            if (updatedData.officialWebsite) {
                const connectedSocials = updatedData.connected_socials || [];
                const websiteIndex = connectedSocials.findIndex((social: any) => social.platform === 'website');

                if (websiteIndex === -1) {
                    connectedSocials.push({
                        platform: 'website',
                        username: updatedData.officialWebsite
                    });
                } else {
                    connectedSocials[websiteIndex].username = updatedData.officialWebsite;
                }

                updatedData.connected_socials = connectedSocials;
            }

            setUserData(updatedData);
            dispatch(setUserInfo(updatedData));
        }
    };

    // Handle profile image save
    const handleProfileImageSave = async (imageData: string) => {
        try {
            // Extract the MIME type from the Base64 string
            const mimeTypeMatch = imageData.match(/data:(image\/[a-zA-Z]+);base64,/);
            if (!mimeTypeMatch) {
                throw new Error('Invalid image data');
            }
            const mimeType = mimeTypeMatch[1];
            const extension = mimeType.split('/')[1];

            // Decode Base64 string to binary data
            const byteCharacters = atob(imageData.split(',')[1]);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);

            // Create a Blob from the binary data
            const blob = new Blob([byteArray], { type: mimeType });

            // Generate a random filename with the correct extension
            const fileName = `${randomImageNameGenerator()}.${extension}`;

            // Create a File object
            const file = new File([blob], fileName, { type: mimeType });

            // Upload the file using UploadThing's uploadFiles function
            const response = await uploadFiles('profileImage', {
                files: [file],
                headers: {
                    'Content-Type': 'application/json',
                    'x-account-type': userData?.accountType || 'user'
                },
            });

            if (response?.[0]?.url) {
                // Update local state with the new image URL
                const updatedUserData = { ...userData, profilePhoto: response[0].url };
                setUserData(updatedUserData);

                // Update Redux store
                dispatch(setUserInfo(updatedUserData));

                toast.success("Profile photo updated successfully");

                return { success: true, message: 'Profile photo updated successfully', url: response[0].url };
            } else {
                throw new Error("No URL in response");
            }
        } catch (error: any) {
            console.error("Error uploading profile photo:", error);
            toast.error("Failed to update profile photo. Please try again.");
            return { success: false, error: error.message };
        }
    };

    // Handle cover banner save
    const handleCoverBannerSave = async (mediaData: string, mediaType: 'image' | 'video' | 'url') => {
        try {
            if (mediaType === 'image') {
                // Extract the MIME type from the Base64 string
                const mimeTypeMatch = mediaData.match(/data:(image\/[a-zA-Z]+);base64,/);
                if (!mimeTypeMatch) {
                    throw new Error('Invalid image data');
                }
                const mimeType = mimeTypeMatch[1];
                const extension = mimeType.split('/')[1];

                // Decode Base64 string to binary data
                const byteCharacters = atob(mediaData.split(',')[1]);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);

                // Create a Blob from the binary data
                const blob = new Blob([byteArray], { type: mimeType });

                // Generate a random filename with the correct extension
                const fileName = `${randomImageNameGenerator()}.${extension}`;

                // Create a File object
                const file = new File([blob], fileName, { type: mimeType });

                // Upload the file using UploadThing's uploadFiles function
                const response = await uploadFiles('coverImage', {
                    files: [file],
                    headers: {
                        'Content-Type': 'application/json',
                        'x-account-type': userData?.accountType || 'user'
                    },
                });

                if (response?.[0]?.url) {
                    // Update local state with the new image URL
                    const updatedUserData = {
                        ...userData,
                        coverBanner: response[0].url,
                        coverBannerType: 'image'
                    };
                    setUserData(updatedUserData);

                    // Update Redux store
                    dispatch(setUserInfo(updatedUserData));

                    toast.success("Cover banner updated successfully");

                    return { success: true, message: 'Cover banner updated successfully', url: response[0].url };
                } else {
                    throw new Error("No URL in response");
                }
            } else if (mediaType === 'video') {
                try {
                    // Extract the MIME type from the Base64 string
                    const mimeTypeMatch = mediaData.match(/data:(video\/[^;]+);base64,/);
                    if (!mimeTypeMatch) {
                        throw new Error('Invalid video data');
                    }
                    const mimeType = mimeTypeMatch[1];
                    const extension = mimeType.split('/')[1];
                    const base64Data = mediaData.split(',')[1];
                    const fileName = `${randomImageNameGenerator()}.${extension}`;
                    // Call Convex mutation
                    const result = await uploadBannerVideo({
                        userId: userData.userId,
                        file: base64Data,
                        fileType: mimeType,
                        fileName,
                    });
                    if (!result.success) {
                        throw new Error(result.message || 'Failed to upload video');
                    }
                    if (result.success && result.url) {
                        const updatedUserData = {
                            ...userData,
                            coverBanner: result.url,
                            coverBannerType: 'video'
                        };
                        setUserData(updatedUserData);
                        dispatch(setUserInfo(updatedUserData));
                        toast.success('Cover banner updated successfully');
                        return { success: true, message: 'Cover banner updated successfully', url: result.url };
                    } else {
                        throw new Error('No URL in response');
                    }
                } catch (error: any) {
                    console.error('Error uploading video:', error);
                    return { success: false, error: error.message };
                }
            } else if (mediaType === 'url') {
                // For URL, we just store the URL directly
                // Determine if it's an image or video URL based on extension or content type
                const isVideoUrl = /\.(mp4|webm|ogg|mov)$/i.test(mediaData);

                // Update local state with the URL
                const updatedUserData = {
                    ...userData,
                    coverBanner: mediaData,
                    coverBannerType: isVideoUrl ? 'video' : 'image'
                };

                const bannerUpdate = await updateUserBanner(userData.userId, userData.accountType, updatedUserData);

                if (!bannerUpdate.success) {
                    throw new Error('Failed to update banner:' + bannerUpdate.error);
                }

                setUserData(updatedUserData);

                // Update Redux store
                dispatch(setUserInfo(updatedUserData));

                toast.success("Cover banner updated successfully");

                return { success: true, message: 'Cover banner updated successfully', url: mediaData };
            }

            throw new Error("Invalid media type");
        } catch (error: any) {
            console.error("Error uploading cover banner:", error);
            return { success: false, error: error.message };
        }
    };

    // Handle tab change and scroll to top
    const handleTabChange = (tab: string) => {
        if (hasUnsavedChanges || isLoading) {
            Swal.fire({
                title: isLoading ? 'Loading...' : 'Unsaved Changes',
                text: isLoading
                    ? 'Please wait for the current operation to complete.'
                    : 'You have unsaved changes. Do you want to discard them?',
                icon: 'warning',
                showCancelButton: !isLoading,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: isLoading ? 'OK' : 'Yes, discard changes',
                cancelButtonText: 'No, stay here'
            }).then((result) => {
                if (result.isConfirmed && !isLoading) {
                    // Only allow tab change if not loading
                    setActiveTab(tab);
                    setHasUnsavedChanges(false);
                    // Reset any form data if necessary
                    setTimeout(() => scrollToContent(), 100); // Add small delay to ensure DOM update
                }
            });
        } else {
            setActiveTab(tab);
            setTimeout(() => scrollToContent(), 100); // Add small delay to ensure DOM update
        }
    };

    // Scroll to content function
    const scrollToContent = () => {
        if (contentRef.current) {
            const element = contentRef.current;
            const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
            window.scrollTo({
                top: elementPosition - 120,
                behavior: 'smooth'
            });
        }
    };

    // Render the appropriate component based on active tab
    const renderTabContent = () => {
        switch (activeTab) {
            case 'Account':
                return <AccountTab userData={userData} onFormChange={handleFormChange} connectedAccounts={connectedAccounts} />;
            case 'Notifications':
                return <NotificationsTab
                    userData={userData}
                    onFormChange={handleFormChange}
                />;
            case 'Payments':
                return <PaymentsTab />;
            case 'Billing':
                return <BillingTab />;
            case 'Privacy':
                return <PrivacySafetyTab userData={userData} onFormChange={handleFormChange} />;
            case 'Help Center':
                return (
                    <motion.div
                        key="help"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                    >
                        <h2 className="text-xl font-semibold text-white mb-4">Help Center</h2>
                        <Link href="/help/faq" className="text-blue-400 hover:text-blue-300 block">FAQs</Link>
                        <Link href="/help/support" className="text-blue-400 hover:text-blue-300 block">Contact Support</Link>
                    </motion.div>
                );
            default:
                return <AccountTab userData={userData} onFormChange={handleFormChange} connectedAccounts={connectedAccounts} />;
        }
    };

    // Handle sticky sidebar
    useEffect(() => {
        const handleScroll = () => {
            const sidebarTop = sidebarRef.current?.getBoundingClientRect().top;
            const headerHeight = 97; // Adjust based on your actual header height

            if (sidebarTop <= headerHeight) {
                setOffset(headerHeight); // Apply offset when sticky
            } else {
                setOffset(0); // Reset when not sticky
            }
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    // Sync user data with Convex session
    useEffect(() => {
        if (convexUser && isAuthenticated) {
            // Use Convex user data as the source of truth
            const convexUserData = {
                userId: convexUser.user_id,
                accountId: convexUser.accountId,
                email: convexUser.email,
                walletAddress: convexUser.wallet_address,
                blockchain: convexUser.blockchain,
                accountType: convexUser.account_type,
                registrationDate: convexUser.registration_date,
                userInfo: convexUser.user_info,
                profilePhoto: convexUser.user_info?.profilePhoto,
                displayName: convexUser.user_info?.account?.displayName,
                username: convexUser.user_info?.account?.username,
                coverBanner: convexUser.user_info?.coverBanner,
                coverBannerType: convexUser.user_info?.coverBannerType,
                status: convexUser.status,
                lastActive: convexUser.lastActive,
                subscribers: convexUser.subscribers,
                ...initialUserData // Merge with any additional initial data
            };

            setUserData(convexUserData);
            dispatch(setUserInfo(convexUserData));
        } else if (initialUserData) {
            // Fallback to initial data if no Convex session
            setUserData(initialUserData);
        }
    }, [convexUser, isAuthenticated, initialUserData, dispatch]);

    // Redirect if not authenticated
    useEffect(() => {
        if (!sessionLoading && !isAuthenticated) {
            router.push('/');
        }
    }, [isAuthenticated, sessionLoading, router]);

    // Show loading state while session is loading
    if (sessionLoading) {
        return (
            <div className="min-h-screen bg-white dark:bg-[#121212] flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900 dark:border-white mx-auto"></div>
                    <p className="mt-4 text-gray-600 dark:text-gray-400">Loading account settings...</p>
                </div>
            </div>
        );
    }

    // Show unauthorized message if not authenticated
    if (!isAuthenticated) {
        return (
            <div className="min-h-screen bg-white dark:bg-[#121212] flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Access Denied</h1>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">You need to be logged in to access account settings.</p>
                    <button
                        onClick={() => router.push('/')}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                    >
                        Go Home
                    </button>
                </div>
            </div>
        );
    }

    // In the return statement, update the Cover Photo section:
    return (
        <div className="min-h-screen bg-white dark:bg-[#121212]">
            {/* Cover Banner */}
            <div className="w-full h-[400px] relative border border-solid border-l-0 border-t-0 border-r-0 border-gray-300 dark:border-white/50">
                <Skeleton loading={bannerLoading} width="100%" height="100%">
                    {userData?.coverBannerType === 'video' ? (
                        userData?.coverBanner ? (
                            <video
                                src={userData?.coverBanner}
                                autoPlay
                                muted
                                loop
                                playsInline
                                className="object-cover w-full h-full absolute top-0 left-0"
                                onLoadedData={() => setBannerLoading(false)}
                                onError={() => setBannerLoading(false)}
                            />
                        ) : (
                            <img
                                src="/images/user/default-banner.webp"
                                className="object-cover w-full h-full absolute top-0 left-0"
                                onLoadedData={() => setBannerLoading(false)}
                                onError={() => setBannerLoading(false)}
                            />
                        )
                    ) : (
                        <Image
                            src={userData?.coverBanner || "/images/user/default-banner.webp"}
                            alt="Cover Photo"
                            fill
                            className="object-cover w-full h-full absolute top-0 left-0"
                            onLoad={() => setBannerLoading(false)}
                            onError={() => setBannerLoading(false)}
                        />
                    )}
                </Skeleton>
                <button
                    onClick={() => setIsCoverModalOpen(true)}
                    className="absolute bottom-4 right-4 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors border border-solid"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="20" height="20" style={{ fill: 'white' }}>
                        <path d="M149.1 64.8L138.7 96 64 96C28.7 96 0 124.7 0 160L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-256c0-35.3-28.7-64-64-64l-74.7 0L362.9 64.8C356.4 45.2 338.1 32 317.4 32L194.6 32c-20.7 0-39 13.2-45.5 32.8zM256 192a96 96 0 1 1 0 192 96 96 0 1 1 0-192z" />
                    </svg>
                </button>
            </div>

            {/* Profile Picture */}
            <div className="relative px-6">
                <div className="absolute -top-20 left-24 flex items-end gap-6">
                    <div className="w-40 h-40 rounded-full overflow-hidden border border-[#121212] dark:border-white relative">
                        <SkeletonCircle width={40} height={40} className="w-full h-full" loading={profileLoading}>
                            <Image
                                src={userData?.profilePhoto || "/images/user/default-avatar.webp"}
                                alt={userData?.username || userData?.displayName || "User"}
                                fill
                                className="object-cover"
                                onLoad={() => setProfileLoading(false)}
                            />
                        </SkeletonCircle>
                    </div>

                    {/* Edit Button */}
                    <button
                        onClick={() => setIsProfileModalOpen(true)}
                        className="absolute top-[83%] left-[65%] bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors z-[2] border border-solid shadow-md"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="20" height="20" style={{ fill: 'white' }}>
                            <path d="M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160L0 416c0 53 43 96 96 96l256 0c53 0 96-43 96-96l0-96c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 96c0 17.7-14.3 32-32 32L96 448c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 64z" />
                        </svg>
                    </button>
                </div>
            </div>

            {/* Account Settings */}
            <div className="container mx-auto px-6 pt-28 pb-8 flex gap-6">
                {/* Sidebar */}
                <div className="w-72 flex-shrink-0" ref={sidebarRef}>
                    <div
                        className="!sticky transition-all duration-200"
                        style={{ top: `${offset}px` }}
                    >
                        <Spotlight
                            className="bg-zinc-700 blur-2xl"
                            size={24}
                            springOptions={{ bounce: 0.3, duration: 0.1 }}
                        />
                        <div className="space-y-2">
                            {menuItems.map((item) => (
                                item === 'Wallet' ? (
                                    <Link
                                        key={item}
                                        href="https://sugarfans.com/site/settings/wallet"
                                        className={`w-full text-left px-4 py-3 border-b transition focus-visible:border-[#121212] dark:focus-visible:border-white ${activeTab === item
                                                ? 'text-[#121212] dark:text-white border-[#121212] dark:border-white'
                                                : 'text-[#121212]/40 dark:text-white/40 hover:bg-[#121212]/20 dark:hover:bg-white/20 border-[#121212]/20 dark:border-white/20'
                                            }`}
                                    >
                                        {item}
                                    </Link>
                                ) : (
                                    <motion.button
                                        key={item}
                                        onClick={() => handleTabChange(item)}
                                        className={`w-full text-left px-4 py-3 border-b transition focus-visible:border-[#121212] dark:focus-visible:border-white ${activeTab === item
                                                ? 'text-[#121212] dark:text-white border-[#121212] dark:border-white'
                                                : 'text-[#121212]/40 dark:text-white/40 hover:bg-[#121212]/20 dark:hover:bg-white/20 border-[#121212]/20 dark:border-white/20'
                                            }`}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        {item}
                                    </motion.button>
                                )
                            ))}
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <div ref={contentRef} className="flex-grow p-6">
                    {renderTabContent()}
                </div>
            </div>

            {/* Modals */}
            <ProfileImageUploadModal
                isOpen={isProfileModalOpen}
                userData={userData}
                onClose={() => setIsProfileModalOpen(false)}
                onSave={async (imageData: string) => {
                    const result = await handleProfileImageSave(imageData);
                    return {
                        success: result.success,
                        error: result.error || result.message || ''
                    };
                }}
                currentImage={userData?.profilePhoto || "/images/user/default-avatar.webp"}
            />

            <CoverBannerUploadModal
                isOpen={isCoverModalOpen}
                onClose={() => setIsCoverModalOpen(false)}
                onSave={async (mediaData: string, mediaType: 'image' | 'video' | 'url'): Promise<{ success: boolean; message: string } | undefined> => {
                    const result = await handleCoverBannerSave(mediaData, mediaType);
                    return {
                        success: result.success,
                        message: result.message || result.error || 'An error occurred'
                    };
                }}
                currentImage={userData?.coverBanner || "/images/user/default-banner.webp"}
            />
        </div>
    );
};
export default AccountSettingsClient;