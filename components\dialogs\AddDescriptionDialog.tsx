'use client';

import React, { useState, useEffect } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface DescriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (description: string) => void;
  initialDescription?: string;
}

const DescriptionModal: React.FC<DescriptionModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialDescription = '',
}) => {
  const [description, setDescription] = useState(initialDescription);

  useEffect(() => {
    setDescription(initialDescription);
  }, [initialDescription, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(description);
    onClose();
  };

  return (
    <AnimatedModal open={isOpen} onOpenChange={onClose} size="md" title="Add Description" closeButton>
      <form onSubmit={handleSubmit}>
        <div className="p-4">
          <Textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Add a description for your media..."
            className="min-h-[100px] resize-none bg-gray-100 dark:bg-zinc-800 border-gray-200 dark:border-zinc-700 rounded-md text-gray-900 dark:text-white focus:ring-turquoise"
          />
        </div>
        <div className="flex justify-end gap-2 p-4 border-t border-gray-200 dark:border-zinc-700">
          <Button
            type="submit"
            className="bg-turquoise hover:bg-turquoise/80 text-white rounded-md px-4 py-2"
            disabled={!description.trim()}
          >
            Save
          </Button>
          <Button
            type="button"
            onClick={onClose}
            className="bg-red-400 hover:bg-red-500 text-white rounded-md px-4 py-2"
          >
            Cancel
          </Button>
        </div>
      </form>
    </AnimatedModal>
  );
};
export default DescriptionModal;