// api/uploadThing/route.ts
import { createRoute<PERSON><PERSON><PERSON> } from "uploadthing/next";
import { uploadRouter } from "./core";

// Export routes for Next App Router
export const { GET, POST } = createRouteHandler({
  router: uploadRouter,
  // Add CORS configuration for Vercel deployment
  //config: { }
});

// Add OPTIONS method handler for CORS preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization, x-account-type"
    }
  });
}