[{"label": "Bitcoin", "value": "bitcoin", "icon_light": "/images/blockchains/light/bitcoin.webp", "icon_dark": "/images/blockchains/dark/bitcoin.webp", "supported_wallets": [{"name": "Xverse", "id": "Xverse Wallet", "provider": "XverseProviders.BitcoinProvider", "icon": "/images/wallet/bitcoin/xverse.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/bitcoin/phantom.png"}, {"name": "Unisat", "id": "Unisat", "provider": "window.unisat", "icon": "/images/wallet/bitcoin/unisat.png"}, {"name": "Leather", "id": "Leather", "provider": "LeatherProvider", "icon": "/images/wallet/bitcoin/leather.png"}, {"name": "OKX", "id": "OKX", "provider": "window.okxwallet.bitcoin", "icon": "/images/wallet/bitcoin/okx.png"}]}, {"label": "Cardano", "value": "cardano", "icon_light": "/images/blockchains/light/cardano.webp", "icon_dark": "/images/blockchains/dark/cardano.webp", "supported_wallets": [{"name": "<PERSON><PERSON>", "id": "nami", "icon": "/images/wallet/cardano/nami.png"}, {"name": "Flint", "id": "flint", "icon": "/images/wallet/cardano/flint.png"}, {"name": "<PERSON><PERSON>", "id": "typhoncip30", "icon": "/images/wallet/cardano/typhon.png"}, {"name": "Eternl", "id": "eternl", "icon": "/images/wallet/cardano/eternl.png"}, {"name": "VESPR", "id": "vespr", "icon": "/images/wallet/cardano/vespr.png"}, {"name": "Gero", "id": "gerowallet", "icon": "/images/wallet/cardano/gero.png"}, {"name": "<PERSON><PERSON><PERSON>", "id": "nufi", "icon": "/images/wallet/cardano/nufi.png"}, {"name": "Lace", "id": "lace", "icon": "/images/wallet/cardano/lace.png"}]}, {"label": "Ethereum", "value": "ethereum", "icon_light": "/images/blockchains/light/ethereum.webp", "icon_dark": "/images/blockchains/dark/ethereum.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "XRP", "value": "xrp", "icon_light": "/images/blockchains/light/xrp.webp", "icon_dark": "/images/blockchains/dark/xrp.webp", "supported_wallets": [{"name": "Gem", "id": "Gem", "icon": "/images/wallet/xrp/gem.png"}, {"name": "Crossmark", "id": "Crossmark", "icon": "/images/wallet/xrp/crossmark.png"}]}, {"label": "Solana", "value": "solana", "icon_light": "/images/blockchains/light/solana.webp", "icon_dark": "/images/blockchains/dark/solana.webp", "supported_wallets": [{"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/solana/phantom.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/solana/exodus.png"}, {"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/solana/coinbase.png"}, {"name": "Trust", "id": "Trust", "icon": "/images/wallet/solana/trust.png"}, {"name": "Solflare", "id": "Solflare", "icon": "/images/wallet/solana/solflare.png"}, {"name": "Glow", "id": "Glow", "icon": "/images/wallet/solana/glow.png"}, {"name": "Backpack", "id": "Backpack", "icon": "/images/wallet/solana/backpack.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/solana/okx.png"}]}, {"label": "Polygon", "value": "polygon", "icon_light": "/images/blockchains/light/polygon.webp", "icon_dark": "/images/blockchains/dark/polygon.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "BNB Smart Chain", "value": "bnb-smart-chain", "icon_light": "/images/blockchains/light/bnb-smart-chain.webp", "icon_dark": "/images/blockchains/dark/bnb-smart-chain.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "Base", "value": "base", "icon_light": "/images/blockchains/light/base.webp", "icon_dark": "/images/blockchains/dark/base.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "Blast", "value": "blast", "icon_light": "/images/blockchains/light/blast.webp", "icon_dark": "/images/blockchains/dark/blast.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "<PERSON><PERSON>t", "value": "polkadot", "icon_light": "/images/blockchains/light/polkadot.webp", "icon_dark": "/images/blockchains/dark/polkadot.webp", "supported_wallets": [{"name": "Fearless", "id": "fearless-wallet", "icon": "/images/wallet/polkadot/fearless.png"}, {"name": "Enkrypt", "id": "enkrypt", "icon": "/images/wallet/polkadot/enkrypt.png"}, {"name": "PolkaGate", "id": "polkagate", "icon": "/images/wallet/polkadot/polkagate.png"}, {"name": "Subwallet", "id": "subwallet-js", "icon": "/images/wallet/polkadot/subwallet.png"}, {"name": "Talisman", "id": "talisman", "icon": "/images/wallet/polkadot/talisman.png"}]}, {"label": "Arbitrum", "value": "arbitrum", "icon_light": "/images/blockchains/light/arbitrum.webp", "icon_dark": "/images/blockchains/dark/arbitrum.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "EOS", "value": "eos", "icon_light": "/images/blockchains/light/eos.webp", "icon_dark": "/images/blockchains/dark/eos.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "Avalanche", "value": "avalanche", "icon_light": "/images/blockchains/light/avalanche.webp", "icon_dark": "/images/blockchains/dark/avalanche.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "Optimism", "value": "optimism", "icon_light": "/images/blockchains/light/optimism.webp", "icon_dark": "/images/blockchains/dark/optimism.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "<PERSON><PERSON><PERSON>", "value": "vechain", "icon_light": "/images/blockchains/light/vechain.webp", "icon_dark": "/images/blockchains/dark/vechain.webp", "supported_wallets": [{"name": "Veworld", "id": "veworld", "icon": "/images/wallet/vechain/veworld.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/vechain/exodus.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/vechain/trust.png"}]}, {"label": "Immutable", "value": "immutable", "icon_light": "/images/blockchains/light/immutable-x.webp", "icon_dark": "/images/blockchains/dark/immutable-x.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "Algorand", "value": "algorand", "icon_light": "/images/blockchains/light/algorand.webp", "icon_dark": "/images/blockchains/dark/algorand.webp", "supported_wallets": [{"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/algorand/pera.png"}, {"name": "Lute", "id": "Lute", "icon": "/images/wallet/algorand/lute.png"}, {"name": "<PERSON><PERSON><PERSON>", "id": "Defly", "icon": "/images/wallet/algorand/defly.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/algorand/exodus.png"}]}, {"label": "Linea", "value": "linea", "icon_light": "/images/blockchains/light/linea.webp", "icon_dark": "/images/blockchains/dark/linea.webp", "supported_wallets": [{"name": "Coinbase", "id": "Coinbase Wallet", "icon": "/images/wallet/ethereum/coinbase.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/ethereum/exodus.png"}, {"name": "Phantom", "id": "Phantom", "icon": "/images/wallet/ethereum/phantom.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/ethereum/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/ethereum/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/ethereum/rainbow.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/ethereum/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "Cronos", "value": "cronos", "icon_light": "/images/blockchains/light/cronos.webp", "icon_dark": "/images/blockchains/dark/cronos.webp", "supported_wallets": [{"name": "Block", "id": "BlockWallet", "icon": "/images/wallet/cronos/block.png"}, {"name": "Metamask", "id": "MetaMask", "icon": "/images/wallet/cronos/metamask.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/cronos/rabby.png"}, {"name": "Rainbow", "id": "Rainbow", "icon": "/images/wallet/cronos/rainbow.png"}, {"name": "Exodus", "id": "Exodus", "icon": "/images/wallet/cronos/exodus.png"}, {"name": "Trust", "id": "Trust Wallet", "icon": "/images/wallet/cronos/trust.png"}, {"name": "OKX", "id": "OKX Wallet", "icon": "/images/wallet/ethereum/okx.png"}]}, {"label": "<PERSON><PERSON>", "value": "sei", "icon_light": "/images/blockchains/light/sei.webp", "icon_dark": "/images/blockchains/dark/sei.webp", "supported_wallets": [{"name": "Keplr", "id": "Keplr", "icon": "/images/wallet/sei/keplr.png"}, {"name": "<PERSON><PERSON>", "id": "<PERSON><PERSON>", "icon": "/images/wallet/sei/leap.png"}, {"name": "<PERSON>mp<PERSON>", "id": "<PERSON>mp<PERSON>", "icon": "/images/wallet/sei/compass.png"}, {"name": "Fin", "id": "Fin", "icon": "/images/wallet/sei/fin.png"}]}]