import { jsx as i, jsxs as p } from "react/jsx-runtime";
import E from "../../store/useStore.js";
import { memo as O, useState as Q, useRef as x, useEffect as W, useMemo as c } from "react";
import { roundNumber as M, formatBalance as D, formatUsd as G, formatInput as J } from "../../utils/formatNumber.js";
import X from "../../assets/svg/IconChevronDown.js";
import { s as Z } from "../../shallow-27fd7e97.js";
import { TokenImage as P } from "../../components/common/TokenImage.js";
import { cn as d } from "../../lib/utils.js";
import { useInputShortcuts as ee } from "../../hooks/useInputShortcuts.js";
import { useUsdPrices as te } from "../hooks/useUsdPrices.js";
import { C as se } from "../../index.esm-fb2f5862.js";
import { u as oe } from "../../QueryClientProvider-6bcd4331.js";
import re from "../../components/ui/tooltipDialog.js";
import { I as ie } from "../../IconTilde-bf643edd.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "./tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
import "../../components/ui/skeleton.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../utils/cardanoUtils.js";
import "../../index-ca8eb9e1.js";
import "../../config/axios.js";
import "../../axios-ddd885c5.js";
import "../../useQuery-febd7967.js";
import "../../query-013b86c3.js";
import "../../hooks/useScreen.js";
import "../../components/ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../components/ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../hooks/useSwapAction.js";
import "../../hooks/useNotify.js";
import "../../react-toastify.esm-a636d9b1.js";
import "../../assets/svg/IconCopy.js";
import "../../assets/svg/IconX.js";
import "../../assets/svg/IconCheckNotify.js";
import "../../assets/svg/IconAlertTriangleNotify.js";
import "../../assets/svg/IconArrowUpRightNotify.js";
import "../../createReactComponent-ec43b511.js";
const ne = () => {
  const {
    openTokenSearchModal: A,
    setSwapType: R,
    tokenBuy: t,
    tokenPrice: n,
    swapDetails: s,
    sellAmount: l,
    tokenSell: g,
    setBuyAmount: F,
    buyAmount: T,
    orderType: m,
    setInputMode: L,
    supportedTokens: b
  } = E(
    (e) => ({
      openTokenSearchModal: e.tokenSearchSlice.openModal,
      setSwapType: e.tokenSearchSlice.setSwapType,
      isSwapDetailsLoading: e.swapSlice.isSwapDetailsLoading,
      tokenPrice: e.swapSlice.tokenPrice,
      adaUsdPrice: e.tokenSearchSlice.adaUsdPrice,
      swapDetails: e.swapSlice.swapDetails,
      sellAmount: e.swapSlice.sellAmount,
      swapType: e.tokenSearchSlice.swapType,
      isTokenPriceLoading: e.swapSlice.isTokenPriceLoading,
      tokenSell: e.swapSlice.tokenSell,
      tokenBuy: e.swapSlice.tokenBuy,
      setBuyAmount: e.swapSlice.setBuyAmount,
      buyAmount: e.swapSlice.buyAmount,
      orderType: e.swapSlice.orderType,
      setInputMode: e.swapSlice.setInputMode,
      supportedTokens: e.tokenSearchSlice.supportedTokens
    }),
    Z
  ), { handleKeyDown: U } = ee(), B = oe(), { tokeBuyUsdPrice: v, userTokenBuyBalance: k } = te(), [j, f] = Q(""), y = x(n == null ? void 0 : n.average_price), _ = x(l), N = x(), C = x();
  W(() => {
    const e = Math.round((n == null ? void 0 : n.average_price) * 1e4), o = Math.round(y.current * 1e4), r = N.current !== g || C.current !== t, h = _.current !== l;
    if (e !== o && !r && !h) {
      o < e ? f("dhs-animate-fade-green") : o > e && f("dhs-animate-fade-red");
      const a = setTimeout(() => {
        f("");
      }, 2e3);
      return y.current = n, N.current = g, C.current = t, _.current = l, () => clearTimeout(a);
    }
  }, [n, l, g, t]);
  const H = (e) => {
    F(J(e));
  }, V = () => {
    A(), R("BUY");
  }, u = c(() => s != null && s.total_output_without_slippage ? M(s == null ? void 0 : s.total_output_without_slippage) : s != null && s.expected_output ? M(s == null ? void 0 : s.expected_output) : T || 0, [T, s]), $ = (e) => {
    const o = e.target;
    o.value == 0 && setTimeout(() => {
      o.setSelectionRange(o.value.length, o.value.length);
    }, 0), B.cancelQueries({
      predicate: (r) => r.queryKey[0] === "swapDetails"
    }), L("BUY");
  }, K = (u == null ? void 0 : u.toString().length) > 12, I = c(() => b.length === 1, [b]), S = c(() => {
    var o;
    return (o = s == null ? void 0 : s.splits) == null ? void 0 : o.reduce((r, h) => h.price_impact > r ? h.price_impact : r, 0);
  }, [s]), Y = c(() => m === "DCA" ? /* @__PURE__ */ i("span", { children: "Approx. receive" }) : /* @__PURE__ */ p("span", { children: [
    "Balance: ",
    D(k)
  ] }), [m, k]), w = c(() => {
    let e = 0, o = 0;
    return s != null && s.splits && s.splits.forEach((r) => {
      e += r.amount_in, o += r.pool_fee * 100 * r.amount_in;
    }), e > 0 ? o / e : 0;
  }, [s.splits]), q = c(() => {
    const e = S >= 3 && m === "SWAP", o = w >= 2, r = e || o, h = /* @__PURE__ */ p(
      "div",
      {
        className: d(
          "dhs-text-base dhs-sm:text-sm/none dhs-text-gray-103 dhs-font-proximaMedium dhs-flex dhs-items-center dhs-leading-none",
          r && "dhs-text-warning"
        ),
        children: [
          /* @__PURE__ */ i(
            ie,
            {
              width: 12,
              className: d("sm:dhs-h-[14px]", r && "dhs-text-warning")
            }
          ),
          G(v || 0),
          r && /* @__PURE__ */ i(
            "img",
            {
              src: "https://storage.googleapis.com/dexhunter-images/public/warningyellow.svg",
              width: 14,
              height: 14,
              alt: "warning",
              className: "dhs-pb-0.5"
            }
          )
        ]
      }
    ), a = [];
    e && a.push(`High ${S.toFixed(2)}% price impact`), o && a.push(`High ${w.toFixed(2)}% pool fee`);
    const z = a.join(". ") + (a.length > 0 ? "." : "");
    return r ? /* @__PURE__ */ i(
      re,
      {
        activeMobile: !1,
        trigger: h,
        content: z,
        contentClass: "dhs-text-white dhs-rounded-md"
      }
    ) : h;
  }, [S, v, w, m]);
  return /* @__PURE__ */ p(
    "div",
    {
      className: d(
        "dhs-flex dhs-flex-col dhs-px-3.5 dhs-py-3 @sm/appRoot:dhs-px-8 sm/appRoot:dhs-py-5 dhs-bg-background dhs-rounded-3xl dhs-w-full dhs-border-containers dhs-border-2 dhs-min-h-[110px]"
      ),
      children: [
        /* @__PURE__ */ p("div", { className: "dhs-text-sm dhs-font-semibold dhs-justify-between dhs-w-full dhs-flex sm:dhs-pb-[7px]", children: [
          /* @__PURE__ */ i("span", { className: "dhs-text-lg sm:dhs-text-sm/none dhs-text-subText dhs-font-proximaMedium sm:dhs-font-proximaSemiBold dhs-tracking-tight", children: "You buy" }),
          /* @__PURE__ */ i("div", { className: "dhs-flex dhs-gadhs-p-2 dhs-items-center dhs-text-subText sm:dhs-text-sm/[17px] sm:dhs-font-proximaSemiBold", children: Y })
        ] }),
        /* @__PURE__ */ p("div", { className: "dhs-flex dhs-items-center dhs-space-x-2 dhs-justify-between", children: [
          /* @__PURE__ */ p(
            "div",
            {
              className: d(
                "dhs-flex dhs-items-center dhs-p-2 -dhs-ml-2 sm:dhs-ml-0 sm:dhs-p-0 dhs-gap-1 dhs-rounded-lg dhs-cursor-pointer dhs-duration-200 dhs-ease-in-out hover:dhs-bg-background",
                I ? "dhs-pointer-events-none" : ""
              ),
              onClick: V,
              children: [
                /* @__PURE__ */ i(
                  P,
                  {
                    token: t,
                    isVerified: t == null ? void 0 : t.is_verified,
                    className: "dhs-w-[18px] dhs-h-[18px] @md/appRoot:dhs-hidden dhs-mr-1 dhs-rounded-full dhs-pb-0.5",
                    size: 18,
                    verifiedClass: "dhs-w-2.5 dhs-h-2.5"
                  }
                ),
                /* @__PURE__ */ i(
                  P,
                  {
                    token: t,
                    isVerified: t == null ? void 0 : t.is_verified,
                    className: "dhs-hidden dhs-w-[30px] dhs-h-[30px] @md/appRoot:dhs-block @md/appRoot:dhs-h-[30px] dhs-mr-1 dhs-rounded-full dhs-pb-0.5",
                    size: 30
                  }
                ),
                /* @__PURE__ */ i(
                  "span",
                  {
                    className: d(
                      "@md/appRoot:dhs-text-3xl dhs-text-lg dhs-leading-none dhs-text-mainText dhs-font-proximaMedium dhs-tracking-tighter",
                      (t == null ? void 0 : t.ticker.length) > 4 && "@md/appRoot:dhs-text-[18px]"
                    ),
                    children: (t == null ? void 0 : t.ticker) || (t == null ? void 0 : t.token_ascii)
                  }
                ),
                /* @__PURE__ */ i(
                  X,
                  {
                    width: 15,
                    height: 15,
                    className: d(
                      "dhs-text-mainText dhs-w-2.5 dhs-h-2.5 @md/appRoot:dhs-w-3 @md/appRoot:dhs-h-3",
                      I ? "dhs-hidden" : ""
                    )
                  }
                )
              ]
            }
          ),
          /* @__PURE__ */ i("div", { className: d(j), children: /* @__PURE__ */ i(
            se,
            {
              type: "text",
              placeholder: "0",
              className: d(
                "dhs-px-0 dhs-border-none dhs-border-transparent focus-visible:dhs-border-transparent focus-visible:dhs-ring-0 focus-visible:dhs-ring-offset-0 dhs-text-right dhs-bg-transparent dhs-w-full sm:dhs-h-7.5 @md/appRoot:dhs-text-3xl dhs-text-lg dhs-text-mainText dhs-font-proximaMedium dhs-tracking-tighter",
                K && "dhs-text-md @md/appRoot:dhs-text-[22px]"
              ),
              onValueChange: H,
              value: u,
              onKeyDown: U,
              onFocus: $,
              maxLength: 12,
              allowNegativeValue: !1,
              decimalsLimit: 8,
              disabled: m === "LIMIT" || m === "STOP_LOSS",
              intlConfig: { locale: "en-US", currency: "" }
            }
          ) })
        ] }),
        /* @__PURE__ */ p("div", { className: "dhs-text-sm dhs-flex dhs-justify-between dhs-w-full dhs-items-center sm:dhs-pt-[7px]", children: [
          /* @__PURE__ */ i("span", { className: "dhs-text-base sm:dhs-text-sm/none dhs-text-subText dhs-font-proximaMedium", children: (t == null ? void 0 : t.token_ascii) || (t == null ? void 0 : t.ticker) }),
          q
        ] })
      ]
    }
  );
}, lt = O(ne);
export {
  lt as default
};
