'use client';

import React, { useState, useEffect, useRef } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { EmojiPicker } from '@/components/ui/chat/emoji-picker';
import { toast } from 'react-toastify';
import { cn } from '@/lib/utils';
import { Crop, Smile, Square } from 'lucide-react';
import { RiBlurOffLine } from 'react-icons/ri';
import Tippy from '@tippyjs/react';
import * as fabric from 'fabric';

interface ImageEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageData: string, imageIndex: number) => void;
  imageUrl: string;
  imageIndex: number;
}

interface BlurRegion {
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  object: fabric.Rect;
}

interface Emoji {
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  object: fabric.Image;
}

const ImageEditDialog: React.FC<ImageEditModalProps> = ({ isOpen, onClose, onSave, imageUrl, imageIndex }) => {
  const [editMode, setEditMode] = useState<'fullBlur' | 'partialBlur' | 'emoji' | 'crop' | null>(null);
  const [interactionMode, setInteractionMode] = useState<'add' | 'edit'>('add');
  const [fullBlur, setFullBlur] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingError, setLoadingError] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [blurRegions, setBlurRegions] = useState<BlurRegion[]>([]);
  const [emojis, setEmojis] = useState<Emoji[]>([]);
  const [selectedObject, setSelectedObject] = useState<fabric.Object | null>(null);
  const objectUrlRef = useRef<string | null>(null);
  const canvasRef = useRef<fabric.Canvas | null>(null);
  const cropRectRef = useRef<fabric.Rect | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const mainImageRef = useRef<fabric.Image | null>(null);

  const restrictToImageBounds = (obj: fabric.Object) => {
    const canvas = canvasRef.current;
    const mainImage = mainImageRef.current;
    if (!canvas || !mainImage) return;

    const imgBounds = {
      left: mainImage.left ?? 0,
      top: mainImage.top ?? 0,
      right: (mainImage.left ?? 0) + (mainImage.width ?? 0) * (mainImage.scaleX ?? 1),
      bottom: (mainImage.top ?? 0) + (mainImage.height ?? 0) * (mainImage.scaleY ?? 1),
    };

    const objBounds = obj.getBoundingRect(true);
    const scaleX = obj.scaleX ?? 1;
    const scaleY = obj.scaleY ?? 1;

    // Restrict position
    if (objBounds.left < imgBounds.left) obj.set({ left: imgBounds.left });
    if (objBounds.top < imgBounds.top) obj.set({ top: imgBounds.top });
    if (objBounds.left + objBounds.width > imgBounds.right) {
      obj.set({ left: imgBounds.right - objBounds.width / scaleX });
    }
    if (objBounds.top + objBounds.height > imgBounds.bottom) {
      obj.set({ top: imgBounds.bottom - objBounds.height / scaleY });
    }

    // Restrict scaling
    if (objBounds.width > imgBounds.right - imgBounds.left) {
      obj.set({ scaleX: (imgBounds.right - imgBounds.left) / (obj.width ?? 1) });
    }
    if (objBounds.height > imgBounds.bottom - imgBounds.top) {
      obj.set({ scaleY: (imgBounds.bottom - imgBounds.top) / (obj.height ?? 1) });
    }

    obj.setCoords();
    canvas.renderAll();
  };

  const handleCanvasClick = (e: fabric.TEvent) => {
    const canvas = canvasRef.current;
    if (!canvas || interactionMode !== 'add') return;

    const pointer = canvas.getPointer(e.e);
    const mainImage = mainImageRef.current;
    if (!mainImage) return;

    const imgBounds = {
      left: mainImage.left ?? 0,
      top: mainImage.top ?? 0,
      right: (mainImage.left ?? 0) + (mainImage.width ?? 0) * (mainImage.scaleX ?? 1),
      bottom: (mainImage.top ?? 0) + (mainImage.height ?? 0) * (mainImage.scaleY ?? 1),
    };

    // Only add if click is within image bounds
    if (pointer.x < imgBounds.left || pointer.x > imgBounds.right || pointer.y < imgBounds.top || pointer.y > imgBounds.bottom) {
      return;
    }

    if (editMode === 'partialBlur') {
      const blurRect = new fabric.Rect({
        left: pointer.x,
        top: pointer.y,
        width: 100,
        height: 100,
        fill: 'rgba(0, 0, 0, 0.5)',
        selectable: true,
        name: `blur-${Date.now()}`,
      });
      restrictToImageBounds(blurRect);
      canvas.add(blurRect);
      setBlurRegions([...blurRegions, { name: blurRect.name!, x: pointer.x, y: pointer.y, width: 100, height: 100, object: blurRect }]);
    } else if (editMode === 'emoji') {
      addEmoji('😄', pointer.x, pointer.y);
    }
    canvas.renderAll();
  };

  function handleEmojiSelect(emoji: string) {
    const canvas = canvasRef.current;
    if (interactionMode === 'add' && canvas) {
      const center = { x: canvas.width! / 2, y: canvas.height! / 2 };
      addEmoji(emoji, center.x, center.y);
    }
  };

  const addEmoji = async (emoji: string, x: number, y: number) => {
    const canvas = canvasRef.current;
    const mainImage = mainImageRef.current;
    if (!canvas || !mainImage) return;

    const imgBounds = {
      left: mainImage.left ?? 0,
      top: mainImage.top ?? 0,
      right: (mainImage.left ?? 0) + (mainImage.width ?? 0) * (mainImage.scaleX ?? 1),
      bottom: (mainImage.top ?? 0) + (mainImage.height ?? 0) * (mainImage.scaleY ?? 1),
    };

    // Adjust position to ensure emoji is within bounds
    const adjustedX = Math.max(imgBounds.left, Math.min(x, imgBounds.right - 50));
    const adjustedY = Math.max(imgBounds.top, Math.min(y, imgBounds.bottom - 50));

    const emojiCode = emoji.codePointAt(0)?.toString(16).toUpperCase() || '1F604';
    const emojiUrl = `https://openmoji.org/data/color/svg/${emojiCode}.svg`;

    try {
      const img = await fabric.Image.fromURL(emojiUrl, { crossOrigin: 'anonymous' });
      img.set({
        left: adjustedX,
        top: adjustedY,
        width: 50,
        height: 50,
        selectable: true,
        name: `emoji-${Date.now()}`,
      });
      restrictToImageBounds(img);
      canvas.add(img);
      setEmojis(prev => [...prev, { name: (img as fabric.Object).name!, x, y, width: 50, height: 50, object: img }]);
      canvas.renderAll();
    } catch (error) {
      console.error('Failed to load emoji image', error);
      toast.error('Could not add emoji.');
    }
  };

  const handleObjectModified = (object: fabric.Object) => {
    if (!object?.name) return;

    restrictToImageBounds(object);

    if (object.name.startsWith('blur-')) {
      const updatedRegion = blurRegions.find(r => r.name === object.name);
      if (updatedRegion) {
        updatedRegion.x = object.left ?? 0;
        updatedRegion.y = object.top ?? 0;
        updatedRegion.width = object.width! * (object.scaleX ?? 1);
        updatedRegion.height = object.height! * (object.scaleY ?? 1);
        setBlurRegions([...blurRegions]);
      }
    } else if (object.name?.startsWith('emoji-')) {
      const updatedEmoji = emojis.find(e => e.name === object.name);
      if (updatedEmoji) {
        updatedEmoji.x = object.left ?? 0;
        updatedEmoji.y = object.top ?? 0;
        updatedEmoji.width = object.width! * (object.scaleX ?? 1);
        updatedEmoji.height = object.height! * (object.scaleY ?? 1);
        setEmojis([...emojis]);
      }
    }
  };

  const handleSelectionCreated = (object: fabric.Object) => {
    setSelectedObject(object);
  };

  const handleSave = () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      toast.error('Canvas not initialized');
      return;
    }

    setIsSaving(true);
    try {
      // Remove background image before saving
      const bgImage = canvas.getObjects().find(obj => obj.name === 'background');
      if (bgImage) canvas.remove(bgImage);

      const dataURL = canvas.toDataURL({
        format: 'webp',
        quality: 0.95,
        multiplier: 1,
      });
      onSave(dataURL, imageIndex);
      onClose();
    } catch (error) {
      console.error('Error saving edited image:', error);
      toast.error('Failed to save edited image');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    initializeCanvas(canvas);
    setBlurRegions([]);
    setEmojis([]);
    setEditMode(null);
    setInteractionMode('add');
    setSelectedObject(null);
    setFullBlur(0);
  };

  const handleUndo = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const objects = canvas.getObjects();
    if (objects.length > 1) {
      const lastObject = objects[objects.length - 1];
      if (lastObject.name !== 'background') {
        canvas.remove(lastObject);
        if (lastObject.name?.startsWith('blur-')) {
          setBlurRegions(blurRegions.filter(r => r.name !== lastObject.name));
        } else if (lastObject.name?.startsWith('emoji-')) {
          setEmojis(emojis.filter(e => e.name !== lastObject.name));
        }
        canvas.renderAll();
      }
    }
  };

  const handleFullBlurChange = ([value]: number[]) => {
    setFullBlur(value);
    const canvas = canvasRef.current;
    const mainImage = mainImageRef.current;
    if (canvas && mainImage) {
      const blurValue = value / 100;
      mainImage.filters = blurValue > 0 ? [new fabric.filters.Blur({ blur: blurValue })] : [];
      mainImage.applyFilters();
      canvas.renderAll();
    }
  };

  const initializeCanvas = async (canvas: fabric.Canvas) => {
    setIsLoading(true);
    setLoadingError(false);
    canvas.clear();

    const container = containerRef.current;
    if (!container) {
      setIsLoading(false);
      setLoadingError(true);
      toast.error('Container not found');
      return;
    }

    const W = container.clientWidth;
    const H = 460;
    canvas.setDimensions({ width: W, height: H });

    let objectUrl: string | null = null;
    try {
      const response = await fetch(imageUrl, { mode: 'cors' });
      if (!response.ok) throw new Error(`Network response was not ok: ${response.statusText}`);

      const blob = await response.blob();
      // Create separate URLs for background and main image
      const bgObjectUrl = URL.createObjectURL(blob);
      // Store the main image URL in the ref
      if (objectUrlRef.current) {
        URL.revokeObjectURL(objectUrlRef.current);
      }
      objectUrlRef.current = URL.createObjectURL(blob);

      // Load background image (blurred)
      const bgImg = await new Promise<fabric.Image>((resolve, reject) => {
        fabric.Image.fromURL(bgObjectUrl, (img) => {
          if (!img) {
            URL.revokeObjectURL(bgObjectUrl);
            reject(new Error('Failed to load background image'));
            return;
          }
          resolve(img);
        }, {
          crossOrigin: 'anonymous',
          name: 'background',
          selectable: false,
          evented: false,
        });
      });

      // Clean up the background URL after loading
      URL.revokeObjectURL(bgObjectUrl);
      const bgScale = Math.max(W / (bgImg.width || 1), H / (bgImg.height || 1));
      bgImg.set({
        scaleX: bgScale,
        scaleY: bgScale,
        left: (W - (bgImg.width || 1) * bgScale) / 2,
        top: (H - (bgImg.height || 1) * bgScale) / 2,
        filters: [new fabric.filters.Blur({ blur: 0.3 })],
      });
      bgImg.applyFilters();
      canvas.add(bgImg);
      // Use moveTo on the object with type assertion
      (bgImg as any).moveTo(0);

      if (!objectUrl) {
        throw new Error('Failed to create object URL');
      }

      return new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';

        img.onload = () => {
          const fabricImg = new fabric.Image(img);
          const scale = Math.min(W / img.width, H / img.height);

          fabricImg.set({
            left: (W - img.width * scale) / 2,
            top: (H - img.height * scale) / 2,
            scaleX: scale,
            scaleY: scale,
            selectable: false,
            hasControls: false,
            hasBorders: false
          });

          mainImageRef.current = fabricImg;
          canvas.add(fabricImg);
          canvas.renderAll();
          resolve();
        };

        img.onerror = () => {
          reject(new Error('Failed to load main image'));
        };

        if (objectUrlRef.current) {
          img.src = objectUrlRef.current;
        } else {
          reject(new Error('Failed to create image URL'));
        }
      });
    } catch (err) {
      console.error('Error loading image into Fabric:', err);
      toast.error('Failed to load image. Check console for details.');
      setLoadingError(true);
    } finally {
      // objectUrl is now only used for the main image and will be cleaned up in the useEffect
      setIsLoading(false);
    }
  };

  const toggleCropMode = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    if (editMode === 'crop') {
      cancelCrop();
    } else {
      setEditMode('crop');
      const mainImage = mainImageRef.current;
      if (!mainImage) return;

      const imgBounds = {
        left: mainImage.left ?? 0,
        top: mainImage.top ?? 0,
        width: (mainImage.width ?? 0) * (mainImage.scaleX ?? 1),
        height: (mainImage.height ?? 0) * (mainImage.scaleY ?? 1),
      };

      const cropRect = new fabric.Rect({
        fill: 'rgba(0,0,0,0.5)',
        stroke: 'white',
        strokeDashArray: [4, 4],
        left: imgBounds.left + 50,
        top: imgBounds.top + 50,
        width: imgBounds.width - 100,
        height: imgBounds.height - 100,
        borderColor: '#00c4b3',
        cornerColor: 'white',
        cornerStyle: 'circle',
        transparentCorners: false,
        cornerSize: 12,
        selectable: true,
      });
      restrictToImageBounds(cropRect);
      canvas.add(cropRect);
      canvas.setActiveObject(cropRect);
      cropRectRef.current = cropRect;
      canvas.renderAll();
    }
    setEditMode(editMode === 'crop' ? null : 'crop');
  };

  const applyCrop = async () => {
    const canvas = canvasRef.current;
    const cropRect = cropRectRef.current;
    if (!canvas || !cropRect) {
      toast.error('Canvas or crop rectangle not initialized');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const mainImage = mainImageRef.current;
      if (!mainImage) {
        throw new Error('Main image not found');
      }

      const imgElement = mainImage.getElement() as HTMLImageElement;
      if (!imgElement.complete) {
        await new Promise<void>((resolve, reject) => {
          imgElement.onload = () => resolve();
          imgElement.onerror = () => reject(new Error('Failed to load image'));
        });
      }

      // Calculate crop coordinates in image space
      const scaleX = mainImage.scaleX ?? 1;
      const scaleY = mainImage.scaleY ?? 1;
      const imgLeft = mainImage.left ?? 0;
      const imgTop = mainImage.top ?? 0;

      const left = Math.max(0, (cropRect.left! - imgLeft) / scaleX);
      const top = Math.max(0, (cropRect.top! - imgTop) / scaleY);
      let width = cropRect.width! / scaleX;
      let height = cropRect.height! / scaleY;

      // Ensure crop dimensions are valid
      if (width <= 0 || height <= 0) {
        throw new Error('Invalid crop dimensions');
      }

      // Cap dimensions to image bounds
      width = Math.min(width, (imgElement.naturalWidth || imgElement.width) - left);
      height = Math.min(height, (imgElement.naturalHeight || imgElement.height) - top);

      // Create temporary canvas
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) {
        throw new Error('Could not create 2D context');
      }

      tempCanvas.width = Math.round(width * scaleX);
      tempCanvas.height = Math.round(height * scaleY);

      // Draw cropped image
      tempCtx.drawImage(
        imgElement,
        Math.round(left),
        Math.round(top),
        Math.round(width),
        Math.round(height),
        0,
        0,
        tempCanvas.width,
        tempCanvas.height
      );

      // Get cropped image data
      const croppedDataUrl = tempCanvas.toDataURL('image/webp', 0.95);

      // Clear canvas and update dimensions
      canvas.clear();
      canvas.setDimensions({
        width: tempCanvas.width,
        height: tempCanvas.height,
      });

      // Re-add blurred background
      const bgImg = await fabric.Image.fromURL(imageUrl, {
        crossOrigin: 'anonymous',
        name: 'background',
        selectable: false,
        evented: false,
      });
      const bgScale = Math.max(canvas.width / (bgImg.width || 1), canvas.height / (bgImg.height || 1));
      bgImg.set({
        scaleX: bgScale,
        scaleY: bgScale,
        left: (canvas.width - (bgImg.width || 1) * bgScale) / 2,
        top: (canvas.height - (bgImg.height || 1) * bgScale) / 2,
        filters: [new fabric.filters.Blur({ blur: 0.3 })],
      });
      bgImg.applyFilters();
      canvas.add(bgImg);
      bgImg.sendToBack();

      const newImg = await fabric.Image.fromURL(croppedDataUrl, {
        crossOrigin: 'anonymous',
        left: 0,
        top: 0,
        selectable: false,
      });
      mainImageRef.current = newImg;
      canvas.add(newImg);

      // Re-add blur regions and emojis
      for (const region of blurRegions) {
        const newRect = new fabric.Rect({
          left: region.x,
          top: region.y,
          width: region.width,
          height: region.height,
          fill: 'rgba(0, 0, 0, 0.5)',
          selectable: true,
          name: region.name,
        });
        restrictToImageBounds(newRect);
        canvas.add(newRect);
        region.object = newRect;
      }

      for (const emoji of emojis) {
        const newEmoji = await fabric.Image.fromURL(emoji.object.getSrc(), {
          crossOrigin: 'anonymous',
          left: emoji.x,
          top: emoji.y,
          width: emoji.width,
          height: emoji.height,
          selectable: true,
          name: emoji.name,
        });
        restrictToImageBounds(newEmoji);
        canvas.add(newEmoji);
        emoji.object = newEmoji;
      }

      cancelCrop();
      canvas.renderAll();
    } catch (error) {
      console.error('Error applying crop:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to apply crop');
    } finally {
      setIsLoading(false);
    }
  };

  const cancelCrop = () => {
    const canvas = canvasRef.current;
    if (canvas && cropRectRef.current) {
      canvas.remove(cropRectRef.current);
      cropRectRef.current = null;
      canvas.renderAll();
    }
    setEditMode(null);
  };

  const CropTools = () => (
    <div className="flex flex-col gap-4">
      <h3 className="text-xs font-semibold text-gray-700 dark:text-gray-200 mb-2 tracking-wide uppercase">Cropping</h3>
      <Button onClick={applyCrop} className="w-full bg-turquoise hover:bg-turquoise/80 text-white" disabled={isLoading}>
        Apply Crop
      </Button>
      <Button onClick={cancelCrop} className="w-full bg-gray-300 dark:bg-zinc-900 hover:bg-gray-500 text-white">
        Cancel
      </Button>
    </div>
  );

  // Clean up object URLs when component unmounts or when isOpen changes
  useEffect(() => {
    return () => {
      // Clean up the URL when the component unmounts or when isOpen changes to false
      if (objectUrlRef.current) {
        URL.revokeObjectURL(objectUrlRef.current);
        objectUrlRef.current = null;
      }
    };
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;

    const timerId = setTimeout(() => {
      const container = containerRef.current;
      if (!container) return;

      const canvasEl = document.createElement('canvas');
      container.appendChild(canvasEl);

      const canvas = new fabric.Canvas(canvasEl, {
        backgroundColor: 'transparent',
        selection: true,
      });
      canvasRef.current = canvas;

      canvas.on('mouse:down', handleCanvasClick);
      canvas.on('object:modified', (e) => e.target && handleObjectModified(e.target));
      canvas.on('object:moving', (e) => e.target && restrictToImageBounds(e.target));
      canvas.on('object:scaling', (e) => e.target && restrictToImageBounds(e.target));
      canvas.on('selection:created', (e) => e.selected && handleSelectionCreated(e.selected[0]));
      canvas.on('selection:cleared', () => setSelectedObject(null));

      initializeCanvas(canvas);
    }, 100);

    return () => {
      clearTimeout(timerId);
      const currentCanvas = canvasRef.current;
      if (currentCanvas) {
        currentCanvas.dispose();
        canvasRef.current = null;
      }
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, [isOpen, imageUrl]);

  useEffect(() => {
    const handleResize = () => {
      const canvas = canvasRef.current;
      const container = containerRef.current;
      const mainImage = mainImageRef.current;
      if (!container || !canvas || !mainImage) return;

      const W = container.clientWidth;
      const H = 460;
      canvas.setDimensions({ width: W, height: H });

      const scale = Math.min(W / (mainImage.width || 1), H / (mainImage.height || 1));
      mainImage.set({
        scaleX: scale,
        scaleY: scale,
        left: (W - (mainImage.width || 1) * scale) / 2,
        top: (H - (mainImage.height || 1) * scale) / 2,
      });

      const bgImage = canvas.getObjects().find(obj => obj.name === 'background') as fabric.Image;
      if (bgImage) {
        const bgScale = Math.max(W / (bgImage.width || 1), H / (bgImage.height || 1));
        bgImage.set({
          scaleX: bgScale,
          scaleY: bgScale,
          left: (W - (bgImage.width || 1) * bgScale) / 2,
          top: (H - (bgImage.height || 1) * bgScale) / 2,
        });
      }

      // Update positions of blur regions and emojis
      blurRegions.forEach(region => restrictToImageBounds(region.object));
      emojis.forEach(emoji => restrictToImageBounds(emoji.object));

      canvas.renderAll();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [blurRegions, emojis]);

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="4xl"
      title="Edit Image"
      footer={
        <div className="flex justify-between gap-2 w-full">
          <Button
            onClick={handleSave}
            disabled={isLoading || isSaving}
            className="w-full bg-turquoise hover:bg-turquoise/80 text-white rounded-md px-4 py-2"
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
          <Button
            onClick={onClose}
            className="w-full bg-gray-300 dark:bg-zinc-900 hover:bg-gray-500 dark:hover:bg-zinc-900/80 text-white rounded-md px-4 py-2"
          >
            Cancel
          </Button>
        </div>
      }
    >
      <div className="flex p-4 gap-4">
        {/* Image Editing Area */}
        <div
          ref={containerRef}
          className="flex-1 min-w-0 overflow-hidden relative rounded-2xl shadow-lg bg-white/60 dark:bg-zinc-900/60 backdrop-blur-md h-[460px]"
        >
          {isLoading && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
              <div className="w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse">
                <div className="h-full w-full bg-gray-200 dark:bg-zinc-700 animate-pulse"></div>
              </div>
            </div>
          )}

          {loadingError && (
            <div className="absolute inset-0 flex items-center justify-center text-red-500 dark:text-red-400 bg-black/50">
              Failed to load image. Please try again.
            </div>
          )}
        </div>

        {/* Sidebar Controls */}
        <aside className="w-[200px] flex flex-col gap-6 rounded-2xl shadow-xl bg-white/40 dark:bg-zinc-900/40 backdrop-blur-lg p-4 border border-white/30 dark:border-zinc-700/30">
          {editMode === 'crop' ? (
            <CropTools />
          ) : (
            <>
              <h3 className="text-xs font-semibold text-gray-700 dark:text-gray-200 mb-2 tracking-wide uppercase">Tools</h3>
              <div className="grid grid-cols-2 gap-3 items-center justify-center w-full">
                <Tippy content="Crop" placement="top" theme="sugar">
                  <Button
                    variant={(editMode as 'crop' | null) === 'crop' ? 'secondary' : 'ghost'}
                    aria-label="Crop"
                    onClick={toggleCropMode}
                    className={cn(
                      'transition-all w-full aspect-square flex items-center justify-center',
                      (editMode as 'crop' | null) === 'crop' && 'ring-2 ring-turquoise'
                    )}
                  >
                    <Crop className="w-5 h-5" />
                  </Button>
                </Tippy>

                <Tippy content="Full Blur" placement="top" theme="sugar">
                  <Button
                    variant={editMode === 'fullBlur' ? 'secondary' : 'ghost'}
                    size="icon"
                    aria-label="Full Blur"
                    onClick={() => setEditMode('fullBlur')}
                    className={cn(
                      'transition-all w-full aspect-square flex items-center justify-center',
                      editMode === 'fullBlur' && 'ring-2 ring-turquoise'
                    )}
                  >
                    <RiBlurOffLine className="w-5 h-5" />
                  </Button>
                </Tippy>

                <Tippy content="Partial Blur" placement="top" theme="sugar">
                  <Button
                    variant={editMode === 'partialBlur' ? 'secondary' : 'ghost'}
                    size="icon"
                    aria-label="Partial Blur"
                    onClick={() => setEditMode('partialBlur')}
                    className={cn(
                      'transition-all w-full aspect-square flex items-center justify-center',
                      editMode === 'partialBlur' && 'ring-2 ring-turquoise'
                    )}
                  >
                    <Square className="w-5 h-5" />
                  </Button>
                </Tippy>

                <Tippy content="Emoji" placement="top" theme="sugar">
                  <Button
                    variant={editMode === 'emoji' ? 'secondary' : 'ghost'}
                    size="icon"
                    aria-label="Emoji"
                    onClick={() => setEditMode('emoji')}
                    className={cn(
                      'transition-all w-full aspect-square flex items-center justify-center',
                      editMode === 'emoji' && 'ring-2 ring-turquoise'
                    )}
                  >
                    <Smile className="w-5 h-5" />
                  </Button>
                </Tippy>
              </div>

              <div className="my-4 border-t border-white/30 dark:border-zinc-700/30" />

              {/* Contextual Controls */}
              <div className="flex-1 flex flex-col gap-4">
                {(editMode as 'crop' | null) === 'crop' && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">Drag corners to adjust the crop area.</div>
                )}
                {editMode === 'fullBlur' && (
                  <div>
                    <h4 className="text-xs font-medium text-gray-700 dark:text-gray-200 mb-6">Full Image Blur</h4>
                    <Slider
                      value={[fullBlur]}
                      onValueChange={handleFullBlurChange}
                      min={0}
                      max={100}
                      step={1}
                      className="w-full"
                    />
                  </div>
                )}
                {(editMode === 'partialBlur' || editMode === 'emoji') && (
                  <div className="flex gap-2 mb-2">
                    <Button
                      onClick={() => setInteractionMode('add')}
                      variant={interactionMode === 'add' ? 'secondary' : 'ghost'}
                      size="sm"
                      className="flex-1"
                    >
                      Add
                    </Button>
                    <Button
                      onClick={() => setInteractionMode('edit')}
                      variant={interactionMode === 'edit' ? 'secondary' : 'ghost'}
                      size="sm"
                      className="flex-1"
                    >
                      Edit
                    </Button>
                  </div>
                )}
                {editMode === 'partialBlur' && interactionMode === 'add' && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">Click on the image to add a blur region.</div>
                )}
                {editMode === 'partialBlur' && interactionMode === 'edit' && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">Click a blur region to move or resize it.</div>
                )}
                {editMode === 'emoji' && interactionMode === 'add' && (
                  <>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                      Select an emoji, then click on the image to place it.
                    </div>
                    <EmojiPicker onChange={handleEmojiSelect} className="w-full" />
                  </>
                )}
                {editMode === 'emoji' && interactionMode === 'edit' && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">Click an emoji to move or resize it.</div>
                )}
                {(editMode === 'partialBlur' || editMode === 'emoji') && (
                  <Button
                    onClick={handleUndo}
                    variant="outline"
                    className="w-full mt-2 border-gray-200 dark:border-zinc-700 text-gray-900 dark:text-white"
                  >
                    Undo Last {editMode === 'partialBlur' ? 'Blur' : 'Emoji'}
                  </Button>
                )}

                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="w-full border-gray-200 dark:border-zinc-700 text-gray-900 dark:text-white"
                  disabled={isLoading}
                >
                  Reset All
                </Button>
              </div>
            </>
          )}
        </aside>
      </div>
    </AnimatedModal>
  );
};
export default ImageEditDialog;