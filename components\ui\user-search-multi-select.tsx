'use client';

import { useState, useEffect } from 'react';
import { Combobox } from '@headlessui/react';

interface MultiSelectProps {
    selected?: any[];
    onSearch: (query: string) => Promise<any[]>;
    onChange: (selected: any[]) => void;
    displayField: string;
    placeholder?: string;
}

export const MultiSelect = ({
    selected,
    onSearch,
    onChange,
    displayField,
    placeholder
}: MultiSelectProps) => {
    const [query, setQuery] = useState('');
    const [options, setOptions] = useState<any[]>([]);

    useEffect(() => {
        if (query.length > 2) {
            const searchUsers = async () => {
                const results = await onSearch(query);
                setOptions(results);
            };
            searchUsers();
        }
    }, [query, onSearch]);

    return (
        <div className="relative mt-1">
            <Combobox value={selected} onChange={onChange} multiple>
                <div className="relative w-full cursor-default overflow-hidden rounded-lg bg-white text-left border focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm">
                    <div className="flex flex-wrap gap-1 p-1">
                        {selected && selected.map((item) => (
                            <span
                                key={item.id}
                                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                            >
                                {item[displayField]}
                                <button
                                    type="button"
                                    onClick={() => onChange(selected.filter(i => i.id !== item.id))}
                                    className="ml-1 text-blue-400 hover:text-blue-600"
                                >
                                    ×
                                </button>
                            </span>
                        ))}
                        <Combobox.Input
                            className="w-full border-none py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0"
                            placeholder={placeholder}
                            onChange={(event) => setQuery(event.target.value)}
                        />
                    </div>
                    <Combobox.Options className="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                        {options.map((option) => (
                            <Combobox.Option
                                key={option.id}
                                value={option}
                                className={({ active }) =>
                                    `relative cursor-default select-none py-2 pl-10 pr-4 ${
                                        active ? 'bg-teal-600 text-white' : 'text-gray-900'
                                    }`
                                }
                            >
                                {option[displayField]}
                            </Combobox.Option>
                        ))}
                    </Combobox.Options>
                </div>
            </Combobox>
        </div>
    );
};