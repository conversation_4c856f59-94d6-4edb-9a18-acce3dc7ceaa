'use client';

import React, { useState, useEffect } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { X } from 'lucide-react';
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

interface TaggedUser {
  userId: string;
  username: string;
  profilePhoto: string;
}

interface TagPeopleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (tags: TaggedUser[]) => void;
  initialTags?: TaggedUser[];
}

const TagPeopleModal: React.FC<TagPeopleModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialTags = [],
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<TaggedUser[]>(initialTags);
  const [searchResults, setSearchResults] = useState<TaggedUser[]>([]);

  const convexResults = useQuery(
    api.users.searchUsers,
    searchQuery && searchQuery.length >= 3 ? { query: searchQuery } : "skip"
  );

  useEffect(() => {
    if (searchQuery.trim().length < 3) {
      setSearchResults([]);
      return;
    }
    if (convexResults && convexResults.success) {
      setSearchResults(
        Array.isArray(convexResults.data)
          ? convexResults.data.map((u: any) => ({
              userId: u.id,
              username: u.username,
              profilePhoto: u.profilePhoto,
            }))
          : []
      );
    }
  }, [searchQuery, convexResults]);

  useEffect(() => {
    setSelectedUsers(initialTags);
  }, [initialTags, isOpen]);

  const handleSelectUser = (user: TaggedUser) => {
    if (selectedUsers.length < 10 && !selectedUsers.find((u) => u.userId === user.userId)) {
      setSelectedUsers([...selectedUsers, user]);
    }
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUsers(selectedUsers.filter((u) => u.userId !== userId));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(selectedUsers);
    onClose();
  };

  return (
    <AnimatedModal open={isOpen} onOpenChange={onClose} size="md" title="Tag People" closeButton>
      <form onSubmit={handleSubmit}>
        <div className="p-4">
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search for people..."
            className="bg-gray-100 dark:bg-zinc-800 border-gray-200 dark:border-zinc-700 rounded-md text-gray-900 dark:text-white focus:ring-turquoise"
          />
          {selectedUsers.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Tagged People</h4>
              <div className="flex flex-wrap gap-2">
                {selectedUsers.map((user) => (
                  <div
                    key={user.userId}
                    className="flex items-center gap-2 bg-gray-100/50 dark:bg-zinc-800/50 rounded-full px-3 py-1"
                  >
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={user.profilePhoto} />
                      <AvatarFallback>{user.username[0]}</AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-gray-900 dark:text-white">{user.username}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveUser(user.userId)}
                      className="hover:bg-gray-200 dark:hover:bg-zinc-700 rounded-full p-1"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
          <ScrollArea className="mt-4 h-[200px]">
            {searchResults.map((user) => (
              <div
                key={user.userId}
                onClick={() => handleSelectUser(user)}
                className="flex items-center gap-3 p-2 hover:bg-gray-100/50 dark:hover:bg-zinc-800/50 rounded-lg cursor-pointer"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.profilePhoto} />
                  <AvatarFallback>{user.username[0]}</AvatarFallback>
                </Avatar>
                <span className="text-gray-900 dark:text-white">{user.username}</span>
              </div>
            ))}
          </ScrollArea>
        </div>
        <div className="flex justify-end gap-2 p-4 border-t border-gray-200 dark:border-zinc-700">
          <Button
            type="submit"
            className="bg-turquoise hover:bg-turquoise/80 text-white rounded-md px-4 py-2"
            disabled={selectedUsers.length === 0}
          >
            Save
          </Button>
          <Button
            type="button"
            onClick={onClose}
            className="bg-red-400 hover:bg-red-500 text-white rounded-md px-4 py-2"
          >
            Cancel
          </Button>
        </div>
      </form>
    </AnimatedModal>
  );
};

export default TagPeopleModal;