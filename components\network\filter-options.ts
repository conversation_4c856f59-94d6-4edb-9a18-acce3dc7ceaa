export const countryOptions = [
    { value: 'all', label: 'All' },
    { value: 'af', label: 'Afghanistan' },
    { value: 'al', label: 'Albania' },
    { value: 'dz', label: 'Algeria' },
    { value: 'as', label: 'American Samoa' },
    { value: 'ad', label: 'Andorra' },
    { value: 'ao', label: 'Angola' },
    { value: 'ag', label: 'Antigua and Barbuda' },
    { value: 'ar', label: 'Argentina' },
    { value: 'am', label: 'Armenia' },
    { value: 'aw', label: 'Aruba' },
    { value: 'au', label: 'Australia' },
    { value: 'at', label: 'Austria' },
    { value: 'az', label: 'Azerbaijan' },
    { value: 'bs', label: 'The Bahamas' },
    { value: 'bh', label: 'Bahrain' },
    { value: 'bd', label: 'Bangladesh' },
    { value: 'bb', label: 'Barbados' },
    { value: 'by', label: 'Belarus' },
    { value: 'be', label: 'Belgium' },
    { value: 'bz', label: 'Belize' },
    { value: 'bj', label: 'Benin' },
    { value: 'bm', label: 'Bermuda' },
    { value: 'bt', label: 'Bhutan' },
    { value: 'bo', label: 'Bolivia' },
    { value: 'ba', label: 'Bosnia and Herzegovina' },
    { value: 'bw', label: 'Botswana' },
    { value: 'br', label: 'Brazil' },
    { value: 'bn', label: 'Brunei' },
    { value: 'bg', label: 'Bulgaria' },
    { value: 'bf', label: 'Burkina Faso' },
    { value: 'bi', label: 'Burundi' },
    { value: 'cv', label: 'Cabo Verde (Cape Verde)' },
    { value: 'kh', label: 'Cambodia' },
    { value: 'cm', label: 'Cameroon' },
    { value: 'ca', label: 'Canada' },
    { value: 'ky', label: 'Cayman Islands' },
    { value: 'cf', label: 'Central African Republic' },
    { value: 'td', label: 'Chad' },
    { value: 'cl', label: 'Chile' },
    { value: 'cn', label: 'China' },
    { value: 'co', label: 'Colombia' },
    { value: 'km', label: 'Comoros' },
    { value: 'cd', label: 'Democratic Republic of the Congo' },
    { value: 'cg', label: 'Republic of the Congo' },
    { value: 'cr', label: 'Costa Rica' },
    { value: 'ci', label: 'Côte d’Ivoire' },
    { value: 'hr', label: 'Croatia' },
    { value: 'cu', label: 'Cuba' },
    { value: 'cw', label: 'Curaçao' },
    { value: 'cy', label: 'Cyprus' },
    { value: 'cz', label: 'Czech Republic' },
    { value: 'dk', label: 'Denmark' },
    { value: 'dj', label: 'Djibouti' },
    { value: 'dm', label: 'Dominica' },
    { value: 'do', label: 'Dominican Republic' },
    { value: 'tl', label: 'East Timor (Timor-Leste)' },
    { value: 'ec', label: 'Ecuador' },
    { value: 'eg', label: 'Egypt' },
    { value: 'sv', label: 'El Salvador' },
    { value: 'gq', label: 'Equatorial Guinea' },
    { value: 'er', label: 'Eritrea' },
    { value: 'ee', label: 'Estonia' },
    { value: 'sz', label: 'Eswatini (Swaziland)' },
    { value: 'et', label: 'Ethiopia' },
    { value: 'fo', label: 'Faroe Islands' },
    { value: 'fj', label: 'Fiji' },
    { value: 'fi', label: 'Finland' },
    { value: 'fr', label: 'France' },
    { value: 'gf', label: 'French Guiana' },
    { value: 'pf', label: 'French Polynesia' },
    { value: 'ga', label: 'Gabon' },
    { value: 'gm', label: 'The Gambia' },
    { value: 'ps', label: 'Gaza Strip' },
    { value: 'ge', label: 'Georgia' },
    { value: 'de', label: 'Germany' },
    { value: 'gh', label: 'Ghana' },
    { value: 'gr', label: 'Greece' },
    { value: 'gl', label: 'Greenland' },
    { value: 'gd', label: 'Grenada' },
    { value: 'gp', label: 'Guadeloupe' },
    { value: 'gu', label: 'Guam' },
    { value: 'gt', label: 'Guatemala' },
    { value: 'gg', label: 'Guernsey' },
    { value: 'gn', label: 'Guinea' },
    { value: 'gw', label: 'Guinea-Bissau' },
    { value: 'gy', label: 'Guyana' },
    { value: 'ht', label: 'Haiti' },
    { value: 'hn', label: 'Honduras' },
    { value: 'hk', label: 'Hong Kong' },
    { value: 'hu', label: 'Hungary' },
    { value: 'is', label: 'Iceland' },
    { value: 'in', label: 'India' },
    { value: 'id', label: 'Indonesia' },
    { value: 'ir', label: 'Iran' },
    { value: 'iq', label: 'Iraq' },
    { value: 'ie', label: 'Ireland' },
    { value: 'im', label: 'Isle of Man' },
    { value: 'il', label: 'Israel' },
    { value: 'it', label: 'Italy' },
    { value: 'jm', label: 'Jamaica' },
    { value: 'jp', label: 'Japan' },
    { value: 'je', label: 'Jersey' },
    { value: 'jo', label: 'Jordan' },
    { value: 'kz', label: 'Kazakhstan' },
    { value: 'ke', label: 'Kenya' },
    { value: 'ki', label: 'Kiribati' },
    { value: 'kp', label: 'North Korea' },
    { value: 'kr', label: 'South Korea' },
    { value: 'xk', label: 'Kosovo' },
    { value: 'kw', label: 'Kuwait' },
    { value: 'kg', label: 'Kyrgyzstan' },
    { value: 'la', label: 'Laos' },
    { value: 'lv', label: 'Latvia' },
    { value: 'lb', label: 'Lebanon' },
    { value: 'ls', label: 'Lesotho' },
    { value: 'lr', label: 'Liberia' },
    { value: 'ly', label: 'Libya' },
    { value: 'li', label: 'Liechtenstein' },
    { value: 'lt', label: 'Lithuania' },
    { value: 'lu', label: 'Luxembourg' },
    { value: 'mo', label: 'Macau' },
    { value: 'mg', label: 'Madagascar' },
    { value: 'mw', label: 'Malawi' },
    { value: 'my', label: 'Malaysia' },
    { value: 'mv', label: 'Maldives' },
    { value: 'ml', label: 'Mali' },
    { value: 'mt', label: 'Malta' },
    { value: 'mh', label: 'Marshall Islands' },
    { value: 'mq', label: 'Martinique' },
    { value: 'mr', label: 'Mauritania' },
    { value: 'mu', label: 'Mauritius' },
    { value: 'yt', label: 'Mayotte' },
    { value: 'mx', label: 'Mexico' },
    { value: 'fm', label: 'Micronesia' },
    { value: 'md', label: 'Moldova' },
    { value: 'mc', label: 'Monaco' },
    { value: 'mn', label: 'Mongolia' },
    { value: 'me', label: 'Montenegro' },
    { value: 'ma', label: 'Morocco' },
    { value: 'mz', label: 'Mozambique' },
    { value: 'mm', label: 'Myanmar (Burma)' },
    { value: 'na', label: 'Namibia' },
    { value: 'nr', label: 'Nauru' },
    { value: 'np', label: 'Nepal' },
    { value: 'nl', label: 'Netherlands' },
    { value: 'nc', label: 'New Caledonia' },
    { value: 'nz', label: 'New Zealand' },
    { value: 'ni', label: 'Nicaragua' },
    { value: 'ne', label: 'Niger' },
    { value: 'ng', label: 'Nigeria' },
    { value: 'mk', label: 'North Macedonia' },
    { value: 'mp', label: 'Northern Mariana Islands' },
    { value: 'no', label: 'Norway' },
    { value: 'om', label: 'Oman' },
    { value: 'pk', label: 'Pakistan' },
    { value: 'pw', label: 'Palau' },
    { value: 'pa', label: 'Panama' },
    { value: 'pg', label: 'Papua New Guinea' },
    { value: 'py', label: 'Paraguay' },
    { value: 'pe', label: 'Peru' },
    { value: 'ph', label: 'Philippines' },
    { value: 'pl', label: 'Poland' },
    { value: 'pt', label: 'Portugal' },
    { value: 'pr', label: 'Puerto Rico' },
    { value: 'qa', label: 'Qatar' },
    { value: 're', label: 'Réunion' },
    { value: 'ro', label: 'Romania' },
    { value: 'ru', label: 'Russia' },
    { value: 'rw', label: 'Rwanda' },
    { value: 'kn', label: 'Saint Kitts and Nevis' },
    { value: 'lc', label: 'Saint Lucia' },
    { value: 'vc', label: 'Saint Vincent and the Grenadines' },
    { value: 'ws', label: 'Samoa' },
    { value: 'sm', label: 'San Marino' },
    { value: 'st', label: 'Sao Tome and Principe' },
    { value: 'sa', label: 'Saudi Arabia' },
    { value: 'sn', label: 'Senegal' },
    { value: 'rs', label: 'Serbia' },
    { value: 'sc', label: 'Seychelles' },
    { value: 'sl', label: 'Sierra Leone' },
    { value: 'sg', label: 'Singapore' },
    { value: 'sx', label: 'Sint Maarten' },
    { value: 'sk', label: 'Slovakia' },
    { value: 'si', label: 'Slovenia' },
    { value: 'sb', label: 'Solomon Islands' },
    { value: 'so', label: 'Somalia' },
    { value: 'za', label: 'South Africa' },
    { value: 'es', label: 'Spain' },
    { value: 'lk', label: 'Sri Lanka' },
    { value: 'sd', label: 'Sudan' },
    { value: 'ss', label: 'South Sudan' },
    { value: 'sr', label: 'Suriname' },
    { value: 'se', label: 'Sweden' },
    { value: 'ch', label: 'Switzerland' },
    { value: 'sy', label: 'Syria' },
    { value: 'tw', label: 'Taiwan' },
    { value: 'tj', label: 'Tajikistan' },
    { value: 'tz', label: 'Tanzania' },
    { value: 'th', label: 'Thailand' },
    { value: 'tg', label: 'Togo' },
    { value: 'to', label: 'Tonga' },
    { value: 'tt', label: 'Trinidad and Tobago' },
    { value: 'tn', label: 'Tunisia' },
    { value: 'tr', label: 'Turkey' },
    { value: 'tm', label: 'Turkmenistan' },
    { value: 'tv', label: 'Tuvalu' },
    { value: 'ug', label: 'Uganda' },
    { value: 'ua', label: 'Ukraine' },
    { value: 'ae', label: 'United Arab Emirates' },
    { value: 'gb', label: 'United Kingdom' },
    { value: 'us', label: 'United States' },
    { value: 'vi', label: 'United States Virgin Islands' },
    { value: 'uy', label: 'Uruguay' },
    { value: 'uz', label: 'Uzbekistan' },
    { value: 'vu', label: 'Vanuatu' },
    { value: 'va', label: 'Vatican City' },
    { value: 've', label: 'Venezuela' },
    { value: 'vn', label: 'Vietnam' },
    { value: 'ps', label: 'West Bank' },
    { value: 'ye', label: 'Yemen' },
    { value: 'zm', label: 'Zambia' },
    { value: 'zw', label: 'Zimbabwe' },
];

export const accountTypeOptions = [
    { value: 'all', label: 'All' },
    { value: 'creator', label: 'Creator' },
    { value: 'studio', label: 'Studio' },
];

export const ageOptions = [
    { value: 'all', label: 'All' },
    ...Array.from({ length: 82 }, (_, i) => ({
        value: String(i + 18),
        label: String(i + 18)
    })) // Explicitly lists ages 18–99
];

export const ethnicityOptions = [
    { value: 'all', label: 'All' },
    { value: 'white', label: 'White' },
    { value: 'ebony', label: 'Ebony' },
    { value: 'mixed', label: 'Mixed' },
    { value: 'latin', label: 'Latin' },
    { value: 'hispanic', label: 'Hispanic' },
    { value: 'asian', label: 'Asian' },
    { value: 'indian', label: 'Indian' },
    { value: 'arab', label: 'Arab' },
    { value: 'middle-eastern', label: 'Middle Eastern' },
    { value: 'other', label: 'Other' },
];

export const bodyTypeOptions = [
    { value: 'all', label: 'All' },
    { value: 'slim', label: 'Slim' },
    { value: 'petite', label: 'Petite' },
    { value: 'average', label: 'Average' },
    { value: 'curvy', label: 'Curvy' },
    { value: 'thick', label: 'Thick' },
    { value: 'athletic', label: 'Athletic' },
    { value: 'muscular', label: 'Muscular' },
    { value: 'plus-size', label: 'Plus Size' },
];

export const breastTypeOptions = [
    { value: 'all', label: 'All' },
    { value: 'fake', label: 'Fake' },
    { value: 'natural', label: 'Natural' },
];

export const breastSizeOptions = [
    { value: 'all', label: 'All' },
    { value: 'flat', label: 'Flat' },
    { value: 'tiny', label: 'Tiny' },
    { value: 'small', label: 'Small' },
    { value: 'medium', label: 'Medium' },
    { value: 'large', label: 'Large' },
    { value: 'extra-large', label: 'Extra Large' },
];

export const pubicHairOptions = [
    { value: 'all', label: 'All' },
    { value: 'hairy', label: 'Hairy' },
    { value: 'shaved', label: 'Shaved' },
    { value: 'trimmed', label: 'Trimmed' },
];

export const interestedInOptions = [
    { value: 'all', label: 'All' },
    { value: 'men', label: 'Men' },
    { value: 'women', label: 'Women' },
    { value: 'non-binary', label: 'Non-Binary' },
];

export const eyeColourOptions = [
    { value: 'all', label: 'All' },
    { value: 'grey', label: 'Grey' },
    { value: 'blue', label: 'Blue' },
    { value: 'amber', label: 'Amber' },
    { value: 'light-brown', label: 'Light Brown' },
    { value: 'dark-brown', label: 'Dark Brown' },
    { value: 'hazel', label: 'Hazel' },
    { value: 'green', label: 'Green' },
    { value: 'violet', label: 'Violet' },
    { value: 'other', label: 'Other' },
];

export const hairColourOptions = [
    { value: 'all', label: 'All' },
    { value: 'black', label: 'Black' },
    { value: 'brunette', label: 'Brunette' },
    { value: 'blonde', label: 'Blonde' },
    { value: 'redhead', label: 'Redhead' },
    { value: 'grey', label: 'Grey' },
    { value: 'white', label: 'White' },
    { value: 'blue', label: 'Blue' },
    { value: 'green', label: 'Green' },
    { value: 'purple', label: 'Purple' },
    { value: 'pink', label: 'Pink' },
    { value: 'yellow', label: 'Yellow' },
    { value: 'orange', label: 'Orange' },
    { value: 'multi-coloured', label: 'Multi-coloured' },
    { value: 'other', label: 'Other' },
];

export const languageOptions = [
    { value: 'all', label: 'All' },
    { value: 'aa', label: 'Afaraf', flag: 'dj' },
    { value: 'ab', label: 'Аҧсуа', flag: 'ge' },
    { value: 'af', label: 'Afrikaans', flag: 'za' },
    { value: 'ak', label: 'Akan', flag: 'gh' },
    { value: 'am', label: 'አማርኛ', flag: 'et' },
    { value: 'ar', label: 'العربية', flag: 'sa' },
    { value: 'as', label: 'অসমীয়া', flag: 'in' },
    { value: 'av', label: 'Авар', flag: 'ru' },
    { value: 'ay', label: 'Aymar', flag: 'bo' },
    { value: 'az', label: 'Azərbaycan', flag: 'az' },
    { value: 'ba', label: 'Башҡорт', flag: 'ru' },
    { value: 'be', label: 'Беларуская', flag: 'by' },
    { value: 'bg', label: 'Български', flag: 'bg' },
    { value: 'bh', label: 'Bihari', flag: 'in' },
    { value: 'bi', label: 'Bislama', flag: 'vu' },
    { value: 'bm', label: 'Bambara', flag: 'ml' },
    { value: 'bn', label: 'বাংলা', flag: 'bd' },
    { value: 'bo', label: 'བོད་པའི', flag: 'cn' },
    { value: 'br', label: 'Brezhoneg', flag: 'fr' },
    { value: 'bs', label: 'Bosanski', flag: 'ba' },
    { value: 'ca', label: 'Català', flag: 'es' },
    { value: 'ce', label: 'Нохчийн', flag: 'ru' },
    { value: 'ch', label: 'Chamoru', flag: 'gu' },
    { value: 'co', label: 'Corsu', flag: 'fr' },
    { value: 'cr', label: 'Nēhiyawēwin', flag: 'ca' },
    { value: 'cs', label: 'Čeština', flag: 'cz' },
    { value: 'cu', label: 'Словѣньскъ', flag: 'ru' },
    { value: 'cv', label: 'Чӑвашла', flag: 'ru' },
    { value: 'cy', label: 'Cymraeg', flag: 'gb' },
    { value: 'da', label: 'Dansk', flag: 'dk' },
    { value: 'de', label: 'Deutsch', flag: 'de' },
    { value: 'dv', label: 'ދިވެހި', flag: 'mv' },
    { value: 'dz', label: 'རྫོང་ཁ', flag: 'bt' },
    { value: 'ee', label: 'Eʋegbe', flag: 'gh' },
    { value: 'el', label: 'Ελληνικά', flag: 'gr' },
    { value: 'en', label: 'English', flag: 'gb' },
    { value: 'eo', label: 'Esperanto', flag: 'un' },
    { value: 'es', label: 'Español', flag: 'es' },
    { value: 'et', label: 'Eesti', flag: 'ee' },
    { value: 'eu', label: 'Euskara', flag: 'es' },
    { value: 'fa', label: 'فارسی', flag: 'ir' },
    { value: 'ff', label: 'Fulfulde', flag: 'sn' },
    { value: 'fi', label: 'Suomi', flag: 'fi' },
    { value: 'fj', label: 'Na Vosa Vakaviti', flag: 'fj' },
    { value: 'fo', label: 'Føroyskt', flag: 'fo' },
    { value: 'fr', label: 'Français', flag: 'fr' },
    { value: 'fy', label: 'Frysk', flag: 'nl' },
    { value: 'ga', label: 'Gaelige', flag: 'ie' },
    { value: 'gd', label: 'Gàidhlig', flag: 'gb' },
    { value: 'gl', label: 'Galego', flag: 'es' },
    { value: 'gn', label: 'Avañeʼẽ', flag: 'py' },
    { value: 'gu', label: 'ગુજરાતી', flag: 'in' },
    { value: 'gv', label: 'Gaelg', flag: 'im' },
    { value: 'ha', label: 'Hausa', flag: 'ng' },
    { value: 'haw', label: 'ʻŌlelo Hawaiʻi', flag: 'us' },
    { value: 'he', label: 'עברית', flag: 'il' },
    { value: 'hi', label: 'हिन्दी', flag: 'in' },
    { value: 'ho', label: 'Hiri Motu', flag: 'pg' },
    { value: 'hr', label: 'Hrvatski', flag: 'hr' },
    { value: 'ht', label: 'Kreyòl Ayisyen', flag: 'ht' },
    { value: 'hu', label: 'Magyar', flag: 'hu' },
    { value: 'hy', label: 'Հայերեն', flag: 'am' },
    { value: 'hz', label: 'Otjiherero', flag: 'na' },
    { value: 'ia', label: 'Interlingua', flag: 'un' },
    { value: 'id', label: 'Bahasa Indonesia', flag: 'id' },
    { value: 'ie', label: 'Interlingue', flag: 'un' },
    { value: 'ig', label: 'Igbo', flag: 'ng' },
    { value: 'ii', label: 'ꆈꌠ꒿', flag: 'cn' },
    { value: 'ik', label: 'Iñupiaq', flag: 'us' },
    { value: 'io', label: 'Ido', flag: 'un' },
    { value: 'is', label: 'Íslenska', flag: 'is' },
    { value: 'it', label: 'Italiano', flag: 'it' },
    { value: 'iu', label: 'ᐃᓄᒃᑎᑐᑦ', flag: 'ca' },
    { value: 'ja', label: '日本語', flag: 'jp' },
    { value: 'jv', label: 'Basa Jawa', flag: 'id' },
    { value: 'ka', label: 'ქართული', flag: 'ge' },
    { value: 'kg', label: 'Kikongo', flag: 'cd' },
    { value: 'ki', label: 'Gikuyu', flag: 'ke' },
    { value: 'kj', label: 'Kwanyama', flag: 'na' },
    { value: 'kk', label: 'Қазақша', flag: 'kz' },
    { value: 'kl', label: 'Kalaallisut', flag: 'gl' },
    { value: 'km', label: 'ភាសាខ្មែរ', flag: 'kh' },
    { value: 'kn', label: 'ಕನ್ನಡ', flag: 'in' },
    { value: 'ko', label: '한국어', flag: 'kr' },
    { value: 'kr', label: 'Kanuri', flag: 'ng' },
    { value: 'ks', label: 'कश्मीरी', flag: 'in' },
    { value: 'ku', label: 'Kurdî', flag: 'tr' },
    { value: 'kv', label: 'Коми', flag: 'ru' },
    { value: 'kw', label: 'Kernowek', flag: 'gb' },
    { value: 'ky', label: 'Кыргызча', flag: 'kg' },
    { value: 'la', label: 'Latina', flag: 'va' },
    { value: 'lb', label: 'Lëtzebuergesch', flag: 'lu' },
    { value: 'lg', label: 'Luganda', flag: 'ug' },
    { value: 'li', label: 'Limburgs', flag: 'nl' },
    { value: 'ln', label: 'Lingála', flag: 'cd' },
    { value: 'lo', label: 'ພາສາລາວ', flag: 'la' },
    { value: 'lt', label: 'Lietuvių', flag: 'lt' },
    { value: 'lu', label: 'Kiluba', flag: 'cd' },
    { value: 'lv', label: 'Latviešu', flag: 'lv' },
    { value: 'mg', label: 'Malagasy', flag: 'mg' },
    { value: 'mh', label: 'Kajin M̧ajeļ', flag: 'mh' },
    { value: 'mi', label: 'Māori', flag: 'nz' },
    { value: 'mk', label: 'Македонски', flag: 'mk' },
    { value: 'ml', label: 'മലയാളം', flag: 'in' },
    { value: 'mn', label: 'Монгол', flag: 'mn' },
    { value: 'mr', label: 'मराठी', flag: 'in' },
    { value: 'ms', label: 'Bahasa Melayu', flag: 'my' },
    { value: 'mt', label: 'Malti', flag: 'mt' },
    { value: 'my', label: 'မြန်မာဘာသာ', flag: 'mm' },
    { value: 'na', label: 'Nauru', flag: 'nr' },
    { value: 'nb', label: 'Norsk Bokmål', flag: 'no' },
    { value: 'nd', label: 'isiNdebele', flag: 'zw' },
    { value: 'ne', label: 'नेपाली', flag: 'np' },
    { value: 'ng', label: 'Ndonga', flag: 'na' },
    { value: 'nl', label: 'Nederlands', flag: 'nl' },
    { value: 'nn', label: 'Norsk Nynorsk', flag: 'no' },
    { value: 'no', label: 'Norsk', flag: 'no' },
    { value: 'nr', label: 'isiNdebele', flag: 'za' },
    { value: 'nv', label: 'Diné bizaad', flag: 'us' },
    { value: 'ny', label: 'Chichewa', flag: 'mw' },
    { value: 'oc', label: 'Occitan', flag: 'fr' },
    { value: 'oj', label: 'ᐊᓂᔑᓈᐯᒧᐎᓐ', flag: 'ca' },
    { value: 'om', label: 'Afaan Oromoo', flag: 'et' },
    { value: 'or', label: 'ଓଡ଼ିଆ', flag: 'in' },
    { value: 'os', label: 'Ирон', flag: 'ru' },
    { value: 'pa', label: 'ਪੰਜਾਬੀ', flag: 'in' },
    { value: 'pi', label: 'Pāli', flag: 'in' },
    { value: 'pl', label: 'Polski', flag: 'pl' },
    { value: 'ps', label: 'پښتو', flag: 'af' },
    { value: 'pt', label: 'Português', flag: 'pt' },
    { value: 'qu', label: 'Runa Simi', flag: 'pe' },
    { value: 'rm', label: 'Rumantsch', flag: 'ch' },
    { value: 'rn', label: 'Kirundi', flag: 'bi' },
    { value: 'ro', label: 'Română', flag: 'ro' },
    { value: 'ru', label: 'Русский', flag: 'ru' },
    { value: 'rw', label: 'Kinyarwanda', flag: 'rw' },
    { value: 'sa', label: 'संस्कृतम्', flag: 'in' },
    { value: 'sc', label: 'Sardu', flag: 'it' },
    { value: 'sd', label: 'سنڌي', flag: 'pk' },
    { value: 'se', label: 'Sámegiella', flag: 'no' },
    { value: 'sg', label: 'Sängö', flag: 'cf' },
    { value: 'si', label: 'සිංහල', flag: 'lk' },
    { value: 'sk', label: 'Slovenčina', flag: 'sk' },
    { value: 'sl', label: 'Slovenščina', flag: 'si' },
    { value: 'sm', label: 'Gagana Samoa', flag: 'ws' },
    { value: 'sn', label: 'chiShona', flag: 'zw' },
    { value: 'so', label: 'Soomaali', flag: 'so' },
    { value: 'sq', label: 'Shqip', flag: 'al' },
    { value: 'sr', label: 'Српски', flag: 'rs' },
    { value: 'ss', label: 'siSwati', flag: 'sz' },
    { value: 'st', label: 'Sesotho', flag: 'ls' },
    { value: 'su', label: 'Basa Sunda', flag: 'id' },
    { value: 'sv', label: 'Svenska', flag: 'se' },
    { value: 'sw', label: 'Kiswahili', flag: 'ke' },
    { value: 'ta', label: 'தமிழ்', flag: 'in' },
    { value: 'te', label: 'తెలుగు', flag: 'in' },
    { value: 'tg', label: 'Тоҷикӣ', flag: 'tj' },
    { value: 'th', label: 'ไทย', flag: 'th' },
    { value: 'ti', label: 'ትግርኛ', flag: 'er' },
    { value: 'tk', label: 'Türkmen', flag: 'tm' },
    { value: 'tl', label: 'Tagalog', flag: 'ph' },
    { value: 'tn', label: 'Setswana', flag: 'bw' },
    { value: 'to', label: 'Lea Faka-Tonga', flag: 'to' },
    { value: 'tr', label: 'Türkçe', flag: 'tr' },
    { value: 'ts', label: 'Xitsonga', flag: 'za' },
    { value: 'tt', label: 'Татарча', flag: 'ru' },
    { value: 'tw', label: 'Twi', flag: 'gh' },
    { value: 'ty', label: 'Reo Tahiti', flag: 'pf' },
    { value: 'ug', label: 'ئۇيغۇرچە', flag: 'cn' },
    { value: 'uk', label: 'Українська', flag: 'ua' },
    { value: 'ur', label: 'اردو', flag: 'pk' },
    { value: 'uz', label: 'Oʻzbek', flag: 'uz' },
    { value: 've', label: 'Tshivenḓa', flag: 'za' },
    { value: 'vi', label: 'Tiếng Việt', flag: 'vn' },
    { value: 'vo', label: 'Volapük', flag: 'un' },
    { value: 'wa', label: 'Walon', flag: 'be' },
    { value: 'wo', label: 'Wolof', flag: 'sn' },
    { value: 'xh', label: 'isiXhosa', flag: 'za' },
    { value: 'yi', label: 'ייִדיש', flag: 'il' },
    { value: 'yo', label: 'Yorùbá', flag: 'ng' },
    { value: 'za', label: 'Zhuang', flag: 'cn' },
    { value: 'zh-CN', label: '中文 (简体)', flag: 'cn' },
    { value: 'zh-TW', label: '中文 (繁體)', flag: 'tw' },
    { value: 'zu', label: 'isiZulu', flag: 'za' }
];

export const astrologyOptions = [
    { value: 'all', label: 'All' },
    { value: 'aries', label: 'Aries' },
    { value: 'taurus', label: 'Taurus' },
    { value: 'gemini', label: 'Gemini' },
    { value: 'cancer', label: 'Cancer' },
    { value: 'leo', label: 'Leo' },
    { value: 'virgo', label: 'Virgo' },
    { value: 'libra', label: 'Libra' },
    { value: 'scorpio', label: 'Scorpio' },
    { value: 'sagittarius', label: 'Sagittarius' },
    { value: 'capricorn', label: 'Capricorn' },
    { value: 'aquarius', label: 'Aquarius' },
    { value: 'pisces', label: 'Pisces' },
];

export const heightOptions = [
    { value: 'all', label: 'All' },
    ...Array.from({ length: 35 }, (_, i) => {
        const feet = 3 + Math.floor(i / 12);
        const inches = i % 12;
        const cm = Math.round((feet * 12 + inches) * 2.54);
        return {
            value: `${feet}-${inches}`,
            label: `${feet} ft ${inches} in (${cm} cm)`
        };
    }) // Generates 3 ft 0 in to 6 ft 11 in (every inch)
];

export const genderOptions = [
    { value: 'all', label: 'All' },
    { value: 'female', label: 'Female' },
    { value: 'male', label: 'Male' },
    { value: 'trans', label: 'Trans' },
];

export const relationshipStatusOptions = [
    { value: 'all', label: 'All' },
    { value: 'single', label: 'Single' },
    { value: 'in a relationship', label: 'In a Relationship' },
    { value: 'married', label: 'Married' },
    { value: 'divorced', label: 'Divorced' },
    { value: 'widowed', label: 'Widowed' },
    { value: 'complicated', label: "It's Complicated" },
];

export const interestOptions = [
    { value: 'all', label: 'All' },
    { value: 'music', label: 'Music' },
    { value: 'travel', label: 'Travel' },
    { value: 'sports', label: 'Sports' },
];

export const fetishOptions = [
    { value: 'all', label: 'All' },
    { value: 'roleplay', label: 'Roleplay' },
    { value: 'bdsm', label: 'BDSM' },
];

export const piercingOptions = [
    { value: 'all', label: 'All' },
    { value: 'none', label: 'None' },
    { value: 'ears', label: 'Ears' },
    { value: 'nose', label: 'Nose' },
    { value: 'lip', label: 'Lip' },
    { value: 'tongue', label: 'Tongue' },
    { value: 'navel', label: 'Navel' },
];

export const tattooOptions = [
    { value: 'all', label: 'All' },
    { value: 'none', label: 'None' },
    { value: 'face', label: 'Face' },
    { value: 'neck', label: 'Neck' },
    { value: 'chest', label: 'Chest' },
    { value: 'genital', label: 'Genital' },
    { value: 'stomach', label: 'Stomach' },
    { value: 'buttocks', label: 'Buttocks' },
    { value: 'leftFoot', label: 'Left Foot' },
    { value: 'leftHand', label: 'Left Hand' },
    { value: 'lowerBack', label: 'Lower Back' },
    { value: 'rightFoot', label: 'Right Foot' },
    { value: 'rightHand', label: 'Right Hand' },
    { value: 'upperBack', label: 'Upper Back' },
    { value: 'lowerLeftLeg', label: 'Lower Left Leg' },
    { value: 'upperLeftLeg', label: 'Upper Left Leg' },
    { value: 'lowerRightLeg', label: 'Lower Right Leg' },
    { value: 'upperRightLeg', label: 'Upper Right Leg' },
];

export const nicheOptions = [
    { value: 'all', label: 'All' },
    { value: "amateur", label: "Amateur" },
    { value: "anal", label: "Anal" },
    { value: "asian", label: "Asian" },
    { value: "bbw", label: "BBW" },
    { value: "bdsm", label: "BDSM" },
    { value: "big-ass", label: "Big Ass" },
    { value: "big-boobs", label: "Big Boobs" },
    { value: "big-cock", label: "Big Cock" },
    { value: "bisexual", label: "Bisexual" },
    { value: "black", label: "Black" },
    { value: "blowjob", label: "Blowjob" },
    { value: "bondage", label: "Bondage" },
    { value: "brazilian", label: "Brazilian" },
    { value: "brunette", label: "Brunette" },
    { value: "bukkake", label: "Bukkake" },
    { value: "cam-girl", label: "Cam Girl" },
    { value: "cartoon", label: "Cartoon" },
    { value: "casting", label: "Casting" },
    { value: "caucasian", label: "Caucasian" },
    { value: "cheating", label: "Cheating" },
    { value: "chubby", label: "Chubby" },
    { value: "cosplay", label: "Cosplay" },
    { value: "couple", label: "Couple" },
    { value: "creampie", label: "Creampie" },
    { value: "cuckold", label: "Cuckold" },
    { value: "cumshot", label: "Cumshot" },
    { value: "curvy", label: "Curvy" },
    { value: "deepthroat", label: "Deepthroat" },
    { value: "dildo", label: "Dildo" },
    { value: "doggy-style", label: "Doggy Style" },
    { value: "domination", label: "Domination" },
    { value: "double-penetration", label: "Double Penetration" },
    { value: "ebony", label: "Ebony" },
    { value: "erotic", label: "Erotic" },
    { value: "exhibitionist", label: "Exhibitionist" },
    { value: "facial", label: "Facial" },
    { value: "femdom", label: "Femdom" },
    { value: "fetish", label: "Fetish" },
    { value: "fingering", label: "Fingering" },
    { value: "fisting", label: "Fisting" },
    { value: "foot-fetish", label: "Foot Fetish" },
    { value: "gangbang", label: "Gangbang" },
    { value: "gay", label: "Gay" },
    { value: "glamour", label: "Glamour" },
    { value: "group-sex", label: "Group Sex" },
    { value: "hairy", label: "Hairy" },
    { value: "handjob", label: "Handjob" },
    { value: "hardcore", label: "Hardcore" },
    { value: "hentai", label: "Hentai" },
    { value: "interracial", label: "Interracial" },
    { value: "latina", label: "Latina" },
    { value: "lesbian", label: "Lesbian" },
    { value: "lingerie", label: "Lingerie" },
    { value: "massage", label: "Massage" },
    { value: "masturbation", label: "Masturbation" },
    { value: "mature", label: "Mature" },
    { value: "milf", label: "MILF" },
    { value: "nurse", label: "Nurse" },
    { value: "office", label: "Office" },
    { value: "oral", label: "Oral" },
    { value: "orgasm", label: "Orgasm" },
    { value: "orgy", label: "Orgy" },
    { value: "outdoor", label: "Outdoor" },
    { value: "pantyhose", label: "Pantyhose" },
    { value: "petite", label: "Petite" },
    { value: "pornstar", label: "Pornstar" },
    { value: "pov", label: "POV" },
    { value: "public", label: "Public" },
    { value: "pussy-licking", label: "Pussy Licking" },
    { value: "redhead", label: "Redhead" },
    { value: "roleplay", label: "Roleplay" },
    { value: "rough-sex", label: "Rough Sex" },
    { value: "school", label: "School" },
    { value: "shemale", label: "Shemale" },
    { value: "softcore", label: "Softcore" },
    { value: "solo", label: "Solo" },
    { value: "spanking", label: "Spanking" },
    { value: "squirt", label: "Squirt" },
    { value: "stockings", label: "Stockings" },
    { value: "striptease", label: "Striptease" },
    { value: "swingers", label: "Swingers" },
    { value: "tattooed", label: "Tattooed" },
    { value: "teacher", label: "Teacher" },
    { value: "teen", label: "Teen" },
    { value: "threesome", label: "Threesome" },
    { value: "toys", label: "Toys" },
    { value: "trans", label: "Trans" },
    { value: "uniform", label: "Uniform" },
    { value: "vintage", label: "Vintage" },
    { value: "voyeur", label: "Voyeur" },
    { value: "webcam", label: "Webcam" },
    { value: "arab", label: "Arab" },
    { value: "australian", label: "Australian" },
    { value: "blonde", label: "Blonde" },
    { value: "body-worship", label: "Body Worship" },
    { value: "colombian", label: "Colombian" },
    { value: "cowgirl", label: "Cowgirl" },
    { value: "crossdresser", label: "Crossdresser" },
    { value: "czech", label: "Czech" },
    { value: "dancing", label: "Dancing" },
    { value: "ebony-ass", label: "Ebony Ass" },
    { value: "emo", label: "Emo" },
    { value: "face-sitting", label: "Face Sitting" },
    { value: "furry", label: "Furry" },
    { value: "german", label: "German" },
    { value: "goth", label: "Goth" },
    { value: "granny", label: "Granny" },
    { value: "hijab", label: "Hijab" },
    { value: "indian", label: "Indian" },
    { value: "italian", label: "Italian" },
    { value: "japanese", label: "Japanese" },
    { value: "kissing", label: "Kissing" },
    { value: "korean", label: "Korean" },
    { value: "latex", label: "Latex" },
    { value: "leather", label: "Leather" },
    { value: "maid", label: "Maid" },
    { value: "mexican", label: "Mexican" },
    { value: "missionary", label: "Missionary" },
    { value: "muscular", label: "Muscular" },
    { value: "nylon", label: "Nylon" },
    { value: "oiled", label: "Oiled" },
    { value: "pegging", label: "Pegging" },
    { value: "piercing", label: "Piercing" },
    { value: "pregnant", label: "Pregnant" },
    { value: "rimming", label: "Rimming" },
    { value: "russian", label: "Russian" },
    { value: "shaved", label: "Shaved" },
    { value: "skinny", label: "Skinny" },
    { value: "smoking", label: "Smoking" },
    { value: "strapon", label: "Strapon" },
    { value: "submission", label: "Submission" },
    { value: "taboo", label: "Taboo" },
    { value: "thai", label: "Thai" },
    { value: "titjob", label: "Titjob" },
    { value: "twerking", label: "Twerking" },
    { value: "uncensored", label: "Uncensored" },
    { value: "upskirt", label: "Upskirt" },
    { value: "wife", label: "Wife" },
    { value: "yoga", label: "Yoga" }
];

export const waistOptions = [
    { value: 'all', label: 'All' },
    ...Array.from({ length: 29 }, (_, i) => {
        const inches = 22 + i;
        const cm = Math.round(inches * 2.54);
        return {
            value: String(inches),
            label: `${inches}" (${cm} cm)`
        };
    }) // 22" to 50" (every inch)
];

export const hipsOptions = [
    { value: 'all', label: 'All' },
    ...Array.from({ length: 27 }, (_, i) => {
        const inches = 30 + i;
        const cm = Math.round(inches * 2.54);
        return {
            value: String(inches),
            label: `${inches}" (${cm} cm)`
        };
    }) // 30" to 56" (every inch)
];

export const buttOptions = [
    { value: 'all', label: 'All' },
    { value: 'tiny', label: 'Tiny' },
    { value: 'small', label: 'Small' },
    { value: 'medium', label: 'Medium' },
    { value: 'large', label: 'Large' },
    { value: 'extra-large', label: 'Extra Large' },
];

export const shoeSizeOptions: Record<string, Array<{ value: string, label: string }>> = {
    female: [
        { value: 'all', label: 'All' },
        ...Array.from({ length: 17 }, (_, i) => {
            const size = 4 + (i * 0.5);
            const eu = Math.round(35 + i * 0.5);
            return {
                value: `w-${size.toString().replace('.', '-')}`,
                label: `Women's ${size} (EU ${eu})`
            };
        })
    ],
    male: [
        { value: 'all', label: 'All' },
        ...Array.from({ length: 19 }, (_, i) => {
            const size = 6 + (i * 0.5);
            const eu = Math.round(39 + i * 0.5);
            return {
                value: `m-${size.toString().replace('.', '-')}`,
                label: `Men's ${size} (EU ${eu})`
            };
        })
    ],
    trans: [
        { value: 'all', label: 'All' },
        ...Array.from({ length: 17 }, (_, i) => {
            const size = 4 + (i * 0.5);
            const eu = Math.round(35 + i * 0.5);
            return {
                value: `w-${size.toString().replace('.', '-')}`,
                label: `Women's ${size} (EU ${eu})`
            };
        }),
        ...Array.from({ length: 19 }, (_, i) => {
            const size = 6 + (i * 0.5);
            const eu = Math.round(39 + i * 0.5);
            return {
                value: `m-${size.toString().replace('.', '-')}`,
                label: `Men's ${size} (EU ${eu})`
            };
        })
    ]
};
