import { jsxs as o, jsx as r } from "react/jsx-runtime";
import { memo as e } from "react";
const l = (c) => /* @__PURE__ */ o(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 16 4",
    fill: "none",
    ...c,
    children: [
      /* @__PURE__ */ r("circle", { cx: 2, cy: 2, r: 2, fill: "currentColor" }),
      /* @__PURE__ */ r("circle", { cx: 8, cy: 2, r: 2, fill: "currentColor" }),
      /* @__PURE__ */ r("circle", { cx: 14, cy: 2, r: 2, fill: "currentColor" })
    ]
  }
), n = e(l);
export {
  n as default
};
