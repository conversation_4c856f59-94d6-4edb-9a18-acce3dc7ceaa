/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as accounts from "../accounts.js";
import type * as auth from "../auth.js";
import type * as content from "../content.js";
import type * as explore from "../explore.js";
import type * as folders from "../folders.js";
import type * as functions_backfillAuthUserIds from "../functions/backfillAuthUserIds.js";
import type * as functions_migrations from "../functions/migrations.js";
import type * as functions_normalizeUsernames from "../functions/normalizeUsernames.js";
import type * as functions_sendPushNotificationAction from "../functions/sendPushNotificationAction.js";
import type * as hashtags from "../hashtags.js";
import type * as http from "../http.js";
import type * as linkAuthAccount from "../linkAuthAccount.js";
import type * as media from "../media.js";
import type * as mediaLibrary from "../mediaLibrary.js";
import type * as media_cleanup from "../media_cleanup.js";
import type * as messageSearch from "../messageSearch.js";
import type * as messages from "../messages.js";
import type * as node_mediaHelpers from "../node/mediaHelpers.js";
import type * as node_verifyWallet from "../node/verifyWallet.js";
import type * as notifications from "../notifications.js";
import type * as otp_ResendOTP from "../otp/ResendOTP.js";
import type * as otp_TwilioOTP from "../otp/TwilioOTP.js";
import type * as otp_TwilioSDK from "../otp/TwilioSDK.js";
import type * as otp_TwilioVerify from "../otp/TwilioVerify.js";
import type * as otp_VerificationCodeEmail from "../otp/VerificationCodeEmail.js";
import type * as passwordReset_PasswordResetEmail from "../passwordReset/PasswordResetEmail.js";
import type * as passwordReset_ResendOTPPasswordReset from "../passwordReset/ResendOTPPasswordReset.js";
import type * as profileViewers from "../profileViewers.js";
import type * as userDevices from "../userDevices.js";
import type * as users from "../users.js";
import type * as utils from "../utils.js";
import type * as walletAuth from "../walletAuth.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  accounts: typeof accounts;
  auth: typeof auth;
  content: typeof content;
  explore: typeof explore;
  folders: typeof folders;
  "functions/backfillAuthUserIds": typeof functions_backfillAuthUserIds;
  "functions/migrations": typeof functions_migrations;
  "functions/normalizeUsernames": typeof functions_normalizeUsernames;
  "functions/sendPushNotificationAction": typeof functions_sendPushNotificationAction;
  hashtags: typeof hashtags;
  http: typeof http;
  linkAuthAccount: typeof linkAuthAccount;
  media: typeof media;
  mediaLibrary: typeof mediaLibrary;
  media_cleanup: typeof media_cleanup;
  messageSearch: typeof messageSearch;
  messages: typeof messages;
  "node/mediaHelpers": typeof node_mediaHelpers;
  "node/verifyWallet": typeof node_verifyWallet;
  notifications: typeof notifications;
  "otp/ResendOTP": typeof otp_ResendOTP;
  "otp/TwilioOTP": typeof otp_TwilioOTP;
  "otp/TwilioSDK": typeof otp_TwilioSDK;
  "otp/TwilioVerify": typeof otp_TwilioVerify;
  "otp/VerificationCodeEmail": typeof otp_VerificationCodeEmail;
  "passwordReset/PasswordResetEmail": typeof passwordReset_PasswordResetEmail;
  "passwordReset/ResendOTPPasswordReset": typeof passwordReset_ResendOTPPasswordReset;
  profileViewers: typeof profileViewers;
  userDevices: typeof userDevices;
  users: typeof users;
  utils: typeof utils;
  walletAuth: typeof walletAuth;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
