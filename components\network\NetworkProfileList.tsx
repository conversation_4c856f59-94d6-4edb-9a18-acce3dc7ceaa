import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { NetworkProfile } from '@/types/network';
import { Skeleton } from '@/components/ui/skeleton';

interface ProfileListProps {
  profile: NetworkProfile;
}

export default function NetworkProfileList({ profile }: ProfileListProps) {
  const { username, profilePhoto, coverBanner, coverBannerType, niche, isVerified } = profile;
  const [bannerLoading, setBannerLoading] = useState(true);

  return (
    <div className="relative bg-transparent rounded-lg overflow-hidden shadow-md">
      {/* Cover Banner */}
      <div className="h-32 bg-gray-200 relative overflow-hidden">
        {coverBannerType === 'video' ? (
          <Skeleton className="h-full w-full rounded-t-lg" borderRadius="rounded-t-lg" loading={bannerLoading}>
            <video
              src={coverBanner}
              autoPlay
              muted
              loop
              playsInline
              className="object-cover w-full h-full"
              onLoadedData={() => setBannerLoading(false)}
              onError={() => setBannerLoading(false)}
            />
          </Skeleton>
        ) : (
          <Skeleton className="h-full w-full rounded-t-lg" borderRadius="rounded-t-lg" loading={bannerLoading}>
            <Image
              src={coverBanner || '/images/user/default-banner.webp'}
              alt={username}
              width={1000}
              height={320}
              className="object-cover w-full h-full rounded-t-lg"
              onLoad={() => setBannerLoading(false)}
              onError={() => setBannerLoading(false)}
            />
          </Skeleton>
        )}
      </div>

      {/* Main Content */}
      <div className="px-6 pt-16 pb-6 flex items-start justify-between shadow-none border border-solid border-gray-300 dark:border-white">
        {/* Left section: Avatar and user info */}
        <div className="flex items-center -mt-20">
          {/* Avatar */}
          <div className="relative w-24 h-24 mr-4">
            <Image
              src={profilePhoto || '/images/user/default-avatar.webp'}
              alt={`${username} avatar`}
              layout="fill"
              objectFit="cover"
              className="rounded-full border-4 border-gray-400 dark:border-white"
            />
          </div>
          {/* Username and badge */}
          <div>
            <h2 className="flex items-center gap-1 text-xl font-semibold text-gray-900 dark:text-white">{username} {isVerified && (
              <img className="w-4 h-4" alt="Verified User" src="/images/user/verified.png" />
            )}</h2>
            <div className="mt-1 flex items-center space-x-2">
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded px-2 py-1">
                {niche || 'Not Specified'}
              </span>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{niche || 'Not Specified'}</p>
          </div>
        </div>

        {/* Right section: Action buttons */}
        <div className="flex space-x-2">
          <button className="flex items-center space-x-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
            <span>Follow</span>
          </button>
          <button className="flex items-center space-x-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span>Message</span>
          </button>
        </div>
      </div>
    </div>
  );
};