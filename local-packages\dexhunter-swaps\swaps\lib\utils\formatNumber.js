const i = (r, t = 2) => {
  if (!r)
    return "0";
  typeof r == "string" && (r = parseFloat(r));
  let e;
  return r < 1e-12 ? e = 15 : r < 1e-10 ? e = 13 : r < 1e-6 ? e = 10 : r < 1 ? e = 7 : r >= 1e3 ? e = 2 : e = t, r.toLocaleString(void 0, {
    minimumFractionDigits: 0,
    maximumFractionDigits: e
  });
}, h = (r) => Intl.NumberFormat(
  "en-US",
  {
    style: "currency",
    currency: "USD"
  }
).format(r), d = (r) => {
  if (!r)
    return 0;
  let t = r;
  return t >= 100 ? t = t.toFixed(2) : t < 1 && (t = t.toFixed(6)), t = parseFloat(t).toString(), t || 0;
}, M = (r) => (r || (r = null), !(r != null && r.startsWith("0")) && !(r != null && r.startsWith("0.")) && (r = r == null ? void 0 : r.replace(/^0+/, "")), (r == null ? void 0 : r.length) > 12 ? r == null ? void 0 : r.slice(0, 12) : r), g = (r) => (r || (r = "0"), !(r != null && r.startsWith("0")) && !(r != null && r.startsWith("0.")) && (r = r == null ? void 0 : r.replace(/^0+/, "")), r), f = (r) => isNaN(r) ? "0" : r < 1 ? i(r, 6) : r < 10 ? i(r, 2) : r < 100 ? `${Math.round(r)}` : r < 1e3 ? `${Math.round(r)}` : r < 1e4 ? `${Math.round(r / 100) / 10}K` : r < 1e6 ? `${Math.round(r / 1e3)}K` : r < 1e9 ? `${i(r / 1e5 / 10, 2)}M` : r < 1e12 ? `${Math.round(r / 1e8) / 10}B` : `${Math.round(r / 1e11) / 10}T`, c = (r, t = !1, e = !1) => {
  if (!r)
    return "0";
  const n = typeof r == "string" ? parseInt(r) : Number(r);
  if (t)
    return Math.round(Number(n / 1e6)).toString();
  const o = Math.round((n + Number.EPSILON) / 1e4) / 100;
  return e ? String(o) : f(o);
}, u = (r) => Number(r) * 1e6, N = (r) => Number(r) / 1e6, l = {
  formatNumber: f,
  formatLovelace: c,
  toLovelace: u
}, S = (r) => {
  const t = r.BONUS;
  return i(Math.abs(t));
}, a = (r, t) => {
  var n;
  if (!r)
    return 0;
  const e = (n = r == null ? void 0 : r.splits) == null ? void 0 : n.reduce((o, s) => (s == null ? void 0 : s.price_impact) > o ? s == null ? void 0 : s.price_impact : o, 0);
  return t ? Math.max(
    0.1,
    Math.round(e * 2 + Number.EPSILON) / 2
  ) : e;
}, m = (r, t) => {
  var e = Math.pow(10, t || 0);
  return Math.round(r * e + Number.EPSILON) / e;
}, F = (r) => {
  let t = r;
  for (; t.toString().includes("e-"); )
    t *= 10;
  let e = (t.toString().split(".")[1] || "").length;
  (r.toString().includes("e-") || e >= 3) && (e <= 6 ? e += 1 : e === 7 || e === 8 ? e += 3 : e === 9 ? e += 4 : e === 10 ? e += 5 : e === 11 ? e += 6 : e === 12 ? e += 7 : e >= 13 && (e += 8)), e = Math.min(11, e);
  const n = Math.pow(10, e), o = 1 / Math.pow(10, e);
  return {
    pricescale: n,
    minmov: o
  };
}, L = (r) => {
  if (Math.abs(r) < Number.EPSILON)
    return 0;
  const t = Math.log10(Math.abs(r));
  return t < -12 ? Math.round(r * 1e14) / 1e14 : t < -11 ? Math.round(r * 1e13) / 1e13 : t < -10 ? Math.round(r * 1e12) / 1e12 : t < -9 ? Math.round(r * 1e11) / 1e11 : t < -8 ? Math.round(r * 1e10) / 1e10 : t < -6 ? Math.round(r * 1e9) / 1e9 : t >= -6 && t < -5 ? Math.round(r * 1e8) / 1e8 : t < 0 ? Math.round(r * 1e6) / 1e6 : t < 1 || t < 2 ? Math.round(r * 100) / 100 : t < 3 || t < 3 || t >= 3 && t < 6 ? Math.round(r) : Math.round(r / 1e3) * 1e3;
}, p = (r) => r === -1 ? -1 : r / (2 * 100);
export {
  l as LovelaceFormatter,
  F as calculateScale,
  L as candleRounding,
  p as convertFromPercentage,
  i as formatBalance,
  M as formatInput,
  c as formatLovelace,
  f as formatNumber,
  h as formatUsd,
  g as formatZeros,
  N as fromLoveLace,
  S as getBonusOutput,
  a as getHighestPriceImpact,
  m as round,
  d as roundNumber,
  u as toLovelace
};
