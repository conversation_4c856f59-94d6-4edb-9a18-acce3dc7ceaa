import { jsx as e, jsxs as i } from "react/jsx-runtime";
import { Dialog as l, DialogContent as s } from "../ui/dialog.js";
import c from "../../store/useStore.js";
import "react";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../swap/components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
const H = () => {
  const { isOpenShortcuts: t, toggleOpenShortcuts: d } = c((r) => r.globalSettingsSlice);
  return /* @__PURE__ */ e(l, { open: t, onOpenChange: d, children: /* @__PURE__ */ e(s, { className: "rounded-[26px] !w-[480px] p-8 !top-[400px]", children: /* @__PURE__ */ i("div", { className: "font-proximaSemiBold", children: [
    /* @__PURE__ */ e("span", { className: "font-proximaBold text-base text-gray-103", children: "Shortcuts" }),
    /* @__PURE__ */ i("div", { className: "grid grid-cols-2 gap-4 my-2 py-1", children: [
      /* @__PURE__ */ i("div", { className: "space-y-2", children: [
        " ",
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Open token search" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "/" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Execute the swap" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "Enter" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Select first token (on search)" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "Enter" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Flip sell and buy tokens" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "K" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Open swap settings" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "S" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Open swap details" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "D" }) })
        ] })
      ] }),
      /* @__PURE__ */ i("div", { className: "space-y-2", children: [
        " ",
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Flip token prices" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "P" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Open orders page" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "O" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Open swap page" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "S" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Open shortcut info" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "I" }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Hide small balances" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "." }) })
        ] }),
        /* @__PURE__ */ i("div", { children: [
          /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Select order" }),
          /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "X" }) })
        ] })
      ] }),
      /* @__PURE__ */ i("div", { children: [
        /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Deselect order" }),
        /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "Shift+X" }) })
      ] }),
      /* @__PURE__ */ i("div", { children: [
        /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Select all orders" }),
        /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "A" }) })
      ] }),
      /* @__PURE__ */ i("div", { children: [
        /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Cancel selected orders" }),
        /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "C" }) })
      ] }),
      /* @__PURE__ */ i("div", { children: [
        /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Open global settings" }),
        /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "G" }) })
      ] }),
      /* @__PURE__ */ i("div", { children: [
        /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Increase slippage by 1%" }),
        /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "E" }) })
      ] }),
      /* @__PURE__ */ i("div", { children: [
        /* @__PURE__ */ e("div", { className: "text-gray-400 text-sm", children: "Decrease slippage by 1%" }),
        /* @__PURE__ */ e("div", { className: "text-white", children: /* @__PURE__ */ e("kbd", { children: "Q" }) })
      ] })
    ] })
  ] }) }) });
};
export {
  H as default
};
