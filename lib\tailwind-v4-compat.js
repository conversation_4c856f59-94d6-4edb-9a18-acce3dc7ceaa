// This plugin helps maintain compatibility with Tailwind v3 class names in v4
const plugin = require('tailwindcss/plugin');

module.exports = plugin(function({ addUtilities }) {
  // Add any utility classes that might be missing in v4
  const legacyUtilities = {
    '.bg-white': { backgroundColor: '#ffffff' },
    '.bg-black': { backgroundColor: '#000000' },
    '.text-white': { color: '#ffffff' },
    '.text-black': { color: '#000000' },
    // Add more as needed
  };
  
  addUtilities(legacyUtilities);
});