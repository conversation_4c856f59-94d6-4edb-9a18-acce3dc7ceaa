import { jsxs as C, jsx as t } from "react/jsx-runtime";
import { memo as r } from "react";
const e = (o) => /* @__PURE__ */ C(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 22 18",
    fill: "none",
    ...o,
    children: [
      /* @__PURE__ */ t(
        "path",
        {
          d: "M3.97883 6.68508C1.99294 5.89073 1 5.49355 1 5C1 4.50645 1.99294 4.10927 3.97883 3.31492L6.7873 2.19153C8.77318 1.39718 9.7661 1 11 1C12.2339 1 13.2268 1.39718 15.2127 2.19153L18.0212 3.31492C20.0071 4.10927 21 4.50645 21 5C21 5.49355 20.0071 5.89073 18.0212 6.68508L15.2127 7.8085C13.2268 8.6028 12.2339 9 11 9C9.7661 9 8.77318 8.6028 6.7873 7.8085L3.97883 6.68508Z",
          stroke: "currentColor",
          strokeWidth: 2
        }
      ),
      /* @__PURE__ */ t(
        "path",
        {
          d: "M4.76613 7L3.97883 7.3149C1.99294 8.1093 1 8.5065 1 9C1 9.4935 1.99294 9.8907 3.97883 10.6851L6.7873 11.8085C8.77318 12.6028 9.7661 13 11 13C12.2339 13 13.2268 12.6028 15.2127 11.8085L18.0212 10.6851C20.0071 9.8907 21 9.4935 21 9C21 8.5065 20.0071 8.1093 18.0212 7.3149L17.2339 7",
          stroke: "currentColor",
          strokeWidth: 2
        }
      ),
      /* @__PURE__ */ t(
        "path",
        {
          d: "M4.76613 11L3.97883 11.3149C1.99294 12.1093 1 12.5065 1 13C1 13.4935 1.99294 13.8907 3.97883 14.6851L6.7873 15.8085C8.77318 16.6028 9.7661 17 11 17C12.2339 17 13.2268 16.6028 15.2127 15.8085L18.0212 14.6851C20.0071 13.8907 21 13.4935 21 13C21 12.5065 20.0071 12.1093 18.0212 11.3149L17.2339 11",
          stroke: "currentColor",
          strokeWidth: 2
        }
      )
    ]
  }
), h = r(e);
export {
  h as default
};
