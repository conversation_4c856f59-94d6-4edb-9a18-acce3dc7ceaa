import { jsxs as r, jsx as o } from "react/jsx-runtime";
import { memo as t } from "react";
const l = (e) => /* @__PURE__ */ r(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 16 16",
    fill: "none",
    ...e,
    children: [
      /* @__PURE__ */ o(
        "path",
        {
          fillRule: "evenodd",
          clipRule: "evenodd",
          d: "M1 8C1 4.70017 1 3.05025 2.02513 2.02513C3.05025 1 4.70017 1 8 1C11.2998 1 12.9498 1 13.9748 2.02513C15 3.05025 15 4.70017 15 8C15 11.2998 15 12.9498 13.9748 13.9748C12.9498 15 11.2998 15 8 15C4.70017 15 3.05025 15 2.02513 13.9748C1 12.9498 1 11.2998 1 8Z",
          stroke: "currentColor",
          strokeWidth: 1.5
        }
      ),
      /* @__PURE__ */ o(
        "path",
        {
          d: "M7.93262 11.4668V7.6668",
          stroke: "currentColor",
          strokeWidth: 1.86667,
          strokeLinecap: "round"
        }
      ),
      /* @__PURE__ */ o(
        "circle",
        {
          cx: 7.93288,
          cy: 4.93337,
          r: 0.933333,
          transform: "rotate(-180 7.93288 4.93337)",
          fill: "currentColor"
        }
      )
    ]
  }
), i = t(l);
export {
  i as default
};
