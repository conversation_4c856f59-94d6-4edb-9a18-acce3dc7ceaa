/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import React, { useEffect, useState } from 'react';
import { useRouteChange } from './RouteChangeProvider';
import PageLoader from './PageLoader';
import '@/styles/loader.css';

const Loader = () => {
    const { isRouteChanging } = useRouteChange();
    const [isLoading, setIsLoading] = useState(true);
    const [isFirstLoad, setIsFirstLoad] = useState(true);

    // Handle initial page load
    useEffect(() => {
        const timer = setTimeout(() => {
            setIsLoading(false);
            setIsFirstLoad(false);
        }, 1800);

        return () => clearTimeout(timer);
    }, []);

    // Handle route changes
    useEffect(() => {
        if (!isFirstLoad) {  // Don't respond to route changes during initial load
            setIsLoading(isRouteChanging);
        }
    }, [isRouteChanging, isFirstLoad]);

    if (!isLoading) return null;

    return (
        <PageLoader />
    );
};
export default Loader;