export const ethnicityOptions = [
  { value: 'asian', label: 'Asian' },
  { value: 'black', label: 'Black' },
  { value: 'hispanic', label: 'Hispanic' },
  { value: 'white', label: 'White' },
  { value: 'mixed', label: 'Mixed' },
  { value: 'other', label: 'Other' },
];

export const interestedInOptions = [
  { value: 'men', label: 'Men' },
  { value: 'women', label: 'Women' },
  { value: 'non-binary', label: 'Non-Binary' },
  { value: 'all', label: 'All' },
];

export const eyeColorOptions = [
  { value: 'brown', label: 'Brown' },
  { value: 'blue', label: 'Blue' },
  { value: 'green', label: 'Green' },
  { value: 'hazel', label: 'Hazel' },
  { value: 'grey', label: 'Grey' },
  { value: 'mixed', label: 'Mixed' },
];

export const hairColorOptions = [
  { value: 'black', label: 'Black' },
  { value: 'brown', label: 'Brown' },
  { value: 'blonde', label: '<PERSON><PERSON><PERSON>' },
  { value: 'red', label: 'Red' },
  { value: 'other', label: 'Other' },
];

export const bodyTypeOptions = [
  { value: 'athletic', label: 'Athletic' },
  { value: 'average', label: 'Average' },
  { value: 'slim', label: 'Slim' },
  { value: 'curvy', label: 'Curvy' },
  { value: 'muscular', label: 'Muscular' },
  { value: 'plus-size', label: 'Plus-Size' },
];

export const languageOptions = [
  { value: 'aa', label: 'Afaraf', flag: 'dj' },
  { value: 'ab', label: 'Аҧсуа', flag: 'ge' },
  { value: 'af', label: 'Afrikaans', flag: 'za' },
  { value: 'ak', label: 'Akan', flag: 'gh' },
  { value: 'am', label: 'አማርኛ', flag: 'et' },
  { value: 'ar', label: 'العربية', flag: 'sa' },
  { value: 'as', label: 'অসমীয়া', flag: 'in' },
  { value: 'av', label: 'Авар', flag: 'ru' },
  { value: 'ay', label: 'Aymar', flag: 'bo' },
  { value: 'az', label: 'Azərbaycan', flag: 'az' },
  { value: 'ba', label: 'Башҡорт', flag: 'ru' },
  { value: 'be', label: 'Беларуская', flag: 'by' },
  { value: 'bg', label: 'Български', flag: 'bg' },
  { value: 'bh', label: 'Bihari', flag: 'in' },
  { value: 'bi', label: 'Bislama', flag: 'vu' },
  { value: 'bm', label: 'Bambara', flag: 'ml' },
  { value: 'bn', label: 'বাংলা', flag: 'bd' },
  { value: 'bo', label: 'བོད་པའི', flag: 'cn' },
  { value: 'br', label: 'Brezhoneg', flag: 'fr' },
  { value: 'bs', label: 'Bosanski', flag: 'ba' },
  { value: 'ca', label: 'Català', flag: 'es' },
  { value: 'ce', label: 'Нохчийн', flag: 'ru' },
  { value: 'ch', label: 'Chamoru', flag: 'gu' },
  { value: 'co', label: 'Corsu', flag: 'fr' },
  { value: 'cr', label: 'Nēhiyawēwin', flag: 'ca' },
  { value: 'cs', label: 'Čeština', flag: 'cz' },
  { value: 'cu', label: 'Словѣньскъ', flag: 'ru' },
  { value: 'cv', label: 'Чӑвашла', flag: 'ru' },
  { value: 'cy', label: 'Cymraeg', flag: 'gb' },
  { value: 'da', label: 'Dansk', flag: 'dk' },
  { value: 'de', label: 'Deutsch', flag: 'de' },
  { value: 'dv', label: 'ދިވެހި', flag: 'mv' },
  { value: 'dz', label: 'རྫོང་ཁ', flag: 'bt' },
  { value: 'ee', label: 'Eʋegbe', flag: 'gh' },
  { value: 'el', label: 'Ελληνικά', flag: 'gr' },
  { value: 'en', label: 'English', flag: 'gb' },
  { value: 'eo', label: 'Esperanto', flag: 'un' },
  { value: 'es', label: 'Español', flag: 'es' },
  { value: 'et', label: 'Eesti', flag: 'ee' },
  { value: 'eu', label: 'Euskara', flag: 'es' },
  { value: 'fa', label: 'فارسی', flag: 'ir' },
  { value: 'ff', label: 'Fulfulde', flag: 'sn' },
  { value: 'fi', label: 'Suomi', flag: 'fi' },
  { value: 'fj', label: 'Na Vosa Vakaviti', flag: 'fj' },
  { value: 'fo', label: 'Føroyskt', flag: 'fo' },
  { value: 'fr', label: 'Français', flag: 'fr' },
  { value: 'fy', label: 'Frysk', flag: 'nl' },
  { value: 'ga', label: 'Gaelige', flag: 'ie' },
  { value: 'gd', label: 'Gàidhlig', flag: 'gb' },
  { value: 'gl', label: 'Galego', flag: 'es' },
  { value: 'gn', label: 'Avañeʼẽ', flag: 'py' },
  { value: 'gu', label: 'ગુજરાતી', flag: 'in' },
  { value: 'gv', label: 'Gaelg', flag: 'im' },
  { value: 'ha', label: 'Hausa', flag: 'ng' },
  { value: 'haw', label: 'ʻŌlelo Hawaiʻi', flag: 'us' },
  { value: 'he', label: 'עברית', flag: 'il' },
  { value: 'hi', label: 'हिन्दी', flag: 'in' },
  { value: 'ho', label: 'Hiri Motu', flag: 'pg' },
  { value: 'hr', label: 'Hrvatski', flag: 'hr' },
  { value: 'ht', label: 'Kreyòl Ayisyen', flag: 'ht' },
  { value: 'hu', label: 'Magyar', flag: 'hu' },
  { value: 'hy', label: 'Հայերեն', flag: 'am' },
  { value: 'hz', label: 'Otjiherero', flag: 'na' },
  { value: 'ia', label: 'Interlingua', flag: 'un' },
  { value: 'id', label: 'Bahasa Indonesia', flag: 'id' },
  { value: 'ie', label: 'Interlingue', flag: 'un' },
  { value: 'ig', label: 'Igbo', flag: 'ng' },
  { value: 'ii', label: 'ꆈꌠ꒿', flag: 'cn' },
  { value: 'ik', label: 'Iñupiaq', flag: 'us' },
  { value: 'io', label: 'Ido', flag: 'un' },
  { value: 'is', label: 'Íslenska', flag: 'is' },
  { value: 'it', label: 'Italiano', flag: 'it' },
  { value: 'iu', label: 'ᐃᓄᒃᑎᑐᑦ', flag: 'ca' },
  { value: 'ja', label: '日本語', flag: 'jp' },
  { value: 'jv', label: 'Basa Jawa', flag: 'id' },
  { value: 'ka', label: 'ქართული', flag: 'ge' },
  { value: 'kg', label: 'Kikongo', flag: 'cd' },
  { value: 'ki', label: 'Gikuyu', flag: 'ke' },
  { value: 'kj', label: 'Kwanyama', flag: 'na' },
  { value: 'kk', label: 'Қазақша', flag: 'kz' },
  { value: 'kl', label: 'Kalaallisut', flag: 'gl' },
  { value: 'km', label: 'ភាសាខ្មែរ', flag: 'kh' },
  { value: 'kn', label: 'ಕನ್ನಡ', flag: 'in' },
  { value: 'ko', label: '한국어', flag: 'kr' },
  { value: 'kr', label: 'Kanuri', flag: 'ng' },
  { value: 'ks', label: 'कश्मीरी', flag: 'in' },
  { value: 'ku', label: 'Kurdî', flag: 'tr' },
  { value: 'kv', label: 'Коми', flag: 'ru' },
  { value: 'kw', label: 'Kernowek', flag: 'gb' },
  { value: 'ky', label: 'Кыргызча', flag: 'kg' },
  { value: 'la', label: 'Latina', flag: 'va' },
  { value: 'lb', label: 'Lëtzebuergesch', flag: 'lu' },
  { value: 'lg', label: 'Luganda', flag: 'ug' },
  { value: 'li', label: 'Limburgs', flag: 'nl' },
  { value: 'ln', label: 'Lingála', flag: 'cd' },
  { value: 'lo', label: 'ພາສາລາວ', flag: 'la' },
  { value: 'lt', label: 'Lietuvių', flag: 'lt' },
  { value: 'lu', label: 'Kiluba', flag: 'cd' },
  { value: 'lv', label: 'Latviešu', flag: 'lv' },
  { value: 'mg', label: 'Malagasy', flag: 'mg' },
  { value: 'mh', label: 'Kajin M̧ajeļ', flag: 'mh' },
  { value: 'mi', label: 'Māori', flag: 'nz' },
  { value: 'mk', label: 'Македонски', flag: 'mk' },
  { value: 'ml', label: 'മലയാളം', flag: 'in' },
  { value: 'mn', label: 'Монгол', flag: 'mn' },
  { value: 'mr', label: 'मराठी', flag: 'in' },
  { value: 'ms', label: 'Bahasa Melayu', flag: 'my' },
  { value: 'mt', label: 'Malti', flag: 'mt' },
  { value: 'my', label: 'မြန်မာဘာသာ', flag: 'mm' },
  { value: 'na', label: 'Nauru', flag: 'nr' },
  { value: 'nb', label: 'Norsk Bokmål', flag: 'no' },
  { value: 'nd', label: 'isiNdebele', flag: 'zw' },
  { value: 'ne', label: 'नेपाली', flag: 'np' },
  { value: 'ng', label: 'Ndonga', flag: 'na' },
  { value: 'nl', label: 'Nederlands', flag: 'nl' },
  { value: 'nn', label: 'Norsk Nynorsk', flag: 'no' },
  { value: 'no', label: 'Norsk', flag: 'no' },
  { value: 'nr', label: 'isiNdebele', flag: 'za' },
  { value: 'nv', label: 'Diné bizaad', flag: 'us' },
  { value: 'ny', label: 'Chichewa', flag: 'mw' },
  { value: 'oc', label: 'Occitan', flag: 'fr' },
  { value: 'oj', label: 'ᐊᓂᔑᓈᐯᒧᐎᓐ', flag: 'ca' },
  { value: 'om', label: 'Afaan Oromoo', flag: 'et' },
  { value: 'or', label: 'ଓଡ଼ିଆ', flag: 'in' },
  { value: 'os', label: 'Ирон', flag: 'ru' },
  { value: 'pa', label: 'ਪੰਜਾਬੀ', flag: 'in' },
  { value: 'pi', label: 'Pāli', flag: 'in' },
  { value: 'pl', label: 'Polski', flag: 'pl' },
  { value: 'ps', label: 'پښتو', flag: 'af' },
  { value: 'pt', label: 'Português', flag: 'pt' },
  { value: 'qu', label: 'Runa Simi', flag: 'pe' },
  { value: 'rm', label: 'Rumantsch', flag: 'ch' },
  { value: 'rn', label: 'Kirundi', flag: 'bi' },
  { value: 'ro', label: 'Română', flag: 'ro' },
  { value: 'ru', label: 'Русский', flag: 'ru' },
  { value: 'rw', label: 'Kinyarwanda', flag: 'rw' },
  { value: 'sa', label: 'संस्कृतम्', flag: 'in' },
  { value: 'sc', label: 'Sardu', flag: 'it' },
  { value: 'sd', label: 'سنڌي', flag: 'pk' },
  { value: 'se', label: 'Sámegiella', flag: 'no' },
  { value: 'sg', label: 'Sängö', flag: 'cf' },
  { value: 'si', label: 'සිංහල', flag: 'lk' },
  { value: 'sk', label: 'Slovenčina', flag: 'sk' },
  { value: 'sl', label: 'Slovenščina', flag: 'si' },
  { value: 'sm', label: 'Gagana Samoa', flag: 'ws' },
  { value: 'sn', label: 'chiShona', flag: 'zw' },
  { value: 'so', label: 'Soomaali', flag: 'so' },
  { value: 'sq', label: 'Shqip', flag: 'al' },
  { value: 'sr', label: 'Српски', flag: 'rs' },
  { value: 'ss', label: 'siSwati', flag: 'sz' },
  { value: 'st', label: 'Sesotho', flag: 'ls' },
  { value: 'su', label: 'Basa Sunda', flag: 'id' },
  { value: 'sv', label: 'Svenska', flag: 'se' },
  { value: 'sw', label: 'Kiswahili', flag: 'ke' },
  { value: 'ta', label: 'தமிழ்', flag: 'in' },
  { value: 'te', label: 'తెలుగు', flag: 'in' },
  { value: 'tg', label: 'Тоҷикӣ', flag: 'tj' },
  { value: 'th', label: 'ไทย', flag: 'th' },
  { value: 'ti', label: 'ትግርኛ', flag: 'er' },
  { value: 'tk', label: 'Türkmen', flag: 'tm' },
  { value: 'tl', label: 'Tagalog', flag: 'ph' },
  { value: 'tn', label: 'Setswana', flag: 'bw' },
  { value: 'to', label: 'Lea Faka-Tonga', flag: 'to' },
  { value: 'tr', label: 'Türkçe', flag: 'tr' },
  { value: 'ts', label: 'Xitsonga', flag: 'za' },
  { value: 'tt', label: 'Татарча', flag: 'ru' },
  { value: 'tw', label: 'Twi', flag: 'gh' },
  { value: 'ty', label: 'Reo Tahiti', flag: 'pf' },
  { value: 'ug', label: 'ئۇيغۇرچە', flag: 'cn' },
  { value: 'uk', label: 'Українська', flag: 'ua' },
  { value: 'ur', label: 'اردو', flag: 'pk' },
  { value: 'uz', label: 'Oʻzbek', flag: 'uz' },
  { value: 've', label: 'Tshivenḓa', flag: 'za' },
  { value: 'vi', label: 'Tiếng Việt', flag: 'vn' },
  { value: 'vo', label: 'Volapük', flag: 'un' },
  { value: 'wa', label: 'Walon', flag: 'be' },
  { value: 'wo', label: 'Wolof', flag: 'sn' },
  { value: 'xh', label: 'isiXhosa', flag: 'za' },
  { value: 'yi', label: 'ייִדיש', flag: 'il' },
  { value: 'yo', label: 'Yorùbá', flag: 'ng' },
  { value: 'za', label: 'Zhuang', flag: 'cn' },
  { value: 'zh-CN', label: '中文 (简体)', flag: 'cn' },
  { value: 'zh-TW', label: '中文 (繁體)', flag: 'tw' },
  { value: 'zu', label: 'isiZulu', flag: 'za' }
];

export const astrologyOptions = [
  { value: 'aries', label: 'Aries' },
  { value: 'taurus', label: 'Taurus' },
  { value: 'gemini', label: 'Gemini' },
  { value: 'cancer', label: 'Cancer' },
  { value: 'leo', label: 'Leo' },
  { value: 'virgo', label: 'Virgo' },
  { value: 'libra', label: 'Libra' },
  { value: 'scorpio', label: 'Scorpio' },
  { value: 'sagittarius', label: 'Sagittarius' },
  { value: 'capricorn', label: 'Capricorn' },
  { value: 'aquarius', label: 'Aquarius' },
  { value: 'pisces', label: 'Pisces' },
];

export const heightOptions = [
  { value: "< 5'", label: "< 5' (< 152 cm)" },
  { value: "5'0\"", label: "5'0\" (152 cm)" },
  { value: "5'1\"", label: "5'1\" (155 cm)" },
  { value: "5'2\"", label: "5'2\" (157 cm)" },
  { value: "5'3\"", label: "5'3\" (160 cm)" },
  { value: "5'4\"", label: "5'4\" (163 cm)" },
  { value: "5'5\"", label: "5'5\" (165 cm)" },
  { value: "5'6\"", label: "5'6\" (168 cm)" },
  { value: "5'7\"", label: "5'7\" (170 cm)" },
  { value: "5'8\"", label: "5'8\" (173 cm)" },
  { value: "5'9\"", label: "5'9\" (175 cm)" },
  { value: "5'10\"", label: "5'10\" (178 cm)" },
  { value: "5'11\"", label: "5'11\" (180 cm)" },
  { value: "6'0\"", label: "6'0\" (183 cm)" },
  { value: "6'1\"", label: "6'1\" (185 cm)" },
  { value: "6'2\"", label: "6'2\" (188 cm)" },
  { value: "6'3\"", label: "6'3\" (191 cm)" },
  { value: "6'4\"", label: "6'4\" (193 cm)" },
  { value: "6'5\"", label: "6'5\" (196 cm)" },
  { value: "6'6\"", label: "6'6\" (198 cm)" },
  { value: "6'7\"", label: "6'7\" (201 cm)" },
  { value: "6'8\"", label: "6'8\" (203 cm)" },
  { value: "6'9\"", label: "6'9\" (206 cm)" },
  { value: "6'10\"", label: "6'10\" (208 cm)" },
  { value: "6'11\"", label: "6'11\" (211 cm)" },
  { value: "> 7'0\"", label: "> 7'0\" (> 213 cm)" },
];

export const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'non-binary', label: 'Non-Binary' },
  { value: 'other', label: 'Other' },
];

export const relationshipStatusOptions = [
  { value: 'single', label: 'Single' },
  { value: 'in a relationship', label: 'In a Relationship' },
  { value: 'married', label: 'Married' },
  { value: 'divorced', label: 'Divorced' },
  { value: 'widowed', label: 'Widowed' },
  { value: 'complicated', label: "It's Complicated" },
];

export const breastSizeOptions = [
  { value: 'extra big', label: 'Extra Big' },
  { value: 'big', label: 'Big' },
  { value: 'medium', label: 'Medium' },
  { value: 'small', label: 'Small' },
  { value: 'tiny', label: 'Tiny' },
];

export const breastTypeOptions = [
  { value: 'fake', label: 'Fake' },
  { value: 'natural', label: 'Natural' },
];

export const interestOptions = [
  { value: 'music', label: 'Music' },
  { value: 'travel', label: 'Travel' },
  { value: 'sports', label: 'Sports' },
];

export const piercingOptions = [
  { value: 'ears', label: 'Ears' },
  { value: 'nose', label: 'Nose' },
  { value: 'lip', label: 'Lip' },
  { value: 'tongue', label: 'Tongue' },
  { value: 'navel', label: 'Navel' },
];

export const tattooOptions = [
  { value: 'face', label: 'Face' },
  { value: 'neck', label: 'Neck' },
  { value: 'chest', label: 'Chest' },
  { value: 'genital', label: 'Genital' },
  { value: 'stomach', label: 'Stomach' },
  { value: 'buttocks', label: 'Buttocks' },
  { value: 'leftFoot', label: 'Left Foot' },
  { value: 'leftHand', label: 'Left Hand' },
  { value: 'lowerBack', label: 'Lower Back' },
  { value: 'rightFoot', label: 'Right Foot' },
  { value: 'rightHand', label: 'Right Hand' },
  { value: 'upperBack', label: 'Upper Back' },
  { value: 'lowerLeftLeg', label: 'Lower Left Leg' },
  { value: 'upperLeftLeg', label: 'Upper Left Leg' },
  { value: 'lowerRightLeg', label: 'Lower Right Leg' },
  { value: 'upperRightLeg', label: 'Upper Right Leg' },
];

export const nicheOptions = [
  { value: 'art', label: 'Art' },
  { value: 'music', label: 'Music' },
  { value: 'photography', label: 'Photography' },
  { value: 'writing', label: 'Writing' },
  { value: 'cooking', label: 'Cooking' },
  { value: 'travel', label: 'Travel' },
  { value: 'sports', label: 'Sports' },
  { value: 'gaming', label: 'Gaming' },
  { value: 'fitness', label: 'Fitness' },
  { value: 'fashion', label: 'Fashion' },
  { value: 'tech', label: 'Tech' },
  { value: 'business', label: 'Business' },
  { value: 'politics', label: 'Politics' },
  { value: 'science', label: 'Science' },
  { value: 'history', label: 'History' },
  { value: 'literature', label: 'Literature' },
];

// Waist size options (in inches)
export const waistSizeOptions = [
  { value: '<24', label: '< 24" (< 61 cm)' },
  { value: '24', label: '24" (61 cm)' },
  { value: '25', label: '25" (64 cm)' },
  { value: '26', label: '26" (66 cm)' },
  { value: '27', label: '27" (69 cm)' },
  { value: '28', label: '28" (71 cm)' },
  { value: '29', label: '29" (74 cm)' },
  { value: '30', label: '30" (76 cm)' },
  { value: '31', label: '31" (79 cm)' },
  { value: '32', label: '32" (81 cm)' },
  { value: '33', label: '33" (84 cm)' },
  { value: '34', label: '34" (86 cm)' },
  { value: '35', label: '35" (89 cm)' },
  { value: '36', label: '36" (91 cm)' },
  { value: '37', label: '37" (94 cm)' },
  { value: '38', label: '38" (97 cm)' },
  { value: '39', label: '39" (99 cm)' },
  { value: '40', label: '40" (102 cm)' },
  { value: '41', label: '41" (104 cm)' },
  { value: '42', label: '42" (107 cm)' },
  { value: '43', label: '43" (109 cm)' },
  { value: '44', label: '44" (112 cm)' },
  { value: '45', label: '45" (114 cm)' },
  { value: '46', label: '46" (117 cm)' },
  { value: '47', label: '47" (119 cm)' },
  { value: '48', label: '48" (122 cm)' },
  { value: '49', label: '49" (124 cm)' },
  { value: '50', label: '50" (127 cm)' },
  { value: '>50', label: '> 50" (> 127 cm)' },
];

// Hips size options (in inches)
export const hipsSizeOptions = [
  { value: '<30', label: '< 30" (< 76 cm)' },
  { value: '30', label: '30" (76 cm)' },
  { value: '31', label: '31" (79 cm)' },
  { value: '32', label: '32" (81 cm)' },
  { value: '33', label: '33" (84 cm)' },
  { value: '34', label: '34" (86 cm)' },
  { value: '35', label: '35" (89 cm)' },
  { value: '36', label: '36" (91 cm)' },
  { value: '37', label: '37" (94 cm)' },
  { value: '38', label: '38" (97 cm)' },
  { value: '39', label: '39" (99 cm)' },
  { value: '40', label: '40" (102 cm)' },
  { value: '41', label: '41" (104 cm)' },
  { value: '42', label: '42" (107 cm)' },
  { value: '43', label: '43" (109 cm)' },
  { value: '44', label: '44" (112 cm)' },
  { value: '45', label: '45" (114 cm)' },
  { value: '46', label: '46" (117 cm)' },
  { value: '47', label: '47" (119 cm)' },
  { value: '48', label: '48" (122 cm)' },
  { value: '49', label: '49" (124 cm)' },
  { value: '50', label: '50" (127 cm)' },
  { value: '51', label: '51" (130 cm)' },
  { value: '52', label: '52" (132 cm)' },
  { value: '53', label: '53" (135 cm)' },
  { value: '54', label: '54" (137 cm)' },
  { value: '55', label: '55" (140 cm)' },
  { value: '56', label: '56" (142 cm)' },
  { value: '>56', label: '> 56" (> 142 cm)' },
];

// Butt size options (descriptive)
export const buttSizeOptions = [
  { value: 'tiny', label: 'Tiny' },
  { value: 'small', label: 'Small' },
  { value: 'medium', label: 'Medium' },
  { value: 'large', label: 'Large' },
  { value: 'extra-large', label: 'Extra Large' },
];

// Shoe size options (US sizes with EU equivalents)
export const shoeSizeOptions = [
  // Women's sizes
  { value: 'w4', label: "Women's 4 (EU 35)" },
  { value: 'w4.5', label: "Women's 4.5 (EU 35.5)" },
  { value: 'w5', label: "Women's 5 (EU 36)" },
  { value: 'w5.5', label: "Women's 5.5 (EU 36.5)" },
  { value: 'w6', label: "Women's 6 (EU 37)" },
  { value: 'w6.5', label: "Women's 6.5 (EU 37.5)" },
  { value: 'w7', label: "Women's 7 (EU 38)" },
  { value: 'w7.5', label: "Women's 7.5 (EU 38.5)" },
  { value: 'w8', label: "Women's 8 (EU 39)" },
  { value: 'w8.5', label: "Women's 8.5 (EU 39.5)" },
  { value: 'w9', label: "Women's 9 (EU 40)" },
  { value: 'w9.5', label: "Women's 9.5 (EU 40.5)" },
  { value: 'w10', label: "Women's 10 (EU 41)" },
  { value: 'w10.5', label: "Women's 10.5 (EU 41.5)" },
  { value: 'w11', label: "Women's 11 (EU 42)" },
  { value: 'w11.5', label: "Women's 11.5 (EU 42.5)" },
  { value: 'w12', label: "Women's 12 (EU 43)" },

  // Men's sizes
  { value: 'm6', label: "Men's 6 (EU 39)" },
  { value: 'm6.5', label: "Men's 6.5 (EU 39.5)" },
  { value: 'm7', label: "Men's 7 (EU 40)" },
  { value: 'm7.5', label: "Men's 7.5 (EU 40.5)" },
  { value: 'm8', label: "Men's 8 (EU 41)" },
  { value: 'm8.5', label: "Men's 8.5 (EU 41.5)" },
  { value: 'm9', label: "Men's 9 (EU 42)" },
  { value: 'm9.5', label: "Men's 9.5 (EU 42.5)" },
  { value: 'm10', label: "Men's 10 (EU 43)" },
  { value: 'm10.5', label: "Men's 10.5 (EU 43.5)" },
  { value: 'm11', label: "Men's 11 (EU 44)" },
  { value: 'm11.5', label: "Men's 11.5 (EU 44.5)" },
  { value: 'm12', label: "Men's 12 (EU 45)" },
  { value: 'm12.5', label: "Men's 12.5 (EU 45.5)" },
  { value: 'm13', label: "Men's 13 (EU 46)" },
  { value: 'm13.5', label: "Men's 13.5 (EU 46.5)" },
  { value: 'm14', label: "Men's 14 (EU 47)" },
  { value: 'm14.5', label: "Men's 14.5 (EU 47.5)" },
  { value: 'm15', label: "Men's 15 (EU 48)" },
];

export const countryOptions = [
    { value: 'all', label: 'All' },
    { value: 'af', label: 'Afghanistan' },
    { value: 'al', label: 'Albania' },
    { value: 'dz', label: 'Algeria' },
    { value: 'as', label: 'American Samoa' },
    { value: 'ad', label: 'Andorra' },
    { value: 'ao', label: 'Angola' },
    { value: 'ag', label: 'Antigua and Barbuda' },
    { value: 'ar', label: 'Argentina' },
    { value: 'am', label: 'Armenia' },
    { value: 'aw', label: 'Aruba' },
    { value: 'au', label: 'Australia' },
    { value: 'at', label: 'Austria' },
    { value: 'az', label: 'Azerbaijan' },
    { value: 'bs', label: 'The Bahamas' },
    { value: 'bh', label: 'Bahrain' },
    { value: 'bd', label: 'Bangladesh' },
    { value: 'bb', label: 'Barbados' },
    { value: 'by', label: 'Belarus' },
    { value: 'be', label: 'Belgium' },
    { value: 'bz', label: 'Belize' },
    { value: 'bj', label: 'Benin' },
    { value: 'bm', label: 'Bermuda' },
    { value: 'bt', label: 'Bhutan' },
    { value: 'bo', label: 'Bolivia' },
    { value: 'ba', label: 'Bosnia and Herzegovina' },
    { value: 'bw', label: 'Botswana' },
    { value: 'br', label: 'Brazil' },
    { value: 'bn', label: 'Brunei' },
    { value: 'bg', label: 'Bulgaria' },
    { value: 'bf', label: 'Burkina Faso' },
    { value: 'bi', label: 'Burundi' },
    { value: 'cv', label: 'Cabo Verde (Cape Verde)' },
    { value: 'kh', label: 'Cambodia' },
    { value: 'cm', label: 'Cameroon' },
    { value: 'ca', label: 'Canada' },
    { value: 'ky', label: 'Cayman Islands' },
    { value: 'cf', label: 'Central African Republic' },
    { value: 'td', label: 'Chad' },
    { value: 'cl', label: 'Chile' },
    { value: 'cn', label: 'China' },
    { value: 'co', label: 'Colombia' },
    { value: 'km', label: 'Comoros' },
    { value: 'cd', label: 'Democratic Republic of the Congo' },
    { value: 'cg', label: 'Republic of the Congo' },
    { value: 'cr', label: 'Costa Rica' },
    { value: 'ci', label: 'Côte d’Ivoire' },
    { value: 'hr', label: 'Croatia' },
    { value: 'cu', label: 'Cuba' },
    { value: 'cw', label: 'Curaçao' },
    { value: 'cy', label: 'Cyprus' },
    { value: 'cz', label: 'Czech Republic' },
    { value: 'dk', label: 'Denmark' },
    { value: 'dj', label: 'Djibouti' },
    { value: 'dm', label: 'Dominica' },
    { value: 'do', label: 'Dominican Republic' },
    { value: 'tl', label: 'East Timor (Timor-Leste)' },
    { value: 'ec', label: 'Ecuador' },
    { value: 'eg', label: 'Egypt' },
    { value: 'sv', label: 'El Salvador' },
    { value: 'gq', label: 'Equatorial Guinea' },
    { value: 'er', label: 'Eritrea' },
    { value: 'ee', label: 'Estonia' },
    { value: 'sz', label: 'Eswatini (Swaziland)' },
    { value: 'et', label: 'Ethiopia' },
    { value: 'fo', label: 'Faroe Islands' },
    { value: 'fj', label: 'Fiji' },
    { value: 'fi', label: 'Finland' },
    { value: 'fr', label: 'France' },
    { value: 'gf', label: 'French Guiana' },
    { value: 'pf', label: 'French Polynesia' },
    { value: 'ga', label: 'Gabon' },
    { value: 'gm', label: 'The Gambia' },
    { value: 'ps', label: 'Gaza Strip' },
    { value: 'ge', label: 'Georgia' },
    { value: 'de', label: 'Germany' },
    { value: 'gh', label: 'Ghana' },
    { value: 'gr', label: 'Greece' },
    { value: 'gl', label: 'Greenland' },
    { value: 'gd', label: 'Grenada' },
    { value: 'gp', label: 'Guadeloupe' },
    { value: 'gu', label: 'Guam' },
    { value: 'gt', label: 'Guatemala' },
    { value: 'gg', label: 'Guernsey' },
    { value: 'gn', label: 'Guinea' },
    { value: 'gw', label: 'Guinea-Bissau' },
    { value: 'gy', label: 'Guyana' },
    { value: 'ht', label: 'Haiti' },
    { value: 'hn', label: 'Honduras' },
    { value: 'hk', label: 'Hong Kong' },
    { value: 'hu', label: 'Hungary' },
    { value: 'is', label: 'Iceland' },
    { value: 'in', label: 'India' },
    { value: 'id', label: 'Indonesia' },
    { value: 'ir', label: 'Iran' },
    { value: 'iq', label: 'Iraq' },
    { value: 'ie', label: 'Ireland' },
    { value: 'im', label: 'Isle of Man' },
    { value: 'il', label: 'Israel' },
    { value: 'it', label: 'Italy' },
    { value: 'jm', label: 'Jamaica' },
    { value: 'jp', label: 'Japan' },
    { value: 'je', label: 'Jersey' },
    { value: 'jo', label: 'Jordan' },
    { value: 'kz', label: 'Kazakhstan' },
    { value: 'ke', label: 'Kenya' },
    { value: 'ki', label: 'Kiribati' },
    { value: 'kp', label: 'North Korea' },
    { value: 'kr', label: 'South Korea' },
    { value: 'xk', label: 'Kosovo' },
    { value: 'kw', label: 'Kuwait' },
    { value: 'kg', label: 'Kyrgyzstan' },
    { value: 'la', label: 'Laos' },
    { value: 'lv', label: 'Latvia' },
    { value: 'lb', label: 'Lebanon' },
    { value: 'ls', label: 'Lesotho' },
    { value: 'lr', label: 'Liberia' },
    { value: 'ly', label: 'Libya' },
    { value: 'li', label: 'Liechtenstein' },
    { value: 'lt', label: 'Lithuania' },
    { value: 'lu', label: 'Luxembourg' },
    { value: 'mo', label: 'Macau' },
    { value: 'mg', label: 'Madagascar' },
    { value: 'mw', label: 'Malawi' },
    { value: 'my', label: 'Malaysia' },
    { value: 'mv', label: 'Maldives' },
    { value: 'ml', label: 'Mali' },
    { value: 'mt', label: 'Malta' },
    { value: 'mh', label: 'Marshall Islands' },
    { value: 'mq', label: 'Martinique' },
    { value: 'mr', label: 'Mauritania' },
    { value: 'mu', label: 'Mauritius' },
    { value: 'yt', label: 'Mayotte' },
    { value: 'mx', label: 'Mexico' },
    { value: 'fm', label: 'Micronesia' },
    { value: 'md', label: 'Moldova' },
    { value: 'mc', label: 'Monaco' },
    { value: 'mn', label: 'Mongolia' },
    { value: 'me', label: 'Montenegro' },
    { value: 'ma', label: 'Morocco' },
    { value: 'mz', label: 'Mozambique' },
    { value: 'mm', label: 'Myanmar (Burma)' },
    { value: 'na', label: 'Namibia' },
    { value: 'nr', label: 'Nauru' },
    { value: 'np', label: 'Nepal' },
    { value: 'nl', label: 'Netherlands' },
    { value: 'nc', label: 'New Caledonia' },
    { value: 'nz', label: 'New Zealand' },
    { value: 'ni', label: 'Nicaragua' },
    { value: 'ne', label: 'Niger' },
    { value: 'ng', label: 'Nigeria' },
    { value: 'mk', label: 'North Macedonia' },
    { value: 'mp', label: 'Northern Mariana Islands' },
    { value: 'no', label: 'Norway' },
    { value: 'om', label: 'Oman' },
    { value: 'pk', label: 'Pakistan' },
    { value: 'pw', label: 'Palau' },
    { value: 'pa', label: 'Panama' },
    { value: 'pg', label: 'Papua New Guinea' },
    { value: 'py', label: 'Paraguay' },
    { value: 'pe', label: 'Peru' },
    { value: 'ph', label: 'Philippines' },
    { value: 'pl', label: 'Poland' },
    { value: 'pt', label: 'Portugal' },
    { value: 'pr', label: 'Puerto Rico' },
    { value: 'qa', label: 'Qatar' },
    { value: 're', label: 'Réunion' },
    { value: 'ro', label: 'Romania' },
    { value: 'ru', label: 'Russia' },
    { value: 'rw', label: 'Rwanda' },
    { value: 'kn', label: 'Saint Kitts and Nevis' },
    { value: 'lc', label: 'Saint Lucia' },
    { value: 'vc', label: 'Saint Vincent and the Grenadines' },
    { value: 'ws', label: 'Samoa' },
    { value: 'sm', label: 'San Marino' },
    { value: 'st', label: 'Sao Tome and Principe' },
    { value: 'sa', label: 'Saudi Arabia' },
    { value: 'sn', label: 'Senegal' },
    { value: 'rs', label: 'Serbia' },
    { value: 'sc', label: 'Seychelles' },
    { value: 'sl', label: 'Sierra Leone' },
    { value: 'sg', label: 'Singapore' },
    { value: 'sx', label: 'Sint Maarten' },
    { value: 'sk', label: 'Slovakia' },
    { value: 'si', label: 'Slovenia' },
    { value: 'sb', label: 'Solomon Islands' },
    { value: 'so', label: 'Somalia' },
    { value: 'za', label: 'South Africa' },
    { value: 'es', label: 'Spain' },
    { value: 'lk', label: 'Sri Lanka' },
    { value: 'sd', label: 'Sudan' },
    { value: 'ss', label: 'South Sudan' },
    { value: 'sr', label: 'Suriname' },
    { value: 'se', label: 'Sweden' },
    { value: 'ch', label: 'Switzerland' },
    { value: 'sy', label: 'Syria' },
    { value: 'tw', label: 'Taiwan' },
    { value: 'tj', label: 'Tajikistan' },
    { value: 'tz', label: 'Tanzania' },
    { value: 'th', label: 'Thailand' },
    { value: 'tg', label: 'Togo' },
    { value: 'to', label: 'Tonga' },
    { value: 'tt', label: 'Trinidad and Tobago' },
    { value: 'tn', label: 'Tunisia' },
    { value: 'tr', label: 'Turkey' },
    { value: 'tm', label: 'Turkmenistan' },
    { value: 'tv', label: 'Tuvalu' },
    { value: 'ug', label: 'Uganda' },
    { value: 'ua', label: 'Ukraine' },
    { value: 'ae', label: 'United Arab Emirates' },
    { value: 'gb', label: 'United Kingdom' },
    { value: 'us', label: 'United States' },
    { value: 'vi', label: 'United States Virgin Islands' },
    { value: 'uy', label: 'Uruguay' },
    { value: 'uz', label: 'Uzbekistan' },
    { value: 'vu', label: 'Vanuatu' },
    { value: 'va', label: 'Vatican City' },
    { value: 've', label: 'Venezuela' },
    { value: 'vn', label: 'Vietnam' },
    { value: 'ps', label: 'West Bank' },
    { value: 'ye', label: 'Yemen' },
    { value: 'zm', label: 'Zambia' },
    { value: 'zw', label: 'Zimbabwe' },
];