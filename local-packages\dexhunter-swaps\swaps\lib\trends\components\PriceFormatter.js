import { jsx as n, jsxs as l } from "react/jsx-runtime";
import p from "../../components/ui/tooltipDialog.js";
import a from "../../store/useStore.js";
import { formatNumber as d, formatBalance as m } from "../../utils/formatNumber.js";
import "../../hooks/useScreen.js";
import "react";
import "../../components/ui/dialog.js";
import "../../index-840f2930.js";
import "../../index-1c873780.js";
import "../../index-c7156e07.js";
import "../../index-563d1ed8.js";
import "../../index-4914f99c.js";
import "../../index-67500cd3.js";
import "../../index-c8f2666b.js";
import "../../_commonjsHelpers-10dfc225.js";
import "../../index-27cadef5.js";
import "../../index-5116e957.js";
import "../../lib.js";
import "../../extend-tailwind-merge-e63b2b56.js";
import "../../components/ui/tooltip.js";
import "../../index-0ce202b9.js";
import "../../index-bcfeaad9.js";
import "../../index-f7426637.js";
import "../../store/createTokenSearchSlice.js";
import "../../immer-548168ec.js";
import "../../store/createWalletSlice.js";
import "../../store/createSwapSettingsSlice.js";
import "../../store/createGlobalSettingsSlice.js";
import "../../store/createUserOrdersSlice.js";
import "../../store/createSwapSlice.js";
import "../../store/createChartSlice.js";
import "../../store/createBasketSlice.js";
import "../../swap/components/tokens.js";
import "../../store/createModalWhatsNewSlice.js";
import "../../store/createSwapParamsSlice.js";
function f(t) {
  if (t === "0" || !t)
    return {
      whole: "0",
      firstZero: "",
      zeroCount: 0,
      decimals: ""
    };
  const o = t == null ? void 0 : t.split(".");
  if (o.length === 1)
    return {
      whole: o[0],
      firstZero: "",
      zeroCount: 0,
      decimals: ""
    };
  const r = o[1];
  let i = 0;
  for (; i < r.length && r[i] === "0"; )
    i++;
  const e = r.substring(i);
  return i > 3 ? {
    whole: o[0] + ".",
    firstZero: "0",
    zeroCount: i - 1,
    // Deducting 1 as the first zero is displayed
    decimals: e
  } : {
    whole: o[0] + ".",
    firstZero: r.slice(0, i),
    zeroCount: 0,
    decimals: e
  };
}
const W = ({
  price: t,
  className: o,
  symbol: r
}) => {
  const { isPricesFlipped: i } = a((s) => s.globalSettingsSlice);
  if (i && (t = 1 / parseFloat(t)), parseFloat(t) > 1e8)
    return /* @__PURE__ */ n(
      p,
      {
        trigger: /* @__PURE__ */ l(
          "div",
          {
            style: {
              display: "inline-flex",
              alignItems: "baseline",
              gap: "3px"
            },
            className: o,
            children: [
              !i && /* @__PURE__ */ n("span", { children: r }),
              /* @__PURE__ */ n("span", { children: d(parseInt(t)) }),
              " "
            ]
          }
        ),
        content: m(t),
        contentClass: "text-white"
      }
    );
  if (typeof t == "number" && (t = m(t)), parseFloat(t) > 1)
    return /* @__PURE__ */ l(
      "div",
      {
        style: { display: "inline-flex", alignItems: "baseline", gap: "3px" },
        className: o,
        children: [
          !i && /* @__PURE__ */ n("span", { children: r }),
          /* @__PURE__ */ n("span", { children: t }),
          " "
        ]
      }
    );
  const e = f(t);
  return /* @__PURE__ */ l(
    "div",
    {
      className: o,
      style: { display: "inline-flex", alignItems: "baseline", gap: "3px" },
      children: [
        !i && /* @__PURE__ */ n("span", { children: r }),
        /* @__PURE__ */ l("span", { style: { display: "inline-flex", alignItems: "baseline" }, children: [
          e.whole,
          e.firstZero,
          e.zeroCount >= 3 && /* @__PURE__ */ n(
            "span",
            {
              style: { fontSize: "smaller", position: "relative", top: "3px" },
              children: e.zeroCount
            }
          ),
          e.decimals
        ] })
      ]
    }
  );
};
export {
  W as default
};
