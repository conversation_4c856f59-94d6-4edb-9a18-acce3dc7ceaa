import { jsx as C } from "react/jsx-runtime";
import { memo as e } from "react";
const t = (o) => /* @__PURE__ */ C(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 14 11",
    fill: "none",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M12.907 2.86018C13.382 2.35927 13.3611 1.56809 12.8602 1.09303C12.3593 0.617967 11.5681 0.638916 11.093 1.13982L12.907 2.86018ZM4.45427 8.13986C3.97923 8.64079 4.00021 9.43197 4.50114 9.90701C5.00206 10.382 5.79324 10.3611 6.26828 9.86014L4.45427 8.13986ZM2.90783 4.58952C2.43327 4.08814 1.64211 4.0664 1.14073 4.54096C0.639349 5.01553 0.617609 5.80668 1.09217 6.30806L2.90783 4.58952ZM4.44595 9.85137C4.92051 10.3527 5.71167 10.3745 6.21305 9.89992C6.71443 9.42536 6.73617 8.6342 6.26161 8.13282L4.44595 9.85137ZM12 2C11.093 1.13982 11.093 1.13984 11.093 1.13988C11.0929 1.13992 11.0929 1.13998 11.0928 1.14006C11.0926 1.14022 11.0924 1.14046 11.0921 1.14078C11.0915 1.14143 11.0906 1.14238 11.0894 1.14366C11.087 1.1462 11.0834 1.15001 11.0786 1.15503C11.0691 1.16508 11.0549 1.18002 11.0364 1.19957C10.9993 1.23869 10.9446 1.29628 10.8746 1.37018C10.7344 1.518 10.5323 1.73107 10.2847 1.99213C9.78951 2.51425 9.11231 3.22829 8.38423 3.99599C6.92808 5.53137 5.26838 7.28139 4.45427 8.13986L6.26828 9.86014C7.08236 9.00171 8.74202 7.25172 10.1982 5.71633C10.9263 4.94864 11.6035 4.2346 12.0986 3.71249C12.3462 3.45143 12.5483 3.23835 12.6885 3.09054C12.7586 3.01663 12.8132 2.95904 12.8503 2.91993C12.8688 2.90037 12.883 2.88543 12.8925 2.87539C12.8973 2.87036 12.9009 2.86656 12.9033 2.86401C12.9045 2.86274 12.9054 2.86178 12.9061 2.86114C12.9064 2.86082 12.9066 2.86058 12.9067 2.86042C12.9068 2.86034 12.9069 2.86028 12.9069 2.86024C12.9069 2.8602 12.907 2.86018 12 2ZM1.09217 6.30806L4.44595 9.85137L6.26161 8.13282L2.90783 4.58952L1.09217 6.30806Z",
        fill: "currentColor"
      }
    )
  }
), L = e(t);
export {
  L as default
};
