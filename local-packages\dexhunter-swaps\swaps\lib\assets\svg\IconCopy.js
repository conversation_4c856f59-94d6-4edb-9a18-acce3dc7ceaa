import { jsx as L } from "react/jsx-runtime";
import { memo as H } from "react";
const V = (C) => /* @__PURE__ */ L(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    viewBox: "0 0 12 12",
    fill: "none",
    ...C,
    children: /* @__PURE__ */ L(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M5.7918 7.74193e-06V0L5.78855 1.54898e-05C4.96744 0.00455225 4.18179 0.335094 3.60437 0.918928C3.1172 1.41152 2.81198 2.05037 2.73122 2.7312C2.03852 2.81336 1.38998 3.1278 0.894702 3.6286C0.317645 4.21209 -0.00413175 5.00083 4.00649e-05 5.82141V8.88937C-0.00809343 10.5982 1.3699 11.9906 3.07889 12H3.08209H6.20823H6.21149C7.03259 11.9954 7.81824 11.6649 8.39563 11.0811C8.88278 10.5885 9.18801 9.94962 9.26878 9.26881C9.96148 9.18664 10.61 8.87224 11.1053 8.37144C11.6827 7.78754 12.0045 6.99825 12 6.17714V3.11317C12.0045 2.29205 11.6827 1.50276 11.1053 0.918928C10.5279 0.335094 9.74226 0.00455225 8.92115 1.54839e-05V7.74115e-06H8.9179L5.7918 7.74193e-06ZM8.9179 0.587719L8.92115 1.54839e-05L8.9179 0.580653V0.587719ZM8.91605 1.1613H8.9179V0.587719L8.91473 1.16129L8.91605 1.1613ZM9.29036 8.0901C9.66314 8.01412 10.0082 7.82924 10.2796 7.55482C10.6405 7.18994 10.8415 6.6967 10.8387 6.18364L10.8387 6.18039V3.10994V3.10671H10.8387C10.8415 2.59359 10.6405 2.10036 10.2796 1.73552C9.91916 1.371 9.4287 1.16448 8.91605 1.1613H5.79365L5.79497 1.16129L5.7918 0.587576V0.580652L5.78859 0.0071403L5.7918 0.587576V1.1613H5.79365C5.28099 1.16448 4.79054 1.371 4.43007 1.73552C4.16371 2.00483 3.9844 2.3441 3.9099 2.70968H3.29035H3.08209L3.08209 2.70968L3.07889 2.70969C2.96212 2.71034 2.84606 2.71757 2.73122 2.7312C2.71632 2.85682 2.70906 2.98387 2.70971 3.11169V3.29033C2.70971 3.22849 2.71937 3.16891 2.73728 3.11302L3.0811 3.1111L3.08209 3.1111L3.29033 3.10994H3.08209H3.0811H2.73827C2.79221 2.94477 2.9182 2.81218 3.07911 2.74931L3.08209 2.74815C3.14674 2.7233 3.21695 2.70968 3.29035 2.70968C3.54692 2.70968 3.76463 2.8761 3.84142 3.10688L3.84243 3.10994C3.86097 3.16672 3.87099 3.22736 3.87099 3.29033V3.10994H3.871L3.87098 3.10671C3.87024 2.97236 3.88347 2.83936 3.9099 2.70968H6.20831H6.21149V2.70969C7.03259 2.71423 7.81824 3.04477 8.39563 3.6286C8.97226 4.21167 9.294 4.99968 9.2903 5.81962H8.70972L9.29029 5.82287C9.29029 5.82178 9.2903 5.8207 9.2903 5.81962H9.29036V8.0901ZM9.29036 8.70968V8.0901C9.16761 8.11512 9.04185 8.12833 8.91473 8.12903H8.70964C8.78192 8.12903 8.85111 8.14224 8.91493 8.16636L8.9179 8.70285V8.70968L8.9189 8.8889L8.70964 8.89007L8.91891 8.89007L8.92093 9.25071L8.9179 9.25189C8.85328 9.27672 8.78309 9.29032 8.70972 9.29032H8.9179H8.92115C9.03791 9.28967 9.15395 9.28243 9.26878 9.26881C9.28374 9.1427 9.291 9.01514 9.29029 8.88682V8.71925L9.29036 8.70968ZM9.29029 8.70968C9.29029 8.46239 9.13573 8.2512 8.9179 8.16749V8.12903H8.91473L8.91493 8.16636L8.9179 8.16749V8.70285L8.91794 8.70968L8.91893 8.8889H8.9189L8.91891 8.89007H8.91894L8.92093 9.25071C9.08185 9.18784 9.20786 9.05524 9.2618 8.89007H9.29029V8.88682L9.2628 8.88697C9.27977 8.83397 9.28934 8.77766 9.29029 8.71925V8.70968ZM9.2628 8.88697L9.2618 8.89007H8.91894L8.91893 8.8889L9.2628 8.88697ZM8.12908 5.81962V5.81637C8.13186 5.30331 7.93081 4.81007 7.56996 4.4452C7.20951 4.08069 6.71907 3.87417 6.20644 3.87097H6.20831V3.29033L6.21148 2.71032L6.20506 3.87097L6.20644 3.87097H3.29035H3.08392L3.0853 3.87097L3.08209 3.29033L3.08209 3.87097H3.08392C2.5713 3.87417 2.08089 4.08069 1.72039 4.4452C1.35957 4.81007 1.15847 5.30331 1.16132 5.81637L1.16133 5.81962H1.16133V8.89084V8.89386H1.16132C1.15571 9.96217 2.01702 10.8328 3.0853 10.8387H3.08209V11.4194L3.0853 10.8387L6.20506 10.8387L6.20823 11.4124V11.4194L6.21149 12L6.20823 11.4124V10.8387L6.20506 10.8387C6.71819 10.8358 7.20918 10.6293 7.56996 10.2645C7.93081 9.89961 8.13186 9.40638 8.12908 8.89332L8.129 8.89007V8.70968C8.129 8.77265 8.13903 8.83328 8.15757 8.89007L8.15859 8.89315C8.19222 8.99423 8.2529 9.08295 8.33203 9.15075C8.25293 9.08294 8.19228 8.99422 8.15866 8.89315L8.15764 8.89007C8.1391 8.83328 8.12908 8.77265 8.12908 8.70968V5.81962ZM2.73827 3.10994L2.73728 3.11302L2.70971 3.11317L2.70971 3.11169V3.10994H2.73827ZM12 3.11317L11.4193 3.10994H12V3.11317ZM12 6.17714V6.18039H11.4193L12 6.17714ZM4.00649e-05 8.88937L4.78068e-05 8.88782L0.580683 8.89084H4.00649e-05V8.88937ZM4.00649e-05 5.82141V5.81962H0.580683L4.78068e-05 5.82287L4.00649e-05 5.82141Z",
        fill: "currentColor"
      }
    )
  }
), M = H(V);
export {
  M as default
};
