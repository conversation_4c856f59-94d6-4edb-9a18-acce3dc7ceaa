'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { Skeleton, SkeletonCircle } from "@/components/ui/skeleton";
import { Spotlight } from '@/components/ui/spotlight';
import { genUploader } from "uploadthing/client";
import type { OurFileRouter } from "@/app/api/uploadthing/core";
import { randomImageNameGenerator } from "@/public/main";
import Swal from 'sweetalert2';
import { useDispatch } from 'react-redux';
import { setUserInfo } from '@/redux/slices/userInfoSlice';
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import { updateUserBanner } from "@/lib/api/updateUserSettings";
import AccountTab from './tabs/AccountTab/AccountTab';
import NotificationsTab from './tabs/NotificationsTab';
import PaymentsTab from './tabs/PaymentsTab';
import BillingTab from './tabs/BillingTab';
import PrivacySafetyTab from './tabs/PrivacySafetyTab';
import ProfileImageUploadModal from './modals/ProfileImageUploadModal';
import CoverBannerUploadModal from './modals/CoverBannerUploadModal';
import { useMutation } from 'convex/react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useUser } from '@/hooks/useUser';

interface ControlPanelClientProps {
    initialUserData?: any;
    connectedAccounts?: any;
}

const ControlPanelClient = ({ initialUserData, connectedAccounts }: ControlPanelClientProps) => {
    const router = useRouter();
    const wallet = useSelector((state: any) => state.wallet);

    // Convex session management
    const { isAuthenticated, user: convexUser, signOut, isLoading: sessionLoading } = useUser();

    const [activeTab, setActiveTab] = useState('Account');
    const [bannerLoading, setBannerLoading] = useState<boolean>(true);
    const [profileLoading, setProfileLoading] = useState<boolean>(true);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [userData, setUserData] = useState(initialUserData || {});
    const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
    const [isCoverModalOpen, setIsCoverModalOpen] = useState(false);
    const [offset, setOffset] = useState(0);
    const contentRef = useRef<HTMLDivElement>(null);
    const sidebarRef = useRef(null) as any;
    const dispatch = useDispatch();

    const Toast = () => toast('');

    const { uploadFiles } = genUploader<OurFileRouter>();

    const uploadBannerVideo = useMutation(api.accounts.uploadBannerVideo);

    // Sidebar menu options
    const menuItems = [
        'Notification Feed',
        'Subscriptions',
        'Followers',
        'Following',
        'Ad Alerts',
        'Analytics',
        'Recent Activity',
    ];

    // Handle form changes
    const handleFormChange = (hasChanges: boolean, updatedData?: any, loading?: boolean) => {
        setHasUnsavedChanges(hasChanges);
        setIsLoading(loading || false);
        if (updatedData) {
            // Make sure website is included in connected_socials
            if (updatedData.officialWebsite) {
                const connectedSocials = updatedData.connected_socials || [];
                const websiteIndex = connectedSocials.findIndex((social: any) => social.platform === 'website');

                if (websiteIndex === -1) {
                    connectedSocials.push({
                        platform: 'website',
                        username: updatedData.officialWebsite
                    });
                } else {
                    connectedSocials[websiteIndex].username = updatedData.officialWebsite;
                }

                updatedData.connected_socials = connectedSocials;
            }

            setUserData(updatedData);
            dispatch(setUserInfo(updatedData));
        }
    };

    // Handle tab change and scroll to top
    const handleTabChange = (tab: string) => {
        if (hasUnsavedChanges || isLoading) {
            Swal.fire({
                title: isLoading ? 'Loading...' : 'Unsaved Changes',
                text: isLoading
                    ? 'Please wait for the current operation to complete.'
                    : 'You have unsaved changes. Do you want to discard them?',
                icon: 'warning',
                showCancelButton: !isLoading,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: isLoading ? 'OK' : 'Yes, discard changes',
                cancelButtonText: 'No, stay here'
            }).then((result) => {
                if (result.isConfirmed && !isLoading) {
                    // Only allow tab change if not loading
                    setActiveTab(tab);
                    setHasUnsavedChanges(false);
                    // Reset any form data if necessary
                    setTimeout(() => scrollToContent(), 100); // Add small delay to ensure DOM update
                }
            });
        } else {
            setActiveTab(tab);
            setTimeout(() => scrollToContent(), 100); // Add small delay to ensure DOM update
        }
    };

    // Scroll to content function
    const scrollToContent = () => {
        if (contentRef.current) {
            const element = contentRef.current;
            const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
            window.scrollTo({
                top: elementPosition - 120,
                behavior: 'smooth'
            });
        }
    };

    // Render the appropriate component based on active tab
    const renderTabContent = () => {
        switch (activeTab) {
            case 'Account':
                return <AccountTab userData={userData} onFormChange={handleFormChange} connectedAccounts={connectedAccounts} />;
            case 'Notifications':
                return <NotificationsTab
                    userData={userData}
                    onFormChange={handleFormChange}
                />;
            case 'Payments':
                return <PaymentsTab />;
            case 'Billing':
                return <BillingTab />;
            case 'Privacy':
                return <PrivacySafetyTab userData={userData} onFormChange={handleFormChange} />;
            case 'Help Center':
                return (
                    <motion.div
                        key="help"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                    >
                        <h2 className="text-xl font-semibold text-white mb-4">Help Center</h2>
                        <Link href="/help/faq" className="text-blue-400 hover:text-blue-300 block">FAQs</Link>
                        <Link href="/help/support" className="text-blue-400 hover:text-blue-300 block">Contact Support</Link>
                    </motion.div>
                );
            default:
                return <AccountTab userData={userData} onFormChange={handleFormChange} connectedAccounts={connectedAccounts} />;
        }
    };

    // Handle sticky sidebar
    useEffect(() => {
        const handleScroll = () => {
            const sidebarTop = sidebarRef.current?.getBoundingClientRect().top;
            const headerHeight = 97; // Adjust based on your actual header height

            if (sidebarTop <= headerHeight) {
                setOffset(headerHeight); // Apply offset when sticky
            } else {
                setOffset(0); // Reset when not sticky
            }
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    // Sync user data with Convex session
    useEffect(() => {
        if (convexUser && isAuthenticated) {
            // Use Convex user data as the source of truth
            const convexUserData = {
                userId: convexUser.user_id,
                accountId: convexUser.accountId,
                email: convexUser.email,
                walletAddress: convexUser.wallet_address,
                blockchain: convexUser.blockchain,
                accountType: convexUser.account_type,
                registrationDate: convexUser.registration_date,
                userInfo: convexUser.user_info,
                profilePhoto: convexUser.user_info?.profilePhoto,
                displayName: convexUser.user_info?.account?.displayName,
                username: convexUser.user_info?.account?.username,
                coverBanner: convexUser.user_info?.coverBanner,
                coverBannerType: convexUser.user_info?.coverBannerType,
                status: convexUser.status,
                lastActive: convexUser.lastActive,
                subscribers: convexUser.subscribers,
                ...initialUserData // Merge with any additional initial data
            };

            setUserData(convexUserData);
            dispatch(setUserInfo(convexUserData));
        } else if (initialUserData) {
            // Fallback to initial data if no Convex session
            setUserData(initialUserData);
        }
    }, [convexUser, isAuthenticated, initialUserData, dispatch]);

    // Redirect if not authenticated
    useEffect(() => {
        if (!sessionLoading && !isAuthenticated) {
            router.push('/');
        }
    }, [isAuthenticated, sessionLoading, router]);

    // Show loading state while session is loading
    if (sessionLoading) {
        return (
            <div className="min-h-screen bg-white dark:bg-[#121212] flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900 dark:border-white mx-auto"></div>
                    <span classNAme="control-panel-loader"></span>
                </div>
            </div>
        );
    }

    // Show unauthorized message if not authenticated
    if (!isAuthenticated) {
        return (
            <div className="min-h-screen bg-white dark:bg-[#121212] flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Access Denied</h1>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">You need to be logged in to access account settings.</p>
                    <button
                        onClick={() => router.push('/')}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                    >
                        Go Home
                    </button>
                </div>
            </div>
        );
    }

    // In the return statement, update the Cover Photo section:
    return (
        <div className="min-h-screen bg-white dark:bg-[#121212]">
            {/* Control Panel */}
            <div className="container mx-auto pt-4 pb-8 flex gap-6 xl:max-w-[82.86%] 2xl:max-w-[90.7%]">
                {/* Sidebar */}
                <div className="w-72 flex-shrink-0" ref={sidebarRef}>
                    <div
                        className="!sticky transition-all duration-200"
                        style={{ top: `${offset}px` }}
                    >
                        <Spotlight
                            className="bg-zinc-700 blur-2xl"
                            size={24}
                            springOptions={{ bounce: 0.3, duration: 0.1 }}
                        />
                        <div className="space-y-2">
                            {menuItems.map((item) => (
                                <motion.button
                                    key={item}
                                    onClick={() => handleTabChange(item)}
                                    className={`w-full text-left py-3 px-2 border-b transition focus-visible:border-[#121212] dark:focus-visible:border-white ${activeTab === item
                                        ? 'text-[#121212] dark:text-white border-[#121212] dark:border-white'
                                        : 'text-[#121212]/40 dark:text-white/40 hover:bg-[#121212]/20 dark:hover:bg-white/20 border-[#121212]/20 dark:border-white/20'
                                        }`}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    {item}
                                </motion.button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <div ref={contentRef} className="flex-grow p-4 pr-0">
                    {renderTabContent()}
                </div>
            </div>
        </div>
    );
};
export default ControlPanelClient;