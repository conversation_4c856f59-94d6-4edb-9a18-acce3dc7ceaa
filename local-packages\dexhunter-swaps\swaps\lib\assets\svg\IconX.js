import { jsx as C } from "react/jsx-runtime";
import { memo as t } from "react";
const e = (o) => /* @__PURE__ */ C(
  "svg",
  {
    width: "1em",
    height: "1em",
    viewBox: "0 0 12 12",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...o,
    children: /* @__PURE__ */ C(
      "path",
      {
        d: "M11.7071 1.70711C12.0976 1.31658 12.0976 0.683419 11.7071 0.292894C11.3166 -0.0976307 10.6834 -0.0976315 10.2929 0.292893L11.7071 1.70711ZM0.292894 10.2929C-0.0976307 10.6834 -0.0976313 11.3166 0.292893 11.7071C0.683416 12.0976 1.31658 12.0976 1.70711 11.7071L0.292894 10.2929ZM1.70711 0.292893C1.31658 -0.0976311 0.683417 -0.0976311 0.292893 0.292893C-0.0976311 0.683417 -0.0976311 1.31658 0.292893 1.70711L1.70711 0.292893ZM10.2929 11.7071C10.6834 12.0976 11.3166 12.0976 11.7071 11.7071C12.0976 11.3166 12.0976 10.6834 11.7071 10.2929L10.2929 11.7071ZM11 1C10.2929 0.292893 10.2929 0.292905 10.2929 0.29293C10.2928 0.292956 10.2928 0.292994 10.2927 0.293044C10.2926 0.293145 10.2925 0.293297 10.2923 0.293498C10.2919 0.293901 10.2913 0.294505 10.2905 0.295306C10.2889 0.296909 10.2865 0.299304 10.2833 0.302472C10.277 0.308808 10.2676 0.318236 10.2552 0.330605C10.2304 0.355342 10.1939 0.391842 10.1469 0.438893C10.0528 0.532994 9.91649 0.669297 9.74768 0.838107C9.41006 1.17573 8.94241 1.64337 8.42232 2.16346C7.38213 3.20366 6.13213 4.45366 5.29289 5.29289L6.70711 6.70711C7.54634 5.86787 8.79634 4.61787 9.83653 3.57768C10.3566 3.05758 10.8243 2.58994 11.1619 2.25232C11.3307 2.08351 11.467 1.94721 11.5611 1.85311C11.6082 1.80606 11.6447 1.76956 11.6694 1.74482C11.6818 1.73245 11.6912 1.72302 11.6975 1.71669C11.7007 1.71352 11.7031 1.71112 11.7047 1.70952C11.7055 1.70872 11.7061 1.70812 11.7065 1.70771C11.7067 1.70751 11.7069 1.70736 11.707 1.70726C11.707 1.70721 11.707 1.70717 11.7071 1.70715C11.7071 1.70712 11.7071 1.70711 11 1ZM5.29289 5.29289C4.45366 6.13213 3.20366 7.38213 2.16347 8.42232C1.64337 8.94242 1.17573 9.41006 0.838109 9.74768C0.669299 9.91649 0.532996 10.0528 0.438894 10.1469C0.391843 10.1939 0.355343 10.2304 0.330606 10.2552C0.318237 10.2675 0.308809 10.277 0.302473 10.2833C0.299306 10.2865 0.296911 10.2889 0.295308 10.2905C0.294506 10.2913 0.293903 10.2919 0.2935 10.2923C0.293299 10.2925 0.293146 10.2926 0.293046 10.2927C0.292995 10.2928 0.292957 10.2928 0.292932 10.2929C0.292907 10.2929 0.292894 10.2929 1 11C1.70711 11.7071 1.70712 11.7071 1.70714 11.7071C1.70717 11.707 1.70721 11.707 1.70726 11.707C1.70736 11.7069 1.70751 11.7067 1.70771 11.7065C1.70811 11.7061 1.70872 11.7055 1.70952 11.7047C1.71112 11.7031 1.71352 11.7007 1.71669 11.6975C1.72302 11.6912 1.73245 11.6818 1.74482 11.6694C1.76956 11.6447 1.80606 11.6082 1.85311 11.5611C1.94721 11.467 2.08351 11.3307 2.25232 11.1619C2.58994 10.8243 3.05759 10.3566 3.57768 9.83654C4.61787 8.79634 5.86787 7.54634 6.70711 6.70711L5.29289 5.29289ZM0.292893 1.70711L10.2929 11.7071L11.7071 10.2929L1.70711 0.292893L0.292893 1.70711Z",
        fill: "currentColor"
      }
    )
  }
), L = t(e);
export {
  L as default
};
