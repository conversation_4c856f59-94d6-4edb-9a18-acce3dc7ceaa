'use client';

import React from 'react';
import { genUploader } from "uploadthing/client";
import type { OurFileRouter } from "@/app/api/uploadthing/core";
import { randomImageNameGenerator } from "@/public/main";
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { updateUserBanner } from "@/lib/api/updateUserSettings";
import { toast } from 'react-toastify';
import { useDispatch } from 'react-redux';
import { setUserInfo } from '@/redux/slices/userInfoSlice';

export const useImageUploads = (userData: any, setUserData: (data: any) => void) => {
    const dispatch = useDispatch();
    const { uploadFiles } = genUploader<OurFileRouter>();
    const uploadBannerVideo = useMutation(api.accounts.uploadBannerVideo);

    const handleProfileImageSave = async (imageData: string) => {
        try {
            const mimeTypeMatch = imageData.match(/data:(image\/[a-zA-Z]+);base64,/);
            if (!mimeTypeMatch) {
                throw new Error('Invalid image data');
            }
            const mimeType = mimeTypeMatch[1];
            const extension = mimeType.split('/')[1];

            const byteCharacters = atob(imageData.split(',')[1]);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: mimeType });
            const fileName = `${randomImageNameGenerator()}.${extension}`;
            const file = new File([blob], fileName, { type: mimeType });

            const response = await uploadFiles('profileImage', {
                files: [file],
                headers: {
                    'Content-Type': 'application/json',
                    'x-account-type': userData?.accountType || 'user'
                },
            });

            if (response?.[0]?.url) {
                const updatedUserData = { ...userData, profilePhoto: response[0].url };
                setUserData(updatedUserData);
                dispatch(setUserInfo(updatedUserData));
                toast.success("Profile photo updated successfully");
                return { success: true, message: 'Profile photo updated successfully', url: response[0].url };
            } else {
                throw new Error("No URL in response");
            }
        } catch (error: any) {
            console.error("Error uploading profile photo:", error);
            toast.error("Failed to update profile photo. Please try again.");
            return { success: false, error: error.message };
        }
    };

    const handleCoverBannerSave = async (mediaData: string, mediaType: 'image' | 'video' | 'url') => {
        try {
            if (mediaType === 'image') {
                const mimeTypeMatch = mediaData.match(/data:(image\/[a-zA-Z]+);base64,/);
                if (!mimeTypeMatch) {
                    throw new Error('Invalid image data');
                }
                const mimeType = mimeTypeMatch[1];
                const extension = mimeType.split('/')[1];
                const byteCharacters = atob(mediaData.split(',')[1]);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], { type: mimeType });
                const fileName = `${randomImageNameGenerator()}.${extension}`;
                const file = new File([blob], fileName, { type: mimeType });

                const response = await uploadFiles('coverImage', {
                    files: [file],
                    headers: {
                        'Content-Type': 'application/json',
                        'x-account-type': userData?.accountType || 'user'
                    },
                });

                if (response?.[0]?.url) {
                    const updatedUserData = {
                        ...userData,
                        coverBanner: response[0].url,
                        coverBannerType: 'image'
                    };
                    setUserData(updatedUserData);
                    dispatch(setUserInfo(updatedUserData));
                    toast.success("Cover banner updated successfully");
                    return { success: true, message: 'Cover banner updated successfully', url: response[0].url };
                } else {
                    throw new Error("No URL in response");
                }
            } else if (mediaType === 'video') {
                const mimeTypeMatch = mediaData.match(/data:(video\/[^;]+);base64,/);
                if (!mimeTypeMatch) {
                    throw new Error('Invalid video data');
                }
                const mimeType = mimeTypeMatch[1];
                const extension = mimeType.split('/')[1];
                const base64Data = mediaData.split(',')[1];
                const fileName = `${randomImageNameGenerator()}.${extension}`;
                const result = await uploadBannerVideo({
                    userId: userData.userId,
                    file: base64Data,
                    fileType: mimeType,
                    fileName,
                });
                if (!result.success) {
                    throw new Error(result.error || 'Failed to upload video');
                }
                if (result.success && result.url) {
                    const updatedUserData = {
                        ...userData,
                        coverBanner: result.url,
                        coverBannerType: 'video'
                    };
                    setUserData(updatedUserData);
                    dispatch(setUserInfo(updatedUserData));
                    toast.success('Cover banner updated successfully');
                    return { success: true, message: 'Cover banner updated successfully', url: result.url };
                } else {
                    throw new Error('No URL in response');
                }
            } else if (mediaType === 'url') {
                const isVideoUrl = /\.(mp4|webm|ogg|mov)$/i.test(mediaData);
                const updatedUserData = {
                    ...userData,
                    coverBanner: mediaData,
                    coverBannerType: isVideoUrl ? 'video' : 'image'
                };
                const bannerUpdate = await updateUserBanner(userData.userId, userData.accountType, updatedUserData);
                if (!bannerUpdate.success) {
                    throw new Error('Failed to update banner:' + bannerUpdate.error);
                }
                setUserData(updatedUserData);
                dispatch(setUserInfo(updatedUserData));
                toast.success("Cover banner updated successfully");
                return { success: true, message: 'Cover banner updated successfully', url: mediaData };
            }
            throw new Error("Invalid media type");
        } catch (error: any) {
            console.error("Error uploading cover banner:", error);
            return { success: false, error: error.message };
        }
    };

    return { handleProfileImageSave, handleCoverBannerSave };
};