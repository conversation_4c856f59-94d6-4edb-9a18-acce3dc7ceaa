'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useOutsideClick } from '@/hooks/useOutsideClick';
import { Skeleton } from "@/components/ui/skeleton";
import { languageOptions } from '@/app/account/settings/constants';

export default function LanguageSelector() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentLang, setCurrentLang] = useState('en');
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(() => {
    // Initialize all languages with loading state true
    return languageOptions.reduce((acc, lang) => ({
      ...acc,
      [lang.value]: true
    }), { current: true }); // 'current' is for the main button flag
  });
  const dropdownRef = useRef<HTMLDivElement>(null) as any;
  const router = useRouter();

  const handleImageLoad = (langCode: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [langCode]: false
    }));
  };

  // Helper function to get flag code from language code
  const getFlagCode = (langCode: string) => {
    const language = languageOptions.find((lang: any) => lang.value === langCode);
    return language ? language.flag : langCode;
  };

  // Close dropdown when clicking outside
  useOutsideClick(dropdownRef, () => setIsOpen(false));

  // Detect user's browser language on component mount
  useEffect(() => {
    const detectLanguage = () => {
      const browserLang = navigator.language.split('-')[0];
      const supportedLang = languageOptions.find((lang: any) => lang.value === browserLang);
      if (supportedLang) {
        setCurrentLang(supportedLang.value);
      }
    };

    detectLanguage();
  }, []);

  const handleLanguageChange = (langCode: string) => {
    setCurrentLang(langCode);
    setIsOpen(false);

    // Here you would implement your language change logic
    // For example, setting a cookie or localStorage value
    localStorage.setItem('preferredLanguage', langCode);

    // You might want to refresh the page or update the UI
    // router.refresh();

    console.log('Language changed to:', langCode);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-11 h-11 flex items-center justify-center p-2 rounded-full border-solid border-[1px] border-gray-300 dark:border-gray-300 focus-visible:border-solid focus-visible:border"
        aria-label="Select language"
      >
        <Skeleton loading={loadingStates.current} width="100%" borderRadius="50%">
          <Image
            src={`/images/flags/ISO-flags/${getFlagCode(currentLang)}.svg`}  // Changed from getFlagCode(currentLang)
            alt={`${currentLang} flag`}
            width={28}
            height={28}
            className="rounded-none object-cover w-7 h-6"
            onLoad={() => handleImageLoad('current')}
          />
        </Skeleton>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 max-h-[400px] overflow-y-auto bg-white/80 dark:bg-[#18181b]/80 backdrop-blur-md rounded-lg pt-3 border-solid border-black/20 dark:border-white/20 z-50">
          <div className="py-2">
            {languageOptions.map((lang: any) => (
              <button
                key={lang.value}
                onClick={() => handleLanguageChange(lang.value)}
                className={`w-full px-4 py-2 text-left flex items-center hover:bg-accent/50 transition-colors ${currentLang === lang.value ? 'bg-accent/30' : ''
                  }`}
              >
                <Skeleton
                  loading={loadingStates[lang.value]}
                  width="20px"
                  height="20px"
                  className="mr-3"
                >
                  <Image
                    src={`/images/flags/ISO-flags/${getFlagCode(lang.value)}.svg`}  // Changed from lang.flag
                    alt={`${lang.value} flag`}
                    width={20}
                    height={20}
                    className="rounded-none mr-3 object-cover"
                    onLoad={() => handleImageLoad(lang.value)}
                  />
                </Skeleton>
                <span className="text-sm text-foreground">{lang.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};