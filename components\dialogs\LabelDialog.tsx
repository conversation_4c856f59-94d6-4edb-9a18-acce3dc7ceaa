import React, { useState, useEffect } from 'react';
import { AnimatedModal } from '@/components/ui/animated-modal';
import { toast } from 'react-toastify';

interface LabelOption {
  value: string;
  label: string;
  color: string;
}

interface LabelDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (label: string, customLabel?: string) => void;
  labelOptions: LabelOption[];
  selectedConversations: string[];
}

const LabelDialog: React.FC<LabelDialogProps> = ({
  isOpen,
  onClose,
  onApply,
  labelOptions,
  selectedConversations
}) => {
  const [selectedLabel, setSelectedLabel] = useState<string | null>(null);
  const [newLabel, setNewLabel] = useState<string>('');

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedLabel(null);
      setNewLabel('');
    }
  }, [isOpen]);

  const handleLabelSelect = (labelValue: string) => {
    setSelectedLabel(labelValue);
  };

  const handleApply = () => {
    if (!selectedLabel) return;

    if (selectedLabel === 'custom' && newLabel.trim()) {
      onApply(selectedLabel, newLabel.trim());
    } else if (selectedLabel !== 'custom') {
      onApply(selectedLabel);
    }
  };

  return (
    <AnimatedModal
      open={isOpen}
      onOpenChange={onClose}
      size="xl"
      title="Labels"
    >
      <div className="p-4 space-y-6">
        <div className="grid grid-cols-4 gap-3">
          {labelOptions.map((label) => (
            <button
              key={label.value}
              onClick={() => handleLabelSelect(label.value)}
              className={`flex items-center justify-center px-4 py-2 rounded-full border border-gray-700 dark:border-gray-600 transition-all ${selectedLabel === label.value
                  ? `${label.color} text-white font-medium`
                  : `${label.color} hover:bg-gray-100 hover:text-[#81818b] dark:hover:bg-white dark:hover:text-[#81818B]`
                }`}
            >
              {label.label}
            </button>
          ))}

        </div>

        <div className="flex justify-between mt-6">
          <button
            onClick={handleApply}
            disabled={!selectedLabel || (selectedLabel === 'custom' && !newLabel.trim())}
            className="px-6 py-2 bg-turquoise text-white rounded-md hover:bg-turquoise/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Apply
          </button>

          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white bg-red-500 hover:bg-red-700 rounded-lg"
          >
            Cancel
          </button>
        </div>
      </div>
    </AnimatedModal>
  );
};
export default LabelDialog;