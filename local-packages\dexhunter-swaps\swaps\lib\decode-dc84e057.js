import { b as ae } from "./index-ca8eb9e1.js";
let L;
try {
  L = new TextDecoder();
} catch {
}
let a, O, r = 0;
const fe = 105, ue = 57342, oe = 57343, H = 57337, W = 6, _ = {};
let u = {}, y, P, N = 0, I = 0, x, k, p = [], J = [], S, m, j, Q = {
  useRecords: !1,
  mapsAsObjects: !0
}, D = !1, te = 2;
try {
  new Function("");
} catch {
  te = 1 / 0;
}
class C {
  constructor(t) {
    if (t && ((t.keyMap || t._keyMap) && !t.useRecords && (t.useRecords = !1, t.mapsAsObjects = !0), t.useRecords === !1 && t.mapsAsObjects === void 0 && (t.mapsAsObjects = !0), t.getStructures && (t.getShared = t.getStructures), t.getShared && !t.structures && ((t.structures = []).uninitialized = !0), t.keyMap)) {
      this.mapKey = /* @__PURE__ */ new Map();
      for (let [s, n] of Object.entries(t.keyMap))
        this.mapKey.set(n, s);
    }
    Object.assign(this, t);
  }
  /*
  decodeKey(key) {
  	return this.keyMap
  		? Object.keys(this.keyMap)[Object.values(this.keyMap).indexOf(key)] || key
  		: key
  }
  */
  decodeKey(t) {
    return this.keyMap && this.mapKey.get(t) || t;
  }
  encodeKey(t) {
    return this.keyMap && this.keyMap.hasOwnProperty(t) ? this.keyMap[t] : t;
  }
  encodeKeys(t) {
    if (!this._keyMap)
      return t;
    let s = /* @__PURE__ */ new Map();
    for (let [n, i] of Object.entries(t))
      s.set(this._keyMap.hasOwnProperty(n) ? this._keyMap[n] : n, i);
    return s;
  }
  decodeKeys(t) {
    if (!this._keyMap || t.constructor.name != "Map")
      return t;
    if (!this._mapKey) {
      this._mapKey = /* @__PURE__ */ new Map();
      for (let [n, i] of Object.entries(this._keyMap))
        this._mapKey.set(i, n);
    }
    let s = {};
    return t.forEach((n, i) => s[E(this._mapKey.has(i) ? this._mapKey.get(i) : i)] = n), s;
  }
  mapDecode(t, s) {
    let n = this.decode(t);
    if (this._keyMap)
      switch (n.constructor.name) {
        case "Array":
          return n.map((i) => this.decodeKeys(i));
      }
    return n;
  }
  decode(t, s) {
    if (a)
      return se(() => ($(), this ? this.decode(t, s) : C.prototype.decode.call(Q, t, s)));
    O = s > -1 ? s : t.length, r = 0, I = 0, P = null, x = null, a = t;
    try {
      m = t.dataView || (t.dataView = new DataView(t.buffer, t.byteOffset, t.byteLength));
    } catch (n) {
      throw a = null, t instanceof Uint8Array ? n : new Error("Source must be a Uint8Array or Buffer but was a " + (t && typeof t == "object" ? t.constructor.name : typeof t));
    }
    if (this instanceof C) {
      if (u = this, S = this.sharedValues && (this.pack ? new Array(this.maxPrivatePackedValues || 16).concat(this.sharedValues) : this.sharedValues), this.structures)
        return y = this.structures, B();
      (!y || y.length > 0) && (y = []);
    } else
      u = Q, (!y || y.length > 0) && (y = []), S = null;
    return B();
  }
  decodeMultiple(t, s) {
    let n, i = 0;
    try {
      let l = t.length;
      D = !0;
      let f = this ? this.decode(t, l) : Y.decode(t, l);
      if (s) {
        if (s(f) === !1)
          return;
        for (; r < l; )
          if (i = r, s(B()) === !1)
            return;
      } else {
        for (n = [f]; r < l; )
          i = r, n.push(B());
        return n;
      }
    } catch (l) {
      throw l.lastPosition = i, l.values = n, l;
    } finally {
      D = !1, $();
    }
  }
}
function B() {
  try {
    let e = o();
    if (x) {
      if (r >= x.postBundlePosition) {
        let t = new Error("Unexpected bundle position");
        throw t.incomplete = !0, t;
      }
      r = x.postBundlePosition, x = null;
    }
    if (r == O)
      y = null, a = null, k && (k = null);
    else if (r > O) {
      let t = new Error("Unexpected end of CBOR data");
      throw t.incomplete = !0, t;
    } else if (!D)
      throw new Error("Data read, but end of buffer not reached");
    return e;
  } catch (e) {
    throw $(), (e instanceof RangeError || e.message.startsWith("Unexpected end of buffer")) && (e.incomplete = !0), e;
  }
}
function o() {
  let e = a[r++], t = e >> 5;
  if (e = e & 31, e > 23)
    switch (e) {
      case 24:
        e = a[r++];
        break;
      case 25:
        if (t == 7)
          return ye();
        e = m.getUint16(r), r += 2;
        break;
      case 26:
        if (t == 7) {
          let s = m.getFloat32(r);
          if (u.useFloat32 > 2) {
            let n = le[(a[r] & 127) << 1 | a[r + 1] >> 7];
            return r += 4, (n * s + (s > 0 ? 0.5 : -0.5) >> 0) / n;
          }
          return r += 4, s;
        }
        e = m.getUint32(r), r += 4;
        break;
      case 27:
        if (t == 7) {
          let s = m.getFloat64(r);
          return r += 8, s;
        }
        if (t > 1) {
          if (m.getUint32(r) > 0)
            throw new Error("JavaScript does not support arrays, maps, or strings with length over 4294967295");
          e = m.getUint32(r + 4);
        } else
          u.int64AsNumber ? (e = m.getUint32(r) * 4294967296, e += m.getUint32(r + 4)) : e = m.getBigUint64(r);
        r += 8;
        break;
      case 31:
        switch (t) {
          case 2:
          case 3:
            throw new Error("Indefinite length not supported for byte or text strings");
          case 4:
            let s = [], n, i = 0;
            for (; (n = o()) != _; )
              s[i++] = n;
            return t == 4 ? s : t == 3 ? s.join("") : ae.Buffer.concat(s);
          case 5:
            let l;
            if (u.mapsAsObjects) {
              let f = {};
              if (u.keyMap)
                for (; (l = o()) != _; )
                  f[E(u.decodeKey(l))] = o();
              else
                for (; (l = o()) != _; )
                  f[E(l)] = o();
              return f;
            } else {
              j && (u.mapsAsObjects = !0, j = !1);
              let f = /* @__PURE__ */ new Map();
              if (u.keyMap)
                for (; (l = o()) != _; )
                  f.set(u.decodeKey(l), o());
              else
                for (; (l = o()) != _; )
                  f.set(l, o());
              return f;
            }
          case 7:
            return _;
          default:
            throw new Error("Invalid major type for indefinite length " + t);
        }
      default:
        throw new Error("Unknown token " + e);
    }
  switch (t) {
    case 0:
      return e;
    case 1:
      return ~e;
    case 2:
      return he(e);
    case 3:
      if (I >= r)
        return P.slice(r - N, (r += e) - N);
      if (I == 0 && O < 140 && e < 32) {
        let i = e < 16 ? re(e) : de(e);
        if (i != null)
          return i;
      }
      return ce(e);
    case 4:
      let s = new Array(e);
      for (let i = 0; i < e; i++)
        s[i] = o();
      return s;
    case 5:
      if (u.mapsAsObjects) {
        let i = {};
        if (u.keyMap)
          for (let l = 0; l < e; l++)
            i[E(u.decodeKey(o()))] = o();
        else
          for (let l = 0; l < e; l++)
            i[E(o())] = o();
        return i;
      } else {
        j && (u.mapsAsObjects = !0, j = !1);
        let i = /* @__PURE__ */ new Map();
        if (u.keyMap)
          for (let l = 0; l < e; l++)
            i.set(u.decodeKey(o()), o());
        else
          for (let l = 0; l < e; l++)
            i.set(o(), o());
        return i;
      }
    case 6:
      if (e >= H) {
        let i = y[e & 8191];
        if (i)
          return i.read || (i.read = T(i)), i.read();
        if (e < 65536) {
          if (e == oe) {
            let l = U(), f = o(), c = o();
            G(f, c);
            let d = {};
            if (u.keyMap)
              for (let h = 2; h < l; h++) {
                let b = u.decodeKey(c[h - 2]);
                d[E(b)] = o();
              }
            else
              for (let h = 2; h < l; h++) {
                let b = c[h - 2];
                d[E(b)] = o();
              }
            return d;
          } else if (e == ue) {
            let l = U(), f = o();
            for (let c = 2; c < l; c++)
              G(f++, o());
            return o();
          } else if (e == H)
            return me();
          if (u.getShared && (q(), i = y[e & 8191], i))
            return i.read || (i.read = T(i)), i.read();
        }
      }
      let n = p[e];
      if (n)
        return n.handlesRead ? n(o) : n(o());
      {
        let i = o();
        for (let l = 0; l < J.length; l++) {
          let f = J[l](e, i);
          if (f !== void 0)
            return f;
        }
        return new V(i, e);
      }
    case 7:
      switch (e) {
        case 20:
          return !1;
        case 21:
          return !0;
        case 22:
          return null;
        case 23:
          return;
        case 31:
        default:
          let i = (S || M())[e];
          if (i !== void 0)
            return i;
          throw new Error("Unknown token " + e);
      }
    default:
      if (isNaN(e)) {
        let i = new Error("Unexpected end of CBOR data");
        throw i.incomplete = !0, i;
      }
      throw new Error("Unknown CBOR token " + e);
  }
}
const X = /^[a-zA-Z_$][a-zA-Z\d_$]*$/;
function T(e) {
  function t() {
    let s = a[r++];
    if (s = s & 31, s > 23)
      switch (s) {
        case 24:
          s = a[r++];
          break;
        case 25:
          s = m.getUint16(r), r += 2;
          break;
        case 26:
          s = m.getUint32(r), r += 4;
          break;
        default:
          throw new Error("Expected array header, but got " + a[r - 1]);
      }
    let n = this.compiledReader;
    for (; n; ) {
      if (n.propertyCount === s)
        return n(o);
      n = n.next;
    }
    if (this.slowReads++ >= te) {
      let l = this.length == s ? this : this.slice(0, s);
      return n = u.keyMap ? new Function("r", "return {" + l.map((f) => u.decodeKey(f)).map((f) => X.test(f) ? E(f) + ":r()" : "[" + JSON.stringify(f) + "]:r()").join(",") + "}") : new Function("r", "return {" + l.map((f) => X.test(f) ? E(f) + ":r()" : "[" + JSON.stringify(f) + "]:r()").join(",") + "}"), this.compiledReader && (n.next = this.compiledReader), n.propertyCount = s, this.compiledReader = n, n(o);
    }
    let i = {};
    if (u.keyMap)
      for (let l = 0; l < s; l++)
        i[E(u.decodeKey(this[l]))] = o();
    else
      for (let l = 0; l < s; l++)
        i[E(this[l])] = o();
    return i;
  }
  return e.slowReads = 0, t;
}
function E(e) {
  return e === "__proto__" ? "__proto_" : e;
}
let ce = z;
function z(e) {
  let t;
  if (e < 16 && (t = re(e)))
    return t;
  if (e > 64 && L)
    return L.decode(a.subarray(r, r += e));
  const s = r + e, n = [];
  for (t = ""; r < s; ) {
    const i = a[r++];
    if (!(i & 128))
      n.push(i);
    else if ((i & 224) === 192) {
      const l = a[r++] & 63;
      n.push((i & 31) << 6 | l);
    } else if ((i & 240) === 224) {
      const l = a[r++] & 63, f = a[r++] & 63;
      n.push((i & 31) << 12 | l << 6 | f);
    } else if ((i & 248) === 240) {
      const l = a[r++] & 63, f = a[r++] & 63, c = a[r++] & 63;
      let d = (i & 7) << 18 | l << 12 | f << 6 | c;
      d > 65535 && (d -= 65536, n.push(d >>> 10 & 1023 | 55296), d = 56320 | d & 1023), n.push(d);
    } else
      n.push(i);
    n.length >= 4096 && (t += w.apply(String, n), n.length = 0);
  }
  return n.length > 0 && (t += w.apply(String, n)), t;
}
let w = String.fromCharCode;
function de(e) {
  let t = r, s = new Array(e);
  for (let n = 0; n < e; n++) {
    const i = a[r++];
    if ((i & 128) > 0) {
      r = t;
      return;
    }
    s[n] = i;
  }
  return w.apply(String, s);
}
function re(e) {
  if (e < 4)
    if (e < 2) {
      if (e === 0)
        return "";
      {
        let t = a[r++];
        if ((t & 128) > 1) {
          r -= 1;
          return;
        }
        return w(t);
      }
    } else {
      let t = a[r++], s = a[r++];
      if ((t & 128) > 0 || (s & 128) > 0) {
        r -= 2;
        return;
      }
      if (e < 3)
        return w(t, s);
      let n = a[r++];
      if ((n & 128) > 0) {
        r -= 3;
        return;
      }
      return w(t, s, n);
    }
  else {
    let t = a[r++], s = a[r++], n = a[r++], i = a[r++];
    if ((t & 128) > 0 || (s & 128) > 0 || (n & 128) > 0 || (i & 128) > 0) {
      r -= 4;
      return;
    }
    if (e < 6) {
      if (e === 4)
        return w(t, s, n, i);
      {
        let l = a[r++];
        if ((l & 128) > 0) {
          r -= 5;
          return;
        }
        return w(t, s, n, i, l);
      }
    } else if (e < 8) {
      let l = a[r++], f = a[r++];
      if ((l & 128) > 0 || (f & 128) > 0) {
        r -= 6;
        return;
      }
      if (e < 7)
        return w(t, s, n, i, l, f);
      let c = a[r++];
      if ((c & 128) > 0) {
        r -= 7;
        return;
      }
      return w(t, s, n, i, l, f, c);
    } else {
      let l = a[r++], f = a[r++], c = a[r++], d = a[r++];
      if ((l & 128) > 0 || (f & 128) > 0 || (c & 128) > 0 || (d & 128) > 0) {
        r -= 8;
        return;
      }
      if (e < 10) {
        if (e === 8)
          return w(t, s, n, i, l, f, c, d);
        {
          let h = a[r++];
          if ((h & 128) > 0) {
            r -= 9;
            return;
          }
          return w(t, s, n, i, l, f, c, d, h);
        }
      } else if (e < 12) {
        let h = a[r++], b = a[r++];
        if ((h & 128) > 0 || (b & 128) > 0) {
          r -= 10;
          return;
        }
        if (e < 11)
          return w(t, s, n, i, l, f, c, d, h, b);
        let g = a[r++];
        if ((g & 128) > 0) {
          r -= 11;
          return;
        }
        return w(t, s, n, i, l, f, c, d, h, b, g);
      } else {
        let h = a[r++], b = a[r++], g = a[r++], A = a[r++];
        if ((h & 128) > 0 || (b & 128) > 0 || (g & 128) > 0 || (A & 128) > 0) {
          r -= 12;
          return;
        }
        if (e < 14) {
          if (e === 12)
            return w(t, s, n, i, l, f, c, d, h, b, g, A);
          {
            let R = a[r++];
            if ((R & 128) > 0) {
              r -= 13;
              return;
            }
            return w(t, s, n, i, l, f, c, d, h, b, g, A, R);
          }
        } else {
          let R = a[r++], F = a[r++];
          if ((R & 128) > 0 || (F & 128) > 0) {
            r -= 14;
            return;
          }
          if (e < 15)
            return w(t, s, n, i, l, f, c, d, h, b, g, A, R, F);
          let Z = a[r++];
          if ((Z & 128) > 0) {
            r -= 15;
            return;
          }
          return w(t, s, n, i, l, f, c, d, h, b, g, A, R, F, Z);
        }
      }
    }
  }
}
function he(e) {
  return u.copyBuffers ? (
    // specifically use the copying slice (not the node one)
    Uint8Array.prototype.slice.call(a, r, r += e)
  ) : a.subarray(r, r += e);
}
let ne = new Float32Array(1), K = new Uint8Array(ne.buffer, 0, 4);
function ye() {
  let e = a[r++], t = a[r++], s = (e & 127) >> 2;
  if (s === 31)
    return t || e & 3 ? NaN : e & 128 ? -1 / 0 : 1 / 0;
  if (s === 0) {
    let n = ((e & 3) << 8 | t) / 16777216;
    return e & 128 ? -n : n;
  }
  return K[3] = e & 128 | // sign bit
  (s >> 1) + 56, K[2] = (e & 7) << 5 | // last exponent bit and first two mantissa bits
  t >> 3, K[1] = t << 5, K[0] = 0, ne[0];
}
new Array(4096);
class V {
  constructor(t, s) {
    this.value = t, this.tag = s;
  }
}
p[0] = (e) => new Date(e);
p[1] = (e) => new Date(Math.round(e * 1e3));
p[2] = (e) => {
  let t = BigInt(0);
  for (let s = 0, n = e.byteLength; s < n; s++)
    t = BigInt(e[s]) + t << BigInt(8);
  return t;
};
p[3] = (e) => BigInt(-1) - p[2](e);
p[4] = (e) => +(e[1] + "e" + e[0]);
p[5] = (e) => e[1] * Math.exp(e[0] * Math.log(2));
const G = (e, t) => {
  e = e - 57344;
  let s = y[e];
  s && s.isShared && ((y.restoreStructures || (y.restoreStructures = []))[e] = s), y[e] = t, t.read = T(t);
};
p[fe] = (e) => {
  let t = e.length, s = e[1];
  G(e[0], s);
  let n = {};
  for (let i = 2; i < t; i++) {
    let l = s[i - 2];
    n[E(l)] = e[i];
  }
  return n;
};
p[14] = (e) => x ? x[0].slice(x.position0, x.position0 += e) : new V(e, 14);
p[15] = (e) => x ? x[1].slice(x.position1, x.position1 += e) : new V(e, 15);
let pe = { Error, RegExp };
p[27] = (e) => (pe[e[0]] || Error)(e[1], e[2]);
const ie = (e) => {
  if (a[r++] != 132) {
    let s = new Error("Packed values structure must be followed by a 4 element array");
    throw a.length < r && (s.incomplete = !0), s;
  }
  let t = e();
  if (!t || !t.length) {
    let s = new Error("Packed values structure must be followed by a 4 element array");
    throw s.incomplete = !0, s;
  }
  return S = S ? t.concat(S.slice(t.length)) : t, S.prefixes = e(), S.suffixes = e(), e();
};
ie.handlesRead = !0;
p[51] = ie;
p[W] = (e) => {
  if (!S)
    if (u.getShared)
      q();
    else
      return new V(e, W);
  if (typeof e == "number")
    return S[16 + (e >= 0 ? 2 * e : -2 * e - 1)];
  let t = new Error("No support for non-integer packed references yet");
  throw e === void 0 && (t.incomplete = !0), t;
};
p[28] = (e) => {
  k || (k = /* @__PURE__ */ new Map(), k.id = 0);
  let t = k.id++, s = a[r], n;
  s >> 5 == 4 ? n = [] : n = {};
  let i = { target: n };
  k.set(t, i);
  let l = e();
  return i.used ? Object.assign(n, l) : (i.target = l, l);
};
p[28].handlesRead = !0;
p[29] = (e) => {
  let t = k.get(e);
  return t.used = !0, t.target;
};
p[258] = (e) => new Set(e);
(p[259] = (e) => (u.mapsAsObjects && (u.mapsAsObjects = !1, j = !0), e())).handlesRead = !0;
function v(e, t) {
  return typeof e == "string" ? e + t : e instanceof Array ? e.concat(t) : Object.assign({}, e, t);
}
function M() {
  if (!S)
    if (u.getShared)
      q();
    else
      throw new Error("No packed values available");
  return S;
}
const xe = 1399353956;
J.push((e, t) => {
  if (e >= 225 && e <= 255)
    return v(M().prefixes[e - 224], t);
  if (e >= 28704 && e <= 32767)
    return v(M().prefixes[e - 28672], t);
  if (e >= 1879052288 && e <= 2147483647)
    return v(M().prefixes[e - 1879048192], t);
  if (e >= 216 && e <= 223)
    return v(t, M().suffixes[e - 216]);
  if (e >= 27647 && e <= 28671)
    return v(t, M().suffixes[e - 27639]);
  if (e >= 1811940352 && e <= 1879048191)
    return v(t, M().suffixes[e - 1811939328]);
  if (e == xe)
    return {
      packedValues: S,
      structures: y.slice(0),
      version: t
    };
  if (e == 55799)
    return t;
});
const we = new Uint8Array(new Uint16Array([1]).buffer)[0] == 1, ee = [
  Uint8Array,
  Uint8ClampedArray,
  Uint16Array,
  Uint32Array,
  typeof BigUint64Array > "u" ? { name: "BigUint64Array" } : BigUint64Array,
  Int8Array,
  Int16Array,
  Int32Array,
  typeof BigInt64Array > "u" ? { name: "BigInt64Array" } : BigInt64Array,
  Float32Array,
  Float64Array
], be = [64, 68, 69, 70, 71, 72, 77, 78, 79, 85, 86];
for (let e = 0; e < ee.length; e++)
  ge(ee[e], be[e]);
function ge(e, t) {
  let s = "get" + e.name.slice(0, -5), n;
  typeof e == "function" ? n = e.BYTES_PER_ELEMENT : e = null;
  for (let i = 0; i < 2; i++) {
    if (!i && n == 1)
      continue;
    let l = n == 2 ? 1 : n == 4 ? 2 : 3;
    p[i ? t : t - 4] = n == 1 || i == we ? (f) => {
      if (!e)
        throw new Error("Could not find typed array for code " + t);
      return !u.copyBuffers && (n === 1 || n === 2 && !(f.byteOffset & 1) || n === 4 && !(f.byteOffset & 3) || n === 8 && !(f.byteOffset & 7)) ? new e(f.buffer, f.byteOffset, f.byteLength) : new e(Uint8Array.prototype.slice.call(f, 0).buffer);
    } : (f) => {
      if (!e)
        throw new Error("Could not find typed array for code " + t);
      let c = new DataView(f.buffer, f.byteOffset, f.byteLength), d = f.length >> l, h = new e(d), b = c[s];
      for (let g = 0; g < d; g++)
        h[g] = b.call(c, g << l, i);
      return h;
    };
  }
}
function me() {
  let e = U(), t = r + o();
  for (let n = 2; n < e; n++) {
    let i = U();
    r += i;
  }
  let s = r;
  return r = t, x = [z(U()), z(U())], x.position0 = 0, x.position1 = 0, x.postBundlePosition = r, r = s, o();
}
function U() {
  let e = a[r++] & 31;
  if (e > 23)
    switch (e) {
      case 24:
        e = a[r++];
        break;
      case 25:
        e = m.getUint16(r), r += 2;
        break;
      case 26:
        e = m.getUint32(r), r += 4;
        break;
    }
  return e;
}
function q() {
  if (u.getShared) {
    let e = se(() => (a = null, u.getShared())) || {}, t = e.structures || [];
    u.sharedVersion = e.version, S = u.sharedValues = e.packedValues, y === !0 ? u.structures = y = t : y.splice.apply(y, [0, t.length].concat(t));
  }
}
function se(e) {
  let t = O, s = r, n = N, i = I, l = P, f = k, c = x, d = new Uint8Array(a.slice(0, O)), h = y, b = u, g = D, A = e();
  return O = t, r = s, N = n, I = i, P = l, k = f, x = c, a = d, D = g, y = h, u = b, m = new DataView(a.buffer, a.byteOffset, a.byteLength), A;
}
function $() {
  a = null, k = null, y = null;
}
const le = new Array(147);
for (let e = 0; e < 256; e++)
  le[e] = +("1e" + Math.floor(45.15 - e * 0.30103));
let Y = new C({ useRecords: !1 });
const Ee = Y.decode;
Y.decodeMultiple;
export {
  C as Decoder,
  V as Tag,
  B as checkedRead,
  $ as clearSource,
  Ee as decode,
  le as mult10,
  o as read,
  ee as typedArrays
};
