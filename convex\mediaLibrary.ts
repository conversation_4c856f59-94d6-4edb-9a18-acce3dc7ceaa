import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Upload media (POST)
export const uploadMedia = mutation({
  args: {
    fileUrl: v.string(),
    fileType: v.string(),
    thumbnailUrl: v.string(),
    metadata: v.object({
      filename: v.string(),
      size: v.number(),
      key: v.string(),
      thumbnailKey: v.optional(v.string()),
    }),
    privacySetting: v.string(),
    tags: v.array(v.string()),
    folderId: v.optional(v.union(v.string(), v.null())),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');

    const now = new Date().toISOString();
    const mediaId = uuidv4(); // or use your preferred UUID method

    const id = await ctx.db.insert('MediaLibrary', {
      creator_id: user.subject,
      media_id: mediaId,
      file_path: args.fileUrl,
      file_type: args.fileType,
      thumbnail_url: args.thumbnailUrl,
      metadata: args.metadata,
      privacy_settings: args.privacySetting,
      tags: args.tags,
      folder_id: args.folderId ?? null,
      created_at: now,
      updated_at: now,
    });

    const media = await ctx.db.get(id);
    return { success: true, media };
  },
});

// Fetch media (GET)
export const fetchMedia = query({
  args: {
    folderId: v.optional(v.union(v.string(), v.null())),
    fileType: v.optional(v.string()),
    searchQuery: v.optional(v.string()),
    page: v.optional(v.number()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    if (!user) throw new Error('Unauthorized');
    const limit = args.limit ?? 20;
    const cursor = args.cursor ?? null;
    let q = ctx.db
      .query('MediaLibrary')
      .filter(q => q.eq(q.field('creator_id'), user.subject));
    if (args.folderId !== undefined) {
      if (args.folderId === null) {
        q = q.filter(q => q.eq(q.field('folder_id'), null));
      } else {
        q = q.filter(q => q.eq(q.field('folder_id'), args.folderId));
      }
    }
    if (args.fileType) {
      q = q.filter(q => q.eq(q.field('file_type'), args.fileType));
    }
    // Pagination
    const result = await q.order('desc').paginate({ numItems: limit, cursor });
    const paged = result.page;
    return {
      success: true,
      media: paged,
      cursor: result.continueCursor,
      isDone: result.isDone,
      total: undefined, // Not available with cursor-based pagination
      page: undefined, // Deprecated
      limit,
    };
  },
});